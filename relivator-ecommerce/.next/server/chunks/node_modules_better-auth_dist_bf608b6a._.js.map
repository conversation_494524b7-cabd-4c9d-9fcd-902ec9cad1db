{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/better-auth/dist/cookies/index.mjs"], "sourcesContent": ["import { B as BetterAuthError } from '../shared/better-auth.DdzSJf-n.mjs';\nimport { g as getDate } from '../shared/better-auth.CW6D9eSx.mjs';\nimport { a as isProduction, e as env } from '../shared/better-auth.8zoxzg-F.mjs';\nimport { base64Url } from '@better-auth/utils/base64';\nimport { createHMAC } from '@better-auth/utils/hmac';\nimport { s as safeJSONParse } from '../shared/better-auth.tB5eU6EY.mjs';\nimport { a as getBaseURL } from '../shared/better-auth.VTXNLFMT.mjs';\nimport { binary } from '@better-auth/utils/binary';\n\nconst createTime = (value, format) => {\n  const toMilliseconds = () => {\n    switch (format) {\n      case \"ms\":\n        return value;\n      case \"s\":\n        return value * 1e3;\n      case \"m\":\n        return value * 1e3 * 60;\n      case \"h\":\n        return value * 1e3 * 60 * 60;\n      case \"d\":\n        return value * 1e3 * 60 * 60 * 24;\n      case \"w\":\n        return value * 1e3 * 60 * 60 * 24 * 7;\n      case \"y\":\n        return value * 1e3 * 60 * 60 * 24 * 365;\n    }\n  };\n  const time = {\n    t: `${value}${format}`,\n    value,\n    tFormat: format,\n    toMilliseconds,\n    toSeconds: () => time.toMilliseconds() / 1e3,\n    toMinutes: () => time.toSeconds() / 60,\n    toHours: () => time.toMinutes() / 60,\n    toDays: () => time.toHours() / 24,\n    toWeeks: () => time.toDays() / 7,\n    toYears: () => time.toDays() / 365,\n    getDate: () => new Date(Date.now() + time.toMilliseconds()),\n    add: (other) => {\n      const otherMs = typeof other === \"string\" ? parseTime(other).toMilliseconds() : other.toMilliseconds();\n      return createTime(time.toMilliseconds() + otherMs, \"ms\");\n    },\n    subtract: (other) => {\n      const otherMs = typeof other === \"string\" ? parseTime(other).toMilliseconds() : other.toMilliseconds();\n      return createTime(time.toMilliseconds() - otherMs, \"ms\");\n    },\n    multiply: (factor) => createTime(time.toMilliseconds() * factor, \"ms\"),\n    divide: (divisor) => createTime(time.toMilliseconds() / divisor, \"ms\"),\n    equals: (other) => {\n      const otherMs = typeof other === \"string\" ? parseTime(other).toMilliseconds() : other.toMilliseconds();\n      return time.toMilliseconds() === otherMs;\n    },\n    lessThan: (other) => {\n      const otherMs = typeof other === \"string\" ? parseTime(other).toMilliseconds() : other.toMilliseconds();\n      return time.toMilliseconds() < otherMs;\n    },\n    greaterThan: (other) => {\n      const otherMs = typeof other === \"string\" ? parseTime(other).toMilliseconds() : other.toMilliseconds();\n      return time.toMilliseconds() > otherMs;\n    },\n    format: (pattern) => {\n      const date = time.getDate();\n      return pattern.replace(/YYYY|MM|DD|HH|mm|ss/g, (match) => {\n        switch (match) {\n          case \"YYYY\":\n            return date.getFullYear().toString();\n          case \"MM\":\n            return (date.getMonth() + 1).toString().padStart(2, \"0\");\n          case \"DD\":\n            return date.getDate().toString().padStart(2, \"0\");\n          case \"HH\":\n            return date.getHours().toString().padStart(2, \"0\");\n          case \"mm\":\n            return date.getMinutes().toString().padStart(2, \"0\");\n          case \"ss\":\n            return date.getSeconds().toString().padStart(2, \"0\");\n          default:\n            return match;\n        }\n      });\n    },\n    fromNow: () => {\n      const ms = time.toMilliseconds();\n      if (ms < 0) return time.ago();\n      if (ms < 1e3) return \"in a few seconds\";\n      if (ms < 6e4) return `in ${Math.round(ms / 1e3)} seconds`;\n      if (ms < 36e5) return `in ${Math.round(ms / 6e4)} minutes`;\n      if (ms < 864e5) return `in ${Math.round(ms / 36e5)} hours`;\n      if (ms < 6048e5) return `in ${Math.round(ms / 864e5)} days`;\n      if (ms < 26298e5) return `in ${Math.round(ms / 6048e5)} weeks`;\n      if (ms < 315576e5) return `in ${Math.round(ms / 26298e5)} months`;\n      return `in ${Math.round(ms / 315576e5)} years`;\n    },\n    ago: () => {\n      const ms = -time.toMilliseconds();\n      if (ms < 0) return time.fromNow();\n      if (ms < 1e3) return \"a few seconds ago\";\n      if (ms < 6e4) return `${Math.round(ms / 1e3)} seconds ago`;\n      if (ms < 36e5) return `${Math.round(ms / 6e4)} minutes ago`;\n      if (ms < 864e5) return `${Math.round(ms / 36e5)} hours ago`;\n      if (ms < 6048e5) return `${Math.round(ms / 864e5)} days ago`;\n      if (ms < 26298e5) return `${Math.round(ms / 6048e5)} weeks ago`;\n      if (ms < 315576e5) return `${Math.round(ms / 26298e5)} months ago`;\n      return `${Math.round(ms / 315576e5)} years ago`;\n    }\n  };\n  return time;\n};\nconst parseTime = (time) => {\n  const match = time.match(/^(\\d+)(ms|s|m|h|d|w|y)$/);\n  if (!match) throw new Error(\"Invalid time format\");\n  return createTime(parseInt(match[1]), match[2]);\n};\n\nfunction parseSetCookieHeader(setCookie) {\n  const cookies = /* @__PURE__ */ new Map();\n  const cookieArray = setCookie.split(\", \");\n  cookieArray.forEach((cookieString) => {\n    const parts = cookieString.split(\";\").map((part) => part.trim());\n    const [nameValue, ...attributes] = parts;\n    const [name, ...valueParts] = nameValue.split(\"=\");\n    const value = valueParts.join(\"=\");\n    if (!name || value === void 0) {\n      return;\n    }\n    const attrObj = { value };\n    attributes.forEach((attribute) => {\n      const [attrName, ...attrValueParts] = attribute.split(\"=\");\n      const attrValue = attrValueParts.join(\"=\");\n      const normalizedAttrName = attrName.trim().toLowerCase();\n      switch (normalizedAttrName) {\n        case \"max-age\":\n          attrObj[\"max-age\"] = attrValue ? parseInt(attrValue.trim(), 10) : void 0;\n          break;\n        case \"expires\":\n          attrObj.expires = attrValue ? new Date(attrValue.trim()) : void 0;\n          break;\n        case \"domain\":\n          attrObj.domain = attrValue ? attrValue.trim() : void 0;\n          break;\n        case \"path\":\n          attrObj.path = attrValue ? attrValue.trim() : void 0;\n          break;\n        case \"secure\":\n          attrObj.secure = true;\n          break;\n        case \"httponly\":\n          attrObj.httponly = true;\n          break;\n        case \"samesite\":\n          attrObj.samesite = attrValue ? attrValue.trim().toLowerCase() : void 0;\n          break;\n        default:\n          attrObj[normalizedAttrName] = attrValue ? attrValue.trim() : true;\n          break;\n      }\n    });\n    cookies.set(name, attrObj);\n  });\n  return cookies;\n}\nfunction setCookieToHeader(headers) {\n  return (context) => {\n    const setCookieHeader = context.response.headers.get(\"set-cookie\");\n    if (!setCookieHeader) {\n      return;\n    }\n    const cookieMap = /* @__PURE__ */ new Map();\n    const existingCookiesHeader = headers.get(\"cookie\") || \"\";\n    existingCookiesHeader.split(\";\").forEach((cookie) => {\n      const [name, ...rest] = cookie.trim().split(\"=\");\n      if (name && rest.length > 0) {\n        cookieMap.set(name, rest.join(\"=\"));\n      }\n    });\n    const setCookieHeaders = setCookieHeader.split(\",\");\n    setCookieHeaders.forEach((header) => {\n      const cookies = parseSetCookieHeader(header);\n      cookies.forEach((value, name) => {\n        cookieMap.set(name, value.value);\n      });\n    });\n    const updatedCookies = Array.from(cookieMap.entries()).map(([name, value]) => `${name}=${value}`).join(\"; \");\n    headers.set(\"cookie\", updatedCookies);\n  };\n}\n\nfunction createCookieGetter(options) {\n  const secure = options.advanced?.useSecureCookies !== void 0 ? options.advanced?.useSecureCookies : options.baseURL !== void 0 ? options.baseURL.startsWith(\"https://\") ? true : false : isProduction;\n  const secureCookiePrefix = secure ? \"__Secure-\" : \"\";\n  const crossSubdomainEnabled = !!options.advanced?.crossSubDomainCookies?.enabled;\n  const domain = crossSubdomainEnabled ? options.advanced?.crossSubDomainCookies?.domain || (options.baseURL ? new URL(options.baseURL).hostname : void 0) : void 0;\n  if (crossSubdomainEnabled && !domain) {\n    throw new BetterAuthError(\n      \"baseURL is required when crossSubdomainCookies are enabled\"\n    );\n  }\n  function createCookie(cookieName, overrideAttributes = {}) {\n    const prefix = options.advanced?.cookiePrefix || \"better-auth\";\n    const name = options.advanced?.cookies?.[cookieName]?.name || `${prefix}.${cookieName}`;\n    const attributes = options.advanced?.cookies?.[cookieName]?.attributes;\n    return {\n      name: `${secureCookiePrefix}${name}`,\n      attributes: {\n        secure: !!secureCookiePrefix,\n        sameSite: \"lax\",\n        path: \"/\",\n        httpOnly: true,\n        ...crossSubdomainEnabled ? { domain } : {},\n        ...options.advanced?.defaultCookieAttributes,\n        ...overrideAttributes,\n        ...attributes\n      }\n    };\n  }\n  return createCookie;\n}\nfunction getCookies(options) {\n  const createCookie = createCookieGetter(options);\n  const sessionMaxAge = options.session?.expiresIn || createTime(7, \"d\").toSeconds();\n  const sessionToken = createCookie(\"session_token\", {\n    maxAge: sessionMaxAge\n  });\n  const sessionData = createCookie(\"session_data\", {\n    maxAge: options.session?.cookieCache?.maxAge || 60 * 5\n  });\n  const dontRememberToken = createCookie(\"dont_remember\");\n  return {\n    sessionToken: {\n      name: sessionToken.name,\n      options: sessionToken.attributes\n    },\n    /**\n     * This cookie is used to store the session data in the cookie\n     * This is useful for when you want to cache the session in the cookie\n     */\n    sessionData: {\n      name: sessionData.name,\n      options: sessionData.attributes\n    },\n    dontRememberToken: {\n      name: dontRememberToken.name,\n      options: dontRememberToken.attributes\n    }\n  };\n}\nasync function setCookieCache(ctx, session) {\n  const shouldStoreSessionDataInCookie = ctx.context.options.session?.cookieCache?.enabled;\n  if (shouldStoreSessionDataInCookie) {\n    const filteredSession = Object.entries(session.session).reduce(\n      (acc, [key, value]) => {\n        const fieldConfig = ctx.context.options.session?.additionalFields?.[key];\n        if (!fieldConfig || fieldConfig.returned !== false) {\n          acc[key] = value;\n        }\n        return acc;\n      },\n      {}\n    );\n    const sessionData = { session: filteredSession, user: session.user };\n    const data = base64Url.encode(\n      JSON.stringify({\n        session: sessionData,\n        expiresAt: getDate(\n          ctx.context.authCookies.sessionData.options.maxAge || 60,\n          \"sec\"\n        ).getTime(),\n        signature: await createHMAC(\"SHA-256\", \"base64urlnopad\").sign(\n          ctx.context.secret,\n          JSON.stringify({\n            ...sessionData,\n            expiresAt: getDate(\n              ctx.context.authCookies.sessionData.options.maxAge || 60,\n              \"sec\"\n            ).getTime()\n          })\n        )\n      }),\n      {\n        padding: false\n      }\n    );\n    if (data.length > 4093) {\n      throw new BetterAuthError(\n        \"Session data is too large to store in the cookie. Please disable session cookie caching or reduce the size of the session data\"\n      );\n    }\n    ctx.setCookie(\n      ctx.context.authCookies.sessionData.name,\n      data,\n      ctx.context.authCookies.sessionData.options\n    );\n  }\n}\nasync function setSessionCookie(ctx, session, dontRememberMe, overrides) {\n  const dontRememberMeCookie = await ctx.getSignedCookie(\n    ctx.context.authCookies.dontRememberToken.name,\n    ctx.context.secret\n  );\n  dontRememberMe = dontRememberMe !== void 0 ? dontRememberMe : !!dontRememberMeCookie;\n  const options = ctx.context.authCookies.sessionToken.options;\n  const maxAge = dontRememberMe ? void 0 : ctx.context.sessionConfig.expiresIn;\n  await ctx.setSignedCookie(\n    ctx.context.authCookies.sessionToken.name,\n    session.session.token,\n    ctx.context.secret,\n    {\n      ...options,\n      maxAge,\n      ...overrides\n    }\n  );\n  if (dontRememberMe) {\n    await ctx.setSignedCookie(\n      ctx.context.authCookies.dontRememberToken.name,\n      \"true\",\n      ctx.context.secret,\n      ctx.context.authCookies.dontRememberToken.options\n    );\n  }\n  await setCookieCache(ctx, session);\n  ctx.context.setNewSession(session);\n  if (ctx.context.options.secondaryStorage) {\n    await ctx.context.secondaryStorage?.set(\n      session.session.token,\n      JSON.stringify({\n        user: session.user,\n        session: session.session\n      }),\n      Math.floor(\n        (new Date(session.session.expiresAt).getTime() - Date.now()) / 1e3\n      )\n    );\n  }\n}\nfunction deleteSessionCookie(ctx, skipDontRememberMe) {\n  ctx.setCookie(ctx.context.authCookies.sessionToken.name, \"\", {\n    ...ctx.context.authCookies.sessionToken.options,\n    maxAge: 0\n  });\n  ctx.setCookie(ctx.context.authCookies.sessionData.name, \"\", {\n    ...ctx.context.authCookies.sessionData.options,\n    maxAge: 0\n  });\n  if (!skipDontRememberMe) {\n    ctx.setCookie(ctx.context.authCookies.dontRememberToken.name, \"\", {\n      ...ctx.context.authCookies.dontRememberToken.options,\n      maxAge: 0\n    });\n  }\n}\nfunction parseCookies(cookieHeader) {\n  const cookies = cookieHeader.split(\"; \");\n  const cookieMap = /* @__PURE__ */ new Map();\n  cookies.forEach((cookie) => {\n    const [name, value] = cookie.split(\"=\");\n    cookieMap.set(name, value);\n  });\n  return cookieMap;\n}\nconst getSessionCookie = (request, config) => {\n  if (config?.cookiePrefix) {\n    if (config.cookieName) {\n      config.cookiePrefix = `${config.cookiePrefix}-`;\n    } else {\n      config.cookiePrefix = `${config.cookiePrefix}.`;\n    }\n  }\n  const headers = \"headers\" in request ? request.headers : request;\n  const req = request instanceof Request ? request : void 0;\n  getBaseURL(req?.url, config?.path, req);\n  const cookies = headers.get(\"cookie\");\n  if (!cookies) {\n    return null;\n  }\n  const { cookieName = \"session_token\", cookiePrefix = \"better-auth.\" } = config || {};\n  const name = `${cookiePrefix}${cookieName}`;\n  const secureCookieName = `__Secure-${name}`;\n  const parsedCookie = parseCookies(cookies);\n  const sessionToken = parsedCookie.get(name) || parsedCookie.get(secureCookieName);\n  if (sessionToken) {\n    return sessionToken;\n  }\n  return null;\n};\nconst getCookieCache = async (request, config) => {\n  const headers = request instanceof Headers ? request : request.headers;\n  const cookies = headers.get(\"cookie\");\n  if (!cookies) {\n    return null;\n  }\n  const { cookieName = \"session_data\", cookiePrefix = \"better-auth\" } = config || {};\n  const name = config?.isSecure !== void 0 ? config.isSecure ? `__Secure-${cookiePrefix}.${cookieName}` : `${cookiePrefix}.${cookieName}` : isProduction ? `__Secure-${cookiePrefix}.${cookieName}` : `${cookiePrefix}.${cookieName}`;\n  const parsedCookie = parseCookies(cookies);\n  const sessionData = parsedCookie.get(name);\n  if (sessionData) {\n    const sessionDataPayload = safeJSONParse(binary.decode(base64Url.decode(sessionData)));\n    if (!sessionDataPayload) {\n      return null;\n    }\n    const secret = config?.secret || env.BETTER_AUTH_SECRET;\n    if (!secret) {\n      throw new BetterAuthError(\n        \"getCookieCache requires a secret to be provided. Either pass it as an option or set the BETTER_AUTH_SECRET environment variable\"\n      );\n    }\n    const isValid = await createHMAC(\"SHA-256\", \"base64urlnopad\").verify(\n      secret,\n      JSON.stringify({\n        ...sessionDataPayload.session,\n        expiresAt: sessionDataPayload.expiresAt\n      }),\n      sessionDataPayload.signature\n    );\n    if (!isValid) {\n      return null;\n    }\n    return sessionDataPayload.session;\n  }\n  return null;\n};\n\nexport { createCookieGetter, deleteSessionCookie, getCookieCache, getCookies, getSessionCookie, parseCookies, parseSetCookieHeader, setCookieCache, setCookieToHeader, setSessionCookie };\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,aAAa,CAAC,OAAO;IACzB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ,MAAM;YACvB,KAAK;gBACH,OAAO,QAAQ,MAAM,KAAK;YAC5B,KAAK;gBACH,OAAO,QAAQ,MAAM,KAAK,KAAK;YACjC,KAAK;gBACH,OAAO,QAAQ,MAAM,KAAK,KAAK,KAAK;YACtC,KAAK;gBACH,OAAO,QAAQ,MAAM,KAAK,KAAK,KAAK;QACxC;IACF;IACA,MAAM,OAAO;QACX,GAAG,GAAG,QAAQ,QAAQ;QACtB;QACA,SAAS;QACT;QACA,WAAW,IAAM,KAAK,cAAc,KAAK;QACzC,WAAW,IAAM,KAAK,SAAS,KAAK;QACpC,SAAS,IAAM,KAAK,SAAS,KAAK;QAClC,QAAQ,IAAM,KAAK,OAAO,KAAK;QAC/B,SAAS,IAAM,KAAK,MAAM,KAAK;QAC/B,SAAS,IAAM,KAAK,MAAM,KAAK;QAC/B,SAAS,IAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,cAAc;QACxD,KAAK,CAAC;YACJ,MAAM,UAAU,OAAO,UAAU,WAAW,UAAU,OAAO,cAAc,KAAK,MAAM,cAAc;YACpG,OAAO,WAAW,KAAK,cAAc,KAAK,SAAS;QACrD;QACA,UAAU,CAAC;YACT,MAAM,UAAU,OAAO,UAAU,WAAW,UAAU,OAAO,cAAc,KAAK,MAAM,cAAc;YACpG,OAAO,WAAW,KAAK,cAAc,KAAK,SAAS;QACrD;QACA,UAAU,CAAC,SAAW,WAAW,KAAK,cAAc,KAAK,QAAQ;QACjE,QAAQ,CAAC,UAAY,WAAW,KAAK,cAAc,KAAK,SAAS;QACjE,QAAQ,CAAC;YACP,MAAM,UAAU,OAAO,UAAU,WAAW,UAAU,OAAO,cAAc,KAAK,MAAM,cAAc;YACpG,OAAO,KAAK,cAAc,OAAO;QACnC;QACA,UAAU,CAAC;YACT,MAAM,UAAU,OAAO,UAAU,WAAW,UAAU,OAAO,cAAc,KAAK,MAAM,cAAc;YACpG,OAAO,KAAK,cAAc,KAAK;QACjC;QACA,aAAa,CAAC;YACZ,MAAM,UAAU,OAAO,UAAU,WAAW,UAAU,OAAO,cAAc,KAAK,MAAM,cAAc;YACpG,OAAO,KAAK,cAAc,KAAK;QACjC;QACA,QAAQ,CAAC;YACP,MAAM,OAAO,KAAK,OAAO;YACzB,OAAO,QAAQ,OAAO,CAAC,wBAAwB,CAAC;gBAC9C,OAAQ;oBACN,KAAK;wBACH,OAAO,KAAK,WAAW,GAAG,QAAQ;oBACpC,KAAK;wBACH,OAAO,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBACtD,KAAK;wBACH,OAAO,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBAC/C,KAAK;wBACH,OAAO,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBAChD,KAAK;wBACH,OAAO,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBAClD,KAAK;wBACH,OAAO,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBAClD;wBACE,OAAO;gBACX;YACF;QACF;QACA,SAAS;YACP,MAAM,KAAK,KAAK,cAAc;YAC9B,IAAI,KAAK,GAAG,OAAO,KAAK,GAAG;YAC3B,IAAI,KAAK,KAAK,OAAO;YACrB,IAAI,KAAK,KAAK,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC;YACzD,IAAI,KAAK,MAAM,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC;YAC1D,IAAI,KAAK,OAAO,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,MAAM,CAAC;YAC1D,IAAI,KAAK,QAAQ,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,OAAO,KAAK,CAAC;YAC3D,IAAI,KAAK,SAAS,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,QAAQ,MAAM,CAAC;YAC9D,IAAI,KAAK,UAAU,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,SAAS,OAAO,CAAC;YACjE,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,UAAU,MAAM,CAAC;QAChD;QACA,KAAK;YACH,MAAM,KAAK,CAAC,KAAK,cAAc;YAC/B,IAAI,KAAK,GAAG,OAAO,KAAK,OAAO;YAC/B,IAAI,KAAK,KAAK,OAAO;YACrB,IAAI,KAAK,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,KAAK,YAAY,CAAC;YAC1D,IAAI,KAAK,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,KAAK,YAAY,CAAC;YAC3D,IAAI,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,UAAU,CAAC;YAC3D,IAAI,KAAK,QAAQ,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,OAAO,SAAS,CAAC;YAC5D,IAAI,KAAK,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,QAAQ,UAAU,CAAC;YAC/D,IAAI,KAAK,UAAU,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,SAAS,WAAW,CAAC;YAClE,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,UAAU,UAAU,CAAC;QACjD;IACF;IACA,OAAO;AACT;AACA,MAAM,YAAY,CAAC;IACjB,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,IAAI,CAAC,OAAO,MAAM,IAAI,MAAM;IAC5B,OAAO,WAAW,SAAS,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;AAChD;AAEA,SAAS,qBAAqB,SAAS;IACrC,MAAM,UAAU,aAAa,GAAG,IAAI;IACpC,MAAM,cAAc,UAAU,KAAK,CAAC;IACpC,YAAY,OAAO,CAAC,CAAC;QACnB,MAAM,QAAQ,aAAa,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI;QAC7D,MAAM,CAAC,WAAW,GAAG,WAAW,GAAG;QACnC,MAAM,CAAC,MAAM,GAAG,WAAW,GAAG,UAAU,KAAK,CAAC;QAC9C,MAAM,QAAQ,WAAW,IAAI,CAAC;QAC9B,IAAI,CAAC,QAAQ,UAAU,KAAK,GAAG;YAC7B;QACF;QACA,MAAM,UAAU;YAAE;QAAM;QACxB,WAAW,OAAO,CAAC,CAAC;YAClB,MAAM,CAAC,UAAU,GAAG,eAAe,GAAG,UAAU,KAAK,CAAC;YACtD,MAAM,YAAY,eAAe,IAAI,CAAC;YACtC,MAAM,qBAAqB,SAAS,IAAI,GAAG,WAAW;YACtD,OAAQ;gBACN,KAAK;oBACH,OAAO,CAAC,UAAU,GAAG,YAAY,SAAS,UAAU,IAAI,IAAI,MAAM,KAAK;oBACvE;gBACF,KAAK;oBACH,QAAQ,OAAO,GAAG,YAAY,IAAI,KAAK,UAAU,IAAI,MAAM,KAAK;oBAChE;gBACF,KAAK;oBACH,QAAQ,MAAM,GAAG,YAAY,UAAU,IAAI,KAAK,KAAK;oBACrD;gBACF,KAAK;oBACH,QAAQ,IAAI,GAAG,YAAY,UAAU,IAAI,KAAK,KAAK;oBACnD;gBACF,KAAK;oBACH,QAAQ,MAAM,GAAG;oBACjB;gBACF,KAAK;oBACH,QAAQ,QAAQ,GAAG;oBACnB;gBACF,KAAK;oBACH,QAAQ,QAAQ,GAAG,YAAY,UAAU,IAAI,GAAG,WAAW,KAAK,KAAK;oBACrE;gBACF;oBACE,OAAO,CAAC,mBAAmB,GAAG,YAAY,UAAU,IAAI,KAAK;oBAC7D;YACJ;QACF;QACA,QAAQ,GAAG,CAAC,MAAM;IACpB;IACA,OAAO;AACT;AACA,SAAS,kBAAkB,OAAO;IAChC,OAAO,CAAC;QACN,MAAM,kBAAkB,QAAQ,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;QACrD,IAAI,CAAC,iBAAiB;YACpB;QACF;QACA,MAAM,YAAY,aAAa,GAAG,IAAI;QACtC,MAAM,wBAAwB,QAAQ,GAAG,CAAC,aAAa;QACvD,sBAAsB,KAAK,CAAC,KAAK,OAAO,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,GAAG,KAAK,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;YAC5C,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;gBAC3B,UAAU,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC;YAChC;QACF;QACA,MAAM,mBAAmB,gBAAgB,KAAK,CAAC;QAC/C,iBAAiB,OAAO,CAAC,CAAC;YACxB,MAAM,UAAU,qBAAqB;YACrC,QAAQ,OAAO,CAAC,CAAC,OAAO;gBACtB,UAAU,GAAG,CAAC,MAAM,MAAM,KAAK;YACjC;QACF;QACA,MAAM,iBAAiB,MAAM,IAAI,CAAC,UAAU,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,GAAG,KAAK,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC;QACvG,QAAQ,GAAG,CAAC,UAAU;IACxB;AACF;AAEA,SAAS,mBAAmB,OAAO;IACjC,MAAM,SAAS,QAAQ,QAAQ,EAAE,qBAAqB,KAAK,IAAI,QAAQ,QAAQ,EAAE,mBAAmB,QAAQ,OAAO,KAAK,KAAK,IAAI,QAAQ,OAAO,CAAC,UAAU,CAAC,cAAc,OAAO,QAAQ,oLAAA,CAAA,IAAY;IACrM,MAAM,qBAAqB,SAAS,cAAc;IAClD,MAAM,wBAAwB,CAAC,CAAC,QAAQ,QAAQ,EAAE,uBAAuB;IACzE,MAAM,SAAS,wBAAwB,QAAQ,QAAQ,EAAE,uBAAuB,UAAU,CAAC,QAAQ,OAAO,GAAG,IAAI,IAAI,QAAQ,OAAO,EAAE,QAAQ,GAAG,KAAK,CAAC,IAAI,KAAK;IAChK,IAAI,yBAAyB,CAAC,QAAQ;QACpC,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB;IAEJ;IACA,SAAS,aAAa,UAAU,EAAE,qBAAqB,CAAC,CAAC;QACvD,MAAM,SAAS,QAAQ,QAAQ,EAAE,gBAAgB;QACjD,MAAM,OAAO,QAAQ,QAAQ,EAAE,SAAS,CAAC,WAAW,EAAE,QAAQ,GAAG,OAAO,CAAC,EAAE,YAAY;QACvF,MAAM,aAAa,QAAQ,QAAQ,EAAE,SAAS,CAAC,WAAW,EAAE;QAC5D,OAAO;YACL,MAAM,GAAG,qBAAqB,MAAM;YACpC,YAAY;gBACV,QAAQ,CAAC,CAAC;gBACV,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,GAAG,wBAAwB;oBAAE;gBAAO,IAAI,CAAC,CAAC;gBAC1C,GAAG,QAAQ,QAAQ,EAAE,uBAAuB;gBAC5C,GAAG,kBAAkB;gBACrB,GAAG,UAAU;YACf;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,WAAW,OAAO;IACzB,MAAM,eAAe,mBAAmB;IACxC,MAAM,gBAAgB,QAAQ,OAAO,EAAE,aAAa,WAAW,GAAG,KAAK,SAAS;IAChF,MAAM,eAAe,aAAa,iBAAiB;QACjD,QAAQ;IACV;IACA,MAAM,cAAc,aAAa,gBAAgB;QAC/C,QAAQ,QAAQ,OAAO,EAAE,aAAa,UAAU,KAAK;IACvD;IACA,MAAM,oBAAoB,aAAa;IACvC,OAAO;QACL,cAAc;YACZ,MAAM,aAAa,IAAI;YACvB,SAAS,aAAa,UAAU;QAClC;QACA;;;KAGC,GACD,aAAa;YACX,MAAM,YAAY,IAAI;YACtB,SAAS,YAAY,UAAU;QACjC;QACA,mBAAmB;YACjB,MAAM,kBAAkB,IAAI;YAC5B,SAAS,kBAAkB,UAAU;QACvC;IACF;AACF;AACA,eAAe,eAAe,GAAG,EAAE,OAAO;IACxC,MAAM,iCAAiC,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,aAAa;IACjF,IAAI,gCAAgC;QAClC,MAAM,kBAAkB,OAAO,OAAO,CAAC,QAAQ,OAAO,EAAE,MAAM,CAC5D,CAAC,KAAK,CAAC,KAAK,MAAM;YAChB,MAAM,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,kBAAkB,CAAC,IAAI;YACxE,IAAI,CAAC,eAAe,YAAY,QAAQ,KAAK,OAAO;gBAClD,GAAG,CAAC,IAAI,GAAG;YACb;YACA,OAAO;QACT,GACA,CAAC;QAEH,MAAM,cAAc;YAAE,SAAS;YAAiB,MAAM,QAAQ,IAAI;QAAC;QACnE,MAAM,OAAO,8JAAA,CAAA,YAAS,CAAC,MAAM,CAC3B,KAAK,SAAS,CAAC;YACb,SAAS;YACT,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EACf,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,IAAI,IACtD,OACA,OAAO;YACT,WAAW,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,kBAAkB,IAAI,CAC3D,IAAI,OAAO,CAAC,MAAM,EAClB,KAAK,SAAS,CAAC;gBACb,GAAG,WAAW;gBACd,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EACf,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,IAAI,IACtD,OACA,OAAO;YACX;QAEJ,IACA;YACE,SAAS;QACX;QAEF,IAAI,KAAK,MAAM,GAAG,MAAM;YACtB,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB;QAEJ;QACA,IAAI,SAAS,CACX,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EACxC,MACA,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO;IAE/C;AACF;AACA,eAAe,iBAAiB,GAAG,EAAE,OAAO,EAAE,cAAc,EAAE,SAAS;IACrE,MAAM,uBAAuB,MAAM,IAAI,eAAe,CACpD,IAAI,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,EAC9C,IAAI,OAAO,CAAC,MAAM;IAEpB,iBAAiB,mBAAmB,KAAK,IAAI,iBAAiB,CAAC,CAAC;IAChE,MAAM,UAAU,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO;IAC5D,MAAM,SAAS,iBAAiB,KAAK,IAAI,IAAI,OAAO,CAAC,aAAa,CAAC,SAAS;IAC5E,MAAM,IAAI,eAAe,CACvB,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,EACzC,QAAQ,OAAO,CAAC,KAAK,EACrB,IAAI,OAAO,CAAC,MAAM,EAClB;QACE,GAAG,OAAO;QACV;QACA,GAAG,SAAS;IACd;IAEF,IAAI,gBAAgB;QAClB,MAAM,IAAI,eAAe,CACvB,IAAI,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,EAC9C,QACA,IAAI,OAAO,CAAC,MAAM,EAClB,IAAI,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO;IAErD;IACA,MAAM,eAAe,KAAK;IAC1B,IAAI,OAAO,CAAC,aAAa,CAAC;IAC1B,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE;QACxC,MAAM,IAAI,OAAO,CAAC,gBAAgB,EAAE,IAClC,QAAQ,OAAO,CAAC,KAAK,EACrB,KAAK,SAAS,CAAC;YACb,MAAM,QAAQ,IAAI;YAClB,SAAS,QAAQ,OAAO;QAC1B,IACA,KAAK,KAAK,CACR,CAAC,IAAI,KAAK,QAAQ,OAAO,CAAC,SAAS,EAAE,OAAO,KAAK,KAAK,GAAG,EAAE,IAAI;IAGrE;AACF;AACA,SAAS,oBAAoB,GAAG,EAAE,kBAAkB;IAClD,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI;QAC3D,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO;QAC/C,QAAQ;IACV;IACA,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI;QAC1D,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO;QAC9C,QAAQ;IACV;IACA,IAAI,CAAC,oBAAoB;QACvB,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI;YAChE,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO;YACpD,QAAQ;QACV;IACF;AACF;AACA,SAAS,aAAa,YAAY;IAChC,MAAM,UAAU,aAAa,KAAK,CAAC;IACnC,MAAM,YAAY,aAAa,GAAG,IAAI;IACtC,QAAQ,OAAO,CAAC,CAAC;QACf,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,KAAK,CAAC;QACnC,UAAU,GAAG,CAAC,MAAM;IACtB;IACA,OAAO;AACT;AACA,MAAM,mBAAmB,CAAC,SAAS;IACjC,IAAI,QAAQ,cAAc;QACxB,IAAI,OAAO,UAAU,EAAE;YACrB,OAAO,YAAY,GAAG,GAAG,OAAO,YAAY,CAAC,CAAC,CAAC;QACjD,OAAO;YACL,OAAO,YAAY,GAAG,GAAG,OAAO,YAAY,CAAC,CAAC,CAAC;QACjD;IACF;IACA,MAAM,UAAU,aAAa,UAAU,QAAQ,OAAO,GAAG;IACzD,MAAM,MAAM,mBAAmB,UAAU,UAAU,KAAK;IACxD,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD,EAAE,KAAK,KAAK,QAAQ,MAAM;IACnC,MAAM,UAAU,QAAQ,GAAG,CAAC;IAC5B,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IACA,MAAM,EAAE,aAAa,eAAe,EAAE,eAAe,cAAc,EAAE,GAAG,UAAU,CAAC;IACnF,MAAM,OAAO,GAAG,eAAe,YAAY;IAC3C,MAAM,mBAAmB,CAAC,SAAS,EAAE,MAAM;IAC3C,MAAM,eAAe,aAAa;IAClC,MAAM,eAAe,aAAa,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC;IAChE,IAAI,cAAc;QAChB,OAAO;IACT;IACA,OAAO;AACT;AACA,MAAM,iBAAiB,OAAO,SAAS;IACrC,MAAM,UAAU,mBAAmB,UAAU,UAAU,QAAQ,OAAO;IACtE,MAAM,UAAU,QAAQ,GAAG,CAAC;IAC5B,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IACA,MAAM,EAAE,aAAa,cAAc,EAAE,eAAe,aAAa,EAAE,GAAG,UAAU,CAAC;IACjF,MAAM,OAAO,QAAQ,aAAa,KAAK,IAAI,OAAO,QAAQ,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE,YAAY,GAAG,GAAG,aAAa,CAAC,EAAE,YAAY,GAAG,oLAAA,CAAA,IAAY,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE,YAAY,GAAG,GAAG,aAAa,CAAC,EAAE,YAAY;IACnO,MAAM,eAAe,aAAa;IAClC,MAAM,cAAc,aAAa,GAAG,CAAC;IACrC,IAAI,aAAa;QACf,MAAM,qBAAqB,CAAA,GAAA,iLAAA,CAAA,IAAa,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,MAAM,CAAC,8JAAA,CAAA,YAAS,CAAC,MAAM,CAAC;QACxE,IAAI,CAAC,oBAAoB;YACvB,OAAO;QACT;QACA,MAAM,SAAS,QAAQ,UAAU,oLAAA,CAAA,IAAG,CAAC,kBAAkB;QACvD,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB;QAEJ;QACA,MAAM,UAAU,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,kBAAkB,MAAM,CAClE,QACA,KAAK,SAAS,CAAC;YACb,GAAG,mBAAmB,OAAO;YAC7B,WAAW,mBAAmB,SAAS;QACzC,IACA,mBAAmB,SAAS;QAE9B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QACA,OAAO,mBAAmB,OAAO;IACnC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/better-auth/dist/social-providers/index.mjs"], "sourcesContent": ["import { betterFetch } from '@better-fetch/fetch';\nimport { APIError } from 'better-call';\nimport { decodeJwt, decodeProtectedHeader, jwtVerify, importJWK, createRemoteJWKSet } from 'jose';\nimport { r as refreshAccessToken, v as validateAuthorizationCode, c as createAuthorizationURL, b as getOAuth2Tokens, g as generateCodeChallenge } from '../shared/better-auth.DufyW0qf.mjs';\nimport '@better-auth/utils/hash';\nimport { base64 } from '@better-auth/utils/base64';\nimport { z } from 'zod';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../shared/better-auth.B4Qoxdgc.mjs';\nimport { B as BetterAuthError } from '../shared/better-auth.DdzSJf-n.mjs';\nimport { l as logger } from '../shared/better-auth.Cqykj82J.mjs';\nimport '@better-auth/utils/random';\nimport '../shared/better-auth.CW6D9eSx.mjs';\n\nconst apple = (options) => {\n  const tokenEndpoint = \"https://appleid.apple.com/auth/token\";\n  return {\n    id: \"apple\",\n    name: \"Apple\",\n    async createAuthorizationURL({ state, scopes, redirectURI }) {\n      const _scope = options.disableDefaultScope ? [] : [\"email\", \"name\"];\n      options.scope && _scope.push(...options.scope);\n      scopes && _scope.push(...scopes);\n      const url = await createAuthorizationURL({\n        id: \"apple\",\n        options,\n        authorizationEndpoint: \"https://appleid.apple.com/auth/authorize\",\n        scopes: _scope,\n        state,\n        redirectURI,\n        responseMode: \"form_post\",\n        responseType: \"code id_token\"\n      });\n      return url;\n    },\n    validateAuthorizationCode: async ({ code, codeVerifier, redirectURI }) => {\n      return validateAuthorizationCode({\n        code,\n        codeVerifier,\n        redirectURI,\n        options,\n        tokenEndpoint\n      });\n    },\n    async verifyIdToken(token, nonce) {\n      if (options.disableIdTokenSignIn) {\n        return false;\n      }\n      if (options.verifyIdToken) {\n        return options.verifyIdToken(token, nonce);\n      }\n      const decodedHeader = decodeProtectedHeader(token);\n      const { kid, alg: jwtAlg } = decodedHeader;\n      if (!kid || !jwtAlg) return false;\n      const publicKey = await getApplePublicKey(kid);\n      const { payload: jwtClaims } = await jwtVerify(token, publicKey, {\n        algorithms: [jwtAlg],\n        issuer: \"https://appleid.apple.com\",\n        audience: options.appBundleIdentifier || options.clientId,\n        maxTokenAge: \"1h\"\n      });\n      [\"email_verified\", \"is_private_email\"].forEach((field) => {\n        if (jwtClaims[field] !== void 0) {\n          jwtClaims[field] = Boolean(jwtClaims[field]);\n        }\n      });\n      if (nonce && jwtClaims.nonce !== nonce) {\n        return false;\n      }\n      return !!jwtClaims;\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://appleid.apple.com/auth/token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      if (!token.idToken) {\n        return null;\n      }\n      const profile = decodeJwt(token.idToken);\n      if (!profile) {\n        return null;\n      }\n      const name = token.user ? `${token.user.name?.firstName} ${token.user.name?.lastName}` : profile.name || profile.email;\n      const emailVerified = typeof profile.email_verified === \"boolean\" ? profile.email_verified : profile.email_verified === \"true\";\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.sub,\n          name,\n          emailVerified,\n          email: profile.email,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\nconst getApplePublicKey = async (kid) => {\n  const APPLE_BASE_URL = \"https://appleid.apple.com\";\n  const JWKS_APPLE_URI = \"/auth/keys\";\n  const { data } = await betterFetch(`${APPLE_BASE_URL}${JWKS_APPLE_URI}`);\n  if (!data?.keys) {\n    throw new APIError(\"BAD_REQUEST\", {\n      message: \"Keys not found\"\n    });\n  }\n  const jwk = data.keys.find((key) => key.kid === kid);\n  if (!jwk) {\n    throw new Error(`JWK with kid ${kid} not found`);\n  }\n  return await importJWK(jwk, jwk.alg);\n};\n\nconst discord = (options) => {\n  return {\n    id: \"discord\",\n    name: \"Discord\",\n    createAuthorizationURL({ state, scopes, redirectURI }) {\n      const _scopes = options.disableDefaultScope ? [] : [\"identify\", \"email\"];\n      scopes && _scopes.push(...scopes);\n      options.scope && _scopes.push(...options.scope);\n      return new URL(\n        `https://discord.com/api/oauth2/authorize?scope=${_scopes.join(\n          \"+\"\n        )}&response_type=code&client_id=${options.clientId}&redirect_uri=${encodeURIComponent(\n          options.redirectURI || redirectURI\n        )}&state=${state}&prompt=${options.prompt || \"none\"}`\n      );\n    },\n    validateAuthorizationCode: async ({ code, redirectURI }) => {\n      return validateAuthorizationCode({\n        code,\n        redirectURI,\n        options,\n        tokenEndpoint: \"https://discord.com/api/oauth2/token\"\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://discord.com/api/oauth2/token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data: profile, error } = await betterFetch(\n        \"https://discord.com/api/users/@me\",\n        {\n          headers: {\n            authorization: `Bearer ${token.accessToken}`\n          }\n        }\n      );\n      if (error) {\n        return null;\n      }\n      if (profile.avatar === null) {\n        const defaultAvatarNumber = profile.discriminator === \"0\" ? Number(BigInt(profile.id) >> BigInt(22)) % 6 : parseInt(profile.discriminator) % 5;\n        profile.image_url = `https://cdn.discordapp.com/embed/avatars/${defaultAvatarNumber}.png`;\n      } else {\n        const format = profile.avatar.startsWith(\"a_\") ? \"gif\" : \"png\";\n        profile.image_url = `https://cdn.discordapp.com/avatars/${profile.id}/${profile.avatar}.${format}`;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.id,\n          name: profile.global_name || profile.username || \"\",\n          email: profile.email,\n          emailVerified: profile.verified,\n          image: profile.image_url,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst facebook = (options) => {\n  return {\n    id: \"facebook\",\n    name: \"Facebook\",\n    async createAuthorizationURL({ state, scopes, redirectURI, loginHint }) {\n      const _scopes = options.disableDefaultScope ? [] : [\"email\", \"public_profile\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return await createAuthorizationURL({\n        id: \"facebook\",\n        options,\n        authorizationEndpoint: \"https://www.facebook.com/v21.0/dialog/oauth\",\n        scopes: _scopes,\n        state,\n        redirectURI,\n        loginHint,\n        additionalParams: options.configId ? {\n          config_id: options.configId\n        } : {}\n      });\n    },\n    validateAuthorizationCode: async ({ code, redirectURI }) => {\n      return validateAuthorizationCode({\n        code,\n        redirectURI,\n        options,\n        tokenEndpoint: \"https://graph.facebook.com/oauth/access_token\"\n      });\n    },\n    async verifyIdToken(token, nonce) {\n      if (options.disableIdTokenSignIn) {\n        return false;\n      }\n      if (options.verifyIdToken) {\n        return options.verifyIdToken(token, nonce);\n      }\n      if (token.split(\".\").length) {\n        try {\n          const { payload: jwtClaims } = await jwtVerify(\n            token,\n            createRemoteJWKSet(\n              new URL(\"https://www.facebook.com/.well-known/oauth/openid/jwks\")\n            ),\n            {\n              algorithms: [\"RS256\"],\n              audience: options.clientId,\n              issuer: \"https://www.facebook.com\"\n            }\n          );\n          if (nonce && jwtClaims.nonce !== nonce) {\n            return false;\n          }\n          return !!jwtClaims;\n        } catch (error) {\n          return false;\n        }\n      }\n      return true;\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://graph.facebook.com/v18.0/oauth/access_token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      if (token.idToken) {\n        const profile2 = decodeJwt(token.idToken);\n        const user = {\n          id: profile2.sub,\n          name: profile2.name,\n          email: profile2.email,\n          picture: {\n            data: {\n              url: profile2.picture,\n              height: 100,\n              width: 100,\n              is_silhouette: false\n            }\n          }\n        };\n        const userMap2 = await options.mapProfileToUser?.({\n          ...user,\n          email_verified: true\n        });\n        return {\n          user: {\n            ...user,\n            emailVerified: true,\n            ...userMap2\n          },\n          data: profile2\n        };\n      }\n      const fields = [\n        \"id\",\n        \"name\",\n        \"email\",\n        \"picture\",\n        ...options?.fields || []\n      ];\n      const { data: profile, error } = await betterFetch(\n        \"https://graph.facebook.com/me?fields=\" + fields.join(\",\"),\n        {\n          auth: {\n            type: \"Bearer\",\n            token: token.accessToken\n          }\n        }\n      );\n      if (error) {\n        return null;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.id,\n          name: profile.name,\n          email: profile.email,\n          image: profile.picture.data.url,\n          emailVerified: profile.email_verified,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst github = (options) => {\n  const tokenEndpoint = \"https://github.com/login/oauth/access_token\";\n  return {\n    id: \"github\",\n    name: \"GitHub\",\n    createAuthorizationURL({ state, scopes, loginHint, redirectURI }) {\n      const _scopes = options.disableDefaultScope ? [] : [\"read:user\", \"user:email\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return createAuthorizationURL({\n        id: \"github\",\n        options,\n        authorizationEndpoint: \"https://github.com/login/oauth/authorize\",\n        scopes: _scopes,\n        state,\n        redirectURI,\n        loginHint,\n        prompt: options.prompt\n      });\n    },\n    validateAuthorizationCode: async ({ code, redirectURI }) => {\n      return validateAuthorizationCode({\n        code,\n        redirectURI,\n        options,\n        tokenEndpoint\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://github.com/login/oauth/access_token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data: profile, error } = await betterFetch(\n        \"https://api.github.com/user\",\n        {\n          headers: {\n            \"User-Agent\": \"better-auth\",\n            authorization: `Bearer ${token.accessToken}`\n          }\n        }\n      );\n      if (error) {\n        return null;\n      }\n      const { data: emails } = await betterFetch(\"https://api.github.com/user/emails\", {\n        headers: {\n          Authorization: `Bearer ${token.accessToken}`,\n          \"User-Agent\": \"better-auth\"\n        }\n      });\n      if (!profile.email && emails) {\n        profile.email = (emails.find((e) => e.primary) ?? emails[0])?.email;\n      }\n      const emailVerified = emails?.find((e) => e.email === profile.email)?.verified ?? false;\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.id.toString(),\n          name: profile.name || profile.login,\n          email: profile.email,\n          image: profile.avatar_url,\n          emailVerified,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst google = (options) => {\n  return {\n    id: \"google\",\n    name: \"Google\",\n    async createAuthorizationURL({\n      state,\n      scopes,\n      codeVerifier,\n      redirectURI,\n      loginHint,\n      display\n    }) {\n      if (!options.clientId || !options.clientSecret) {\n        logger.error(\n          \"Client Id and Client Secret is required for Google. Make sure to provide them in the options.\"\n        );\n        throw new BetterAuthError(\"CLIENT_ID_AND_SECRET_REQUIRED\");\n      }\n      if (!codeVerifier) {\n        throw new BetterAuthError(\"codeVerifier is required for Google\");\n      }\n      const _scopes = options.disableDefaultScope ? [] : [\"email\", \"profile\", \"openid\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      const url = await createAuthorizationURL({\n        id: \"google\",\n        options,\n        authorizationEndpoint: \"https://accounts.google.com/o/oauth2/auth\",\n        scopes: _scopes,\n        state,\n        codeVerifier,\n        redirectURI,\n        prompt: options.prompt,\n        accessType: options.accessType,\n        display: display || options.display,\n        loginHint,\n        hd: options.hd,\n        additionalParams: {\n          include_granted_scopes: \"true\"\n        }\n      });\n      return url;\n    },\n    validateAuthorizationCode: async ({ code, codeVerifier, redirectURI }) => {\n      return validateAuthorizationCode({\n        code,\n        codeVerifier,\n        redirectURI,\n        options,\n        tokenEndpoint: \"https://oauth2.googleapis.com/token\"\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://www.googleapis.com/oauth2/v4/token\"\n      });\n    },\n    async verifyIdToken(token, nonce) {\n      if (options.disableIdTokenSignIn) {\n        return false;\n      }\n      if (options.verifyIdToken) {\n        return options.verifyIdToken(token, nonce);\n      }\n      const googlePublicKeyUrl = `https://www.googleapis.com/oauth2/v3/tokeninfo?id_token=${token}`;\n      const { data: tokenInfo } = await betterFetch(googlePublicKeyUrl);\n      if (!tokenInfo) {\n        return false;\n      }\n      const isValid = tokenInfo.aud === options.clientId && (tokenInfo.iss === \"https://accounts.google.com\" || tokenInfo.iss === \"accounts.google.com\");\n      return isValid;\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      if (!token.idToken) {\n        return null;\n      }\n      const user = decodeJwt(token.idToken);\n      const userMap = await options.mapProfileToUser?.(user);\n      return {\n        user: {\n          id: user.sub,\n          name: user.name,\n          email: user.email,\n          image: user.picture,\n          emailVerified: user.email_verified,\n          ...userMap\n        },\n        data: user\n      };\n    },\n    options\n  };\n};\n\nconst huggingface = (options) => {\n  return {\n    id: \"huggingface\",\n    name: \"Hugging Face\",\n    createAuthorizationURL({ state, scopes, codeVerifier, redirectURI }) {\n      const _scopes = options.disableDefaultScope ? [] : [\"openid\", \"profile\", \"email\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return createAuthorizationURL({\n        id: \"huggingface\",\n        options,\n        authorizationEndpoint: \"https://huggingface.co/oauth/authorize\",\n        scopes: _scopes,\n        state,\n        codeVerifier,\n        redirectURI\n      });\n    },\n    validateAuthorizationCode: async ({ code, codeVerifier, redirectURI }) => {\n      return validateAuthorizationCode({\n        code,\n        codeVerifier,\n        redirectURI,\n        options,\n        tokenEndpoint: \"https://huggingface.co/oauth/token\"\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://huggingface.co/oauth/token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data: profile, error } = await betterFetch(\n        \"https://huggingface.co/oauth/userinfo\",\n        {\n          method: \"GET\",\n          headers: {\n            Authorization: `Bearer ${token.accessToken}`\n          }\n        }\n      );\n      if (error) {\n        return null;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.sub,\n          name: profile.name || profile.preferred_username,\n          email: profile.email,\n          image: profile.picture,\n          emailVerified: profile.email_verified ?? false,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst microsoft = (options) => {\n  const tenant = options.tenantId || \"common\";\n  const authorizationEndpoint = `https://login.microsoftonline.com/${tenant}/oauth2/v2.0/authorize`;\n  const tokenEndpoint = `https://login.microsoftonline.com/${tenant}/oauth2/v2.0/token`;\n  return {\n    id: \"microsoft\",\n    name: \"Microsoft EntraID\",\n    createAuthorizationURL(data) {\n      const scopes = options.disableDefaultScope ? [] : [\"openid\", \"profile\", \"email\", \"User.Read\", \"offline_access\"];\n      options.scope && scopes.push(...options.scope);\n      data.scopes && scopes.push(...data.scopes);\n      return createAuthorizationURL({\n        id: \"microsoft\",\n        options,\n        authorizationEndpoint,\n        state: data.state,\n        codeVerifier: data.codeVerifier,\n        scopes,\n        redirectURI: data.redirectURI,\n        prompt: options.prompt\n      });\n    },\n    validateAuthorizationCode({ code, codeVerifier, redirectURI }) {\n      return validateAuthorizationCode({\n        code,\n        codeVerifier,\n        redirectURI,\n        options,\n        tokenEndpoint\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      if (!token.idToken) {\n        return null;\n      }\n      const user = decodeJwt(token.idToken);\n      const profilePhotoSize = options.profilePhotoSize || 48;\n      await betterFetch(\n        `https://graph.microsoft.com/v1.0/me/photos/${profilePhotoSize}x${profilePhotoSize}/$value`,\n        {\n          headers: {\n            Authorization: `Bearer ${token.accessToken}`\n          },\n          async onResponse(context) {\n            if (options.disableProfilePhoto || !context.response.ok) {\n              return;\n            }\n            try {\n              const response = context.response.clone();\n              const pictureBuffer = await response.arrayBuffer();\n              const pictureBase64 = base64.encode(pictureBuffer);\n              user.picture = `data:image/jpeg;base64, ${pictureBase64}`;\n            } catch (e) {\n              logger.error(\n                e && typeof e === \"object\" && \"name\" in e ? e.name : \"\",\n                e\n              );\n            }\n          }\n        }\n      );\n      const userMap = await options.mapProfileToUser?.(user);\n      return {\n        user: {\n          id: user.sub,\n          name: user.name,\n          email: user.email,\n          image: user.picture,\n          emailVerified: true,\n          ...userMap\n        },\n        data: user\n      };\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      const scopes = options.disableDefaultScope ? [] : [\"openid\", \"profile\", \"email\", \"User.Read\", \"offline_access\"];\n      options.scope && scopes.push(...options.scope);\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientSecret: options.clientSecret\n        },\n        extraParams: {\n          scope: scopes.join(\" \")\n          // Include the scopes in request to microsoft\n        },\n        tokenEndpoint\n      });\n    },\n    options\n  };\n};\n\nconst spotify = (options) => {\n  return {\n    id: \"spotify\",\n    name: \"Spotify\",\n    createAuthorizationURL({ state, scopes, codeVerifier, redirectURI }) {\n      const _scopes = options.disableDefaultScope ? [] : [\"user-read-email\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return createAuthorizationURL({\n        id: \"spotify\",\n        options,\n        authorizationEndpoint: \"https://accounts.spotify.com/authorize\",\n        scopes: _scopes,\n        state,\n        codeVerifier,\n        redirectURI\n      });\n    },\n    validateAuthorizationCode: async ({ code, codeVerifier, redirectURI }) => {\n      return validateAuthorizationCode({\n        code,\n        codeVerifier,\n        redirectURI,\n        options,\n        tokenEndpoint: \"https://accounts.spotify.com/api/token\"\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://accounts.spotify.com/api/token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data: profile, error } = await betterFetch(\n        \"https://api.spotify.com/v1/me\",\n        {\n          method: \"GET\",\n          headers: {\n            Authorization: `Bearer ${token.accessToken}`\n          }\n        }\n      );\n      if (error) {\n        return null;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.id,\n          name: profile.display_name,\n          email: profile.email,\n          image: profile.images[0]?.url,\n          emailVerified: false,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst twitch = (options) => {\n  return {\n    id: \"twitch\",\n    name: \"Twitch\",\n    createAuthorizationURL({ state, scopes, redirectURI }) {\n      const _scopes = options.disableDefaultScope ? [] : [\"user:read:email\", \"openid\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return createAuthorizationURL({\n        id: \"twitch\",\n        redirectURI,\n        options,\n        authorizationEndpoint: \"https://id.twitch.tv/oauth2/authorize\",\n        scopes: _scopes,\n        state,\n        claims: options.claims || [\n          \"email\",\n          \"email_verified\",\n          \"preferred_username\",\n          \"picture\"\n        ]\n      });\n    },\n    validateAuthorizationCode: async ({ code, redirectURI }) => {\n      return validateAuthorizationCode({\n        code,\n        redirectURI,\n        options,\n        tokenEndpoint: \"https://id.twitch.tv/oauth2/token\"\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://id.twitch.tv/oauth2/token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const idToken = token.idToken;\n      if (!idToken) {\n        logger.error(\"No idToken found in token\");\n        return null;\n      }\n      const profile = decodeJwt(idToken);\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.sub,\n          name: profile.preferred_username,\n          email: profile.email,\n          image: profile.picture,\n          emailVerified: false,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst twitter = (options) => {\n  return {\n    id: \"twitter\",\n    name: \"Twitter\",\n    createAuthorizationURL(data) {\n      const _scopes = options.disableDefaultScope ? [] : [\"users.read\", \"tweet.read\", \"offline.access\", \"users.email\"];\n      options.scope && _scopes.push(...options.scope);\n      data.scopes && _scopes.push(...data.scopes);\n      return createAuthorizationURL({\n        id: \"twitter\",\n        options,\n        authorizationEndpoint: \"https://x.com/i/oauth2/authorize\",\n        scopes: _scopes,\n        state: data.state,\n        codeVerifier: data.codeVerifier,\n        redirectURI: data.redirectURI\n      });\n    },\n    validateAuthorizationCode: async ({ code, codeVerifier, redirectURI }) => {\n      return validateAuthorizationCode({\n        code,\n        codeVerifier,\n        authentication: \"basic\",\n        redirectURI,\n        options,\n        tokenEndpoint: \"https://api.x.com/2/oauth2/token\"\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://api.x.com/2/oauth2/token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data: profile, error: profileError } = await betterFetch(\n        \"https://api.x.com/2/users/me?user.fields=profile_image_url\",\n        {\n          method: \"GET\",\n          headers: {\n            Authorization: `Bearer ${token.accessToken}`\n          }\n        }\n      );\n      if (profileError) {\n        return null;\n      }\n      const { data: emailData, error: emailError } = await betterFetch(\"https://api.x.com/2/users/me?user.fields=confirmed_email\", {\n        method: \"GET\",\n        headers: {\n          Authorization: `Bearer ${token.accessToken}`\n        }\n      });\n      let emailVerified = false;\n      if (!emailError && emailData?.data?.confirmed_email) {\n        profile.data.email = emailData.data.confirmed_email;\n        emailVerified = true;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.data.id,\n          name: profile.data.name,\n          email: profile.data.email || profile.data.username || null,\n          image: profile.data.profile_image_url,\n          emailVerified,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst dropbox = (options) => {\n  const tokenEndpoint = \"https://api.dropboxapi.com/oauth2/token\";\n  return {\n    id: \"dropbox\",\n    name: \"Dropbox\",\n    createAuthorizationURL: async ({\n      state,\n      scopes,\n      codeVerifier,\n      redirectURI\n    }) => {\n      const _scopes = options.disableDefaultScope ? [] : [\"account_info.read\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return await createAuthorizationURL({\n        id: \"dropbox\",\n        options,\n        authorizationEndpoint: \"https://www.dropbox.com/oauth2/authorize\",\n        scopes: _scopes,\n        state,\n        redirectURI,\n        codeVerifier\n      });\n    },\n    validateAuthorizationCode: async ({ code, codeVerifier, redirectURI }) => {\n      return await validateAuthorizationCode({\n        code,\n        codeVerifier,\n        redirectURI,\n        options,\n        tokenEndpoint\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://api.dropbox.com/oauth2/token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data: profile, error } = await betterFetch(\n        \"https://api.dropboxapi.com/2/users/get_current_account\",\n        {\n          method: \"POST\",\n          headers: {\n            Authorization: `Bearer ${token.accessToken}`\n          }\n        }\n      );\n      if (error) {\n        return null;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.account_id,\n          name: profile.name?.display_name,\n          email: profile.email,\n          emailVerified: profile.email_verified || false,\n          image: profile.profile_photo_url,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst linkedin = (options) => {\n  const authorizationEndpoint = \"https://www.linkedin.com/oauth/v2/authorization\";\n  const tokenEndpoint = \"https://www.linkedin.com/oauth/v2/accessToken\";\n  return {\n    id: \"linkedin\",\n    name: \"Linkedin\",\n    createAuthorizationURL: async ({\n      state,\n      scopes,\n      redirectURI,\n      loginHint\n    }) => {\n      const _scopes = options.disableDefaultScope ? [] : [\"profile\", \"email\", \"openid\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return await createAuthorizationURL({\n        id: \"linkedin\",\n        options,\n        authorizationEndpoint,\n        scopes: _scopes,\n        state,\n        loginHint,\n        redirectURI\n      });\n    },\n    validateAuthorizationCode: async ({ code, redirectURI }) => {\n      return await validateAuthorizationCode({\n        code,\n        redirectURI,\n        options,\n        tokenEndpoint\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data: profile, error } = await betterFetch(\n        \"https://api.linkedin.com/v2/userinfo\",\n        {\n          method: \"GET\",\n          headers: {\n            Authorization: `Bearer ${token.accessToken}`\n          }\n        }\n      );\n      if (error) {\n        return null;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.sub,\n          name: profile.name,\n          email: profile.email,\n          emailVerified: profile.email_verified || false,\n          image: profile.picture,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst cleanDoubleSlashes = (input = \"\") => {\n  return input.split(\"://\").map((str) => str.replace(/\\/{2,}/g, \"/\")).join(\"://\");\n};\nconst issuerToEndpoints = (issuer) => {\n  let baseUrl = issuer || \"https://gitlab.com\";\n  return {\n    authorizationEndpoint: cleanDoubleSlashes(`${baseUrl}/oauth/authorize`),\n    tokenEndpoint: cleanDoubleSlashes(`${baseUrl}/oauth/token`),\n    userinfoEndpoint: cleanDoubleSlashes(`${baseUrl}/api/v4/user`)\n  };\n};\nconst gitlab = (options) => {\n  const { authorizationEndpoint, tokenEndpoint, userinfoEndpoint } = issuerToEndpoints(options.issuer);\n  const issuerId = \"gitlab\";\n  const issuerName = \"Gitlab\";\n  return {\n    id: issuerId,\n    name: issuerName,\n    createAuthorizationURL: async ({\n      state,\n      scopes,\n      codeVerifier,\n      loginHint,\n      redirectURI\n    }) => {\n      const _scopes = options.disableDefaultScope ? [] : [\"read_user\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return await createAuthorizationURL({\n        id: issuerId,\n        options,\n        authorizationEndpoint,\n        scopes: _scopes,\n        state,\n        redirectURI,\n        codeVerifier,\n        loginHint\n      });\n    },\n    validateAuthorizationCode: async ({ code, redirectURI, codeVerifier }) => {\n      return validateAuthorizationCode({\n        code,\n        redirectURI,\n        options,\n        codeVerifier,\n        tokenEndpoint\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://gitlab.com/oauth/token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data: profile, error } = await betterFetch(\n        userinfoEndpoint,\n        { headers: { authorization: `Bearer ${token.accessToken}` } }\n      );\n      if (error || profile.state !== \"active\" || profile.locked) {\n        return null;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.id.toString(),\n          name: profile.name ?? profile.username,\n          email: profile.email,\n          image: profile.avatar_url,\n          emailVerified: true,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst tiktok = (options) => {\n  return {\n    id: \"tiktok\",\n    name: \"TikTok\",\n    createAuthorizationURL({ state, scopes, redirectURI }) {\n      const _scopes = options.disableDefaultScope ? [] : [\"user.info.profile\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return new URL(\n        `https://www.tiktok.com/v2/auth/authorize?scope=${_scopes.join(\n          \",\"\n        )}&response_type=code&client_key=${options.clientKey}&client_secret=${options.clientSecret}&redirect_uri=${encodeURIComponent(\n          options.redirectURI || redirectURI\n        )}&state=${state}`\n      );\n    },\n    validateAuthorizationCode: async ({ code, redirectURI }) => {\n      return validateAuthorizationCode({\n        code,\n        redirectURI: options.redirectURI || redirectURI,\n        options,\n        tokenEndpoint: \"https://open.tiktokapis.com/v2/oauth/token/\"\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://open.tiktokapis.com/v2/oauth/token/\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const fields = [\n        \"open_id\",\n        \"avatar_large_url\",\n        \"display_name\",\n        \"username\"\n      ];\n      const { data: profile, error } = await betterFetch(\n        `https://open.tiktokapis.com/v2/user/info/?fields=${fields.join(\",\")}`,\n        {\n          headers: {\n            authorization: `Bearer ${token.accessToken}`\n          }\n        }\n      );\n      if (error) {\n        return null;\n      }\n      return {\n        user: {\n          email: profile.data.user.email || profile.data.user.username,\n          id: profile.data.user.open_id,\n          name: profile.data.user.display_name || profile.data.user.username,\n          image: profile.data.user.avatar_large_url,\n          /** @note Tiktok does not provide emailVerified or even email*/\n          emailVerified: profile.data.user.email ? true : false\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst reddit = (options) => {\n  return {\n    id: \"reddit\",\n    name: \"Reddit\",\n    createAuthorizationURL({ state, scopes, redirectURI }) {\n      const _scopes = options.disableDefaultScope ? [] : [\"identity\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return createAuthorizationURL({\n        id: \"reddit\",\n        options,\n        authorizationEndpoint: \"https://www.reddit.com/api/v1/authorize\",\n        scopes: _scopes,\n        state,\n        redirectURI,\n        duration: options.duration\n      });\n    },\n    validateAuthorizationCode: async ({ code, redirectURI }) => {\n      const body = new URLSearchParams({\n        grant_type: \"authorization_code\",\n        code,\n        redirect_uri: options.redirectURI || redirectURI\n      });\n      const headers = {\n        \"content-type\": \"application/x-www-form-urlencoded\",\n        accept: \"text/plain\",\n        \"user-agent\": \"better-auth\",\n        Authorization: `Basic ${base64.encode(\n          `${options.clientId}:${options.clientSecret}`\n        )}`\n      };\n      const { data, error } = await betterFetch(\n        \"https://www.reddit.com/api/v1/access_token\",\n        {\n          method: \"POST\",\n          headers,\n          body: body.toString()\n        }\n      );\n      if (error) {\n        throw error;\n      }\n      return getOAuth2Tokens(data);\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://www.reddit.com/api/v1/access_token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data: profile, error } = await betterFetch(\n        \"https://oauth.reddit.com/api/v1/me\",\n        {\n          headers: {\n            Authorization: `Bearer ${token.accessToken}`,\n            \"User-Agent\": \"better-auth\"\n          }\n        }\n      );\n      if (error) {\n        return null;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.id,\n          name: profile.name,\n          email: profile.oauth_client_id,\n          emailVerified: profile.has_verified_email,\n          image: profile.icon_img?.split(\"?\")[0],\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst roblox = (options) => {\n  return {\n    id: \"roblox\",\n    name: \"Roblox\",\n    createAuthorizationURL({ state, scopes, redirectURI }) {\n      const _scopes = options.disableDefaultScope ? [] : [\"openid\", \"profile\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return new URL(\n        `https://apis.roblox.com/oauth/v1/authorize?scope=${_scopes.join(\n          \"+\"\n        )}&response_type=code&client_id=${options.clientId}&redirect_uri=${encodeURIComponent(\n          options.redirectURI || redirectURI\n        )}&state=${state}&prompt=${options.prompt || \"select_account+consent\"}`\n      );\n    },\n    validateAuthorizationCode: async ({ code, redirectURI }) => {\n      return validateAuthorizationCode({\n        code,\n        redirectURI: options.redirectURI || redirectURI,\n        options,\n        tokenEndpoint: \"https://apis.roblox.com/oauth/v1/token\",\n        authentication: \"post\"\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://apis.roblox.com/oauth/v1/token\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data: profile, error } = await betterFetch(\n        \"https://apis.roblox.com/oauth/v1/userinfo\",\n        {\n          headers: {\n            authorization: `Bearer ${token.accessToken}`\n          }\n        }\n      );\n      if (error) {\n        return null;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.sub,\n          name: profile.nickname || profile.preferred_username || \"\",\n          image: profile.picture,\n          email: profile.preferred_username || null,\n          // Roblox does not provide email\n          emailVerified: true,\n          ...userMap\n        },\n        data: {\n          ...profile\n        }\n      };\n    },\n    options\n  };\n};\n\nvar LANG = /* @__PURE__ */ ((LANG2) => {\n  LANG2[LANG2[\"RUS\"] = 0] = \"RUS\";\n  LANG2[LANG2[\"UKR\"] = 1] = \"UKR\";\n  LANG2[LANG2[\"ENG\"] = 3] = \"ENG\";\n  LANG2[LANG2[\"SPA\"] = 4] = \"SPA\";\n  LANG2[LANG2[\"GERMAN\"] = 6] = \"GERMAN\";\n  LANG2[LANG2[\"POL\"] = 15] = \"POL\";\n  LANG2[LANG2[\"FRA\"] = 16] = \"FRA\";\n  LANG2[LANG2[\"TURKEY\"] = 82] = \"TURKEY\";\n  return LANG2;\n})(LANG || {});\nconst vk = (options) => {\n  return {\n    id: \"vk\",\n    name: \"VK\",\n    async createAuthorizationURL({ state, scopes, codeVerifier, redirectURI }) {\n      const _scopes = options.disableDefaultScope ? [] : [\"email\", \"phone\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      const authorizationEndpoint = \"https://id.vk.com/authorize\";\n      return createAuthorizationURL({\n        id: \"vk\",\n        options,\n        authorizationEndpoint,\n        scopes: _scopes,\n        state,\n        redirectURI,\n        codeVerifier\n      });\n    },\n    validateAuthorizationCode: async ({\n      code,\n      codeVerifier,\n      redirectURI,\n      deviceId\n    }) => {\n      return validateAuthorizationCode({\n        code,\n        codeVerifier,\n        redirectURI: options.redirectURI || redirectURI,\n        options,\n        deviceId,\n        tokenEndpoint: \"https://id.vk.com/oauth2/auth\"\n      });\n    },\n    refreshAccessToken: options.refreshAccessToken ? options.refreshAccessToken : async (refreshToken) => {\n      return refreshAccessToken({\n        refreshToken,\n        options: {\n          clientId: options.clientId,\n          clientKey: options.clientKey,\n          clientSecret: options.clientSecret\n        },\n        tokenEndpoint: \"https://id.vk.com/oauth2/auth\"\n      });\n    },\n    async getUserInfo(data) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(data);\n      }\n      if (!data.accessToken) {\n        return null;\n      }\n      const formBody = new URLSearchParams({\n        access_token: data.accessToken,\n        client_id: options.clientId\n      }).toString();\n      const { data: profile, error } = await betterFetch(\n        \"https://id.vk.com/oauth2/user_info\",\n        {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n          },\n          body: formBody\n        }\n      );\n      if (error) {\n        return null;\n      }\n      if (!profile.user.email) {\n        return null;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.user.user_id,\n          first_name: profile.user.first_name,\n          last_name: profile.user.last_name,\n          email: profile.user.email,\n          image: profile.user.avatar,\n          /** @note VK does not provide emailVerified*/\n          emailVerified: !!profile.user.email,\n          birthday: profile.user.birthday,\n          sex: profile.user.sex,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst kick = (options) => {\n  return {\n    id: \"kick\",\n    name: \"Kick\",\n    createAuthorizationURL({ state, scopes, redirectURI, codeVerifier }) {\n      const _scopes = options.disableDefaultScope ? [] : [\"user:read\"];\n      options.scope && _scopes.push(...options.scope);\n      scopes && _scopes.push(...scopes);\n      return createAuthorizationURL({\n        id: \"kick\",\n        redirectURI,\n        options,\n        authorizationEndpoint: \"https://id.kick.com/oauth/authorize\",\n        scopes: _scopes,\n        codeVerifier,\n        state\n      });\n    },\n    async validateAuthorizationCode({ code, redirectURI, codeVerifier }) {\n      return validateAuthorizationCode({\n        code,\n        redirectURI,\n        options,\n        tokenEndpoint: \"https://id.kick.com/oauth/token\",\n        codeVerifier\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data, error } = await betterFetch(\"https://api.kick.com/public/v1/users\", {\n        method: \"GET\",\n        headers: {\n          Authorization: `Bearer ${token.accessToken}`\n        }\n      });\n      if (error) {\n        return null;\n      }\n      const profile = data.data[0];\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.user_id,\n          name: profile.name,\n          email: profile.email,\n          image: profile.profile_picture,\n          emailVerified: true,\n          ...userMap\n        },\n        data: profile\n      };\n    },\n    options\n  };\n};\n\nconst zoom = (userOptions) => {\n  const options = {\n    pkce: true,\n    ...userOptions\n  };\n  return {\n    id: \"zoom\",\n    name: \"Zoom\",\n    createAuthorizationURL: async ({ state, redirectURI, codeVerifier }) => {\n      const params = new URLSearchParams({\n        response_type: \"code\",\n        redirect_uri: options.redirectURI ? options.redirectURI : redirectURI,\n        client_id: options.clientId,\n        state\n      });\n      if (options.pkce) {\n        const codeChallenge = await generateCodeChallenge(codeVerifier);\n        params.set(\"code_challenge_method\", \"S256\");\n        params.set(\"code_challenge\", codeChallenge);\n      }\n      const url = new URL(\"https://zoom.us/oauth/authorize\");\n      url.search = params.toString();\n      return url;\n    },\n    validateAuthorizationCode: async ({ code, redirectURI, codeVerifier }) => {\n      return validateAuthorizationCode({\n        code,\n        redirectURI: options.redirectURI || redirectURI,\n        codeVerifier,\n        options,\n        tokenEndpoint: \"https://zoom.us/oauth/token\",\n        authentication: \"post\"\n      });\n    },\n    async getUserInfo(token) {\n      if (options.getUserInfo) {\n        return options.getUserInfo(token);\n      }\n      const { data: profile, error } = await betterFetch(\n        \"https://api.zoom.us/v2/users/me\",\n        {\n          headers: {\n            authorization: `Bearer ${token.accessToken}`\n          }\n        }\n      );\n      if (error) {\n        return null;\n      }\n      const userMap = await options.mapProfileToUser?.(profile);\n      return {\n        user: {\n          id: profile.id,\n          name: profile.display_name,\n          image: profile.pic_url,\n          email: profile.email,\n          emailVerified: Boolean(profile.verified),\n          ...userMap\n        },\n        data: {\n          ...profile\n        }\n      };\n    }\n  };\n};\n\nconst socialProviders = {\n  apple,\n  discord,\n  facebook,\n  github,\n  microsoft,\n  google,\n  huggingface,\n  spotify,\n  twitch,\n  twitter,\n  dropbox,\n  kick,\n  linkedin,\n  gitlab,\n  tiktok,\n  reddit,\n  roblox,\n  vk,\n  zoom\n};\nconst socialProviderList = Object.keys(socialProviders);\nconst SocialProviderListEnum = z.enum(socialProviderList).or(z.string());\n\nexport { LANG, SocialProviderListEnum, apple, discord, dropbox, facebook, getApplePublicKey, github, gitlab, google, huggingface, kick, linkedin, microsoft, reddit, roblox, socialProviderList, socialProviders, spotify, tiktok, twitch, twitter, vk, zoom };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAKA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,QAAQ,CAAC;IACb,MAAM,gBAAgB;IACtB,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;YACzD,MAAM,SAAS,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAS;aAAO;YACnE,QAAQ,KAAK,IAAI,OAAO,IAAI,IAAI,QAAQ,KAAK;YAC7C,UAAU,OAAO,IAAI,IAAI;YACzB,MAAM,MAAM,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBACvC,IAAI;gBACJ;gBACA,uBAAuB;gBACvB,QAAQ;gBACR;gBACA;gBACA,cAAc;gBACd,cAAc;YAChB;YACA,OAAO;QACT;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE;YACnE,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QACA,MAAM,eAAc,KAAK,EAAE,KAAK;YAC9B,IAAI,QAAQ,oBAAoB,EAAE;gBAChC,OAAO;YACT;YACA,IAAI,QAAQ,aAAa,EAAE;gBACzB,OAAO,QAAQ,aAAa,CAAC,OAAO;YACtC;YACA,MAAM,gBAAgB,CAAA,GAAA,gLAAA,CAAA,wBAAqB,AAAD,EAAE;YAC5C,MAAM,EAAE,GAAG,EAAE,KAAK,MAAM,EAAE,GAAG;YAC7B,IAAI,CAAC,OAAO,CAAC,QAAQ,OAAO;YAC5B,MAAM,YAAY,MAAM,kBAAkB;YAC1C,MAAM,EAAE,SAAS,SAAS,EAAE,GAAG,MAAM,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,OAAO,WAAW;gBAC/D,YAAY;oBAAC;iBAAO;gBACpB,QAAQ;gBACR,UAAU,QAAQ,mBAAmB,IAAI,QAAQ,QAAQ;gBACzD,aAAa;YACf;YACA;gBAAC;gBAAkB;aAAmB,CAAC,OAAO,CAAC,CAAC;gBAC9C,IAAI,SAAS,CAAC,MAAM,KAAK,KAAK,GAAG;oBAC/B,SAAS,CAAC,MAAM,GAAG,QAAQ,SAAS,CAAC,MAAM;gBAC7C;YACF;YACA,IAAI,SAAS,UAAU,KAAK,KAAK,OAAO;gBACtC,OAAO;YACT;YACA,OAAO,CAAC,CAAC;QACX;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,IAAI,CAAC,MAAM,OAAO,EAAE;gBAClB,OAAO;YACT;YACA,MAAM,UAAU,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO;YACvC,IAAI,CAAC,SAAS;gBACZ,OAAO;YACT;YACA,MAAM,OAAO,MAAM,IAAI,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,MAAM,IAAI,CAAC,IAAI,EAAE,UAAU,GAAG,QAAQ,IAAI,IAAI,QAAQ,KAAK;YACtH,MAAM,gBAAgB,OAAO,QAAQ,cAAc,KAAK,YAAY,QAAQ,cAAc,GAAG,QAAQ,cAAc,KAAK;YACxH,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,GAAG;oBACf;oBACA;oBACA,OAAO,QAAQ,KAAK;oBACpB,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AACA,MAAM,oBAAoB,OAAO;IAC/B,MAAM,iBAAiB;IACvB,MAAM,iBAAiB;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,GAAG,iBAAiB,gBAAgB;IACvE,IAAI,CAAC,MAAM,MAAM;QACf,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;YAChC,SAAS;QACX;IACF;IACA,MAAM,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,MAAQ,IAAI,GAAG,KAAK;IAChD,IAAI,CAAC,KAAK;QACR,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,IAAI,UAAU,CAAC;IACjD;IACA,OAAO,MAAM,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,KAAK,IAAI,GAAG;AACrC;AAEA,MAAM,UAAU,CAAC;IACf,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;YACnD,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAY;aAAQ;YACxE,UAAU,QAAQ,IAAI,IAAI;YAC1B,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,OAAO,IAAI,IACT,CAAC,+CAA+C,EAAE,QAAQ,IAAI,CAC5D,KACA,8BAA8B,EAAE,QAAQ,QAAQ,CAAC,cAAc,EAAE,mBACjE,QAAQ,WAAW,IAAI,aACvB,OAAO,EAAE,MAAM,QAAQ,EAAE,QAAQ,MAAM,IAAI,QAAQ;QAEzD;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE;YACrD,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA;gBACA,eAAe;YACjB;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,qCACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,IAAI,QAAQ,MAAM,KAAK,MAAM;gBAC3B,MAAM,sBAAsB,QAAQ,aAAa,KAAK,MAAM,OAAO,OAAO,QAAQ,EAAE,KAAK,OAAO,OAAO,IAAI,SAAS,QAAQ,aAAa,IAAI;gBAC7I,QAAQ,SAAS,GAAG,CAAC,yCAAyC,EAAE,oBAAoB,IAAI,CAAC;YAC3F,OAAO;gBACL,MAAM,SAAS,QAAQ,MAAM,CAAC,UAAU,CAAC,QAAQ,QAAQ;gBACzD,QAAQ,SAAS,GAAG,CAAC,mCAAmC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ;YACpG;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,EAAE;oBACd,MAAM,QAAQ,WAAW,IAAI,QAAQ,QAAQ,IAAI;oBACjD,OAAO,QAAQ,KAAK;oBACpB,eAAe,QAAQ,QAAQ;oBAC/B,OAAO,QAAQ,SAAS;oBACxB,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,WAAW,CAAC;IAChB,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE;YACpE,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAS;aAAiB;YAC9E,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAClC,IAAI;gBACJ;gBACA,uBAAuB;gBACvB,QAAQ;gBACR;gBACA;gBACA;gBACA,kBAAkB,QAAQ,QAAQ,GAAG;oBACnC,WAAW,QAAQ,QAAQ;gBAC7B,IAAI,CAAC;YACP;QACF;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE;YACrD,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA;gBACA,eAAe;YACjB;QACF;QACA,MAAM,eAAc,KAAK,EAAE,KAAK;YAC9B,IAAI,QAAQ,oBAAoB,EAAE;gBAChC,OAAO;YACT;YACA,IAAI,QAAQ,aAAa,EAAE;gBACzB,OAAO,QAAQ,aAAa,CAAC,OAAO;YACtC;YACA,IAAI,MAAM,KAAK,CAAC,KAAK,MAAM,EAAE;gBAC3B,IAAI;oBACF,MAAM,EAAE,SAAS,SAAS,EAAE,GAAG,MAAM,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAC3C,OACA,CAAA,GAAA,+JAAA,CAAA,qBAAkB,AAAD,EACf,IAAI,IAAI,4DAEV;wBACE,YAAY;4BAAC;yBAAQ;wBACrB,UAAU,QAAQ,QAAQ;wBAC1B,QAAQ;oBACV;oBAEF,IAAI,SAAS,UAAU,KAAK,KAAK,OAAO;wBACtC,OAAO;oBACT;oBACA,OAAO,CAAC,CAAC;gBACX,EAAE,OAAO,OAAO;oBACd,OAAO;gBACT;YACF;YACA,OAAO;QACT;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,WAAW,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO;gBACxC,MAAM,OAAO;oBACX,IAAI,SAAS,GAAG;oBAChB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,SAAS;wBACP,MAAM;4BACJ,KAAK,SAAS,OAAO;4BACrB,QAAQ;4BACR,OAAO;4BACP,eAAe;wBACjB;oBACF;gBACF;gBACA,MAAM,WAAW,MAAM,QAAQ,gBAAgB,GAAG;oBAChD,GAAG,IAAI;oBACP,gBAAgB;gBAClB;gBACA,OAAO;oBACL,MAAM;wBACJ,GAAG,IAAI;wBACP,eAAe;wBACf,GAAG,QAAQ;oBACb;oBACA,MAAM;gBACR;YACF;YACA,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA;mBACG,SAAS,UAAU,EAAE;aACzB;YACD,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,0CAA0C,OAAO,IAAI,CAAC,MACtD;gBACE,MAAM;oBACJ,MAAM;oBACN,OAAO,MAAM,WAAW;gBAC1B;YACF;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,EAAE;oBACd,MAAM,QAAQ,IAAI;oBAClB,OAAO,QAAQ,KAAK;oBACpB,OAAO,QAAQ,OAAO,CAAC,IAAI,CAAC,GAAG;oBAC/B,eAAe,QAAQ,cAAc;oBACrC,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,SAAS,CAAC;IACd,MAAM,gBAAgB;IACtB,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE;YAC9D,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAa;aAAa;YAC9E,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAC5B,IAAI;gBACJ;gBACA,uBAAuB;gBACvB,QAAQ;gBACR;gBACA;gBACA;gBACA,QAAQ,QAAQ,MAAM;YACxB;QACF;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE;YACrD,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA;gBACA;YACF;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,+BACA;gBACE,SAAS;oBACP,cAAc;oBACd,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,MAAM,EAAE,MAAM,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,sCAAsC;gBAC/E,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;oBAC5C,cAAc;gBAChB;YACF;YACA,IAAI,CAAC,QAAQ,KAAK,IAAI,QAAQ;gBAC5B,QAAQ,KAAK,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO,KAAK,MAAM,CAAC,EAAE,GAAG;YAChE;YACA,MAAM,gBAAgB,QAAQ,KAAK,CAAC,IAAM,EAAE,KAAK,KAAK,QAAQ,KAAK,GAAG,YAAY;YAClF,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,EAAE,CAAC,QAAQ;oBACvB,MAAM,QAAQ,IAAI,IAAI,QAAQ,KAAK;oBACnC,OAAO,QAAQ,KAAK;oBACpB,OAAO,QAAQ,UAAU;oBACzB;oBACA,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,SAAS,CAAC;IACd,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM,wBAAuB,EAC3B,KAAK,EACL,MAAM,EACN,YAAY,EACZ,WAAW,EACX,SAAS,EACT,OAAO,EACR;YACC,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,YAAY,EAAE;gBAC9C,iLAAA,CAAA,IAAM,CAAC,KAAK,CACV;gBAEF,MAAM,IAAI,oLAAA,CAAA,IAAe,CAAC;YAC5B;YACA,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,oLAAA,CAAA,IAAe,CAAC;YAC5B;YACA,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAS;gBAAW;aAAS;YACjF,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,MAAM,MAAM,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBACvC,IAAI;gBACJ;gBACA,uBAAuB;gBACvB,QAAQ;gBACR;gBACA;gBACA;gBACA,QAAQ,QAAQ,MAAM;gBACtB,YAAY,QAAQ,UAAU;gBAC9B,SAAS,WAAW,QAAQ,OAAO;gBACnC;gBACA,IAAI,QAAQ,EAAE;gBACd,kBAAkB;oBAChB,wBAAwB;gBAC1B;YACF;YACA,OAAO;QACT;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE;YACnE,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA;gBACA;gBACA,eAAe;YACjB;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,eAAc,KAAK,EAAE,KAAK;YAC9B,IAAI,QAAQ,oBAAoB,EAAE;gBAChC,OAAO;YACT;YACA,IAAI,QAAQ,aAAa,EAAE;gBACzB,OAAO,QAAQ,aAAa,CAAC,OAAO;YACtC;YACA,MAAM,qBAAqB,CAAC,wDAAwD,EAAE,OAAO;YAC7F,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE;YAC9C,IAAI,CAAC,WAAW;gBACd,OAAO;YACT;YACA,MAAM,UAAU,UAAU,GAAG,KAAK,QAAQ,QAAQ,IAAI,CAAC,UAAU,GAAG,KAAK,iCAAiC,UAAU,GAAG,KAAK,qBAAqB;YACjJ,OAAO;QACT;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,IAAI,CAAC,MAAM,OAAO,EAAE;gBAClB,OAAO;YACT;YACA,MAAM,OAAO,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO;YACpC,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,KAAK,GAAG;oBACZ,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,OAAO;oBACnB,eAAe,KAAK,cAAc;oBAClC,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,cAAc,CAAC;IACnB,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE;YACjE,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAU;gBAAW;aAAQ;YACjF,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAC5B,IAAI;gBACJ;gBACA,uBAAuB;gBACvB,QAAQ;gBACR;gBACA;gBACA;YACF;QACF;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE;YACnE,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA;gBACA;gBACA,eAAe;YACjB;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,yCACA;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,GAAG;oBACf,MAAM,QAAQ,IAAI,IAAI,QAAQ,kBAAkB;oBAChD,OAAO,QAAQ,KAAK;oBACpB,OAAO,QAAQ,OAAO;oBACtB,eAAe,QAAQ,cAAc,IAAI;oBACzC,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,YAAY,CAAC;IACjB,MAAM,SAAS,QAAQ,QAAQ,IAAI;IACnC,MAAM,wBAAwB,CAAC,kCAAkC,EAAE,OAAO,sBAAsB,CAAC;IACjG,MAAM,gBAAgB,CAAC,kCAAkC,EAAE,OAAO,kBAAkB,CAAC;IACrF,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAuB,IAAI;YACzB,MAAM,SAAS,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAU;gBAAW;gBAAS;gBAAa;aAAiB;YAC/G,QAAQ,KAAK,IAAI,OAAO,IAAI,IAAI,QAAQ,KAAK;YAC7C,KAAK,MAAM,IAAI,OAAO,IAAI,IAAI,KAAK,MAAM;YACzC,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAC5B,IAAI;gBACJ;gBACA;gBACA,OAAO,KAAK,KAAK;gBACjB,cAAc,KAAK,YAAY;gBAC/B;gBACA,aAAa,KAAK,WAAW;gBAC7B,QAAQ,QAAQ,MAAM;YACxB;QACF;QACA,2BAA0B,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE;YAC3D,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,IAAI,CAAC,MAAM,OAAO,EAAE;gBAClB,OAAO;YACT;YACA,MAAM,OAAO,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO;YACpC,MAAM,mBAAmB,QAAQ,gBAAgB,IAAI;YACrD,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EACd,CAAC,2CAA2C,EAAE,iBAAiB,CAAC,EAAE,iBAAiB,OAAO,CAAC,EAC3F;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;gBACA,MAAM,YAAW,OAAO;oBACtB,IAAI,QAAQ,mBAAmB,IAAI,CAAC,QAAQ,QAAQ,CAAC,EAAE,EAAE;wBACvD;oBACF;oBACA,IAAI;wBACF,MAAM,WAAW,QAAQ,QAAQ,CAAC,KAAK;wBACvC,MAAM,gBAAgB,MAAM,SAAS,WAAW;wBAChD,MAAM,gBAAgB,8JAAA,CAAA,SAAM,CAAC,MAAM,CAAC;wBACpC,KAAK,OAAO,GAAG,CAAC,wBAAwB,EAAE,eAAe;oBAC3D,EAAE,OAAO,GAAG;wBACV,iLAAA,CAAA,IAAM,CAAC,KAAK,CACV,KAAK,OAAO,MAAM,YAAY,UAAU,IAAI,EAAE,IAAI,GAAG,IACrD;oBAEJ;gBACF;YACF;YAEF,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,KAAK,GAAG;oBACZ,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,OAAO;oBACnB,eAAe;oBACf,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,MAAM,SAAS,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAU;gBAAW;gBAAS;gBAAa;aAAiB;YAC/G,QAAQ,KAAK,IAAI,OAAO,IAAI,IAAI,QAAQ,KAAK;YAC7C,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,cAAc,QAAQ,YAAY;gBACpC;gBACA,aAAa;oBACX,OAAO,OAAO,IAAI,CAAC;gBAErB;gBACA;YACF;QACF;QACA;IACF;AACF;AAEA,MAAM,UAAU,CAAC;IACf,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE;YACjE,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;aAAkB;YACtE,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAC5B,IAAI;gBACJ;gBACA,uBAAuB;gBACvB,QAAQ;gBACR;gBACA;gBACA;YACF;QACF;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE;YACnE,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA;gBACA;gBACA,eAAe;YACjB;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,iCACA;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,EAAE;oBACd,MAAM,QAAQ,YAAY;oBAC1B,OAAO,QAAQ,KAAK;oBACpB,OAAO,QAAQ,MAAM,CAAC,EAAE,EAAE;oBAC1B,eAAe;oBACf,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,SAAS,CAAC;IACd,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;YACnD,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAmB;aAAS;YAChF,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAC5B,IAAI;gBACJ;gBACA;gBACA,uBAAuB;gBACvB,QAAQ;gBACR;gBACA,QAAQ,QAAQ,MAAM,IAAI;oBACxB;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE;YACrD,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA;gBACA,eAAe;YACjB;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,UAAU,MAAM,OAAO;YAC7B,IAAI,CAAC,SAAS;gBACZ,iLAAA,CAAA,IAAM,CAAC,KAAK,CAAC;gBACb,OAAO;YACT;YACA,MAAM,UAAU,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;YAC1B,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,GAAG;oBACf,MAAM,QAAQ,kBAAkB;oBAChC,OAAO,QAAQ,KAAK;oBACpB,OAAO,QAAQ,OAAO;oBACtB,eAAe;oBACf,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,UAAU,CAAC;IACf,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAuB,IAAI;YACzB,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAc;gBAAc;gBAAkB;aAAc;YAChH,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,KAAK,MAAM,IAAI,QAAQ,IAAI,IAAI,KAAK,MAAM;YAC1C,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAC5B,IAAI;gBACJ;gBACA,uBAAuB;gBACvB,QAAQ;gBACR,OAAO,KAAK,KAAK;gBACjB,cAAc,KAAK,YAAY;gBAC/B,aAAa,KAAK,WAAW;YAC/B;QACF;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE;YACnE,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA,gBAAgB;gBAChB;gBACA;gBACA,eAAe;YACjB;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC7D,8DACA;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YAEF,IAAI,cAAc;gBAChB,OAAO;YACT;YACA,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,4DAA4D;gBAC3H,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YACA,IAAI,gBAAgB;YACpB,IAAI,CAAC,cAAc,WAAW,MAAM,iBAAiB;gBACnD,QAAQ,IAAI,CAAC,KAAK,GAAG,UAAU,IAAI,CAAC,eAAe;gBACnD,gBAAgB;YAClB;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,IAAI,CAAC,EAAE;oBACnB,MAAM,QAAQ,IAAI,CAAC,IAAI;oBACvB,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI,QAAQ,IAAI,CAAC,QAAQ,IAAI;oBACtD,OAAO,QAAQ,IAAI,CAAC,iBAAiB;oBACrC;oBACA,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,UAAU,CAAC;IACf,MAAM,gBAAgB;IACtB,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAwB,OAAO,EAC7B,KAAK,EACL,MAAM,EACN,YAAY,EACZ,WAAW,EACZ;YACC,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;aAAoB;YACxE,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAClC,IAAI;gBACJ;gBACA,uBAAuB;gBACvB,QAAQ;gBACR;gBACA;gBACA;YACF;QACF;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE;YACnE,OAAO,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBACrC;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,0DACA;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,UAAU;oBACtB,MAAM,QAAQ,IAAI,EAAE;oBACpB,OAAO,QAAQ,KAAK;oBACpB,eAAe,QAAQ,cAAc,IAAI;oBACzC,OAAO,QAAQ,iBAAiB;oBAChC,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,wBAAwB;IAC9B,MAAM,gBAAgB;IACtB,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAwB,OAAO,EAC7B,KAAK,EACL,MAAM,EACN,WAAW,EACX,SAAS,EACV;YACC,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAW;gBAAS;aAAS;YACjF,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAClC,IAAI;gBACJ;gBACA;gBACA,QAAQ;gBACR;gBACA;gBACA;YACF;QACF;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE;YACrD,OAAO,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBACrC;gBACA;gBACA;gBACA;YACF;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA;YACF;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,wCACA;gBACE,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,GAAG;oBACf,MAAM,QAAQ,IAAI;oBAClB,OAAO,QAAQ,KAAK;oBACpB,eAAe,QAAQ,cAAc,IAAI;oBACzC,OAAO,QAAQ,OAAO;oBACtB,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,qBAAqB,CAAC,QAAQ,EAAE;IACpC,OAAO,MAAM,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,MAAQ,IAAI,OAAO,CAAC,WAAW,MAAM,IAAI,CAAC;AAC3E;AACA,MAAM,oBAAoB,CAAC;IACzB,IAAI,UAAU,UAAU;IACxB,OAAO;QACL,uBAAuB,mBAAmB,GAAG,QAAQ,gBAAgB,CAAC;QACtE,eAAe,mBAAmB,GAAG,QAAQ,YAAY,CAAC;QAC1D,kBAAkB,mBAAmB,GAAG,QAAQ,YAAY,CAAC;IAC/D;AACF;AACA,MAAM,SAAS,CAAC;IACd,MAAM,EAAE,qBAAqB,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,kBAAkB,QAAQ,MAAM;IACnG,MAAM,WAAW;IACjB,MAAM,aAAa;IACnB,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAwB,OAAO,EAC7B,KAAK,EACL,MAAM,EACN,YAAY,EACZ,SAAS,EACT,WAAW,EACZ;YACC,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;aAAY;YAChE,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAClC,IAAI;gBACJ;gBACA;gBACA,QAAQ;gBACR;gBACA;gBACA;gBACA;YACF;QACF;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE;YACnE,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,kBACA;gBAAE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAAC;YAAE;YAE9D,IAAI,SAAS,QAAQ,KAAK,KAAK,YAAY,QAAQ,MAAM,EAAE;gBACzD,OAAO;YACT;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,EAAE,CAAC,QAAQ;oBACvB,MAAM,QAAQ,IAAI,IAAI,QAAQ,QAAQ;oBACtC,OAAO,QAAQ,KAAK;oBACpB,OAAO,QAAQ,UAAU;oBACzB,eAAe;oBACf,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,SAAS,CAAC;IACd,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;YACnD,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;aAAoB;YACxE,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,IAAI,IACT,CAAC,+CAA+C,EAAE,QAAQ,IAAI,CAC5D,KACA,+BAA+B,EAAE,QAAQ,SAAS,CAAC,eAAe,EAAE,QAAQ,YAAY,CAAC,cAAc,EAAE,mBACzG,QAAQ,WAAW,IAAI,aACvB,OAAO,EAAE,OAAO;QAEtB;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE;YACrD,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA,aAAa,QAAQ,WAAW,IAAI;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA;aACD;YACD,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,CAAC,iDAAiD,EAAE,OAAO,IAAI,CAAC,MAAM,EACtE;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,OAAO;gBACL,MAAM;oBACJ,OAAO,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ;oBAC5D,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO;oBAC7B,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ;oBAClE,OAAO,QAAQ,IAAI,CAAC,IAAI,CAAC,gBAAgB;oBACzC,6DAA6D,GAC7D,eAAe,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO;gBAClD;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,SAAS,CAAC;IACd,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;YACnD,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;aAAW;YAC/D,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAC5B,IAAI;gBACJ;gBACA,uBAAuB;gBACvB,QAAQ;gBACR;gBACA;gBACA,UAAU,QAAQ,QAAQ;YAC5B;QACF;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE;YACrD,MAAM,OAAO,IAAI,gBAAgB;gBAC/B,YAAY;gBACZ;gBACA,cAAc,QAAQ,WAAW,IAAI;YACvC;YACA,MAAM,UAAU;gBACd,gBAAgB;gBAChB,QAAQ;gBACR,cAAc;gBACd,eAAe,CAAC,MAAM,EAAE,8JAAA,CAAA,SAAM,CAAC,MAAM,CACnC,GAAG,QAAQ,QAAQ,CAAC,CAAC,EAAE,QAAQ,YAAY,EAAE,GAC5C;YACL;YACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EACtC,8CACA;gBACE,QAAQ;gBACR;gBACA,MAAM,KAAK,QAAQ;YACrB;YAEF,IAAI,OAAO;gBACT,MAAM;YACR;YACA,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAe,AAAD,EAAE;QACzB;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,sCACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;oBAC5C,cAAc;gBAChB;YACF;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,EAAE;oBACd,MAAM,QAAQ,IAAI;oBAClB,OAAO,QAAQ,eAAe;oBAC9B,eAAe,QAAQ,kBAAkB;oBACzC,OAAO,QAAQ,QAAQ,EAAE,MAAM,IAAI,CAAC,EAAE;oBACtC,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,SAAS,CAAC;IACd,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE;YACnD,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAU;aAAU;YACxE,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,IAAI,IACT,CAAC,iDAAiD,EAAE,QAAQ,IAAI,CAC9D,KACA,8BAA8B,EAAE,QAAQ,QAAQ,CAAC,cAAc,EAAE,mBACjE,QAAQ,WAAW,IAAI,aACvB,OAAO,EAAE,MAAM,QAAQ,EAAE,QAAQ,MAAM,IAAI,0BAA0B;QAE3E;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE;YACrD,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA,aAAa,QAAQ,WAAW,IAAI;gBACpC;gBACA,eAAe;gBACf,gBAAgB;YAClB;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,6CACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,GAAG;oBACf,MAAM,QAAQ,QAAQ,IAAI,QAAQ,kBAAkB,IAAI;oBACxD,OAAO,QAAQ,OAAO;oBACtB,OAAO,QAAQ,kBAAkB,IAAI;oBACrC,gCAAgC;oBAChC,eAAe;oBACf,GAAG,OAAO;gBACZ;gBACA,MAAM;oBACJ,GAAG,OAAO;gBACZ;YACF;QACF;QACA;IACF;AACF;AAEA,IAAI,OAAO,aAAa,GAAG,CAAC,CAAC;IAC3B,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG;IAC1B,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG;IAC1B,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG;IAC1B,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG;IAC1B,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,GAAG;IAC7B,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG;IAC3B,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG;IAC3B,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,GAAG;IAC9B,OAAO;AACT,CAAC,EAAE,QAAQ,CAAC;AACZ,MAAM,KAAK,CAAC;IACV,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE;YACvE,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;gBAAS;aAAQ;YACrE,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,MAAM,wBAAwB;YAC9B,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAC5B,IAAI;gBACJ;gBACA;gBACA,QAAQ;gBACR;gBACA;gBACA;YACF;QACF;QACA,2BAA2B,OAAO,EAChC,IAAI,EACJ,YAAY,EACZ,WAAW,EACX,QAAQ,EACT;YACC,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA,aAAa,QAAQ,WAAW,IAAI;gBACpC;gBACA;gBACA,eAAe;YACjB;QACF;QACA,oBAAoB,QAAQ,kBAAkB,GAAG,QAAQ,kBAAkB,GAAG,OAAO;YACnF,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;gBACxB;gBACA,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,cAAc,QAAQ,YAAY;gBACpC;gBACA,eAAe;YACjB;QACF;QACA,MAAM,aAAY,IAAI;YACpB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,IAAI,CAAC,KAAK,WAAW,EAAE;gBACrB,OAAO;YACT;YACA,MAAM,WAAW,IAAI,gBAAgB;gBACnC,cAAc,KAAK,WAAW;gBAC9B,WAAW,QAAQ,QAAQ;YAC7B,GAAG,QAAQ;YACX,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,sCACA;gBACE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM;YACR;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;gBACvB,OAAO;YACT;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,IAAI,CAAC,OAAO;oBACxB,YAAY,QAAQ,IAAI,CAAC,UAAU;oBACnC,WAAW,QAAQ,IAAI,CAAC,SAAS;oBACjC,OAAO,QAAQ,IAAI,CAAC,KAAK;oBACzB,OAAO,QAAQ,IAAI,CAAC,MAAM;oBAC1B,2CAA2C,GAC3C,eAAe,CAAC,CAAC,QAAQ,IAAI,CAAC,KAAK;oBACnC,UAAU,QAAQ,IAAI,CAAC,QAAQ;oBAC/B,KAAK,QAAQ,IAAI,CAAC,GAAG;oBACrB,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,OAAO,CAAC;IACZ,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAuB,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE;YACjE,MAAM,UAAU,QAAQ,mBAAmB,GAAG,EAAE,GAAG;gBAAC;aAAY;YAChE,QAAQ,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK;YAC9C,UAAU,QAAQ,IAAI,IAAI;YAC1B,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;gBAC5B,IAAI;gBACJ;gBACA;gBACA,uBAAuB;gBACvB,QAAQ;gBACR;gBACA;YACF;QACF;QACA,MAAM,2BAA0B,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE;YACjE,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA;gBACA;gBACA,eAAe;gBACf;YACF;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,wCAAwC;gBAChF,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YACA,IAAI,OAAO;gBACT,OAAO;YACT;YACA,MAAM,UAAU,KAAK,IAAI,CAAC,EAAE;YAC5B,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,OAAO;oBACnB,MAAM,QAAQ,IAAI;oBAClB,OAAO,QAAQ,KAAK;oBACpB,OAAO,QAAQ,eAAe;oBAC9B,eAAe;oBACf,GAAG,OAAO;gBACZ;gBACA,MAAM;YACR;QACF;QACA;IACF;AACF;AAEA,MAAM,OAAO,CAAC;IACZ,MAAM,UAAU;QACd,MAAM;QACN,GAAG,WAAW;IAChB;IACA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,wBAAwB,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,YAAY,EAAE;YACjE,MAAM,SAAS,IAAI,gBAAgB;gBACjC,eAAe;gBACf,cAAc,QAAQ,WAAW,GAAG,QAAQ,WAAW,GAAG;gBAC1D,WAAW,QAAQ,QAAQ;gBAC3B;YACF;YACA,IAAI,QAAQ,IAAI,EAAE;gBAChB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAqB,AAAD,EAAE;gBAClD,OAAO,GAAG,CAAC,yBAAyB;gBACpC,OAAO,GAAG,CAAC,kBAAkB;YAC/B;YACA,MAAM,MAAM,IAAI,IAAI;YACpB,IAAI,MAAM,GAAG,OAAO,QAAQ;YAC5B,OAAO;QACT;QACA,2BAA2B,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE;YACnE,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;gBAC/B;gBACA,aAAa,QAAQ,WAAW,IAAI;gBACpC;gBACA;gBACA,eAAe;gBACf,gBAAgB;YAClB;QACF;QACA,MAAM,aAAY,KAAK;YACrB,IAAI,QAAQ,WAAW,EAAE;gBACvB,OAAO,QAAQ,WAAW,CAAC;YAC7B;YACA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/C,mCACA;gBACE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,MAAM,WAAW,EAAE;gBAC9C;YACF;YAEF,IAAI,OAAO;gBACT,OAAO;YACT;YACA,MAAM,UAAU,MAAM,QAAQ,gBAAgB,GAAG;YACjD,OAAO;gBACL,MAAM;oBACJ,IAAI,QAAQ,EAAE;oBACd,MAAM,QAAQ,YAAY;oBAC1B,OAAO,QAAQ,OAAO;oBACtB,OAAO,QAAQ,KAAK;oBACpB,eAAe,QAAQ,QAAQ,QAAQ;oBACvC,GAAG,OAAO;gBACZ;gBACA,MAAM;oBACJ,GAAG,OAAO;gBACZ;YACF;QACF;IACF;AACF;AAEA,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF;AACA,MAAM,qBAAqB,OAAO,IAAI,CAAC;AACvC,MAAM,yBAAyB,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2052, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/better-auth/dist/integrations/next-js.mjs"], "sourcesContent": ["import '../shared/better-auth.8zoxzg-F.mjs';\nimport '@better-auth/utils/base64';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport { parseSetCookieHeader } from '../cookies/index.mjs';\nimport 'better-call';\nimport 'zod';\nimport { c as createAuthMiddleware } from '../shared/better-auth.CSYtKqNt.mjs';\nimport '../shared/better-auth.Cc72UxUH.mjs';\nimport '../shared/better-auth.CdAbNVca.mjs';\nimport '../plugins/organization/access/index.mjs';\nimport '../shared/better-auth.B4Qoxdgc.mjs';\nimport '../shared/better-auth.Cqykj82J.mjs';\nimport 'defu';\nimport '@better-auth/utils/hash';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '@better-auth/utils/otp';\nimport '../plugins/admin/access/index.mjs';\nimport '@better-fetch/fetch';\nimport '@better-auth/utils/random';\nimport '../shared/better-auth.fsvwNeUx.mjs';\nimport 'kysely';\nimport '../shared/better-auth.DdzSJf-n.mjs';\nimport '../shared/better-auth.CW6D9eSx.mjs';\nimport '../shared/better-auth.tB5eU6EY.mjs';\nimport '../shared/better-auth.VTXNLFMT.mjs';\nimport '../shared/better-auth.DPBqdYQ3.mjs';\nimport '../social-providers/index.mjs';\nimport '../shared/better-auth.DufyW0qf.mjs';\nimport '../shared/better-auth.BUPPRXfK.mjs';\nimport '../shared/better-auth.DDEbWX-S.mjs';\nimport 'jose/errors';\nimport '../shared/better-auth.ffWeg50w.mjs';\nimport '../shared/better-auth.OuYYTHC7.mjs';\nimport '../plugins/access/index.mjs';\n\nfunction toNextJsHandler(auth) {\n  const handler = async (request) => {\n    return \"handler\" in auth ? auth.handler(request) : auth(request);\n  };\n  return {\n    GET: handler,\n    POST: handler\n  };\n}\nconst nextCookies = () => {\n  return {\n    id: \"next-cookies\",\n    hooks: {\n      after: [\n        {\n          matcher(ctx) {\n            return true;\n          },\n          handler: createAuthMiddleware(async (ctx) => {\n            const returned = ctx.context.responseHeaders;\n            if (\"_flag\" in ctx && ctx._flag === \"router\") {\n              return;\n            }\n            if (returned instanceof Headers) {\n              const setCookies = returned?.get(\"set-cookie\");\n              if (!setCookies) return;\n              const parsed = parseSetCookieHeader(setCookies);\n              const { cookies } = await import('next/headers');\n              const cookieHelper = await cookies();\n              parsed.forEach((value, key) => {\n                if (!key) return;\n                const opts = {\n                  sameSite: value.samesite,\n                  secure: value.secure,\n                  maxAge: value[\"max-age\"],\n                  httpOnly: value.httponly,\n                  domain: value.domain,\n                  path: value.path\n                };\n                try {\n                  cookieHelper.set(key, decodeURIComponent(value.value), opts);\n                } catch (e) {\n                }\n              });\n              return;\n            }\n          })\n        }\n      ]\n    }\n  };\n};\n\nexport { nextCookies, toNextJsHandler };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAS,gBAAgB,IAAI;IAC3B,MAAM,UAAU,OAAO;QACrB,OAAO,aAAa,OAAO,KAAK,OAAO,CAAC,WAAW,KAAK;IAC1D;IACA,OAAO;QACL,KAAK;QACL,MAAM;IACR;AACF;AACA,MAAM,cAAc;IAClB,OAAO;QACL,IAAI;QACJ,OAAO;YACL,OAAO;gBACL;oBACE,SAAQ,GAAG;wBACT,OAAO;oBACT;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,WAAW,IAAI,OAAO,CAAC,eAAe;wBAC5C,IAAI,WAAW,OAAO,IAAI,KAAK,KAAK,UAAU;4BAC5C;wBACF;wBACA,IAAI,oBAAoB,SAAS;4BAC/B,MAAM,aAAa,UAAU,IAAI;4BACjC,IAAI,CAAC,YAAY;4BACjB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,uBAAoB,AAAD,EAAE;4BACpC,MAAM,EAAE,OAAO,EAAE,GAAG;4BACpB,MAAM,eAAe,MAAM;4BAC3B,OAAO,OAAO,CAAC,CAAC,OAAO;gCACrB,IAAI,CAAC,KAAK;gCACV,MAAM,OAAO;oCACX,UAAU,MAAM,QAAQ;oCACxB,QAAQ,MAAM,MAAM;oCACpB,QAAQ,KAAK,CAAC,UAAU;oCACxB,UAAU,MAAM,QAAQ;oCACxB,QAAQ,MAAM,MAAM;oCACpB,MAAM,MAAM,IAAI;gCAClB;gCACA,IAAI;oCACF,aAAa,GAAG,CAAC,KAAK,mBAAmB,MAAM,KAAK,GAAG;gCACzD,EAAE,OAAO,GAAG,CACZ;4BACF;4BACA;wBACF;oBACF;gBACF;aACD;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/better-auth/dist/api/index.mjs"], "sourcesContent": ["import { APIError, toR<PERSON>ponse, createRouter } from 'better-call';\nexport { APIError } from 'better-call';\nimport { a as createAuthEndpoint, B as BASE_ERROR_CODES, e as createEmailVerificationToken, w as wildcardMatch, l as listSessions, u as updateUser, b as getSession, i as originCheckMiddleware, j as error, k as ok, m as accountInfo, n as getAccessToken, r as refreshToken, p as unlinkAccount, q as deleteUserCallback, t as listUserAccounts, v as linkSocialAccount, x as revokeOtherSessions, y as revokeSessions, z as revokeSession, A as requestPasswordResetCallback, C as requestPasswordReset, D as forgetPasswordCallback, E as deleteUser, F as setPassword, G as changePassword, I as changeEmail, J as sendVerificationEmail, K as verifyEmail, L as resetPassword, M as forgetPassword, N as signInEmail, O as signOut, P as callbackOAuth, Q as signInSocial } from '../shared/better-auth.CSYtKqNt.mjs';\nexport { c as createAuthMiddleware, f as freshSessionMiddleware, g as getSessionFromCtx, S as optionsMiddleware, o as originCheck, R as requestOnlySessionMiddleware, d as sendVerificationEmailFn, s as sessionMiddleware } from '../shared/better-auth.CSYtKqNt.mjs';\nimport { z } from 'zod';\nimport { setSessionCookie } from '../cookies/index.mjs';\nimport { f as parseUserInput } from '../shared/better-auth.Cc72UxUH.mjs';\nimport { b as isDevelopment } from '../shared/better-auth.8zoxzg-F.mjs';\nimport { l as logger } from '../shared/better-auth.Cqykj82J.mjs';\nimport { g as getIp } from '../shared/better-auth.iKoUsdFE.mjs';\nimport defu from 'defu';\nimport '@better-auth/utils/random';\nimport '../shared/better-auth.DPBqdYQ3.mjs';\nimport '@better-auth/utils/hash';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport '@better-auth/utils/base64';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../shared/better-auth.B4Qoxdgc.mjs';\nimport '../social-providers/index.mjs';\nimport '@better-fetch/fetch';\nimport '../shared/better-auth.DufyW0qf.mjs';\nimport '../shared/better-auth.CW6D9eSx.mjs';\nimport '../shared/better-auth.DdzSJf-n.mjs';\nimport '../shared/better-auth.tB5eU6EY.mjs';\nimport '../shared/better-auth.BUPPRXfK.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport '../shared/better-auth.DDEbWX-S.mjs';\nimport '../shared/better-auth.VTXNLFMT.mjs';\nimport 'jose/errors';\n\nconst signUpEmail = () => createAuthEndpoint(\n  \"/sign-up/email\",\n  {\n    method: \"POST\",\n    body: z.record(z.string(), z.any()),\n    metadata: {\n      $Infer: {\n        body: {}\n      },\n      openapi: {\n        description: \"Sign up a user using email and password\",\n        requestBody: {\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                properties: {\n                  name: {\n                    type: \"string\",\n                    description: \"The name of the user\"\n                  },\n                  email: {\n                    type: \"string\",\n                    description: \"The email of the user\"\n                  },\n                  password: {\n                    type: \"string\",\n                    description: \"The password of the user\"\n                  },\n                  callbackURL: {\n                    type: \"string\",\n                    description: \"The URL to use for email verification callback\"\n                  }\n                },\n                required: [\"name\", \"email\", \"password\"]\n              }\n            }\n          }\n        },\n        responses: {\n          \"200\": {\n            description: \"Successfully created user\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    token: {\n                      type: \"string\",\n                      nullable: true,\n                      description: \"Authentication token for the session\"\n                    },\n                    user: {\n                      type: \"object\",\n                      properties: {\n                        id: {\n                          type: \"string\",\n                          description: \"The unique identifier of the user\"\n                        },\n                        email: {\n                          type: \"string\",\n                          format: \"email\",\n                          description: \"The email address of the user\"\n                        },\n                        name: {\n                          type: \"string\",\n                          description: \"The name of the user\"\n                        },\n                        image: {\n                          type: \"string\",\n                          format: \"uri\",\n                          nullable: true,\n                          description: \"The profile image URL of the user\"\n                        },\n                        emailVerified: {\n                          type: \"boolean\",\n                          description: \"Whether the email has been verified\"\n                        },\n                        createdAt: {\n                          type: \"string\",\n                          format: \"date-time\",\n                          description: \"When the user was created\"\n                        },\n                        updatedAt: {\n                          type: \"string\",\n                          format: \"date-time\",\n                          description: \"When the user was last updated\"\n                        }\n                      },\n                      required: [\n                        \"id\",\n                        \"email\",\n                        \"name\",\n                        \"emailVerified\",\n                        \"createdAt\",\n                        \"updatedAt\"\n                      ]\n                    }\n                  },\n                  required: [\"user\"]\n                  // token is optional\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  },\n  async (ctx) => {\n    if (!ctx.context.options.emailAndPassword?.enabled || ctx.context.options.emailAndPassword?.disableSignUp) {\n      throw new APIError(\"BAD_REQUEST\", {\n        message: \"Email and password sign up is not enabled\"\n      });\n    }\n    const body = ctx.body;\n    const { name, email, password, image, callbackURL, ...additionalFields } = body;\n    const isValidEmail = z.string().email().safeParse(email);\n    if (!isValidEmail.success) {\n      throw new APIError(\"BAD_REQUEST\", {\n        message: BASE_ERROR_CODES.INVALID_EMAIL\n      });\n    }\n    const minPasswordLength = ctx.context.password.config.minPasswordLength;\n    if (password.length < minPasswordLength) {\n      ctx.context.logger.error(\"Password is too short\");\n      throw new APIError(\"BAD_REQUEST\", {\n        message: BASE_ERROR_CODES.PASSWORD_TOO_SHORT\n      });\n    }\n    const maxPasswordLength = ctx.context.password.config.maxPasswordLength;\n    if (password.length > maxPasswordLength) {\n      ctx.context.logger.error(\"Password is too long\");\n      throw new APIError(\"BAD_REQUEST\", {\n        message: BASE_ERROR_CODES.PASSWORD_TOO_LONG\n      });\n    }\n    const dbUser = await ctx.context.internalAdapter.findUserByEmail(email);\n    if (dbUser?.user) {\n      ctx.context.logger.info(`Sign-up attempt for existing email: ${email}`);\n      throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n        message: BASE_ERROR_CODES.USER_ALREADY_EXISTS\n      });\n    }\n    const additionalData = parseUserInput(\n      ctx.context.options,\n      additionalFields\n    );\n    const hash = await ctx.context.password.hash(password);\n    let createdUser;\n    try {\n      createdUser = await ctx.context.internalAdapter.createUser(\n        {\n          email: email.toLowerCase(),\n          name,\n          image,\n          ...additionalData,\n          emailVerified: false\n        },\n        ctx\n      );\n      if (!createdUser) {\n        throw new APIError(\"BAD_REQUEST\", {\n          message: BASE_ERROR_CODES.FAILED_TO_CREATE_USER\n        });\n      }\n    } catch (e) {\n      if (isDevelopment) {\n        ctx.context.logger.error(\"Failed to create user\", e);\n      }\n      if (e instanceof APIError) {\n        throw e;\n      }\n      throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n        message: BASE_ERROR_CODES.FAILED_TO_CREATE_USER,\n        details: e\n      });\n    }\n    if (!createdUser) {\n      throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n        message: BASE_ERROR_CODES.FAILED_TO_CREATE_USER\n      });\n    }\n    await ctx.context.internalAdapter.linkAccount(\n      {\n        userId: createdUser.id,\n        providerId: \"credential\",\n        accountId: createdUser.id,\n        password: hash\n      },\n      ctx\n    );\n    if (ctx.context.options.emailVerification?.sendOnSignUp || ctx.context.options.emailAndPassword.requireEmailVerification) {\n      const token = await createEmailVerificationToken(\n        ctx.context.secret,\n        createdUser.email,\n        void 0,\n        ctx.context.options.emailVerification?.expiresIn\n      );\n      const url = `${ctx.context.baseURL}/verify-email?token=${token}&callbackURL=${body.callbackURL || \"/\"}`;\n      await ctx.context.options.emailVerification?.sendVerificationEmail?.(\n        {\n          user: createdUser,\n          url,\n          token\n        },\n        ctx.request\n      );\n    }\n    if (ctx.context.options.emailAndPassword.autoSignIn === false || ctx.context.options.emailAndPassword.requireEmailVerification) {\n      return ctx.json({\n        token: null,\n        user: {\n          id: createdUser.id,\n          email: createdUser.email,\n          name: createdUser.name,\n          image: createdUser.image,\n          emailVerified: createdUser.emailVerified,\n          createdAt: createdUser.createdAt,\n          updatedAt: createdUser.updatedAt\n        }\n      });\n    }\n    const session = await ctx.context.internalAdapter.createSession(\n      createdUser.id,\n      ctx\n    );\n    if (!session) {\n      throw new APIError(\"BAD_REQUEST\", {\n        message: BASE_ERROR_CODES.FAILED_TO_CREATE_SESSION\n      });\n    }\n    await setSessionCookie(ctx, {\n      session,\n      user: createdUser\n    });\n    return ctx.json({\n      token: session.token,\n      user: {\n        id: createdUser.id,\n        email: createdUser.email,\n        name: createdUser.name,\n        image: createdUser.image,\n        emailVerified: createdUser.emailVerified,\n        createdAt: createdUser.createdAt,\n        updatedAt: createdUser.updatedAt\n      }\n    });\n  }\n);\n\nfunction shouldRateLimit(max, window, rateLimitData) {\n  const now = Date.now();\n  const windowInMs = window * 1e3;\n  const timeSinceLastRequest = now - rateLimitData.lastRequest;\n  return timeSinceLastRequest < windowInMs && rateLimitData.count >= max;\n}\nfunction rateLimitResponse(retryAfter) {\n  return new Response(\n    JSON.stringify({\n      message: \"Too many requests. Please try again later.\"\n    }),\n    {\n      status: 429,\n      statusText: \"Too Many Requests\",\n      headers: {\n        \"X-Retry-After\": retryAfter.toString()\n      }\n    }\n  );\n}\nfunction getRetryAfter(lastRequest, window) {\n  const now = Date.now();\n  const windowInMs = window * 1e3;\n  return Math.ceil((lastRequest + windowInMs - now) / 1e3);\n}\nfunction createDBStorage(ctx, modelName) {\n  const model = ctx.options.rateLimit?.modelName || \"rateLimit\";\n  const db = ctx.adapter;\n  return {\n    get: async (key) => {\n      const res = await db.findMany({\n        model,\n        where: [{ field: \"key\", value: key }]\n      });\n      const data = res[0];\n      if (typeof data?.lastRequest === \"bigint\") {\n        data.lastRequest = Number(data.lastRequest);\n      }\n      return data;\n    },\n    set: async (key, value, _update) => {\n      try {\n        if (_update) {\n          await db.updateMany({\n            model: \"rateLimit\",\n            where: [{ field: \"key\", value: key }],\n            update: {\n              count: value.count,\n              lastRequest: value.lastRequest\n            }\n          });\n        } else {\n          await db.create({\n            model: \"rateLimit\",\n            data: {\n              key,\n              count: value.count,\n              lastRequest: value.lastRequest\n            }\n          });\n        }\n      } catch (e) {\n        ctx.logger.error(\"Error setting rate limit\", e);\n      }\n    }\n  };\n}\nconst memory = /* @__PURE__ */ new Map();\nfunction getRateLimitStorage(ctx) {\n  if (ctx.options.rateLimit?.customStorage) {\n    return ctx.options.rateLimit.customStorage;\n  }\n  if (ctx.rateLimit.storage === \"secondary-storage\") {\n    return {\n      get: async (key) => {\n        const stringified = await ctx.options.secondaryStorage?.get(key);\n        return stringified ? JSON.parse(stringified) : void 0;\n      },\n      set: async (key, value) => {\n        await ctx.options.secondaryStorage?.set?.(key, JSON.stringify(value));\n      }\n    };\n  }\n  const storage = ctx.rateLimit.storage;\n  if (storage === \"memory\") {\n    return {\n      async get(key) {\n        return memory.get(key);\n      },\n      async set(key, value, _update) {\n        memory.set(key, value);\n      }\n    };\n  }\n  return createDBStorage(ctx, ctx.rateLimit.modelName);\n}\nasync function onRequestRateLimit(req, ctx) {\n  if (!ctx.rateLimit.enabled) {\n    return;\n  }\n  const path = new URL(req.url).pathname.replace(\n    ctx.options.basePath || \"/api/auth\",\n    \"\"\n  );\n  let window = ctx.rateLimit.window;\n  let max = ctx.rateLimit.max;\n  const ip = getIp(req, ctx.options);\n  if (!ip) {\n    return;\n  }\n  const key = ip + path;\n  const specialRules = getDefaultSpecialRules();\n  const specialRule = specialRules.find((rule) => rule.pathMatcher(path));\n  if (specialRule) {\n    window = specialRule.window;\n    max = specialRule.max;\n  }\n  for (const plugin of ctx.options.plugins || []) {\n    if (plugin.rateLimit) {\n      const matchedRule = plugin.rateLimit.find(\n        (rule) => rule.pathMatcher(path)\n      );\n      if (matchedRule) {\n        window = matchedRule.window;\n        max = matchedRule.max;\n        break;\n      }\n    }\n  }\n  if (ctx.rateLimit.customRules) {\n    const _path = Object.keys(ctx.rateLimit.customRules).find((p) => {\n      if (p.includes(\"*\")) {\n        const isMatch = wildcardMatch(p)(path);\n        return isMatch;\n      }\n      return p === path;\n    });\n    if (_path) {\n      const customRule = ctx.rateLimit.customRules[_path];\n      const resolved = typeof customRule === \"function\" ? await customRule(req) : customRule;\n      if (resolved) {\n        window = resolved.window;\n        max = resolved.max;\n      }\n    }\n  }\n  const storage = getRateLimitStorage(ctx);\n  const data = await storage.get(key);\n  const now = Date.now();\n  if (!data) {\n    await storage.set(key, {\n      key,\n      count: 1,\n      lastRequest: now\n    });\n  } else {\n    const timeSinceLastRequest = now - data.lastRequest;\n    if (shouldRateLimit(max, window, data)) {\n      const retryAfter = getRetryAfter(data.lastRequest, window);\n      return rateLimitResponse(retryAfter);\n    } else if (timeSinceLastRequest > window * 1e3) {\n      await storage.set(\n        key,\n        {\n          ...data,\n          count: 1,\n          lastRequest: now\n        },\n        true\n      );\n    } else {\n      await storage.set(\n        key,\n        {\n          ...data,\n          count: data.count + 1,\n          lastRequest: now\n        },\n        true\n      );\n    }\n  }\n}\nfunction getDefaultSpecialRules() {\n  const specialRules = [\n    {\n      pathMatcher(path) {\n        return path.startsWith(\"/sign-in\") || path.startsWith(\"/sign-up\") || path.startsWith(\"/change-password\") || path.startsWith(\"/change-email\");\n      },\n      window: 10,\n      max: 3\n    }\n  ];\n  return specialRules;\n}\n\nfunction toAuthEndpoints(endpoints, ctx) {\n  const api = {};\n  for (const [key, endpoint] of Object.entries(endpoints)) {\n    api[key] = async (context) => {\n      const authContext = await ctx;\n      let internalContext = {\n        ...context,\n        context: {\n          ...authContext,\n          returned: void 0,\n          responseHeaders: void 0,\n          session: null\n        },\n        path: endpoint.path,\n        headers: context?.headers ? new Headers(context?.headers) : void 0\n      };\n      const { beforeHooks, afterHooks } = getHooks(authContext);\n      const before = await runBeforeHooks(internalContext, beforeHooks);\n      if (\"context\" in before && before.context && typeof before.context === \"object\") {\n        const { headers, ...rest } = before.context;\n        if (headers) {\n          headers.forEach((value, key2) => {\n            internalContext.headers.set(key2, value);\n          });\n        }\n        internalContext = defu(rest, internalContext);\n      } else if (before) {\n        return before;\n      }\n      internalContext.asResponse = false;\n      internalContext.returnHeaders = true;\n      const result = await endpoint(internalContext).catch((e) => {\n        if (e instanceof APIError) {\n          return {\n            response: e,\n            headers: e.headers ? new Headers(e.headers) : null\n          };\n        }\n        throw e;\n      });\n      internalContext.context.returned = result.response;\n      internalContext.context.responseHeaders = result.headers;\n      const after = await runAfterHooks(internalContext, afterHooks);\n      if (after.response) {\n        result.response = after.response;\n      }\n      if (result.response instanceof APIError && !context?.asResponse) {\n        throw result.response;\n      }\n      const response = context?.asResponse ? toResponse(result.response, {\n        headers: result.headers\n      }) : context?.returnHeaders ? {\n        headers: result.headers,\n        response: result.response\n      } : result.response;\n      return response;\n    };\n    api[key].path = endpoint.path;\n    api[key].options = endpoint.options;\n  }\n  return api;\n}\nasync function runBeforeHooks(context, hooks) {\n  let modifiedContext = {};\n  for (const hook of hooks) {\n    if (hook.matcher(context)) {\n      const result = await hook.handler({\n        ...context,\n        returnHeaders: false\n      });\n      if (result && typeof result === \"object\") {\n        if (\"context\" in result && typeof result.context === \"object\") {\n          const { headers, ...rest } = result.context;\n          if (headers instanceof Headers) {\n            if (modifiedContext.headers) {\n              headers.forEach((value, key) => {\n                modifiedContext.headers?.set(key, value);\n              });\n            } else {\n              modifiedContext.headers = headers;\n            }\n          }\n          modifiedContext = defu(rest, modifiedContext);\n          continue;\n        }\n        return result;\n      }\n    }\n  }\n  return { context: modifiedContext };\n}\nasync function runAfterHooks(context, hooks) {\n  for (const hook of hooks) {\n    if (hook.matcher(context)) {\n      const result = await hook.handler(context).catch((e) => {\n        if (e instanceof APIError) {\n          return {\n            response: e,\n            headers: e.headers ? new Headers(e.headers) : null\n          };\n        }\n        throw e;\n      });\n      if (result.headers) {\n        result.headers.forEach((value, key) => {\n          if (!context.context.responseHeaders) {\n            context.context.responseHeaders = new Headers({\n              [key]: value\n            });\n          } else {\n            if (key.toLowerCase() === \"set-cookie\") {\n              context.context.responseHeaders.append(key, value);\n            } else {\n              context.context.responseHeaders.set(key, value);\n            }\n          }\n        });\n      }\n      if (result.response) {\n        context.context.returned = result.response;\n      }\n    }\n  }\n  return {\n    response: context.context.returned,\n    headers: context.context.responseHeaders\n  };\n}\nfunction getHooks(authContext) {\n  const plugins = authContext.options.plugins || [];\n  const beforeHooks = [];\n  const afterHooks = [];\n  if (authContext.options.hooks?.before) {\n    beforeHooks.push({\n      matcher: () => true,\n      handler: authContext.options.hooks.before\n    });\n  }\n  if (authContext.options.hooks?.after) {\n    afterHooks.push({\n      matcher: () => true,\n      handler: authContext.options.hooks.after\n    });\n  }\n  const pluginBeforeHooks = plugins.map((plugin) => {\n    if (plugin.hooks?.before) {\n      return plugin.hooks.before;\n    }\n  }).filter((plugin) => plugin !== void 0).flat();\n  const pluginAfterHooks = plugins.map((plugin) => {\n    if (plugin.hooks?.after) {\n      return plugin.hooks.after;\n    }\n  }).filter((plugin) => plugin !== void 0).flat();\n  pluginBeforeHooks.length && beforeHooks.push(...pluginBeforeHooks);\n  pluginAfterHooks.length && afterHooks.push(...pluginAfterHooks);\n  return {\n    beforeHooks,\n    afterHooks\n  };\n}\n\nfunction getEndpoints(ctx, options) {\n  const pluginEndpoints = options.plugins?.reduce(\n    (acc, plugin) => {\n      return {\n        ...acc,\n        ...plugin.endpoints\n      };\n    },\n    {}\n  );\n  const middlewares = options.plugins?.map(\n    (plugin) => plugin.middlewares?.map((m) => {\n      const middleware = async (context) => {\n        return m.middleware({\n          ...context,\n          context: {\n            ...ctx,\n            ...context.context\n          }\n        });\n      };\n      middleware.options = m.middleware.options;\n      return {\n        path: m.path,\n        middleware\n      };\n    })\n  ).filter((plugin) => plugin !== void 0).flat() || [];\n  const baseEndpoints = {\n    signInSocial,\n    callbackOAuth,\n    getSession: getSession(),\n    signOut,\n    signUpEmail: signUpEmail(),\n    signInEmail,\n    forgetPassword,\n    resetPassword,\n    verifyEmail,\n    sendVerificationEmail,\n    changeEmail,\n    changePassword,\n    setPassword,\n    updateUser: updateUser(),\n    deleteUser,\n    forgetPasswordCallback,\n    requestPasswordReset,\n    requestPasswordResetCallback,\n    listSessions: listSessions(),\n    revokeSession,\n    revokeSessions,\n    revokeOtherSessions,\n    linkSocialAccount,\n    listUserAccounts,\n    deleteUserCallback,\n    unlinkAccount,\n    refreshToken,\n    getAccessToken,\n    accountInfo\n  };\n  const endpoints = {\n    ...baseEndpoints,\n    ...pluginEndpoints,\n    ok,\n    error\n  };\n  const api = toAuthEndpoints(endpoints, ctx);\n  return {\n    api,\n    middlewares\n  };\n}\nconst router = (ctx, options) => {\n  const { api, middlewares } = getEndpoints(ctx, options);\n  const basePath = new URL(ctx.baseURL).pathname;\n  return createRouter(api, {\n    routerContext: ctx,\n    openapi: {\n      disabled: true\n    },\n    basePath,\n    routerMiddleware: [\n      {\n        path: \"/**\",\n        middleware: originCheckMiddleware\n      },\n      ...middlewares\n    ],\n    async onRequest(req) {\n      const disabledPaths = ctx.options.disabledPaths || [];\n      const path = new URL(req.url).pathname.replace(basePath, \"\");\n      if (disabledPaths.includes(path)) {\n        return new Response(\"Not Found\", { status: 404 });\n      }\n      for (const plugin of ctx.options.plugins || []) {\n        if (plugin.onRequest) {\n          const response = await plugin.onRequest(req, ctx);\n          if (response && \"response\" in response) {\n            return response.response;\n          }\n        }\n      }\n      return onRequestRateLimit(req, ctx);\n    },\n    async onResponse(res) {\n      for (const plugin of ctx.options.plugins || []) {\n        if (plugin.onResponse) {\n          const response = await plugin.onResponse(res, ctx);\n          if (response) {\n            return response.response;\n          }\n        }\n      }\n      return res;\n    },\n    onError(e) {\n      if (e instanceof APIError && e.status === \"FOUND\") {\n        return;\n      }\n      if (options.onAPIError?.throw) {\n        throw e;\n      }\n      if (options.onAPIError?.onError) {\n        options.onAPIError.onError(e, ctx);\n        return;\n      }\n      const optLogLevel = options.logger?.level;\n      const log = optLogLevel === \"error\" || optLogLevel === \"warn\" || optLogLevel === \"debug\" ? logger : void 0;\n      if (options.logger?.disabled !== true) {\n        if (e && typeof e === \"object\" && \"message\" in e && typeof e.message === \"string\") {\n          if (e.message.includes(\"no column\") || e.message.includes(\"column\") || e.message.includes(\"relation\") || e.message.includes(\"table\") || e.message.includes(\"does not exist\")) {\n            ctx.logger?.error(e.message);\n            return;\n          }\n        }\n        if (e instanceof APIError) {\n          if (e.status === \"INTERNAL_SERVER_ERROR\") {\n            ctx.logger.error(e.status, e);\n          }\n          log?.error(e.message);\n        } else {\n          ctx.logger?.error(\n            e && typeof e === \"object\" && \"name\" in e ? e.name : \"\",\n            e\n          );\n        }\n      }\n    }\n  });\n};\n\nexport { accountInfo, callbackOAuth, changeEmail, changePassword, createAuthEndpoint, createEmailVerificationToken, deleteUser, deleteUserCallback, error, forgetPassword, forgetPasswordCallback, getAccessToken, getEndpoints, getSession, linkSocialAccount, listSessions, listUserAccounts, ok, originCheckMiddleware, refreshToken, requestPasswordReset, requestPasswordResetCallback, resetPassword, revokeOtherSessions, revokeSession, revokeSessions, router, sendVerificationEmail, setPassword, signInEmail, signInSocial, signOut, signUpEmail, unlinkAccount, updateUser, verifyEmail };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,cAAc,IAAM,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACzC,kBACA;QACE,QAAQ;QACR,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,mLAAA,CAAA,IAAC,CAAC,GAAG;QAChC,UAAU;YACR,QAAQ;gBACN,MAAM,CAAC;YACT;YACA,SAAS;gBACP,aAAa;gBACb,aAAa;oBACX,SAAS;wBACP,oBAAoB;4BAClB,QAAQ;gCACN,MAAM;gCACN,YAAY;oCACV,MAAM;wCACJ,MAAM;wCACN,aAAa;oCACf;oCACA,OAAO;wCACL,MAAM;wCACN,aAAa;oCACf;oCACA,UAAU;wCACR,MAAM;wCACN,aAAa;oCACf;oCACA,aAAa;wCACX,MAAM;wCACN,aAAa;oCACf;gCACF;gCACA,UAAU;oCAAC;oCAAQ;oCAAS;iCAAW;4BACzC;wBACF;oBACF;gBACF;gBACA,WAAW;oBACT,OAAO;wBACL,aAAa;wBACb,SAAS;4BACP,oBAAoB;gCAClB,QAAQ;oCACN,MAAM;oCACN,YAAY;wCACV,OAAO;4CACL,MAAM;4CACN,UAAU;4CACV,aAAa;wCACf;wCACA,MAAM;4CACJ,MAAM;4CACN,YAAY;gDACV,IAAI;oDACF,MAAM;oDACN,aAAa;gDACf;gDACA,OAAO;oDACL,MAAM;oDACN,QAAQ;oDACR,aAAa;gDACf;gDACA,MAAM;oDACJ,MAAM;oDACN,aAAa;gDACf;gDACA,OAAO;oDACL,MAAM;oDACN,QAAQ;oDACR,UAAU;oDACV,aAAa;gDACf;gDACA,eAAe;oDACb,MAAM;oDACN,aAAa;gDACf;gDACA,WAAW;oDACT,MAAM;oDACN,QAAQ;oDACR,aAAa;gDACf;gDACA,WAAW;oDACT,MAAM;oDACN,QAAQ;oDACR,aAAa;gDACf;4CACF;4CACA,UAAU;gDACR;gDACA;gDACA;gDACA;gDACA;gDACA;6CACD;wCACH;oCACF;oCACA,UAAU;wCAAC;qCAAO;gCAEpB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF,GACA,OAAO;QACL,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,eAAe;YACzG,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS;YACX;QACF;QACA,MAAM,OAAO,IAAI,IAAI;QACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,kBAAkB,GAAG;QAC3E,MAAM,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,GAAG,SAAS,CAAC;QAClD,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,aAAa;YACzC;QACF;QACA,MAAM,oBAAoB,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,iBAAiB;QACvE,IAAI,SAAS,MAAM,GAAG,mBAAmB;YACvC,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;YACzB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,kBAAkB;YAC9C;QACF;QACA,MAAM,oBAAoB,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,iBAAiB;QACvE,IAAI,SAAS,MAAM,GAAG,mBAAmB;YACvC,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;YACzB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,iBAAiB;YAC7C;QACF;QACA,MAAM,SAAS,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC;QACjE,IAAI,QAAQ,MAAM;YAChB,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,oCAAoC,EAAE,OAAO;YACtE,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;gBACzC,SAAS,iLAAA,CAAA,IAAgB,CAAC,mBAAmB;YAC/C;QACF;QACA,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,IAAc,AAAD,EAClC,IAAI,OAAO,CAAC,OAAO,EACnB;QAEF,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC7C,IAAI;QACJ,IAAI;YACF,cAAc,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CACxD;gBACE,OAAO,MAAM,WAAW;gBACxB;gBACA;gBACA,GAAG,cAAc;gBACjB,eAAe;YACjB,GACA;YAEF,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;oBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,qBAAqB;gBACjD;YACF;QACF,EAAE,OAAO,GAAG;YACV,IAAI,oLAAA,CAAA,IAAa,EAAE;gBACjB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB;YACpD;YACA,IAAI,aAAa,iJAAA,CAAA,WAAQ,EAAE;gBACzB,MAAM;YACR;YACA,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;gBACzC,SAAS,iLAAA,CAAA,IAAgB,CAAC,qBAAqB;gBAC/C,SAAS;YACX;QACF;QACA,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;gBACzC,SAAS,iLAAA,CAAA,IAAgB,CAAC,qBAAqB;YACjD;QACF;QACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,WAAW,CAC3C;YACE,QAAQ,YAAY,EAAE;YACtB,YAAY;YACZ,WAAW,YAAY,EAAE;YACzB,UAAU;QACZ,GACA;QAEF,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,wBAAwB,EAAE;YACxH,MAAM,QAAQ,MAAM,CAAA,GAAA,iLAAA,CAAA,IAA4B,AAAD,EAC7C,IAAI,OAAO,CAAC,MAAM,EAClB,YAAY,KAAK,EACjB,KAAK,GACL,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAEzC,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,oBAAoB,EAAE,MAAM,aAAa,EAAE,KAAK,WAAW,IAAI,KAAK;YACvG,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,wBAC3C;gBACE,MAAM;gBACN;gBACA;YACF,GACA,IAAI,OAAO;QAEf;QACA,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,UAAU,KAAK,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,wBAAwB,EAAE;YAC9H,OAAO,IAAI,IAAI,CAAC;gBACd,OAAO;gBACP,MAAM;oBACJ,IAAI,YAAY,EAAE;oBAClB,OAAO,YAAY,KAAK;oBACxB,MAAM,YAAY,IAAI;oBACtB,OAAO,YAAY,KAAK;oBACxB,eAAe,YAAY,aAAa;oBACxC,WAAW,YAAY,SAAS;oBAChC,WAAW,YAAY,SAAS;gBAClC;YACF;QACF;QACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7D,YAAY,EAAE,EACd;QAEF,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,wBAAwB;YACpD;QACF;QACA,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;YAC1B;YACA,MAAM;QACR;QACA,OAAO,IAAI,IAAI,CAAC;YACd,OAAO,QAAQ,KAAK;YACpB,MAAM;gBACJ,IAAI,YAAY,EAAE;gBAClB,OAAO,YAAY,KAAK;gBACxB,MAAM,YAAY,IAAI;gBACtB,OAAO,YAAY,KAAK;gBACxB,eAAe,YAAY,aAAa;gBACxC,WAAW,YAAY,SAAS;gBAChC,WAAW,YAAY,SAAS;YAClC;QACF;IACF;AAGF,SAAS,gBAAgB,GAAG,EAAE,MAAM,EAAE,aAAa;IACjD,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,aAAa,SAAS;IAC5B,MAAM,uBAAuB,MAAM,cAAc,WAAW;IAC5D,OAAO,uBAAuB,cAAc,cAAc,KAAK,IAAI;AACrE;AACA,SAAS,kBAAkB,UAAU;IACnC,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;QACb,SAAS;IACX,IACA;QACE,QAAQ;QACR,YAAY;QACZ,SAAS;YACP,iBAAiB,WAAW,QAAQ;QACtC;IACF;AAEJ;AACA,SAAS,cAAc,WAAW,EAAE,MAAM;IACxC,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,aAAa,SAAS;IAC5B,OAAO,KAAK,IAAI,CAAC,CAAC,cAAc,aAAa,GAAG,IAAI;AACtD;AACA,SAAS,gBAAgB,GAAG,EAAE,SAAS;IACrC,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,aAAa;IAClD,MAAM,KAAK,IAAI,OAAO;IACtB,OAAO;QACL,KAAK,OAAO;YACV,MAAM,MAAM,MAAM,GAAG,QAAQ,CAAC;gBAC5B;gBACA,OAAO;oBAAC;wBAAE,OAAO;wBAAO,OAAO;oBAAI;iBAAE;YACvC;YACA,MAAM,OAAO,GAAG,CAAC,EAAE;YACnB,IAAI,OAAO,MAAM,gBAAgB,UAAU;gBACzC,KAAK,WAAW,GAAG,OAAO,KAAK,WAAW;YAC5C;YACA,OAAO;QACT;QACA,KAAK,OAAO,KAAK,OAAO;YACtB,IAAI;gBACF,IAAI,SAAS;oBACX,MAAM,GAAG,UAAU,CAAC;wBAClB,OAAO;wBACP,OAAO;4BAAC;gCAAE,OAAO;gCAAO,OAAO;4BAAI;yBAAE;wBACrC,QAAQ;4BACN,OAAO,MAAM,KAAK;4BAClB,aAAa,MAAM,WAAW;wBAChC;oBACF;gBACF,OAAO;oBACL,MAAM,GAAG,MAAM,CAAC;wBACd,OAAO;wBACP,MAAM;4BACJ;4BACA,OAAO,MAAM,KAAK;4BAClB,aAAa,MAAM,WAAW;wBAChC;oBACF;gBACF;YACF,EAAE,OAAO,GAAG;gBACV,IAAI,MAAM,CAAC,KAAK,CAAC,4BAA4B;YAC/C;QACF;IACF;AACF;AACA,MAAM,SAAS,aAAa,GAAG,IAAI;AACnC,SAAS,oBAAoB,GAAG;IAC9B,IAAI,IAAI,OAAO,CAAC,SAAS,EAAE,eAAe;QACxC,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,aAAa;IAC5C;IACA,IAAI,IAAI,SAAS,CAAC,OAAO,KAAK,qBAAqB;QACjD,OAAO;YACL,KAAK,OAAO;gBACV,MAAM,cAAc,MAAM,IAAI,OAAO,CAAC,gBAAgB,EAAE,IAAI;gBAC5D,OAAO,cAAc,KAAK,KAAK,CAAC,eAAe,KAAK;YACtD;YACA,KAAK,OAAO,KAAK;gBACf,MAAM,IAAI,OAAO,CAAC,gBAAgB,EAAE,MAAM,KAAK,KAAK,SAAS,CAAC;YAChE;QACF;IACF;IACA,MAAM,UAAU,IAAI,SAAS,CAAC,OAAO;IACrC,IAAI,YAAY,UAAU;QACxB,OAAO;YACL,MAAM,KAAI,GAAG;gBACX,OAAO,OAAO,GAAG,CAAC;YACpB;YACA,MAAM,KAAI,GAAG,EAAE,KAAK,EAAE,OAAO;gBAC3B,OAAO,GAAG,CAAC,KAAK;YAClB;QACF;IACF;IACA,OAAO,gBAAgB,KAAK,IAAI,SAAS,CAAC,SAAS;AACrD;AACA,eAAe,mBAAmB,GAAG,EAAE,GAAG;IACxC,IAAI,CAAC,IAAI,SAAS,CAAC,OAAO,EAAE;QAC1B;IACF;IACA,MAAM,OAAO,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC,OAAO,CAC5C,IAAI,OAAO,CAAC,QAAQ,IAAI,aACxB;IAEF,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM;IACjC,IAAI,MAAM,IAAI,SAAS,CAAC,GAAG;IAC3B,MAAM,KAAK,CAAA,GAAA,iLAAA,CAAA,IAAK,AAAD,EAAE,KAAK,IAAI,OAAO;IACjC,IAAI,CAAC,IAAI;QACP;IACF;IACA,MAAM,MAAM,KAAK;IACjB,MAAM,eAAe;IACrB,MAAM,cAAc,aAAa,IAAI,CAAC,CAAC,OAAS,KAAK,WAAW,CAAC;IACjE,IAAI,aAAa;QACf,SAAS,YAAY,MAAM;QAC3B,MAAM,YAAY,GAAG;IACvB;IACA,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE,CAAE;QAC9C,IAAI,OAAO,SAAS,EAAE;YACpB,MAAM,cAAc,OAAO,SAAS,CAAC,IAAI,CACvC,CAAC,OAAS,KAAK,WAAW,CAAC;YAE7B,IAAI,aAAa;gBACf,SAAS,YAAY,MAAM;gBAC3B,MAAM,YAAY,GAAG;gBACrB;YACF;QACF;IACF;IACA,IAAI,IAAI,SAAS,CAAC,WAAW,EAAE;QAC7B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACzD,IAAI,EAAE,QAAQ,CAAC,MAAM;gBACnB,MAAM,UAAU,CAAA,GAAA,iLAAA,CAAA,IAAa,AAAD,EAAE,GAAG;gBACjC,OAAO;YACT;YACA,OAAO,MAAM;QACf;QACA,IAAI,OAAO;YACT,MAAM,aAAa,IAAI,SAAS,CAAC,WAAW,CAAC,MAAM;YACnD,MAAM,WAAW,OAAO,eAAe,aAAa,MAAM,WAAW,OAAO;YAC5E,IAAI,UAAU;gBACZ,SAAS,SAAS,MAAM;gBACxB,MAAM,SAAS,GAAG;YACpB;QACF;IACF;IACA,MAAM,UAAU,oBAAoB;IACpC,MAAM,OAAO,MAAM,QAAQ,GAAG,CAAC;IAC/B,MAAM,MAAM,KAAK,GAAG;IACpB,IAAI,CAAC,MAAM;QACT,MAAM,QAAQ,GAAG,CAAC,KAAK;YACrB;YACA,OAAO;YACP,aAAa;QACf;IACF,OAAO;QACL,MAAM,uBAAuB,MAAM,KAAK,WAAW;QACnD,IAAI,gBAAgB,KAAK,QAAQ,OAAO;YACtC,MAAM,aAAa,cAAc,KAAK,WAAW,EAAE;YACnD,OAAO,kBAAkB;QAC3B,OAAO,IAAI,uBAAuB,SAAS,KAAK;YAC9C,MAAM,QAAQ,GAAG,CACf,KACA;gBACE,GAAG,IAAI;gBACP,OAAO;gBACP,aAAa;YACf,GACA;QAEJ,OAAO;YACL,MAAM,QAAQ,GAAG,CACf,KACA;gBACE,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,GAAG;gBACpB,aAAa;YACf,GACA;QAEJ;IACF;AACF;AACA,SAAS;IACP,MAAM,eAAe;QACnB;YACE,aAAY,IAAI;gBACd,OAAO,KAAK,UAAU,CAAC,eAAe,KAAK,UAAU,CAAC,eAAe,KAAK,UAAU,CAAC,uBAAuB,KAAK,UAAU,CAAC;YAC9H;YACA,QAAQ;YACR,KAAK;QACP;KACD;IACD,OAAO;AACT;AAEA,SAAS,gBAAgB,SAAS,EAAE,GAAG;IACrC,MAAM,MAAM,CAAC;IACb,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,OAAO,OAAO,CAAC,WAAY;QACvD,GAAG,CAAC,IAAI,GAAG,OAAO;YAChB,MAAM,cAAc,MAAM;YAC1B,IAAI,kBAAkB;gBACpB,GAAG,OAAO;gBACV,SAAS;oBACP,GAAG,WAAW;oBACd,UAAU,KAAK;oBACf,iBAAiB,KAAK;oBACtB,SAAS;gBACX;gBACA,MAAM,SAAS,IAAI;gBACnB,SAAS,SAAS,UAAU,IAAI,QAAQ,SAAS,WAAW,KAAK;YACnE;YACA,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,SAAS;YAC7C,MAAM,SAAS,MAAM,eAAe,iBAAiB;YACrD,IAAI,aAAa,UAAU,OAAO,OAAO,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU;gBAC/E,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,GAAG,OAAO,OAAO;gBAC3C,IAAI,SAAS;oBACX,QAAQ,OAAO,CAAC,CAAC,OAAO;wBACtB,gBAAgB,OAAO,CAAC,GAAG,CAAC,MAAM;oBACpC;gBACF;gBACA,kBAAkB,CAAA,GAAA,uIAAA,CAAA,UAAI,AAAD,EAAE,MAAM;YAC/B,OAAO,IAAI,QAAQ;gBACjB,OAAO;YACT;YACA,gBAAgB,UAAU,GAAG;YAC7B,gBAAgB,aAAa,GAAG;YAChC,MAAM,SAAS,MAAM,SAAS,iBAAiB,KAAK,CAAC,CAAC;gBACpD,IAAI,aAAa,iJAAA,CAAA,WAAQ,EAAE;oBACzB,OAAO;wBACL,UAAU;wBACV,SAAS,EAAE,OAAO,GAAG,IAAI,QAAQ,EAAE,OAAO,IAAI;oBAChD;gBACF;gBACA,MAAM;YACR;YACA,gBAAgB,OAAO,CAAC,QAAQ,GAAG,OAAO,QAAQ;YAClD,gBAAgB,OAAO,CAAC,eAAe,GAAG,OAAO,OAAO;YACxD,MAAM,QAAQ,MAAM,cAAc,iBAAiB;YACnD,IAAI,MAAM,QAAQ,EAAE;gBAClB,OAAO,QAAQ,GAAG,MAAM,QAAQ;YAClC;YACA,IAAI,OAAO,QAAQ,YAAY,iJAAA,CAAA,WAAQ,IAAI,CAAC,SAAS,YAAY;gBAC/D,MAAM,OAAO,QAAQ;YACvB;YACA,MAAM,WAAW,SAAS,aAAa,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,OAAO,QAAQ,EAAE;gBACjE,SAAS,OAAO,OAAO;YACzB,KAAK,SAAS,gBAAgB;gBAC5B,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;YAC3B,IAAI,OAAO,QAAQ;YACnB,OAAO;QACT;QACA,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,IAAI;QAC7B,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,SAAS,OAAO;IACrC;IACA,OAAO;AACT;AACA,eAAe,eAAe,OAAO,EAAE,KAAK;IAC1C,IAAI,kBAAkB,CAAC;IACvB,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,OAAO,CAAC,UAAU;YACzB,MAAM,SAAS,MAAM,KAAK,OAAO,CAAC;gBAChC,GAAG,OAAO;gBACV,eAAe;YACjB;YACA,IAAI,UAAU,OAAO,WAAW,UAAU;gBACxC,IAAI,aAAa,UAAU,OAAO,OAAO,OAAO,KAAK,UAAU;oBAC7D,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,GAAG,OAAO,OAAO;oBAC3C,IAAI,mBAAmB,SAAS;wBAC9B,IAAI,gBAAgB,OAAO,EAAE;4BAC3B,QAAQ,OAAO,CAAC,CAAC,OAAO;gCACtB,gBAAgB,OAAO,EAAE,IAAI,KAAK;4BACpC;wBACF,OAAO;4BACL,gBAAgB,OAAO,GAAG;wBAC5B;oBACF;oBACA,kBAAkB,CAAA,GAAA,uIAAA,CAAA,UAAI,AAAD,EAAE,MAAM;oBAC7B;gBACF;gBACA,OAAO;YACT;QACF;IACF;IACA,OAAO;QAAE,SAAS;IAAgB;AACpC;AACA,eAAe,cAAc,OAAO,EAAE,KAAK;IACzC,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,OAAO,CAAC,UAAU;YACzB,MAAM,SAAS,MAAM,KAAK,OAAO,CAAC,SAAS,KAAK,CAAC,CAAC;gBAChD,IAAI,aAAa,iJAAA,CAAA,WAAQ,EAAE;oBACzB,OAAO;wBACL,UAAU;wBACV,SAAS,EAAE,OAAO,GAAG,IAAI,QAAQ,EAAE,OAAO,IAAI;oBAChD;gBACF;gBACA,MAAM;YACR;YACA,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;oBAC7B,IAAI,CAAC,QAAQ,OAAO,CAAC,eAAe,EAAE;wBACpC,QAAQ,OAAO,CAAC,eAAe,GAAG,IAAI,QAAQ;4BAC5C,CAAC,IAAI,EAAE;wBACT;oBACF,OAAO;wBACL,IAAI,IAAI,WAAW,OAAO,cAAc;4BACtC,QAAQ,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK;wBAC9C,OAAO;4BACL,QAAQ,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;wBAC3C;oBACF;gBACF;YACF;YACA,IAAI,OAAO,QAAQ,EAAE;gBACnB,QAAQ,OAAO,CAAC,QAAQ,GAAG,OAAO,QAAQ;YAC5C;QACF;IACF;IACA,OAAO;QACL,UAAU,QAAQ,OAAO,CAAC,QAAQ;QAClC,SAAS,QAAQ,OAAO,CAAC,eAAe;IAC1C;AACF;AACA,SAAS,SAAS,WAAW;IAC3B,MAAM,UAAU,YAAY,OAAO,CAAC,OAAO,IAAI,EAAE;IACjD,MAAM,cAAc,EAAE;IACtB,MAAM,aAAa,EAAE;IACrB,IAAI,YAAY,OAAO,CAAC,KAAK,EAAE,QAAQ;QACrC,YAAY,IAAI,CAAC;YACf,SAAS,IAAM;YACf,SAAS,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM;QAC3C;IACF;IACA,IAAI,YAAY,OAAO,CAAC,KAAK,EAAE,OAAO;QACpC,WAAW,IAAI,CAAC;YACd,SAAS,IAAM;YACf,SAAS,YAAY,OAAO,CAAC,KAAK,CAAC,KAAK;QAC1C;IACF;IACA,MAAM,oBAAoB,QAAQ,GAAG,CAAC,CAAC;QACrC,IAAI,OAAO,KAAK,EAAE,QAAQ;YACxB,OAAO,OAAO,KAAK,CAAC,MAAM;QAC5B;IACF,GAAG,MAAM,CAAC,CAAC,SAAW,WAAW,KAAK,GAAG,IAAI;IAC7C,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAC;QACpC,IAAI,OAAO,KAAK,EAAE,OAAO;YACvB,OAAO,OAAO,KAAK,CAAC,KAAK;QAC3B;IACF,GAAG,MAAM,CAAC,CAAC,SAAW,WAAW,KAAK,GAAG,IAAI;IAC7C,kBAAkB,MAAM,IAAI,YAAY,IAAI,IAAI;IAChD,iBAAiB,MAAM,IAAI,WAAW,IAAI,IAAI;IAC9C,OAAO;QACL;QACA;IACF;AACF;AAEA,SAAS,aAAa,GAAG,EAAE,OAAO;IAChC,MAAM,kBAAkB,QAAQ,OAAO,EAAE,OACvC,CAAC,KAAK;QACJ,OAAO;YACL,GAAG,GAAG;YACN,GAAG,OAAO,SAAS;QACrB;IACF,GACA,CAAC;IAEH,MAAM,cAAc,QAAQ,OAAO,EAAE,IACnC,CAAC,SAAW,OAAO,WAAW,EAAE,IAAI,CAAC;YACnC,MAAM,aAAa,OAAO;gBACxB,OAAO,EAAE,UAAU,CAAC;oBAClB,GAAG,OAAO;oBACV,SAAS;wBACP,GAAG,GAAG;wBACN,GAAG,QAAQ,OAAO;oBACpB;gBACF;YACF;YACA,WAAW,OAAO,GAAG,EAAE,UAAU,CAAC,OAAO;YACzC,OAAO;gBACL,MAAM,EAAE,IAAI;gBACZ;YACF;QACF,IACA,OAAO,CAAC,SAAW,WAAW,KAAK,GAAG,UAAU,EAAE;IACpD,MAAM,gBAAgB;QACpB,cAAA,iLAAA,CAAA,IAAY;QACZ,eAAA,iLAAA,CAAA,IAAa;QACb,YAAY,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD;QACrB,SAAA,iLAAA,CAAA,IAAO;QACP,aAAa;QACb,aAAA,iLAAA,CAAA,IAAW;QACX,gBAAA,iLAAA,CAAA,IAAc;QACd,eAAA,iLAAA,CAAA,IAAa;QACb,aAAA,iLAAA,CAAA,IAAW;QACX,uBAAA,iLAAA,CAAA,IAAqB;QACrB,aAAA,iLAAA,CAAA,IAAW;QACX,gBAAA,iLAAA,CAAA,IAAc;QACd,aAAA,iLAAA,CAAA,IAAW;QACX,YAAY,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD;QACrB,YAAA,iLAAA,CAAA,IAAU;QACV,wBAAA,iLAAA,CAAA,IAAsB;QACtB,sBAAA,iLAAA,CAAA,IAAoB;QACpB,8BAAA,iLAAA,CAAA,IAA4B;QAC5B,cAAc,CAAA,GAAA,iLAAA,CAAA,IAAY,AAAD;QACzB,eAAA,iLAAA,CAAA,IAAa;QACb,gBAAA,iLAAA,CAAA,IAAc;QACd,qBAAA,iLAAA,CAAA,IAAmB;QACnB,mBAAA,iLAAA,CAAA,IAAiB;QACjB,kBAAA,iLAAA,CAAA,IAAgB;QAChB,oBAAA,iLAAA,CAAA,IAAkB;QAClB,eAAA,iLAAA,CAAA,IAAa;QACb,cAAA,iLAAA,CAAA,IAAY;QACZ,gBAAA,iLAAA,CAAA,IAAc;QACd,aAAA,iLAAA,CAAA,IAAW;IACb;IACA,MAAM,YAAY;QAChB,GAAG,aAAa;QAChB,GAAG,eAAe;QAClB,IAAA,iLAAA,CAAA,IAAE;QACF,OAAA,iLAAA,CAAA,IAAK;IACP;IACA,MAAM,MAAM,gBAAgB,WAAW;IACvC,OAAO;QACL;QACA;IACF;AACF;AACA,MAAM,SAAS,CAAC,KAAK;IACnB,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,aAAa,KAAK;IAC/C,MAAM,WAAW,IAAI,IAAI,IAAI,OAAO,EAAE,QAAQ;IAC9C,OAAO,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,KAAK;QACvB,eAAe;QACf,SAAS;YACP,UAAU;QACZ;QACA;QACA,kBAAkB;YAChB;gBACE,MAAM;gBACN,YAAY,iLAAA,CAAA,IAAqB;YACnC;eACG;SACJ;QACD,MAAM,WAAU,GAAG;YACjB,MAAM,gBAAgB,IAAI,OAAO,CAAC,aAAa,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,UAAU;YACzD,IAAI,cAAc,QAAQ,CAAC,OAAO;gBAChC,OAAO,IAAI,SAAS,aAAa;oBAAE,QAAQ;gBAAI;YACjD;YACA,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE,CAAE;gBAC9C,IAAI,OAAO,SAAS,EAAE;oBACpB,MAAM,WAAW,MAAM,OAAO,SAAS,CAAC,KAAK;oBAC7C,IAAI,YAAY,cAAc,UAAU;wBACtC,OAAO,SAAS,QAAQ;oBAC1B;gBACF;YACF;YACA,OAAO,mBAAmB,KAAK;QACjC;QACA,MAAM,YAAW,GAAG;YAClB,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE,CAAE;gBAC9C,IAAI,OAAO,UAAU,EAAE;oBACrB,MAAM,WAAW,MAAM,OAAO,UAAU,CAAC,KAAK;oBAC9C,IAAI,UAAU;wBACZ,OAAO,SAAS,QAAQ;oBAC1B;gBACF;YACF;YACA,OAAO;QACT;QACA,SAAQ,CAAC;YACP,IAAI,aAAa,iJAAA,CAAA,WAAQ,IAAI,EAAE,MAAM,KAAK,SAAS;gBACjD;YACF;YACA,IAAI,QAAQ,UAAU,EAAE,OAAO;gBAC7B,MAAM;YACR;YACA,IAAI,QAAQ,UAAU,EAAE,SAAS;gBAC/B,QAAQ,UAAU,CAAC,OAAO,CAAC,GAAG;gBAC9B;YACF;YACA,MAAM,cAAc,QAAQ,MAAM,EAAE;YACpC,MAAM,MAAM,gBAAgB,WAAW,gBAAgB,UAAU,gBAAgB,UAAU,iLAAA,CAAA,IAAM,GAAG,KAAK;YACzG,IAAI,QAAQ,MAAM,EAAE,aAAa,MAAM;gBACrC,IAAI,KAAK,OAAO,MAAM,YAAY,aAAa,KAAK,OAAO,EAAE,OAAO,KAAK,UAAU;oBACjF,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,OAAO,CAAC,QAAQ,CAAC,eAAe,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,mBAAmB;wBAC5K,IAAI,MAAM,EAAE,MAAM,EAAE,OAAO;wBAC3B;oBACF;gBACF;gBACA,IAAI,aAAa,iJAAA,CAAA,WAAQ,EAAE;oBACzB,IAAI,EAAE,MAAM,KAAK,yBAAyB;wBACxC,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE;oBAC7B;oBACA,KAAK,MAAM,EAAE,OAAO;gBACtB,OAAO;oBACL,IAAI,MAAM,EAAE,MACV,KAAK,OAAO,MAAM,YAAY,UAAU,IAAI,EAAE,IAAI,GAAG,IACrD;gBAEJ;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3027, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/better-auth/dist/index.mjs"], "sourcesContent": ["import { getEndpoints, router } from './api/index.mjs';\nimport { defu } from 'defu';\nimport { v as verifyPassword, h as hashPassword } from './shared/better-auth.OT3XFeFk.mjs';\nimport { a as getAdapter, c as createInternalAdapter, e as getMigrations } from './shared/better-auth.L2-Rkk2U.mjs';\nimport { g as getAuthTables } from './shared/better-auth.DORkW_Ge.mjs';\nimport 'zod';\nimport './shared/better-auth.Cc72UxUH.mjs';\nimport { getCookies, createCookieGetter } from './cookies/index.mjs';\nimport { c as createLogger } from './shared/better-auth.Cqykj82J.mjs';\nexport { a as levels, l as logger, s as shouldPublishLog } from './shared/better-auth.Cqykj82J.mjs';\nimport { socialProviders } from './social-providers/index.mjs';\nimport { g as generateId } from './shared/better-auth.BUPPRXfK.mjs';\nimport 'better-call';\nimport '@better-auth/utils/hash';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport '@better-auth/utils/base64';\nimport 'jose';\nimport './shared/better-auth.B4Qoxdgc.mjs';\nimport { e as env, a as isProduction } from './shared/better-auth.8zoxzg-F.mjs';\nimport { c as checkPassword } from './shared/better-auth.YwDQhoPc.mjs';\nimport { a as getBaseURL, g as getOrigin } from './shared/better-auth.VTXNLFMT.mjs';\nimport { B as BetterAuthError } from './shared/better-auth.DdzSJf-n.mjs';\nexport { M as MissingDependencyError } from './shared/better-auth.DdzSJf-n.mjs';\nimport { B as BASE_ERROR_CODES } from './shared/better-auth.CSYtKqNt.mjs';\nexport { H as HIDE_METADATA } from './shared/better-auth.CSYtKqNt.mjs';\nexport { c as capitalizeFirstLetter } from './shared/better-auth.D-2CmEwz.mjs';\nexport { g as generateState, p as parseState } from './shared/better-auth.DPBqdYQ3.mjs';\nimport './shared/better-auth.iKoUsdFE.mjs';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport './shared/better-auth.CW6D9eSx.mjs';\nimport './shared/better-auth.tB5eU6EY.mjs';\nimport '@better-auth/utils/random';\nimport './shared/better-auth.BVR0McvJ.mjs';\nimport 'kysely';\nimport './shared/better-auth.CHAevr_z.mjs';\nimport './shared/better-auth.DGdvqtd1.mjs';\nimport './shared/better-auth.BkxDNI9v.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport '@better-fetch/fetch';\nimport './shared/better-auth.DufyW0qf.mjs';\nimport './shared/better-auth.DDEbWX-S.mjs';\nimport 'jose/errors';\n\nconst DEFAULT_SECRET = \"better-auth-secret-123456789\";\n\nconst init = async (options) => {\n  const adapter = await getAdapter(options);\n  const plugins = options.plugins || [];\n  const internalPlugins = getInternalPlugins(options);\n  const logger = createLogger(options.logger);\n  const baseURL = getBaseURL(options.baseURL, options.basePath);\n  const secret = options.secret || env.BETTER_AUTH_SECRET || env.AUTH_SECRET || DEFAULT_SECRET;\n  if (secret === DEFAULT_SECRET) {\n    if (isProduction) {\n      logger.error(\n        \"You are using the default secret. Please set `BETTER_AUTH_SECRET` in your environment variables or pass `secret` in your auth config.\"\n      );\n    }\n  }\n  options = {\n    ...options,\n    secret,\n    baseURL: baseURL ? new URL(baseURL).origin : \"\",\n    basePath: options.basePath || \"/api/auth\",\n    plugins: plugins.concat(internalPlugins)\n  };\n  const cookies = getCookies(options);\n  const tables = getAuthTables(options);\n  const providers = Object.keys(options.socialProviders || {}).map((key) => {\n    const value = options.socialProviders?.[key];\n    if (!value || value.enabled === false) {\n      return null;\n    }\n    if (!value.clientId) {\n      logger.warn(\n        `Social provider ${key} is missing clientId or clientSecret`\n      );\n    }\n    const provider = socialProviders[key](\n      value\n      // TODO: fix this\n    );\n    provider.disableImplicitSignUp = value.disableImplicitSignUp;\n    return provider;\n  }).filter((x) => x !== null);\n  const generateIdFunc = ({ model, size }) => {\n    if (typeof options.advanced?.generateId === \"function\") {\n      return options.advanced.generateId({ model, size });\n    }\n    if (typeof options?.advanced?.database?.generateId === \"function\") {\n      return options.advanced.database.generateId({ model, size });\n    }\n    return generateId(size);\n  };\n  const ctx = {\n    appName: options.appName || \"Better Auth\",\n    socialProviders: providers,\n    options,\n    tables,\n    trustedOrigins: getTrustedOrigins(options),\n    baseURL: baseURL || \"\",\n    sessionConfig: {\n      updateAge: options.session?.updateAge !== void 0 ? options.session.updateAge : 24 * 60 * 60,\n      // 24 hours\n      expiresIn: options.session?.expiresIn || 60 * 60 * 24 * 7,\n      // 7 days\n      freshAge: options.session?.freshAge === void 0 ? 60 * 60 * 24 : options.session.freshAge\n    },\n    secret,\n    rateLimit: {\n      ...options.rateLimit,\n      enabled: options.rateLimit?.enabled ?? isProduction,\n      window: options.rateLimit?.window || 10,\n      max: options.rateLimit?.max || 100,\n      storage: options.rateLimit?.storage || (options.secondaryStorage ? \"secondary-storage\" : \"memory\")\n    },\n    authCookies: cookies,\n    logger,\n    generateId: generateIdFunc,\n    session: null,\n    secondaryStorage: options.secondaryStorage,\n    password: {\n      hash: options.emailAndPassword?.password?.hash || hashPassword,\n      verify: options.emailAndPassword?.password?.verify || verifyPassword,\n      config: {\n        minPasswordLength: options.emailAndPassword?.minPasswordLength || 8,\n        maxPasswordLength: options.emailAndPassword?.maxPasswordLength || 128\n      },\n      checkPassword\n    },\n    setNewSession(session) {\n      this.newSession = session;\n    },\n    newSession: null,\n    adapter,\n    internalAdapter: createInternalAdapter(adapter, {\n      options,\n      hooks: options.databaseHooks ? [options.databaseHooks] : []}),\n    createAuthCookie: createCookieGetter(options),\n    async runMigrations() {\n      if (!options.database || \"updateMany\" in options.database) {\n        throw new BetterAuthError(\n          \"Database is not provided or it's an adapter. Migrations are only supported with a database instance.\"\n        );\n      }\n      const { runMigrations } = await getMigrations(options);\n      await runMigrations();\n    }\n  };\n  let { context } = runPluginInit(ctx);\n  return context;\n};\nfunction runPluginInit(ctx) {\n  let options = ctx.options;\n  const plugins = options.plugins || [];\n  let context = ctx;\n  const dbHooks = [];\n  for (const plugin of plugins) {\n    if (plugin.init) {\n      const result = plugin.init(context);\n      if (typeof result === \"object\") {\n        if (result.options) {\n          const { databaseHooks, ...restOpts } = result.options;\n          if (databaseHooks) {\n            dbHooks.push(databaseHooks);\n          }\n          options = defu(options, restOpts);\n        }\n        if (result.context) {\n          context = {\n            ...context,\n            ...result.context\n          };\n        }\n      }\n    }\n  }\n  dbHooks.push(options.databaseHooks);\n  context.internalAdapter = createInternalAdapter(ctx.adapter, {\n    options,\n    hooks: dbHooks.filter((u) => u !== void 0),\n    generateId: ctx.generateId\n  });\n  context.options = options;\n  return { context };\n}\nfunction getInternalPlugins(options) {\n  const plugins = [];\n  if (options.advanced?.crossSubDomainCookies?.enabled) ;\n  return plugins;\n}\nfunction getTrustedOrigins(options) {\n  const baseURL = getBaseURL(options.baseURL, options.basePath);\n  if (!baseURL) {\n    return [];\n  }\n  const trustedOrigins = [new URL(baseURL).origin];\n  if (options.trustedOrigins && Array.isArray(options.trustedOrigins)) {\n    trustedOrigins.push(...options.trustedOrigins);\n  }\n  const envTrustedOrigins = env.BETTER_AUTH_TRUSTED_ORIGINS;\n  if (envTrustedOrigins) {\n    trustedOrigins.push(...envTrustedOrigins.split(\",\"));\n  }\n  if (trustedOrigins.filter((x) => !x).length) {\n    throw new BetterAuthError(\n      \"A provided trusted origin is invalid, make sure your trusted origins list is properly defined.\"\n    );\n  }\n  return trustedOrigins;\n}\n\nconst betterAuth = (options) => {\n  const authContext = init(options);\n  const { api } = getEndpoints(authContext, options);\n  const errorCodes = options.plugins?.reduce((acc, plugin) => {\n    if (plugin.$ERROR_CODES) {\n      return {\n        ...acc,\n        ...plugin.$ERROR_CODES\n      };\n    }\n    return acc;\n  }, {});\n  return {\n    handler: async (request) => {\n      const ctx = await authContext;\n      const basePath = ctx.options.basePath || \"/api/auth\";\n      if (!ctx.options.baseURL) {\n        const baseURL = getBaseURL(void 0, basePath, request);\n        if (baseURL) {\n          ctx.baseURL = baseURL;\n          ctx.options.baseURL = getOrigin(ctx.baseURL) || void 0;\n        } else {\n          throw new BetterAuthError(\n            \"Could not get base URL from request. Please provide a valid base URL.\"\n          );\n        }\n      }\n      ctx.trustedOrigins = [\n        ...options.trustedOrigins ? Array.isArray(options.trustedOrigins) ? options.trustedOrigins : await options.trustedOrigins(request) : [],\n        ctx.options.baseURL\n      ];\n      const { handler } = router(ctx, options);\n      return handler(request);\n    },\n    api,\n    options,\n    $context: authContext,\n    $Infer: {},\n    $ERROR_CODES: {\n      ...errorCodes,\n      ...BASE_ERROR_CODES\n    }\n  };\n};\n\nexport { BetterAuthError, betterAuth, createLogger, generateId };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAIA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,iBAAiB;AAEvB,MAAM,OAAO,OAAO;IAClB,MAAM,UAAU,MAAM,CAAA,GAAA,oLAAA,CAAA,IAAU,AAAD,EAAE;IACjC,MAAM,UAAU,QAAQ,OAAO,IAAI,EAAE;IACrC,MAAM,kBAAkB,mBAAmB;IAC3C,MAAM,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAY,AAAD,EAAE,QAAQ,MAAM;IAC1C,MAAM,UAAU,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD,EAAE,QAAQ,OAAO,EAAE,QAAQ,QAAQ;IAC5D,MAAM,SAAS,QAAQ,MAAM,IAAI,oLAAA,CAAA,IAAG,CAAC,kBAAkB,IAAI,oLAAA,CAAA,IAAG,CAAC,WAAW,IAAI;IAC9E,IAAI,WAAW,gBAAgB;QAC7B,IAAI,oLAAA,CAAA,IAAY,EAAE;YAChB,OAAO,KAAK,CACV;QAEJ;IACF;IACA,UAAU;QACR,GAAG,OAAO;QACV;QACA,SAAS,UAAU,IAAI,IAAI,SAAS,MAAM,GAAG;QAC7C,UAAU,QAAQ,QAAQ,IAAI;QAC9B,SAAS,QAAQ,MAAM,CAAC;IAC1B;IACA,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,MAAM,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAa,AAAD,EAAE;IAC7B,MAAM,YAAY,OAAO,IAAI,CAAC,QAAQ,eAAe,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QAChE,MAAM,QAAQ,QAAQ,eAAe,EAAE,CAAC,IAAI;QAC5C,IAAI,CAAC,SAAS,MAAM,OAAO,KAAK,OAAO;YACrC,OAAO;QACT;QACA,IAAI,CAAC,MAAM,QAAQ,EAAE;YACnB,OAAO,IAAI,CACT,CAAC,gBAAgB,EAAE,IAAI,oCAAoC,CAAC;QAEhE;QACA,MAAM,WAAW,yKAAA,CAAA,kBAAe,CAAC,IAAI,CACnC;QAGF,SAAS,qBAAqB,GAAG,MAAM,qBAAqB;QAC5D,OAAO;IACT,GAAG,MAAM,CAAC,CAAC,IAAM,MAAM;IACvB,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE;QACrC,IAAI,OAAO,QAAQ,QAAQ,EAAE,eAAe,YAAY;YACtD,OAAO,QAAQ,QAAQ,CAAC,UAAU,CAAC;gBAAE;gBAAO;YAAK;QACnD;QACA,IAAI,OAAO,SAAS,UAAU,UAAU,eAAe,YAAY;YACjE,OAAO,QAAQ,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAAE;gBAAO;YAAK;QAC5D;QACA,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD,EAAE;IACpB;IACA,MAAM,MAAM;QACV,SAAS,QAAQ,OAAO,IAAI;QAC5B,iBAAiB;QACjB;QACA;QACA,gBAAgB,kBAAkB;QAClC,SAAS,WAAW;QACpB,eAAe;YACb,WAAW,QAAQ,OAAO,EAAE,cAAc,KAAK,IAAI,QAAQ,OAAO,CAAC,SAAS,GAAG,KAAK,KAAK;YACzF,WAAW;YACX,WAAW,QAAQ,OAAO,EAAE,aAAa,KAAK,KAAK,KAAK;YACxD,SAAS;YACT,UAAU,QAAQ,OAAO,EAAE,aAAa,KAAK,IAAI,KAAK,KAAK,KAAK,QAAQ,OAAO,CAAC,QAAQ;QAC1F;QACA;QACA,WAAW;YACT,GAAG,QAAQ,SAAS;YACpB,SAAS,QAAQ,SAAS,EAAE,WAAW,oLAAA,CAAA,IAAY;YACnD,QAAQ,QAAQ,SAAS,EAAE,UAAU;YACrC,KAAK,QAAQ,SAAS,EAAE,OAAO;YAC/B,SAAS,QAAQ,SAAS,EAAE,WAAW,CAAC,QAAQ,gBAAgB,GAAG,sBAAsB,QAAQ;QACnG;QACA,aAAa;QACb;QACA,YAAY;QACZ,SAAS;QACT,kBAAkB,QAAQ,gBAAgB;QAC1C,UAAU;YACR,MAAM,QAAQ,gBAAgB,EAAE,UAAU,QAAQ,iLAAA,CAAA,IAAY;YAC9D,QAAQ,QAAQ,gBAAgB,EAAE,UAAU,UAAU,iLAAA,CAAA,IAAc;YACpE,QAAQ;gBACN,mBAAmB,QAAQ,gBAAgB,EAAE,qBAAqB;gBAClE,mBAAmB,QAAQ,gBAAgB,EAAE,qBAAqB;YACpE;YACA,eAAA,iLAAA,CAAA,IAAa;QACf;QACA,eAAc,OAAO;YACnB,IAAI,CAAC,UAAU,GAAG;QACpB;QACA,YAAY;QACZ;QACA,iBAAiB,CAAA,GAAA,oLAAA,CAAA,IAAqB,AAAD,EAAE,SAAS;YAC9C;YACA,OAAO,QAAQ,aAAa,GAAG;gBAAC,QAAQ,aAAa;aAAC,GAAG,EAAE;QAAA;QAC7D,kBAAkB,CAAA,GAAA,6JAAA,CAAA,qBAAkB,AAAD,EAAE;QACrC,MAAM;YACJ,IAAI,CAAC,QAAQ,QAAQ,IAAI,gBAAgB,QAAQ,QAAQ,EAAE;gBACzD,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB;YAEJ;YACA,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,CAAA,GAAA,oLAAA,CAAA,IAAa,AAAD,EAAE;YAC9C,MAAM;QACR;IACF;IACA,IAAI,EAAE,OAAO,EAAE,GAAG,cAAc;IAChC,OAAO;AACT;AACA,SAAS,cAAc,GAAG;IACxB,IAAI,UAAU,IAAI,OAAO;IACzB,MAAM,UAAU,QAAQ,OAAO,IAAI,EAAE;IACrC,IAAI,UAAU;IACd,MAAM,UAAU,EAAE;IAClB,KAAK,MAAM,UAAU,QAAS;QAC5B,IAAI,OAAO,IAAI,EAAE;YACf,MAAM,SAAS,OAAO,IAAI,CAAC;YAC3B,IAAI,OAAO,WAAW,UAAU;gBAC9B,IAAI,OAAO,OAAO,EAAE;oBAClB,MAAM,EAAE,aAAa,EAAE,GAAG,UAAU,GAAG,OAAO,OAAO;oBACrD,IAAI,eAAe;wBACjB,QAAQ,IAAI,CAAC;oBACf;oBACA,UAAU,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE,SAAS;gBAC1B;gBACA,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU;wBACR,GAAG,OAAO;wBACV,GAAG,OAAO,OAAO;oBACnB;gBACF;YACF;QACF;IACF;IACA,QAAQ,IAAI,CAAC,QAAQ,aAAa;IAClC,QAAQ,eAAe,GAAG,CAAA,GAAA,oLAAA,CAAA,IAAqB,AAAD,EAAE,IAAI,OAAO,EAAE;QAC3D;QACA,OAAO,QAAQ,MAAM,CAAC,CAAC,IAAM,MAAM,KAAK;QACxC,YAAY,IAAI,UAAU;IAC5B;IACA,QAAQ,OAAO,GAAG;IAClB,OAAO;QAAE;IAAQ;AACnB;AACA,SAAS,mBAAmB,OAAO;IACjC,MAAM,UAAU,EAAE;IAClB,IAAI,QAAQ,QAAQ,EAAE,uBAAuB;IAC7C,OAAO;AACT;AACA,SAAS,kBAAkB,OAAO;IAChC,MAAM,UAAU,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD,EAAE,QAAQ,OAAO,EAAE,QAAQ,QAAQ;IAC5D,IAAI,CAAC,SAAS;QACZ,OAAO,EAAE;IACX;IACA,MAAM,iBAAiB;QAAC,IAAI,IAAI,SAAS,MAAM;KAAC;IAChD,IAAI,QAAQ,cAAc,IAAI,MAAM,OAAO,CAAC,QAAQ,cAAc,GAAG;QACnE,eAAe,IAAI,IAAI,QAAQ,cAAc;IAC/C;IACA,MAAM,oBAAoB,oLAAA,CAAA,IAAG,CAAC,2BAA2B;IACzD,IAAI,mBAAmB;QACrB,eAAe,IAAI,IAAI,kBAAkB,KAAK,CAAC;IACjD;IACA,IAAI,eAAe,MAAM,CAAC,CAAC,IAAM,CAAC,GAAG,MAAM,EAAE;QAC3C,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB;IAEJ;IACA,OAAO;AACT;AAEA,MAAM,aAAa,CAAC;IAClB,MAAM,cAAc,KAAK;IACzB,MAAM,EAAE,GAAG,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,eAAY,AAAD,EAAE,aAAa;IAC1C,MAAM,aAAa,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK;QAC/C,IAAI,OAAO,YAAY,EAAE;YACvB,OAAO;gBACL,GAAG,GAAG;gBACN,GAAG,OAAO,YAAY;YACxB;QACF;QACA,OAAO;IACT,GAAG,CAAC;IACJ,OAAO;QACL,SAAS,OAAO;YACd,MAAM,MAAM,MAAM;YAClB,MAAM,WAAW,IAAI,OAAO,CAAC,QAAQ,IAAI;YACzC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE;gBACxB,MAAM,UAAU,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD,EAAE,KAAK,GAAG,UAAU;gBAC7C,IAAI,SAAS;oBACX,IAAI,OAAO,GAAG;oBACd,IAAI,OAAO,CAAC,OAAO,GAAG,CAAA,GAAA,iLAAA,CAAA,IAAS,AAAD,EAAE,IAAI,OAAO,KAAK,KAAK;gBACvD,OAAO;oBACL,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB;gBAEJ;YACF;YACA,IAAI,cAAc,GAAG;mBAChB,QAAQ,cAAc,GAAG,MAAM,OAAO,CAAC,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,MAAM,QAAQ,cAAc,CAAC,WAAW,EAAE;gBACvI,IAAI,OAAO,CAAC,OAAO;aACpB;YACD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,KAAK;YAChC,OAAO,QAAQ;QACjB;QACA;QACA;QACA,UAAU;QACV,QAAQ,CAAC;QACT,cAAc;YACZ,GAAG,UAAU;YACb,GAAG,iLAAA,CAAA,IAAgB;QACrB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3379, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/better-auth/dist/adapters/drizzle-adapter/index.mjs"], "sourcesContent": ["import { count, desc, asc, sql, eq, inArray, like, lt, lte, ne, gt, gte, and, or } from 'drizzle-orm';\nimport { B as BetterAuthError } from '../../shared/better-auth.DdzSJf-n.mjs';\nimport { c as createAdapter } from '../../shared/better-auth.CHAevr_z.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport '../../shared/better-auth.DGdvqtd1.mjs';\nimport '../../shared/better-auth.DORkW_Ge.mjs';\nimport '../../shared/better-auth.BUPPRXfK.mjs';\nimport '@better-auth/utils/random';\nimport 'zod';\nimport 'better-call';\nimport '@better-auth/utils/hash';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport '@better-auth/utils/base64';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '../../shared/better-auth.Cqykj82J.mjs';\n\nconst drizzleAdapter = (db, config) => createAdapter({\n  config: {\n    adapterId: \"drizzle\",\n    adapterName: \"Drizzle Adapter\",\n    usePlural: config.usePlural ?? false,\n    debugLogs: config.debugLogs ?? false\n  },\n  adapter: ({ getFieldName, debugLog }) => {\n    function getSchema(model) {\n      const schema = config.schema || db._.fullSchema;\n      if (!schema) {\n        throw new BetterAuthError(\n          \"Drizzle adapter failed to initialize. Schema not found. Please provide a schema object in the adapter options object.\"\n        );\n      }\n      const schemaModel = schema[model];\n      if (!schemaModel) {\n        throw new BetterAuthError(\n          `[# Drizzle Adapter]: The model \"${model}\" was not found in the schema object. Please pass the schema directly to the adapter options.`\n        );\n      }\n      return schemaModel;\n    }\n    const withReturning = async (model, builder, data, where) => {\n      if (config.provider !== \"mysql\") {\n        const c = await builder.returning();\n        return c[0];\n      }\n      await builder.execute();\n      const schemaModel = getSchema(model);\n      const builderVal = builder.config?.values;\n      if (where?.length) {\n        const clause = convertWhereClause(where, model);\n        const res = await db.select().from(schemaModel).where(...clause);\n        return res[0];\n      } else if (builderVal && builderVal[0]?.id?.value) {\n        let tId = builderVal[0]?.id?.value;\n        if (!tId) {\n          const lastInsertId = await db.select({ id: sql`LAST_INSERT_ID()` }).from(schemaModel).orderBy(desc(schemaModel.id)).limit(1);\n          tId = lastInsertId[0].id;\n        }\n        const res = await db.select().from(schemaModel).where(eq(schemaModel.id, tId)).limit(1).execute();\n        return res[0];\n      } else if (data.id) {\n        const res = await db.select().from(schemaModel).where(eq(schemaModel.id, data.id)).limit(1).execute();\n        return res[0];\n      } else {\n        if (!(\"id\" in schemaModel)) {\n          throw new BetterAuthError(\n            `The model \"${model}\" does not have an \"id\" field. Please use the \"id\" field as your primary key.`\n          );\n        }\n        const res = await db.select().from(schemaModel).orderBy(desc(schemaModel.id)).limit(1).execute();\n        return res[0];\n      }\n    };\n    function convertWhereClause(where, model) {\n      const schemaModel = getSchema(model);\n      if (!where) return [];\n      if (where.length === 1) {\n        const w = where[0];\n        if (!w) {\n          return [];\n        }\n        const field = getFieldName({ model, field: w.field });\n        if (!schemaModel[field]) {\n          throw new BetterAuthError(\n            `The field \"${w.field}\" does not exist in the schema for the model \"${model}\". Please update your schema.`\n          );\n        }\n        if (w.operator === \"in\") {\n          if (!Array.isArray(w.value)) {\n            throw new BetterAuthError(\n              `The value for the field \"${w.field}\" must be an array when using the \"in\" operator.`\n            );\n          }\n          return [inArray(schemaModel[field], w.value)];\n        }\n        if (w.operator === \"contains\") {\n          return [like(schemaModel[field], `%${w.value}%`)];\n        }\n        if (w.operator === \"starts_with\") {\n          return [like(schemaModel[field], `${w.value}%`)];\n        }\n        if (w.operator === \"ends_with\") {\n          return [like(schemaModel[field], `%${w.value}`)];\n        }\n        if (w.operator === \"lt\") {\n          return [lt(schemaModel[field], w.value)];\n        }\n        if (w.operator === \"lte\") {\n          return [lte(schemaModel[field], w.value)];\n        }\n        if (w.operator === \"ne\") {\n          return [ne(schemaModel[field], w.value)];\n        }\n        if (w.operator === \"gt\") {\n          return [gt(schemaModel[field], w.value)];\n        }\n        if (w.operator === \"gte\") {\n          return [gte(schemaModel[field], w.value)];\n        }\n        return [eq(schemaModel[field], w.value)];\n      }\n      const andGroup = where.filter(\n        (w) => w.connector === \"AND\" || !w.connector\n      );\n      const orGroup = where.filter((w) => w.connector === \"OR\");\n      const andClause = and(\n        ...andGroup.map((w) => {\n          const field = getFieldName({ model, field: w.field });\n          if (w.operator === \"in\") {\n            if (!Array.isArray(w.value)) {\n              throw new BetterAuthError(\n                `The value for the field \"${w.field}\" must be an array when using the \"in\" operator.`\n              );\n            }\n            return inArray(schemaModel[field], w.value);\n          }\n          return eq(schemaModel[field], w.value);\n        })\n      );\n      const orClause = or(\n        ...orGroup.map((w) => {\n          const field = getFieldName({ model, field: w.field });\n          return eq(schemaModel[field], w.value);\n        })\n      );\n      const clause = [];\n      if (andGroup.length) clause.push(andClause);\n      if (orGroup.length) clause.push(orClause);\n      return clause;\n    }\n    function checkMissingFields(schema, model, values) {\n      if (!schema) {\n        throw new BetterAuthError(\n          \"Drizzle adapter failed to initialize. Schema not found. Please provide a schema object in the adapter options object.\"\n        );\n      }\n      for (const key in values) {\n        if (!schema[key]) {\n          throw new BetterAuthError(\n            `The field \"${key}\" does not exist in the \"${model}\" schema. Please update your drizzle schema or re-generate using \"npx @better-auth/cli generate\".`\n          );\n        }\n      }\n    }\n    return {\n      async create({ model, data: values }) {\n        const schemaModel = getSchema(model);\n        checkMissingFields(schemaModel, model, values);\n        const builder = db.insert(schemaModel).values(values);\n        const returned = await withReturning(model, builder, values);\n        return returned;\n      },\n      async findOne({ model, where }) {\n        const schemaModel = getSchema(model);\n        const clause = convertWhereClause(where, model);\n        const res = await db.select().from(schemaModel).where(...clause);\n        if (!res.length) return null;\n        return res[0];\n      },\n      async findMany({ model, where, sortBy, limit, offset }) {\n        const schemaModel = getSchema(model);\n        const clause = where ? convertWhereClause(where, model) : [];\n        const sortFn = sortBy?.direction === \"desc\" ? desc : asc;\n        const builder = db.select().from(schemaModel).limit(limit || 100).offset(offset || 0);\n        if (sortBy?.field) {\n          builder.orderBy(\n            sortFn(\n              schemaModel[getFieldName({ model, field: sortBy?.field })]\n            )\n          );\n        }\n        return await builder.where(...clause);\n      },\n      async count({ model, where }) {\n        const schemaModel = getSchema(model);\n        const clause = where ? convertWhereClause(where, model) : [];\n        const res = await db.select({ count: count() }).from(schemaModel).where(...clause);\n        return res[0].count;\n      },\n      async update({ model, where, update: values }) {\n        const schemaModel = getSchema(model);\n        const clause = convertWhereClause(where, model);\n        const builder = db.update(schemaModel).set(values).where(...clause);\n        return await withReturning(model, builder, values, where);\n      },\n      async updateMany({ model, where, update: values }) {\n        const schemaModel = getSchema(model);\n        const clause = convertWhereClause(where, model);\n        const builder = db.update(schemaModel).set(values).where(...clause);\n        return await builder;\n      },\n      async delete({ model, where }) {\n        const schemaModel = getSchema(model);\n        const clause = convertWhereClause(where, model);\n        const builder = db.delete(schemaModel).where(...clause);\n        return await builder;\n      },\n      async deleteMany({ model, where }) {\n        const schemaModel = getSchema(model);\n        const clause = convertWhereClause(where, model);\n        const builder = db.delete(schemaModel).where(...clause);\n        return await builder;\n      },\n      options: config\n    };\n  }\n});\n\nexport { drizzleAdapter };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,iBAAiB,CAAC,IAAI,SAAW,CAAA,GAAA,iLAAA,CAAA,IAAa,AAAD,EAAE;QACnD,QAAQ;YACN,WAAW;YACX,aAAa;YACb,WAAW,OAAO,SAAS,IAAI;YAC/B,WAAW,OAAO,SAAS,IAAI;QACjC;QACA,SAAS,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE;YAClC,SAAS,UAAU,KAAK;gBACtB,MAAM,SAAS,OAAO,MAAM,IAAI,GAAG,CAAC,CAAC,UAAU;gBAC/C,IAAI,CAAC,QAAQ;oBACX,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB;gBAEJ;gBACA,MAAM,cAAc,MAAM,CAAC,MAAM;gBACjC,IAAI,CAAC,aAAa;oBAChB,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB,CAAC,gCAAgC,EAAE,MAAM,6FAA6F,CAAC;gBAE3I;gBACA,OAAO;YACT;YACA,MAAM,gBAAgB,OAAO,OAAO,SAAS,MAAM;gBACjD,IAAI,OAAO,QAAQ,KAAK,SAAS;oBAC/B,MAAM,IAAI,MAAM,QAAQ,SAAS;oBACjC,OAAO,CAAC,CAAC,EAAE;gBACb;gBACA,MAAM,QAAQ,OAAO;gBACrB,MAAM,cAAc,UAAU;gBAC9B,MAAM,aAAa,QAAQ,MAAM,EAAE;gBACnC,IAAI,OAAO,QAAQ;oBACjB,MAAM,SAAS,mBAAmB,OAAO;oBACzC,MAAM,MAAM,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,aAAa,KAAK,IAAI;oBACzD,OAAO,GAAG,CAAC,EAAE;gBACf,OAAO,IAAI,cAAc,UAAU,CAAC,EAAE,EAAE,IAAI,OAAO;oBACjD,IAAI,MAAM,UAAU,CAAC,EAAE,EAAE,IAAI;oBAC7B,IAAI,CAAC,KAAK;wBACR,MAAM,eAAe,MAAM,GAAG,MAAM,CAAC;4BAAE,IAAI,8IAAA,CAAA,MAAG,CAAC,gBAAgB,CAAC;wBAAC,GAAG,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA,GAAA,gKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,EAAE,GAAG,KAAK,CAAC;wBAC1H,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE;oBAC1B;oBACA,MAAM,MAAM,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,aAAa,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,YAAY,EAAE,EAAE,MAAM,KAAK,CAAC,GAAG,OAAO;oBAC/F,OAAO,GAAG,CAAC,EAAE;gBACf,OAAO,IAAI,KAAK,EAAE,EAAE;oBAClB,MAAM,MAAM,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,aAAa,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,GAAG,OAAO;oBACnG,OAAO,GAAG,CAAC,EAAE;gBACf,OAAO;oBACL,IAAI,CAAC,CAAC,QAAQ,WAAW,GAAG;wBAC1B,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB,CAAC,WAAW,EAAE,MAAM,6EAA6E,CAAC;oBAEtG;oBACA,MAAM,MAAM,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA,GAAA,gKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,EAAE,GAAG,KAAK,CAAC,GAAG,OAAO;oBAC9F,OAAO,GAAG,CAAC,EAAE;gBACf;YACF;YACA,SAAS,mBAAmB,KAAK,EAAE,KAAK;gBACtC,MAAM,cAAc,UAAU;gBAC9B,IAAI,CAAC,OAAO,OAAO,EAAE;gBACrB,IAAI,MAAM,MAAM,KAAK,GAAG;oBACtB,MAAM,IAAI,KAAK,CAAC,EAAE;oBAClB,IAAI,CAAC,GAAG;wBACN,OAAO,EAAE;oBACX;oBACA,MAAM,QAAQ,aAAa;wBAAE;wBAAO,OAAO,EAAE,KAAK;oBAAC;oBACnD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;wBACvB,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,8CAA8C,EAAE,MAAM,6BAA6B,CAAC;oBAE9G;oBACA,IAAI,EAAE,QAAQ,KAAK,MAAM;wBACvB,IAAI,CAAC,MAAM,OAAO,CAAC,EAAE,KAAK,GAAG;4BAC3B,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB,CAAC,yBAAyB,EAAE,EAAE,KAAK,CAAC,gDAAgD,CAAC;wBAEzF;wBACA,OAAO;4BAAC,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK;yBAAE;oBAC/C;oBACA,IAAI,EAAE,QAAQ,KAAK,YAAY;wBAC7B,OAAO;4BAAC,CAAA,GAAA,oKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;yBAAE;oBACnD;oBACA,IAAI,EAAE,QAAQ,KAAK,eAAe;wBAChC,OAAO;4BAAC,CAAA,GAAA,oKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;yBAAE;oBAClD;oBACA,IAAI,EAAE,QAAQ,KAAK,aAAa;wBAC9B,OAAO;4BAAC,CAAA,GAAA,oKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE;yBAAE;oBAClD;oBACA,IAAI,EAAE,QAAQ,KAAK,MAAM;wBACvB,OAAO;4BAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK;yBAAE;oBAC1C;oBACA,IAAI,EAAE,QAAQ,KAAK,OAAO;wBACxB,OAAO;4BAAC,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK;yBAAE;oBAC3C;oBACA,IAAI,EAAE,QAAQ,KAAK,MAAM;wBACvB,OAAO;4BAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK;yBAAE;oBAC1C;oBACA,IAAI,EAAE,QAAQ,KAAK,MAAM;wBACvB,OAAO;4BAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK;yBAAE;oBAC1C;oBACA,IAAI,EAAE,QAAQ,KAAK,OAAO;wBACxB,OAAO;4BAAC,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK;yBAAE;oBAC3C;oBACA,OAAO;wBAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK;qBAAE;gBAC1C;gBACA,MAAM,WAAW,MAAM,MAAM,CAC3B,CAAC,IAAM,EAAE,SAAS,KAAK,SAAS,CAAC,EAAE,SAAS;gBAE9C,MAAM,UAAU,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,KAAK;gBACpD,MAAM,YAAY,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,KACf,SAAS,GAAG,CAAC,CAAC;oBACf,MAAM,QAAQ,aAAa;wBAAE;wBAAO,OAAO,EAAE,KAAK;oBAAC;oBACnD,IAAI,EAAE,QAAQ,KAAK,MAAM;wBACvB,IAAI,CAAC,MAAM,OAAO,CAAC,EAAE,KAAK,GAAG;4BAC3B,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB,CAAC,yBAAyB,EAAE,EAAE,KAAK,CAAC,gDAAgD,CAAC;wBAEzF;wBACA,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK;oBAC5C;oBACA,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK;gBACvC;gBAEF,MAAM,WAAW,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,KACb,QAAQ,GAAG,CAAC,CAAC;oBACd,MAAM,QAAQ,aAAa;wBAAE;wBAAO,OAAO,EAAE,KAAK;oBAAC;oBACnD,OAAO,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,WAAW,CAAC,MAAM,EAAE,EAAE,KAAK;gBACvC;gBAEF,MAAM,SAAS,EAAE;gBACjB,IAAI,SAAS,MAAM,EAAE,OAAO,IAAI,CAAC;gBACjC,IAAI,QAAQ,MAAM,EAAE,OAAO,IAAI,CAAC;gBAChC,OAAO;YACT;YACA,SAAS,mBAAmB,MAAM,EAAE,KAAK,EAAE,MAAM;gBAC/C,IAAI,CAAC,QAAQ;oBACX,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB;gBAEJ;gBACA,IAAK,MAAM,OAAO,OAAQ;oBACxB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;wBAChB,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB,CAAC,WAAW,EAAE,IAAI,yBAAyB,EAAE,MAAM,iGAAiG,CAAC;oBAEzJ;gBACF;YACF;YACA,OAAO;gBACL,MAAM,QAAO,EAAE,KAAK,EAAE,MAAM,MAAM,EAAE;oBAClC,MAAM,cAAc,UAAU;oBAC9B,mBAAmB,aAAa,OAAO;oBACvC,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,MAAM,CAAC;oBAC9C,MAAM,WAAW,MAAM,cAAc,OAAO,SAAS;oBACrD,OAAO;gBACT;gBACA,MAAM,SAAQ,EAAE,KAAK,EAAE,KAAK,EAAE;oBAC5B,MAAM,cAAc,UAAU;oBAC9B,MAAM,SAAS,mBAAmB,OAAO;oBACzC,MAAM,MAAM,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,aAAa,KAAK,IAAI;oBACzD,IAAI,CAAC,IAAI,MAAM,EAAE,OAAO;oBACxB,OAAO,GAAG,CAAC,EAAE;gBACf;gBACA,MAAM,UAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;oBACpD,MAAM,cAAc,UAAU;oBAC9B,MAAM,SAAS,QAAQ,mBAAmB,OAAO,SAAS,EAAE;oBAC5D,MAAM,SAAS,QAAQ,cAAc,SAAS,gKAAA,CAAA,OAAI,GAAG,gKAAA,CAAA,MAAG;oBACxD,MAAM,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC,aAAa,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC,UAAU;oBACnF,IAAI,QAAQ,OAAO;wBACjB,QAAQ,OAAO,CACb,OACE,WAAW,CAAC,aAAa;4BAAE;4BAAO,OAAO,QAAQ;wBAAM,GAAG;oBAGhE;oBACA,OAAO,MAAM,QAAQ,KAAK,IAAI;gBAChC;gBACA,MAAM,OAAM,EAAE,KAAK,EAAE,KAAK,EAAE;oBAC1B,MAAM,cAAc,UAAU;oBAC9B,MAAM,SAAS,QAAQ,mBAAmB,OAAO,SAAS,EAAE;oBAC5D,MAAM,MAAM,MAAM,GAAG,MAAM,CAAC;wBAAE,OAAO,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD;oBAAI,GAAG,IAAI,CAAC,aAAa,KAAK,IAAI;oBAC3E,OAAO,GAAG,CAAC,EAAE,CAAC,KAAK;gBACrB;gBACA,MAAM,QAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,MAAM,EAAE;oBAC3C,MAAM,cAAc,UAAU;oBAC9B,MAAM,SAAS,mBAAmB,OAAO;oBACzC,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,GAAG,CAAC,QAAQ,KAAK,IAAI;oBAC5D,OAAO,MAAM,cAAc,OAAO,SAAS,QAAQ;gBACrD;gBACA,MAAM,YAAW,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,MAAM,EAAE;oBAC/C,MAAM,cAAc,UAAU;oBAC9B,MAAM,SAAS,mBAAmB,OAAO;oBACzC,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,GAAG,CAAC,QAAQ,KAAK,IAAI;oBAC5D,OAAO,MAAM;gBACf;gBACA,MAAM,QAAO,EAAE,KAAK,EAAE,KAAK,EAAE;oBAC3B,MAAM,cAAc,UAAU;oBAC9B,MAAM,SAAS,mBAAmB,OAAO;oBACzC,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,KAAK,IAAI;oBAChD,OAAO,MAAM;gBACf;gBACA,MAAM,YAAW,EAAE,KAAK,EAAE,KAAK,EAAE;oBAC/B,MAAM,cAAc,UAAU;oBAC9B,MAAM,SAAS,mBAAmB,OAAO;oBACzC,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,KAAK,IAAI;oBAChD,OAAO,MAAM;gBACf;gBACA,SAAS;YACX;QACF;IACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3650, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/better-auth/dist/crypto/index.mjs"], "sourcesContent": ["import { createHash } from '@better-auth/utils/hash';\nimport { xchacha20poly1305 } from '@noble/ciphers/chacha';\nimport { utf8ToBytes, bytesToHex, hexToBytes } from '@noble/ciphers/utils';\nimport { managedNonce } from '@noble/ciphers/webcrypto';\nimport { c as constantTimeEqual } from '../shared/better-auth.OT3XFeFk.mjs';\nexport { h as hashPassword, v as verifyPassword } from '../shared/better-auth.OT3XFeFk.mjs';\nimport { base64 } from '@better-auth/utils/base64';\nexport { s as signJWT } from '../shared/better-auth.DDEbWX-S.mjs';\nexport { g as generateRandomString } from '../shared/better-auth.B4Qoxdgc.mjs';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport 'jose';\nimport '@better-auth/utils/random';\n\nasync function hashToBase64(data) {\n  const buffer = await createHash(\"SHA-256\").digest(data);\n  return base64.encode(buffer);\n}\nasync function compareHash(data, hash) {\n  const buffer = await createHash(\"SHA-256\").digest(\n    typeof data === \"string\" ? new TextEncoder().encode(data) : data\n  );\n  const hashBuffer = base64.decode(hash);\n  return constantTimeEqual(buffer, hashBuffer);\n}\n\nconst symmetricEncrypt = async ({\n  key,\n  data\n}) => {\n  const keyAsBytes = await createHash(\"SHA-256\").digest(key);\n  const dataAsBytes = utf8ToBytes(data);\n  const chacha = managedNonce(xchacha20poly1305)(new Uint8Array(keyAsBytes));\n  return bytesToHex(chacha.encrypt(dataAsBytes));\n};\nconst symmetricDecrypt = async ({\n  key,\n  data\n}) => {\n  const keyAsBytes = await createHash(\"SHA-256\").digest(key);\n  const dataAsBytes = hexToBytes(data);\n  const chacha = managedNonce(xchacha20poly1305)(new Uint8Array(keyAsBytes));\n  return new TextDecoder().decode(chacha.decrypt(dataAsBytes));\n};\n\nexport { compareHash, constantTimeEqual, hashToBase64, symmetricDecrypt, symmetricEncrypt };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAGA;;;;;;;;;;;;;;;;AAEA,eAAe,aAAa,IAAI;IAC9B,MAAM,SAAS,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAAC;IAClD,OAAO,8JAAA,CAAA,SAAM,CAAC,MAAM,CAAC;AACvB;AACA,eAAe,YAAY,IAAI,EAAE,IAAI;IACnC,MAAM,SAAS,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAC/C,OAAO,SAAS,WAAW,IAAI,cAAc,MAAM,CAAC,QAAQ;IAE9D,MAAM,aAAa,8JAAA,CAAA,SAAM,CAAC,MAAM,CAAC;IACjC,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAiB,AAAD,EAAE,QAAQ;AACnC;AAEA,MAAM,mBAAmB,OAAO,EAC9B,GAAG,EACH,IAAI,EACL;IACC,MAAM,aAAa,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAAC;IACtD,MAAM,cAAc,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE;IAChC,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,qJAAA,CAAA,oBAAiB,EAAE,IAAI,WAAW;IAC9D,OAAO,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,OAAO,OAAO,CAAC;AACnC;AACA,MAAM,mBAAmB,OAAO,EAC9B,GAAG,EACH,IAAI,EACL;IACC,MAAM,aAAa,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAAC;IACtD,MAAM,cAAc,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE;IAC/B,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,qJAAA,CAAA,oBAAiB,EAAE,IAAI,WAAW;IAC9D,OAAO,IAAI,cAAc,MAAM,CAAC,OAAO,OAAO,CAAC;AACjD", "ignoreList": [0], "debugId": null}}]}