{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/better-auth/dist/chunks/bun-sqlite-dialect.mjs"], "sourcesContent": ["import { CompiledQuery, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DEFAULT_MIGRATION_TABLE, DEFAULT_MIGRATION_LOCK_TABLE, sql } from 'kysely';\n\nclass BunSqliteAdapter {\n  get supportsCreateIfNotExists() {\n    return true;\n  }\n  get supportsTransactionalDdl() {\n    return false;\n  }\n  get supportsReturning() {\n    return true;\n  }\n  async acquireMigrationLock() {\n  }\n  async releaseMigrationLock() {\n  }\n  get supportsOutput() {\n    return true;\n  }\n}\nclass BunSqliteDriver {\n  #config;\n  #connectionMutex = new ConnectionMutex();\n  #db;\n  #connection;\n  constructor(config) {\n    this.#config = { ...config };\n  }\n  async init() {\n    this.#db = this.#config.database;\n    this.#connection = new BunSqliteConnection(this.#db);\n    if (this.#config.onCreateConnection) {\n      await this.#config.onCreateConnection(this.#connection);\n    }\n  }\n  async acquireConnection() {\n    await this.#connectionMutex.lock();\n    return this.#connection;\n  }\n  async beginTransaction(connection) {\n    await connection.executeQuery(CompiledQuery.raw(\"begin\"));\n  }\n  async commitTransaction(connection) {\n    await connection.executeQuery(CompiledQuery.raw(\"commit\"));\n  }\n  async rollbackTransaction(connection) {\n    await connection.executeQuery(CompiledQuery.raw(\"rollback\"));\n  }\n  async releaseConnection() {\n    this.#connectionMutex.unlock();\n  }\n  async destroy() {\n    this.#db?.close();\n  }\n}\nclass BunSqliteConnection {\n  #db;\n  constructor(db) {\n    this.#db = db;\n  }\n  executeQuery(compiledQuery) {\n    const { sql: sql2, parameters } = compiledQuery;\n    const stmt = this.#db.prepare(sql2);\n    return Promise.resolve({\n      rows: stmt.all(parameters)\n    });\n  }\n  async *streamQuery() {\n    throw new Error(\"Streaming query is not supported by SQLite driver.\");\n  }\n}\nclass ConnectionMutex {\n  #promise;\n  #resolve;\n  async lock() {\n    while (this.#promise) {\n      await this.#promise;\n    }\n    this.#promise = new Promise((resolve) => {\n      this.#resolve = resolve;\n    });\n  }\n  unlock() {\n    const resolve = this.#resolve;\n    this.#promise = void 0;\n    this.#resolve = void 0;\n    resolve?.();\n  }\n}\nclass BunSqliteIntrospector {\n  #db;\n  constructor(db) {\n    this.#db = db;\n  }\n  async getSchemas() {\n    return [];\n  }\n  async getTables(options = { withInternalKyselyTables: false }) {\n    let query = this.#db.selectFrom(\"sqlite_schema\").where(\"type\", \"=\", \"table\").where(\"name\", \"not like\", \"sqlite_%\").select(\"name\").$castTo();\n    if (!options.withInternalKyselyTables) {\n      query = query.where(\"name\", \"!=\", DEFAULT_MIGRATION_TABLE).where(\"name\", \"!=\", DEFAULT_MIGRATION_LOCK_TABLE);\n    }\n    const tables = await query.execute();\n    return Promise.all(tables.map(({ name }) => this.#getTableMetadata(name)));\n  }\n  async getMetadata(options) {\n    return {\n      tables: await this.getTables(options)\n    };\n  }\n  async #getTableMetadata(table) {\n    const db = this.#db;\n    const createSql = await db.selectFrom(\"sqlite_master\").where(\"name\", \"=\", table).select(\"sql\").$castTo().execute();\n    const autoIncrementCol = createSql[0]?.sql?.split(/[\\(\\),]/)?.find((it) => it.toLowerCase().includes(\"autoincrement\"))?.split(/\\s+/)?.[0]?.replace(/[\"`]/g, \"\");\n    const columns = await db.selectFrom(\n      sql`pragma_table_info(${table})`.as(\"table_info\")\n    ).select([\"name\", \"type\", \"notnull\", \"dflt_value\"]).execute();\n    return {\n      name: table,\n      columns: columns.map((col) => ({\n        name: col.name,\n        dataType: col.type,\n        isNullable: !col.notnull,\n        isAutoIncrementing: col.name === autoIncrementCol,\n        hasDefaultValue: col.dflt_value != null\n      })),\n      isView: true\n    };\n  }\n}\nclass BunSqliteQueryCompiler extends DefaultQueryCompiler {\n  getCurrentParameterPlaceholder() {\n    return \"?\";\n  }\n  getLeftIdentifierWrapper() {\n    return '\"';\n  }\n  getRightIdentifierWrapper() {\n    return '\"';\n  }\n  getAutoIncrement() {\n    return \"autoincrement\";\n  }\n}\nclass BunSqliteDialect {\n  #config;\n  constructor(config) {\n    this.#config = { ...config };\n  }\n  createDriver() {\n    return new BunSqliteDriver(this.#config);\n  }\n  createQueryCompiler() {\n    return new BunSqliteQueryCompiler();\n  }\n  createAdapter() {\n    return new BunSqliteAdapter();\n  }\n  createIntrospector(db) {\n    return new BunSqliteIntrospector(db);\n  }\n}\n\nexport { BunSqliteAdapter, BunSqliteDialect, BunSqliteDriver, BunSqliteIntrospector, BunSqliteQueryCompiler };\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;;AAEA,MAAM;IACJ,IAAI,4BAA4B;QAC9B,OAAO;IACT;IACA,IAAI,2BAA2B;QAC7B,OAAO;IACT;IACA,IAAI,oBAAoB;QACtB,OAAO;IACT;IACA,MAAM,uBAAuB,CAC7B;IACA,MAAM,uBAAuB,CAC7B;IACA,IAAI,iBAAiB;QACnB,OAAO;IACT;AACF;AACA,MAAM;IACJ,CAAA,MAAO,CAAC;IACR,CAAA,eAAgB,GAAG,IAAI,kBAAkB;IACzC,CAAA,EAAG,CAAC;IACJ,CAAA,UAAW,CAAC;IACZ,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,CAAA,MAAO,GAAG;YAAE,GAAG,MAAM;QAAC;IAC7B;IACA,MAAM,OAAO;QACX,IAAI,CAAC,CAAA,EAAG,GAAG,IAAI,CAAC,CAAA,MAAO,CAAC,QAAQ;QAChC,IAAI,CAAC,CAAA,UAAW,GAAG,IAAI,oBAAoB,IAAI,CAAC,CAAA,EAAG;QACnD,IAAI,IAAI,CAAC,CAAA,MAAO,CAAC,kBAAkB,EAAE;YACnC,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,UAAW;QACxD;IACF;IACA,MAAM,oBAAoB;QACxB,MAAM,IAAI,CAAC,CAAA,eAAgB,CAAC,IAAI;QAChC,OAAO,IAAI,CAAC,CAAA,UAAW;IACzB;IACA,MAAM,iBAAiB,UAAU,EAAE;QACjC,MAAM,WAAW,YAAY,CAAC,iLAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IAClD;IACA,MAAM,kBAAkB,UAAU,EAAE;QAClC,MAAM,WAAW,YAAY,CAAC,iLAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IAClD;IACA,MAAM,oBAAoB,UAAU,EAAE;QACpC,MAAM,WAAW,YAAY,CAAC,iLAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IAClD;IACA,MAAM,oBAAoB;QACxB,IAAI,CAAC,CAAA,eAAgB,CAAC,MAAM;IAC9B;IACA,MAAM,UAAU;QACd,IAAI,CAAC,CAAA,EAAG,EAAE;IACZ;AACF;AACA,MAAM;IACJ,CAAA,EAAG,CAAC;IACJ,YAAY,EAAE,CAAE;QACd,IAAI,CAAC,CAAA,EAAG,GAAG;IACb;IACA,aAAa,aAAa,EAAE;QAC1B,MAAM,EAAE,KAAK,IAAI,EAAE,UAAU,EAAE,GAAG;QAClC,MAAM,OAAO,IAAI,CAAC,CAAA,EAAG,CAAC,OAAO,CAAC;QAC9B,OAAO,QAAQ,OAAO,CAAC;YACrB,MAAM,KAAK,GAAG,CAAC;QACjB;IACF;IACA,OAAO,cAAc;QACnB,MAAM,IAAI,MAAM;IAClB;AACF;AACA,MAAM;IACJ,CAAA,OAAQ,CAAC;IACT,CAAA,OAAQ,CAAC;IACT,MAAM,OAAO;QACX,MAAO,IAAI,CAAC,CAAA,OAAQ,CAAE;YACpB,MAAM,IAAI,CAAC,CAAA,OAAQ;QACrB;QACA,IAAI,CAAC,CAAA,OAAQ,GAAG,IAAI,QAAQ,CAAC;YAC3B,IAAI,CAAC,CAAA,OAAQ,GAAG;QAClB;IACF;IACA,SAAS;QACP,MAAM,UAAU,IAAI,CAAC,CAAA,OAAQ;QAC7B,IAAI,CAAC,CAAA,OAAQ,GAAG,KAAK;QACrB,IAAI,CAAC,CAAA,OAAQ,GAAG,KAAK;QACrB;IACF;AACF;AACA,MAAM;IACJ,CAAA,EAAG,CAAC;IACJ,YAAY,EAAE,CAAE;QACd,IAAI,CAAC,CAAA,EAAG,GAAG;IACb;IACA,MAAM,aAAa;QACjB,OAAO,EAAE;IACX;IACA,MAAM,UAAU,UAAU;QAAE,0BAA0B;IAAM,CAAC,EAAE;QAC7D,IAAI,QAAQ,IAAI,CAAC,CAAA,EAAG,CAAC,UAAU,CAAC,iBAAiB,KAAK,CAAC,QAAQ,KAAK,SAAS,KAAK,CAAC,QAAQ,YAAY,YAAY,MAAM,CAAC,QAAQ,OAAO;QACzI,IAAI,CAAC,QAAQ,wBAAwB,EAAE;YACrC,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,gKAAA,CAAA,0BAAuB,EAAE,KAAK,CAAC,QAAQ,MAAM,gKAAA,CAAA,+BAA4B;QAC7G;QACA,MAAM,SAAS,MAAM,MAAM,OAAO;QAClC,OAAO,QAAQ,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,GAAK,IAAI,CAAC,CAAA,gBAAiB,CAAC;IACrE;IACA,MAAM,YAAY,OAAO,EAAE;QACzB,OAAO;YACL,QAAQ,MAAM,IAAI,CAAC,SAAS,CAAC;QAC/B;IACF;IACA,MAAM,CAAA,gBAAiB,CAAC,KAAK;QAC3B,MAAM,KAAK,IAAI,CAAC,CAAA,EAAG;QACnB,MAAM,YAAY,MAAM,GAAG,UAAU,CAAC,iBAAiB,KAAK,CAAC,QAAQ,KAAK,OAAO,MAAM,CAAC,OAAO,OAAO,GAAG,OAAO;QAChH,MAAM,mBAAmB,SAAS,CAAC,EAAE,EAAE,KAAK,MAAM,YAAY,KAAK,CAAC,KAAO,GAAG,WAAW,GAAG,QAAQ,CAAC,mBAAmB,MAAM,QAAQ,CAAC,EAAE,EAAE,QAAQ,SAAS;QAC5J,MAAM,UAAU,MAAM,GAAG,UAAU,CACjC,gKAAA,CAAA,MAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,eACpC,MAAM,CAAC;YAAC;YAAQ;YAAQ;YAAW;SAAa,EAAE,OAAO;QAC3D,OAAO;YACL,MAAM;YACN,SAAS,QAAQ,GAAG,CAAC,CAAC,MAAQ,CAAC;oBAC7B,MAAM,IAAI,IAAI;oBACd,UAAU,IAAI,IAAI;oBAClB,YAAY,CAAC,IAAI,OAAO;oBACxB,oBAAoB,IAAI,IAAI,KAAK;oBACjC,iBAAiB,IAAI,UAAU,IAAI;gBACrC,CAAC;YACD,QAAQ;QACV;IACF;AACF;AACA,MAAM,+BAA+B,4LAAA,CAAA,uBAAoB;IACvD,iCAAiC;QAC/B,OAAO;IACT;IACA,2BAA2B;QACzB,OAAO;IACT;IACA,4BAA4B;QAC1B,OAAO;IACT;IACA,mBAAmB;QACjB,OAAO;IACT;AACF;AACA,MAAM;IACJ,CAAA,MAAO,CAAC;IACR,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,CAAA,MAAO,GAAG;YAAE,GAAG,MAAM;QAAC;IAC7B;IACA,eAAe;QACb,OAAO,IAAI,gBAAgB,IAAI,CAAC,CAAA,MAAO;IACzC;IACA,sBAAsB;QACpB,OAAO,IAAI;IACb;IACA,gBAAgB;QACd,OAAO,IAAI;IACb;IACA,mBAAmB,EAAE,EAAE;QACrB,OAAO,IAAI,sBAAsB;IACnC;AACF", "ignoreList": [0], "debugId": null}}]}