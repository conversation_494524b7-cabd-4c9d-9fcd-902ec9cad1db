module.exports = {

"[project]/node_modules/next/headers.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
    });
});
}}),
"[project]/node_modules/better-auth/dist/chunks/bun-sqlite-dialect.mjs [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_better-auth_dist_chunks_bun-sqlite-dialect_mjs_ff5f03d4._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/better-auth/dist/chunks/bun-sqlite-dialect.mjs [app-route] (ecmascript)");
    });
});
}}),

};