{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "fiberRuntime.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/fiberRuntime.ts"], "sourcesContent": ["import * as RA from \"../Array.js\"\nimport * as Boolean from \"../Boolean.js\"\nimport type * as Cause from \"../Cause.js\"\nimport * as Chunk from \"../Chunk.js\"\nimport type * as Clock from \"../Clock.js\"\nimport type { ConfigProvider } from \"../ConfigProvider.js\"\nimport * as Context from \"../Context.js\"\nimport type { DefaultServices } from \"../DefaultServices.js\"\nimport * as Deferred from \"../Deferred.js\"\nimport type * as Duration from \"../Duration.js\"\nimport type * as Effect from \"../Effect.js\"\nimport * as Effectable from \"../Effectable.js\"\nimport type * as Either from \"../Either.js\"\nimport * as ExecutionStrategy from \"../ExecutionStrategy.js\"\nimport type * as Exit from \"../Exit.js\"\nimport type * as Fiber from \"../Fiber.js\"\nimport * as FiberId from \"../FiberId.js\"\nimport type * as FiberRef from \"../FiberRef.js\"\nimport * as FiberRefs from \"../FiberRefs.js\"\nimport * as FiberRefsPatch from \"../FiberRefsPatch.js\"\nimport * as FiberStatus from \"../FiberStatus.js\"\nimport type { LazyArg } from \"../Function.js\"\nimport { dual, identity, pipe } from \"../Function.js\"\nimport { globalValue } from \"../GlobalValue.js\"\nimport * as HashMap from \"../HashMap.js\"\nimport * as HashSet from \"../HashSet.js\"\nimport * as Inspectable from \"../Inspectable.js\"\nimport type { Logger } from \"../Logger.js\"\nimport * as LogLevel from \"../LogLevel.js\"\nimport type * as MetricLabel from \"../MetricLabel.js\"\nimport * as Micro from \"../Micro.js\"\nimport * as MRef from \"../MutableRef.js\"\nimport * as Option from \"../Option.js\"\nimport { pipeArguments } from \"../Pipeable.js\"\nimport * as Predicate from \"../Predicate.js\"\nimport type * as Random from \"../Random.js\"\nimport * as Ref from \"../Ref.js\"\nimport type { Entry, Request } from \"../Request.js\"\nimport type * as RequestBlock from \"../RequestBlock.js\"\nimport type * as RuntimeFlags from \"../RuntimeFlags.js\"\nimport * as RuntimeFlagsPatch from \"../RuntimeFlagsPatch.js\"\nimport { currentScheduler, type Scheduler } from \"../Scheduler.js\"\nimport type * as Scope from \"../Scope.js\"\nimport type * as Supervisor from \"../Supervisor.js\"\nimport type * as Tracer from \"../Tracer.js\"\nimport type { Concurrency, NoExcessProperties, NoInfer } from \"../Types.js\"\nimport { internalCall, yieldWrapGet } from \"../Utils.js\"\nimport * as RequestBlock_ from \"./blockedRequests.js\"\nimport * as internalCause from \"./cause.js\"\nimport * as clock from \"./clock.js\"\nimport { currentRequestMap } from \"./completedRequestMap.js\"\nimport * as concurrency from \"./concurrency.js\"\nimport { configProviderTag } from \"./configProvider.js\"\nimport * as internalEffect from \"./core-effect.js\"\nimport * as core from \"./core.js\"\nimport * as defaultServices from \"./defaultServices.js\"\nimport { consoleTag } from \"./defaultServices/console.js\"\nimport * as executionStrategy from \"./executionStrategy.js\"\nimport * as internalFiber from \"./fiber.js\"\nimport * as FiberMessage from \"./fiberMessage.js\"\nimport * as fiberRefs from \"./fiberRefs.js\"\nimport * as fiberScope from \"./fiberScope.js\"\nimport * as internalLogger from \"./logger.js\"\nimport * as metric from \"./metric.js\"\nimport * as metricBoundaries from \"./metric/boundaries.js\"\nimport * as metricLabel from \"./metric/label.js\"\nimport * as OpCodes from \"./opCodes/effect.js\"\nimport { randomTag } from \"./random.js\"\nimport { complete } from \"./request.js\"\nimport * as runtimeFlags_ from \"./runtimeFlags.js\"\nimport { OpSupervision } from \"./runtimeFlags.js\"\nimport * as supervisor from \"./supervisor.js\"\nimport * as SupervisorPatch from \"./supervisor/patch.js\"\nimport * as tracer from \"./tracer.js\"\nimport * as version from \"./version.js\"\n\n/** @internal */\nexport const fiberStarted = metric.counter(\"effect_fiber_started\", { incremental: true })\n/** @internal */\nexport const fiberActive = metric.counter(\"effect_fiber_active\")\n/** @internal */\nexport const fiberSuccesses = metric.counter(\"effect_fiber_successes\", { incremental: true })\n/** @internal */\nexport const fiberFailures = metric.counter(\"effect_fiber_failures\", { incremental: true })\n/** @internal */\nexport const fiberLifetimes = metric.tagged(\n  metric.histogram(\n    \"effect_fiber_lifetimes\",\n    metricBoundaries.exponential({\n      start: 0.5,\n      factor: 2,\n      count: 35\n    })\n  ),\n  \"time_unit\",\n  \"milliseconds\"\n)\n\n/** @internal */\ntype EvaluationSignal =\n  | EvaluationSignalContinue\n  | EvaluationSignalDone\n  | EvaluationSignalYieldNow\n\n/** @internal */\nconst EvaluationSignalContinue = \"Continue\" as const\n\n/** @internal */\ntype EvaluationSignalContinue = typeof EvaluationSignalContinue\n\n/** @internal */\nconst EvaluationSignalDone = \"Done\" as const\n\n/** @internal */\ntype EvaluationSignalDone = typeof EvaluationSignalDone\n\n/** @internal */\nconst EvaluationSignalYieldNow = \"Yield\" as const\n\n/** @internal */\ntype EvaluationSignalYieldNow = typeof EvaluationSignalYieldNow\n\nconst runtimeFiberVariance = {\n  /* c8 ignore next */\n  _E: (_: never) => _,\n  /* c8 ignore next */\n  _A: (_: never) => _\n}\n\nconst absurd = (_: never): never => {\n  throw new Error(\n    `BUG: FiberRuntime - ${\n      Inspectable.toStringUnknown(_)\n    } - please report an issue at https://github.com/Effect-TS/effect/issues`\n  )\n}\n\nconst YieldedOp = Symbol.for(\"effect/internal/fiberRuntime/YieldedOp\")\ntype YieldedOp = typeof YieldedOp\nconst yieldedOpChannel: {\n  currentOp: core.Primitive | null\n} = globalValue(\"effect/internal/fiberRuntime/yieldedOpChannel\", () => ({\n  currentOp: null\n}))\n\nconst contOpSuccess = {\n  [OpCodes.OP_ON_SUCCESS]: (\n    _: FiberRuntime<any, any>,\n    cont: core.OnSuccess,\n    value: unknown\n  ) => {\n    return internalCall(() => cont.effect_instruction_i1(value))\n  },\n  [\"OnStep\"]: (\n    _: FiberRuntime<any, any>,\n    _cont: core.OnStep,\n    value: unknown\n  ) => {\n    return core.exitSucceed(core.exitSucceed(value))\n  },\n  [OpCodes.OP_ON_SUCCESS_AND_FAILURE]: (\n    _: FiberRuntime<any, any>,\n    cont: core.OnSuccessAndFailure,\n    value: unknown\n  ) => {\n    return internalCall(() => cont.effect_instruction_i2(value))\n  },\n  [OpCodes.OP_REVERT_FLAGS]: (\n    self: FiberRuntime<any, any>,\n    cont: core.RevertFlags,\n    value: unknown\n  ) => {\n    self.patchRuntimeFlags(self.currentRuntimeFlags, cont.patch)\n    if (runtimeFlags_.interruptible(self.currentRuntimeFlags) && self.isInterrupted()) {\n      return core.exitFailCause(self.getInterruptedCause())\n    } else {\n      return core.exitSucceed(value)\n    }\n  },\n  [OpCodes.OP_WHILE]: (\n    self: FiberRuntime<any, any>,\n    cont: core.While,\n    value: unknown\n  ) => {\n    internalCall(() => cont.effect_instruction_i2(value))\n    if (internalCall(() => cont.effect_instruction_i0())) {\n      self.pushStack(cont)\n      return internalCall(() => cont.effect_instruction_i1())\n    } else {\n      return core.void\n    }\n  },\n  [OpCodes.OP_ITERATOR]: (\n    self: FiberRuntime<any, any>,\n    cont: core.FromIterator,\n    value: unknown\n  ) => {\n    const state = internalCall(() => cont.effect_instruction_i0.next(value))\n    if (state.done) return core.exitSucceed(state.value)\n    self.pushStack(cont)\n    return yieldWrapGet(state.value)\n  }\n}\n\nconst drainQueueWhileRunningTable = {\n  [FiberMessage.OP_INTERRUPT_SIGNAL]: (\n    self: FiberRuntime<any, any>,\n    runtimeFlags: RuntimeFlags.RuntimeFlags,\n    cur: Effect.Effect<any, any, any>,\n    message: FiberMessage.FiberMessage & { _tag: FiberMessage.OP_INTERRUPT_SIGNAL }\n  ) => {\n    self.processNewInterruptSignal(message.cause)\n    return runtimeFlags_.interruptible(runtimeFlags) ? core.exitFailCause(message.cause) : cur\n  },\n  [FiberMessage.OP_RESUME]: (\n    _self: FiberRuntime<any, any>,\n    _runtimeFlags: RuntimeFlags.RuntimeFlags,\n    _cur: Effect.Effect<any, any, any>,\n    _message: FiberMessage.FiberMessage\n  ) => {\n    throw new Error(\"It is illegal to have multiple concurrent run loops in a single fiber\")\n  },\n  [FiberMessage.OP_STATEFUL]: (\n    self: FiberRuntime<any, any>,\n    runtimeFlags: RuntimeFlags.RuntimeFlags,\n    cur: Effect.Effect<any, any, any>,\n    message: FiberMessage.FiberMessage & { _tag: FiberMessage.OP_STATEFUL }\n  ) => {\n    message.onFiber(self, FiberStatus.running(runtimeFlags))\n    return cur\n  },\n  [FiberMessage.OP_YIELD_NOW]: (\n    _self: FiberRuntime<any, any>,\n    _runtimeFlags: RuntimeFlags.RuntimeFlags,\n    cur: Effect.Effect<any, any, any>,\n    _message: FiberMessage.FiberMessage & { _tag: FiberMessage.OP_YIELD_NOW }\n  ) => {\n    return core.flatMap(core.yieldNow(), () => cur)\n  }\n}\n\n/**\n * Executes all requests, submitting requests to each data source in parallel.\n */\nconst runBlockedRequests = (self: RequestBlock.RequestBlock) =>\n  core.forEachSequentialDiscard(\n    RequestBlock_.flatten(self),\n    (requestsByRequestResolver) =>\n      forEachConcurrentDiscard(\n        RequestBlock_.sequentialCollectionToChunk(requestsByRequestResolver),\n        ([dataSource, sequential]) => {\n          const map = new Map<Request<any, any>, Entry<any>>()\n          const arr: Array<Array<Entry<any>>> = []\n          for (const block of sequential) {\n            arr.push(Chunk.toReadonlyArray(block) as any)\n            for (const entry of block) {\n              map.set(entry.request as Request<any, any>, entry)\n            }\n          }\n          const flat = arr.flat()\n          return core.fiberRefLocally(\n            invokeWithInterrupt(dataSource.runAll(arr), flat, () =>\n              flat.forEach((entry) => {\n                entry.listeners.interrupted = true\n              })),\n            currentRequestMap,\n            map\n          )\n        },\n        false,\n        false\n      )\n  )\n\n/** @internal */\nexport interface Snapshot {\n  refs: FiberRefs.FiberRefs\n  flags: RuntimeFlags.RuntimeFlags\n}\n\nconst _version = version.getCurrentVersion()\n\n/** @internal */\nexport class FiberRuntime<in out A, in out E = never> extends Effectable.Class<A, E>\n  implements Fiber.RuntimeFiber<A, E>\n{\n  readonly [internalFiber.FiberTypeId] = internalFiber.fiberVariance\n  readonly [internalFiber.RuntimeFiberTypeId] = runtimeFiberVariance\n  private _fiberRefs: FiberRefs.FiberRefs\n  private _fiberId: FiberId.Runtime\n  private _queue = new Array<FiberMessage.FiberMessage>()\n  private _children: Set<FiberRuntime<any, any>> | null = null\n  private _observers = new Array<(exit: Exit.Exit<A, E>) => void>()\n  private _running = false\n  private _stack: Array<core.Continuation> = []\n  private _asyncInterruptor: ((effect: Effect.Effect<any, any, any>) => any) | null = null\n  private _asyncBlockingOn: FiberId.FiberId | null = null\n  private _exitValue: Exit.Exit<A, E> | null = null\n  private _steps: Array<Snapshot> = []\n  private _isYielding = false\n\n  public currentRuntimeFlags: RuntimeFlags.RuntimeFlags\n  public currentOpCount: number = 0\n  public currentSupervisor!: Supervisor.Supervisor<any>\n  public currentScheduler!: Scheduler\n  public currentTracer!: Tracer.Tracer\n  public currentSpan!: Tracer.AnySpan | undefined\n  public currentContext!: Context.Context<never>\n  public currentDefaultServices!: Context.Context<DefaultServices>\n\n  constructor(\n    fiberId: FiberId.Runtime,\n    fiberRefs0: FiberRefs.FiberRefs,\n    runtimeFlags0: RuntimeFlags.RuntimeFlags\n  ) {\n    super()\n    this.currentRuntimeFlags = runtimeFlags0\n    this._fiberId = fiberId\n    this._fiberRefs = fiberRefs0\n    if (runtimeFlags_.runtimeMetrics(runtimeFlags0)) {\n      const tags = this.getFiberRef(core.currentMetricLabels)\n      fiberStarted.unsafeUpdate(1, tags)\n      fiberActive.unsafeUpdate(1, tags)\n    }\n    this.refreshRefCache()\n  }\n\n  commit(): Effect.Effect<A, E, never> {\n    return internalFiber.join(this)\n  }\n\n  /**\n   * The identity of the fiber.\n   */\n  id(): FiberId.Runtime {\n    return this._fiberId\n  }\n\n  /**\n   * Begins execution of the effect associated with this fiber on in the\n   * background. This can be called to \"kick off\" execution of a fiber after\n   * it has been created.\n   */\n  resume<A, E>(effect: Effect.Effect<A, E, any>): void {\n    this.tell(FiberMessage.resume(effect))\n  }\n\n  /**\n   * The status of the fiber.\n   */\n  get status(): Effect.Effect<FiberStatus.FiberStatus> {\n    return this.ask((_, status) => status)\n  }\n\n  /**\n   * Gets the fiber runtime flags.\n   */\n  get runtimeFlags(): Effect.Effect<RuntimeFlags.RuntimeFlags> {\n    return this.ask((state, status) => {\n      if (FiberStatus.isDone(status)) {\n        return state.currentRuntimeFlags\n      }\n      return status.runtimeFlags\n    })\n  }\n\n  /**\n   * Returns the current `FiberScope` for the fiber.\n   */\n  scope(): fiberScope.FiberScope {\n    return fiberScope.unsafeMake(this)\n  }\n\n  /**\n   * Retrieves the immediate children of the fiber.\n   */\n  get children(): Effect.Effect<Array<Fiber.RuntimeFiber<any, any>>> {\n    return this.ask((fiber) => Array.from(fiber.getChildren()))\n  }\n\n  /**\n   * Gets the fiber's set of children.\n   */\n  getChildren(): Set<FiberRuntime<any, any>> {\n    if (this._children === null) {\n      this._children = new Set()\n    }\n    return this._children\n  }\n\n  /**\n   * Retrieves the interrupted cause of the fiber, which will be `Cause.empty`\n   * if the fiber has not been interrupted.\n   *\n   * **NOTE**: This method is safe to invoke on any fiber, but if not invoked\n   * on this fiber, then values derived from the fiber's state (including the\n   * log annotations and log level) may not be up-to-date.\n   */\n  getInterruptedCause() {\n    return this.getFiberRef(core.currentInterruptedCause)\n  }\n\n  /**\n   * Retrieves the whole set of fiber refs.\n   */\n  fiberRefs(): Effect.Effect<FiberRefs.FiberRefs> {\n    return this.ask((fiber) => fiber.getFiberRefs())\n  }\n\n  /**\n   * Returns an effect that will contain information computed from the fiber\n   * state and status while running on the fiber.\n   *\n   * This allows the outside world to interact safely with mutable fiber state\n   * without locks or immutable data.\n   */\n  ask<Z>(\n    f: (runtime: FiberRuntime<any, any>, status: FiberStatus.FiberStatus) => Z\n  ): Effect.Effect<Z> {\n    return core.suspend(() => {\n      const deferred = core.deferredUnsafeMake<Z>(this._fiberId)\n      this.tell(\n        FiberMessage.stateful((fiber, status) => {\n          core.deferredUnsafeDone(deferred, core.sync(() => f(fiber, status)))\n        })\n      )\n      return core.deferredAwait(deferred)\n    })\n  }\n\n  /**\n   * Adds a message to be processed by the fiber on the fiber.\n   */\n  tell(message: FiberMessage.FiberMessage): void {\n    this._queue.push(message)\n    if (!this._running) {\n      this._running = true\n      this.drainQueueLaterOnExecutor()\n    }\n  }\n\n  get await(): Effect.Effect<Exit.Exit<A, E>> {\n    return core.async((resume) => {\n      const cb = (exit: Exit.Exit<A, E>) => resume(core.succeed(exit))\n      this.tell(\n        FiberMessage.stateful((fiber, _) => {\n          if (fiber._exitValue !== null) {\n            cb(this._exitValue!)\n          } else {\n            fiber.addObserver(cb)\n          }\n        })\n      )\n      return core.sync(() =>\n        this.tell(\n          FiberMessage.stateful((fiber, _) => {\n            fiber.removeObserver(cb)\n          })\n        )\n      )\n    }, this.id())\n  }\n\n  get inheritAll(): Effect.Effect<void> {\n    return core.withFiberRuntime((parentFiber, parentStatus) => {\n      const parentFiberId = parentFiber.id()\n      const parentFiberRefs = parentFiber.getFiberRefs()\n      const parentRuntimeFlags = parentStatus.runtimeFlags\n      const childFiberRefs = this.getFiberRefs()\n      const updatedFiberRefs = fiberRefs.joinAs(parentFiberRefs, parentFiberId, childFiberRefs)\n\n      parentFiber.setFiberRefs(updatedFiberRefs)\n\n      const updatedRuntimeFlags = parentFiber.getFiberRef(currentRuntimeFlags)\n\n      const patch = pipe(\n        runtimeFlags_.diff(parentRuntimeFlags, updatedRuntimeFlags),\n        // Do not inherit WindDown or Interruption!\n        RuntimeFlagsPatch.exclude(runtimeFlags_.Interruption),\n        RuntimeFlagsPatch.exclude(runtimeFlags_.WindDown)\n      )\n\n      return core.updateRuntimeFlags(patch)\n    })\n  }\n\n  /**\n   * Tentatively observes the fiber, but returns immediately if it is not\n   * already done.\n   */\n  get poll(): Effect.Effect<Option.Option<Exit.Exit<A, E>>> {\n    return core.sync(() => Option.fromNullable(this._exitValue))\n  }\n\n  /**\n   * Unsafely observes the fiber, but returns immediately if it is not\n   * already done.\n   */\n  unsafePoll(): Exit.Exit<A, E> | null {\n    return this._exitValue\n  }\n\n  /**\n   * In the background, interrupts the fiber as if interrupted from the specified fiber.\n   */\n  interruptAsFork(fiberId: FiberId.FiberId): Effect.Effect<void> {\n    return core.sync(() => this.tell(FiberMessage.interruptSignal(internalCause.interrupt(fiberId))))\n  }\n\n  /**\n   * In the background, interrupts the fiber as if interrupted from the specified fiber.\n   */\n  unsafeInterruptAsFork(fiberId: FiberId.FiberId) {\n    this.tell(FiberMessage.interruptSignal(internalCause.interrupt(fiberId)))\n  }\n\n  /**\n   * Adds an observer to the list of observers.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  addObserver(observer: (exit: Exit.Exit<A, E>) => void): void {\n    if (this._exitValue !== null) {\n      observer(this._exitValue!)\n    } else {\n      this._observers.push(observer)\n    }\n  }\n\n  /**\n   * Removes the specified observer from the list of observers that will be\n   * notified when the fiber exits.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  removeObserver(observer: (exit: Exit.Exit<A, E>) => void): void {\n    this._observers = this._observers.filter((o) => o !== observer)\n  }\n  /**\n   * Retrieves all fiber refs of the fiber.\n   *\n   * **NOTE**: This method is safe to invoke on any fiber, but if not invoked\n   * on this fiber, then values derived from the fiber's state (including the\n   * log annotations and log level) may not be up-to-date.\n   */\n  getFiberRefs(): FiberRefs.FiberRefs {\n    this.setFiberRef(currentRuntimeFlags, this.currentRuntimeFlags)\n    return this._fiberRefs\n  }\n\n  /**\n   * Deletes the specified fiber ref.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  unsafeDeleteFiberRef<X>(fiberRef: FiberRef.FiberRef<X>): void {\n    this._fiberRefs = fiberRefs.delete_(this._fiberRefs, fiberRef)\n  }\n\n  /**\n   * Retrieves the state of the fiber ref, or else its initial value.\n   *\n   * **NOTE**: This method is safe to invoke on any fiber, but if not invoked\n   * on this fiber, then values derived from the fiber's state (including the\n   * log annotations and log level) may not be up-to-date.\n   */\n  getFiberRef<X>(fiberRef: FiberRef.FiberRef<X>): X {\n    if (this._fiberRefs.locals.has(fiberRef)) {\n      return this._fiberRefs.locals.get(fiberRef)![0][1] as X\n    }\n    return fiberRef.initial\n  }\n\n  /**\n   * Sets the fiber ref to the specified value.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  setFiberRef<X>(fiberRef: FiberRef.FiberRef<X>, value: X): void {\n    this._fiberRefs = fiberRefs.updateAs(this._fiberRefs, {\n      fiberId: this._fiberId,\n      fiberRef,\n      value\n    })\n    this.refreshRefCache()\n  }\n\n  refreshRefCache() {\n    this.currentDefaultServices = this.getFiberRef(defaultServices.currentServices)\n    this.currentTracer = this.currentDefaultServices.unsafeMap.get(tracer.tracerTag.key)\n    this.currentSupervisor = this.getFiberRef(currentSupervisor)\n    this.currentScheduler = this.getFiberRef(currentScheduler)\n    this.currentContext = this.getFiberRef(core.currentContext)\n    this.currentSpan = this.currentContext.unsafeMap.get(tracer.spanTag.key)\n  }\n\n  /**\n   * Wholesale replaces all fiber refs of this fiber.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  setFiberRefs(fiberRefs: FiberRefs.FiberRefs): void {\n    this._fiberRefs = fiberRefs\n    this.refreshRefCache()\n  }\n\n  /**\n   * Adds a reference to the specified fiber inside the children set.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  addChild(child: FiberRuntime<any, any>) {\n    this.getChildren().add(child)\n  }\n\n  /**\n   * Removes a reference to the specified fiber inside the children set.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  removeChild(child: FiberRuntime<any, any>) {\n    this.getChildren().delete(child)\n  }\n\n  /**\n   * Transfers all children of this fiber that are currently running to the\n   * specified fiber scope.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself after it has\n   * evaluated the effects but prior to exiting.\n   */\n  transferChildren(scope: fiberScope.FiberScope) {\n    const children = this._children\n    // Clear the children of the current fiber\n    this._children = null\n    if (children !== null && children.size > 0) {\n      for (const child of children) {\n        // If the child is still running, add it to the scope\n        if (child._exitValue === null) {\n          scope.add(this.currentRuntimeFlags, child)\n        }\n      }\n    }\n  }\n\n  /**\n   * On the current thread, executes all messages in the fiber's inbox. This\n   * method may return before all work is done, in the event the fiber executes\n   * an asynchronous operation.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  drainQueueOnCurrentThread() {\n    let recurse = true\n    while (recurse) {\n      let evaluationSignal: EvaluationSignal = EvaluationSignalContinue\n      const prev = (globalThis as any)[internalFiber.currentFiberURI]\n      ;(globalThis as any)[internalFiber.currentFiberURI] = this\n      try {\n        while (evaluationSignal === EvaluationSignalContinue) {\n          evaluationSignal = this._queue.length === 0 ?\n            EvaluationSignalDone :\n            this.evaluateMessageWhileSuspended(this._queue.splice(0, 1)[0]!)\n        }\n      } finally {\n        this._running = false\n        ;(globalThis as any)[internalFiber.currentFiberURI] = prev\n      }\n      // Maybe someone added something to the queue between us checking, and us\n      // giving up the drain. If so, we need to restart the draining, but only\n      // if we beat everyone else to the restart:\n      if (this._queue.length > 0 && !this._running) {\n        this._running = true\n        if (evaluationSignal === EvaluationSignalYieldNow) {\n          this.drainQueueLaterOnExecutor()\n          recurse = false\n        } else {\n          recurse = true\n        }\n      } else {\n        recurse = false\n      }\n    }\n  }\n\n  /**\n   * Schedules the execution of all messages in the fiber's inbox.\n   *\n   * This method will return immediately after the scheduling\n   * operation is completed, but potentially before such messages have been\n   * executed.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  drainQueueLaterOnExecutor() {\n    this.currentScheduler.scheduleTask(\n      this.run,\n      this.getFiberRef(core.currentSchedulingPriority)\n    )\n  }\n\n  /**\n   * Drains the fiber's message queue while the fiber is actively running,\n   * returning the next effect to execute, which may be the input effect if no\n   * additional effect needs to be executed.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  drainQueueWhileRunning(\n    runtimeFlags: RuntimeFlags.RuntimeFlags,\n    cur0: Effect.Effect<any, any, any>\n  ) {\n    let cur = cur0\n    while (this._queue.length > 0) {\n      const message = this._queue.splice(0, 1)[0]\n      // @ts-expect-error\n      cur = drainQueueWhileRunningTable[message._tag](this, runtimeFlags, cur, message)\n    }\n    return cur\n  }\n\n  /**\n   * Determines if the fiber is interrupted.\n   *\n   * **NOTE**: This method is safe to invoke on any fiber, but if not invoked\n   * on this fiber, then values derived from the fiber's state (including the\n   * log annotations and log level) may not be up-to-date.\n   */\n  isInterrupted(): boolean {\n    return !internalCause.isEmpty(this.getFiberRef(core.currentInterruptedCause))\n  }\n\n  /**\n   * Adds an interruptor to the set of interruptors that are interrupting this\n   * fiber.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  addInterruptedCause(cause: Cause.Cause<never>) {\n    const oldSC = this.getFiberRef(core.currentInterruptedCause)\n    this.setFiberRef(core.currentInterruptedCause, internalCause.sequential(oldSC, cause))\n  }\n\n  /**\n   * Processes a new incoming interrupt signal.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  processNewInterruptSignal(cause: Cause.Cause<never>): void {\n    this.addInterruptedCause(cause)\n    this.sendInterruptSignalToAllChildren()\n  }\n\n  /**\n   * Interrupts all children of the current fiber, returning an effect that will\n   * await the exit of the children. This method will return null if the fiber\n   * has no children.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  sendInterruptSignalToAllChildren(): boolean {\n    if (this._children === null || this._children.size === 0) {\n      return false\n    }\n    let told = false\n    for (const child of this._children) {\n      child.tell(FiberMessage.interruptSignal(internalCause.interrupt(this.id())))\n      told = true\n    }\n    return told\n  }\n\n  /**\n   * Interrupts all children of the current fiber, returning an effect that will\n   * await the exit of the children. This method will return null if the fiber\n   * has no children.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  interruptAllChildren() {\n    if (this.sendInterruptSignalToAllChildren()) {\n      const it = this._children!.values()\n      this._children = null\n      let isDone = false\n      const body = () => {\n        const next = it.next()\n        if (!next.done) {\n          return core.asVoid(next.value.await)\n        } else {\n          return core.sync(() => {\n            isDone = true\n          })\n        }\n      }\n      return core.whileLoop({\n        while: () => !isDone,\n        body,\n        step: () => {\n          //\n        }\n      })\n    }\n    return null\n  }\n\n  reportExitValue(exit: Exit.Exit<A, E>) {\n    if (runtimeFlags_.runtimeMetrics(this.currentRuntimeFlags)) {\n      const tags = this.getFiberRef(core.currentMetricLabels)\n      const startTimeMillis = this.id().startTimeMillis\n      const endTimeMillis = Date.now()\n      fiberLifetimes.unsafeUpdate(endTimeMillis - startTimeMillis, tags)\n      fiberActive.unsafeUpdate(-1, tags)\n      switch (exit._tag) {\n        case OpCodes.OP_SUCCESS: {\n          fiberSuccesses.unsafeUpdate(1, tags)\n          break\n        }\n        case OpCodes.OP_FAILURE: {\n          fiberFailures.unsafeUpdate(1, tags)\n          break\n        }\n      }\n    }\n    if (exit._tag === \"Failure\") {\n      const level = this.getFiberRef(core.currentUnhandledErrorLogLevel)\n      if (!internalCause.isInterruptedOnly(exit.cause) && level._tag === \"Some\") {\n        this.log(\"Fiber terminated with an unhandled error\", exit.cause, level)\n      }\n    }\n  }\n\n  setExitValue(exit: Exit.Exit<A, E>) {\n    this._exitValue = exit\n    this.reportExitValue(exit)\n    for (let i = this._observers.length - 1; i >= 0; i--) {\n      this._observers[i](exit)\n    }\n    this._observers = []\n  }\n\n  getLoggers() {\n    return this.getFiberRef(currentLoggers)\n  }\n\n  log(\n    message: unknown,\n    cause: Cause.Cause<any>,\n    overrideLogLevel: Option.Option<LogLevel.LogLevel>\n  ): void {\n    const logLevel = Option.isSome(overrideLogLevel) ?\n      overrideLogLevel.value :\n      this.getFiberRef(core.currentLogLevel)\n    const minimumLogLevel = this.getFiberRef(currentMinimumLogLevel)\n    if (LogLevel.greaterThan(minimumLogLevel, logLevel)) {\n      return\n    }\n    const spans = this.getFiberRef(core.currentLogSpan)\n    const annotations = this.getFiberRef(core.currentLogAnnotations)\n    const loggers = this.getLoggers()\n    const contextMap = this.getFiberRefs()\n    if (HashSet.size(loggers) > 0) {\n      const clockService = Context.get(this.getFiberRef(defaultServices.currentServices), clock.clockTag)\n      const date = new Date(clockService.unsafeCurrentTimeMillis())\n      Inspectable.withRedactableContext(contextMap, () => {\n        for (const logger of loggers) {\n          logger.log({\n            fiberId: this.id(),\n            logLevel,\n            message,\n            cause,\n            context: contextMap,\n            spans,\n            annotations,\n            date\n          })\n        }\n      })\n    }\n  }\n\n  /**\n   * Evaluates a single message on the current thread, while the fiber is\n   * suspended. This method should only be called while evaluation of the\n   * fiber's effect is suspended due to an asynchronous operation.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  evaluateMessageWhileSuspended(message: FiberMessage.FiberMessage): EvaluationSignal {\n    switch (message._tag) {\n      case FiberMessage.OP_YIELD_NOW: {\n        return EvaluationSignalYieldNow\n      }\n      case FiberMessage.OP_INTERRUPT_SIGNAL: {\n        this.processNewInterruptSignal(message.cause)\n        if (this._asyncInterruptor !== null) {\n          this._asyncInterruptor(core.exitFailCause(message.cause))\n          this._asyncInterruptor = null\n        }\n        return EvaluationSignalContinue\n      }\n      case FiberMessage.OP_RESUME: {\n        this._asyncInterruptor = null\n        this._asyncBlockingOn = null\n        this.evaluateEffect(message.effect)\n        return EvaluationSignalContinue\n      }\n      case FiberMessage.OP_STATEFUL: {\n        message.onFiber(\n          this,\n          this._exitValue !== null ?\n            FiberStatus.done :\n            FiberStatus.suspended(this.currentRuntimeFlags, this._asyncBlockingOn!)\n        )\n        return EvaluationSignalContinue\n      }\n      default: {\n        return absurd(message)\n      }\n    }\n  }\n\n  /**\n   * Evaluates an effect until completion, potentially asynchronously.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  evaluateEffect(effect0: Effect.Effect<any, any, any>) {\n    this.currentSupervisor.onResume(this)\n    try {\n      let effect: Effect.Effect<any, any, any> | null =\n        runtimeFlags_.interruptible(this.currentRuntimeFlags) && this.isInterrupted() ?\n          core.exitFailCause(this.getInterruptedCause()) :\n          effect0\n      while (effect !== null) {\n        const eff: Effect.Effect<any, any, any> = effect\n        const exit = this.runLoop(eff)\n        if (exit === YieldedOp) {\n          const op = yieldedOpChannel.currentOp!\n          yieldedOpChannel.currentOp = null\n          if (op._op === OpCodes.OP_YIELD) {\n            if (runtimeFlags_.cooperativeYielding(this.currentRuntimeFlags)) {\n              this.tell(FiberMessage.yieldNow())\n              this.tell(FiberMessage.resume(core.exitVoid))\n              effect = null\n            } else {\n              effect = core.exitVoid\n            }\n          } else if (op._op === OpCodes.OP_ASYNC) {\n            // Terminate this evaluation, async resumption will continue evaluation:\n            effect = null\n          }\n        } else {\n          this.currentRuntimeFlags = pipe(this.currentRuntimeFlags, runtimeFlags_.enable(runtimeFlags_.WindDown))\n          const interruption = this.interruptAllChildren()\n          if (interruption !== null) {\n            effect = core.flatMap(interruption, () => exit)\n          } else {\n            if (this._queue.length === 0) {\n              // No more messages to process, so we will allow the fiber to end life:\n              this.setExitValue(exit)\n            } else {\n              // There are messages, possibly added by the final op executed by\n              // the fiber. To be safe, we should execute those now before we\n              // allow the fiber to end life:\n              this.tell(FiberMessage.resume(exit))\n            }\n            effect = null\n          }\n        }\n      }\n    } finally {\n      this.currentSupervisor.onSuspend(this)\n    }\n  }\n\n  /**\n   * Begins execution of the effect associated with this fiber on the current\n   * thread. This can be called to \"kick off\" execution of a fiber after it has\n   * been created, in hopes that the effect can be executed synchronously.\n   *\n   * This is not the normal way of starting a fiber, but it is useful when the\n   * express goal of executing the fiber is to synchronously produce its exit.\n   */\n  start<R>(effect: Effect.Effect<A, E, R>): void {\n    if (!this._running) {\n      this._running = true\n      const prev = (globalThis as any)[internalFiber.currentFiberURI]\n      ;(globalThis as any)[internalFiber.currentFiberURI] = this\n      try {\n        this.evaluateEffect(effect)\n      } finally {\n        this._running = false\n        ;(globalThis as any)[internalFiber.currentFiberURI] = prev\n        // Because we're special casing `start`, we have to be responsible\n        // for spinning up the fiber if there were new messages added to\n        // the queue between the completion of the effect and the transition\n        // to the not running state.\n        if (this._queue.length > 0) {\n          this.drainQueueLaterOnExecutor()\n        }\n      }\n    } else {\n      this.tell(FiberMessage.resume(effect))\n    }\n  }\n\n  /**\n   * Begins execution of the effect associated with this fiber on in the\n   * background, and on the correct thread pool. This can be called to \"kick\n   * off\" execution of a fiber after it has been created, in hopes that the\n   * effect can be executed synchronously.\n   */\n  startFork<R>(effect: Effect.Effect<A, E, R>): void {\n    this.tell(FiberMessage.resume(effect))\n  }\n\n  /**\n   * Takes the current runtime flags, patches them to return the new runtime\n   * flags, and then makes any changes necessary to fiber state based on the\n   * specified patch.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  patchRuntimeFlags(oldRuntimeFlags: RuntimeFlags.RuntimeFlags, patch: RuntimeFlagsPatch.RuntimeFlagsPatch) {\n    const newRuntimeFlags = runtimeFlags_.patch(oldRuntimeFlags, patch)\n    ;(globalThis as any)[internalFiber.currentFiberURI] = this\n    this.currentRuntimeFlags = newRuntimeFlags\n    return newRuntimeFlags\n  }\n\n  /**\n   * Initiates an asynchronous operation, by building a callback that will\n   * resume execution, and then feeding that callback to the registration\n   * function, handling error cases and repeated resumptions appropriately.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  initiateAsync(\n    runtimeFlags: RuntimeFlags.RuntimeFlags,\n    asyncRegister: (resume: (effect: Effect.Effect<any, any, any>) => void) => void\n  ) {\n    let alreadyCalled = false\n    const callback = (effect: Effect.Effect<any, any, any>) => {\n      if (!alreadyCalled) {\n        alreadyCalled = true\n        this.tell(FiberMessage.resume(effect))\n      }\n    }\n    if (runtimeFlags_.interruptible(runtimeFlags)) {\n      this._asyncInterruptor = callback\n    }\n    try {\n      asyncRegister(callback)\n    } catch (e) {\n      callback(core.failCause(internalCause.die(e)))\n    }\n  }\n\n  pushStack(cont: core.Continuation) {\n    this._stack.push(cont)\n    if (cont._op === \"OnStep\") {\n      this._steps.push({ refs: this.getFiberRefs(), flags: this.currentRuntimeFlags })\n    }\n  }\n\n  popStack() {\n    const item = this._stack.pop()\n    if (item) {\n      if (item._op === \"OnStep\") {\n        this._steps.pop()\n      }\n      return item\n    }\n    return\n  }\n\n  getNextSuccessCont() {\n    let frame = this.popStack()\n    while (frame) {\n      if (frame._op !== OpCodes.OP_ON_FAILURE) {\n        return frame\n      }\n      frame = this.popStack()\n    }\n  }\n\n  getNextFailCont() {\n    let frame = this.popStack()\n    while (frame) {\n      if (frame._op !== OpCodes.OP_ON_SUCCESS && frame._op !== OpCodes.OP_WHILE && frame._op !== OpCodes.OP_ITERATOR) {\n        return frame\n      }\n      frame = this.popStack()\n    }\n  }\n\n  [OpCodes.OP_TAG](op: core.Primitive & { _op: OpCodes.OP_SYNC }) {\n    return core.sync(() => Context.unsafeGet(this.currentContext, op as unknown as Context.Tag<any, any>))\n  }\n\n  [\"Left\"](op: core.Primitive & { _op: \"Left\" }) {\n    return core.fail(op.left)\n  }\n\n  [\"None\"](_: core.Primitive & { _op: \"None\" }) {\n    return core.fail(new core.NoSuchElementException())\n  }\n\n  [\"Right\"](op: core.Primitive & { _op: \"Right\" }) {\n    return core.exitSucceed(op.right)\n  }\n\n  [\"Some\"](op: core.Primitive & { _op: \"Some\" }) {\n    return core.exitSucceed(op.value)\n  }\n\n  [\"Micro\"](op: Micro.Micro<any, any, never> & { _op: \"Micro\" }) {\n    return core.unsafeAsync<any, any>((microResume) => {\n      let resume = microResume\n      const fiber = Micro.runFork(Micro.provideContext(op, this.currentContext))\n      fiber.addObserver((exit) => {\n        if (exit._tag === \"Success\") {\n          return resume(core.exitSucceed(exit.value))\n        }\n        switch (exit.cause._tag) {\n          case \"Interrupt\": {\n            return resume(core.exitFailCause(internalCause.interrupt(FiberId.none)))\n          }\n          case \"Fail\": {\n            return resume(core.fail(exit.cause.error))\n          }\n          case \"Die\": {\n            return resume(core.die(exit.cause.defect))\n          }\n        }\n      })\n      return core.unsafeAsync<void>((abortResume) => {\n        resume = (_: any) => {\n          abortResume(core.void)\n        }\n        fiber.unsafeInterrupt()\n      })\n    })\n  }\n\n  [OpCodes.OP_SYNC](op: core.Primitive & { _op: OpCodes.OP_SYNC }) {\n    const value = internalCall(() => op.effect_instruction_i0())\n    const cont = this.getNextSuccessCont()\n    if (cont !== undefined) {\n      if (!(cont._op in contOpSuccess)) {\n        // @ts-expect-error\n        absurd(cont)\n      }\n      // @ts-expect-error\n      return contOpSuccess[cont._op](this, cont, value)\n    } else {\n      yieldedOpChannel.currentOp = core.exitSucceed(value) as any\n      return YieldedOp\n    }\n  }\n\n  [OpCodes.OP_SUCCESS](op: core.Primitive & { _op: OpCodes.OP_SUCCESS }) {\n    const oldCur = op\n    const cont = this.getNextSuccessCont()\n    if (cont !== undefined) {\n      if (!(cont._op in contOpSuccess)) {\n        // @ts-expect-error\n        absurd(cont)\n      }\n      // @ts-expect-error\n      return contOpSuccess[cont._op](this, cont, oldCur.effect_instruction_i0)\n    } else {\n      yieldedOpChannel.currentOp = oldCur\n      return YieldedOp\n    }\n  }\n\n  [OpCodes.OP_FAILURE](op: core.Primitive & { _op: OpCodes.OP_FAILURE }) {\n    const cause = op.effect_instruction_i0\n    const cont = this.getNextFailCont()\n    if (cont !== undefined) {\n      switch (cont._op) {\n        case OpCodes.OP_ON_FAILURE:\n        case OpCodes.OP_ON_SUCCESS_AND_FAILURE: {\n          if (!(runtimeFlags_.interruptible(this.currentRuntimeFlags) && this.isInterrupted())) {\n            return internalCall(() => cont.effect_instruction_i1(cause))\n          } else {\n            return core.exitFailCause(internalCause.stripFailures(cause))\n          }\n        }\n        case \"OnStep\": {\n          if (!(runtimeFlags_.interruptible(this.currentRuntimeFlags) && this.isInterrupted())) {\n            return core.exitSucceed(core.exitFailCause(cause))\n          } else {\n            return core.exitFailCause(internalCause.stripFailures(cause))\n          }\n        }\n        case OpCodes.OP_REVERT_FLAGS: {\n          this.patchRuntimeFlags(this.currentRuntimeFlags, cont.patch)\n          if (runtimeFlags_.interruptible(this.currentRuntimeFlags) && this.isInterrupted()) {\n            return core.exitFailCause(internalCause.sequential(cause, this.getInterruptedCause()))\n          } else {\n            return core.exitFailCause(cause)\n          }\n        }\n        default: {\n          absurd(cont)\n        }\n      }\n    } else {\n      yieldedOpChannel.currentOp = core.exitFailCause(cause) as any\n      return YieldedOp\n    }\n  }\n\n  [OpCodes.OP_WITH_RUNTIME](op: core.Primitive & { _op: OpCodes.OP_WITH_RUNTIME }) {\n    return internalCall(() =>\n      op.effect_instruction_i0(\n        this as FiberRuntime<unknown, unknown>,\n        FiberStatus.running(this.currentRuntimeFlags) as FiberStatus.Running\n      )\n    )\n  }\n\n  [\"Blocked\"](op: core.Primitive & { _op: \"Blocked\" }) {\n    const refs = this.getFiberRefs()\n    const flags = this.currentRuntimeFlags\n    if (this._steps.length > 0) {\n      const frames: Array<core.Continuation> = []\n      const snap = this._steps[this._steps.length - 1]\n      let frame = this.popStack()\n      while (frame && frame._op !== \"OnStep\") {\n        frames.push(frame)\n        frame = this.popStack()\n      }\n      this.setFiberRefs(snap.refs)\n      this.currentRuntimeFlags = snap.flags\n      const patchRefs = FiberRefsPatch.diff(snap.refs, refs)\n      const patchFlags = runtimeFlags_.diff(snap.flags, flags)\n      return core.exitSucceed(core.blocked(\n        op.effect_instruction_i0,\n        core.withFiberRuntime<unknown, unknown>((newFiber) => {\n          while (frames.length > 0) {\n            newFiber.pushStack(frames.pop()!)\n          }\n          newFiber.setFiberRefs(\n            FiberRefsPatch.patch(newFiber.id(), newFiber.getFiberRefs())(patchRefs)\n          )\n          newFiber.currentRuntimeFlags = runtimeFlags_.patch(patchFlags)(newFiber.currentRuntimeFlags)\n          return op.effect_instruction_i1\n        })\n      ))\n    }\n    return core.uninterruptibleMask((restore) =>\n      core.flatMap(\n        forkDaemon(core.runRequestBlock(op.effect_instruction_i0)),\n        () => restore(op.effect_instruction_i1)\n      )\n    )\n  }\n\n  [\"RunBlocked\"](op: core.Primitive & { _op: \"RunBlocked\" }) {\n    return runBlockedRequests(op.effect_instruction_i0)\n  }\n\n  [OpCodes.OP_UPDATE_RUNTIME_FLAGS](op: core.Primitive & { _op: OpCodes.OP_UPDATE_RUNTIME_FLAGS }) {\n    const updateFlags = op.effect_instruction_i0\n    const oldRuntimeFlags = this.currentRuntimeFlags\n    const newRuntimeFlags = runtimeFlags_.patch(oldRuntimeFlags, updateFlags)\n    // One more chance to short circuit: if we're immediately going\n    // to interrupt. Interruption will cause immediate reversion of\n    // the flag, so as long as we \"peek ahead\", there's no need to\n    // set them to begin with.\n    if (runtimeFlags_.interruptible(newRuntimeFlags) && this.isInterrupted()) {\n      return core.exitFailCause(this.getInterruptedCause())\n    } else {\n      // Impossible to short circuit, so record the changes\n      this.patchRuntimeFlags(this.currentRuntimeFlags, updateFlags)\n      if (op.effect_instruction_i1) {\n        // Since we updated the flags, we need to revert them\n        const revertFlags = runtimeFlags_.diff(newRuntimeFlags, oldRuntimeFlags)\n        this.pushStack(new core.RevertFlags(revertFlags, op))\n        return internalCall(() => op.effect_instruction_i1!(oldRuntimeFlags))\n      } else {\n        return core.exitVoid\n      }\n    }\n  }\n\n  [OpCodes.OP_ON_SUCCESS](op: core.Primitive & { _op: OpCodes.OP_ON_SUCCESS }) {\n    this.pushStack(op)\n    return op.effect_instruction_i0\n  }\n\n  [\"OnStep\"](op: core.Primitive & { _op: \"OnStep\" }) {\n    this.pushStack(op)\n    return op.effect_instruction_i0\n  }\n\n  [OpCodes.OP_ON_FAILURE](op: core.Primitive & { _op: OpCodes.OP_ON_FAILURE }) {\n    this.pushStack(op)\n    return op.effect_instruction_i0\n  }\n\n  [OpCodes.OP_ON_SUCCESS_AND_FAILURE](op: core.Primitive & { _op: OpCodes.OP_ON_SUCCESS_AND_FAILURE }) {\n    this.pushStack(op)\n    return op.effect_instruction_i0\n  }\n\n  [OpCodes.OP_ASYNC](op: core.Primitive & { _op: OpCodes.OP_ASYNC }) {\n    this._asyncBlockingOn = op.effect_instruction_i1\n    this.initiateAsync(this.currentRuntimeFlags, op.effect_instruction_i0)\n    yieldedOpChannel.currentOp = op\n    return YieldedOp\n  }\n\n  [OpCodes.OP_YIELD](op: core.Primitive & { op: OpCodes.OP_YIELD }) {\n    this._isYielding = false\n    yieldedOpChannel.currentOp = op\n    return YieldedOp\n  }\n\n  [OpCodes.OP_WHILE](op: core.Primitive & { _op: OpCodes.OP_WHILE }) {\n    const check = op.effect_instruction_i0\n    const body = op.effect_instruction_i1\n    if (check()) {\n      this.pushStack(op)\n      return body()\n    } else {\n      return core.exitVoid\n    }\n  }\n\n  [OpCodes.OP_ITERATOR](op: core.Primitive & { _op: OpCodes.OP_ITERATOR }) {\n    return contOpSuccess[OpCodes.OP_ITERATOR](this, op, undefined)\n  }\n\n  [OpCodes.OP_COMMIT](op: core.Primitive & { _op: OpCodes.OP_COMMIT }) {\n    return internalCall(() => op.commit())\n  }\n\n  /**\n   * The main run-loop for evaluating effects.\n   *\n   * **NOTE**: This method must be invoked by the fiber itself.\n   */\n  runLoop(effect0: Effect.Effect<any, any, any>): Exit.Exit<any, any> | YieldedOp {\n    let cur: Effect.Effect<any, any, any> | YieldedOp = effect0\n    this.currentOpCount = 0\n\n    while (true) {\n      if ((this.currentRuntimeFlags & OpSupervision) !== 0) {\n        this.currentSupervisor.onEffect(this, cur)\n      }\n      if (this._queue.length > 0) {\n        cur = this.drainQueueWhileRunning(this.currentRuntimeFlags, cur)\n      }\n      if (!this._isYielding) {\n        this.currentOpCount += 1\n        const shouldYield = this.currentScheduler.shouldYield(this)\n        if (shouldYield !== false) {\n          this._isYielding = true\n          this.currentOpCount = 0\n          const oldCur = cur\n          cur = core.flatMap(core.yieldNow({ priority: shouldYield }), () => oldCur)\n        }\n      }\n      try {\n        // @ts-expect-error\n        cur = this.currentTracer.context(\n          () => {\n            if (_version !== (cur as core.Primitive)[core.EffectTypeId]._V) {\n              return core.dieMessage(\n                `Cannot execute an Effect versioned ${\n                  (cur as core.Primitive)[core.EffectTypeId]._V\n                } with a Runtime of version ${version.getCurrentVersion()}`\n              )\n            }\n            // @ts-expect-error\n            return this[(cur as core.Primitive)._op](cur as core.Primitive)\n          },\n          this\n        )\n\n        if (cur === YieldedOp) {\n          const op = yieldedOpChannel.currentOp!\n          if (\n            op._op === OpCodes.OP_YIELD ||\n            op._op === OpCodes.OP_ASYNC\n          ) {\n            return YieldedOp\n          }\n\n          yieldedOpChannel.currentOp = null\n          return (\n              op._op === OpCodes.OP_SUCCESS ||\n              op._op === OpCodes.OP_FAILURE\n            ) ?\n            op as unknown as Exit.Exit<A, E> :\n            core.exitFailCause(internalCause.die(op))\n        }\n      } catch (e) {\n        if (cur !== YieldedOp && !Predicate.hasProperty(cur, \"_op\") || !((cur as core.Primitive)._op in this)) {\n          cur = core.dieMessage(`Not a valid effect: ${Inspectable.toStringUnknown(cur)}`)\n        } else if (core.isInterruptedException(e)) {\n          cur = core.exitFailCause(\n            internalCause.sequential(internalCause.die(e), internalCause.interrupt(FiberId.none))\n          )\n        } else {\n          cur = core.die(e)\n        }\n      }\n    }\n  }\n\n  run = () => {\n    this.drainQueueOnCurrentThread()\n  }\n}\n\n// circular with Logger\n\n/** @internal */\nexport const currentMinimumLogLevel: FiberRef.FiberRef<LogLevel.LogLevel> = globalValue(\n  \"effect/FiberRef/currentMinimumLogLevel\",\n  () => core.fiberRefUnsafeMake<LogLevel.LogLevel>(LogLevel.fromLiteral(\"Info\"))\n)\n\n/** @internal */\nexport const loggerWithConsoleLog = <M, O>(self: Logger<M, O>): Logger<M, void> =>\n  internalLogger.makeLogger((opts) => {\n    const services = FiberRefs.getOrDefault(opts.context, defaultServices.currentServices)\n    Context.get(services, consoleTag).unsafe.log(self.log(opts))\n  })\n\n/** @internal */\nexport const loggerWithLeveledLog = <M, O>(self: Logger<M, O>): Logger<M, void> =>\n  internalLogger.makeLogger((opts) => {\n    const services = FiberRefs.getOrDefault(opts.context, defaultServices.currentServices)\n    const unsafeLogger = Context.get(services, consoleTag).unsafe\n    switch (opts.logLevel._tag) {\n      case \"Debug\":\n        return unsafeLogger.debug(self.log(opts))\n      case \"Info\":\n        return unsafeLogger.info(self.log(opts))\n      case \"Trace\":\n        return unsafeLogger.trace(self.log(opts))\n      case \"Warning\":\n        return unsafeLogger.warn(self.log(opts))\n      case \"Error\":\n      case \"Fatal\":\n        return unsafeLogger.error(self.log(opts))\n      default:\n        return unsafeLogger.log(self.log(opts))\n    }\n  })\n\n/** @internal */\nexport const loggerWithConsoleError = <M, O>(self: Logger<M, O>): Logger<M, void> =>\n  internalLogger.makeLogger((opts) => {\n    const services = FiberRefs.getOrDefault(opts.context, defaultServices.currentServices)\n    Context.get(services, consoleTag).unsafe.error(self.log(opts))\n  })\n\n/** @internal */\nexport const defaultLogger: Logger<unknown, void> = globalValue(\n  Symbol.for(\"effect/Logger/defaultLogger\"),\n  () => loggerWithConsoleLog(internalLogger.stringLogger)\n)\n\n/** @internal */\nexport const jsonLogger: Logger<unknown, void> = globalValue(\n  Symbol.for(\"effect/Logger/jsonLogger\"),\n  () => loggerWithConsoleLog(internalLogger.jsonLogger)\n)\n\n/** @internal */\nexport const logFmtLogger: Logger<unknown, void> = globalValue(\n  Symbol.for(\"effect/Logger/logFmtLogger\"),\n  () => loggerWithConsoleLog(internalLogger.logfmtLogger)\n)\n\n/** @internal */\nexport const prettyLogger: Logger<unknown, void> = globalValue(\n  Symbol.for(\"effect/Logger/prettyLogger\"),\n  () => internalLogger.prettyLoggerDefault\n)\n\n/** @internal */\nexport const structuredLogger: Logger<unknown, void> = globalValue(\n  Symbol.for(\"effect/Logger/structuredLogger\"),\n  () => loggerWithConsoleLog(internalLogger.structuredLogger)\n)\n\n/** @internal */\nexport const tracerLogger = globalValue(\n  Symbol.for(\"effect/Logger/tracerLogger\"),\n  () =>\n    internalLogger.makeLogger<unknown, void>(({\n      annotations,\n      cause,\n      context,\n      fiberId,\n      logLevel,\n      message\n    }) => {\n      const span = Context.getOption(\n        fiberRefs.getOrDefault(context, core.currentContext),\n        tracer.spanTag\n      )\n      if (span._tag === \"None\" || span.value._tag === \"ExternalSpan\") {\n        return\n      }\n      const clockService = Context.unsafeGet(\n        fiberRefs.getOrDefault(context, defaultServices.currentServices),\n        clock.clockTag\n      )\n\n      const attributes: Record<string, unknown> = {}\n      for (const [key, value] of annotations) {\n        attributes[key] = value\n      }\n      attributes[\"effect.fiberId\"] = FiberId.threadName(fiberId)\n      attributes[\"effect.logLevel\"] = logLevel.label\n\n      if (cause !== null && cause._tag !== \"Empty\") {\n        attributes[\"effect.cause\"] = internalCause.pretty(cause, { renderErrorCause: true })\n      }\n\n      span.value.event(\n        Inspectable.toStringUnknown(Array.isArray(message) ? message[0] : message),\n        clockService.unsafeCurrentTimeNanos(),\n        attributes\n      )\n    })\n)\n\n/** @internal */\nexport const loggerWithSpanAnnotations = <Message, Output>(self: Logger<Message, Output>): Logger<Message, Output> =>\n  internalLogger.mapInputOptions(self, (options: Logger.Options<Message>) => {\n    const span = Option.flatMap(fiberRefs.get(options.context, core.currentContext), Context.getOption(tracer.spanTag))\n    if (span._tag === \"None\") {\n      return options\n    }\n    return {\n      ...options,\n      annotations: pipe(\n        options.annotations,\n        HashMap.set(\"effect.traceId\", span.value.traceId as unknown),\n        HashMap.set(\"effect.spanId\", span.value.spanId as unknown),\n        span.value._tag === \"Span\" ? HashMap.set(\"effect.spanName\", span.value.name as unknown) : identity\n      )\n    }\n  })\n\n/** @internal */\nexport const currentLoggers: FiberRef.FiberRef<\n  HashSet.HashSet<Logger<unknown, any>>\n> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentLoggers\"),\n  () => core.fiberRefUnsafeMakeHashSet(HashSet.make(defaultLogger, tracerLogger))\n)\n\n/** @internal */\nexport const batchedLogger = dual<\n  <Output, R>(\n    window: Duration.DurationInput,\n    f: (messages: Array<NoInfer<Output>>) => Effect.Effect<void, never, R>\n  ) => <Message>(\n    self: Logger<Message, Output>\n  ) => Effect.Effect<Logger<Message, void>, never, Scope.Scope | R>,\n  <Message, Output, R>(\n    self: Logger<Message, Output>,\n    window: Duration.DurationInput,\n    f: (messages: Array<NoInfer<Output>>) => Effect.Effect<void, never, R>\n  ) => Effect.Effect<Logger<Message, void>, never, Scope.Scope | R>\n>(3, <Message, Output, R>(\n  self: Logger<Message, Output>,\n  window: Duration.DurationInput,\n  f: (messages: Array<NoInfer<Output>>) => Effect.Effect<void, never, R>\n): Effect.Effect<Logger<Message, void>, never, Scope.Scope | R> =>\n  core.flatMap(scope, (scope) => {\n    let buffer: Array<Output> = []\n    const flush = core.suspend(() => {\n      if (buffer.length === 0) {\n        return core.void\n      }\n      const arr = buffer\n      buffer = []\n      return f(arr)\n    })\n\n    return core.uninterruptibleMask((restore) =>\n      pipe(\n        internalEffect.sleep(window),\n        core.zipRight(flush),\n        internalEffect.forever,\n        restore,\n        forkDaemon,\n        core.flatMap((fiber) => core.scopeAddFinalizer(scope, core.interruptFiber(fiber))),\n        core.zipRight(addFinalizer(() => flush)),\n        core.as(\n          internalLogger.makeLogger((options) => {\n            buffer.push(self.log(options))\n          })\n        )\n      )\n    )\n  }))\n\nexport const annotateLogsScoped: {\n  (key: string, value: unknown): Effect.Effect<void, never, Scope.Scope>\n  (values: Record<string, unknown>): Effect.Effect<void, never, Scope.Scope>\n} = function() {\n  if (typeof arguments[0] === \"string\") {\n    return fiberRefLocallyScopedWith(\n      core.currentLogAnnotations,\n      HashMap.set(arguments[0], arguments[1])\n    )\n  }\n  const entries = Object.entries(arguments[0])\n  return fiberRefLocallyScopedWith(\n    core.currentLogAnnotations,\n    HashMap.mutate((annotations) => {\n      for (let i = 0; i < entries.length; i++) {\n        const [key, value] = entries[i]\n        HashMap.set(annotations, key, value)\n      }\n      return annotations\n    })\n  )\n}\n\n/** @internal */\nexport const whenLogLevel = dual<\n  (\n    level: LogLevel.LogLevel | LogLevel.Literal\n  ) => <A, E, R>(effect: Effect.Effect<A, E, R>) => Effect.Effect<Option.Option<A>, E, R>,\n  <A, E, R>(\n    effect: Effect.Effect<A, E, R>,\n    level: LogLevel.LogLevel | LogLevel.Literal\n  ) => Effect.Effect<Option.Option<A>, E, R>\n>(2, (effect, level) => {\n  const requiredLogLevel = typeof level === \"string\" ? LogLevel.fromLiteral(level) : level\n\n  return core.withFiberRuntime((fiberState) => {\n    const minimumLogLevel = fiberState.getFiberRef(currentMinimumLogLevel)\n\n    // Imitate the behaviour of `FiberRuntime.log`\n    if (LogLevel.greaterThan(minimumLogLevel, requiredLogLevel)) {\n      return core.succeed(Option.none())\n    }\n\n    return core.map(effect, Option.some)\n  })\n})\n\n// circular with Effect\n\n/* @internal */\nexport const acquireRelease: {\n  <A, X, R2>(\n    release: (a: A, exit: Exit.Exit<unknown, unknown>) => Effect.Effect<X, never, R2>\n  ): <E, R>(acquire: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R2 | R | Scope.Scope>\n  <A, E, R, X, R2>(\n    acquire: Effect.Effect<A, E, R>,\n    release: (a: A, exit: Exit.Exit<unknown, unknown>) => Effect.Effect<X, never, R2>\n  ): Effect.Effect<A, E, R2 | R | Scope.Scope>\n} = dual((args) => core.isEffect(args[0]), (acquire, release) =>\n  core.uninterruptible(\n    core.tap(acquire, (a) => addFinalizer((exit) => release(a, exit)))\n  ))\n\n/* @internal */\nexport const acquireReleaseInterruptible: {\n  <X, R2>(\n    release: (exit: Exit.Exit<unknown, unknown>) => Effect.Effect<X, never, R2>\n  ): <A, E, R>(acquire: Effect.Effect<A, E, R>) => Effect.Effect<A, E, Scope.Scope | R2 | R>\n  <A, E, R, X, R2>(\n    acquire: Effect.Effect<A, E, R>,\n    release: (exit: Exit.Exit<unknown, unknown>) => Effect.Effect<X, never, R2>\n  ): Effect.Effect<A, E, Scope.Scope | R2 | R>\n} = dual((args) => core.isEffect(args[0]), (acquire, release) =>\n  ensuring(\n    acquire,\n    addFinalizer((exit) => release(exit))\n  ))\n\n/* @internal */\nexport const addFinalizer = <X, R>(\n  finalizer: (exit: Exit.Exit<unknown, unknown>) => Effect.Effect<X, never, R>\n): Effect.Effect<void, never, R | Scope.Scope> =>\n  core.withFiberRuntime(\n    (runtime) => {\n      const acquireRefs = runtime.getFiberRefs()\n      const acquireFlags = runtime.currentRuntimeFlags\n      return core.flatMap(scope, (scope) =>\n        core.scopeAddFinalizerExit(scope, (exit) =>\n          core.withFiberRuntime((runtimeFinalizer) => {\n            const preRefs = runtimeFinalizer.getFiberRefs()\n            const preFlags = runtimeFinalizer.currentRuntimeFlags\n            const patchRefs = FiberRefsPatch.diff(preRefs, acquireRefs)\n            const patchFlags = runtimeFlags_.diff(preFlags, acquireFlags)\n            const inverseRefs = FiberRefsPatch.diff(acquireRefs, preRefs)\n            runtimeFinalizer.setFiberRefs(\n              FiberRefsPatch.patch(patchRefs, runtimeFinalizer.id(), acquireRefs)\n            )\n\n            return ensuring(\n              core.withRuntimeFlags(finalizer(exit) as Effect.Effect<X>, patchFlags),\n              core.sync(() => {\n                runtimeFinalizer.setFiberRefs(\n                  FiberRefsPatch.patch(inverseRefs, runtimeFinalizer.id(), runtimeFinalizer.getFiberRefs())\n                )\n              })\n            )\n          })))\n    }\n  )\n\n/* @internal */\nexport const daemonChildren = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<A, E, R> => {\n  const forkScope = core.fiberRefLocally(core.currentForkScopeOverride, Option.some(fiberScope.globalScope))\n  return forkScope(self)\n}\n\n/** @internal */\nconst _existsParFound = Symbol.for(\"effect/Effect/existsPar/found\")\n\n/* @internal */\nexport const exists: {\n  <A, E, R>(predicate: (a: A, i: number) => Effect.Effect<boolean, E, R>, options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }): (elements: Iterable<A>) => Effect.Effect<boolean, E, R>\n  <A, E, R>(elements: Iterable<A>, predicate: (a: A, i: number) => Effect.Effect<boolean, E, R>, options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }): Effect.Effect<boolean, E, R>\n} = dual(\n  (args) => Predicate.isIterable(args[0]) && !core.isEffect(args[0]),\n  <A, E, R>(elements: Iterable<A>, predicate: (a: A, i: number) => Effect.Effect<boolean, E, R>, options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n  }) =>\n    concurrency.matchSimple(\n      options?.concurrency,\n      () => core.suspend(() => existsLoop(elements[Symbol.iterator](), 0, predicate)),\n      () =>\n        core.matchEffect(\n          forEach(\n            elements,\n            (a, i) => core.if_(predicate(a, i), { onTrue: () => core.fail(_existsParFound), onFalse: () => core.void }),\n            options\n          ),\n          {\n            onFailure: (e) => e === _existsParFound ? core.succeed(true) : core.fail(e),\n            onSuccess: () => core.succeed(false)\n          }\n        )\n    )\n)\n\nconst existsLoop = <A, E, R>(\n  iterator: Iterator<A>,\n  index: number,\n  f: (a: A, i: number) => Effect.Effect<boolean, E, R>\n): Effect.Effect<boolean, E, R> => {\n  const next = iterator.next()\n  if (next.done) {\n    return core.succeed(false)\n  }\n  return pipe(core.flatMap(\n    f(next.value, index),\n    (b) => b ? core.succeed(b) : existsLoop(iterator, index + 1, f)\n  ))\n}\n\n/* @internal */\nexport const filter = dual<\n  <A, E, R>(\n    predicate: (a: NoInfer<A>, i: number) => Effect.Effect<boolean, E, R>,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly negate?: boolean | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => (elements: Iterable<A>) => Effect.Effect<Array<A>, E, R>,\n  <A, E, R>(elements: Iterable<A>, predicate: (a: NoInfer<A>, i: number) => Effect.Effect<boolean, E, R>, options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly negate?: boolean | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }) => Effect.Effect<Array<A>, E, R>\n>(\n  (args) => Predicate.isIterable(args[0]) && !core.isEffect(args[0]),\n  <A, E, R>(elements: Iterable<A>, predicate: (a: NoInfer<A>, i: number) => Effect.Effect<boolean, E, R>, options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly negate?: boolean | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }) => {\n    const predicate_ = options?.negate ? (a: A, i: number) => core.map(predicate(a, i), Boolean.not) : predicate\n    return concurrency.matchSimple(\n      options?.concurrency,\n      () =>\n        core.suspend(() =>\n          RA.fromIterable(elements).reduceRight(\n            (effect, a, i) =>\n              core.zipWith(\n                effect,\n                core.suspend(() => predicate_(a, i)),\n                (list, b) => b ? [a, ...list] : list\n              ),\n            core.sync(() => new Array<A>()) as Effect.Effect<Array<A>, E, R>\n          )\n        ),\n      () =>\n        core.map(\n          forEach(\n            elements,\n            (a, i) => core.map(predicate_(a, i), (b) => (b ? Option.some(a) : Option.none())),\n            options\n          ),\n          RA.getSomes\n        )\n    )\n  }\n)\n\n// === all\n\nconst allResolveInput = (\n  input: Iterable<Effect.Effect<any, any, any>> | Record<string, Effect.Effect<any, any, any>>\n): [Iterable<Effect.Effect<any, any, any>>, Option.Option<(as: ReadonlyArray<any>) => any>] => {\n  if (Array.isArray(input) || Predicate.isIterable(input)) {\n    return [input, Option.none()]\n  }\n  const keys = Object.keys(input)\n  const size = keys.length\n  return [\n    keys.map((k) => input[k]),\n    Option.some((values: ReadonlyArray<any>) => {\n      const res = {}\n      for (let i = 0; i < size; i++) {\n        ;(res as any)[keys[i]] = values[i]\n      }\n      return res\n    })\n  ]\n}\n\nconst allValidate = (\n  effects: Iterable<Effect.Effect<any, any, any>>,\n  reconcile: Option.Option<(as: ReadonlyArray<any>) => any>,\n  options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly discard?: boolean | undefined\n    readonly mode?: \"default\" | \"validate\" | \"either\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }\n) => {\n  const eitherEffects: Array<Effect.Effect<Either.Either<unknown, unknown>, never, unknown>> = []\n  for (const effect of effects) {\n    eitherEffects.push(core.either(effect))\n  }\n  return core.flatMap(\n    forEach(eitherEffects, identity, {\n      concurrency: options?.concurrency,\n      batching: options?.batching,\n      concurrentFinalizers: options?.concurrentFinalizers\n    }),\n    (eithers) => {\n      const none = Option.none()\n      const size = eithers.length\n      const errors: Array<unknown> = new Array(size)\n      const successes: Array<unknown> = new Array(size)\n      let errored = false\n      for (let i = 0; i < size; i++) {\n        const either = eithers[i] as Either.Either<unknown, unknown>\n        if (either._tag === \"Left\") {\n          errors[i] = Option.some(either.left)\n          errored = true\n        } else {\n          successes[i] = either.right\n          errors[i] = none\n        }\n      }\n      if (errored) {\n        return reconcile._tag === \"Some\" ?\n          core.fail(reconcile.value(errors)) :\n          core.fail(errors)\n      } else if (options?.discard) {\n        return core.void\n      }\n      return reconcile._tag === \"Some\" ?\n        core.succeed(reconcile.value(successes)) :\n        core.succeed(successes)\n    }\n  )\n}\n\nconst allEither = (\n  effects: Iterable<Effect.Effect<any, any, any>>,\n  reconcile: Option.Option<(as: ReadonlyArray<any>) => any>,\n  options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly discard?: boolean | undefined\n    readonly mode?: \"default\" | \"validate\" | \"either\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }\n) => {\n  const eitherEffects: Array<Effect.Effect<Either.Either<unknown, unknown>, never, unknown>> = []\n  for (const effect of effects) {\n    eitherEffects.push(core.either(effect))\n  }\n\n  if (options?.discard) {\n    return forEach(eitherEffects, identity, {\n      concurrency: options?.concurrency,\n      batching: options?.batching,\n      discard: true,\n      concurrentFinalizers: options?.concurrentFinalizers\n    })\n  }\n\n  return core.map(\n    forEach(eitherEffects, identity, {\n      concurrency: options?.concurrency,\n      batching: options?.batching,\n      concurrentFinalizers: options?.concurrentFinalizers\n    }),\n    (eithers) =>\n      reconcile._tag === \"Some\" ?\n        reconcile.value(eithers) :\n        eithers\n  )\n}\n\n/* @internal */\nexport const all = <\n  const Arg extends Iterable<Effect.Effect<any, any, any>> | Record<string, Effect.Effect<any, any, any>>,\n  O extends NoExcessProperties<{\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly discard?: boolean | undefined\n    readonly mode?: \"default\" | \"validate\" | \"either\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }, O>\n>(\n  arg: Arg,\n  options?: O\n): Effect.All.Return<Arg, O> => {\n  const [effects, reconcile] = allResolveInput(arg)\n\n  if (options?.mode === \"validate\") {\n    return allValidate(effects, reconcile, options) as any\n  } else if (options?.mode === \"either\") {\n    return allEither(effects, reconcile, options) as any\n  }\n\n  return options?.discard !== true && reconcile._tag === \"Some\"\n    ? core.map(\n      forEach(effects, identity, options as any),\n      reconcile.value\n    ) as any\n    : forEach(effects, identity, options as any) as any\n}\n\n/* @internal */\nexport const allWith = <\n  O extends NoExcessProperties<{\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly discard?: boolean | undefined\n    readonly mode?: \"default\" | \"validate\" | \"either\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }, O>\n>(options?: O) =>\n<const Arg extends Iterable<Effect.Effect<any, any, any>> | Record<string, Effect.Effect<any, any, any>>>(\n  arg: Arg\n): Effect.All.Return<Arg, O> => all(arg, options)\n\n/* @internal */\nexport const allSuccesses = <Eff extends Effect.Effect<any, any, any>>(\n  elements: Iterable<Eff>,\n  options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }\n): Effect.Effect<Array<Effect.Effect.Success<Eff>>, never, Effect.Effect.Context<Eff>> =>\n  core.map(\n    all(RA.fromIterable(elements).map(core.exit), options),\n    RA.filterMap((exit) => core.exitIsSuccess(exit) ? Option.some(exit.effect_instruction_i0) : Option.none())\n  )\n\n/* @internal */\nexport const replicate = dual<\n  (n: number) => <A, E, R>(self: Effect.Effect<A, E, R>) => Array<Effect.Effect<A, E, R>>,\n  <A, E, R>(self: Effect.Effect<A, E, R>, n: number) => Array<Effect.Effect<A, E, R>>\n>(2, (self, n) => Array.from({ length: n }, () => self))\n\n/* @internal */\nexport const replicateEffect: {\n  (\n    n: number,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly discard?: false | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ): <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<Array<A>, E, R>\n  (\n    n: number,\n    options: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly discard: true\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ): <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<void, E, R>\n  <A, E, R>(\n    self: Effect.Effect<A, E, R>,\n    n: number,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly discard?: false | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ): Effect.Effect<Array<A>, E, R>\n  <A, E, R>(\n    self: Effect.Effect<A, E, R>,\n    n: number,\n    options: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly discard: true\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ): Effect.Effect<void, E, R>\n} = dual(\n  (args) => core.isEffect(args[0]),\n  (self, n, options) => all(replicate(self, n), options)\n)\n\n/* @internal */\nexport const forEach: {\n  <B, E, R, S extends Iterable<any>>(\n    f: (a: RA.ReadonlyArray.Infer<S>, i: number) => Effect.Effect<B, E, R>,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly discard?: false | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    } | undefined\n  ): (\n    self: S\n  ) => Effect.Effect<RA.ReadonlyArray.With<S, B>, E, R>\n  <A, B, E, R>(\n    f: (a: A, i: number) => Effect.Effect<B, E, R>,\n    options: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly discard: true\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ): (self: Iterable<A>) => Effect.Effect<void, E, R>\n  <A, B, E, R>(\n    self: RA.NonEmptyReadonlyArray<A>,\n    f: (a: A, i: number) => Effect.Effect<B, E, R>,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly discard?: false | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    } | undefined\n  ): Effect.Effect<RA.NonEmptyArray<B>, E, R>\n  <A, B, E, R>(\n    self: Iterable<A>,\n    f: (a: A, i: number) => Effect.Effect<B, E, R>,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly discard?: false | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    } | undefined\n  ): Effect.Effect<Array<B>, E, R>\n  <A, B, E, R>(\n    self: Iterable<A>,\n    f: (a: A, i: number) => Effect.Effect<B, E, R>,\n    options: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly discard: true\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ): Effect.Effect<void, E, R>\n} = dual((args) => Predicate.isIterable(args[0]), <A, R, E, B>(\n  self: Iterable<A>,\n  f: (a: A, i: number) => Effect.Effect<B, E, R>,\n  options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly discard?: boolean | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }\n) =>\n  core.withFiberRuntime<A | void, E, R>((r) => {\n    const isRequestBatchingEnabled = options?.batching === true ||\n      (options?.batching === \"inherit\" && r.getFiberRef(core.currentRequestBatching))\n\n    if (options?.discard) {\n      return concurrency.match(\n        options.concurrency,\n        () =>\n          finalizersMaskInternal(ExecutionStrategy.sequential, options?.concurrentFinalizers)((restore) =>\n            isRequestBatchingEnabled\n              ? forEachConcurrentDiscard(self, (a, i) => restore(f(a, i)), true, false, 1)\n              : core.forEachSequentialDiscard(self, (a, i) => restore(f(a, i)))\n          ),\n        () =>\n          finalizersMaskInternal(ExecutionStrategy.parallel, options?.concurrentFinalizers)((restore) =>\n            forEachConcurrentDiscard(self, (a, i) => restore(f(a, i)), isRequestBatchingEnabled, false)\n          ),\n        (n) =>\n          finalizersMaskInternal(ExecutionStrategy.parallelN(n), options?.concurrentFinalizers)((restore) =>\n            forEachConcurrentDiscard(self, (a, i) => restore(f(a, i)), isRequestBatchingEnabled, false, n)\n          )\n      )\n    }\n\n    return concurrency.match(\n      options?.concurrency,\n      () =>\n        finalizersMaskInternal(ExecutionStrategy.sequential, options?.concurrentFinalizers)((restore) =>\n          isRequestBatchingEnabled\n            ? forEachParN(self, 1, (a, i) => restore(f(a, i)), true)\n            : core.forEachSequential(self, (a, i) => restore(f(a, i)))\n        ),\n      () =>\n        finalizersMaskInternal(ExecutionStrategy.parallel, options?.concurrentFinalizers)((restore) =>\n          forEachParUnbounded(self, (a, i) => restore(f(a, i)), isRequestBatchingEnabled)\n        ),\n      (n) =>\n        finalizersMaskInternal(ExecutionStrategy.parallelN(n), options?.concurrentFinalizers)((restore) =>\n          forEachParN(self, n, (a, i) => restore(f(a, i)), isRequestBatchingEnabled)\n        )\n    )\n  }))\n\n/* @internal */\nexport const forEachParUnbounded = <A, B, E, R>(\n  self: Iterable<A>,\n  f: (a: A, i: number) => Effect.Effect<B, E, R>,\n  batching: boolean\n): Effect.Effect<Array<B>, E, R> =>\n  core.suspend(() => {\n    const as = RA.fromIterable(self)\n    const array = new Array<B>(as.length)\n    const fn = (a: A, i: number) => core.flatMap(f(a, i), (b) => core.sync(() => array[i] = b))\n    return core.zipRight(forEachConcurrentDiscard(as, fn, batching, false), core.succeed(array))\n  })\n\n/** @internal */\nexport const forEachConcurrentDiscard = <A, X, E, R>(\n  self: Iterable<A>,\n  f: (a: A, i: number) => Effect.Effect<X, E, R>,\n  batching: boolean,\n  processAll: boolean,\n  n?: number\n): Effect.Effect<void, E, R> =>\n  core.uninterruptibleMask((restore) =>\n    core.transplant((graft) =>\n      core.withFiberRuntime<void, E, R>((parent) => {\n        let todos = Array.from(self).reverse()\n        let target = todos.length\n        if (target === 0) {\n          return core.void\n        }\n        let counter = 0\n        let interrupted = false\n        const fibersCount = n ? Math.min(todos.length, n) : todos.length\n        const fibers = new Set<FiberRuntime<Exit.Exit<X, E> | Effect.Blocked<X, E>>>()\n        const results = new Array()\n        const interruptAll = () =>\n          fibers.forEach((fiber) => {\n            fiber.currentScheduler.scheduleTask(() => {\n              fiber.unsafeInterruptAsFork(parent.id())\n            }, 0)\n          })\n        const startOrder = new Array<FiberRuntime<Exit.Exit<X, E> | Effect.Blocked<X, E>>>()\n        const joinOrder = new Array<FiberRuntime<Exit.Exit<X, E> | Effect.Blocked<X, E>>>()\n        const residual = new Array<core.Blocked>()\n        const collectExits = () => {\n          const exits: Array<Exit.Exit<any, E>> = results\n            .filter(({ exit }) => exit._tag === \"Failure\")\n            .sort((a, b) => a.index < b.index ? -1 : a.index === b.index ? 0 : 1)\n            .map(({ exit }) => exit)\n          if (exits.length === 0) {\n            exits.push(core.exitVoid)\n          }\n          return exits\n        }\n        const runFiber = <A, E, R>(eff: Effect.Effect<A, E, R>, interruptImmediately = false) => {\n          const runnable = core.uninterruptible(graft(eff))\n          const fiber = unsafeForkUnstarted(\n            runnable,\n            parent,\n            parent.currentRuntimeFlags,\n            fiberScope.globalScope\n          )\n          parent.currentScheduler.scheduleTask(() => {\n            if (interruptImmediately) {\n              fiber.unsafeInterruptAsFork(parent.id())\n            }\n            fiber.resume(runnable)\n          }, 0)\n          return fiber\n        }\n        const onInterruptSignal = () => {\n          if (!processAll) {\n            target -= todos.length\n            todos = []\n          }\n          interrupted = true\n          interruptAll()\n        }\n        const stepOrExit = batching ? core.step : core.exit\n        const processingFiber = runFiber(\n          core.async<any, any, any>((resume) => {\n            const pushResult = <X, E>(res: Exit.Exit<X, E> | Effect.Blocked<X, E>, index: number) => {\n              if (res._op === \"Blocked\") {\n                residual.push(res as core.Blocked)\n              } else {\n                results.push({ index, exit: res })\n                if (res._op === \"Failure\" && !interrupted) {\n                  onInterruptSignal()\n                }\n              }\n            }\n            const next = () => {\n              if (todos.length > 0) {\n                const a = todos.pop()!\n                let index = counter++\n                const returnNextElement = () => {\n                  const a = todos.pop()!\n                  index = counter++\n                  return core.flatMap(core.yieldNow(), () =>\n                    core.flatMap(\n                      stepOrExit(restore(f(a, index))),\n                      onRes\n                    ))\n                }\n                const onRes = (\n                  res: Exit.Exit<X, E> | Effect.Blocked<X, E>\n                ): Effect.Effect<Exit.Exit<X, E> | Effect.Blocked<X, E>, never, R> => {\n                  if (todos.length > 0) {\n                    pushResult(res, index)\n                    if (todos.length > 0) {\n                      return returnNextElement()\n                    }\n                  }\n                  return core.succeed(res)\n                }\n                const todo = core.flatMap(\n                  stepOrExit(restore(f(a, index))),\n                  onRes\n                )\n                const fiber = runFiber(todo)\n                startOrder.push(fiber)\n                fibers.add(fiber)\n                if (interrupted) {\n                  fiber.currentScheduler.scheduleTask(() => {\n                    fiber.unsafeInterruptAsFork(parent.id())\n                  }, 0)\n                }\n                fiber.addObserver((wrapped) => {\n                  let exit: Exit.Exit<any, any> | core.Blocked\n                  if (wrapped._op === \"Failure\") {\n                    exit = wrapped\n                  } else {\n                    exit = wrapped.effect_instruction_i0 as any\n                  }\n                  joinOrder.push(fiber)\n                  fibers.delete(fiber)\n                  pushResult(exit, index)\n                  if (results.length === target) {\n                    resume(core.succeed(Option.getOrElse(\n                      core.exitCollectAll(collectExits(), { parallel: true }),\n                      () => core.exitVoid\n                    )))\n                  } else if (residual.length + results.length === target) {\n                    const exits = collectExits()\n                    const requests = residual.map((blocked) => blocked.effect_instruction_i0).reduce(RequestBlock_.par)\n                    resume(core.succeed(core.blocked(\n                      requests,\n                      forEachConcurrentDiscard(\n                        [\n                          Option.getOrElse(\n                            core.exitCollectAll(exits, { parallel: true }),\n                            () => core.exitVoid\n                          ),\n                          ...residual.map((blocked) => blocked.effect_instruction_i1)\n                        ],\n                        (i) => i,\n                        batching,\n                        true,\n                        n\n                      )\n                    )))\n                  } else {\n                    next()\n                  }\n                })\n              }\n            }\n            for (let i = 0; i < fibersCount; i++) {\n              next()\n            }\n          })\n        )\n        return core.asVoid(\n          core.onExit(\n            core.flatten(restore(internalFiber.join(processingFiber))),\n            core.exitMatch({\n              onFailure: (cause) => {\n                onInterruptSignal()\n                const target = residual.length + 1\n                const concurrency = Math.min(typeof n === \"number\" ? n : residual.length, residual.length)\n                const toPop = Array.from(residual)\n                return core.async<any, any>((cb) => {\n                  const exits: Array<Exit.Exit<any, any>> = []\n                  let count = 0\n                  let index = 0\n                  const check = (index: number, hitNext: boolean) => (exit: Exit.Exit<any, any>) => {\n                    exits[index] = exit\n                    count++\n                    if (count === target) {\n                      cb(core.exitSucceed(core.exitFailCause(cause)))\n                    }\n                    if (toPop.length > 0 && hitNext) {\n                      next()\n                    }\n                  }\n                  const next = () => {\n                    runFiber(toPop.pop()!, true).addObserver(check(index, true))\n                    index++\n                  }\n                  processingFiber.addObserver(check(index, false))\n                  index++\n                  for (let i = 0; i < concurrency; i++) {\n                    next()\n                  }\n                }) as any\n              },\n              onSuccess: () => core.forEachSequential(joinOrder, (f) => f.inheritAll)\n            })\n          )\n        )\n      })\n    )\n  )\n\n/* @internal */\nexport const forEachParN = <A, B, E, R>(\n  self: Iterable<A>,\n  n: number,\n  f: (a: A, i: number) => Effect.Effect<B, E, R>,\n  batching: boolean\n): Effect.Effect<Array<B>, E, R> =>\n  core.suspend(() => {\n    const as = RA.fromIterable(self)\n    const array = new Array<B>(as.length)\n    const fn = (a: A, i: number) => core.map(f(a, i), (b) => array[i] = b)\n    return core.zipRight(forEachConcurrentDiscard(as, fn, batching, false, n), core.succeed(array))\n  })\n\n/* @internal */\nexport const fork = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<Fiber.RuntimeFiber<A, E>, never, R> =>\n  core.withFiberRuntime((state, status) => core.succeed(unsafeFork(self, state, status.runtimeFlags)))\n\n/* @internal */\nexport const forkDaemon = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<Fiber.RuntimeFiber<A, E>, never, R> =>\n  forkWithScopeOverride(self, fiberScope.globalScope)\n\n/* @internal */\nexport const forkWithErrorHandler = dual<\n  <E, X>(\n    handler: (e: E) => Effect.Effect<X>\n  ) => <A, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<Fiber.RuntimeFiber<A, E>, never, R>,\n  <A, E, R, X>(\n    self: Effect.Effect<A, E, R>,\n    handler: (e: E) => Effect.Effect<X>\n  ) => Effect.Effect<Fiber.RuntimeFiber<A, E>, never, R>\n>(2, (self, handler) =>\n  fork(core.onError(self, (cause) => {\n    const either = internalCause.failureOrCause(cause)\n    switch (either._tag) {\n      case \"Left\":\n        return handler(either.left)\n      case \"Right\":\n        return core.failCause(either.right)\n    }\n  })))\n\n/** @internal */\nexport const unsafeFork = <A, E, R, E2, B>(\n  effect: Effect.Effect<A, E, R>,\n  parentFiber: FiberRuntime<B, E2>,\n  parentRuntimeFlags: RuntimeFlags.RuntimeFlags,\n  overrideScope: fiberScope.FiberScope | null = null\n): FiberRuntime<A, E> => {\n  const childFiber = unsafeMakeChildFiber(effect, parentFiber, parentRuntimeFlags, overrideScope)\n  childFiber.resume(effect)\n  return childFiber\n}\n\n/** @internal */\nexport const unsafeForkUnstarted = <A, E, R, E2, B>(\n  effect: Effect.Effect<A, E, R>,\n  parentFiber: FiberRuntime<B, E2>,\n  parentRuntimeFlags: RuntimeFlags.RuntimeFlags,\n  overrideScope: fiberScope.FiberScope | null = null\n): FiberRuntime<A, E> => {\n  const childFiber = unsafeMakeChildFiber(effect, parentFiber, parentRuntimeFlags, overrideScope)\n  return childFiber\n}\n\n/** @internal */\nexport const unsafeMakeChildFiber = <A, E, R, E2, B>(\n  effect: Effect.Effect<A, E, R>,\n  parentFiber: FiberRuntime<B, E2>,\n  parentRuntimeFlags: RuntimeFlags.RuntimeFlags,\n  overrideScope: fiberScope.FiberScope | null = null\n): FiberRuntime<A, E> => {\n  const childId = FiberId.unsafeMake()\n  const parentFiberRefs = parentFiber.getFiberRefs()\n  const childFiberRefs = fiberRefs.forkAs(parentFiberRefs, childId)\n  const childFiber = new FiberRuntime<A, E>(childId, childFiberRefs, parentRuntimeFlags)\n  const childContext = fiberRefs.getOrDefault(\n    childFiberRefs,\n    core.currentContext as unknown as FiberRef.FiberRef<Context.Context<R>>\n  )\n  const supervisor = childFiber.currentSupervisor\n\n  supervisor.onStart(\n    childContext,\n    effect,\n    Option.some(parentFiber),\n    childFiber\n  )\n\n  childFiber.addObserver((exit) => supervisor.onEnd(exit, childFiber))\n\n  const parentScope = overrideScope !== null ? overrideScope : pipe(\n    parentFiber.getFiberRef(core.currentForkScopeOverride),\n    Option.getOrElse(() => parentFiber.scope())\n  )\n\n  parentScope.add(parentRuntimeFlags, childFiber)\n\n  return childFiber\n}\n\n/* @internal */\nconst forkWithScopeOverride = <A, E, R>(\n  self: Effect.Effect<A, E, R>,\n  scopeOverride: fiberScope.FiberScope\n): Effect.Effect<Fiber.RuntimeFiber<A, E>, never, R> =>\n  core.withFiberRuntime((parentFiber, parentStatus) =>\n    core.succeed(unsafeFork(self, parentFiber, parentStatus.runtimeFlags, scopeOverride))\n  )\n\n/* @internal */\nexport const mergeAll = dual<\n  <Z, Eff extends Effect.Effect<any, any, any>>(\n    zero: Z,\n    f: (z: Z, a: Effect.Effect.Success<Eff>, i: number) => Z,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => (elements: Iterable<Eff>) => Effect.Effect<Z, Effect.Effect.Error<Eff>, Effect.Effect.Context<Eff>>,\n  <Eff extends Effect.Effect<any, any, any>, Z>(\n    elements: Iterable<Eff>,\n    zero: Z,\n    f: (z: Z, a: Effect.Effect.Success<Eff>, i: number) => Z,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => Effect.Effect<Z, Effect.Effect.Error<Eff>, Effect.Effect.Context<Eff>>\n>(\n  (args) => Predicate.isFunction(args[2]),\n  <A, E, R, Z>(elements: Iterable<Effect.Effect<A, E, R>>, zero: Z, f: (z: Z, a: A, i: number) => Z, options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }) =>\n    concurrency.matchSimple(\n      options?.concurrency,\n      () =>\n        RA.fromIterable(elements).reduce(\n          (acc, a, i) => core.zipWith(acc, a, (acc, a) => f(acc, a, i)),\n          core.succeed(zero) as Effect.Effect<Z, E, R>\n        ),\n      () =>\n        core.flatMap(Ref.make(zero), (acc) =>\n          core.flatMap(\n            forEach(\n              elements,\n              (effect, i) => core.flatMap(effect, (a) => Ref.update(acc, (b) => f(b, a, i))),\n              options\n            ),\n            () => Ref.get(acc)\n          ))\n    )\n)\n\n/* @internal */\nexport const partition = dual<\n  <A, B, E, R>(\n    f: (a: A, i: number) => Effect.Effect<B, E, R>,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => (elements: Iterable<A>) => Effect.Effect<[excluded: Array<E>, satisfying: Array<B>], never, R>,\n  <A, B, E, R>(\n    elements: Iterable<A>,\n    f: (a: A, i: number) => Effect.Effect<B, E, R>,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => Effect.Effect<[excluded: Array<E>, satisfying: Array<B>], never, R>\n>((args) => Predicate.isIterable(args[0]), (elements, f, options) =>\n  pipe(\n    forEach(elements, (a, i) => core.either(f(a, i)), options),\n    core.map((chunk) => core.partitionMap(chunk, identity))\n  ))\n\n/* @internal */\nexport const validateAll = dual<\n  {\n    <A, B, E, R>(\n      f: (a: A, i: number) => Effect.Effect<B, E, R>,\n      options?: {\n        readonly concurrency?: Concurrency | undefined\n        readonly batching?: boolean | \"inherit\" | undefined\n        readonly discard?: false | undefined\n        readonly concurrentFinalizers?: boolean | undefined\n      }\n    ): (elements: Iterable<A>) => Effect.Effect<Array<B>, RA.NonEmptyArray<E>, R>\n    <A, B, E, R>(\n      f: (a: A, i: number) => Effect.Effect<B, E, R>,\n      options: {\n        readonly concurrency?: Concurrency | undefined\n        readonly batching?: boolean | \"inherit\" | undefined\n        readonly discard: true\n        readonly concurrentFinalizers?: boolean | undefined\n      }\n    ): (elements: Iterable<A>) => Effect.Effect<void, RA.NonEmptyArray<E>, R>\n  },\n  {\n    <A, B, E, R>(\n      elements: Iterable<A>,\n      f: (a: A, i: number) => Effect.Effect<B, E, R>,\n      options?: {\n        readonly concurrency?: Concurrency | undefined\n        readonly batching?: boolean | \"inherit\" | undefined\n        readonly discard?: false | undefined\n        readonly concurrentFinalizers?: boolean | undefined\n      }\n    ): Effect.Effect<Array<B>, RA.NonEmptyArray<E>, R>\n    <A, B, E, R>(\n      elements: Iterable<A>,\n      f: (a: A, i: number) => Effect.Effect<B, E, R>,\n      options: {\n        readonly concurrency?: Concurrency | undefined\n        readonly batching?: boolean | \"inherit\" | undefined\n        readonly discard: true\n        readonly concurrentFinalizers?: boolean | undefined\n      }\n    ): Effect.Effect<void, RA.NonEmptyArray<E>, R>\n  }\n>(\n  (args) => Predicate.isIterable(args[0]),\n  <A, B, E, R>(elements: Iterable<A>, f: (a: A, i: number) => Effect.Effect<B, E, R>, options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly discard?: boolean | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }): Effect.Effect<any, RA.NonEmptyArray<E>, R> =>\n    core.flatMap(\n      partition(elements, f, {\n        concurrency: options?.concurrency,\n        batching: options?.batching,\n        concurrentFinalizers: options?.concurrentFinalizers\n      }),\n      ([es, bs]) =>\n        RA.isNonEmptyArray(es)\n          ? core.fail(es)\n          : options?.discard\n          ? core.void\n          : core.succeed(bs)\n    )\n)\n\n/* @internal */\nexport const raceAll: <Eff extends Effect.Effect<any, any, any>>(\n  all: Iterable<Eff>\n) => Effect.Effect<Effect.Effect.Success<Eff>, Effect.Effect.Error<Eff>, Effect.Effect.Context<Eff>> = <\n  A,\n  E,\n  R\n>(all: Iterable<Effect.Effect<A, E, R>>): Effect.Effect<A, E, R> => {\n  const list = Chunk.fromIterable(all)\n  if (!Chunk.isNonEmpty(list)) {\n    return core.dieSync(() => new core.IllegalArgumentException(`Received an empty collection of effects`))\n  }\n  const self = Chunk.headNonEmpty(list)\n  const effects = Chunk.tailNonEmpty(list)\n  const inheritAll = (res: readonly [A, Fiber.Fiber<A, E>]) =>\n    pipe(\n      internalFiber.inheritAll(res[1]),\n      core.as(res[0])\n    )\n  return pipe(\n    core.deferredMake<readonly [A, Fiber.Fiber<A, E>], E>(),\n    core.flatMap((done) =>\n      pipe(\n        Ref.make(effects.length),\n        core.flatMap((fails) =>\n          core.uninterruptibleMask<A, E, R>((restore) =>\n            pipe(\n              fork(core.interruptible(self)),\n              core.flatMap((head) =>\n                pipe(\n                  effects,\n                  core.forEachSequential((effect) => fork(core.interruptible(effect))),\n                  core.map((fibers) => Chunk.unsafeFromArray(fibers)),\n                  core.map((tail) => pipe(tail, Chunk.prepend(head)) as Chunk.Chunk<Fiber.RuntimeFiber<A, E>>),\n                  core.tap((fibers) =>\n                    pipe(\n                      fibers,\n                      RA.reduce(core.void, (effect, fiber) =>\n                        pipe(\n                          effect,\n                          core.zipRight(\n                            pipe(\n                              internalFiber._await(fiber),\n                              core.flatMap(raceAllArbiter(fibers, fiber, done, fails)),\n                              fork,\n                              core.asVoid\n                            )\n                          )\n                        ))\n                    )\n                  ),\n                  core.flatMap((fibers) =>\n                    pipe(\n                      restore(pipe(Deferred.await(done), core.flatMap(inheritAll))),\n                      core.onInterrupt(() =>\n                        pipe(\n                          fibers,\n                          RA.reduce(\n                            core.void,\n                            (effect, fiber) => pipe(effect, core.zipLeft(core.interruptFiber(fiber)))\n                          )\n                        )\n                      )\n                    )\n                  )\n                )\n              )\n            )\n          )\n        )\n      )\n    )\n  )\n}\n\nconst raceAllArbiter = <E, E1, A, A1>(\n  fibers: Iterable<Fiber.Fiber<A | A1, E | E1>>,\n  winner: Fiber.Fiber<A | A1, E | E1>,\n  deferred: Deferred.Deferred<readonly [A | A1, Fiber.Fiber<A | A1, E | E1>], E | E1>,\n  fails: Ref.Ref<number>\n) =>\n(exit: Exit.Exit<A | A1, E | E1>): Effect.Effect<void> =>\n  core.exitMatchEffect(exit, {\n    onFailure: (cause) =>\n      pipe(\n        Ref.modify(fails, (fails) =>\n          [\n            fails === 0 ?\n              pipe(core.deferredFailCause(deferred, cause), core.asVoid) :\n              core.void,\n            fails - 1\n          ] as const),\n        core.flatten\n      ),\n    onSuccess: (value): Effect.Effect<void> =>\n      pipe(\n        core.deferredSucceed(deferred, [value, winner] as const),\n        core.flatMap((set) =>\n          set ?\n            pipe(\n              Chunk.fromIterable(fibers),\n              RA.reduce(\n                core.void,\n                (effect, fiber) =>\n                  fiber === winner ?\n                    effect :\n                    pipe(effect, core.zipLeft(core.interruptFiber(fiber)))\n              )\n            ) :\n            core.void\n        )\n      )\n  })\n\n/* @internal */\nexport const reduceEffect = dual<\n  <Z, E, R, Eff extends Effect.Effect<any, any, any>>(\n    zero: Effect.Effect<Z, E, R>,\n    f: (z: NoInfer<Z>, a: Effect.Effect.Success<Eff>, i: number) => Z,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => (elements: Iterable<Eff>) => Effect.Effect<Z, E | Effect.Effect.Error<Eff>, R | Effect.Effect.Context<Eff>>,\n  <Eff extends Effect.Effect<any, any, any>, Z, E, R>(\n    elements: Iterable<Eff>,\n    zero: Effect.Effect<Z, E, R>,\n    f: (z: NoInfer<Z>, a: Effect.Effect.Success<Eff>, i: number) => Z,\n    options?: {\n      readonly concurrency?: Concurrency | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => Effect.Effect<Z, E | Effect.Effect.Error<Eff>, R | Effect.Effect.Context<Eff>>\n>((args) => Predicate.isIterable(args[0]) && !core.isEffect(args[0]), <A, E, R, Z>(\n  elements: Iterable<Effect.Effect<A, E, R>>,\n  zero: Effect.Effect<Z, E, R>,\n  f: (z: NoInfer<Z>, a: NoInfer<A>, i: number) => Z,\n  options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }\n) =>\n  concurrency.matchSimple(\n    options?.concurrency,\n    () => RA.fromIterable(elements).reduce((acc, a, i) => core.zipWith(acc, a, (acc, a) => f(acc, a, i)), zero),\n    () =>\n      core.suspend(() =>\n        pipe(\n          mergeAll(\n            [zero, ...elements],\n            Option.none<Z>(),\n            (acc, elem, i) => {\n              switch (acc._tag) {\n                case \"None\": {\n                  return Option.some(elem as Z)\n                }\n                case \"Some\": {\n                  return Option.some(f(acc.value, elem as A, i))\n                }\n              }\n            },\n            options\n          ),\n          core.map((option) => {\n            switch (option._tag) {\n              case \"None\": {\n                throw new Error(\n                  \"BUG: Effect.reduceEffect - please report an issue at https://github.com/Effect-TS/effect/issues\"\n                )\n              }\n              case \"Some\": {\n                return option.value\n              }\n            }\n          })\n        )\n      )\n  ))\n\n/* @internal */\nexport const parallelFinalizers = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<A, E, R> =>\n  core.contextWithEffect((context) =>\n    Option.match(Context.getOption(context, scopeTag), {\n      onNone: () => self,\n      onSome: (scope) => {\n        switch (scope.strategy._tag) {\n          case \"Parallel\":\n            return self\n          case \"Sequential\":\n          case \"ParallelN\":\n            return core.flatMap(\n              core.scopeFork(scope, ExecutionStrategy.parallel),\n              (inner) => scopeExtend(self, inner)\n            )\n        }\n      }\n    })\n  )\n\n/* @internal */\nexport const parallelNFinalizers =\n  (parallelism: number) => <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<A, E, R> =>\n    core.contextWithEffect((context) =>\n      Option.match(Context.getOption(context, scopeTag), {\n        onNone: () => self,\n        onSome: (scope) => {\n          if (scope.strategy._tag === \"ParallelN\" && scope.strategy.parallelism === parallelism) {\n            return self\n          }\n          return core.flatMap(\n            core.scopeFork(scope, ExecutionStrategy.parallelN(parallelism)),\n            (inner) => scopeExtend(self, inner)\n          )\n        }\n      })\n    )\n\n/* @internal */\nexport const finalizersMask = (strategy: ExecutionStrategy.ExecutionStrategy) =>\n<A, E, R>(\n  self: (\n    restore: <A1, E1, R1>(self: Effect.Effect<A1, E1, R1>) => Effect.Effect<A1, E1, R1>\n  ) => Effect.Effect<A, E, R>\n): Effect.Effect<A, E, R> => finalizersMaskInternal(strategy, true)(self)\n\n/* @internal */\nexport const finalizersMaskInternal =\n  (strategy: ExecutionStrategy.ExecutionStrategy, concurrentFinalizers?: boolean | undefined) =>\n  <A, E, R>(\n    self: (\n      restore: <A1, E1, R1>(self: Effect.Effect<A1, E1, R1>) => Effect.Effect<A1, E1, R1>\n    ) => Effect.Effect<A, E, R>\n  ): Effect.Effect<A, E, R> =>\n    core.contextWithEffect((context) =>\n      Option.match(Context.getOption(context, scopeTag), {\n        onNone: () => self(identity),\n        onSome: (scope) => {\n          if (concurrentFinalizers === true) {\n            const patch = strategy._tag === \"Parallel\"\n              ? parallelFinalizers\n              : strategy._tag === \"Sequential\"\n              ? sequentialFinalizers\n              : parallelNFinalizers(strategy.parallelism)\n            switch (scope.strategy._tag) {\n              case \"Parallel\":\n                return patch(self(parallelFinalizers))\n              case \"Sequential\":\n                return patch(self(sequentialFinalizers))\n              case \"ParallelN\":\n                return patch(self(parallelNFinalizers(scope.strategy.parallelism)))\n            }\n          } else {\n            return self(identity)\n          }\n        }\n      })\n    )\n\n/* @internal */\nexport const scopeWith = <A, E, R>(\n  f: (scope: Scope.Scope) => Effect.Effect<A, E, R>\n): Effect.Effect<A, E, R | Scope.Scope> => core.flatMap(scopeTag, f)\n\n/** @internal */\nexport const scopedWith = <A, E, R>(\n  f: (scope: Scope.Scope) => Effect.Effect<A, E, R>\n): Effect.Effect<A, E, R> => core.flatMap(scopeMake(), (scope) => core.onExit(f(scope), (exit) => scope.close(exit)))\n\n/* @internal */\nexport const scopedEffect = <A, E, R>(effect: Effect.Effect<A, E, R>): Effect.Effect<A, E, Exclude<R, Scope.Scope>> =>\n  core.flatMap(scopeMake(), (scope) => scopeUse(effect, scope))\n\n/* @internal */\nexport const sequentialFinalizers = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<A, E, R> =>\n  core.contextWithEffect((context) =>\n    Option.match(Context.getOption(context, scopeTag), {\n      onNone: () => self,\n      onSome: (scope) => {\n        switch (scope.strategy._tag) {\n          case \"Sequential\":\n            return self\n          case \"Parallel\":\n          case \"ParallelN\":\n            return core.flatMap(\n              core.scopeFork(scope, ExecutionStrategy.sequential),\n              (inner) => scopeExtend(self, inner)\n            )\n        }\n      }\n    })\n  )\n\n/* @internal */\nexport const tagMetricsScoped = (key: string, value: string): Effect.Effect<void, never, Scope.Scope> =>\n  labelMetricsScoped([metricLabel.make(key, value)])\n\n/* @internal */\nexport const labelMetricsScoped = (\n  labels: Iterable<MetricLabel.MetricLabel>\n): Effect.Effect<void, never, Scope.Scope> =>\n  fiberRefLocallyScopedWith(core.currentMetricLabels, (old) => RA.union(old, labels))\n\n/* @internal */\nexport const using = dual<\n  <A, A2, E2, R2>(\n    use: (a: A) => Effect.Effect<A2, E2, R2>\n  ) => <E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2, E | E2, Exclude<R, Scope.Scope> | R2>,\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    use: (a: A) => Effect.Effect<A2, E2, R2>\n  ) => Effect.Effect<A2, E | E2, Exclude<R, Scope.Scope> | R2>\n>(2, (self, use) => scopedWith((scope) => core.flatMap(scopeExtend(self, scope), use)))\n\n/** @internal */\nexport const validate = dual<\n  <B, E1, R1>(\n    that: Effect.Effect<B, E1, R1>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<[A, B], E | E1, R | R1>,\n  <A, E, R, B, E1, R1>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<B, E1, R1>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => Effect.Effect<[A, B], E | E1, R | R1>\n>(\n  (args) => core.isEffect(args[1]),\n  (self, that, options) => validateWith(self, that, (a, b) => [a, b], options)\n)\n\n/** @internal */\nexport const validateWith = dual<\n  <B, E1, R1, A, C>(\n    that: Effect.Effect<B, E1, R1>,\n    f: (a: A, b: B) => C,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => <E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<C, E | E1, R | R1>,\n  <A, E, R, B, E1, R1, C>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<B, E1, R1>,\n    f: (a: A, b: B) => C,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => Effect.Effect<C, E | E1, R | R1>\n>((args) => core.isEffect(args[1]), (self, that, f, options) =>\n  core.flatten(zipWithOptions(\n    core.exit(self),\n    core.exit(that),\n    (ea, eb) =>\n      core.exitZipWith(ea, eb, {\n        onSuccess: f,\n        onFailure: (ca, cb) => options?.concurrent ? internalCause.parallel(ca, cb) : internalCause.sequential(ca, cb)\n      }),\n    options\n  )))\n\n/* @internal */\nexport const validateAllPar = dual<\n  <A, B, E, R>(\n    f: (a: A) => Effect.Effect<B, E, R>\n  ) => (elements: Iterable<A>) => Effect.Effect<Array<B>, Array<E>, R>,\n  <A, B, E, R>(\n    elements: Iterable<A>,\n    f: (a: A) => Effect.Effect<B, E, R>\n  ) => Effect.Effect<Array<B>, Array<E>, R>\n>(2, (elements, f) =>\n  core.flatMap(\n    partition(elements, f),\n    ([es, bs]) =>\n      es.length === 0\n        ? core.succeed(bs)\n        : core.fail(es)\n  ))\n\n/* @internal */\nexport const validateAllParDiscard = dual<\n  <A, B, E, R>(\n    f: (a: A) => Effect.Effect<B, E, R>\n  ) => (elements: Iterable<A>) => Effect.Effect<void, Array<E>, R>,\n  <A, B, E, R>(elements: Iterable<A>, f: (a: A) => Effect.Effect<B, E, R>) => Effect.Effect<void, Array<E>, R>\n>(2, (elements, f) =>\n  core.flatMap(\n    partition(elements, f),\n    ([es, _]) =>\n      es.length === 0\n        ? core.void\n        : core.fail(es)\n  ))\n\n/* @internal */\nexport const validateFirst = dual<\n  <A, B, E, R>(f: (a: A, i: number) => Effect.Effect<B, E, R>, options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }) => (elements: Iterable<A>) => Effect.Effect<B, Array<E>, R>,\n  <A, B, E, R>(elements: Iterable<A>, f: (a: A, i: number) => Effect.Effect<B, E, R>, options?: {\n    readonly concurrency?: Concurrency | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }) => Effect.Effect<B, Array<E>, R>\n>(\n  (args) => Predicate.isIterable(args[0]),\n  (elements, f, options) => core.flip(forEach(elements, (a, i) => core.flip(f(a, i)), options))\n)\n\n/* @internal */\nexport const withClockScoped = <C extends Clock.Clock>(c: C) =>\n  fiberRefLocallyScopedWith(defaultServices.currentServices, Context.add(clock.clockTag, c))\n\n/* @internal */\nexport const withRandomScoped = <A extends Random.Random>(value: A) =>\n  fiberRefLocallyScopedWith(defaultServices.currentServices, Context.add(randomTag, value))\n\n/* @internal */\nexport const withConfigProviderScoped = (provider: ConfigProvider) =>\n  fiberRefLocallyScopedWith(defaultServices.currentServices, Context.add(configProviderTag, provider))\n\n/* @internal */\nexport const withEarlyRelease = <A, E, R>(\n  self: Effect.Effect<A, E, R>\n): Effect.Effect<[Effect.Effect<void>, A], E, R | Scope.Scope> =>\n  scopeWith((parent) =>\n    core.flatMap(core.scopeFork(parent, executionStrategy.sequential), (child) =>\n      pipe(\n        self,\n        scopeExtend(child),\n        core.map((value) => [\n          core.fiberIdWith((fiberId) => core.scopeClose(child, core.exitInterrupt(fiberId))),\n          value\n        ])\n      ))\n  )\n\n/** @internal */\nexport const zipOptions = dual<\n  <A2, E2, R2>(\n    that: Effect.Effect<A2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => <A, E, R>(\n    self: Effect.Effect<A, E, R>\n  ) => Effect.Effect<[A, A2], E | E2, R | R2>,\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<A2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => Effect.Effect<[A, A2], E | E2, R | R2>\n>((args) => core.isEffect(args[1]), (\n  self,\n  that,\n  options\n) => zipWithOptions(self, that, (a, b) => [a, b], options))\n\n/** @internal */\nexport const zipLeftOptions = dual<\n  <A2, E2, R2>(\n    that: Effect.Effect<A2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => <A, E, R>(\n    self: Effect.Effect<A, E, R>\n  ) => Effect.Effect<A, E | E2, R | R2>,\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<A2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ) => Effect.Effect<A, E | E2, R | R2>\n>(\n  (args) => core.isEffect(args[1]),\n  (self, that, options) => {\n    if (options?.concurrent !== true && (options?.batching === undefined || options.batching === false)) {\n      return core.zipLeft(self, that)\n    }\n    return zipWithOptions(self, that, (a, _) => a, options)\n  }\n)\n\n/** @internal */\nexport const zipRightOptions: {\n  <A2, E2, R2>(\n    that: Effect.Effect<A2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ): <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2, E2 | E, R2 | R>\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<A2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ): Effect.Effect<A2, E2 | E, R2 | R>\n} = dual((args) => core.isEffect(args[1]), <A, E, R, A2, E2, R2>(\n  self: Effect.Effect<A, E, R>,\n  that: Effect.Effect<A2, E2, R2>,\n  options?: {\n    readonly concurrent?: boolean | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }\n): Effect.Effect<A2, E2 | E, R2 | R> => {\n  if (options?.concurrent !== true && (options?.batching === undefined || options.batching === false)) {\n    return core.zipRight(self, that)\n  }\n  return zipWithOptions(self, that, (_, b) => b, options)\n})\n\n/** @internal */\nexport const zipWithOptions: {\n  <A2, E2, R2, A, B>(\n    that: Effect.Effect<A2, E2, R2>,\n    f: (a: A, b: A2) => B,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ): <E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<B, E2 | E, R2 | R>\n  <A, E, R, A2, E2, R2, B>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<A2, E2, R2>,\n    f: (a: A, b: A2) => B,\n    options?: {\n      readonly concurrent?: boolean | undefined\n      readonly batching?: boolean | \"inherit\" | undefined\n      readonly concurrentFinalizers?: boolean | undefined\n    }\n  ): Effect.Effect<B, E2 | E, R2 | R>\n} = dual((args) => core.isEffect(args[1]), <A, E, R, A2, E2, R2, B>(\n  self: Effect.Effect<A, E, R>,\n  that: Effect.Effect<A2, E2, R2>,\n  f: (a: A, b: A2) => B,\n  options?: {\n    readonly concurrent?: boolean | undefined\n    readonly batching?: boolean | \"inherit\" | undefined\n    readonly concurrentFinalizers?: boolean | undefined\n  }\n): Effect.Effect<B, E2 | E, R2 | R> =>\n  core.map(\n    all([self, that], {\n      concurrency: options?.concurrent ? 2 : 1,\n      batching: options?.batching,\n      concurrentFinalizers: options?.concurrentFinalizers\n    }),\n    ([a, a2]) => f(a, a2)\n  ))\n\n/* @internal */\nexport const withRuntimeFlagsScoped = (\n  update: RuntimeFlagsPatch.RuntimeFlagsPatch\n): Effect.Effect<void, never, Scope.Scope> => {\n  if (update === RuntimeFlagsPatch.empty) {\n    return core.void\n  }\n  return pipe(\n    core.runtimeFlags,\n    core.flatMap((runtimeFlags) => {\n      const updatedRuntimeFlags = runtimeFlags_.patch(runtimeFlags, update)\n      const revertRuntimeFlags = runtimeFlags_.diff(updatedRuntimeFlags, runtimeFlags)\n      return pipe(\n        core.updateRuntimeFlags(update),\n        core.zipRight(addFinalizer(() => core.updateRuntimeFlags(revertRuntimeFlags))),\n        core.asVoid\n      )\n    }),\n    core.uninterruptible\n  )\n}\n\n// circular with Scope\n\n/** @internal */\nexport const scopeTag = Context.GenericTag<Scope.Scope>(\"effect/Scope\")\n\n/* @internal */\nexport const scope: Effect.Effect<Scope.Scope, never, Scope.Scope> = scopeTag\n\n/** @internal */\nexport interface ScopeImpl extends Scope.CloseableScope {\n  state: {\n    readonly _tag: \"Open\"\n    readonly finalizers: Map<{}, Scope.Scope.Finalizer>\n  } | {\n    readonly _tag: \"Closed\"\n    readonly exit: Exit.Exit<unknown, unknown>\n  }\n}\n\nconst scopeUnsafeAddFinalizer = (scope: ScopeImpl, fin: Scope.Scope.Finalizer): void => {\n  if (scope.state._tag === \"Open\") {\n    scope.state.finalizers.set({}, fin)\n  }\n}\n\nconst ScopeImplProto: Omit<ScopeImpl, \"strategy\" | \"state\"> = {\n  [core.ScopeTypeId]: core.ScopeTypeId,\n  [core.CloseableScopeTypeId]: core.CloseableScopeTypeId,\n  pipe() {\n    return pipeArguments(this, arguments)\n  },\n  fork(this: ScopeImpl, strategy) {\n    return core.sync(() => {\n      const newScope = scopeUnsafeMake(strategy)\n      if (this.state._tag === \"Closed\") {\n        newScope.state = this.state\n        return newScope\n      }\n      const key = {}\n      const fin = (exit: Exit.Exit<unknown, unknown>) => newScope.close(exit)\n      this.state.finalizers.set(key, fin)\n      scopeUnsafeAddFinalizer(newScope, (_) =>\n        core.sync(() => {\n          if (this.state._tag === \"Open\") {\n            this.state.finalizers.delete(key)\n          }\n        }))\n      return newScope\n    })\n  },\n  close(this: ScopeImpl, exit) {\n    return core.suspend(() => {\n      if (this.state._tag === \"Closed\") {\n        return core.void\n      }\n      const finalizers = Array.from(this.state.finalizers.values()).reverse()\n      this.state = { _tag: \"Closed\", exit }\n      if (finalizers.length === 0) {\n        return core.void\n      }\n      return executionStrategy.isSequential(this.strategy) ?\n        pipe(\n          core.forEachSequential(finalizers, (fin) => core.exit(fin(exit))),\n          core.flatMap((results) =>\n            pipe(\n              core.exitCollectAll(results),\n              Option.map(core.exitAsVoid),\n              Option.getOrElse(() => core.exitVoid)\n            )\n          )\n        ) :\n        executionStrategy.isParallel(this.strategy) ?\n        pipe(\n          forEachParUnbounded(finalizers, (fin) => core.exit(fin(exit)), false),\n          core.flatMap((results) =>\n            pipe(\n              core.exitCollectAll(results, { parallel: true }),\n              Option.map(core.exitAsVoid),\n              Option.getOrElse(() => core.exitVoid)\n            )\n          )\n        ) :\n        pipe(\n          forEachParN(finalizers, this.strategy.parallelism, (fin) => core.exit(fin(exit)), false),\n          core.flatMap((results) =>\n            pipe(\n              core.exitCollectAll(results, { parallel: true }),\n              Option.map(core.exitAsVoid),\n              Option.getOrElse(() => core.exitVoid)\n            )\n          )\n        )\n    })\n  },\n  addFinalizer(this: ScopeImpl, fin) {\n    return core.suspend(() => {\n      if (this.state._tag === \"Closed\") {\n        return fin(this.state.exit)\n      }\n      this.state.finalizers.set({}, fin)\n      return core.void\n    })\n  }\n}\n\nconst scopeUnsafeMake = (\n  strategy: ExecutionStrategy.ExecutionStrategy = executionStrategy.sequential\n): ScopeImpl => {\n  const scope = Object.create(ScopeImplProto)\n  scope.strategy = strategy\n  scope.state = { _tag: \"Open\", finalizers: new Map() }\n  return scope\n}\n\n/* @internal */\nexport const scopeMake = (\n  strategy: ExecutionStrategy.ExecutionStrategy = executionStrategy.sequential\n): Effect.Effect<Scope.Scope.Closeable> => core.sync(() => scopeUnsafeMake(strategy))\n\n/* @internal */\nexport const scopeExtend = dual<\n  (scope: Scope.Scope) => <A, E, R>(effect: Effect.Effect<A, E, R>) => Effect.Effect<A, E, Exclude<R, Scope.Scope>>,\n  <A, E, R>(effect: Effect.Effect<A, E, R>, scope: Scope.Scope) => Effect.Effect<A, E, Exclude<R, Scope.Scope>>\n>(\n  2,\n  <A, E, R>(effect: Effect.Effect<A, E, R>, scope: Scope.Scope) =>\n    core.mapInputContext<A, E, R, Exclude<R, Scope.Scope>>(\n      effect,\n      // @ts-expect-error\n      Context.merge(Context.make(scopeTag, scope))\n    )\n)\n\n/* @internal */\nexport const scopeUse = dual<\n  (\n    scope: Scope.Scope.Closeable\n  ) => <A, E, R>(effect: Effect.Effect<A, E, R>) => Effect.Effect<A, E, Exclude<R, Scope.Scope>>,\n  <A, E, R>(\n    effect: Effect.Effect<A, E, R>,\n    scope: Scope.Scope.Closeable\n  ) => Effect.Effect<A, E, Exclude<R, Scope.Scope>>\n>(2, (effect, scope) =>\n  pipe(\n    effect,\n    scopeExtend(scope),\n    core.onExit((exit) => scope.close(exit))\n  ))\n\n// circular with Supervisor\n\n/** @internal */\nexport const fiberRefUnsafeMakeSupervisor = (\n  initial: Supervisor.Supervisor<any>\n): FiberRef.FiberRef<Supervisor.Supervisor<any>> =>\n  core.fiberRefUnsafeMakePatch(initial, {\n    differ: SupervisorPatch.differ,\n    fork: SupervisorPatch.empty\n  })\n\n// circular with FiberRef\n\n/* @internal */\nexport const fiberRefLocallyScoped = dual<\n  <A>(value: A) => (self: FiberRef.FiberRef<A>) => Effect.Effect<void, never, Scope.Scope>,\n  <A>(self: FiberRef.FiberRef<A>, value: A) => Effect.Effect<void, never, Scope.Scope>\n>(2, (self, value) =>\n  core.asVoid(\n    acquireRelease(\n      core.flatMap(\n        core.fiberRefGet(self),\n        (oldValue) => core.as(core.fiberRefSet(self, value), oldValue)\n      ),\n      (oldValue) => core.fiberRefSet(self, oldValue)\n    )\n  ))\n\n/* @internal */\nexport const fiberRefLocallyScopedWith = dual<\n  <A>(f: (a: A) => A) => (self: FiberRef.FiberRef<A>) => Effect.Effect<void, never, Scope.Scope>,\n  <A>(self: FiberRef.FiberRef<A>, f: (a: A) => A) => Effect.Effect<void, never, Scope.Scope>\n>(2, (self, f) => core.fiberRefGetWith(self, (a) => fiberRefLocallyScoped(self, f(a))))\n\n/* @internal */\nexport const fiberRefMake = <A>(\n  initial: A,\n  options?: {\n    readonly fork?: ((a: A) => A) | undefined\n    readonly join?: ((left: A, right: A) => A) | undefined\n  }\n): Effect.Effect<FiberRef.FiberRef<A>, never, Scope.Scope> =>\n  fiberRefMakeWith(() => core.fiberRefUnsafeMake(initial, options))\n\n/* @internal */\nexport const fiberRefMakeWith = <Value>(\n  ref: LazyArg<FiberRef.FiberRef<Value>>\n): Effect.Effect<FiberRef.FiberRef<Value>, never, Scope.Scope> =>\n  acquireRelease(\n    core.tap(core.sync(ref), (ref) => core.fiberRefUpdate(ref, identity)),\n    (fiberRef) => core.fiberRefDelete(fiberRef)\n  )\n\n/* @internal */\nexport const fiberRefMakeContext = <A>(\n  initial: Context.Context<A>\n): Effect.Effect<FiberRef.FiberRef<Context.Context<A>>, never, Scope.Scope> =>\n  fiberRefMakeWith(() => core.fiberRefUnsafeMakeContext(initial))\n\n/* @internal */\nexport const fiberRefMakeRuntimeFlags = (\n  initial: RuntimeFlags.RuntimeFlags\n): Effect.Effect<FiberRef.FiberRef<RuntimeFlags.RuntimeFlags>, never, Scope.Scope> =>\n  fiberRefMakeWith(() => core.fiberRefUnsafeMakeRuntimeFlags(initial))\n\n/** @internal */\nexport const currentRuntimeFlags: FiberRef.FiberRef<RuntimeFlags.RuntimeFlags> = core.fiberRefUnsafeMakeRuntimeFlags(\n  runtimeFlags_.none\n)\n\n/** @internal */\nexport const currentSupervisor: FiberRef.FiberRef<Supervisor.Supervisor<any>> = fiberRefUnsafeMakeSupervisor(\n  supervisor.none\n)\n\n// circular with Fiber\n\n/* @internal */\nexport const fiberAwaitAll = <const T extends Iterable<Fiber.Fiber<any, any>>>(\n  fibers: T\n): Effect.Effect<\n  [T] extends [ReadonlyArray<infer U>]\n    ? number extends T[\"length\"] ? Array<U extends Fiber.Fiber<infer A, infer E> ? Exit.Exit<A, E> : never>\n    : { -readonly [K in keyof T]: T[K] extends Fiber.Fiber<infer A, infer E> ? Exit.Exit<A, E> : never }\n    : Array<T extends Iterable<infer U> ? U extends Fiber.Fiber<infer A, infer E> ? Exit.Exit<A, E> : never : never>\n> => forEach(fibers, internalFiber._await) as any\n\n/** @internal */\nexport const fiberAll = <A, E>(fibers: Iterable<Fiber.Fiber<A, E>>): Fiber.Fiber<Array<A>, E> => {\n  const _fiberAll = {\n    ...Effectable.CommitPrototype,\n    commit() {\n      return internalFiber.join(this)\n    },\n    [internalFiber.FiberTypeId]: internalFiber.fiberVariance,\n    id: () =>\n      RA.fromIterable(fibers).reduce((id, fiber) => FiberId.combine(id, fiber.id()), FiberId.none as FiberId.FiberId),\n    await: core.exit(forEachParUnbounded(fibers, (fiber) => core.flatten(fiber.await), false)),\n    children: core.map(forEachParUnbounded(fibers, (fiber) => fiber.children, false), RA.flatten),\n    inheritAll: core.forEachSequentialDiscard(fibers, (fiber) => fiber.inheritAll),\n    poll: core.map(\n      core.forEachSequential(fibers, (fiber) => fiber.poll),\n      RA.reduceRight(\n        Option.some<Exit.Exit<Array<A>, E>>(core.exitSucceed(new Array())),\n        (optionB, optionA) => {\n          switch (optionA._tag) {\n            case \"None\": {\n              return Option.none()\n            }\n            case \"Some\": {\n              switch (optionB._tag) {\n                case \"None\": {\n                  return Option.none()\n                }\n                case \"Some\": {\n                  return Option.some(\n                    core.exitZipWith(optionA.value, optionB.value, {\n                      onSuccess: (a, chunk) => [a, ...chunk],\n                      onFailure: internalCause.parallel\n                    })\n                  )\n                }\n              }\n            }\n          }\n        }\n      )\n    ),\n    interruptAsFork: (fiberId: FiberId.FiberId) =>\n      core.forEachSequentialDiscard(fibers, (fiber) => fiber.interruptAsFork(fiberId))\n  }\n  return _fiberAll\n}\n\n/* @internal */\nexport const fiberInterruptFork = <A, E>(self: Fiber.Fiber<A, E>): Effect.Effect<void> =>\n  core.asVoid(forkDaemon(core.interruptFiber(self)))\n\n/* @internal */\nexport const fiberJoinAll = <A, E>(fibers: Iterable<Fiber.Fiber<A, E>>): Effect.Effect<Array<A>, E> =>\n  internalFiber.join(fiberAll(fibers))\n\n/* @internal */\nexport const fiberScoped = <A, E>(self: Fiber.Fiber<A, E>): Effect.Effect<Fiber.Fiber<A, E>, never, Scope.Scope> =>\n  acquireRelease(core.succeed(self), core.interruptFiber)\n\n//\n// circular race\n//\n\n/** @internal */\nexport const raceWith = dual<\n  <A1, E1, R1, E, A, A2, E2, R2, A3, E3, R3>(\n    other: Effect.Effect<A1, E1, R1>,\n    options: {\n      readonly onSelfDone: (exit: Exit.Exit<A, E>, fiber: Fiber.Fiber<A1, E1>) => Effect.Effect<A2, E2, R2>\n      readonly onOtherDone: (exit: Exit.Exit<A1, E1>, fiber: Fiber.Fiber<A, E>) => Effect.Effect<A3, E3, R3>\n    }\n  ) => <R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A3, E2 | E3, R | R1 | R2 | R3>,\n  <A, E, R, A1, E1, R1, A2, E2, R2, A3, E3, R3>(\n    self: Effect.Effect<A, E, R>,\n    other: Effect.Effect<A1, E1, R1>,\n    options: {\n      readonly onSelfDone: (exit: Exit.Exit<A, E>, fiber: Fiber.Fiber<A1, E1>) => Effect.Effect<A2, E2, R2>\n      readonly onOtherDone: (exit: Exit.Exit<A1, E1>, fiber: Fiber.Fiber<A, E>) => Effect.Effect<A3, E3, R3>\n    }\n  ) => Effect.Effect<A2 | A3, E2 | E3, R | R1 | R2 | R3>\n>(3, (self, other, options) =>\n  raceFibersWith(self, other, {\n    onSelfWin: (winner, loser) =>\n      core.flatMap(winner.await, (exit) => {\n        switch (exit._tag) {\n          case OpCodes.OP_SUCCESS: {\n            return core.flatMap(\n              winner.inheritAll,\n              () => options.onSelfDone(exit, loser)\n            )\n          }\n          case OpCodes.OP_FAILURE: {\n            return options.onSelfDone(exit, loser)\n          }\n        }\n      }),\n    onOtherWin: (winner, loser) =>\n      core.flatMap(winner.await, (exit) => {\n        switch (exit._tag) {\n          case OpCodes.OP_SUCCESS: {\n            return core.flatMap(\n              winner.inheritAll,\n              () => options.onOtherDone(exit, loser)\n            )\n          }\n          case OpCodes.OP_FAILURE: {\n            return options.onOtherDone(exit, loser)\n          }\n        }\n      })\n  }))\n\n/** @internal */\nexport const disconnect = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<A, E, R> =>\n  core.uninterruptibleMask((restore) =>\n    core.fiberIdWith((fiberId) =>\n      core.flatMap(forkDaemon(restore(self)), (fiber) =>\n        pipe(\n          restore(internalFiber.join(fiber)),\n          core.onInterrupt(() => pipe(fiber, internalFiber.interruptAsFork(fiberId)))\n        ))\n    )\n  )\n\n/** @internal */\nexport const race = dual<\n  <A2, E2, R2>(\n    that: Effect.Effect<A2, E2, R2>\n  ) => <A, E, R>(\n    self: Effect.Effect<A, E, R>\n  ) => Effect.Effect<A | A2, E | E2, R | R2>,\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<A2, E2, R2>\n  ) => Effect.Effect<A | A2, E | E2, R | R2>\n>(\n  2,\n  (self, that) =>\n    core.fiberIdWith((parentFiberId) =>\n      raceWith(self, that, {\n        onSelfDone: (exit, right) =>\n          core.exitMatchEffect(exit, {\n            onFailure: (cause) =>\n              pipe(\n                internalFiber.join(right),\n                internalEffect.mapErrorCause((cause2) => internalCause.parallel(cause, cause2))\n              ),\n            onSuccess: (value) =>\n              pipe(\n                right,\n                core.interruptAsFiber(parentFiberId),\n                core.as(value)\n              )\n          }),\n        onOtherDone: (exit, left) =>\n          core.exitMatchEffect(exit, {\n            onFailure: (cause) =>\n              pipe(\n                internalFiber.join(left),\n                internalEffect.mapErrorCause((cause2) => internalCause.parallel(cause2, cause))\n              ),\n            onSuccess: (value) =>\n              pipe(\n                left,\n                core.interruptAsFiber(parentFiberId),\n                core.as(value)\n              )\n          })\n      })\n    )\n)\n\n/** @internal */\nexport const raceFibersWith = dual<\n  <A1, E1, R1, E, A, A2, E2, R2, A3, E3, R3>(\n    other: Effect.Effect<A1, E1, R1>,\n    options: {\n      readonly onSelfWin: (\n        winner: Fiber.RuntimeFiber<A, E>,\n        loser: Fiber.RuntimeFiber<A1, E1>\n      ) => Effect.Effect<A2, E2, R2>\n      readonly onOtherWin: (\n        winner: Fiber.RuntimeFiber<A1, E1>,\n        loser: Fiber.RuntimeFiber<A, E>\n      ) => Effect.Effect<A3, E3, R3>\n      readonly selfScope?: fiberScope.FiberScope | undefined\n      readonly otherScope?: fiberScope.FiberScope | undefined\n    }\n  ) => <R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A3, E2 | E3, R | R1 | R2 | R3>,\n  <A, E, R, A1, E1, R1, A2, E2, R2, A3, E3, R3>(\n    self: Effect.Effect<A, E, R>,\n    other: Effect.Effect<A1, E1, R1>,\n    options: {\n      readonly onSelfWin: (\n        winner: Fiber.RuntimeFiber<A, E>,\n        loser: Fiber.RuntimeFiber<A1, E1>\n      ) => Effect.Effect<A2, E2, R2>\n      readonly onOtherWin: (\n        winner: Fiber.RuntimeFiber<A1, E1>,\n        loser: Fiber.RuntimeFiber<A, E>\n      ) => Effect.Effect<A3, E3, R3>\n      readonly selfScope?: fiberScope.FiberScope | undefined\n      readonly otherScope?: fiberScope.FiberScope | undefined\n    }\n  ) => Effect.Effect<A2 | A3, E2 | E3, R | R1 | R2 | R3>\n>(3, <A, E, R, A1, E1, R1, A2, E2, R2, A3, E3, R3>(\n  self: Effect.Effect<A, E, R>,\n  other: Effect.Effect<A1, E1, R1>,\n  options: {\n    readonly onSelfWin: (\n      winner: Fiber.RuntimeFiber<A, E>,\n      loser: Fiber.RuntimeFiber<A1, E1>\n    ) => Effect.Effect<A2, E2, R2>\n    readonly onOtherWin: (\n      winner: Fiber.RuntimeFiber<A1, E1>,\n      loser: Fiber.RuntimeFiber<A, E>\n    ) => Effect.Effect<A3, E3, R3>\n    readonly selfScope?: fiberScope.FiberScope | undefined\n    readonly otherScope?: fiberScope.FiberScope | undefined\n  }\n) =>\n  core.withFiberRuntime((parentFiber, parentStatus) => {\n    const parentRuntimeFlags = parentStatus.runtimeFlags\n    const raceIndicator = MRef.make(true)\n    const leftFiber: FiberRuntime<A, E> = unsafeMakeChildFiber(\n      self,\n      parentFiber,\n      parentRuntimeFlags,\n      options.selfScope\n    )\n    const rightFiber: FiberRuntime<A1, E1> = unsafeMakeChildFiber(\n      other,\n      parentFiber,\n      parentRuntimeFlags,\n      options.otherScope\n    )\n    return core.async((cb) => {\n      leftFiber.addObserver(() => completeRace(leftFiber, rightFiber, options.onSelfWin, raceIndicator, cb))\n      rightFiber.addObserver(() => completeRace(rightFiber, leftFiber, options.onOtherWin, raceIndicator, cb))\n      leftFiber.startFork(self)\n      rightFiber.startFork(other)\n    }, FiberId.combine(leftFiber.id(), rightFiber.id()))\n  }))\n\nconst completeRace = <A2, A3, E2, E3, R, R1, R2, R3>(\n  winner: Fiber.RuntimeFiber<any, any>,\n  loser: Fiber.RuntimeFiber<any, any>,\n  cont: (winner: Fiber.RuntimeFiber<any, any>, loser: Fiber.RuntimeFiber<any, any>) => Effect.Effect<any, any, any>,\n  ab: MRef.MutableRef<boolean>,\n  cb: (_: Effect.Effect<A2 | A3, E2 | E3, R | R1 | R2 | R3>) => void\n): void => {\n  if (MRef.compareAndSet(true, false)(ab)) {\n    cb(cont(winner, loser))\n  }\n}\n\n/** @internal */\nexport const ensuring: {\n  <X, R1>(\n    finalizer: Effect.Effect<X, never, R1>\n  ): <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R1 | R>\n  <A, E, R, X, R1>(self: Effect.Effect<A, E, R>, finalizer: Effect.Effect<X, never, R1>): Effect.Effect<A, E, R1 | R>\n} = dual(\n  2,\n  <A, E, R, X, R1>(self: Effect.Effect<A, E, R>, finalizer: Effect.Effect<X, never, R1>): Effect.Effect<A, E, R1 | R> =>\n    core.uninterruptibleMask((restore) =>\n      core.matchCauseEffect(restore(self), {\n        onFailure: (cause1) =>\n          core.matchCauseEffect(finalizer, {\n            onFailure: (cause2) => core.failCause(internalCause.sequential(cause1, cause2)),\n            onSuccess: () => core.failCause(cause1)\n          }),\n        onSuccess: (a) => core.as(finalizer, a)\n      })\n    )\n)\n\n/** @internal */\nexport const invokeWithInterrupt: <A, E, R>(\n  self: Effect.Effect<A, E, R>,\n  entries: ReadonlyArray<Entry<unknown>>,\n  onInterrupt?: () => void\n) => Effect.Effect<void, E, R> = <A, E, R>(\n  self: Effect.Effect<A, E, R>,\n  entries: ReadonlyArray<Entry<unknown>>,\n  onInterrupt?: () => void\n) =>\n  core.fiberIdWith((id) =>\n    core.flatMap(\n      core.flatMap(\n        forkDaemon(core.interruptible(self)),\n        (processing) =>\n          core.async<void, E>((cb) => {\n            const counts = entries.map((_) => _.listeners.count)\n            const checkDone = () => {\n              if (counts.every((count) => count === 0)) {\n                if (\n                  entries.every((_) => {\n                    if (_.result.state.current._tag === \"Pending\") {\n                      return true\n                    } else if (\n                      _.result.state.current._tag === \"Done\" &&\n                      core.exitIsExit(_.result.state.current.effect) &&\n                      _.result.state.current.effect._tag === \"Failure\" &&\n                      internalCause.isInterrupted(_.result.state.current.effect.cause)\n                    ) {\n                      return true\n                    } else {\n                      return false\n                    }\n                  })\n                ) {\n                  cleanup.forEach((f) => f())\n                  onInterrupt?.()\n                  cb(core.interruptFiber(processing))\n                }\n              }\n            }\n            processing.addObserver((exit) => {\n              cleanup.forEach((f) => f())\n              cb(exit)\n            })\n            const cleanup = entries.map((r, i) => {\n              const observer = (count: number) => {\n                counts[i] = count\n                checkDone()\n              }\n              r.listeners.addObserver(observer)\n              return () => r.listeners.removeObserver(observer)\n            })\n            checkDone()\n            return core.sync(() => {\n              cleanup.forEach((f) => f())\n            })\n          })\n      ),\n      () =>\n        core.suspend(() => {\n          const residual = entries.flatMap((entry) => {\n            if (!entry.state.completed) {\n              return [entry]\n            }\n            return []\n          })\n          return core.forEachSequentialDiscard(\n            residual,\n            (entry) => complete(entry.request as any, core.exitInterrupt(id))\n          )\n        })\n    )\n  )\n\n/** @internal */\nexport const interruptWhenPossible = dual<\n  (all: Iterable<Request<any, any>>) => <A, E, R>(\n    self: Effect.Effect<A, E, R>\n  ) => Effect.Effect<void, E, R>,\n  <A, E, R>(\n    self: Effect.Effect<A, E, R>,\n    all: Iterable<Request<any, any>>\n  ) => Effect.Effect<void, E, R>\n>(2, (self, all) =>\n  core.fiberRefGetWith(\n    currentRequestMap,\n    (map) =>\n      core.suspend(() => {\n        const entries = RA.fromIterable(all).flatMap((_) => map.has(_) ? [map.get(_)!] : [])\n        return invokeWithInterrupt(self, entries)\n      })\n  ))\n\n// circular Tracer\n\n/** @internal */\nexport const makeSpanScoped = (\n  name: string,\n  options?: Tracer.SpanOptions | undefined\n): Effect.Effect<Tracer.Span, never, Scope.Scope> => {\n  options = tracer.addSpanStackTrace(options)\n  return core.uninterruptible(\n    core.withFiberRuntime((fiber) => {\n      const scope = Context.unsafeGet(fiber.getFiberRef(core.currentContext), scopeTag)\n      const span = internalEffect.unsafeMakeSpan(fiber, name, options)\n      const timingEnabled = fiber.getFiberRef(core.currentTracerTimingEnabled)\n      const clock_ = Context.get(fiber.getFiberRef(defaultServices.currentServices), clock.clockTag)\n      return core.as(\n        core.scopeAddFinalizerExit(scope, (exit) => internalEffect.endSpan(span, exit, clock_, timingEnabled)),\n        span\n      )\n    })\n  )\n}\n\n/* @internal */\nexport const withTracerScoped = (value: Tracer.Tracer): Effect.Effect<void, never, Scope.Scope> =>\n  fiberRefLocallyScopedWith(defaultServices.currentServices, Context.add(tracer.tracerTag, value))\n\n/** @internal */\nexport const withSpanScoped: {\n  (\n    name: string,\n    options?: Tracer.SpanOptions\n  ): <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, Scope.Scope | Exclude<R, Tracer.ParentSpan>>\n  <A, E, R>(\n    self: Effect.Effect<A, E, R>,\n    name: string,\n    options?: Tracer.SpanOptions\n  ): Effect.Effect<A, E, Scope.Scope | Exclude<R, Tracer.ParentSpan>>\n} = function() {\n  const dataFirst = typeof arguments[0] !== \"string\"\n  const name = dataFirst ? arguments[1] : arguments[0]\n  const options = tracer.addSpanStackTrace(dataFirst ? arguments[2] : arguments[1])\n  if (dataFirst) {\n    const self = arguments[0]\n    return core.flatMap(\n      makeSpanScoped(name, tracer.addSpanStackTrace(options)),\n      (span) => internalEffect.provideService(self, tracer.spanTag, span)\n    )\n  }\n  return (self: Effect.Effect<any, any, any>) =>\n    core.flatMap(\n      makeSpanScoped(name, tracer.addSpanStackTrace(options)),\n      (span) => internalEffect.provideService(self, tracer.spanTag, span)\n    )\n} as any\n"], "names": ["RA", "Boolean", "Chunk", "Context", "Deferred", "Effectable", "ExecutionStrategy", "FiberId", "FiberRefs", "FiberRefsPatch", "FiberStatus", "dual", "identity", "pipe", "globalValue", "HashMap", "HashSet", "Inspectable", "LogLevel", "Micro", "MRef", "Option", "pipeArguments", "Predicate", "Ref", "RuntimeFlagsPatch", "currentScheduler", "internalCall", "yieldWrapGet", "RequestBlock_", "internalCause", "clock", "currentRequestMap", "concurrency", "configProviderTag", "internalEffect", "core", "defaultServices", "consoleTag", "executionStrategy", "internalFiber", "FiberMessage", "fiberRefs", "fiberScope", "internalLogger", "metric", "metricBoundaries", "metricLabel", "OpCodes", "randomTag", "complete", "runtimeFlags_", "OpSupervision", "supervisor", "SupervisorPatch", "tracer", "version", "fiberStarted", "counter", "incremental", "fiberActive", "fiberSuccesses", "fiberFailures", "fiberLifetimes", "tagged", "histogram", "exponential", "start", "factor", "count", "EvaluationSignalContinue", "EvaluationSignalDone", "EvaluationSignalYieldNow", "runtime<PERSON><PERSON><PERSON><PERSON><PERSON>", "_E", "_", "_A", "absurd", "Error", "toStringUnknown", "YieldedOp", "Symbol", "for", "yieldedOpChannel", "currentOp", "contOpSuccess", "OP_ON_SUCCESS", "cont", "value", "effect_instruction_i1", "OnStep", "_cont", "exitSucceed", "OP_ON_SUCCESS_AND_FAILURE", "effect_instruction_i2", "OP_REVERT_FLAGS", "self", "patchRuntimeFlags", "currentRuntimeFlags", "patch", "interruptible", "isInterrupted", "exitFailCause", "getInterruptedCause", "OP_WHILE", "effect_instruction_i0", "pushStack", "void", "OP_ITERATOR", "state", "next", "done", "drainQueueWhileRunningTable", "OP_INTERRUPT_SIGNAL", "runtimeFlags", "cur", "message", "processNewInterruptSignal", "cause", "OP_RESUME", "_self", "_runtimeFlags", "_cur", "_message", "OP_STATEFUL", "onFiber", "running", "OP_YIELD_NOW", "flatMap", "yieldNow", "runBlockedRequests", "forEachSequentialDiscard", "flatten", "requestsByRequestResolver", "forEachConcurrentDiscard", "sequentialCollectionToChunk", "dataSource", "sequential", "map", "Map", "arr", "block", "push", "toReadonlyArray", "entry", "set", "request", "flat", "fiberRefLocally", "invokeWithInterrupt", "runAll", "for<PERSON>ach", "listeners", "interrupted", "_version", "getCurrentVersion", "FiberRuntime", "Class", "FiberTypeId", "fiberVariance", "RuntimeFiberTypeId", "_fiberRefs", "_fiberId", "_queue", "Array", "_children", "_observers", "_running", "_stack", "_asyncInterruptor", "_asyncBlockingOn", "_exitValue", "_steps", "_isYielding", "currentOpCount", "currentSupervisor", "currentTracer", "currentSpan", "currentContext", "currentDefaultServices", "constructor", "fiberId", "fiberRefs0", "runtimeFlags0", "runtimeMetrics", "tags", "getFiberRef", "currentMetricLabels", "unsafeUpdate", "refreshRefCache", "commit", "join", "id", "resume", "effect", "tell", "status", "ask", "isDone", "scope", "unsafeMake", "children", "fiber", "from", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Set", "currentInterruptedCause", "getFiberRefs", "f", "suspend", "deferred", "deferredUnsafeMake", "stateful", "deferredUnsafeDone", "sync", "deferred<PERSON><PERSON><PERSON>", "drainQueueLaterOnExecutor", "await", "async", "cb", "exit", "succeed", "addObserver", "removeObserver", "inheritAll", "withFiberRuntime", "parentFiber", "parentStatus", "parentFiberId", "parentFiberRefs", "parentRuntimeFlags", "childFiberRefs", "updatedFiberRefs", "joinAs", "setFiberRefs", "updatedRuntimeFlags", "diff", "exclude", "Interruption", "WindDown", "updateRuntimeFlags", "poll", "fromNullable", "unsafePoll", "interruptAsFork", "interruptSignal", "interrupt", "unsafeInterruptAsFork", "observer", "filter", "o", "setFiberRef", "unsafeDeleteFiberRef", "fiberRef", "delete_", "locals", "has", "get", "initial", "updateAs", "currentServices", "unsafeMap", "tracerTag", "key", "spanTag", "<PERSON><PERSON><PERSON><PERSON>", "child", "add", "<PERSON><PERSON><PERSON><PERSON>", "delete", "transferChildren", "size", "drainQueueOnCurrentThread", "recurse", "evaluationSignal", "prev", "globalThis", "currentFiberURI", "length", "evaluateMessageWhileSuspended", "splice", "scheduleTask", "run", "currentSchedulingPriority", "drain<PERSON>ueueWhileR<PERSON>ning", "cur0", "_tag", "isEmpty", "addInterruptedCause", "oldSC", "sendInterruptSignalToAllChildren", "told", "interrupt<PERSON>ll<PERSON><PERSON><PERSON><PERSON>", "it", "values", "body", "asVoid", "<PERSON><PERSON><PERSON>", "while", "step", "reportExitValue", "startTimeMillis", "endTimeMillis", "Date", "now", "OP_SUCCESS", "OP_FAILURE", "level", "currentUnhandledErrorLogLevel", "isInterruptedOnly", "log", "setExitValue", "i", "getLoggers", "currentLoggers", "overrideLogLevel", "logLevel", "isSome", "currentLogLevel", "minimumLogLevel", "currentMinimumLogLevel", "greaterThan", "spans", "currentLogSpan", "annotations", "currentLogAnnotations", "loggers", "contextMap", "clockService", "clockTag", "date", "unsafeCurrentTimeMillis", "withRedactableContext", "logger", "context", "evaluateEffect", "suspended", "effect0", "onResume", "eff", "run<PERSON><PERSON>", "op", "_op", "OP_YIELD", "cooperativeYielding", "exitVoid", "OP_ASYNC", "enable", "interruption", "onSuspend", "startFork", "oldRuntimeFlags", "newRuntimeFlags", "initiateAsync", "asyncRegister", "alreadyCalled", "callback", "e", "failCause", "die", "refs", "flags", "popStack", "item", "pop", "getNextSuccessCont", "frame", "OP_ON_FAILURE", "getNextFailCont", "OP_TAG", "unsafeGet", "Left", "fail", "left", "None", "NoSuchElementException", "Right", "right", "Some", "unsafeAsync", "microResume", "runFork", "provideContext", "none", "error", "defect", "abortResume", "unsafeInterrupt", "OP_SYNC", "undefined", "oldCur", "stripFailures", "OP_WITH_RUNTIME", "Blocked", "frames", "snap", "patchRefs", "patchFlags", "blocked", "newFiber", "uninterruptibleMask", "restore", "forkDaemon", "runRequestBlock", "RunBlocked", "OP_UPDATE_RUNTIME_FLAGS", "updateFlags", "revertFlags", "RevertFlags", "check", "OP_COMMIT", "onEffect", "<PERSON><PERSON><PERSON>", "priority", "EffectTypeId", "_V", "dieMessage", "hasProperty", "isInterruptedException", "fiberRefUnsafeMake", "fromLiteral", "loggerWithConsoleLog", "<PERSON><PERSON>ogger", "opts", "services", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unsafe", "loggerWithLeveledLog", "unsafeLogger", "debug", "info", "trace", "warn", "loggerWithConsoleError", "defaultLogger", "stringLogger", "jsonLogger", "logFmtLogger", "logfmtLogger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "structuredLogger", "tracer<PERSON><PERSON>ger", "span", "getOption", "attributes", "threadName", "label", "pretty", "renderErrorCause", "event", "isArray", "unsafeCurrentTimeNanos", "loggerWithSpanAnnotations", "mapInputOptions", "options", "traceId", "spanId", "name", "fiberRefUnsafeMakeHashSet", "make", "batchedLogger", "window", "buffer", "flush", "sleep", "zipRight", "forever", "scopeAddFinalizer", "interruptFiber", "addFinalizer", "as", "annotateLogsScoped", "arguments", "fiberRefLocallyScopedWith", "entries", "Object", "mutate", "whenLogLevel", "requiredLogLevel", "fiberState", "some", "acquireRelease", "args", "isEffect", "acquire", "release", "uninterruptible", "tap", "a", "acquireReleaseInterruptible", "ensuring", "finalizer", "runtime", "acquireRefs", "acquireFlags", "scopeAddFinalizerExit", "runtimeFinalizer", "preRefs", "preFlags", "inverseRefs", "withRuntimeFlags", "da<PERSON><PERSON><PERSON><PERSON><PERSON>", "forkScope", "currentForkScopeOverride", "globalScope", "_existsParFound", "exists", "isIterable", "elements", "predicate", "matchSimple", "existsLoop", "iterator", "matchEffect", "if_", "onTrue", "onFalse", "onFailure", "onSuccess", "index", "b", "predicate_", "negate", "not", "fromIterable", "reduceRight", "zipWith", "list", "getSomes", "allResolveInput", "input", "keys", "k", "res", "allValidate", "effects", "reconcile", "eitherEffects", "either", "batching", "concurrentFinalizers", "eithers", "errors", "successes", "errored", "discard", "allEither", "all", "arg", "mode", "allWith", "allSuccesses", "filterMap", "exitIsSuccess", "replicate", "n", "replicateEffect", "r", "isRequestBatchingEnabled", "currentRequestBatching", "match", "finalizersMaskInternal", "parallel", "parallelN", "forEachParN", "forEachSequential", "forEachParUnbounded", "array", "fn", "processAll", "transplant", "graft", "parent", "todos", "reverse", "target", "fibersCount", "Math", "min", "fibers", "results", "interruptAll", "startOrder", "joinOrder", "residual", "collectExits", "exits", "sort", "runFiber", "interruptImmediately", "runnable", "unsafeForkUnstarted", "onInterruptSignal", "stepOrExit", "processingFiber", "pushResult", "returnNextElement", "onRes", "todo", "wrapped", "getOr<PERSON><PERSON>e", "exitCollectAll", "requests", "reduce", "par", "onExit", "exitMatch", "toPop", "hitNext", "fork", "unsafeFork", "forkWithScopeOverride", "forkWithErrorHandler", "handler", "onError", "failureOrCause", "overrideScope", "child<PERSON>iber", "unsafeMakeChildFiber", "childId", "forkAs", "childContext", "onStart", "onEnd", "parentScope", "scopeOverride", "mergeAll", "isFunction", "zero", "acc", "update", "partition", "chunk", "partitionMap", "validateAll", "es", "bs", "isNonEmptyArray", "raceAll", "isNonEmpty", "dieSync", "IllegalArgumentException", "headNonEmpty", "tailNonEmpty", "deferred<PERSON><PERSON>", "fails", "head", "unsafeFromArray", "tail", "prepend", "_await", "raceAllArbiter", "onInterrupt", "zipLeft", "winner", "exitMatchEffect", "modify", "deferred<PERSON>ail<PERSON><PERSON><PERSON>", "deferred<PERSON>ucceed", "reduceEffect", "elem", "option", "parallelFinalizers", "contextWithEffect", "scopeTag", "onNone", "onSome", "strategy", "scopeFork", "inner", "scopeExtend", "parallelNFinalizers", "parallelism", "finalizersMask", "sequentialFinalizers", "scopeWith", "scopedWith", "scopeMake", "close", "scopedEffect", "scopeUse", "tagMetricsScoped", "labelMetricsScoped", "labels", "old", "union", "using", "use", "validate", "that", "validateWith", "zipWithOptions", "ea", "eb", "exitZipWith", "ca", "concurrent", "validateAllPar", "validateAllParDiscard", "validate<PERSON><PERSON><PERSON>", "flip", "withClockScoped", "c", "withRandomScoped", "withConfigProviderScoped", "provider", "withEarlyRelease", "fiberIdWith", "scopeClose", "exitInterrupt", "zipOptions", "zipLeftOptions", "zipRightOptions", "a2", "withRuntimeFlagsScoped", "empty", "revertRuntimeFlags", "GenericTag", "scopeUnsafeAddFinalizer", "fin", "finalizers", "ScopeImplProto", "ScopeTypeId", "CloseableScopeTypeId", "newScope", "scopeUnsafeMake", "isSequential", "exitAsVoid", "isParallel", "create", "mapInputContext", "merge", "fiberRefUnsafeMakeSupervisor", "fiberRefUnsafeMakePatch", "differ", "fiberRefLocallyScoped", "fiberRefGet", "oldValue", "fiberRefSet", "fiberRefGetWith", "fiberRefMake", "fiberRefMakeWith", "ref", "fiberRefUpdate", "fiberRefDelete", "fiberRefMakeContext", "fiberRefUnsafeMakeContext", "fiberRefMakeRuntimeFlags", "fiberRefUnsafeMakeRuntimeFlags", "fiberAwaitAll", "fiberAll", "_fiberAll", "CommitPrototype", "combine", "optionB", "optionA", "fiberInterruptFork", "fiberJoinAll", "fiberScoped", "raceWith", "other", "raceFibersWith", "onSelfWin", "loser", "onSelfDone", "onOtherWin", "onOtherDone", "disconnect", "race", "mapErrorCause", "cause2", "interruptAsFiber", "raceIndicator", "leftFiber", "selfScope", "rightFiber", "otherScope", "completeRace", "ab", "compareAndSet", "matchCauseEffect", "cause1", "processing", "counts", "checkDone", "every", "result", "current", "exitIsExit", "cleanup", "completed", "interruptWhenPossible", "makeSpanScoped", "addSpanStackTrace", "unsafeMakeSpan", "timingEnabled", "currentTracer<PERSON><PERSON>ing<PERSON>nabled", "clock_", "endSpan", "withTracerScoped", "withSpanScoped", "dataFirst", "provideService"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,EAAE,MAAM,aAAa;AACjC,OAAO,KAAKC,OAAO,MAAM,eAAe;AAExC,OAAO,KAAKC,KAAK,MAAM,aAAa;AAGpC,OAAO,KAAKC,OAAO,MAAM,eAAe;AAExC,OAAO,KAAKC,QAAQ,MAAM,gBAAgB;AAG1C,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAE9C,OAAO,KAAKC,iBAAiB,MAAM,yBAAyB;AAG5D,OAAO,KAAKC,OAAO,MAAM,eAAe;AAExC,OAAO,KAAKC,SAAS,MAAM,iBAAiB;AAC5C,OAAO,KAAKC,cAAc,MAAM,sBAAsB;AACtD,OAAO,KAAKC,WAAW,MAAM,mBAAmB;AAEhD,SAASC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,gBAAgB;AACrD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,OAAO,KAAKC,OAAO,MAAM,eAAe;AACxC,OAAO,KAAKC,OAAO,MAAM,eAAe;AACxC,OAAO,KAAKC,WAAW,MAAM,mBAAmB;AAEhD,OAAO,KAAKC,QAAQ,MAAM,gBAAgB;AAE1C,OAAO,KAAKC,KAAK,MAAM,aAAa;AACpC,OAAO,KAAKC,IAAI,MAAM,kBAAkB;AACxC,OAAO,KAAKC,MAAM,MAAM,cAAc;AACtC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAO,KAAKC,SAAS,MAAM,iBAAiB;AAE5C,OAAO,KAAKC,GAAG,MAAM,WAAW;AAIhC,OAAO,KAAKC,iBAAiB,MAAM,yBAAyB;AAC5D,SAASC,gBAAgB,QAAwB,iBAAiB;AAKlE,SAASC,YAAY,EAAEC,YAAY,QAAQ,aAAa;AACxD,OAAO,KAAKC,aAAa,MAAM,sBAAsB;AACrD,OAAO,KAAKC,aAAa,MAAM,YAAY;AAC3C,OAAO,KAAKC,KAAK,MAAM,YAAY;AACnC,SAASC,iBAAiB,QAAQ,0BAA0B;AAC5D,OAAO,KAAKC,WAAW,MAAM,kBAAkB;AAC/C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,OAAO,KAAKC,cAAc,MAAM,kBAAkB;AAClD,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,eAAe,MAAM,sBAAsB;AACvD,SAASC,UAAU,QAAQ,8BAA8B;AACzD,OAAO,KAAKC,iBAAiB,MAAM,wBAAwB;AAC3D,OAAO,KAAKC,aAAa,MAAM,YAAY;AAC3C,OAAO,KAAKC,YAAY,MAAM,mBAAmB;AACjD,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAC3C,OAAO,KAAKC,UAAU,MAAM,iBAAiB;AAC7C,OAAO,KAAKC,cAAc,MAAM,aAAa;AAC7C,OAAO,KAAKC,MAAM,MAAM,aAAa;AACrC,OAAO,KAAKC,gBAAgB,MAAM,wBAAwB;AAC1D,OAAO,KAAKC,WAAW,MAAM,mBAAmB;AAChD,OAAO,KAAKC,OAAO,MAAM,qBAAqB;AAC9C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,QAAQ,QAAQ,cAAc;AACvC,OAAO,KAAKC,aAAa,MAAM,mBAAmB;AAElD,OAAO,KAAKE,UAAU,MAAM,iBAAiB;AAC7C,OAAO,KAAKC,eAAe,MAAM,uBAAuB;AACxD,OAAO,KAAKC,MAAM,MAAM,aAAa;AACrC,OAAO,KAAKC,OAAO,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGhC,MAAMC,YAAY,GAAA,WAAA,mKAAGZ,MAAM,CAACa,GAAAA,AAAO,EAAC,sBAAsB,EAAE;IAAEC,WAAW,EAAE;AAAI,CAAE,CAAC;AAElF,MAAMC,WAAW,GAAA,WAAA,mKAAGf,MAAM,CAACa,GAAAA,AAAO,EAAC,qBAAqB,CAAC;AAEzD,MAAMG,cAAc,GAAA,WAAA,mKAAGhB,MAAM,CAACa,GAAAA,AAAO,EAAC,wBAAwB,EAAE;IAAEC,WAAW,EAAE;AAAI,CAAE,CAAC;AAEtF,MAAMG,aAAa,GAAA,WAAA,mKAAGjB,MAAM,CAACa,GAAAA,AAAO,EAAC,uBAAuB,EAAE;IAAEC,WAAW,EAAE;AAAI,CAAE,CAAC;AAEpF,MAAMI,cAAc,GAAA,WAAA,IAAGlB,MAAM,CAACmB,iKAAAA,AAAM,EAAA,WAAA,mKACzCnB,MAAM,CAACoB,KAAAA,AAAS,EACd,wBAAwB,EAAA,WAAA,iLACxBnB,cAAiBoB,AAAW,EAAZ,AAAa,CAAZA;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE;CACR,CAAC,CACH,EACD,WAAW,EACX,cAAc,CACf;AAQD,cAAA,GACA,MAAMC,wBAAwB,GAAG,UAAmB;AAKpD,cAAA,GACA,MAAMC,oBAAoB,GAAG,MAAe;AAK5C,cAAA,GACA,MAAMC,wBAAwB,GAAG,OAAgB;AAKjD,MAAMC,oBAAoB,GAAG;IAC3B,kBAAA,GACAC,EAAE,GAAGC,CAAQ,GAAKA,CAAC;IACnB,kBAAA,GACAC,EAAE,GAAGD,CAAQ,GAAKA;CACnB;AAED,MAAME,MAAM,IAAIF,CAAQ,IAAW;IACjC,MAAM,IAAIG,KAAK,CACb,CAAA,oBAAA,MACE7D,WAAW,CAAC8D,2JAAAA,AAAe,EAACJ,CAAC,CAC/B,CAAA,uEAAA,CAAyE,CAC1E;AACH,CAAC;AAED,MAAMK,SAAS,GAAA,WAAA,GAAGC,MAAM,CAACC,GAAG,CAAC,wCAAwC,CAAC;AAEtE,MAAMC,gBAAgB,GAAA,WAAA,4JAElBrE,cAAAA,AAAW,EAAC,+CAA+C,EAAE,IAAA,CAAO;QACtEsE,SAAS,EAAE;KACZ,CAAC,CAAC;AAEH,MAAMC,aAAa,GAAG;IACpB,wKAACrC,OAAO,CAACsC,QAAa,CAAA,EAAG,CACvBX,CAAyB,EACzBY,IAAoB,EACpBC,KAAc,KACZ;QACF,yJAAO7D,gBAAAA,AAAY,EAAC,IAAM4D,IAAI,CAACE,qBAAqB,CAACD,KAAK,CAAC,CAAC;IAC9D,CAAC;IACD,CAAC,QAAQ,CAAA,EAAGE,CACVf,CAAyB,EACzBgB,KAAkB,EAClBH,KAAc,KACZ;QACF,OAAOpD,IAAI,CAACwD,uKAAAA,AAAW,gKAACxD,IAAI,CAACwD,SAAAA,AAAW,EAACJ,KAAK,CAAC,CAAC;IAClD,CAAC;IACD,wKAACxC,OAAO,CAAC6C,oBAAyB,CAAA,EAAG,CACnClB,CAAyB,EACzBY,IAA8B,EAC9BC,KAAc,KACZ;QACF,0JAAO7D,eAAAA,AAAY,EAAC,IAAM4D,IAAI,CAACO,qBAAqB,CAACN,KAAK,CAAC,CAAC;IAC9D,CAAC;IACD,wKAACxC,OAAO,CAAC+C,UAAe,CAAA,EAAG,CACzBC,IAA4B,EAC5BT,IAAsB,EACtBC,KAAc,KACZ;QACFQ,IAAI,CAACC,iBAAiB,CAACD,IAAI,CAACE,mBAAmB,EAAEX,IAAI,CAACY,KAAK,CAAC;QAC5D,0KAAIhD,aAAa,CAACiD,EAAAA,AAAa,EAACJ,IAAI,CAACE,mBAAmB,CAAC,IAAIF,IAAI,CAACK,aAAa,EAAE,EAAE;YACjF,qKAAOjE,IAAI,CAACkE,WAAAA,AAAa,EAACN,IAAI,CAACO,mBAAmB,EAAE,CAAC;QACvD,CAAC,MAAM;YACL,qKAAOnE,IAAI,CAACwD,SAAAA,AAAW,EAACJ,KAAK,CAAC;QAChC;IACF,CAAC;IACD,wKAACxC,OAAO,CAACwD,GAAQ,CAAA,EAAG,CAClBR,IAA4B,EAC5BT,IAAgB,EAChBC,KAAc,KACZ;2JACF7D,eAAAA,AAAY,EAAC,IAAM4D,IAAI,CAACO,qBAAqB,CAACN,KAAK,CAAC,CAAC;QACrD,sJAAI7D,gBAAAA,AAAY,EAAC,IAAM4D,IAAI,CAACkB,qBAAqB,EAAE,CAAC,EAAE;YACpDT,IAAI,CAACU,SAAS,CAACnB,IAAI,CAAC;YACpB,OAAO5D,kKAAAA,AAAY,EAAC,IAAM4D,IAAI,CAACE,qBAAqB,EAAE,CAAC;QACzD,CAAC,MAAM;YACL,iKAAOrD,IAAI,CAACuE,EAAI;QAClB;IACF,CAAC;IACD,wKAAC3D,OAAO,CAAC4D,MAAW,CAAA,EAAG,CACrBZ,IAA4B,EAC5BT,IAAuB,EACvBC,KAAc,KACZ;QACF,MAAMqB,KAAK,sJAAGlF,eAAAA,AAAY,EAAC,IAAM4D,IAAI,CAACkB,qBAAqB,CAACK,IAAI,CAACtB,KAAK,CAAC,CAAC;QACxE,IAAIqB,KAAK,CAACE,IAAI,EAAE,qKAAO3E,IAAI,CAACwD,SAAAA,AAAW,EAACiB,KAAK,CAACrB,KAAK,CAAC;QACpDQ,IAAI,CAACU,SAAS,CAACnB,IAAI,CAAC;QACpB,0JAAO3D,eAAY,AAAZA,EAAaiF,KAAK,CAACrB,KAAK,CAAC;IAClC;CACD;AAED,MAAMwB,2BAA2B,GAAG;IAClC,mKAACvE,YAAY,CAACwE,SAAmB,CAAA,EAAG,CAClCjB,IAA4B,EAC5BkB,YAAuC,EACvCC,GAAiC,EACjCC,OAA+E,KAC7E;QACFpB,IAAI,CAACqB,yBAAyB,CAACD,OAAO,CAACE,KAAK,CAAC;QAC7C,6KAAOnE,aAAa,CAACiD,EAAAA,AAAa,EAACc,YAAY,CAAC,iKAAG9E,IAAI,CAACkE,WAAAA,AAAa,EAACc,OAAO,CAACE,KAAK,CAAC,GAAGH,GAAG;IAC5F,CAAC;IACD,mKAAC1E,YAAsB,AAAV,CAAC8E,AAAS,EAAG,CACxBC,KAA6B,EAC7BC,aAAwC,EACxCC,IAAkC,EAClCC,QAAmC,KACjC;QACF,MAAM,IAAI7C,KAAK,CAAC,uEAAuE,CAAC;IAC1F,CAAC;IACD,mKAACrC,YAAY,CAACmF,CAAW,CAAA,EAAG,CAC1B5B,IAA4B,EAC5BkB,YAAuC,EACvCC,GAAiC,EACjCC,OAAuE,KACrE;QACFA,OAAO,CAACS,OAAO,CAAC7B,IAAI,2JAAEtF,UAAYoH,AAAO,CAAR,CAACA,AAAQZ,YAAY,CAAC,CAAC;QACxD,OAAOC,GAAG;IACZ,CAAC;IACD,mKAAC1E,YAAY,CAACsF,EAAY,CAAA,EAAG,CAC3BP,KAA6B,EAC7BC,aAAwC,EACxCN,GAAiC,EACjCQ,QAAyE,KACvE;QACF,oKAAOvF,IAAI,CAAC4F,MAAAA,AAAO,gKAAC5F,IAAI,CAAC6F,MAAAA,AAAQ,EAAE,GAAE,IAAMd,GAAG,CAAC;IACjD;CACD;AAED;;IAGA,MAAMe,kBAAkB,GAAIlC,IAA+B,kKACzD5D,IAAI,CAAC+F,sBAAAA,AAAwB,2KAC3BtG,UAAcuG,AAAO,EAACpC,CAAT,CAACoC,EAAY,CAAC,GAC1BC,yBAAyB,GACxBC,wBAAwB,0KACtBzG,aAAa,CAAC0G,gBAAAA,AAA2B,EAACF,yBAAyB,CAAC,EACpE,CAAC,CAACG,UAAU,EAAEC,UAAU,CAAC,KAAI;YAC3B,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAiC;YACpD,MAAMC,GAAG,GAA6B,EAAE;YACxC,KAAK,MAAMC,KAAK,IAAIJ,UAAU,CAAE;gBAC9BG,GAAG,CAACE,IAAI,oJAAC5I,KAAK,CAAC6I,YAAAA,AAAe,EAACF,KAAK,CAAQ,CAAC;gBAC7C,KAAK,MAAMG,KAAK,IAAIH,KAAK,CAAE;oBACzBH,GAAG,CAACO,GAAG,CAACD,KAAK,CAACE,OAA4B,EAAEF,KAAK,CAAC;gBACpD;YACF;YACA,MAAMG,IAAI,GAAGP,GAAG,CAACO,IAAI,EAAE;YACvB,qKAAO/G,IAAI,CAACgH,aAAAA,AAAe,EACzBC,mBAAmB,CAACb,UAAU,CAACc,MAAM,CAACV,GAAG,CAAC,EAAEO,IAAI,EAAE,IAChDA,IAAI,CAACI,OAAO,EAAEP,KAAK,IAAI;oBACrBA,KAAK,CAACQ,SAAS,CAACC,WAAW,GAAG,IAAI;gBACpC,CAAC,CAAC,CAAC,2KACLzH,oBAAiB,EACjB0G,GAAG,CACJ;QACH,CAAC,EACD,KAAK,EACL,KAAK,CACN,CACJ;AAQH,MAAMgB,QAAQ,GAAA,WAAA,oKAAGlG,OAAO,CAACmG,YAAAA,AAAiB,EAAE;AAGtC,MAAOC,YAAyC,6JAAQvJ,QAAsB,EAAZ,CAACwJ;IAG9D,4JAACrH,aAAa,CAACsH,AAAW,CAAA,8JAAItH,aAAa,CAACuH,EAAa,CAAA;IACzD,4JAACvH,aAAa,CAACwH,OAAkB,CAAA,GAAIvF,oBAAoB,CAAA;IAC1DwF,UAAU,CAAA;IACVC,QAAQ,CAAA;IACRC,MAAM,GAAA,WAAA,GAAG,IAAIC,KAAK,EAA6B,CAAA;IAC/CC,SAAS,GAAuC,IAAI,CAAA;IACpDC,UAAU,GAAA,WAAA,GAAG,IAAIF,KAAK,EAAmC,CAAA;IACzDG,QAAQ,GAAG,KAAK,CAAA;IAChBC,MAAM,GAA6B,EAAE,CAAA;IACrCC,iBAAiB,GAA2D,IAAI,CAAA;IAChFC,gBAAgB,GAA2B,IAAI,CAAA;IAC/CC,UAAU,GAA2B,IAAI,CAAA;IACzCC,MAAM,GAAoB,EAAE,CAAA;IAC5BC,WAAW,GAAG,KAAK,CAAA;IAEpB3E,mBAAmB,CAAA;IACnB4E,cAAc,GAAW,CAAC,CAAA;IAC1BC,iBAAiB,CAAA;IACjBrJ,gBAAgB,CAAA;IAChBsJ,aAAa,CAAA;IACbC,WAAW,CAAA;IACXC,cAAc,CAAA;IACdC,sBAAsB,CAAA;IAE7BC,YACEC,OAAwB,EACxBC,UAA+B,EAC/BC,aAAwC,CAAA;QAExC,KAAK,EAAE;QACP,IAAI,CAACrF,mBAAmB,GAAGqF,aAAa;QACxC,IAAI,CAACrB,QAAQ,GAAGmB,OAAO;QACvB,IAAI,CAACpB,UAAU,GAAGqB,UAAU;QAC5B,IAAInI,aAAa,CAACqI,yKAAAA,AAAc,EAACD,aAAa,CAAC,EAAE;YAC/C,MAAME,IAAI,GAAG,IAAI,CAACC,WAAW,2JAACtJ,IAAI,CAACuJ,iBAAmB,CAAC;YACvDlI,YAAY,CAACmI,YAAY,CAAC,CAAC,EAAEH,IAAI,CAAC;YAClC7H,WAAW,CAACgI,YAAY,CAAC,CAAC,EAAEH,IAAI,CAAC;QACnC;QACA,IAAI,CAACI,eAAe,EAAE;IACxB;IAEAC,MAAMA,CAAA,EAAA;QACJ,sKAAOtJ,OAAkB,AAAJuJ,EAAK,IAAI,AAAV,CAAW,AAAVA;IACvB;IAEA;;MAGAC,EAAEA,CAAA,EAAA;QACA,OAAO,IAAI,CAAC9B,QAAQ;IACtB;IAEA;;;;MAKA+B,MAAMA,CAAOC,MAAgC,EAAA;QAC3C,IAAI,CAACC,IAAI,uKAAC1J,SAAawJ,AAAM,EAACC,CAAR,CAACD,IAAa,CAAC,CAAC;IACxC;IAEA;;MAGA,IAAIG,MAAMA,CAAA,EAAA;QACR,OAAO,IAAI,CAACC,GAAG,CAAC,CAAC1H,CAAC,EAAEyH,MAAM,GAAKA,MAAM,CAAC;IACxC;IAEA;;MAGA,IAAIlF,YAAYA,CAAA,EAAA;QACd,OAAO,IAAI,CAACmF,GAAG,CAAC,CAACxF,KAAK,EAAEuF,MAAM,KAAI;YAChC,6JAAI1L,SAAY4L,AAAM,EAAP,AAAQF,CAAPE,KAAa,CAAC,EAAE;gBAC9B,OAAOzF,KAAK,CAACX,mBAAmB;YAClC;YACA,OAAOkG,MAAM,CAAClF,YAAY;QAC5B,CAAC,CAAC;IACJ;IAEA;;MAGAqF,KAAKA,CAAA,EAAA;QACH,WAAO5J,UAAU,CAAC6J,kKAAAA,AAAU,EAAC,IAAI,CAAC;IACpC;IAEA;;MAGA,IAAIC,QAAQA,CAAA,EAAA;QACV,OAAO,IAAI,CAACJ,GAAG,CAAEK,KAAK,IAAKtC,KAAK,CAACuC,IAAI,CAACD,KAAK,CAACE,WAAW,EAAE,CAAC,CAAC;IAC7D;IAEA;;MAGAA,WAAWA,CAAA,EAAA;QACT,IAAI,IAAI,CAACvC,SAAS,KAAK,IAAI,EAAE;YAC3B,IAAI,CAACA,SAAS,GAAG,IAAIwC,GAAG,EAAE;QAC5B;QACA,OAAO,IAAI,CAACxC,SAAS;IACvB;IAEA;;;;;;;MAQA9D,mBAAmBA,CAAA,EAAA;QACjB,OAAO,IAAI,CAACmF,WAAW,2JAACtJ,IAAI,CAAC0K,qBAAuB,CAAC;IACvD;IAEA;;MAGApK,SAASA,CAAA,EAAA;QACP,OAAO,IAAI,CAAC2J,GAAG,EAAEK,KAAK,GAAKA,KAAK,CAACK,YAAY,EAAE,CAAC;IAClD;IAEA;;;;;;MAOAV,GAAGA,CACDW,CAA0E,EAAA;QAE1E,qKAAO5K,IAAI,CAAC6K,KAAAA,AAAO,EAAC,MAAK;YACvB,MAAMC,QAAQ,iKAAG9K,IAAI,CAAC+K,gBAAAA,AAAkB,EAAI,IAAI,CAACjD,QAAQ,CAAC;YAC1D,IAAI,CAACiC,IAAI,uKACP1J,WAAqB,AAAR2K,CAAD,CAACA,AAAS,CAACV,KAAK,EAAEN,MAAM,KAAI;8KACtChK,IAAI,CAACiL,gBAAAA,AAAkB,EAACH,QAAQ,gKAAE9K,IAAI,CAACkL,EAAAA,AAAI,EAAC,IAAMN,CAAC,CAACN,KAAK,EAAEN,MAAM,CAAC,CAAC,CAAC;YACtE,CAAC,CAAC,CACH;YACD,WAAOhK,IAAI,CAACmL,qKAAa,AAAbA,EAAcL,QAAQ,CAAC;QACrC,CAAC,CAAC;IACJ;IAEA;;MAGAf,IAAIA,CAAC/E,OAAkC,EAAA;QACrC,IAAI,CAAC+C,MAAM,CAACrB,IAAI,CAAC1B,OAAO,CAAC;QACzB,IAAI,CAAC,IAAI,CAACmD,QAAQ,EAAE;YAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;YACpB,IAAI,CAACiD,yBAAyB,EAAE;QAClC;IACF;IAEA,IAAIC,KAAKA,CAAA,EAAA;QACP,qKAAOrL,IAAI,CAACsL,GAAAA,AAAK,GAAEzB,MAAM,IAAI;YAC3B,MAAM0B,EAAE,IAAIC,IAAqB,GAAK3B,MAAM,KAAC7J,IAAI,CAACyL,+JAAAA,AAAO,EAACD,IAAI,CAAC,CAAC;YAChE,IAAI,CAACzB,IAAI,CACP1J,YAAY,CAAC2K,oKAAAA,AAAQ,EAAC,CAACV,KAAK,EAAE/H,CAAC,KAAI;gBACjC,IAAI+H,KAAK,CAAC/B,UAAU,KAAK,IAAI,EAAE;oBAC7BgD,EAAE,CAAC,IAAI,CAAChD,UAAW,CAAC;gBACtB,CAAC,MAAM;oBACL+B,KAAK,CAACoB,WAAW,CAACH,EAAE,CAAC;gBACvB;YACF,CAAC,CAAC,CACH;YACD,qKAAOvL,IAAI,CAACkL,EAAAA,AAAI,EAAC,IACf,IAAI,CAACnB,IAAI,uKACP1J,WAAqB,AAAR2K,CAAD,CAAU,AAATA,CAAUV,KAAK,EAAE/H,CAAC,KAAI;oBACjC+H,KAAK,CAACqB,cAAc,CAACJ,EAAE,CAAC;gBAC1B,CAAC,CAAC,CACH,CACF;QACH,CAAC,EAAE,IAAI,CAAC3B,EAAE,EAAE,CAAC;IACf;IAEA,IAAIgC,UAAUA,CAAA,EAAA;QACZ,OAAO5L,IAAI,CAAC6L,4KAAAA,AAAgB,EAAC,CAACC,WAAW,EAAEC,YAAY,KAAI;YACzD,MAAMC,aAAa,GAAGF,WAAW,CAAClC,EAAE,EAAE;YACtC,MAAMqC,eAAe,GAAGH,WAAW,CAACnB,YAAY,EAAE;YAClD,MAAMuB,kBAAkB,GAAGH,YAAY,CAACjH,YAAY;YACpD,MAAMqH,cAAc,GAAG,IAAI,CAACxB,YAAY,EAAE;YAC1C,MAAMyB,gBAAgB,sKAAG9L,SAAS,AAAC+L,AAAM,CAANA,CAAOJ,eAAe,EAAED,aAAa,EAAEG,cAAc,CAAC;YAEzFL,WAAW,CAACQ,YAAY,CAACF,gBAAgB,CAAC;YAE1C,MAAMG,mBAAmB,GAAGT,WAAW,CAACxC,WAAW,CAACxF,mBAAmB,CAAC;YAExE,MAAMC,KAAK,yJAAGtF,OAAAA,AAAI,wKAChBsC,OAAcyL,AAAI,EAACN,IAAN,CAACM,aAAuB,EAAED,mBAAmB,CAAC,EAC3D,2CAAA;YACAlN,iBAAiB,CAACoN,uJAAAA,AAAO,oKAAC1L,aAAa,CAAC2L,CAAY,CAAC,iKACrDrN,UAAkBoN,AAAO,OAAR,CAACA,4JAAQ1L,WAAsB,CAAC,CAClD,AADwC,CAAC4L;YAG1C,qKAAO3M,IAAI,CAAC4M,gBAAAA,AAAkB,EAAC7I,KAAK,CAAC;QACvC,CAAC,CAAC;IACJ;IAEA;;;MAIA,IAAI8I,IAAIA,CAAA,EAAA;QACN,qKAAO7M,IAAI,CAACkL,EAAAA,AAAI,EAAC,wJAAMjM,MAAM,CAAC6N,QAAAA,AAAY,EAAC,IAAI,CAACvE,UAAU,CAAC,CAAC;IAC9D;IAEA;;;MAIAwE,UAAUA,CAAA,EAAA;QACR,OAAO,IAAI,CAACxE,UAAU;IACxB;IAEA;;MAGAyE,eAAeA,CAAC/D,OAAwB,EAAA;QACtC,qKAAOjJ,IAAI,CAACkL,EAAAA,AAAI,EAAC,IAAM,IAAI,CAACnB,IAAI,EAAC1J,YAAY,CAAC4M,0KAAAA,AAAe,iKAACvN,YAAuB,AAATwN,CAAD,CAACA,AAAUjE,OAAO,CAAC,CAAC,CAAC,CAAC;IACnG;IAEA;;MAGAkE,qBAAqBA,CAAClE,OAAwB,EAAA;QAC5C,IAAI,CAACc,IAAI,CAAC1J,YAAY,CAAC4M,2KAAAA,AAAe,iKAACvN,YAAuB,AAATwN,CAAD,CAACA,AAAUjE,OAAO,CAAC,CAAC,CAAC;IAC3E;IAEA;;;;MAKAyC,WAAWA,CAAC0B,QAAyC,EAAA;QACnD,IAAI,IAAI,CAAC7E,UAAU,KAAK,IAAI,EAAE;YAC5B6E,QAAQ,CAAC,IAAI,CAAC7E,UAAW,CAAC;QAC5B,CAAC,MAAM;YACL,IAAI,CAACL,UAAU,CAACxB,IAAI,CAAC0G,QAAQ,CAAC;QAChC;IACF;IAEA;;;;;MAMAzB,cAAcA,CAACyB,QAAyC,EAAA;QACtD,IAAI,CAAClF,UAAU,GAAG,IAAI,CAACA,UAAU,CAACmF,MAAM,EAAEC,CAAC,GAAKA,CAAC,KAAKF,QAAQ,CAAC;IACjE;IACA;;;;;;MAOAzC,YAAYA,CAAA,EAAA;QACV,IAAI,CAAC4C,WAAW,CAACzJ,mBAAmB,EAAE,IAAI,CAACA,mBAAmB,CAAC;QAC/D,OAAO,IAAI,CAAC+D,UAAU;IACxB;IAEA;;;;MAKA2F,oBAAoBA,CAAIC,QAA8B,EAAA;QACpD,IAAI,CAAC5F,UAAU,IAAGvH,SAAS,CAACoN,kKAAAA,AAAO,EAAC,IAAI,CAAC7F,UAAU,EAAE4F,QAAQ,CAAC;IAChE;IAEA;;;;;;MAOAnE,WAAWA,CAAImE,QAA8B,EAAA;QAC3C,IAAI,IAAI,CAAC5F,UAAU,CAAC8F,MAAM,CAACC,GAAG,CAACH,QAAQ,CAAC,EAAE;YACxC,OAAO,IAAI,CAAC5F,UAAU,CAAC8F,MAAM,CAACE,GAAG,CAACJ,QAAQ,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAM;QACzD;QACA,OAAOA,QAAQ,CAACK,OAAO;IACzB;IAEA;;;;MAKAP,WAAWA,CAAIE,QAA8B,EAAErK,KAAQ,EAAA;QACrD,IAAI,CAACyE,UAAU,GAAGvH,SAAS,CAACyN,oKAAAA,AAAQ,EAAC,IAAI,CAAClG,UAAU,EAAE;YACpDoB,OAAO,EAAE,IAAI,CAACnB,QAAQ;YACtB2F,QAAQ;YACRrK;SACD,CAAC;QACF,IAAI,CAACqG,eAAe,EAAE;IACxB;IAEAA,eAAeA,CAAA,EAAA;QACb,IAAI,CAACV,sBAAsB,GAAG,IAAI,CAACO,WAAW,sKAACrJ,eAAe,CAAC+N,EAAe,CAAC;QAC/E,IAAI,CAACpF,aAAa,GAAG,IAAI,CAACG,sBAAsB,CAACkF,SAAS,CAACJ,GAAG,6JAAC1M,MAAM,CAAC+M,KAAS,CAACC,GAAG,CAAC;QACpF,IAAI,CAACxF,iBAAiB,GAAG,IAAI,CAACW,WAAW,CAACX,iBAAiB,CAAC;QAC5D,IAAI,CAACrJ,gBAAgB,GAAG,IAAI,CAACgK,WAAW,oJAAChK,mBAAgB,CAAC;QAC1D,IAAI,CAACwJ,cAAc,GAAG,IAAI,CAACQ,WAAW,2JAACtJ,IAAI,CAAC8I,YAAc,CAAC;QAC3D,IAAI,CAACD,WAAW,GAAG,IAAI,CAACC,cAAc,CAACmF,SAAS,CAACJ,GAAG,6JAAC1M,MAAM,CAACiN,GAAO,CAACD,GAAG,CAAC;IAC1E;IAEA;;;;MAKA7B,YAAYA,CAAChM,SAA8B,EAAA;QACzC,IAAI,CAACuH,UAAU,GAAGvH,SAAS;QAC3B,IAAI,CAACmJ,eAAe,EAAE;IACxB;IAEA;;;;MAKA4E,QAAQA,CAACC,KAA6B,EAAA;QACpC,IAAI,CAAC9D,WAAW,EAAE,CAAC+D,GAAG,CAACD,KAAK,CAAC;IAC/B;IAEA;;;;MAKAE,WAAWA,CAACF,KAA6B,EAAA;QACvC,IAAI,CAAC9D,WAAW,EAAE,CAACiE,MAAM,CAACH,KAAK,CAAC;IAClC;IAEA;;;;;;MAOAI,gBAAgBA,CAACvE,KAA4B,EAAA;QAC3C,MAAME,QAAQ,GAAG,IAAI,CAACpC,SAAS;QAC/B,0CAAA;QACA,IAAI,CAACA,SAAS,GAAG,IAAI;QACrB,IAAIoC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,CAACsE,IAAI,GAAG,CAAC,EAAE;YAC1C,KAAK,MAAML,KAAK,IAAIjE,QAAQ,CAAE;gBAC5B,qDAAA;gBACA,IAAIiE,KAAK,CAAC/F,UAAU,KAAK,IAAI,EAAE;oBAC7B4B,KAAK,CAACoE,GAAG,CAAC,IAAI,CAACzK,mBAAmB,EAAEwK,KAAK,CAAC;gBAC5C;YACF;QACF;IACF;IAEA;;;;;;MAOAM,yBAAyBA,CAAA,EAAA;QACvB,IAAIC,OAAO,GAAG,IAAI;QAClB,MAAOA,OAAO,CAAE;YACd,IAAIC,gBAAgB,GAAqB5M,wBAAwB;YACjE,MAAM6M,IAAI,GAAIC,UAAkB,2JAAC5O,aAAa,CAAC6O,KAAe,CAAC;YAC7DD,UAAkB,4JAAC5O,aAAa,CAAC6O,IAAe,CAAC,GAAG,IAAI;YAC1D,IAAI;gBACF,MAAOH,gBAAgB,KAAK5M,wBAAwB,CAAE;oBACpD4M,gBAAgB,GAAG,IAAI,CAAC/G,MAAM,CAACmH,MAAM,KAAK,CAAC,GACzC/M,oBAAoB,GACpB,IAAI,CAACgN,6BAA6B,CAAC,IAAI,CAACpH,MAAM,CAACqH,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC;gBACpE;YACF,CAAC,QAAS;gBACR,IAAI,CAACjH,QAAQ,GAAG,KAAK;gBACnB6G,UAAkB,CAAC5O,aAAa,CAAC6O,+JAAe,CAAC,GAAGF,IAAI;YAC5D;YACA,yEAAA;YACA,wEAAA;YACA,2CAAA;YACA,IAAI,IAAI,CAAChH,MAAM,CAACmH,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC/G,QAAQ,EAAE;gBAC5C,IAAI,CAACA,QAAQ,GAAG,IAAI;gBACpB,IAAI2G,gBAAgB,KAAK1M,wBAAwB,EAAE;oBACjD,IAAI,CAACgJ,yBAAyB,EAAE;oBAChCyD,OAAO,GAAG,KAAK;gBACjB,CAAC,MAAM;oBACLA,OAAO,GAAG,IAAI;gBAChB;YACF,CAAC,MAAM;gBACLA,OAAO,GAAG,KAAK;YACjB;QACF;IACF;IAEA;;;;;;;;MASAzD,yBAAyBA,CAAA,EAAA;QACvB,IAAI,CAAC9L,gBAAgB,CAAC+P,YAAY,CAChC,IAAI,CAACC,GAAG,EACR,IAAI,CAAChG,WAAW,CAACtJ,IAAI,CAACuP,iLAAyB,CAAC,CACjD;IACH;IAEA;;;;;;MAOAC,sBAAsBA,CACpB1K,YAAuC,EACvC2K,IAAkC,EAAA;QAElC,IAAI1K,GAAG,GAAG0K,IAAI;QACd,MAAO,IAAI,CAAC1H,MAAM,CAACmH,MAAM,GAAG,CAAC,CAAE;YAC7B,MAAMlK,OAAO,GAAG,IAAI,CAAC+C,MAAM,CAACqH,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,mBAAA;YACArK,GAAG,GAAGH,2BAA2B,CAACI,OAAO,CAAC0K,IAAI,CAAC,CAAC,IAAI,EAAE5K,YAAY,EAAEC,GAAG,EAAEC,OAAO,CAAC;QACnF;QACA,OAAOD,GAAG;IACZ;IAEA;;;;;;MAOAd,aAAaA,CAAA,EAAA;QACX,OAAO,gKAACvE,UAAciQ,AAAO,EAAC,CAAT,CAACA,EAAY,CAACrG,WAAW,CAACtJ,IAAI,CAAC0K,+KAAuB,CAAC,CAAC;IAC/E;IAEA;;;;;MAMAkF,mBAAmBA,CAAC1K,KAAyB,EAAA;QAC3C,MAAM2K,KAAK,GAAG,IAAI,CAACvG,WAAW,2JAACtJ,IAAI,CAAC0K,qBAAuB,CAAC;QAC5D,IAAI,CAAC6C,WAAW,2JAACvN,IAAI,CAAC0K,qBAAuB,EAAEhL,aAAa,CAAC2G,8JAAAA,AAAU,EAACwJ,KAAK,EAAE3K,KAAK,CAAC,CAAC;IACxF;IAEA;;;;MAKAD,yBAAyBA,CAACC,KAAyB,EAAA;QACjD,IAAI,CAAC0K,mBAAmB,CAAC1K,KAAK,CAAC;QAC/B,IAAI,CAAC4K,gCAAgC,EAAE;IACzC;IAEA;;;;;;MAOAA,gCAAgCA,CAAA,EAAA;QAC9B,IAAI,IAAI,CAAC7H,SAAS,KAAK,IAAI,IAAI,IAAI,CAACA,SAAS,CAAC0G,IAAI,KAAK,CAAC,EAAE;YACxD,OAAO,KAAK;QACd;QACA,IAAIoB,IAAI,GAAG,KAAK;QAChB,KAAK,MAAMzB,KAAK,IAAI,IAAI,CAACrG,SAAS,CAAE;YAClCqG,KAAK,CAACvE,IAAI,CAAC1J,YAAY,CAAC4M,2KAAAA,AAAe,iKAACvN,YAAcwN,AAAS,CAAV,CAACA,AAAU,IAAI,CAACtD,EAAE,EAAE,CAAC,CAAC,CAAC;YAC5EmG,IAAI,GAAG,IAAI;QACb;QACA,OAAOA,IAAI;IACb;IAEA;;;;;;MAOAC,oBAAoBA,CAAA,EAAA;QAClB,IAAI,IAAI,CAACF,gCAAgC,EAAE,EAAE;YAC3C,MAAMG,EAAE,GAAG,IAAI,CAAChI,SAAU,CAACiI,MAAM,EAAE;YACnC,IAAI,CAACjI,SAAS,GAAG,IAAI;YACrB,IAAIiC,MAAM,GAAG,KAAK;YAClB,MAAMiG,IAAI,GAAGA,CAAA,KAAK;gBAChB,MAAMzL,IAAI,GAAGuL,EAAE,CAACvL,IAAI,EAAE;gBACtB,IAAI,CAACA,IAAI,CAACC,IAAI,EAAE;oBACd,qKAAO3E,IAAI,CAACoQ,IAAAA,AAAM,EAAC1L,IAAI,CAACtB,KAAK,CAACiI,KAAK,CAAC;gBACtC,CAAC,MAAM;oBACL,OAAOrL,IAAI,CAACkL,gKAAAA,AAAI,EAAC,MAAK;wBACpBhB,MAAM,GAAG,IAAI;oBACf,CAAC,CAAC;gBACJ;YACF,CAAC;YACD,QAAOlK,IAAI,CAACqQ,oKAAAA,AAAS,EAAC;gBACpBC,KAAK,EAAEA,CAAA,GAAM,CAACpG,MAAM;gBACpBiG,IAAI;gBACJI,IAAI,EAAEA,CAAA,KAAK;gBACT,EAAA;gBAAA;aAEH,CAAC;QACJ;QACA,OAAO,IAAI;IACb;IAEAC,eAAeA,CAAChF,IAAqB,EAAA;QACnC,yKAAIzK,aAAa,CAACqI,IAAAA,AAAc,EAAC,IAAI,CAACtF,mBAAmB,CAAC,EAAE;YAC1D,MAAMuF,IAAI,GAAG,IAAI,CAACC,WAAW,2JAACtJ,IAAI,CAACuJ,iBAAmB,CAAC;YACvD,MAAMkH,eAAe,GAAG,IAAI,CAAC7G,EAAE,EAAE,CAAC6G,eAAe;YACjD,MAAMC,aAAa,GAAGC,IAAI,CAACC,GAAG,EAAE;YAChCjP,cAAc,CAAC6H,YAAY,CAACkH,aAAa,GAAGD,eAAe,EAAEpH,IAAI,CAAC;YAClE7H,WAAW,CAACgI,YAAY,CAAC,CAAC,CAAC,EAAEH,IAAI,CAAC;YAClC,OAAQmC,IAAI,CAACkE,IAAI;gBACf,4KAAK9O,OAAO,CAACiQ,KAAU;oBAAE;wBACvBpP,cAAc,CAAC+H,YAAY,CAAC,CAAC,EAAEH,IAAI,CAAC;wBACpC;oBACF;gBACA,4KAAKzI,OAAO,CAACkQ,KAAU;oBAAE;wBACvBpP,aAAa,CAAC8H,YAAY,CAAC,CAAC,EAAEH,IAAI,CAAC;wBACnC;oBACF;YACF;QACF;QACA,IAAImC,IAAI,CAACkE,IAAI,KAAK,SAAS,EAAE;YAC3B,MAAMqB,KAAK,GAAG,IAAI,CAACzH,WAAW,0JAACtJ,IAAI,CAACgR,4BAA6B,CAAC;YAClE,IAAI,gKAACtR,aAAa,CAACuR,MAAAA,AAAiB,EAACzF,IAAI,CAACtG,KAAK,CAAC,IAAI6L,KAAK,CAACrB,IAAI,KAAK,MAAM,EAAE;gBACzE,IAAI,CAACwB,GAAG,CAAC,0CAA0C,EAAE1F,IAAI,CAACtG,KAAK,EAAE6L,KAAK,CAAC;YACzE;QACF;IACF;IAEAI,YAAYA,CAAC3F,IAAqB,EAAA;QAChC,IAAI,CAACjD,UAAU,GAAGiD,IAAI;QACtB,IAAI,CAACgF,eAAe,CAAChF,IAAI,CAAC;QAC1B,IAAK,IAAI4F,CAAC,GAAG,IAAI,CAAClJ,UAAU,CAACgH,MAAM,GAAG,CAAC,EAAEkC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,CAAE;YACpD,IAAI,CAAClJ,UAAU,CAACkJ,CAAC,CAAC,CAAC5F,IAAI,CAAC;QAC1B;QACA,IAAI,CAACtD,UAAU,GAAG,EAAE;IACtB;IAEAmJ,UAAUA,CAAA,EAAA;QACR,OAAO,IAAI,CAAC/H,WAAW,CAACgI,cAAc,CAAC;IACzC;IAEAJ,GAAGA,CACDlM,OAAgB,EAChBE,KAAuB,EACvBqM,gBAAkD,EAAA;QAElD,MAAMC,QAAQ,uJAAGvS,MAAM,CAACwS,EAAAA,AAAM,EAACF,gBAAgB,CAAC,GAC9CA,gBAAgB,CAACnO,KAAK,GACtB,IAAI,CAACkG,WAAW,2JAACtJ,IAAI,CAAC0R,aAAe,CAAC;QACxC,MAAMC,eAAe,GAAG,IAAI,CAACrI,WAAW,CAACsI,sBAAsB,CAAC;QAChE,0JAAI9S,QAAQ,CAAC+S,KAAAA,AAAW,EAACF,eAAe,EAAEH,QAAQ,CAAC,EAAE;YACnD;QACF;QACA,MAAMM,KAAK,GAAG,IAAI,CAACxI,WAAW,2JAACtJ,IAAI,CAAC+R,YAAc,CAAC;QACnD,MAAMC,WAAW,GAAG,IAAI,CAAC1I,WAAW,2JAACtJ,IAAI,CAACiS,mBAAqB,CAAC;QAChE,MAAMC,OAAO,GAAG,IAAI,CAACb,UAAU,EAAE;QACjC,MAAMc,UAAU,GAAG,IAAI,CAACxH,YAAY,EAAE;QACtC,IAAI/L,OAAO,CAAC+P,oJAAAA,AAAI,EAACuD,OAAO,CAAC,GAAG,CAAC,EAAE;YAC7B,MAAME,YAAY,wJAAGrU,MAAQ8P,AAAG,CAAJ,CAACA,AAAI,IAAI,CAACvE,WAAW,sKAACrJ,eAAe,CAAC+N,EAAe,CAAC,EAAErO,KAAK,CAAC0S,gKAAQ,CAAC;YACnG,MAAMC,IAAI,GAAG,IAAI3B,IAAI,CAACyB,YAAY,CAACG,uBAAuB,EAAE,CAAC;qKAC7D1T,WAAW,CAAC2T,YAAqB,AAArBA,EAAsBL,UAAU,EAAE,MAAK;gBACjD,KAAK,MAAMM,MAAM,IAAIP,OAAO,CAAE;oBAC5BO,MAAM,CAACvB,GAAG,CAAC;wBACTjI,OAAO,EAAE,IAAI,CAACW,EAAE,EAAE;wBAClB4H,QAAQ;wBACRxM,OAAO;wBACPE,KAAK;wBACLwN,OAAO,EAAEP,UAAU;wBACnBL,KAAK;wBACLE,WAAW;wBACXM;qBACD,CAAC;gBACJ;YACF,CAAC,CAAC;QACJ;IACF;IAEA;;;;;;MAOAnD,6BAA6BA,CAACnK,OAAkC,EAAA;QAC9D,OAAQA,OAAO,CAAC0K,IAAI;YAClB,uKAAKrP,YAAY,CAACsF,EAAY;gBAAE;oBAC9B,OAAOvD,wBAAwB;gBACjC;YACA,uKAAK/B,YAAY,CAACwE,SAAmB;gBAAE;oBACrC,IAAI,CAACI,yBAAyB,CAACD,OAAO,CAACE,KAAK,CAAC;oBAC7C,IAAI,IAAI,CAACmD,iBAAiB,KAAK,IAAI,EAAE;wBACnC,IAAI,CAACA,iBAAiB,+JAACrI,IAAI,CAACkE,WAAAA,AAAa,EAACc,OAAO,CAACE,KAAK,CAAC,CAAC;wBACzD,IAAI,CAACmD,iBAAiB,GAAG,IAAI;oBAC/B;oBACA,OAAOnG,wBAAwB;gBACjC;YACA,uKAAK7B,YAAY,AAAU,CAAT8E;gBAAW;oBAC3B,IAAI,CAACkD,iBAAiB,GAAG,IAAI;oBAC7B,IAAI,CAACC,gBAAgB,GAAG,IAAI;oBAC5B,IAAI,CAACqK,cAAc,CAAC3N,OAAO,CAAC8E,MAAM,CAAC;oBACnC,OAAO5H,wBAAwB;gBACjC;YACA,uKAAK7B,YAAY,CAACmF,CAAW;gBAAE;oBAC7BR,OAAO,CAACS,OAAO,CACb,IAAI,EACJ,IAAI,CAAC8C,UAAU,KAAK,IAAI,uJACtBjK,QAAgB,GAAL,CAACqG,wJACZrG,WAAW,CAACsU,AAAS,AAATA,EAAU,IAAI,CAAC9O,mBAAmB,EAAE,IAAI,CAACwE,gBAAiB,CAAC,CAC1E;oBACD,OAAOpG,wBAAwB;gBACjC;YACA;gBAAS;oBACP,OAAOO,MAAM,CAACuC,OAAO,CAAC;gBACxB;QACF;IACF;IAEA;;;;MAKA2N,cAAcA,CAACE,OAAqC,EAAA;QAClD,IAAI,CAAClK,iBAAiB,CAACmK,QAAQ,CAAC,IAAI,CAAC;QACrC,IAAI;YACF,IAAIhJ,MAAM,GACR/I,aAAa,CAACiD,wKAAAA,AAAa,EAAC,IAAI,CAACF,mBAAmB,CAAC,IAAI,IAAI,CAACG,aAAa,EAAE,iKAC3EjE,IAAI,CAACkE,WAAAA,AAAa,EAAC,IAAI,CAACC,mBAAmB,EAAE,CAAC,GAC9C0O,OAAO;YACX,MAAO/I,MAAM,KAAK,IAAI,CAAE;gBACtB,MAAMiJ,GAAG,GAAiCjJ,MAAM;gBAChD,MAAM0B,IAAI,GAAG,IAAI,CAACwH,OAAO,CAACD,GAAG,CAAC;gBAC9B,IAAIvH,IAAI,KAAK5I,SAAS,EAAE;oBACtB,MAAMqQ,EAAE,GAAGlQ,gBAAgB,CAACC,SAAU;oBACtCD,gBAAgB,CAACC,SAAS,GAAG,IAAI;oBACjC,IAAIiQ,EAAE,CAACC,GAAG,4KAAKtS,OAAO,CAACuS,GAAQ,EAAE;wBAC/B,0KAAIpS,aAAa,CAACqS,QAAAA,AAAmB,EAAC,IAAI,CAACtP,mBAAmB,CAAC,EAAE;4BAC/D,IAAI,CAACiG,IAAI,uKAAC1J,WAAawF,AAAQ,CAAT,CAACA,AAAU,CAAC;4BAClC,IAAI,CAACkE,IAAI,sKAAC1J,UAAawJ,AAAM,EAAP,CAACA,yJAAO7J,IAAI,CAACqT,MAAQ,CAAC,CAAC;4BAC7CvJ,MAAM,GAAG,IAAI;wBACf,CAAC,MAAM;4BACLA,MAAM,6JAAG9J,IAAI,CAACqT,MAAQ;wBACxB;oBACF,CAAC,MAAM,IAAIJ,EAAE,CAACC,GAAG,4KAAKtS,OAAO,CAAC0S,GAAQ,EAAE;wBACtC,wEAAA;wBACAxJ,MAAM,GAAG,IAAI;oBACf;gBACF,CAAC,MAAM;oBACL,IAAI,CAAChG,mBAAmB,yJAAGrF,OAAAA,AAAI,EAAC,IAAI,CAACqF,mBAAmB,wKAAE/C,SAAcwS,AAAM,EAACxS,EAAR,CAACwS,UAAoB,CAAC5G,+JAAQ,CAAC,CAAC;oBACvG,MAAM6G,YAAY,GAAG,IAAI,CAACxD,oBAAoB,EAAE;oBAChD,IAAIwD,YAAY,KAAK,IAAI,EAAE;wBACzB1J,MAAM,iKAAG9J,IAAI,CAAC4F,KAAAA,AAAO,EAAC4N,YAAY,EAAE,IAAMhI,IAAI,CAAC;oBACjD,CAAC,MAAM;wBACL,IAAI,IAAI,CAACzD,MAAM,CAACmH,MAAM,KAAK,CAAC,EAAE;4BAC5B,uEAAA;4BACA,IAAI,CAACiC,YAAY,CAAC3F,IAAI,CAAC;wBACzB,CAAC,MAAM;4BACL,iEAAA;4BACA,+DAAA;4BACA,+BAAA;4BACA,IAAI,CAACzB,IAAI,uKAAC1J,SAAawJ,AAAM,EAAC2B,CAAR,CAAC3B,EAAW,CAAC,CAAC;wBACtC;wBACAC,MAAM,GAAG,IAAI;oBACf;gBACF;YACF;QACF,CAAC,QAAS;YACR,IAAI,CAACnB,iBAAiB,CAAC8K,SAAS,CAAC,IAAI,CAAC;QACxC;IACF;IAEA;;;;;;;MAQA1R,KAAKA,CAAI+H,MAA8B,EAAA;QACrC,IAAI,CAAC,IAAI,CAAC3B,QAAQ,EAAE;YAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;YACpB,MAAM4G,IAAI,GAAIC,UAAkB,4JAAC5O,aAAa,CAAC6O,IAAe,CAAC;YAC7DD,UAAkB,4JAAC5O,aAAa,CAAC6O,IAAe,CAAC,GAAG,IAAI;YAC1D,IAAI;gBACF,IAAI,CAAC0D,cAAc,CAAC7I,MAAM,CAAC;YAC7B,CAAC,QAAS;gBACR,IAAI,CAAC3B,QAAQ,GAAG,KAAK;gBACnB6G,UAAkB,4JAAC5O,aAAa,CAAC6O,IAAe,CAAC,GAAGF,IAAI;gBAC1D,kEAAA;gBACA,gEAAA;gBACA,oEAAA;gBACA,4BAAA;gBACA,IAAI,IAAI,CAAChH,MAAM,CAACmH,MAAM,GAAG,CAAC,EAAE;oBAC1B,IAAI,CAAC9D,yBAAyB,EAAE;gBAClC;YACF;QACF,CAAC,MAAM;YACL,IAAI,CAACrB,IAAI,uKAAC1J,SAAawJ,AAAM,EAACC,CAAR,CAACD,IAAa,CAAC,CAAC;QACxC;IACF;IAEA;;;;;MAMA6J,SAASA,CAAI5J,MAA8B,EAAA;QACzC,IAAI,CAACC,IAAI,uKAAC1J,SAAmB,AAANwJ,EAAOC,CAAR,CAACD,IAAa,CAAC,CAAC;IACxC;IAEA;;;;;;MAOAhG,iBAAiBA,CAAC8P,eAA0C,EAAE5P,KAA0C,EAAA;QACtG,MAAM6P,eAAe,yKAAG7S,QAAcgD,AAAK,EAAC4P,GAAP,CAAC5P,WAAqB,EAAEA,KAAK,CAAC;QACjEiL,UAAkB,4JAAC5O,aAAa,CAAC6O,IAAe,CAAC,GAAG,IAAI;QAC1D,IAAI,CAACnL,mBAAmB,GAAG8P,eAAe;QAC1C,OAAOA,eAAe;IACxB;IAEA;;;;;;MAOAC,aAAaA,CACX/O,YAAuC,EACvCgP,aAA+E,EAAA;QAE/E,IAAIC,aAAa,GAAG,KAAK;QACzB,MAAMC,QAAQ,IAAIlK,MAAoC,IAAI;YACxD,IAAI,CAACiK,aAAa,EAAE;gBAClBA,aAAa,GAAG,IAAI;gBACpB,IAAI,CAAChK,IAAI,sKAAC1J,UAAawJ,AAAM,EAAP,AAAQC,CAAPD,KAAa,CAAC,CAAC;YACxC;QACF,CAAC;QACD,0KAAI9I,aAAa,CAACiD,EAAAA,AAAa,EAACc,YAAY,CAAC,EAAE;YAC7C,IAAI,CAACuD,iBAAiB,GAAG2L,QAAQ;QACnC;QACA,IAAI;YACFF,aAAa,CAACE,QAAQ,CAAC;QACzB,CAAC,CAAC,OAAOC,CAAC,EAAE;YACVD,QAAQ,+JAAChU,IAAI,CAACkU,OAAAA,AAAS,iKAACxU,MAAcyU,AAAG,EAACF,CAAC,CAAC,CAAC,CAAC,CAAT,CAACE;QACxC;IACF;IAEA7P,SAASA,CAACnB,IAAuB,EAAA;QAC/B,IAAI,CAACiF,MAAM,CAAC1B,IAAI,CAACvD,IAAI,CAAC;QACtB,IAAIA,IAAI,CAAC+P,GAAG,KAAK,QAAQ,EAAE;YACzB,IAAI,CAAC1K,MAAM,CAAC9B,IAAI,CAAC;gBAAE0N,IAAI,EAAE,IAAI,CAACzJ,YAAY,EAAE;gBAAE0J,KAAK,EAAE,IAAI,CAACvQ,mBAAAA;YAAmB,CAAE,CAAC;QAClF;IACF;IAEAwQ,QAAQA,CAAA,EAAA;QACN,MAAMC,IAAI,GAAG,IAAI,CAACnM,MAAM,CAACoM,GAAG,EAAE;QAC9B,IAAID,IAAI,EAAE;YACR,IAAIA,IAAI,CAACrB,GAAG,KAAK,QAAQ,EAAE;gBACzB,IAAI,CAAC1K,MAAM,CAACgM,GAAG,EAAE;YACnB;YACA,OAAOD,IAAI;QACb;QACA;IACF;IAEAE,kBAAkBA,CAAA,EAAA;QAChB,IAAIC,KAAK,GAAG,IAAI,CAACJ,QAAQ,EAAE;QAC3B,MAAOI,KAAK,CAAE;YACZ,IAAIA,KAAK,CAACxB,GAAG,4KAAKtS,OAAO,CAAC+T,QAAa,EAAE;gBACvC,OAAOD,KAAK;YACd;YACAA,KAAK,GAAG,IAAI,CAACJ,QAAQ,EAAE;QACzB;IACF;IAEAM,eAAeA,CAAA,EAAA;QACb,IAAIF,KAAK,GAAG,IAAI,CAACJ,QAAQ,EAAE;QAC3B,MAAOI,KAAK,CAAE;YACZ,IAAIA,KAAK,CAACxB,GAAG,4KAAKtS,OAAO,CAACsC,QAAa,IAAIwR,KAAK,CAACxB,GAAG,4KAAKtS,OAAO,CAACwD,GAAQ,IAAIsQ,KAAK,CAACxB,GAAG,4KAAKtS,OAAO,CAAC4D,MAAW,EAAE;gBAC9G,OAAOkQ,KAAK;YACd;YACAA,KAAK,GAAG,IAAI,CAACJ,QAAQ,EAAE;QACzB;IACF;IAEA,wKAAC1T,OAAO,CAACiU,CAAM,CAAA,CAAE5B,EAA6C,EAAA;QAC5D,qKAAOjT,IAAI,CAACkL,EAAAA,AAAI,EAAC,KAAMnN,OAAO,CAAC+W,wJAAAA,AAAS,EAAC,IAAI,CAAChM,cAAc,EAAEmK,EAAsC,CAAC,CAAC;IACxG;IAEA,CAAC,MAAM,CAAA8B,CAAE9B,EAAoC,EAAA;QAC3C,qKAAOjT,IAAI,CAACgV,EAAAA,AAAI,EAAC/B,EAAE,CAACgC,IAAI,CAAC;IAC3B;IAEA,CAAC,MAAM,CAAAC,CAAE3S,CAAmC,EAAA;QAC1C,qKAAOvC,IAAI,CAACgV,EAAAA,AAAI,EAAC,IAAIhV,IAAI,CAACmV,8KAAsB,EAAE,CAAC;IACrD;IAEA,CAAC,OAAO,CAAAC,CAAEnC,EAAqC,EAAA;QAC7C,qKAAOjT,IAAI,CAACwD,SAAAA,AAAW,EAACyP,EAAE,CAACoC,KAAK,CAAC;IACnC;IAEA,CAAC,MAAM,CAAAC,CAAErC,EAAoC,EAAA;QAC3C,OAAOjT,IAAI,CAACwD,uKAAAA,AAAW,EAACyP,EAAE,CAAC7P,KAAK,CAAC;IACnC;IAEA,CAAC,OAAO,CAAArE,CAAEkU,EAAmD,EAAA;QAC3D,qKAAOjT,IAAI,CAACuV,SAAAA,AAAW,GAAYC,WAAW,IAAI;YAChD,IAAI3L,MAAM,GAAG2L,WAAW;YACxB,MAAMlL,KAAK,sJAAGvL,KAAK,CAAC0W,IAAAA,AAAO,qJAAC1W,KAAK,CAAC2W,WAAAA,AAAc,EAACzC,EAAE,EAAE,IAAI,CAACnK,cAAc,CAAC,CAAC;YAC1EwB,KAAK,CAACoB,WAAW,EAAEF,IAAI,IAAI;gBACzB,IAAIA,IAAI,CAACkE,IAAI,KAAK,SAAS,EAAE;oBAC3B,OAAO7F,MAAM,EAAC7J,IAAI,CAACwD,sKAAAA,AAAW,EAACgI,IAAI,CAACpI,KAAK,CAAC,CAAC;gBAC7C;gBACA,OAAQoI,IAAI,CAACtG,KAAK,CAACwK,IAAI;oBACrB,KAAK,WAAW;wBAAE;4BAChB,OAAO7F,MAAM,8JAAC7J,IAAI,CAACkE,YAAAA,AAAa,iKAACxE,YAAcwN,AAAS,CAAV,CAACA,iJAAU/O,OAAO,AAAK,CAAJwX,AAAK,CAAC,CAAC;wBAC1E;oBACA,KAAK,MAAM;wBAAE;4BACX,OAAO9L,MAAM,+JAAC7J,IAAI,CAACgV,EAAAA,AAAI,EAACxJ,IAAI,CAACtG,KAAK,CAAC0Q,KAAK,CAAC,CAAC;wBAC5C;oBACA,KAAK,KAAK;wBAAE;4BACV,OAAO/L,MAAM,+JAAC7J,IAAI,CAACmU,CAAAA,AAAG,EAAC3I,IAAI,CAACtG,KAAK,CAAC2Q,MAAM,CAAC,CAAC;wBAC5C;gBACF;YACF,CAAC,CAAC;YACF,qKAAO7V,IAAI,CAACuV,SAAW,AAAXA,GAAmBO,WAAW,IAAI;gBAC5CjM,MAAM,IAAItH,CAAM,IAAI;oBAClBuT,WAAW,2JAAC9V,IAAI,CAACuE,EAAI,CAAC;gBACxB,CAAC;gBACD+F,KAAK,CAACyL,eAAe,EAAE;YACzB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ;IAEA,wKAACnV,OAAO,CAACoV,EAAO,CAAA,CAAE/C,EAA6C,EAAA;QAC7D,MAAM7P,KAAK,GAAG7D,kKAAAA,AAAY,EAAC,IAAM0T,EAAE,CAAC5O,qBAAqB,EAAE,CAAC;QAC5D,MAAMlB,IAAI,GAAG,IAAI,CAACsR,kBAAkB,EAAE;QACtC,IAAItR,IAAI,KAAK8S,SAAS,EAAE;YACtB,IAAI,CAAA,CAAE9S,IAAI,CAAC+P,GAAG,IAAIjQ,aAAa,CAAC,EAAE;gBAChC,mBAAA;gBACAR,MAAM,CAACU,IAAI,CAAC;YACd;YACA,mBAAA;YACA,OAAOF,aAAa,CAACE,IAAI,CAAC+P,GAAG,CAAC,CAAC,IAAI,EAAE/P,IAAI,EAAEC,KAAK,CAAC;QACnD,CAAC,MAAM;YACLL,gBAAgB,CAACC,SAAS,GAAGhD,IAAI,CAACwD,uKAAW,AAAXA,EAAYJ,KAAK,CAAQ;YAC3D,OAAOR,SAAS;QAClB;IACF;IAEA,wKAAChC,OAAO,CAACiQ,KAAU,CAAA,CAAEoC,EAAgD,EAAA;QACnE,MAAMiD,MAAM,GAAGjD,EAAE;QACjB,MAAM9P,IAAI,GAAG,IAAI,CAACsR,kBAAkB,EAAE;QACtC,IAAItR,IAAI,KAAK8S,SAAS,EAAE;YACtB,IAAI,CAAA,CAAE9S,IAAI,CAAC+P,GAAG,IAAIjQ,aAAa,CAAC,EAAE;gBAChC,mBAAA;gBACAR,MAAM,CAACU,IAAI,CAAC;YACd;YACA,mBAAA;YACA,OAAOF,aAAa,CAACE,IAAI,CAAC+P,GAAG,CAAC,CAAC,IAAI,EAAE/P,IAAI,EAAE+S,MAAM,CAAC7R,qBAAqB,CAAC;QAC1E,CAAC,MAAM;YACLtB,gBAAgB,CAACC,SAAS,GAAGkT,MAAM;YACnC,OAAOtT,SAAS;QAClB;IACF;IAEA,wKAAChC,OAAO,CAACkQ,KAAU,CAAA,CAAEmC,EAAgD,EAAA;QACnE,MAAM/N,KAAK,GAAG+N,EAAE,CAAC5O,qBAAqB;QACtC,MAAMlB,IAAI,GAAG,IAAI,CAACyR,eAAe,EAAE;QACnC,IAAIzR,IAAI,KAAK8S,SAAS,EAAE;YACtB,OAAQ9S,IAAI,CAAC+P,GAAG;gBACd,4KAAKtS,OAAO,CAAC+T,QAAa;gBAC1B,4KAAK/T,OAAO,CAAC6C,oBAAyB;oBAAE;wBACtC,IAAI,CAAA,uKAAE1C,aAAa,CAACiD,EAAAA,AAAa,EAAC,IAAI,CAACF,mBAAmB,CAAC,IAAI,IAAI,CAACG,aAAa,EAAE,CAAC,EAAE;4BACpF,0JAAO1E,eAAAA,AAAY,EAAC,IAAM4D,IAAI,CAACE,qBAAqB,CAAC6B,KAAK,CAAC,CAAC;wBAC9D,CAAC,MAAM;4BACL,qKAAOlF,IAAI,CAACkE,WAAAA,AAAa,gKAACxE,aAAa,CAACyW,GAAAA,AAAa,EAACjR,KAAK,CAAC,CAAC;wBAC/D;oBACF;gBACA,KAAK,QAAQ;oBAAE;wBACb,IAAI,CAAA,uKAAEnE,aAAa,CAACiD,EAAAA,AAAa,EAAC,IAAI,CAACF,mBAAmB,CAAC,IAAI,IAAI,CAACG,aAAa,EAAE,CAAC,EAAE;4BACpF,qKAAOjE,IAAI,CAACwD,SAAAA,AAAW,gKAACxD,IAAI,CAACkE,WAAAA,AAAa,EAACgB,KAAK,CAAC,CAAC;wBACpD,CAAC,MAAM;4BACL,OAAOlF,IAAI,CAACkE,yKAAAA,AAAa,EAACxE,aAAa,CAACyW,iKAAAA,AAAa,EAACjR,KAAK,CAAC,CAAC;wBAC/D;oBACF;gBACA,4KAAKtE,OAAO,CAAC+C,UAAe;oBAAE;wBAC5B,IAAI,CAACE,iBAAiB,CAAC,IAAI,CAACC,mBAAmB,EAAEX,IAAI,CAACY,KAAK,CAAC;wBAC5D,IAAIhD,aAAa,CAACiD,wKAAAA,AAAa,EAAC,IAAI,CAACF,mBAAmB,CAAC,IAAI,IAAI,CAACG,aAAa,EAAE,EAAE;4BACjF,qKAAOjE,IAAI,CAACkE,WAAAA,AAAa,gKAACxE,aAAa,CAAC2G,AAAU,EAACnB,KAAK,EAAE,IAAI,CAACf,mBAAmB,EAAE,CAAC,CAAC;wBACxF,CAAC,MAAM;4BACL,qKAAOnE,IAAI,CAACkE,WAAAA,AAAa,EAACgB,KAAK,CAAC;wBAClC;oBACF;gBACA;oBAAS;wBACPzC,MAAM,CAACU,IAAI,CAAC;oBACd;YACF;QACF,CAAC,MAAM;YACLJ,gBAAgB,CAACC,SAAS,iKAAGhD,IAAI,CAACkE,WAAAA,AAAa,EAACgB,KAAK,CAAQ;YAC7D,OAAOtC,SAAS;QAClB;IACF;IAEA,wKAAChC,OAAO,CAACwV,UAAe,CAAA,CAAEnD,EAAqD,EAAA;QAC7E,0JAAO1T,eAAAA,AAAY,EAAC,IAClB0T,EAAE,CAAC5O,qBAAqB,CACtB,IAAsC,2JACtC/F,UAAYoH,AAAO,CAAR,CAACA,AAAQ,IAAI,CAAC5B,mBAAmB,CAAwB,CACrE,CACF;IACH;IAEA,CAAC,SAAS,CAAAuS,CAAEpD,EAAuC,EAAA;QACjD,MAAMmB,IAAI,GAAG,IAAI,CAACzJ,YAAY,EAAE;QAChC,MAAM0J,KAAK,GAAG,IAAI,CAACvQ,mBAAmB;QACtC,IAAI,IAAI,CAAC0E,MAAM,CAAC0G,MAAM,GAAG,CAAC,EAAE;YAC1B,MAAMoH,MAAM,GAA6B,EAAE;YAC3C,MAAMC,IAAI,GAAG,IAAI,CAAC/N,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC0G,MAAM,GAAG,CAAC,CAAC;YAChD,IAAIwF,KAAK,GAAG,IAAI,CAACJ,QAAQ,EAAE;YAC3B,MAAOI,KAAK,IAAIA,KAAK,CAACxB,GAAG,KAAK,QAAQ,CAAE;gBACtCoD,MAAM,CAAC5P,IAAI,CAACgO,KAAK,CAAC;gBAClBA,KAAK,GAAG,IAAI,CAACJ,QAAQ,EAAE;YACzB;YACA,IAAI,CAAChI,YAAY,CAACiK,IAAI,CAACnC,IAAI,CAAC;YAC5B,IAAI,CAACtQ,mBAAmB,GAAGyS,IAAI,CAAClC,KAAK;YACrC,MAAMmC,SAAS,+JAAGnY,OAAmB,AAAJmO,EAAK+J,IAAI,CAACnC,AAAX,CAAC5H,GAAc,EAAE4H,IAAI,CAAC;YACtD,MAAMqC,UAAU,yKAAG1V,OAAcyL,AAAI,EAAC+J,IAAN,AAAU,CAAT/J,AAAU6H,KAAK,EAAEA,KAAK,CAAC;YACxD,qKAAOrU,IAAI,CAACwD,SAAW,AAAXA,+JAAYxD,IAAI,CAAC0W,MAAAA,AAAO,EAClCzD,EAAE,CAAC5O,qBAAqB,gKACxBrE,IAAI,CAAC6L,cAAAA,AAAgB,GAAoB8K,QAAQ,IAAI;gBACnD,MAAOL,MAAM,CAACpH,MAAM,GAAG,CAAC,CAAE;oBACxByH,QAAQ,CAACrS,SAAS,CAACgS,MAAM,CAAC9B,GAAG,EAAG,CAAC;gBACnC;gBACAmC,QAAQ,CAACrK,YAAY,6JACnBjO,QAAe0F,AAAK,EAAC4S,IAAP,CAAC5S,GAAc,CAAC6F,EAAE,EAAE,EAAE+M,QAAQ,CAAChM,YAAY,EAAE,CAAC,CAAC6L,SAAS,CAAC,CACxE;gBACDG,QAAQ,CAAC7S,mBAAmB,GAAG/C,aAAa,CAACgD,gKAAAA,AAAK,EAAC0S,UAAU,CAAC,CAACE,QAAQ,CAAC7S,mBAAmB,CAAC;gBAC5F,OAAOmP,EAAE,CAAC5P,qBAAqB;YACjC,CAAC,CAAC,CACH,CAAC;QACJ;QACA,OAAOrD,IAAI,CAAC4W,+KAAAA,AAAmB,GAAEC,OAAO,iKACtC7W,IAAI,CAAC4F,KAAAA,AAAO,EACVkR,UAAU,+JAAC9W,IAAI,CAAC+W,aAAe,AAAfA,EAAgB9D,EAAE,CAAC5O,qBAAqB,CAAC,CAAC,EAC1D,IAAMwS,OAAO,CAAC5D,EAAE,CAAC5P,qBAAqB,CAAC,CACxC,CACF;IACH;IAEA,CAAC,YAAY,CAAA2T,CAAE/D,EAA0C,EAAA;QACvD,OAAOnN,kBAAkB,CAACmN,EAAE,CAAC5O,qBAAqB,CAAC;IACrD;IAEA,wKAACzD,OAAO,CAACqW,kBAAuB,CAAA,CAAEhE,EAA6D,EAAA;QAC7F,MAAMiE,WAAW,GAAGjE,EAAE,CAAC5O,qBAAqB;QAC5C,MAAMsP,eAAe,GAAG,IAAI,CAAC7P,mBAAmB;QAChD,MAAM8P,eAAe,IAAG7S,aAAa,CAACgD,+JAAAA,AAAK,EAAC4P,eAAe,EAAEuD,WAAW,CAAC;QACzE,+DAAA;QACA,+DAAA;QACA,8DAAA;QACA,0BAAA;QACA,0KAAInW,aAAa,CAACiD,EAAAA,AAAa,EAAC4P,eAAe,CAAC,IAAI,IAAI,CAAC3P,aAAa,EAAE,EAAE;YACxE,qKAAOjE,IAAI,CAACkE,WAAAA,AAAa,EAAC,IAAI,CAACC,mBAAmB,EAAE,CAAC;QACvD,CAAC,MAAM;YACL,qDAAA;YACA,IAAI,CAACN,iBAAiB,CAAC,IAAI,CAACC,mBAAmB,EAAEoT,WAAW,CAAC;YAC7D,IAAIjE,EAAE,CAAC5P,qBAAqB,EAAE;gBAC5B,qDAAA;gBACA,MAAM8T,WAAW,wKAAGpW,QAAcyL,AAAI,EAACoH,GAAN,CAACpH,WAAoB,EAAEmH,eAAe,CAAC;gBACxE,IAAI,CAACrP,SAAS,CAAC,8JAAItE,IAAI,CAACoX,SAAW,CAACD,WAAW,EAAElE,EAAE,CAAC,CAAC;gBACrD,WAAO1T,8JAAY,AAAZA,EAAa,IAAM0T,EAAE,CAAC5P,qBAAsB,CAACsQ,eAAe,CAAC,CAAC;YACvE,CAAC,MAAM;gBACL,iKAAO3T,IAAI,CAACqT,MAAQ;YACtB;QACF;IACF;IAEA,CAACzS,OAAO,CAACsC,+KAAa,CAAA,CAAE+P,EAAmD,EAAA;QACzE,IAAI,CAAC3O,SAAS,CAAC2O,EAAE,CAAC;QAClB,OAAOA,EAAE,CAAC5O,qBAAqB;IACjC;IAEA,CAAC,QAAQ,CAAAf,CAAE2P,EAAsC,EAAA;QAC/C,IAAI,CAAC3O,SAAS,CAAC2O,EAAE,CAAC;QAClB,OAAOA,EAAE,CAAC5O,qBAAqB;IACjC;IAEA,wKAACzD,OAAO,CAAC+T,QAAa,CAAA,CAAE1B,EAAmD,EAAA;QACzE,IAAI,CAAC3O,SAAS,CAAC2O,EAAE,CAAC;QAClB,OAAOA,EAAE,CAAC5O,qBAAqB;IACjC;IAEA,CAACzD,OAAO,CAAC6C,2LAAyB,CAAA,CAAEwP,EAA+D,EAAA;QACjG,IAAI,CAAC3O,SAAS,CAAC2O,EAAE,CAAC;QAClB,OAAOA,EAAE,CAAC5O,qBAAqB;IACjC;IAEA,wKAACzD,OAAO,CAAC0S,GAAQ,CAAA,CAAEL,EAA8C,EAAA;QAC/D,IAAI,CAAC3K,gBAAgB,GAAG2K,EAAE,CAAC5P,qBAAqB;QAChD,IAAI,CAACwQ,aAAa,CAAC,IAAI,CAAC/P,mBAAmB,EAAEmP,EAAE,CAAC5O,qBAAqB,CAAC;QACtEtB,gBAAgB,CAACC,SAAS,GAAGiQ,EAAE;QAC/B,OAAOrQ,SAAS;IAClB;IAEA,wKAAChC,OAAO,CAACuS,GAAQ,CAAA,CAAEF,EAA6C,EAAA;QAC9D,IAAI,CAACxK,WAAW,GAAG,KAAK;QACxB1F,gBAAgB,CAACC,SAAS,GAAGiQ,EAAE;QAC/B,OAAOrQ,SAAS;IAClB;IAEA,wKAAChC,OAAO,CAACwD,GAAQ,CAAA,CAAE6O,EAA8C,EAAA;QAC/D,MAAMoE,KAAK,GAAGpE,EAAE,CAAC5O,qBAAqB;QACtC,MAAM8L,IAAI,GAAG8C,EAAE,CAAC5P,qBAAqB;QACrC,IAAIgU,KAAK,EAAE,EAAE;YACX,IAAI,CAAC/S,SAAS,CAAC2O,EAAE,CAAC;YAClB,OAAO9C,IAAI,EAAE;QACf,CAAC,MAAM;YACL,iKAAOnQ,IAAI,CAACqT,MAAQ;QACtB;IACF;IAEA,wKAACzS,OAAO,CAAC4D,MAAW,CAAA,CAAEyO,EAAiD,EAAA;QACrE,OAAOhQ,aAAa,wKAACrC,OAAO,CAAC4D,MAAW,CAAC,CAAC,IAAI,EAAEyO,EAAE,EAAEgD,SAAS,CAAC;IAChE;IAEA,wKAACrV,OAAO,CAAC0W,IAAS,CAAA,CAAErE,EAA+C,EAAA;QACjE,0JAAO1T,eAAAA,AAAY,EAAC,IAAM0T,EAAE,CAACvJ,MAAM,EAAE,CAAC;IACxC;IAEA;;;;MAKAsJ,OAAOA,CAACH,OAAqC,EAAA;QAC3C,IAAI9N,GAAG,GAA6C8N,OAAO;QAC3D,IAAI,CAACnK,cAAc,GAAG,CAAC;QAEvB,MAAO,IAAI,CAAE;YACX,IAAI,CAAC,IAAI,CAAC5E,mBAAmB,qKAAG9C,gBAAa,MAAM,CAAC,EAAE;gBACpD,IAAI,CAAC2H,iBAAiB,CAAC4O,QAAQ,CAAC,IAAI,EAAExS,GAAG,CAAC;YAC5C;YACA,IAAI,IAAI,CAACgD,MAAM,CAACmH,MAAM,GAAG,CAAC,EAAE;gBAC1BnK,GAAG,GAAG,IAAI,CAACyK,sBAAsB,CAAC,IAAI,CAAC1L,mBAAmB,EAAEiB,GAAG,CAAC;YAClE;YACA,IAAI,CAAC,IAAI,CAAC0D,WAAW,EAAE;gBACrB,IAAI,CAACC,cAAc,IAAI,CAAC;gBACxB,MAAM8O,WAAW,GAAG,IAAI,CAAClY,gBAAgB,CAACkY,WAAW,CAAC,IAAI,CAAC;gBAC3D,IAAIA,WAAW,KAAK,KAAK,EAAE;oBACzB,IAAI,CAAC/O,WAAW,GAAG,IAAI;oBACvB,IAAI,CAACC,cAAc,GAAG,CAAC;oBACvB,MAAMwN,MAAM,GAAGnR,GAAG;oBAClBA,GAAG,gKAAG/E,IAAI,CAAC4F,MAAAA,AAAO,gKAAC5F,IAAI,CAAC6F,MAAAA,AAAQ,EAAC;wBAAE4R,QAAQ,EAAED;oBAAW,CAAE,CAAC,EAAE,IAAMtB,MAAM,CAAC;gBAC5E;YACF;YACA,IAAI;gBACF,mBAAA;gBACAnR,GAAG,GAAG,IAAI,CAAC6D,aAAa,CAAC8J,OAAO,CAC9B,MAAK;oBACH,IAAIpL,QAAQ,KAAMvC,GAAsB,CAAC/E,IAAI,CAAC0X,oKAAY,CAAC,CAACC,EAAE,EAAE;wBAC9D,qKAAO3X,IAAI,CAAC4X,QAAU,AAAVA,EACV,CAAA,mCAAA,EACG7S,GAAsB,2JAAC/E,IAAI,CAAC0X,UAAY,CAAC,CAACC,EAC7C,CAAA,2BAAA,mKAA8BvW,OAAO,CAACmG,YAAAA,AAAiB,EAAE,GAAE,CAC5D;oBACH;oBACA,mBAAA;oBACA,OAAO,IAAI,CAAExC,GAAsB,CAACmO,GAAG,CAAC,CAACnO,GAAqB,CAAC;gBACjE,CAAC,EACD,IAAI,CACL;gBAED,IAAIA,GAAG,KAAKnC,SAAS,EAAE;oBACrB,MAAMqQ,EAAE,GAAGlQ,gBAAgB,CAACC,SAAU;oBACtC,IACEiQ,EAAE,CAACC,GAAG,4KAAKtS,OAAO,CAACuS,GAAQ,IAC3BF,EAAE,CAACC,GAAG,4KAAKtS,OAAO,CAAC0S,GAAQ,EAC3B;wBACA,OAAO1Q,SAAS;oBAClB;oBAEAG,gBAAgB,CAACC,SAAS,GAAG,IAAI;oBACjC,OACIiQ,EAAE,CAACC,GAAG,4KAAKtS,OAAO,CAACiQ,KAAU,IAC7BoC,EAAE,CAACC,GAAG,4KAAKtS,OAAO,CAACkQ,KAAU,GAE/BmC,EAAgC,iKAChCjT,IAAI,CAACkE,WAAAA,AAAa,iKAACxE,MAAcyU,AAAG,EAAClB,EAAE,CAAC,CAAC,CAAT,CAACkB;gBACrC;YACF,CAAC,CAAC,OAAOF,CAAC,EAAE;gBACV,IAAIlP,GAAG,KAAKnC,SAAS,IAAI,wJAACzD,SAAS,CAAC0Y,IAAW,AAAXA,EAAY9S,GAAG,EAAE,KAAK,CAAC,IAAI,CAAA,CAAGA,GAAsB,CAACmO,GAAG,IAAI,IAAI,CAAC,EAAE;oBACrGnO,GAAG,iKAAG/E,IAAI,CAAC4X,QAAAA,AAAU,EAAC,CAAA,oBAAA,2JAAuB/Y,WAAW,CAAC8D,MAAAA,AAAe,EAACoC,GAAG,CAAC,EAAE,CAAC;gBAClF,CAAC,MAAM,IAAI/E,IAAI,CAAC8X,kLAAAA,AAAsB,EAAC7D,CAAC,CAAC,EAAE;oBACzClP,GAAG,iKAAG/E,IAAI,CAACkE,WAAAA,AAAa,iKACtBxE,aAAa,AAAC2G,AAAU,CAAVA,gKAAW3G,MAAcyU,AAAG,EAACF,CAAC,CAAC,GAAP,CAACE,6JAAQzU,YAAcwN,AAAS,CAAV,CAACA,iJAAU/O,OAAO,AAAK,CAAJwX,AAAK,CAAC,CACtF;gBACH,CAAC,MAAM;oBACL5Q,GAAG,IAAG/E,IAAI,CAACmU,8JAAAA,AAAG,EAACF,CAAC,CAAC;gBACnB;YACF;QACF;IACF;IAEA3E,GAAG,GAAGA,CAAA,KAAK;QACT,IAAI,CAACV,yBAAyB,EAAE;IAClC,CAAC,CAAA;;AAMI,MAAMgD,sBAAsB,GAAA,WAAA,4JAAyClT,cAAAA,AAAW,EACrF,wCAAwC,EACxC,kKAAMsB,IAAI,CAAC+X,gBAAAA,AAAkB,wJAAoBjZ,QAAQ,CAACkZ,KAAAA,AAAW,EAAC,MAAM,CAAC,CAAC,CAC/E;AAGM,MAAMC,oBAAoB,IAAUrU,IAAkB,OAC3DpD,cAAc,CAAC0X,0JAAAA,AAAU,GAAEC,IAAI,IAAI;QACjC,MAAMC,QAAQ,0JAAGha,SAAS,CAACia,KAAAA,AAAY,EAACF,IAAI,CAACzF,OAAO,uKAAEzS,eAAe,CAAC+N,EAAe,CAAC;6JACtFjQ,MAAQ8P,AAAG,CAAJ,CAACA,AAAIuK,QAAQ,kLAAElY,aAAU,CAAC,CAACoY,MAAM,CAACpH,GAAG,CAACtN,IAAI,CAACsN,GAAG,CAACiH,IAAI,CAAC,CAAC;IAC9D,CAAC,CAAC;AAGG,MAAMI,oBAAoB,IAAU3U,IAAkB,IAC3DpD,cAAc,CAAC0X,6JAAAA,AAAU,GAAEC,IAAI,IAAI;QACjC,MAAMC,QAAQ,0JAAGha,SAAS,CAACia,KAAAA,AAAY,EAACF,IAAI,CAACzF,OAAO,uKAAEzS,eAAe,CAAC+N,EAAe,CAAC;QACtF,MAAMwK,YAAY,wJAAGza,MAAW,AAAH8P,CAAD,CAACA,AAAIuK,QAAQ,kLAAElY,aAAU,CAAC,CAACoY,MAAM;QAC7D,OAAQH,IAAI,CAAC3G,QAAQ,CAAC9B,IAAI;YACxB,KAAK,OAAO;gBACV,OAAO8I,YAAY,CAACC,KAAK,CAAC7U,IAAI,CAACsN,GAAG,CAACiH,IAAI,CAAC,CAAC;YAC3C,KAAK,MAAM;gBACT,OAAOK,YAAY,CAACE,IAAI,CAAC9U,IAAI,CAACsN,GAAG,CAACiH,IAAI,CAAC,CAAC;YAC1C,KAAK,OAAO;gBACV,OAAOK,YAAY,CAACG,KAAK,CAAC/U,IAAI,CAACsN,GAAG,CAACiH,IAAI,CAAC,CAAC;YAC3C,KAAK,SAAS;gBACZ,OAAOK,YAAY,CAACI,IAAI,CAAChV,IAAI,CAACsN,GAAG,CAACiH,IAAI,CAAC,CAAC;YAC1C,KAAK,OAAO;YACZ,KAAK,OAAO;gBACV,OAAOK,YAAY,CAAC5C,KAAK,CAAChS,IAAI,CAACsN,GAAG,CAACiH,IAAI,CAAC,CAAC;YAC3C;gBACE,OAAOK,YAAY,CAACtH,GAAG,CAACtN,IAAI,CAACsN,GAAG,CAACiH,IAAI,CAAC,CAAC;QAC3C;IACF,CAAC,CAAC;AAGG,MAAMU,sBAAsB,IAAUjV,IAAkB,GAC7DpD,cAAc,CAAC0X,8JAAU,AAAVA,GAAYC,IAAI,IAAI;QACjC,MAAMC,QAAQ,0JAAGha,SAAS,CAACia,KAAAA,AAAY,EAACF,IAAI,CAACzF,OAAO,uKAAEzS,eAAe,CAAC+N,EAAe,CAAC;6JACtFjQ,MAAQ8P,AAAG,CAAJ,CAACA,AAAIuK,QAAQ,kLAAElY,aAAU,CAAC,CAACoY,MAAM,CAAC1C,KAAK,CAAChS,IAAI,CAACsN,GAAG,CAACiH,IAAI,CAAC,CAAC;IAChE,CAAC,CAAC;AAGG,MAAMW,aAAa,GAAA,WAAA,4JAA0Bpa,cAAAA,AAAW,EAAA,WAAA,GAC7DmE,MAAM,CAACC,GAAG,CAAC,6BAA6B,CAAC,EACzC,IAAMmV,oBAAoB,CAACzX,cAAc,CAACuY,4JAAY,CAAC,CACxD;AAGM,MAAMC,UAAU,GAAA,WAAA,4JAA0Bta,cAAW,AAAXA,EAAW,WAAA,GAC1DmE,MAAM,CAACC,GAAG,CAAC,0BAA0B,CAAC,EACtC,IAAMmV,oBAAoB,6JAACzX,aAAyB,CAAX,AAAY,CAAXwY,AAC3C;AAGM,MAAMC,YAAY,GAAA,WAAA,OAA0Bva,mKAAAA,AAAW,EAAA,WAAA,GAC5DmE,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,EACxC,IAAMmV,oBAAoB,6JAACzX,cAAc,CAAC0Y,AAAY,CAAC,CACxD;AAGM,MAAMC,YAAY,GAAA,WAAA,4JAA0Bza,cAAAA,AAAW,EAAA,WAAA,GAC5DmE,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,EACxC,gKAAMtC,cAAc,CAAC4Y,OAAmB,CACzC;AAGM,MAAMC,gBAAgB,GAAA,WAAA,OAA0B3a,mKAAAA,AAAW,EAAA,WAAA,GAChEmE,MAAM,CAACC,GAAG,CAAC,gCAAgC,CAAC,EAC5C,IAAMmV,oBAAoB,6JAACzX,cAAc,CAAC6Y,IAAgB,CAAC,CAC5D;AAGM,MAAMC,YAAY,GAAA,WAAA,4JAAG5a,cAAAA,AAAW,EAAA,WAAA,GACrCmE,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,EACxC,QACEtC,cAAc,CAAC0X,0JAAAA,AAAU,EAAgB,CAAC,EACxClG,WAAW,EACX9M,KAAK,EACLwN,OAAO,EACPzJ,OAAO,EACPuI,QAAQ,EACRxM,OAAAA,EACD,KAAI;QACH,MAAMuU,IAAI,wJAAGxb,OAAO,CAACyb,IAAS,AAATA,qKACnBlZ,SAAS,CAAC+X,KAAAA,AAAY,EAAC3F,OAAO,4JAAE1S,IAAI,CAAC8I,YAAc,CAAC,8JACpD3H,MAAM,CAACiN,GAAO,CACf;QACD,IAAImL,IAAI,CAAC7J,IAAI,KAAK,MAAM,IAAI6J,IAAI,CAACnW,KAAK,CAACsM,IAAI,KAAK,cAAc,EAAE;YAC9D;QACF;QACA,MAAM0C,YAAY,wJAAGrU,OAAO,CAAC+W,IAAAA,AAAS,qKACpCxU,SAAS,CAAC+X,KAAAA,AAAY,EAAC3F,OAAO,uKAAEzS,eAAe,CAAC+N,EAAe,CAAC,EAChErO,KAAK,CAAC0S,gKAAQ,CACf;QAED,MAAMoH,UAAU,GAA4B,CAAA,CAAE;QAC9C,KAAK,MAAM,CAACtL,GAAG,EAAE/K,KAAK,CAAC,IAAI4O,WAAW,CAAE;YACtCyH,UAAU,CAACtL,GAAG,CAAC,GAAG/K,KAAK;QACzB;QACAqW,UAAU,CAAC,gBAAgB,CAAC,uJAAGtb,OAAO,CAACub,MAAAA,AAAU,EAACzQ,OAAO,CAAC;QAC1DwQ,UAAU,CAAC,iBAAiB,CAAC,GAAGjI,QAAQ,CAACmI,KAAK;QAE9C,IAAIzU,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACwK,IAAI,KAAK,OAAO,EAAE;YAC5C+J,UAAU,CAAC,cAAc,CAAC,kKAAG/Z,SAAcka,AAAM,EAAC1U,EAAR,CAAC0U,EAAY,EAAE;gBAAEC,gBAAgB,EAAE;YAAI,CAAE,CAAC;QACtF;QAEAN,IAAI,CAACnW,KAAK,CAAC0W,KAAK,CACdjb,WAAW,CAAC8D,+JAAAA,AAAe,EAACqF,KAAK,CAAC+R,OAAO,CAAC/U,OAAO,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,EAC1EoN,YAAY,CAAC4H,sBAAsB,EAAE,EACrCP,UAAU,CACX;IACH,CAAC,CAAC,CACL;AAGM,MAAMQ,yBAAyB,GAAqBrW,IAA6B,oKACtFpD,cAAc,CAAC0Z,GAAAA,AAAe,EAACtW,IAAI,EAAGuW,OAAgC,IAAI;QACxE,MAAMZ,IAAI,uJAAGta,MAAM,CAAC2G,GAAAA,AAAO,qKAACtF,MAAUuN,AAAG,EAACsM,CAAL,CAACtM,KAAW,CAAC6E,OAAO,4JAAE1S,IAAI,CAAC8I,YAAc,CAAC,uJAAE/K,OAAO,CAACyb,IAAAA,AAAS,EAACrY,MAAM,CAACiN,+JAAO,CAAC,CAAC;QACnH,IAAImL,IAAI,CAAC7J,IAAI,KAAK,MAAM,EAAE;YACxB,OAAOyK,OAAO;QAChB;QACA,OAAO;YACL,GAAGA,OAAO;YACVnI,WAAW,EAAEvT,6JAAAA,AAAI,EACf0b,OAAO,CAACnI,WAAW,uJACnBrT,MAAW,AAAHkI,CAAD,CAACA,AAAI,gBAAgB,EAAE0S,IAAI,CAACnW,KAAK,CAACgX,OAAkB,CAAC,EAC5Dzb,OAAO,CAACkI,mJAAAA,AAAG,EAAC,eAAe,EAAE0S,IAAI,CAACnW,KAAK,CAACiX,MAAiB,CAAC,EAC1Dd,IAAI,CAACnW,KAAK,CAACsM,IAAI,KAAK,MAAM,wJAAG/Q,MAAQkI,AAAG,CAAJ,CAACA,AAAI,iBAAiB,EAAE0S,IAAI,CAACnW,KAAK,CAACkX,IAAe,CAAC,qJAAG9b,WAAQ;SAErG;IACH,CAAC,CAAC;AAGG,MAAM8S,cAAc,GAAA,WAAA,4JAEvB5S,cAAAA,AAAW,EAAA,WAAA,GACbmE,MAAM,CAACC,GAAG,CAAC,gCAAgC,CAAC,EAC5C,kKAAM9C,IAAI,CAACua,uBAAAA,AAAyB,uJAAC3b,OAAY,AAAJ4b,AAAD,CAACA,CAAK1B,aAAa,EAAEQ,YAAY,CAAC,CAAC,CAChF;AAGM,MAAMmB,aAAa,GAAA,WAAA,yJAAGlc,OAAAA,AAAI,EAY/B,CAAC,EAAE,CACHqF,IAA6B,EAC7B8W,MAA8B,EAC9B9P,CAAsE,iKAEtE5K,IAAI,CAAC4F,KAAO,AAAPA,EAAQuE,KAAK,GAAGA,KAAK,IAAI;QAC5B,IAAIwQ,MAAM,GAAkB,EAAE;QAC9B,MAAMC,KAAK,iKAAG5a,IAAI,CAAC6K,KAAO,AAAPA,EAAQ,MAAK;YAC9B,IAAI8P,MAAM,CAACzL,MAAM,KAAK,CAAC,EAAE;gBACvB,iKAAOlP,IAAI,CAACuE,EAAI;YAClB;YACA,MAAMiC,GAAG,GAAGmU,MAAM;YAClBA,MAAM,GAAG,EAAE;YACX,OAAO/P,CAAC,CAACpE,GAAG,CAAC;QACf,CAAC,CAAC;QAEF,qKAAOxG,IAAI,CAAC4W,iBAAAA,AAAmB,GAAEC,OAAO,yJACtCpY,OAAAA,AAAI,0KACFsB,QAAe8a,AAAK,EAACH,IAAP,CAACG,CAAY,CAAC,gKAC5B7a,IAAI,CAAC8a,MAAAA,AAAQ,EAACF,KAAK,CAAC,EACpB7a,cAAc,CAACgb,+JAAO,EACtBlE,OAAO,EACPC,UAAU,gKACV9W,IAAI,CAAC4F,KAAAA,AAAO,GAAE0E,KAAK,iKAAKtK,IAAI,CAACgb,eAAAA,AAAiB,EAAC7Q,KAAK,gKAAEnK,IAAI,CAACib,YAAAA,AAAc,EAAC3Q,KAAK,CAAC,CAAC,CAAC,gKAClFtK,IAAI,CAAC8a,MAAQ,AAARA,EAASI,YAAY,CAAC,IAAMN,KAAK,CAAC,CAAC,gKACxC5a,IAAI,CAACmb,AAAE,kKACL3a,aAAe0X,AAAU,CAAX,CAACA,CAAYiC,OAAO,IAAI;gBACpCQ,MAAM,CAACjU,IAAI,CAAC9C,IAAI,CAACsN,GAAG,CAACiJ,OAAO,CAAC,CAAC;YAChC,CAAC,CAAC,CACH,CACF,CACF;IACH,CAAC,CAAC,CAAC;AAEE,MAAMiB,kBAAkB,GAG3B,SAAAA,CAAA;IACF,IAAI,OAAOC,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QACpC,OAAOC,yBAAyB,2JAC9Btb,IAAI,CAACiS,mBAAqB,uJAC1BtT,MAAQkI,AAAG,CAAJ,CAAKwU,AAAJxU,SAAa,CAAC,CAAC,CAAC,EAAEwU,SAAS,CAAC,CAAC,CAAC,CAAC,CACxC;IACH;IACA,MAAME,OAAO,GAAGC,MAAM,CAACD,OAAO,CAACF,SAAS,CAAC,CAAC,CAAC,CAAC;IAC5C,OAAOC,yBAAyB,2JAC9Btb,IAAI,CAACiS,mBAAqB,sJAC1BtT,OAAO,CAAC8c,EAAM,AAANA,GAAQzJ,WAAW,IAAI;QAC7B,IAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmK,OAAO,CAACrM,MAAM,EAAEkC,CAAC,EAAE,CAAE;YACvC,MAAM,CAACjD,GAAG,EAAE/K,KAAK,CAAC,GAAGmY,OAAO,CAACnK,CAAC,CAAC;aAC/BzS,OAAO,CAACkI,kJAAAA,AAAG,EAACmL,WAAW,EAAE7D,GAAG,EAAE/K,KAAK,CAAC;QACtC;QACA,OAAO4O,WAAW;IACpB,CAAC,CAAC,CACH;AACH,CAAC;AAGM,MAAM0J,YAAY,GAAA,WAAA,wJAAGnd,QAAAA,AAAI,EAQ9B,CAAC,EAAE,CAACuL,MAAM,EAAEiH,KAAK,KAAI;IACrB,MAAM4K,gBAAgB,GAAG,OAAO5K,KAAK,KAAK,QAAQ,wJAAGjS,QAAQ,CAACkZ,MAAW,AAAXA,EAAYjH,KAAK,CAAC,GAAGA,KAAK;IAExF,qKAAO/Q,IAAI,CAAC6L,cAAAA,AAAgB,GAAE+P,UAAU,IAAI;QAC1C,MAAMjK,eAAe,GAAGiK,UAAU,CAACtS,WAAW,CAACsI,sBAAsB,CAAC;QAEtE,8CAAA;QACA,IAAI9S,QAAQ,CAAC+S,2JAAAA,AAAW,EAACF,eAAe,EAAEgK,gBAAgB,CAAC,EAAE;YAC3D,qKAAO3b,IAAI,CAACyL,KAAAA,AAAO,sJAACxM,MAAM,CAAC0W,AAAI,EAAE,CAAC;QACpC;QAEA,qKAAO3V,IAAI,CAACsG,CAAAA,AAAG,EAACwD,MAAM,kJAAE7K,MAAM,CAAC4c,AAAI,CAAC;IACtC,CAAC,CAAC;AACJ,CAAC,CAAC;AAKK,MAAMC,cAAc,GAAA,WAAA,yJAQvBvd,OAAAA,AAAI,GAAEwd,IAAI,iKAAK/b,IAAI,CAACgc,MAAAA,AAAQ,EAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAACE,OAAO,EAAEC,OAAO,iKAC1Dlc,IAAI,CAACmc,aAAAA,AAAe,gKAClBnc,IAAI,CAACoc,CAAAA,AAAG,EAACH,OAAO,GAAGI,CAAC,GAAKnB,YAAY,EAAE1P,IAAI,GAAK0Q,OAAO,CAACG,CAAC,EAAE7Q,IAAI,CAAC,CAAC,CAAC,CACnE,CAAC;AAGG,MAAM8Q,2BAA2B,GAAA,WAAA,yJAQpC/d,OAAAA,AAAI,GAAEwd,IAAI,gKAAK/b,IAAI,CAACgc,OAAAA,AAAQ,EAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAACE,OAAO,EAAEC,OAAO,GAC1DK,QAAQ,CACNN,OAAO,EACPf,YAAY,EAAE1P,IAAI,GAAK0Q,OAAO,CAAC1Q,IAAI,CAAC,CAAC,CACtC,CAAC;AAGG,MAAM0P,YAAY,IACvBsB,SAA4E,GAE5Exc,IAAI,CAAC6L,4KAAAA,AAAgB,GAClB4Q,OAAO,IAAI;QACV,MAAMC,WAAW,GAAGD,OAAO,CAAC9R,YAAY,EAAE;QAC1C,MAAMgS,YAAY,GAAGF,OAAO,CAAC3Y,mBAAmB;QAChD,qKAAO9D,IAAI,CAAC4F,KAAAA,AAAO,EAACuE,KAAK,GAAGA,KAAK,GAC/BnK,IAAI,CAAC4c,iLAAAA,AAAqB,EAACzS,KAAK,GAAGqB,IAAI,iKACrCxL,IAAI,CAAC6L,cAAAA,AAAgB,GAAEgR,gBAAgB,IAAI;oBACzC,MAAMC,OAAO,GAAGD,gBAAgB,CAAClS,YAAY,EAAE;oBAC/C,MAAMoS,QAAQ,GAAGF,gBAAgB,CAAC/Y,mBAAmB;oBACrD,MAAM0S,SAAS,+JAAGnY,OAAemO,AAAI,EAACsQ,KAAN,CAACtQ,CAAY,EAAEkQ,WAAW,CAAC;oBAC3D,MAAMjG,UAAU,GAAG1V,aAAa,CAACyL,+JAAAA,AAAI,EAACuQ,QAAQ,EAAEJ,YAAY,CAAC;oBAC7D,MAAMK,WAAW,+JAAG3e,OAAemO,AAAI,EAACkQ,KAAN,CAAClQ,KAAgB,EAAEsQ,OAAO,CAAC;oBAC7DD,gBAAgB,CAACvQ,YAAY,EAC3BjO,cAAc,CAAC0F,oJAAK,AAALA,EAAMyS,SAAS,EAAEqG,gBAAgB,CAACjT,EAAE,EAAE,EAAE8S,WAAW,CAAC,CACpE;oBAED,OAAOH,QAAQ,+JACbvc,IAAI,CAACid,cAAAA,AAAgB,EAACT,SAAS,CAAChR,IAAI,CAAqB,EAAEiL,UAAU,CAAC,gKACtEzW,IAAI,CAACkL,EAAI,AAAJA,EAAK,MAAK;wBACb2R,gBAAgB,CAACvQ,YAAY,4JAC3BjO,SAAe0F,AAAK,EAACiZ,GAAP,CAACjZ,OAAiB,EAAE8Y,gBAAgB,CAACjT,EAAE,EAAE,EAAEiT,gBAAgB,CAAClS,YAAY,EAAE,CAAC,CAC1F;oBACH,CAAC,CAAC,CACH;gBACH,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CACF;AAGI,MAAMuS,cAAc,IAAatZ,IAA4B,IAA4B;IAC9F,MAAMuZ,SAAS,OAAGnd,IAAI,CAACgH,uKAAAA,AAAe,4JAAChH,IAAI,CAACod,sBAAwB,sJAAEne,MAAM,CAAC4c,AAAI,AAAJA,EAAKtb,UAAU,CAAC8c,mKAAW,CAAC,CAAC;IAC1G,OAAOF,SAAS,CAACvZ,IAAI,CAAC;AACxB,CAAC;AAED,cAAA,GACA,MAAM0Z,eAAe,GAAA,WAAA,GAAGza,MAAM,CAACC,GAAG,CAAC,+BAA+B,CAAC;AAG5D,MAAMya,MAAM,GAAA,WAAA,IAWfhf,4JAAAA,AAAI,GACLwd,IAAI,0JAAK5c,SAAS,CAACqe,GAAAA,AAAU,EAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,+JAAC/b,IAAI,CAACgc,MAAAA,AAAQ,EAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAClE,CAAU0B,QAAqB,EAAEC,SAA4D,EAAEvD,OAG9F,wKACCta,WAAW,CAAC8d,EAAAA,AAAW,EACrBxD,OAAO,EAAEta,WAAW,EACpB,kKAAMG,IAAI,CAAC6K,KAAAA,AAAO,EAAC,IAAM+S,UAAU,CAACH,QAAQ,CAAC5a,MAAM,CAACgb,QAAQ,CAAC,EAAE,EAAE,CAAC,EAAEH,SAAS,CAAC,CAAC,EAC/E,iKACE1d,IAAI,CAAC8d,UAAW,AAAXA,EACH3W,OAAO,CACLsW,QAAQ,EACR,CAACpB,CAAC,EAAEjL,CAAC,iKAAKpR,IAAI,CAAC+d,CAAG,AAAHA,EAAIL,SAAS,CAACrB,CAAC,EAAEjL,CAAC,CAAC,EAAE;gBAAE4M,MAAM,EAAEA,CAAA,iKAAMhe,IAAI,CAACgV,EAAI,AAAJA,EAAKsI,eAAe,CAAC;gBAAEW,OAAO,EAAEA,CAAA,6JAAMje,IAAI,CAACuE,EAAAA;YAAI,CAAE,CAAC,EAC3G4V,OAAO,CACR,EACD;YACE+D,SAAS,GAAGjK,CAAC,GAAKA,CAAC,KAAKqJ,eAAe,gKAAGtd,IAAI,CAACyL,MAAO,AAAPA,EAAQ,IAAI,CAAC,iKAAGzL,IAAI,CAACgV,EAAAA,AAAI,EAACf,CAAC,CAAC;YAC3EkK,SAAS,EAAEA,CAAA,iKAAMne,IAAI,CAACyL,KAAAA,AAAO,EAAC,KAAK;SACpC,CACF,CACJ,CACJ;AAED,MAAMmS,UAAU,GAAGA,CACjBC,QAAqB,EACrBO,KAAa,EACbxT,CAAoD,KACpB;IAChC,MAAMlG,IAAI,GAAGmZ,QAAQ,CAACnZ,IAAI,EAAE;IAC5B,IAAIA,IAAI,CAACC,IAAI,EAAE;QACb,qKAAO3E,IAAI,CAACyL,KAAAA,AAAO,EAAC,KAAK,CAAC;IAC5B;IACA,OAAOhN,6JAAAA,AAAI,gKAACuB,IAAI,CAAC4F,KAAAA,AAAO,EACtBgF,CAAC,CAAClG,IAAI,CAACtB,KAAK,EAAEgb,KAAK,CAAC,GACnBC,CAAC,GAAKA,CAAC,iKAAGre,IAAI,CAACyL,KAAAA,AAAO,EAAC4S,CAAC,CAAC,GAAGT,UAAU,CAACC,QAAQ,EAAEO,KAAK,GAAG,CAAC,EAAExT,CAAC,CAAC,CAChE,CAAC;AACJ,CAAC;AAGM,MAAMyC,MAAM,GAAA,WAAA,yJAAG9O,OAAAA,AAAI,GAiBvBwd,IAAI,0JAAK5c,SAAS,CAACqe,GAAAA,AAAU,EAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,+JAAC/b,IAAI,CAACgc,MAAAA,AAAQ,EAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAClE,CAAU0B,QAAqB,EAAEC,SAAqE,EAAEvD,OAKvG,KAAI;IACH,MAAMmE,UAAU,GAAGnE,OAAO,EAAEoE,MAAM,GAAG,CAAClC,CAAI,EAAEjL,CAAS,iKAAKpR,IAAI,CAACsG,CAAAA,AAAG,EAACoX,SAAS,CAACrB,CAAC,EAAEjL,CAAC,CAAC,kJAAEvT,OAAO,AAAI,CAAH2gB,AAAI,GAAGd,SAAS;IAC5G,4KAAO7d,WAAW,CAAC8d,EAAAA,AAAW,EAC5BxD,OAAO,EAAEta,WAAW,EACpB,kKACEG,IAAI,CAAC6K,KAAAA,AAAO,EAAC,QACXjN,EAAE,CAAC6gB,2JAAAA,AAAY,EAAChB,QAAQ,CAAC,CAACiB,WAAW,CACnC,CAAC5U,MAAM,EAAEuS,CAAC,EAAEjL,CAAC,GACXpR,IAAI,CAAC2e,mKAAAA,AAAO,EACV7U,MAAM,gKACN9J,IAAI,CAAC6K,KAAO,AAAPA,EAAQ,IAAMyT,UAAU,CAACjC,CAAC,EAAEjL,CAAC,CAAC,CAAC,EACpC,CAACwN,IAAI,EAAEP,CAAC,GAAKA,CAAC,GAAG;wBAAChC,CAAC,EAAE;2BAAGuC,IAAI;qBAAC,GAAGA,IAAI,CACrC,gKACH5e,IAAI,CAACkL,EAAI,AAAJA,EAAK,IAAM,IAAIlD,KAAK,EAAK,CAAkC,CACjE,CACF,EACH,kKACEhI,IAAI,CAACsG,CAAAA,AAAG,EACNa,OAAO,CACLsW,QAAQ,EACR,CAACpB,CAAC,EAAEjL,CAAC,GAAKpR,IAAI,CAACsG,+JAAG,AAAHA,EAAIgY,UAAU,CAACjC,CAAC,EAAEjL,CAAC,CAAC,GAAGiN,CAAC,GAAMA,CAAC,uJAAGpf,MAAM,CAAC4c,AAAI,EAACQ,CAAC,CAAC,GAAGpd,MAAM,CAAC0W,oJAAAA,AAAI,EAAG,CAAC,GACjFwE,OAAO,CACR,iJACDvc,EAAE,CAACihB,QAAQ,CACZ,CACJ;AACH,CAAC,CACF;AAED,UAAA;AAEA,MAAMC,eAAe,IACnBC,KAA4F,IACA;IAC5F,IAAI/W,KAAK,CAAC+R,OAAO,CAACgF,KAAK,CAAC,0JAAI5f,SAAS,CAACqe,IAAAA,AAAU,EAACuB,KAAK,CAAC,EAAE;QACvD,OAAO;YAACA,KAAK;gKAAE9f,MAAM,CAAC0W,AAAI,EAAE;SAAC;IAC/B;IACA,MAAMqJ,IAAI,GAAGxD,MAAM,CAACwD,IAAI,CAACD,KAAK,CAAC;IAC/B,MAAMpQ,IAAI,GAAGqQ,IAAI,CAAC9P,MAAM;IACxB,OAAO;QACL8P,IAAI,CAAC1Y,GAAG,EAAE2Y,CAAC,GAAKF,KAAK,CAACE,CAAC,CAAC,CAAC;4JACzBhgB,MAAM,CAAC4c,AAAI,GAAE3L,MAA0B,IAAI;YACzC,MAAMgP,GAAG,GAAG,CAAA,CAAE;YACd,IAAK,IAAI9N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,IAAI,EAAEyC,CAAC,EAAE,CAAE;;gBAC3B8N,GAAW,CAACF,IAAI,CAAC5N,CAAC,CAAC,CAAC,GAAGlB,MAAM,CAACkB,CAAC,CAAC;YACpC;YACA,OAAO8N,GAAG;QACZ,CAAC,CAAC;KACH;AACH,CAAC;AAED,MAAMC,WAAW,GAAGA,CAClBC,OAA+C,EAC/CC,SAAyD,EACzDlF,OAMC,KACC;IACF,MAAMmF,aAAa,GAA0E,EAAE;IAC/F,KAAK,MAAMxV,MAAM,IAAIsV,OAAO,CAAE;QAC5BE,aAAa,CAAC5Y,IAAI,+JAAC1G,IAAI,CAACuf,IAAAA,AAAM,EAACzV,MAAM,CAAC,CAAC;IACzC;IACA,qKAAO9J,IAAI,CAAC4F,KAAAA,AAAO,EACjBuB,OAAO,CAACmY,aAAa,oJAAE9gB,WAAQ,EAAE;QAC/BqB,WAAW,EAAEsa,OAAO,EAAEta,WAAW;QACjC2f,QAAQ,EAAErF,OAAO,EAAEqF,QAAQ;QAC3BC,oBAAoB,EAAEtF,OAAO,EAAEsF;KAChC,CAAC,GACDC,OAAO,IAAI;QACV,MAAM/J,IAAI,uJAAG1W,MAAM,CAAC0W,AAAI,EAAE;QAC1B,MAAMhH,IAAI,GAAG+Q,OAAO,CAACxQ,MAAM;QAC3B,MAAMyQ,MAAM,GAAmB,IAAI3X,KAAK,CAAC2G,IAAI,CAAC;QAC9C,MAAMiR,SAAS,GAAmB,IAAI5X,KAAK,CAAC2G,IAAI,CAAC;QACjD,IAAIkR,OAAO,GAAG,KAAK;QACnB,IAAK,IAAIzO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,IAAI,EAAEyC,CAAC,EAAE,CAAE;YAC7B,MAAMmO,MAAM,GAAGG,OAAO,CAACtO,CAAC,CAAoC;YAC5D,IAAImO,MAAM,CAAC7P,IAAI,KAAK,MAAM,EAAE;gBAC1BiQ,MAAM,CAACvO,CAAC,CAAC,uJAAGnS,MAAM,CAAC4c,AAAI,EAAC0D,MAAM,CAACtK,IAAI,CAAC;gBACpC4K,OAAO,GAAG,IAAI;YAChB,CAAC,MAAM;gBACLD,SAAS,CAACxO,CAAC,CAAC,GAAGmO,MAAM,CAAClK,KAAK;gBAC3BsK,MAAM,CAACvO,CAAC,CAAC,GAAGuE,IAAI;YAClB;QACF;QACA,IAAIkK,OAAO,EAAE;YACX,OAAOR,SAAS,CAAC3P,IAAI,KAAK,MAAM,iKAC9B1P,IAAI,CAACgV,EAAAA,AAAI,EAACqK,SAAS,CAACjc,KAAK,CAACuc,MAAM,CAAC,CAAC,iKAClC3f,IAAI,CAACgV,EAAAA,AAAI,EAAC2K,MAAM,CAAC;QACrB,CAAC,MAAM,IAAIxF,OAAO,EAAE2F,OAAO,EAAE;YAC3B,iKAAO9f,IAAI,CAACuE,EAAI;QAClB;QACA,OAAO8a,SAAS,CAAC3P,IAAI,KAAK,MAAM,iKAC9B1P,IAAI,CAACyL,KAAAA,AAAO,EAAC4T,SAAS,CAACjc,KAAK,CAACwc,SAAS,CAAC,CAAC,gKACxC5f,IAAI,CAACyL,MAAAA,AAAO,EAACmU,SAAS,CAAC;IAC3B,CAAC,CACF;AACH,CAAC;AAED,MAAMG,SAAS,GAAGA,CAChBX,OAA+C,EAC/CC,SAAyD,EACzDlF,OAMC,KACC;IACF,MAAMmF,aAAa,GAA0E,EAAE;IAC/F,KAAK,MAAMxV,MAAM,IAAIsV,OAAO,CAAE;QAC5BE,aAAa,CAAC5Y,IAAI,+JAAC1G,IAAI,CAACuf,IAAAA,AAAM,EAACzV,MAAM,CAAC,CAAC;IACzC;IAEA,IAAIqQ,OAAO,EAAE2F,OAAO,EAAE;QACpB,OAAO3Y,OAAO,CAACmY,aAAa,oJAAE9gB,WAAQ,EAAE;YACtCqB,WAAW,EAAEsa,OAAO,EAAEta,WAAW;YACjC2f,QAAQ,EAAErF,OAAO,EAAEqF,QAAQ;YAC3BM,OAAO,EAAE,IAAI;YACbL,oBAAoB,EAAEtF,OAAO,EAAEsF;SAChC,CAAC;IACJ;IAEA,oKAAOzf,IAAI,CAACsG,EAAAA,AAAG,EACba,OAAO,CAACmY,aAAa,oJAAE9gB,WAAQ,EAAE;QAC/BqB,WAAW,EAAEsa,OAAO,EAAEta,WAAW;QACjC2f,QAAQ,EAAErF,OAAO,EAAEqF,QAAQ;QAC3BC,oBAAoB,EAAEtF,OAAO,EAAEsF;KAChC,CAAC,GACDC,OAAO,GACNL,SAAS,CAAC3P,IAAI,KAAK,MAAM,GACvB2P,SAAS,CAACjc,KAAK,CAACsc,OAAO,CAAC,GACxBA,OAAO,CACZ;AACH,CAAC;AAGM,MAAMM,GAAG,GAAGA,CAUjBC,GAAQ,EACR9F,OAAW,KACkB;IAC7B,MAAM,CAACiF,OAAO,EAAEC,SAAS,CAAC,GAAGP,eAAe,CAACmB,GAAG,CAAC;IAEjD,IAAI9F,OAAO,EAAE+F,IAAI,KAAK,UAAU,EAAE;QAChC,OAAOf,WAAW,CAACC,OAAO,EAAEC,SAAS,EAAElF,OAAO,CAAQ;IACxD,CAAC,MAAM,IAAIA,OAAO,EAAE+F,IAAI,KAAK,QAAQ,EAAE;QACrC,OAAOH,SAAS,CAACX,OAAO,EAAEC,SAAS,EAAElF,OAAO,CAAQ;IACtD;IAEA,OAAOA,OAAO,EAAE2F,OAAO,KAAK,IAAI,IAAIT,SAAS,CAAC3P,IAAI,KAAK,MAAM,GACzD1P,IAAI,CAACsG,+JAAAA,AAAG,EACRa,OAAO,CAACiY,OAAO,oJAAE5gB,WAAQ,EAAE2b,OAAc,CAAC,EAC1CkF,SAAS,CAACjc,KAAK,CACT,GACN+D,OAAO,CAACiY,OAAO,oJAAE5gB,WAAQ,EAAE2b,OAAc,CAAQ;AACvD,CAAC;AAGM,MAAMgG,OAAO,IAQlBhG,OAAW,IAEX8F,GAAQ,GACsBD,GAAG,CAACC,GAAG,EAAE9F,OAAO,CAAC;AAG1C,MAAMiG,YAAY,GAAGA,CAC1B3C,QAAuB,EACvBtD,OAIC,iKAEDna,IAAI,CAACsG,CAAG,AAAHA,EACH0Z,GAAG,oJAACpiB,EAAE,CAAC6gB,YAAAA,AAAY,EAAChB,QAAQ,CAAC,CAACnX,GAAG,2JAACtG,IAAI,CAACwL,EAAI,CAAC,EAAE2O,OAAO,CAAC,EACtDvc,EAAE,CAACyiB,4JAAAA,AAAS,GAAE7U,IAAI,iKAAKxL,IAAI,CAACsgB,WAAAA,AAAa,EAAC9U,IAAI,CAAC,uJAAGvM,MAAM,CAAC4c,AAAI,EAACrQ,IAAI,CAACnH,qBAAqB,CAAC,uJAAGpF,MAAM,CAAC0W,AAAI,AAAJA,EAAM,CAAC,CAC3G;AAGI,MAAM4K,SAAS,GAAA,WAAA,yJAAGhiB,OAAAA,AAAI,EAG3B,CAAC,EAAE,CAACqF,IAAI,EAAE4c,CAAC,GAAKxY,KAAK,CAACuC,IAAI,CAAC;QAAE2E,MAAM,EAAEsR;IAAC,CAAE,EAAE,IAAM5c,IAAI,CAAC,CAAC;AAGjD,MAAM6c,eAAe,GAAA,WAAA,yJAuCxBliB,OAAAA,AAAI,GACLwd,IAAI,iKAAK/b,IAAI,CAACgc,MAAQ,AAARA,EAASD,IAAI,CAAC,CAAC,CAAC,CAAC,EAChC,CAACnY,IAAI,EAAE4c,CAAC,EAAErG,OAAO,GAAK6F,GAAG,CAACO,SAAS,CAAC3c,IAAI,EAAE4c,CAAC,CAAC,EAAErG,OAAO,CAAC,CACvD;AAGM,MAAMhT,OAAO,GAAA,WAAA,yJAmDhB5I,OAAAA,AAAI,GAAEwd,IAAI,0JAAK5c,SAAS,CAACqe,GAAAA,AAAU,EAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAChDnY,IAAiB,EACjBgH,CAA8C,EAC9CuP,OAKC,iKAEDna,IAAI,CAAC6L,cAAgB,AAAhBA,GAAkC6U,CAAC,IAAI;QAC1C,MAAMC,wBAAwB,GAAGxG,OAAO,EAAEqF,QAAQ,KAAK,IAAI,IACxDrF,OAAO,EAAEqF,QAAQ,KAAK,SAAS,IAAIkB,CAAC,CAACpX,WAAW,2JAACtJ,IAAI,CAAC4gB,oBAAsB,CAAE;QAEjF,IAAIzG,OAAO,EAAE2F,OAAO,EAAE;YACpB,2KAAOjgB,SAAYghB,AAAK,EAAN,AAChB1G,CADiB0G,MACV,CAAChhB,WAAW,EACnB,IACEihB,sBAAsB,4JAAC5iB,aAA4B,EAAEic,EAAb,CAAC9T,IAAmB,EAAEoZ,oBAAoB,CAAC,EAAE5I,OAAO,GAC1F8J,wBAAwB,GACpBza,wBAAwB,CAACtC,IAAI,EAAE,CAACyY,CAAC,EAAEjL,CAAC,GAAKyF,OAAO,CAACjM,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,iKAC1EpR,IAAI,CAAC+F,sBAAAA,AAAwB,EAACnC,IAAI,EAAE,CAACyY,CAAC,EAAEjL,CAAC,GAAKyF,OAAO,CAACjM,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,CAAC,CAAC,CACpE,EACH,IACE0P,sBAAsB,4JAAC5iB,WAA0B,EAAEic,IAAX,CAAC4G,EAAiB,EAAEtB,oBAAoB,CAAC,EAAE5I,OAAO,GACxF3Q,wBAAwB,CAACtC,IAAI,EAAE,CAACyY,CAAC,EAAEjL,CAAC,GAAKyF,OAAO,CAACjM,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,CAAC,EAAEuP,wBAAwB,EAAE,KAAK,CAAC,CAC5F,EACFH,CAAC,IACAM,sBAAsB,gKAAC5iB,YAAkB8iB,AAAS,EAACR,CAAC,CAAC,CAAb,CAACQ,AAAc7G,OAAO,EAAEsF,oBAAoB,CAAC,EAAE5I,OAAO,GAC5F3Q,wBAAwB,CAACtC,IAAI,EAAE,CAACyY,CAAC,EAAEjL,CAAC,GAAKyF,OAAO,CAACjM,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,CAAC,EAAEuP,wBAAwB,EAAE,KAAK,EAAEH,CAAC,CAAC,CAC/F,CACJ;QACH;QAEA,4KAAO3gB,QAAYghB,AAAK,EACtB1G,CADgB,CAAC0G,KACV,EAAEhhB,WAAW,EACpB,IACEihB,sBAAsB,4JAAC5iB,aAA4B,EAAEic,EAAb,CAAC9T,IAAmB,EAAEoZ,oBAAoB,CAAC,EAAE5I,OAAO,GAC1F8J,wBAAwB,GACpBM,WAAW,CAACrd,IAAI,EAAE,CAAC,EAAE,CAACyY,CAAC,EAAEjL,CAAC,GAAKyF,OAAO,CAACjM,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,iKACtDpR,IAAI,CAACkhB,eAAAA,AAAiB,EAACtd,IAAI,EAAE,CAACyY,CAAC,EAAEjL,CAAC,GAAKyF,OAAO,CAACjM,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,CAAC,CAAC,CAC7D,EACH,IACE0P,sBAAsB,4JAAC5iB,WAA0B,EAAEic,IAAX,CAAC4G,EAAiB,EAAEtB,oBAAoB,CAAC,EAAE5I,OAAO,GACxFsK,mBAAmB,CAACvd,IAAI,EAAE,CAACyY,CAAC,EAAEjL,CAAC,GAAKyF,OAAO,CAACjM,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,CAAC,EAAEuP,wBAAwB,CAAC,CAChF,GACFH,CAAC,GACAM,sBAAsB,gKAAC5iB,YAAkB8iB,AAAS,EAACR,CAAC,CAAC,CAAb,CAAerG,AAAd6G,OAAqB,EAAEvB,oBAAoB,CAAC,CAAE5I,OAAO,IAC5FoK,WAAW,CAACrd,IAAI,EAAE4c,CAAC,EAAE,CAACnE,CAAC,EAAEjL,CAAC,GAAKyF,OAAO,CAACjM,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,CAAC,EAAEuP,wBAAwB,CAAC,CAC3E,CACJ;IACH,CAAC,CAAC,CAAC;AAGE,MAAMQ,mBAAmB,GAAGA,CACjCvd,IAAiB,EACjBgH,CAA8C,EAC9C4U,QAAiB,iKAEjBxf,IAAI,CAAC6K,KAAAA,AAAO,EAAC,MAAK;QAChB,MAAMsQ,EAAE,sJAAGvd,EAAE,CAAC6gB,YAAY,AAAZA,EAAa7a,IAAI,CAAC;QAChC,MAAMwd,KAAK,GAAG,IAAIpZ,KAAK,CAAImT,EAAE,CAACjM,MAAM,CAAC;QACrC,MAAMmS,EAAE,GAAGA,CAAChF,CAAI,EAAEjL,CAAS,iKAAKpR,IAAI,CAAC4F,KAAAA,AAAO,EAACgF,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,GAAGiN,CAAC,IAAKre,IAAI,CAACkL,+JAAAA,AAAI,EAAC,IAAMkW,KAAK,CAAChQ,CAAC,CAAC,GAAGiN,CAAC,CAAC,CAAC;QAC3F,WAAOre,IAAI,CAAC8a,gKAAAA,AAAQ,EAAC5U,wBAAwB,CAACiV,EAAE,EAAEkG,EAAE,EAAE7B,QAAQ,EAAE,KAAK,CAAC,gKAAExf,IAAI,CAACyL,KAAAA,AAAO,EAAC2V,KAAK,CAAC,CAAC;IAC9F,CAAC,CAAC;AAGG,MAAMlb,wBAAwB,GAAGA,CACtCtC,IAAiB,EACjBgH,CAA8C,EAC9C4U,QAAiB,EACjB8B,UAAmB,EACnBd,CAAU,iKAEVxgB,IAAI,CAAC4W,iBAAAA,AAAmB,GAAEC,OAAO,OAC/B7W,IAAI,CAACuhB,kKAAAA,AAAU,GAAEC,KAAK,iKACpBxhB,IAAI,CAAC6L,cAAgB,AAAhBA,GAA8B4V,MAAM,IAAI;gBAC3C,IAAIC,KAAK,GAAG1Z,KAAK,CAACuC,IAAI,CAAC3G,IAAI,CAAC,CAAC+d,OAAO,EAAE;gBACtC,IAAIC,MAAM,GAAGF,KAAK,CAACxS,MAAM;gBACzB,IAAI0S,MAAM,KAAK,CAAC,EAAE;oBAChB,iKAAO5hB,IAAI,CAACuE,EAAI;gBAClB;gBACA,IAAIjD,OAAO,GAAG,CAAC;gBACf,IAAI+F,WAAW,GAAG,KAAK;gBACvB,MAAMwa,WAAW,GAAGrB,CAAC,GAAGsB,IAAI,CAACC,GAAG,CAACL,KAAK,CAACxS,MAAM,EAAEsR,CAAC,CAAC,GAAGkB,KAAK,CAACxS,MAAM;gBAChE,MAAM8S,MAAM,GAAG,IAAIvX,GAAG,EAAwD;gBAC9E,MAAMwX,OAAO,GAAG,IAAIja,KAAK,EAAE;gBAC3B,MAAMka,YAAY,GAAGA,CAAA,GACnBF,MAAM,CAAC7a,OAAO,EAAEmD,KAAK,IAAI;wBACvBA,KAAK,CAAChL,gBAAgB,CAAC+P,YAAY,CAAC,MAAK;4BACvC/E,KAAK,CAAC6C,qBAAqB,CAACsU,MAAM,CAAC7X,EAAE,EAAE,CAAC;wBAC1C,CAAC,EAAE,CAAC,CAAC;oBACP,CAAC,CAAC;gBACJ,MAAMuY,UAAU,GAAG,IAAIna,KAAK,EAAwD;gBACpF,MAAMoa,SAAS,GAAG,IAAIpa,KAAK,EAAwD;gBACnF,MAAMqa,QAAQ,GAAG,IAAIra,KAAK,EAAgB;gBAC1C,MAAMsa,YAAY,GAAGA,CAAA,KAAK;oBACxB,MAAMC,KAAK,GAA6BN,OAAO,CAC5C5U,MAAM,CAAC,CAAC,EAAE7B,IAAAA,EAAM,GAAKA,IAAI,CAACkE,IAAI,KAAK,SAAS,CAAC,CAC7C8S,IAAI,CAAC,CAACnG,CAAC,EAAEgC,CAAC,GAAKhC,CAAC,CAAC+B,KAAK,GAAGC,CAAC,CAACD,KAAK,GAAG,CAAC,CAAC,GAAG/B,CAAC,CAAC+B,KAAK,KAAKC,CAAC,CAACD,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CACpE9X,GAAG,CAAC,CAAC,EAAEkF,IAAAA,EAAM,GAAKA,IAAI,CAAC;oBAC1B,IAAI+W,KAAK,CAACrT,MAAM,KAAK,CAAC,EAAE;wBACtBqT,KAAK,CAAC7b,IAAI,CAAC1G,IAAI,CAACqT,gKAAQ,CAAC;oBAC3B;oBACA,OAAOkP,KAAK;gBACd,CAAC;gBACD,MAAME,QAAQ,GAAGA,CAAU1P,GAA2B,EAAE2P,oBAAoB,GAAG,KAAK,KAAI;oBACtF,MAAMC,QAAQ,iKAAG3iB,IAAI,CAACmc,aAAAA,AAAe,EAACqF,KAAK,CAACzO,GAAG,CAAC,CAAC;oBACjD,MAAMzI,KAAK,GAAGsY,mBAAmB,CAC/BD,QAAQ,EACRlB,MAAM,EACNA,MAAM,CAAC3d,mBAAmB,kKAC1BvD,UAAU,CAAC8c,GAAW,CACvB;oBACDoE,MAAM,CAACniB,gBAAgB,CAAC+P,YAAY,CAAC,MAAK;wBACxC,IAAIqT,oBAAoB,EAAE;4BACxBpY,KAAK,CAAC6C,qBAAqB,CAACsU,MAAM,CAAC7X,EAAE,EAAE,CAAC;wBAC1C;wBACAU,KAAK,CAACT,MAAM,CAAC8Y,QAAQ,CAAC;oBACxB,CAAC,EAAE,CAAC,CAAC;oBACL,OAAOrY,KAAK;gBACd,CAAC;gBACD,MAAMuY,iBAAiB,GAAGA,CAAA,KAAK;oBAC7B,IAAI,CAACvB,UAAU,EAAE;wBACfM,MAAM,IAAIF,KAAK,CAACxS,MAAM;wBACtBwS,KAAK,GAAG,EAAE;oBACZ;oBACAra,WAAW,GAAG,IAAI;oBAClB6a,YAAY,EAAE;gBAChB,CAAC;gBACD,MAAMY,UAAU,GAAGtD,QAAQ,GAAGxf,IAAI,CAACuQ,4JAAI,6JAAGvQ,IAAI,CAACwL,EAAI;gBACnD,MAAMuX,eAAe,GAAGN,QAAQ,+JAC9BziB,IAAI,CAACsL,GAAAA,AAAK,GAAiBzB,MAAM,IAAI;oBACnC,MAAMmZ,UAAU,GAAGA,CAAO9D,GAA2C,EAAEd,KAAa,KAAI;wBACtF,IAAIc,GAAG,CAAChM,GAAG,KAAK,SAAS,EAAE;4BACzBmP,QAAQ,CAAC3b,IAAI,CAACwY,GAAmB,CAAC;wBACpC,CAAC,MAAM;4BACL+C,OAAO,CAACvb,IAAI,CAAC;gCAAE0X,KAAK;gCAAE5S,IAAI,EAAE0T;4BAAG,CAAE,CAAC;4BAClC,IAAIA,GAAG,CAAChM,GAAG,KAAK,SAAS,IAAI,CAAC7L,WAAW,EAAE;gCACzCwb,iBAAiB,EAAE;4BACrB;wBACF;oBACF,CAAC;oBACD,MAAMne,IAAI,GAAGA,CAAA,KAAK;wBAChB,IAAIgd,KAAK,CAACxS,MAAM,GAAG,CAAC,EAAE;4BACpB,MAAMmN,CAAC,GAAGqF,KAAK,CAAClN,GAAG,EAAG;4BACtB,IAAI4J,KAAK,GAAG9c,OAAO,EAAE;4BACrB,MAAM2hB,iBAAiB,GAAGA,CAAA,KAAK;gCAC7B,MAAM5G,CAAC,GAAGqF,KAAK,CAAClN,GAAG,EAAG;gCACtB4J,KAAK,GAAG9c,OAAO,EAAE;gCACjB,qKAAOtB,IAAI,CAAC4F,KAAAA,AAAO,gKAAC5F,IAAI,CAAC6F,MAAQ,AAARA,EAAU,GAAE,IACnC7F,IAAI,CAAC4F,mKAAO,AAAPA,EACHkd,UAAU,CAACjM,OAAO,CAACjM,CAAC,CAACyR,CAAC,EAAE+B,KAAK,CAAC,CAAC,CAAC,EAChC8E,KAAK,CACN,CAAC;4BACN,CAAC;4BACD,MAAMA,KAAK,GACThE,GAA2C,IACwB;gCACnE,IAAIwC,KAAK,CAACxS,MAAM,GAAG,CAAC,EAAE;oCACpB8T,UAAU,CAAC9D,GAAG,EAAEd,KAAK,CAAC;oCACtB,IAAIsD,KAAK,CAACxS,MAAM,GAAG,CAAC,EAAE;wCACpB,OAAO+T,iBAAiB,EAAE;oCAC5B;gCACF;gCACA,qKAAOjjB,IAAI,CAACyL,KAAAA,AAAO,EAACyT,GAAG,CAAC;4BAC1B,CAAC;4BACD,MAAMiE,IAAI,iKAAGnjB,IAAI,CAAC4F,KAAAA,AAAO,EACvBkd,UAAU,CAACjM,OAAO,CAACjM,CAAC,CAACyR,CAAC,EAAE+B,KAAK,CAAC,CAAC,CAAC,EAChC8E,KAAK,CACN;4BACD,MAAM5Y,KAAK,GAAGmY,QAAQ,CAACU,IAAI,CAAC;4BAC5BhB,UAAU,CAACzb,IAAI,CAAC4D,KAAK,CAAC;4BACtB0X,MAAM,CAACzT,GAAG,CAACjE,KAAK,CAAC;4BACjB,IAAIjD,WAAW,EAAE;gCACfiD,KAAK,CAAChL,gBAAgB,CAAC+P,YAAY,CAAC,MAAK;oCACvC/E,KAAK,CAAC6C,qBAAqB,CAACsU,MAAM,CAAC7X,EAAE,EAAE,CAAC;gCAC1C,CAAC,EAAE,CAAC,CAAC;4BACP;4BACAU,KAAK,CAACoB,WAAW,EAAE0X,OAAO,IAAI;gCAC5B,IAAI5X,IAAwC;gCAC5C,IAAI4X,OAAO,CAAClQ,GAAG,KAAK,SAAS,EAAE;oCAC7B1H,IAAI,GAAG4X,OAAO;gCAChB,CAAC,MAAM;oCACL5X,IAAI,GAAG4X,OAAO,CAAC/e,qBAA4B;gCAC7C;gCACA+d,SAAS,CAAC1b,IAAI,CAAC4D,KAAK,CAAC;gCACrB0X,MAAM,CAACvT,MAAM,CAACnE,KAAK,CAAC;gCACpB0Y,UAAU,CAACxX,IAAI,EAAE4S,KAAK,CAAC;gCACvB,IAAI6D,OAAO,CAAC/S,MAAM,KAAK0S,MAAM,EAAE;oCAC7B/X,MAAM,EAAC7J,IAAI,CAACyL,kKAAO,AAAPA,sJAAQxM,MAAM,CAACokB,KAAAA,AAAS,gKAClCrjB,IAAI,CAACsjB,YAAAA,AAAc,EAAChB,YAAY,EAAE,EAAE;wCAAEvB,QAAQ,EAAE;oCAAI,CAAE,CAAC,EACvD,IAAM/gB,IAAI,CAACqT,gKAAQ,CACpB,CAAC,CAAC;gCACL,CAAC,MAAM,IAAIgP,QAAQ,CAACnT,MAAM,GAAG+S,OAAO,CAAC/S,MAAM,KAAK0S,MAAM,EAAE;oCACtD,MAAMW,KAAK,GAAGD,YAAY,EAAE;oCAC5B,MAAMiB,QAAQ,GAAGlB,QAAQ,CAAC/b,GAAG,EAAEoQ,OAAO,GAAKA,OAAO,CAACrS,qBAAqB,CAAC,CAACmf,MAAM,sKAAC/jB,MAAiB,CAAC,MAAL,CAACgkB;oCAC/F5Z,MAAM,+JAAC7J,IAAI,CAACyL,KAAO,AAAPA,gKAAQzL,IAAI,CAAC0W,KAAAA,AAAO,EAC9B6M,QAAQ,EACRrd,wBAAwB,CACtB;4LACEjH,MAAM,CAACokB,KAAAA,AAAS,gKACdrjB,IAAI,CAACsjB,YAAAA,AAAc,EAACf,KAAK,EAAE;4CAAExB,QAAQ,EAAE;wCAAI,CAAE,CAAC,EAC9C,8JAAM/gB,IAAI,CAACqT,MAAQ,CACpB,EACD;2CAAGgP,QAAQ,CAAC/b,GAAG,CAAEoQ,OAAO,IAAKA,OAAO,CAACrT,qBAAqB,CAAC;qCAC5D,EACA+N,CAAC,IAAKA,CAAC,EACRoO,QAAQ,EACR,IAAI,EACJgB,CAAC,CACF,CACF,CAAC,CAAC;gCACL,CAAC,MAAM;oCACL9b,IAAI,EAAE;gCACR;4BACF,CAAC,CAAC;wBACJ;oBACF,CAAC;oBACD,IAAK,IAAI0M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyQ,WAAW,EAAEzQ,CAAC,EAAE,CAAE;wBACpC1M,IAAI,EAAE;oBACR;gBACF,CAAC,CAAC,CACH;gBACD,qKAAO1E,IAAI,CAACoQ,IAAAA,AAAM,gKAChBpQ,IAAI,CAAC0jB,IAAAA,AAAM,gKACT1jB,IAAI,CAACgG,KAAAA,AAAO,EAAC6Q,OAAO,gKAACzW,OAAcuJ,AAAI,EAACoZ,IAAN,CAACpZ,UAAoB,CAAC,CAAC,CAAC,EAC1D3J,IAAI,CAAC2jB,qKAAAA,AAAS,EAAC;oBACbzF,SAAS,GAAGhZ,KAAK,IAAI;wBACnB2d,iBAAiB,EAAE;wBACnB,MAAMjB,MAAM,GAAGS,QAAQ,CAACnT,MAAM,GAAG,CAAC;wBAClC,MAAMrP,WAAW,GAAGiiB,IAAI,CAACC,GAAG,CAAC,OAAOvB,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAG6B,QAAQ,CAACnT,MAAM,EAAEmT,QAAQ,CAACnT,MAAM,CAAC;wBAC1F,MAAM0U,KAAK,GAAG5b,KAAK,CAACuC,IAAI,CAAC8X,QAAQ,CAAC;wBAClC,qKAAOriB,IAAI,CAACsL,GAAAA,AAAK,GAAYC,EAAE,IAAI;4BACjC,MAAMgX,KAAK,GAA+B,EAAE;4BAC5C,IAAItgB,KAAK,GAAG,CAAC;4BACb,IAAImc,KAAK,GAAG,CAAC;4BACb,MAAM/G,KAAK,GAAGA,CAAC+G,KAAa,EAAEyF,OAAgB,GAAMrY,IAAyB,IAAI;oCAC/E+W,KAAK,CAACnE,KAAK,CAAC,GAAG5S,IAAI;oCACnBvJ,KAAK,EAAE;oCACP,IAAIA,KAAK,KAAK2f,MAAM,EAAE;wCACpBrW,EAAE,+JAACvL,IAAI,CAACwD,SAAAA,AAAW,gKAACxD,IAAI,CAACkE,WAAa,AAAbA,EAAcgB,KAAK,CAAC,CAAC,CAAC;oCACjD;oCACA,IAAI0e,KAAK,CAAC1U,MAAM,GAAG,CAAC,IAAI2U,OAAO,EAAE;wCAC/Bnf,IAAI,EAAE;oCACR;gCACF,CAAC;4BACD,MAAMA,IAAI,GAAGA,CAAA,KAAK;gCAChB+d,QAAQ,CAACmB,KAAK,CAACpP,GAAG,EAAG,EAAE,IAAI,CAAC,CAAC9I,WAAW,CAAC2L,KAAK,CAAC+G,KAAK,EAAE,IAAI,CAAC,CAAC;gCAC5DA,KAAK,EAAE;4BACT,CAAC;4BACD2E,eAAe,CAACrX,WAAW,CAAC2L,KAAK,CAAC+G,KAAK,EAAE,KAAK,CAAC,CAAC;4BAChDA,KAAK,EAAE;4BACP,IAAK,IAAIhN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvR,WAAW,EAAEuR,CAAC,EAAE,CAAE;gCACpC1M,IAAI,EAAE;4BACR;wBACF,CAAC,CAAQ;oBACX,CAAC;oBACDyZ,SAAS,EAAEA,CAAA,iKAAMne,IAAI,CAACkhB,eAAAA,AAAiB,EAACkB,SAAS,GAAGxX,CAAC,GAAKA,CAAC,CAACgB,UAAU;iBACvE,CAAC,CACH,CACF;YACH,CAAC,CAAC,CACH,CACF;AAGI,MAAMqV,WAAW,GAAGA,CACzBrd,IAAiB,EACjB4c,CAAS,EACT5V,CAA8C,EAC9C4U,QAAiB,iKAEjBxf,IAAI,CAAC6K,KAAAA,AAAO,EAAC,MAAK;QAChB,MAAMsQ,EAAE,IAAGvd,EAAE,CAAC6gB,8JAAAA,AAAY,EAAC7a,IAAI,CAAC;QAChC,MAAMwd,KAAK,GAAG,IAAIpZ,KAAK,CAAImT,EAAE,CAACjM,MAAM,CAAC;QACrC,MAAMmS,EAAE,GAAGA,CAAChF,CAAI,EAAEjL,CAAS,iKAAKpR,IAAI,CAACsG,CAAAA,AAAG,EAACsE,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,EAAGiN,CAAC,IAAK+C,KAAK,CAAChQ,CAAC,CAAC,GAAGiN,CAAC,CAAC;QACtE,qKAAOre,IAAI,CAAC8a,MAAAA,AAAQ,EAAC5U,wBAAwB,CAACiV,EAAE,EAAEkG,EAAE,EAAE7B,QAAQ,EAAE,KAAK,EAAEgB,CAAC,CAAC,gKAAExgB,IAAI,CAACyL,KAAAA,AAAO,EAAC2V,KAAK,CAAC,CAAC;IACjG,CAAC,CAAC;AAGG,MAAM0C,IAAI,IAAalgB,IAA4B,iKACxD5D,IAAI,CAAC6L,cAAAA,AAAgB,EAAC,CAACpH,KAAK,EAAEuF,MAAM,iKAAKhK,IAAI,CAACyL,KAAAA,AAAO,EAACsY,UAAU,CAACngB,IAAI,EAAEa,KAAK,EAAEuF,MAAM,CAAClF,YAAY,CAAC,CAAC,CAAC;AAG/F,MAAMgS,UAAU,IAAalT,IAA4B,GAC9DogB,qBAAqB,CAACpgB,IAAI,kKAAErD,UAAU,CAAC8c,GAAW,CAAC;AAG9C,MAAM4G,oBAAoB,GAAA,WAAA,yJAAG1lB,OAAI,AAAJA,EAQlC,CAAC,EAAE,CAACqF,IAAI,EAAEsgB,OAAO,GACjBJ,IAAI,+JAAC9jB,IAAI,CAACmkB,KAAAA,AAAO,EAACvgB,IAAI,GAAGsB,KAAK,IAAI;QAChC,MAAMqa,MAAM,kKAAG7f,aAAa,CAAC0kB,GAAc,AAAdA,EAAelf,KAAK,CAAC;QAClD,OAAQqa,MAAM,CAAC7P,IAAI;YACjB,KAAK,MAAM;gBACT,OAAOwU,OAAO,CAAC3E,MAAM,CAACtK,IAAI,CAAC;YAC7B,KAAK,OAAO;gBACV,qKAAOjV,IAAI,CAACkU,OAAAA,AAAS,EAACqL,MAAM,CAAClK,KAAK,CAAC;QACvC;IACF,CAAC,CAAC,CAAC,CAAC;AAGC,MAAM0O,UAAU,GAAGA,CACxBja,MAA8B,EAC9BgC,WAAgC,EAChCI,kBAA6C,EAC7CmY,aAAA,GAA8C,IAAI,KAC5B;IACtB,MAAMC,UAAU,GAAGC,oBAAoB,CAACza,MAAM,EAAEgC,WAAW,EAAEI,kBAAkB,EAAEmY,aAAa,CAAC;IAC/FC,UAAU,CAACza,MAAM,CAACC,MAAM,CAAC;IACzB,OAAOwa,UAAU;AACnB,CAAC;AAGM,MAAM1B,mBAAmB,GAAGA,CACjC9Y,MAA8B,EAC9BgC,WAAgC,EAChCI,kBAA6C,EAC7CmY,aAAA,GAA8C,IAAI,KAC5B;IACtB,MAAMC,UAAU,GAAGC,oBAAoB,CAACza,MAAM,EAAEgC,WAAW,EAAEI,kBAAkB,EAAEmY,aAAa,CAAC;IAC/F,OAAOC,UAAU;AACnB,CAAC;AAGM,MAAMC,oBAAoB,GAAGA,CAClCza,MAA8B,EAC9BgC,WAAgC,EAChCI,kBAA6C,EAC7CmY,aAAA,GAA8C,IAAI,KAC5B;IACtB,MAAMG,OAAO,wJAAGrmB,OAAO,CAACiM,KAAAA,AAAU,EAAE;IACpC,MAAM6B,eAAe,GAAGH,WAAW,CAACnB,YAAY,EAAE;IAClD,MAAMwB,cAAc,sKAAG7L,SAAS,AAACmkB,AAAM,CAANA,CAAOxY,eAAe,EAAEuY,OAAO,CAAC;IACjE,MAAMF,UAAU,GAAG,IAAI9c,YAAY,CAAOgd,OAAO,EAAErY,cAAc,EAAED,kBAAkB,CAAC;IACtF,MAAMwY,YAAY,GAAGpkB,SAAS,CAAC+X,wKAAAA,AAAY,EACzClM,cAAc,4JACdnM,IAAI,CAAC8I,YAAkE,CACxE;IACD,MAAM7H,UAAU,GAAGqjB,UAAU,CAAC3b,iBAAiB;IAE/C1H,UAAU,CAAC0jB,OAAO,CAChBD,YAAY,EACZ5a,MAAM,sJACN7K,MAAM,CAAC4c,AAAI,EAAC/P,WAAW,CAAC,EACxBwY,UAAU,CACX;IAEDA,UAAU,CAAC5Y,WAAW,EAAEF,IAAI,GAAKvK,UAAU,CAAC2jB,KAAK,CAACpZ,IAAI,EAAE8Y,UAAU,CAAC,CAAC;IAEpE,MAAMO,WAAW,GAAGR,aAAa,KAAK,IAAI,GAAGA,aAAa,yJAAG5lB,OAAAA,AAAI,EAC/DqN,WAAW,CAACxC,WAAW,CAACtJ,IAAI,CAACod,gLAAwB,CAAC,sJACtDne,MAAM,CAACokB,KAAS,AAATA,EAAU,IAAMvX,WAAW,CAAC3B,KAAK,EAAE,CAAC,CAC5C;IAED0a,WAAW,CAACtW,GAAG,CAACrC,kBAAkB,EAAEoY,UAAU,CAAC;IAE/C,OAAOA,UAAU;AACnB,CAAC;AAED,aAAA,GACA,MAAMN,qBAAqB,GAAGA,CAC5BpgB,IAA4B,EAC5BkhB,aAAoC,iKAEpC9kB,IAAI,CAAC6L,cAAAA,AAAgB,EAAC,CAACC,WAAW,EAAEC,YAAY,iKAC9C/L,IAAI,CAACyL,KAAAA,AAAO,EAACsY,UAAU,CAACngB,IAAI,EAAEkI,WAAW,EAAEC,YAAY,CAACjH,YAAY,EAAEggB,aAAa,CAAC,CAAC,CACtF;AAGI,MAAMC,QAAQ,GAAA,WAAA,yJAAGxmB,OAAAA,AAAI,GAqBzBwd,IAAI,IAAK5c,SAAS,CAAC6lB,yJAAAA,AAAU,EAACjJ,IAAI,CAAC,CAAC,CAAC,CAAC,EACvC,CAAa0B,QAA0C,EAAEwH,IAAO,EAAEra,CAA+B,EAAEuP,OAIlG,uKACCta,WAAW,CAAC8d,GAAAA,AAAW,EACrBxD,OAAO,EAAEta,WAAW,EACpB,uJACEjC,EAAE,CAAC6gB,YAAAA,AAAY,EAAChB,QAAQ,CAAC,CAAC+F,MAAM,CAC9B,CAAC0B,GAAG,EAAE7I,CAAC,EAAEjL,CAAC,GAAKpR,IAAI,CAAC2e,mKAAAA,AAAO,EAACuG,GAAG,EAAE7I,CAAC,EAAE,CAAC6I,GAAG,EAAE7I,CAAC,GAAKzR,CAAC,CAACsa,GAAG,EAAE7I,CAAC,EAAEjL,CAAC,CAAC,CAAC,gKAC7DpR,IAAI,CAACyL,KAAAA,AAAO,EAACwZ,IAAI,CAA2B,CAC7C,EACH,kKACEjlB,IAAI,CAAC4F,KAAAA,AAAO,mJAACxG,GAAG,CAACob,GAAAA,AAAI,EAACyK,IAAI,CAAC,GAAGC,GAAG,iKAC/BllB,IAAI,CAAC4F,KAAAA,AAAO,EACVuB,OAAO,CACLsW,QAAQ,EACR,CAAC3T,MAAM,EAAEsH,CAAC,iKAAKpR,IAAI,CAAC4F,KAAAA,AAAO,EAACkE,MAAM,GAAGuS,CAAC,oJAAKjd,GAAG,CAAC+lB,KAAAA,AAAM,EAACD,GAAG,GAAG7G,CAAC,GAAKzT,CAAC,CAACyT,CAAC,EAAEhC,CAAC,EAAEjL,CAAC,CAAC,CAAC,CAAC,EAC9E+I,OAAO,CACR,EACD,qJAAM/a,GAAG,CAACyO,EAAAA,AAAG,EAACqX,GAAG,CAAC,CACnB,CAAC,CACP,CACJ;AAGM,MAAME,SAAS,GAAA,WAAA,wJAAG7mB,QAAAA,AAAI,GAkB1Bwd,IAAI,0JAAK5c,SAAS,CAACqe,GAAAA,AAAU,EAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC0B,QAAQ,EAAE7S,CAAC,EAAEuP,OAAO,GAC9D1b,6JAAAA,AAAI,EACF0I,OAAO,CAACsW,QAAQ,EAAE,CAACpB,CAAC,EAAEjL,CAAC,iKAAKpR,IAAI,CAACuf,IAAAA,AAAM,EAAC3U,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,CAAC,EAAE+I,OAAO,CAAC,gKAC1Dna,IAAI,CAACsG,CAAAA,AAAG,GAAE+e,KAAK,OAAKrlB,IAAI,CAACslB,oKAAAA,AAAY,EAACD,KAAK,oJAAE7mB,WAAQ,CAAC,CAAC,CACxD,CAAC;AAGG,MAAM+mB,WAAW,GAAA,WAAA,yJAAGhnB,OAAAA,AAAI,GA4C5Bwd,IAAI,0JAAK5c,SAAS,CAACqe,GAAAA,AAAU,EAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,EACvC,CAAa0B,QAAqB,EAAE7S,CAA8C,EAAEuP,OAKnF,iKACCna,IAAI,CAAC4F,KAAAA,AAAO,EACVwf,SAAS,CAAC3H,QAAQ,EAAE7S,CAAC,EAAE;QACrB/K,WAAW,EAAEsa,OAAO,EAAEta,WAAW;QACjC2f,QAAQ,EAAErF,OAAO,EAAEqF,QAAQ;QAC3BC,oBAAoB,EAAEtF,OAAO,EAAEsF;KAChC,CAAC,EACF,CAAC,CAAC+F,EAAE,EAAEC,EAAE,CAAC,sJACP7nB,EAAE,CAAC8nB,eAAAA,AAAe,EAACF,EAAE,CAAC,iKAClBxlB,IAAI,CAACgV,EAAAA,AAAI,EAACwQ,EAAE,CAAC,GACbrL,OAAO,EAAE2F,OAAO,6JAChB9f,IAAI,CAACuE,EAAI,iKACTvE,IAAI,CAACyL,KAAAA,AAAO,EAACga,EAAE,CAAC,CACvB,CACJ;AAGM,MAAME,OAAO,IAMlB3F,GAAqC,IAA4B;IACjE,MAAMpB,IAAI,sJAAG9gB,KAAK,CAAC2gB,SAAAA,AAAY,EAACuB,GAAG,CAAC;IACpC,IAAI,CAACliB,KAAK,CAAC8nB,0JAAAA,AAAU,EAAChH,IAAI,CAAC,EAAE;QAC3B,qKAAO5e,IAAI,CAAC6lB,KAAAA,AAAO,EAAC,IAAM,8JAAI7lB,IAAI,CAAC8lB,sBAAwB,CAAC,CAAA,uCAAA,CAAyC,CAAC,CAAC;IACzG;IACA,MAAMliB,IAAI,sJAAG9F,KAAK,CAACioB,SAAAA,AAAY,EAACnH,IAAI,CAAC;IACrC,MAAMQ,OAAO,sJAAGthB,KAAK,CAACkoB,SAAAA,AAAY,EAACpH,IAAI,CAAC;IACxC,MAAMhT,UAAU,IAAIsT,GAAoC,yJACtDzgB,OAAAA,AAAI,MACF2B,aAAa,CAACwL,0JAAAA,AAAU,EAACsT,GAAG,CAAC,CAAC,CAAC,CAAC,gKAChClf,IAAI,CAACmb,AAAE,EAAC+D,GAAG,CAAC,CAAC,CAAC,CAAC,CAChB;IACH,6JAAOzgB,OAAAA,AAAI,gKACTuB,IAAI,CAACimB,UAAAA,AAAY,EAAsC,iKACvDjmB,IAAI,CAAC4F,KAAAA,AAAO,GAAEjB,IAAI,wJAChBlG,QAAAA,AAAI,mJACFW,GAAG,CAACob,GAAAA,AAAI,EAAC4E,OAAO,CAAClQ,MAAM,CAAC,gKACxBlP,IAAI,CAAC4F,KAAAA,AAAO,GAAEsgB,KAAK,iKACjBlmB,IAAI,CAAC4W,iBAAAA,AAAmB,GAAWC,OAAO,yJACxCpY,OAAAA,AAAI,EACFqlB,IAAI,+JAAC9jB,IAAI,CAACgE,WAAAA,AAAa,EAACJ,IAAI,CAAC,CAAC,+JAC9B5D,IAAI,CAAC4F,MAAAA,AAAO,GAAEugB,IAAI,yJAChB1nB,OAAI,AAAJA,EACE2gB,OAAO,gKACPpf,IAAI,CAACkhB,eAAAA,AAAiB,GAAEpX,MAAM,GAAKga,IAAI,CAAC9jB,IAAI,CAACgE,yKAAAA,AAAa,EAAC8F,MAAM,CAAC,CAAC,CAAC,gKACpE9J,IAAI,CAACsG,CAAAA,AAAG,GAAE0b,MAAM,GAAKlkB,KAAK,CAACsoB,+JAAAA,AAAe,EAACpE,MAAM,CAAC,CAAC,gKACnDhiB,IAAI,CAACsG,CAAAA,AAAG,GAAE+f,IAAI,yJAAK5nB,OAAAA,AAAI,EAAC4nB,IAAI,qJAAEvoB,KAAK,CAACwoB,IAAAA,AAAO,EAACH,IAAI,CAAC,CAA0C,CAAC,gKAC5FnmB,IAAI,CAACoc,CAAAA,AAAG,GAAE4F,MAAM,yJACdvjB,OAAAA,AAAI,EACFujB,MAAM,qJACNpkB,EAAE,CAAC4lB,MAAAA,AAAM,4JAACxjB,IAAI,CAACuE,EAAI,EAAE,CAACuF,MAAM,EAAEQ,KAAK,yJACjC7L,OAAAA,AAAI,EACFqL,MAAM,gKACN9J,IAAI,CAAC8a,MAAAA,AAAQ,wJACXrc,OAAAA,AAAI,iKACF2B,SAAoB,AAANmmB,EAAOjc,EAAR,CAACic,EAAY,CAAC,gKAC3BvmB,IAAI,CAAC4F,KAAAA,AAAO,EAAC4gB,cAAc,CAACxE,MAAM,EAAE1X,KAAK,EAAE3F,IAAI,EAAEuhB,KAAK,CAAC,CAAC,EACxDpC,IAAI,4JACJ9jB,IAAI,CAACoQ,IAAM,CACZ,CACF,CACF,CAAC,CACL,CACF,gKACDpQ,IAAI,CAAC4F,KAAAA,AAAO,GAAEoc,MAAM,yJAClBvjB,OAAAA,AAAI,EACFoY,OAAO,EAACpY,4JAAI,AAAJA,wJAAKT,QAAQ,AAACqN,AAAK,CAALA,CAAM1G,IAAI,CAAC,gKAAE3E,IAAI,CAAC4F,KAAAA,AAAO,EAACgG,UAAU,CAAC,CAAC,CAAC,EAC7D5L,IAAI,CAACymB,uKAAW,AAAXA,EAAY,0JACfhoB,OAAAA,AAAI,EACFujB,MAAM,qJACNpkB,EAAE,CAAC4lB,MAAAA,AAAM,4JACPxjB,IAAI,CAACuE,EAAI,EACT,CAACuF,MAAM,EAAEQ,KAAK,yJAAK7L,OAAAA,AAAI,EAACqL,MAAM,MAAE9J,IAAI,CAAC0mB,+JAAAA,AAAO,gKAAC1mB,IAAI,CAACib,YAAc,AAAdA,EAAe3Q,KAAK,CAAC,CAAC,CAAC,CAC1E,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF,CACF;AACH,CAAC;AAED,MAAMkc,cAAc,GAAGA,CACrBxE,MAA6C,EAC7C2E,MAAmC,EACnC7b,QAAmF,EACnFob,KAAsB,IAEvB1a,IAA+B,iKAC9BxL,IAAI,CAAC4mB,aAAAA,AAAe,EAACpb,IAAI,EAAE;YACzB0S,SAAS,GAAGhZ,KAAK,yJACfzG,OAAAA,AAAI,kJACFW,GAAG,CAACynB,MAAAA,AAAM,EAACX,KAAK,GAAGA,KAAK,GACtB;wBACEA,KAAK,KAAK,CAAC,yJACTznB,OAAI,AAAJA,+JAAKuB,IAAI,CAAC8mB,gBAAAA,AAAiB,EAAChc,QAAQ,EAAE5F,KAAK,CAAC,4JAAElF,IAAI,CAACoQ,IAAM,CAAC,6JAC1DpQ,IAAI,CAACuE,EAAI;wBACX2hB,KAAK,GAAG,CAAC;qBACD,CAAC,4JACblmB,IAAI,CAACgG,KAAO,CACb;YACHmY,SAAS,EAAG/a,KAAK,IACf3E,6JAAAA,AAAI,gKACFuB,IAAI,CAAC+mB,aAAe,AAAfA,EAAgBjc,QAAQ,EAAE;oBAAC1H,KAAK;oBAAEujB,MAAM;iBAAU,CAAC,EACxD3mB,IAAI,CAAC4F,mKAAAA,AAAO,GAAEiB,GAAG,GACfA,GAAG,yJACDpI,OAAAA,AAAI,MACFX,KAAK,CAAC2gB,wJAAAA,AAAY,EAACuD,MAAM,CAAC,qJAC1BpkB,EAAE,CAAC4lB,MAAAA,AAAM,4JACPxjB,IAAI,CAACuE,EAAI,EACT,CAACuF,MAAM,EAAEQ,KAAK,GACZA,KAAK,KAAKqc,MAAM,GACd7c,MAAM,yJACNrL,OAAAA,AAAI,EAACqL,MAAM,MAAE9J,IAAI,CAAC0mB,+JAAAA,AAAO,MAAC1mB,IAAI,CAACib,sKAAAA,AAAc,EAAC3Q,KAAK,CAAC,CAAC,CAAC,CAC3D,CACF,GACDtK,IAAI,CAACuE,4JAAI,CACZ;SAEN,CAAC;AAGG,MAAMyiB,YAAY,GAAA,WAAA,yJAAGzoB,OAAAA,AAAI,GAoB7Bwd,IAAI,0JAAK5c,SAAS,CAACqe,GAAAA,AAAU,EAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,+JAAC/b,IAAI,CAACgc,MAAAA,AAAQ,EAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CACpE0B,QAA0C,EAC1CwH,IAA4B,EAC5Bra,CAAiD,EACjDuP,OAIC,wKAEDta,WAAW,CAAC8d,EAAAA,AAAW,EACrBxD,OAAO,EAAEta,WAAW,EACpB,uJAAMjC,EAAE,CAAC6gB,YAAAA,AAAY,EAAChB,QAAQ,CAAC,CAAC+F,MAAM,CAAC,CAAC0B,GAAG,EAAE7I,CAAC,EAAEjL,CAAC,OAAKpR,IAAI,CAAC2e,+JAAAA,AAAO,EAACuG,GAAG,EAAE7I,CAAC,EAAE,CAAC6I,GAAG,EAAE7I,CAAC,GAAKzR,CAAC,CAACsa,GAAG,EAAE7I,CAAC,EAAEjL,CAAC,CAAC,CAAC,EAAE6T,IAAI,CAAC,EAC3G,kKACEjlB,IAAI,CAAC6K,KAAAA,AAAO,EAAC,0JACXpM,OAAAA,AAAI,EACFsmB,QAAQ,CACN;gBAACE,IAAI,EAAE;mBAAGxH,QAAQ;aAAC,sJACnBxe,MAAM,CAAC0W,AAAI,EAAK,GAChB,CAACuP,GAAG,EAAE+B,IAAI,EAAE7V,CAAC,KAAI;gBACf,OAAQ8T,GAAG,CAACxV,IAAI;oBACd,KAAK,MAAM;wBAAE;4BACX,0JAAOzQ,MAAM,CAAC4c,CAAAA,AAAI,EAACoL,IAAS,CAAC;wBAC/B;oBACA,KAAK,MAAM;wBAAE;4BACX,2JAAOhoB,MAAM,CAAC4c,AAAI,EAACjR,CAAC,CAACsa,GAAG,CAAC9hB,KAAK,EAAE6jB,IAAS,EAAE7V,CAAC,CAAC,CAAC;wBAChD;gBACF;YACF,CAAC,EACD+I,OAAO,CACR,gKACDna,IAAI,CAACsG,CAAAA,AAAG,GAAE4gB,MAAM,IAAI;gBAClB,OAAQA,MAAM,CAACxX,IAAI;oBACjB,KAAK,MAAM;wBAAE;4BACX,MAAM,IAAIhN,KAAK,CACb,iGAAiG,CAClG;wBACH;oBACA,KAAK,MAAM;wBAAE;4BACX,OAAOwkB,MAAM,CAAC9jB,KAAK;wBACrB;gBACF;YACF,CAAC,CAAC,CACH,CACF,CACJ,CAAC;AAGG,MAAM+jB,kBAAkB,GAAavjB,IAA4B,QACtE5D,IAAI,CAAConB,yKAAAA,AAAiB,GAAE1U,OAAO,uJAC7BzT,MAAM,CAAC4hB,CAAAA,AAAK,uJAAC9iB,OAAO,CAACyb,IAAAA,AAAS,EAAC9G,OAAO,EAAE2U,QAAQ,CAAC,EAAE;YACjDC,MAAM,EAAEA,CAAA,GAAM1jB,IAAI;YAClB2jB,MAAM,GAAGpd,KAAK,IAAI;gBAChB,OAAQA,KAAK,CAACqd,QAAQ,CAAC9X,IAAI;oBACzB,KAAK,UAAU;wBACb,OAAO9L,IAAI;oBACb,KAAK,YAAY;oBACjB,KAAK,WAAW;wBACd,OAAO5D,IAAI,CAAC4F,mKAAAA,AAAO,gKACjB5F,IAAI,CAACynB,OAAAA,AAAS,EAACtd,KAAK,6JAAEjM,WAA0B,CAAC,GAChDwpB,EADsC,CAAC3G,EAClC,GAAK4G,WAAW,CAAC/jB,IAAI,EAAE8jB,KAAK,CAAC,CACpC;gBACL;YACF;SACD,CAAC,CACH;AAGI,MAAME,mBAAmB,IAC7BC,WAAmB,IAAejkB,IAA4B,iKAC7D5D,IAAI,CAAConB,eAAAA,AAAiB,EAAE1U,OAAO,uJAC7BzT,MAAM,CAAC4hB,EAAK,AAALA,uJAAM9iB,OAAO,CAACyb,IAAAA,AAAS,EAAC9G,OAAO,EAAE2U,QAAQ,CAAC,EAAE;gBACjDC,MAAM,EAAEA,CAAA,GAAM1jB,IAAI;gBAClB2jB,MAAM,GAAGpd,KAAK,IAAI;oBAChB,IAAIA,KAAK,CAACqd,QAAQ,CAAC9X,IAAI,KAAK,WAAW,IAAIvF,KAAK,CAACqd,QAAQ,CAACK,WAAW,KAAKA,WAAW,EAAE;wBACrF,OAAOjkB,IAAI;oBACb;oBACA,qKAAO5D,IAAI,CAAC4F,KAAO,AAAPA,EACV5F,IAAI,CAACynB,qKAAAA,AAAS,EAACtd,KAAK,iKAAEjM,YAAkB8iB,AAAS,EAAC6G,GAAX,CAAC7G,OAAqB,CAAC,CAAC,GAC9D0G,KAAK,GAAKC,WAAW,CAAC/jB,IAAI,EAAE8jB,KAAK,CAAC,CACpC;gBACH;aACD,CAAC,CACH;AAGE,MAAMI,cAAc,GAAIN,QAA6C,KAE1E5jB,IAE2B,GACAkd,sBAAsB,CAAC0G,QAAQ,EAAE,IAAI,CAAC,CAAC5jB,IAAI,CAAC;AAGlE,MAAMkd,sBAAsB,GACjCA,CAAC0G,QAA6C,EAAE/H,oBAA0C,IAExF7b,IAE2B,iKAE3B5D,IAAI,CAAConB,eAAAA,AAAiB,GAAE1U,OAAO,uJAC7BzT,MAAM,CAAC4hB,CAAK,AAALA,uJAAM9iB,OAAO,CAACyb,IAAS,AAATA,EAAU9G,OAAO,EAAE2U,QAAQ,CAAC,EAAE;gBACjDC,MAAM,EAAEA,CAAA,GAAM1jB,IAAI,mJAACpF,WAAQ,CAAC;gBAC5B+oB,MAAM,GAAGpd,KAAK,IAAI;oBAChB,IAAIsV,oBAAoB,KAAK,IAAI,EAAE;wBACjC,MAAM1b,KAAK,GAAGyjB,QAAQ,CAAC9X,IAAI,KAAK,UAAU,GACtCyX,kBAAkB,GAClBK,QAAQ,CAAC9X,IAAI,KAAK,YAAY,GAC9BqY,oBAAoB,GACpBH,mBAAmB,CAACJ,QAAQ,CAACK,WAAW,CAAC;wBAC7C,OAAQ1d,KAAK,CAACqd,QAAQ,CAAC9X,IAAI;4BACzB,KAAK,UAAU;gCACb,OAAO3L,KAAK,CAACH,IAAI,CAACujB,kBAAkB,CAAC,CAAC;4BACxC,KAAK,YAAY;gCACf,OAAOpjB,KAAK,CAACH,IAAI,CAACmkB,oBAAoB,CAAC,CAAC;4BAC1C,KAAK,WAAW;gCACd,OAAOhkB,KAAK,CAACH,IAAI,CAACgkB,mBAAmB,CAACzd,KAAK,CAACqd,QAAQ,CAACK,WAAW,CAAC,CAAC,CAAC;wBACvE;oBACF,CAAC,MAAM;wBACL,OAAOjkB,IAAI,mJAACpF,WAAQ,CAAC;oBACvB;gBACF;aACD,CAAC,CACH;AAGE,MAAMwpB,SAAS,IACpBpd,CAAiD,iKACR5K,IAAI,CAAC4F,KAAAA,AAAO,EAACyhB,QAAQ,EAAEzc,CAAC,CAAC;AAG7D,MAAMqd,UAAU,IACrBrd,CAAiD,iKACtB5K,IAAI,CAAC4F,KAAAA,AAAO,EAACsiB,SAAS,EAAE,GAAG/d,KAAK,iKAAKnK,IAAI,CAAC0jB,IAAAA,AAAM,EAAC9Y,CAAC,CAACT,KAAK,CAAC,GAAGqB,IAAI,GAAKrB,KAAK,CAACge,KAAK,CAAC3c,IAAI,CAAC,CAAC,CAAC;AAG9G,MAAM4c,YAAY,IAAate,MAA8B,iKAClE9J,IAAI,CAAC4F,KAAAA,AAAO,EAACsiB,SAAS,EAAE,GAAG/d,KAAK,GAAKke,QAAQ,CAACve,MAAM,EAAEK,KAAK,CAAC,CAAC;AAGxD,MAAM4d,oBAAoB,IAAankB,IAA4B,iKACxE5D,IAAI,CAAConB,eAAAA,AAAiB,GAAE1U,OAAO,uJAC7BzT,MAAM,CAAC4hB,CAAAA,AAAK,GAAC9iB,OAAO,CAACyb,wJAAS,AAATA,EAAU9G,OAAO,EAAE2U,QAAQ,CAAC,EAAE;YACjDC,MAAM,EAAEA,CAAA,GAAM1jB,IAAI;YAClB2jB,MAAM,GAAGpd,KAAK,IAAI;gBAChB,OAAQA,KAAK,CAACqd,QAAQ,CAAC9X,IAAI;oBACzB,KAAK,YAAY;wBACf,OAAO9L,IAAI;oBACb,KAAK,UAAU;oBACf,KAAK,WAAW;wBACd,qKAAO5D,IAAI,CAAC4F,KAAAA,AAAO,gKACjB5F,IAAI,CAACynB,OAAS,AAATA,EAAUtd,KAAK,6JAAEjM,aAA4B,CAAC,EAClDwpB,CADsC,CAACrhB,GAClC,IAAKshB,WAAW,CAAC/jB,IAAI,EAAE8jB,KAAK,CAAC,CACpC;gBACL;YACF;SACD,CAAC,CACH;AAGI,MAAMY,gBAAgB,GAAGA,CAACna,GAAW,EAAE/K,KAAa,GACzDmlB,kBAAkB,CAAC;gLAAC5nB,QAAY6Z,AAAI,EAACrM,CAAN,CAACqM,CAAQ,EAAEpX,KAAK,CAAC;KAAC,CAAC;AAG7C,MAAMmlB,kBAAkB,IAC7BC,MAAyC,GAEzClN,yBAAyB,2JAACtb,IAAI,CAACuJ,iBAAmB,GAAGkf,GAAG,OAAK7qB,EAAE,CAAC8qB,oJAAAA,AAAK,EAACD,GAAG,EAAED,MAAM,CAAC,CAAC;AAG9E,MAAMG,KAAK,GAAA,WAAA,yJAAGpqB,OAAAA,AAAI,EAQvB,CAAC,EAAE,CAACqF,IAAI,EAAEglB,GAAG,GAAKX,UAAU,EAAE9d,KAAK,OAAKnK,IAAI,CAAC4F,+JAAO,AAAPA,EAAQ+hB,WAAW,CAAC/jB,IAAI,EAAEuG,KAAK,CAAC,EAAEye,GAAG,CAAC,CAAC,CAAC;AAGhF,MAAMC,QAAQ,GAAA,WAAA,yJAAGtqB,OAAAA,AAAI,GAmBzBwd,IAAI,GAAK/b,IAAI,CAACgc,oKAAAA,AAAQ,EAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAChC,CAACnY,IAAI,EAAEklB,IAAI,EAAE3O,OAAO,GAAK4O,YAAY,CAACnlB,IAAI,EAAEklB,IAAI,EAAE,CAACzM,CAAC,EAAEgC,CAAC,GAAK;YAAChC,CAAC;YAAEgC,CAAC;SAAC,EAAElE,OAAO,CAAC,CAC7E;AAGM,MAAM4O,YAAY,GAAA,WAAA,yJAAGxqB,OAAAA,AAAI,EAoB7Bwd,IAAI,IAAK/b,IAAI,CAACgc,oKAAAA,AAAQ,EAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAACnY,IAAI,EAAEklB,IAAI,EAAEle,CAAC,EAAEuP,OAAO,gKACzDna,IAAI,CAACgG,MAAAA,AAAO,EAACgjB,cAAc,+JACzBhpB,IAAI,CAACwL,EAAAA,AAAI,EAAC5H,IAAI,CAAC,gKACf5D,IAAI,CAACwL,EAAAA,AAAI,EAACsd,IAAI,CAAC,EACf,CAACG,EAAE,EAAEC,EAAE,iKACLlpB,IAAI,CAACmpB,SAAAA,AAAW,EAACF,EAAE,EAAEC,EAAE,EAAE;YACvB/K,SAAS,EAAEvT,CAAC;YACZsT,SAAS,EAAEA,CAACkL,EAAE,EAAE7d,EAAE,GAAK4O,OAAO,EAAEkP,UAAU,kKAAG3pB,WAAcqhB,AAAQ,EAAT,AAAUqI,CAATrI,CAAW,EAAExV,EAAE,CAAC,OAAG7L,aAAa,CAAC2G,0JAAAA,AAAU,EAAC+iB,EAAE,EAAE7d,EAAE;SAC9G,CAAC,EACJ4O,OAAO,CACR,CAAC,CAAC;AAGE,MAAMmP,cAAc,GAAA,WAAA,yJAAG/qB,OAAAA,AAAI,EAQhC,CAAC,EAAE,CAACkf,QAAQ,EAAE7S,CAAC,iKACf5K,IAAI,CAAC4F,KAAAA,AAAO,EACVwf,SAAS,CAAC3H,QAAQ,EAAE7S,CAAC,CAAC,EACtB,CAAC,CAAC4a,EAAE,EAAEC,EAAE,CAAC,GACPD,EAAE,CAACtW,MAAM,KAAK,CAAC,iKACXlP,IAAI,CAACyL,KAAAA,AAAO,EAACga,EAAE,CAAC,iKAChBzlB,IAAI,CAACgV,EAAAA,AAAI,EAACwQ,EAAE,CAAC,CACpB,CAAC;AAGG,MAAM+D,qBAAqB,GAAA,WAAA,GAAGhrB,6JAAI,AAAJA,EAKnC,CAAC,EAAE,CAACkf,QAAQ,EAAE7S,CAAC,iKACf5K,IAAI,CAAC4F,KAAAA,AAAO,EACVwf,SAAS,CAAC3H,QAAQ,EAAE7S,CAAC,CAAC,EACtB,CAAC,CAAC4a,EAAE,EAAEjjB,CAAC,CAAC,GACNijB,EAAE,CAACtW,MAAM,KAAK,CAAC,6JACXlP,IAAI,CAACuE,EAAI,iKACTvE,IAAI,CAACgV,EAAAA,AAAI,EAACwQ,EAAE,CAAC,CACpB,CAAC;AAGG,MAAMgE,aAAa,GAAA,WAAA,wJAAGjrB,QAAAA,AAAI,GAY9Bwd,IAAI,0JAAK5c,SAAS,CAACqe,GAAAA,AAAU,EAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,EACvC,CAAC0B,QAAQ,EAAE7S,CAAC,EAAEuP,OAAO,iKAAKna,IAAI,CAACypB,EAAI,AAAJA,EAAKtiB,OAAO,CAACsW,QAAQ,EAAE,CAACpB,CAAC,EAAEjL,CAAC,iKAAKpR,IAAI,CAACypB,EAAAA,AAAI,EAAC7e,CAAC,CAACyR,CAAC,EAAEjL,CAAC,CAAC,CAAC,EAAE+I,OAAO,CAAC,CAAC,CAC9F;AAGM,MAAMuP,eAAe,IAA2BC,CAAI,GACzDrO,yBAAyB,CAACrb,eAAe,CAAC+N,uKAAe,uJAAEjQ,MAAW,AAAHwQ,CAAD,CAACA,2JAAI5O,KAAK,CAAC0S,KAAQ,EAAEsX,CAAC,CAAC,CAAC;AAGrF,MAAMC,gBAAgB,IAA6BxmB,KAAQ,GAChEkY,yBAAyB,sKAACrb,eAAe,CAAC+N,EAAe,uJAAEjQ,MAAQwQ,AAAG,CAAJ,CAACA,4JAAI1N,YAAS,EAAEuC,KAAK,CAAC,CAAC;AAGpF,MAAMymB,wBAAwB,IAAIC,QAAwB,GAC/DxO,yBAAyB,sKAACrb,eAAe,CAAC+N,EAAe,uJAAEjQ,MAAQwQ,AAAG,CAAJ,CAACA,oKAAIzO,oBAAiB,EAAEgqB,QAAQ,CAAC,CAAC;AAG/F,MAAMC,gBAAgB,IAC3BnmB,IAA4B,GAE5BokB,SAAS,EAAEvG,MAAM,iKACfzhB,IAAI,CAAC4F,KAAO,AAAPA,gKAAQ5F,IAAI,CAACynB,OAAAA,AAAS,EAAChG,MAAM,yKAAEthB,aAA4B,CAAC,EAAGmO,CAAf,CAACjI,GAAmB,0JACvE5H,OAAAA,AAAI,EACFmF,IAAI,EACJ+jB,WAAW,CAACrZ,KAAK,CAAC,+JAClBtO,IAAI,CAACsG,EAAAA,AAAG,GAAElD,KAAK,GAAK;kLAClBpD,IAAI,CAACgqB,SAAAA,AAAW,GAAE/gB,OAAO,OAAKjJ,IAAI,CAACiqB,kKAAAA,AAAU,EAAC3b,KAAK,gKAAEtO,IAAI,CAACkqB,WAAAA,AAAa,EAACjhB,OAAO,CAAC,CAAC,CAAC;oBAClF7F,KAAK;iBACN,CAAC,CACH,CAAC,CACL;AAGI,MAAM+mB,UAAU,GAAA,WAAA,IAAG5rB,4JAAAA,AAAI,GAoB3Bwd,IAAI,iKAAK/b,IAAI,CAACgc,MAAQ,AAARA,EAASD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAClCnY,IAAI,EACJklB,IAAI,EACJ3O,OAAO,GACJ6O,cAAc,CAACplB,IAAI,EAAEklB,IAAI,EAAE,CAACzM,CAAC,EAAEgC,CAAC,GAAK;YAAChC,CAAC;YAAEgC,CAAC;SAAC,EAAElE,OAAO,CAAC,CAAC;AAGpD,MAAMiQ,cAAc,GAAA,WAAA,yJAAG7rB,OAAAA,AAAI,GAqB/Bwd,IAAI,IAAK/b,IAAI,CAACgc,mKAAAA,AAAQ,EAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAChC,CAACnY,IAAI,EAAEklB,IAAI,EAAE3O,OAAO,KAAI;IACtB,IAAIA,OAAO,EAAEkP,UAAU,KAAK,IAAI,IAAA,CAAKlP,OAAO,EAAEqF,QAAQ,KAAKvJ,SAAS,IAAIkE,OAAO,CAACqF,QAAQ,KAAK,KAAK,CAAC,EAAE;QACnG,qKAAOxf,IAAI,CAAC0mB,KAAAA,AAAO,EAAC9iB,IAAI,EAAEklB,IAAI,CAAC;IACjC;IACA,OAAOE,cAAc,CAACplB,IAAI,EAAEklB,IAAI,EAAE,CAACzM,CAAC,EAAE9Z,CAAC,GAAK8Z,CAAC,EAAElC,OAAO,CAAC;AACzD,CAAC,CACF;AAGM,MAAMkQ,eAAe,GAAA,WAAA,yJAkBxB9rB,OAAAA,AAAI,GAAEwd,IAAI,iKAAK/b,IAAI,CAACgc,MAAAA,AAAQ,EAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CACzCnY,IAA4B,EAC5BklB,IAA+B,EAC/B3O,OAIC,KACoC;IACrC,IAAIA,OAAO,EAAEkP,UAAU,KAAK,IAAI,IAAA,CAAKlP,OAAO,EAAEqF,QAAQ,KAAKvJ,SAAS,IAAIkE,OAAO,CAACqF,QAAQ,KAAK,KAAK,CAAC,EAAE;QACnG,qKAAOxf,IAAI,CAAC8a,MAAAA,AAAQ,EAAClX,IAAI,EAAEklB,IAAI,CAAC;IAClC;IACA,OAAOE,cAAc,CAACplB,IAAI,EAAEklB,IAAI,EAAE,CAACvmB,CAAC,EAAE8b,CAAC,GAAKA,CAAC,EAAElE,OAAO,CAAC;AACzD,CAAC,CAAC;AAGK,MAAM6O,cAAc,GAAA,WAAA,yJAoBvBzqB,OAAAA,AAAI,GAAEwd,IAAI,iKAAK/b,IAAI,CAACgc,MAAAA,AAAQ,EAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CACzCnY,IAA4B,EAC5BklB,IAA+B,EAC/Ble,CAAqB,EACrBuP,OAIC,iKAEDna,IAAI,CAACsG,CAAAA,AAAG,EACN0Z,GAAG,CAAC;QAACpc,IAAI;QAAEklB,IAAI;KAAC,EAAE;QAChBjpB,WAAW,EAAEsa,OAAO,EAAEkP,UAAU,GAAG,CAAC,GAAG,CAAC;QACxC7J,QAAQ,EAAErF,OAAO,EAAEqF,QAAQ;QAC3BC,oBAAoB,EAAEtF,OAAO,EAAEsF;KAChC,CAAC,EACF,CAAC,CAACpD,CAAC,EAAEiO,EAAE,CAAC,GAAK1f,CAAC,CAACyR,CAAC,EAAEiO,EAAE,CAAC,CACtB,CAAC;AAGG,MAAMC,sBAAsB,IACjCpF,MAA2C,IACA;IAC3C,IAAIA,MAAM,gKAAK9lB,QAAuB,EAAE,OAAR,CAACmrB;QAC/B,OAAOxqB,IAAI,CAACuE,4JAAI;IAClB;IACA,6JAAO9F,OAAAA,AAAI,4JACTuB,IAAI,CAAC8E,UAAY,gKACjB9E,IAAI,CAAC4F,KAAAA,AAAO,GAAEd,YAAY,IAAI;QAC5B,MAAMyH,mBAAmB,IAAGxL,aAAa,CAACgD,+JAAAA,AAAK,EAACe,YAAY,EAAEqgB,MAAM,CAAC;QACrE,MAAMsF,kBAAkB,yKAAG1pB,OAAcyL,AAAI,EAACD,IAAN,CAACC,cAAwB,EAAE1H,YAAY,CAAC;QAChF,6JAAOrG,OAAAA,AAAI,GACTuB,IAAI,CAAC4M,6KAAkB,AAAlBA,EAAmBuY,MAAM,CAAC,gKAC/BnlB,IAAI,CAAC8a,MAAAA,AAAQ,EAACI,YAAY,CAAC,kKAAMlb,IAAI,CAAC4M,gBAAAA,AAAkB,EAAC6d,kBAAkB,CAAC,CAAC,CAAC,4JAC9EzqB,IAAI,CAACoQ,IAAM,CACZ;IACH,CAAC,CAAC,2JACFpQ,IAAI,CAACmc,cAAe,CACrB;AACH,CAAC;AAKM,MAAMkL,QAAQ,GAAA,WAAA,wJAAGtpB,OAAO,CAAC2sB,KAAAA,AAAU,EAAc,cAAc,CAAC;AAGhE,MAAMvgB,KAAK,GAAmDkd,QAAQ;AAa7E,MAAMsD,uBAAuB,GAAGA,CAACxgB,KAAgB,EAAEygB,GAA0B,KAAU;IACrF,IAAIzgB,KAAK,CAAC1F,KAAK,CAACiL,IAAI,KAAK,MAAM,EAAE;QAC/BvF,KAAK,CAAC1F,KAAK,CAAComB,UAAU,CAAChkB,GAAG,CAAC,CAAA,CAAE,EAAE+jB,GAAG,CAAC;IACrC;AACF,CAAC;AAED,MAAME,cAAc,GAA0C;IAC5D,2JAAC9qB,IAAI,CAAC+qB,SAAW,CAAA,2JAAG/qB,IAAI,CAAC+qB,UAAW;IACpC,2JAAC/qB,IAAI,CAACgrB,kBAAoB,CAAA,4JAAGhrB,IAAI,CAACgrB,kBAAoB;IACtDvsB,IAAIA,CAAA;QACF,6JAAOS,gBAAAA,AAAa,EAAC,IAAI,EAAEmc,SAAS,CAAC;IACvC,CAAC;IACDyI,IAAIA,EAAkB0D,QAAQ;QAC5B,QAAOxnB,IAAI,CAACkL,+JAAAA,AAAI,EAAC,MAAK;YACpB,MAAM+f,QAAQ,GAAGC,eAAe,CAAC1D,QAAQ,CAAC;YAC1C,IAAI,IAAI,CAAC/iB,KAAK,CAACiL,IAAI,KAAK,QAAQ,EAAE;gBAChCub,QAAQ,CAACxmB,KAAK,GAAG,IAAI,CAACA,KAAK;gBAC3B,OAAOwmB,QAAQ;YACjB;YACA,MAAM9c,GAAG,GAAG,CAAA,CAAE;YACd,MAAMyc,GAAG,IAAIpf,IAAiC,GAAKyf,QAAQ,CAAC9C,KAAK,CAAC3c,IAAI,CAAC;YACvE,IAAI,CAAC/G,KAAK,CAAComB,UAAU,CAAChkB,GAAG,CAACsH,GAAG,EAAEyc,GAAG,CAAC;YACnCD,uBAAuB,CAACM,QAAQ,GAAG1oB,CAAC,OAClCvC,IAAI,CAACkL,4JAAI,AAAJA,EAAK,MAAK;oBACb,IAAI,IAAI,CAACzG,KAAK,CAACiL,IAAI,KAAK,MAAM,EAAE;wBAC9B,IAAI,CAACjL,KAAK,CAAComB,UAAU,CAACpc,MAAM,CAACN,GAAG,CAAC;oBACnC;gBACF,CAAC,CAAC,CAAC;YACL,OAAO8c,QAAQ;QACjB,CAAC,CAAC;IACJ,CAAC;IACD9C,KAAKA,EAAkB3c,IAAI;QACzB,qKAAOxL,IAAI,CAAC6K,KAAO,AAAPA,EAAQ,MAAK;YACvB,IAAI,IAAI,CAACpG,KAAK,CAACiL,IAAI,KAAK,QAAQ,EAAE;gBAChC,iKAAO1P,IAAI,CAACuE,EAAI;YAClB;YACA,MAAMsmB,UAAU,GAAG7iB,KAAK,CAACuC,IAAI,CAAC,IAAI,CAAC9F,KAAK,CAAComB,UAAU,CAAC3a,MAAM,EAAE,CAAC,CAACyR,OAAO,EAAE;YACvE,IAAI,CAACld,KAAK,GAAG;gBAAEiL,IAAI,EAAE,QAAQ;gBAAElE;YAAI,CAAE;YACrC,IAAIqf,UAAU,CAAC3b,MAAM,KAAK,CAAC,EAAE;gBAC3B,OAAOlP,IAAI,CAACuE,4JAAI;YAClB;YACA,kLAAOpE,eAAkBgrB,AAAY,EAAb,AAAc,CAAbA,GAAiB,CAAC3D,QAAQ,CAAC,yJAClD/oB,OAAAA,AAAI,GACFuB,IAAI,CAACkhB,4KAAAA,AAAiB,EAAC2J,UAAU,GAAGD,GAAG,iKAAK5qB,IAAI,CAACwL,EAAAA,AAAI,EAACof,GAAG,CAACpf,IAAI,CAAC,CAAC,CAAC,gKACjExL,IAAI,CAAC4F,KAAAA,AAAO,GAAEqc,OAAO,GACnBxjB,6JAAAA,AAAI,gKACFuB,IAAI,CAACsjB,YAAAA,AAAc,EAACrB,OAAO,CAAC,sJAC5BhjB,MAAM,AAACqH,AAAG,CAAHA,2JAAItG,IAAI,CAACorB,QAAU,CAAC,sJAC3BnsB,MAAM,CAACokB,KAAAA,AAAS,EAAC,8JAAMrjB,IAAI,CAACqT,MAAQ,CAAC,CACtC,CACF,CACF,8KACDlT,aAAkBkrB,AAAU,EAAC,EAAZ,CAACA,CAAe,CAAC7D,QAAQ,CAAC,yJAC3C/oB,OAAAA,AAAI,EACF0iB,mBAAmB,CAAC0J,UAAU,GAAGD,GAAG,IAAK5qB,IAAI,CAACwL,+JAAAA,AAAI,EAACof,GAAG,CAACpf,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,gKACrExL,IAAI,CAAC4F,KAAAA,AAAO,GAAEqc,OAAO,GACnBxjB,6JAAAA,AAAI,gKACFuB,IAAI,CAACsjB,YAAAA,AAAc,EAACrB,OAAO,EAAE;oBAAElB,QAAQ,EAAE;gBAAI,CAAE,CAAC,sJAChD9hB,MAAM,AAACqH,AAAG,CAAHA,2JAAItG,IAAI,CAACorB,QAAU,CAAC,qJAC3BnsB,MAAM,CAACokB,MAAAA,AAAS,EAAC,8JAAMrjB,IAAI,CAACqT,MAAQ,CAAC,CACtC,CACF,CACF,yJACD5U,OAAAA,AAAI,EACFwiB,WAAW,CAAC4J,UAAU,EAAE,IAAI,CAACrD,QAAQ,CAACK,WAAW,GAAG+C,GAAG,iKAAK5qB,IAAI,CAACwL,EAAAA,AAAI,EAACof,GAAG,CAACpf,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,EACxFxL,IAAI,CAAC4F,mKAAAA,AAAO,GAAEqc,OAAO,yJACnBxjB,OAAAA,AAAI,gKACFuB,IAAI,CAACsjB,YAAAA,AAAc,EAACrB,OAAO,EAAE;oBAAElB,QAAQ,EAAE;gBAAI,CAAE,CAAC,sJAChD9hB,MAAM,AAACqH,AAAG,CAAHA,CAAItG,IAAI,CAACorB,kKAAU,CAAC,sJAC3BnsB,MAAM,CAACokB,KAAAA,AAAS,EAAC,8JAAMrjB,IAAI,CAACqT,MAAQ,CAAC,CACtC,CACF,CACF;QACL,CAAC,CAAC;IACJ,CAAC;IACD6H,YAAYA,EAAkB0P,GAAG;QAC/B,oKAAO5qB,IAAI,CAAC6K,MAAAA,AAAO,EAAC,MAAK;YACvB,IAAI,IAAI,CAACpG,KAAK,CAACiL,IAAI,KAAK,QAAQ,EAAE;gBAChC,OAAOkb,GAAG,CAAC,IAAI,CAACnmB,KAAK,CAAC+G,IAAI,CAAC;YAC7B;YACA,IAAI,CAAC/G,KAAK,CAAComB,UAAU,CAAChkB,GAAG,CAAC,CAAA,CAAE,EAAE+jB,GAAG,CAAC;YAClC,iKAAO5qB,IAAI,CAACuE,EAAI;QAClB,CAAC,CAAC;IACJ;CACD;AAED,MAAM2mB,eAAe,GAAGA,CACtB1D,QAAA,0KAAgDrnB,aAA4B,IAAX,CAACkG,AACrD;IACb,MAAM8D,KAAK,GAAGqR,MAAM,CAAC8P,MAAM,CAACR,cAAc,CAAC;IAC3C3gB,KAAK,CAACqd,QAAQ,GAAGA,QAAQ;IACzBrd,KAAK,CAAC1F,KAAK,GAAG;QAAEiL,IAAI,EAAE,MAAM;QAAEmb,UAAU,EAAE,IAAItkB,GAAG;IAAE,CAAE;IACrD,OAAO4D,KAAK;AACd,CAAC;AAGM,MAAM+d,SAAS,GAAGA,CACvBV,QAAA,GAAgDrnB,iBAAiB,CAACkG,kKAAU,iKACnCrG,IAAI,CAACkL,EAAAA,AAAI,EAAC,IAAMggB,eAAe,CAAC1D,QAAQ,CAAC,CAAC;AAG9E,MAAMG,WAAW,GAAA,WAAA,yJAAGppB,OAAAA,AAAI,EAI7B,CAAC,EACD,CAAUuL,MAA8B,EAAEK,KAAkB,GAC1DnK,IAAI,CAACurB,2KAAe,AAAfA,EACHzhB,MAAM,EACN,mBAAA;yJACA/L,OAAO,CAACytB,AAAK,MAACztB,OAAO,CAACyc,gJAAAA,AAAI,EAAC6M,QAAQ,EAAEld,KAAK,CAAC,CAAC,CAC7C,CACJ;AAGM,MAAMke,QAAQ,GAAA,WAAA,yJAAG9pB,OAAAA,AAAI,EAQ1B,CAAC,EAAE,CAACuL,MAAM,EAAEK,KAAK,yJACjB1L,OAAAA,AAAI,EACFqL,MAAM,EACN6d,WAAW,CAACxd,KAAK,CAAC,GAClBnK,IAAI,CAAC0jB,iKAAAA,AAAM,GAAElY,IAAI,GAAKrB,KAAK,CAACge,KAAK,CAAC3c,IAAI,CAAC,CAAC,CACzC,CAAC;AAKG,MAAMigB,4BAA4B,IACvC3d,OAAmC,iKAEnC9N,IAAI,CAAC0rB,qBAAuB,AAAvBA,EAAwB5d,OAAO,EAAE;QACpC6d,MAAM,0KAAEzqB,UAAsB,KAAP,CAACyqB;QACxB7H,IAAI,2KAAE5iB,QAAgBspB,OAAD,CAACA;KACvB,CAAC;AAKG,MAAMoB,qBAAqB,GAAA,WAAA,yJAAGrtB,OAAAA,AAAI,EAGvC,CAAC,EAAE,CAACqF,IAAI,EAAER,KAAK,iKACfpD,IAAI,CAACoQ,IAAAA,AAAM,EACT0L,cAAc,+JACZ9b,IAAI,CAAC4F,KAAAA,AAAO,GACV5F,IAAI,CAAC6rB,sKAAW,AAAXA,EAAYjoB,IAAI,CAAC,GACrBkoB,QAAQ,iKAAK9rB,IAAI,CAACmb,AAAE,EAACnb,IAAI,CAAC+rB,uKAAAA,AAAW,EAACnoB,IAAI,EAAER,KAAK,CAAC,EAAE0oB,QAAQ,CAAC,CAC/D,GACAA,QAAQ,iKAAK9rB,IAAI,CAAC+rB,SAAAA,AAAW,EAACnoB,IAAI,EAAEkoB,QAAQ,CAAC,CAC/C,CACF,CAAC;AAGG,MAAMxQ,yBAAyB,GAAA,WAAA,yJAAG/c,OAAAA,AAAI,EAG3C,CAAC,EAAE,CAACqF,IAAI,EAAEgH,CAAC,OAAK5K,IAAI,CAACgsB,uKAAAA,AAAe,EAACpoB,IAAI,GAAGyY,CAAC,GAAKuP,qBAAqB,CAAChoB,IAAI,EAAEgH,CAAC,CAACyR,CAAC,CAAC,CAAC,CAAC,CAAC;AAGhF,MAAM4P,YAAY,GAAGA,CAC1Bne,OAAU,EACVqM,OAGC,GAED+R,gBAAgB,CAAC,kKAAMlsB,IAAI,CAAC+X,gBAAAA,AAAkB,EAACjK,OAAO,EAAEqM,OAAO,CAAC,CAAC;AAG5D,MAAM+R,gBAAgB,IAC3BC,GAAsC,GAEtCrQ,cAAc,CACZ9b,IAAI,CAACoc,+JAAAA,AAAG,EAACpc,IAAI,CAACkL,gKAAAA,AAAI,EAACihB,GAAG,CAAC,GAAGA,GAAG,iKAAKnsB,IAAI,CAACosB,YAAAA,AAAc,EAACD,GAAG,oJAAE3tB,WAAQ,CAAC,CAAC,GACpEiP,QAAQ,iKAAKzN,IAAI,CAACqsB,YAAAA,AAAc,EAAC5e,QAAQ,CAAC,CAC5C;AAGI,MAAM6e,mBAAmB,IAC9Bxe,OAA2B,GAE3Boe,gBAAgB,CAAC,kKAAMlsB,IAAI,CAACusB,uBAAAA,AAAyB,EAACze,OAAO,CAAC,CAAC;AAG1D,MAAM0e,wBAAwB,IACnC1e,OAAkC,GAElCoe,gBAAgB,CAAC,IAAMlsB,IAAI,CAACysB,0LAAAA,AAA8B,EAAC3e,OAAO,CAAC,CAAC;AAG/D,MAAMhK,mBAAmB,GAAA,WAAA,iKAAiD9D,IAAI,CAACysB,4BAAAA,AAA8B,oKAClH1rB,OAAkB,CACnB,KADc,CAAC4U;AAIT,MAAMhN,iBAAiB,GAAA,WAAA,GAAkD8iB,4BAA4B,iKAC1GxqB,OAAe,CAChB,EADW,CAAC0U;AAMN,MAAM+W,aAAa,IACxB1K,MAAS,GAMN7a,OAAO,CAAC6a,MAAM,6JAAE5hB,SAAoB,CAAQ,GAAf,CAACmmB;AAG5B,MAAMoG,QAAQ,IAAU3K,MAAmC,IAA8B;IAC9F,MAAM4K,SAAS,GAAG;QAChB,uJAAG3uB,UAAU,CAAC4uB,OAAe;QAC7BnjB,MAAMA,CAAA;YACJ,sKAAOtJ,OAAcuJ,AAAI,EAAC,IAAN,AAAU,CAATA,AAAU;QACjC,CAAC;QACD,4JAACvJ,aAAa,CAACsH,AAAW,CAAA,6JAAGtH,aAAa,CAACuH,EAAa;QACxDiC,EAAE,EAAEA,CAAA,sJACFhM,EAAE,CAAC6gB,YAAAA,AAAY,EAACuD,MAAM,CAAC,CAACwB,MAAM,CAAC,CAAC5Z,EAAE,EAAEU,KAAK,OAAKnM,OAAO,CAAC2uB,mJAAAA,AAAO,EAACljB,EAAE,EAAEU,KAAK,CAACV,EAAE,EAAE,CAAC,mJAAEzL,OAAO,AAAwB,CAAvBwX,AAAwB;QACjHtK,KAAK,EAAErL,IAAI,CAACwL,gKAAAA,AAAI,EAAC2V,mBAAmB,CAACa,MAAM,GAAG1X,KAAK,iKAAKtK,IAAI,CAACgG,KAAAA,AAAO,EAACsE,KAAK,CAACe,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;QAC1FhB,QAAQ,MAAErK,IAAI,CAACsG,2JAAAA,AAAG,EAAC6a,mBAAmB,CAACa,MAAM,GAAG1X,KAAK,GAAKA,KAAK,CAACD,QAAQ,EAAE,KAAK,CAAC,iJAAEzM,EAAE,CAACoI,OAAO,CAAC;QAC7F4F,UAAU,gKAAE5L,IAAI,CAAC+F,sBAAwB,AAAxBA,EAAyBic,MAAM,GAAG1X,KAAK,GAAKA,KAAK,CAACsB,UAAU,CAAC;QAC9EiB,IAAI,gKAAE7M,IAAI,CAACsG,CAAAA,AAAG,gKACZtG,IAAI,CAACkhB,eAAAA,AAAiB,EAACc,MAAM,GAAG1X,KAAK,GAAKA,KAAK,CAACuC,IAAI,CAAC,qJACrDjP,EAAE,CAAC8gB,WAAAA,AAAW,sJACZzf,MAAM,CAAC4c,AAAI,+JAAyB7b,IAAI,CAACwD,UAAAA,AAAW,EAAC,IAAIwE,KAAK,EAAE,CAAC,CAAC,EAClE,CAAC+kB,OAAO,EAAEC,OAAO,KAAI;YACnB,OAAQA,OAAO,CAACtd,IAAI;gBAClB,KAAK,MAAM;oBAAE;wBACX,QAAOzQ,MAAM,CAAC0W,mJAAAA,AAAI,EAAE;oBACtB;gBACA,KAAK,MAAM;oBAAE;wBACX,OAAQoX,OAAO,CAACrd,IAAI;4BAClB,KAAK,MAAM;gCAAE;oCACX,0JAAOzQ,MAAM,CAAC0W,CAAAA,AAAI,EAAE;gCACtB;4BACA,KAAK,MAAM;gCAAE;oCACX,2JAAO1W,MAAM,CAAC4c,AAAI,MAChB7b,IAAI,CAACmpB,mKAAW,AAAXA,EAAY6D,OAAO,CAAC5pB,KAAK,EAAE2pB,OAAO,CAAC3pB,KAAK,EAAE;wCAC7C+a,SAAS,EAAEA,CAAC9B,CAAC,EAAEgJ,KAAK,GAAK;gDAAChJ,CAAC,EAAE;mDAAGgJ,KAAK;6CAAC;wCACtCnH,SAAS,6JAAExe,WAAcqhB,EAAD,CAACA;qCAC1B,CAAC,CACH;gCACH;wBACF;oBACF;YACF;QACF,CAAC,CACF,CACF;QACD/T,eAAe,GAAG/D,OAAwB,iKACxCjJ,IAAI,CAAC+F,sBAAAA,AAAwB,EAACic,MAAM,GAAG1X,KAAK,GAAKA,KAAK,CAAC0C,eAAe,CAAC/D,OAAO,CAAC;KAClF;IACD,OAAO2jB,SAAS;AAClB,CAAC;AAGM,MAAMK,kBAAkB,IAAUrpB,IAAuB,iKAC9D5D,IAAI,CAACoQ,IAAAA,AAAM,EAAC0G,UAAU,+JAAC9W,IAAI,CAACib,YAAAA,AAAc,EAACrX,IAAI,CAAC,CAAC,CAAC;AAG7C,MAAMspB,YAAY,IAAUlL,MAAmC,kKACpE5hB,OAAcuJ,AAAI,EAACgjB,IAAN,CAAChjB,GAAa,CAACqY,MAAM,CAAC,CAAC;AAG/B,MAAMmL,WAAW,IAAUvpB,IAAuB,GACvDkY,cAAc,KAAC9b,IAAI,CAACyL,+JAAAA,AAAO,EAAC7H,IAAI,CAAC,4JAAE5D,IAAI,CAACib,YAAc,CAAC;AAOlD,MAAMmS,QAAQ,GAAA,WAAA,OAAG7uB,yJAAAA,AAAI,EAgB1B,CAAC,EAAE,CAACqF,IAAI,EAAEypB,KAAK,EAAElT,OAAO,GACxBmT,cAAc,CAAC1pB,IAAI,EAAEypB,KAAK,EAAE;QAC1BE,SAAS,EAAEA,CAAC5G,MAAM,EAAE6G,KAAK,gKACvBxtB,IAAI,CAAC4F,MAAAA,AAAO,EAAC+gB,MAAM,CAACtb,KAAK,GAAGG,IAAI,IAAI;gBAClC,OAAQA,IAAI,CAACkE,IAAI;oBACf,KAAK9O,OAAO,CAACiQ,4KAAU;wBAAE;4BACvB,qKAAO7Q,IAAI,CAAC4F,KAAO,AAAPA,EACV+gB,MAAM,CAAC/a,UAAU,EACjB,IAAMuO,OAAO,CAACsT,UAAU,CAACjiB,IAAI,EAAEgiB,KAAK,CAAC,CACtC;wBACH;oBACA,4KAAK5sB,OAAO,CAACkQ,KAAU;wBAAE;4BACvB,OAAOqJ,OAAO,CAACsT,UAAU,CAACjiB,IAAI,EAAEgiB,KAAK,CAAC;wBACxC;gBACF;YACF,CAAC,CAAC;QACJE,UAAU,EAAEA,CAAC/G,MAAM,EAAE6G,KAAK,iKACxBxtB,IAAI,CAAC4F,KAAAA,AAAO,EAAC+gB,MAAM,CAACtb,KAAK,GAAGG,IAAI,IAAI;gBAClC,OAAQA,IAAI,CAACkE,IAAI;oBACf,4KAAK9O,OAAO,CAACiQ,KAAU;wBAAE;4BACvB,QAAO7Q,IAAI,CAAC4F,kKAAO,AAAPA,EACV+gB,MAAM,CAAC/a,UAAU,EACjB,IAAMuO,OAAO,CAACwT,WAAW,CAACniB,IAAI,EAAEgiB,KAAK,CAAC,CACvC;wBACH;oBACA,4KAAK5sB,OAAO,CAACkQ,KAAU;wBAAE;4BACvB,OAAOqJ,OAAO,CAACwT,WAAW,CAACniB,IAAI,EAAEgiB,KAAK,CAAC;wBACzC;gBACF;YACF,CAAC;KACJ,CAAC,CAAC;AAGE,MAAMI,UAAU,IAAahqB,IAA4B,iKAC9D5D,IAAI,CAAC4W,iBAAAA,AAAmB,GAAEC,OAAO,iKAC/B7W,IAAI,CAACgqB,SAAAA,AAAW,GAAE/gB,OAAO,GACvBjJ,IAAI,CAAC4F,mKAAAA,AAAO,EAACkR,UAAU,CAACD,OAAO,CAACjT,IAAI,CAAC,CAAC,GAAG0G,KAAK,yJAC5C7L,OAAAA,AAAI,EACFoY,OAAO,gKAACzW,OAAcuJ,AAAI,EAACW,IAAN,CAACX,AAAU,CAAC,CAAC,GAClC3J,IAAI,CAACymB,sKAAW,AAAXA,EAAY,0JAAMhoB,OAAAA,AAAI,EAAC6L,KAAK,iKAAElK,aAAa,CAAC4M,IAAAA,AAAe,EAAC/D,OAAO,CAAC,CAAC,CAAC,CAC5E,CAAC,CACL,CACF;AAGI,MAAM4kB,IAAI,GAAA,WAAA,yJAAGtvB,OAAAA,AAAI,EAWtB,CAAC,EACD,CAACqF,IAAI,EAAEklB,IAAI,iKACT9oB,IAAI,CAACgqB,SAAAA,AAAW,GAAEhe,aAAa,GAC7BohB,QAAQ,CAACxpB,IAAI,EAAEklB,IAAI,EAAE;YACnB2E,UAAU,EAAEA,CAACjiB,IAAI,EAAE6J,KAAK,GACtBrV,IAAI,CAAC4mB,2KAAAA,AAAe,EAACpb,IAAI,EAAE;oBACzB0S,SAAS,GAAGhZ,KAAK,yJACfzG,OAAAA,AAAI,iKACF2B,OAAcuJ,AAAI,EAAC0L,IAAN,CAAC1L,AAAU,CAAC,GACzB5J,cAAc,CAAC+tB,wKAAa,AAAbA,GAAeC,MAAM,kKAAKruB,WAAcqhB,AAAQ,EAAT,AAAU7b,CAAT6b,IAAc,EAAEgN,MAAM,CAAC,CAAC,CAChF;oBACH5P,SAAS,GAAG/a,KAAK,wJACf3E,QAAI,AAAJA,EACE4W,KAAK,gKACLrV,IAAI,CAACguB,cAAAA,AAAgB,EAAChiB,aAAa,CAAC,gKACpChM,IAAI,CAACmb,AAAE,EAAC/X,KAAK,CAAC;iBAEnB,CAAC;YACJuqB,WAAW,EAAEA,CAACniB,IAAI,EAAEyJ,IAAI,GACtBjV,IAAI,CAAC4mB,2KAAAA,AAAe,EAACpb,IAAI,EAAE;oBACzB0S,SAAS,GAAGhZ,KAAK,yJACfzG,OAAAA,AAAI,EACF2B,aAAa,CAACuJ,wJAAAA,AAAI,EAACsL,IAAI,CAAC,0KACxBlV,cAAc,CAAC+tB,CAAa,AAAbA,GAAeC,MAAM,GAAKruB,aAAa,CAACqhB,4JAAAA,AAAQ,EAACgN,MAAM,EAAE7oB,KAAK,CAAC,CAAC,CAChF;oBACHiZ,SAAS,GAAG/a,KAAK,yJACf3E,OAAAA,AAAI,EACFwW,IAAI,EACJjV,IAAI,CAACguB,4KAAAA,AAAgB,EAAChiB,aAAa,CAAC,gKACpChM,IAAI,CAACmb,AAAE,EAAC/X,KAAK,CAAC;iBAEnB;SACJ,CAAC,CACH,CACJ;AAGM,MAAMkqB,cAAc,GAAA,WAAA,yJAAG/uB,OAAI,AAAJA,EAgC5B,CAAC,EAAE,CACHqF,IAA4B,EAC5BypB,KAAgC,EAChClT,OAWC,iKAEDna,IAAI,CAAC6L,cAAAA,AAAgB,EAAC,CAACC,WAAW,EAAEC,YAAY,KAAI;QAClD,MAAMG,kBAAkB,GAAGH,YAAY,CAACjH,YAAY;QACpD,MAAMmpB,aAAa,GAAGjvB,IAAI,CAACwb,0JAAAA,AAAI,EAAC,IAAI,CAAC;QACrC,MAAM0T,SAAS,GAAuB3J,oBAAoB,CACxD3gB,IAAI,EACJkI,WAAW,EACXI,kBAAkB,EAClBiO,OAAO,CAACgU,SAAS,CAClB;QACD,MAAMC,UAAU,GAAyB7J,oBAAoB,CAC3D8I,KAAK,EACLvhB,WAAW,EACXI,kBAAkB,EAClBiO,OAAO,CAACkU,UAAU,CACnB;QACD,qKAAOruB,IAAI,CAACsL,GAAK,AAALA,GAAOC,EAAE,IAAI;YACvB2iB,SAAS,CAACxiB,WAAW,CAAC,IAAM4iB,YAAY,CAACJ,SAAS,EAAEE,UAAU,EAAEjU,OAAO,CAACoT,SAAS,EAAEU,aAAa,EAAE1iB,EAAE,CAAC,CAAC;YACtG6iB,UAAU,CAAC1iB,WAAW,CAAC,IAAM4iB,YAAY,CAACF,UAAU,EAAEF,SAAS,EAAE/T,OAAO,CAACuT,UAAU,EAAEO,aAAa,EAAE1iB,EAAE,CAAC,CAAC;YACxG2iB,SAAS,CAACxa,SAAS,CAAC9P,IAAI,CAAC;YACzBwqB,UAAU,CAAC1a,SAAS,CAAC2Z,KAAK,CAAC;QAC7B,CAAC,uJAAElvB,OAAO,CAAC2uB,EAAAA,AAAO,EAACoB,SAAS,CAACtkB,EAAE,EAAE,EAAEwkB,UAAU,CAACxkB,EAAE,EAAE,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;AAEL,MAAM0kB,YAAY,GAAGA,CACnB3H,MAAoC,EACpC6G,KAAmC,EACnCrqB,IAAiH,EACjHorB,EAA4B,EAC5BhjB,EAAkE,KAC1D;IACR,4JAAIvM,IAAI,CAACwvB,WAAAA,AAAa,EAAC,IAAI,EAAE,KAAK,CAAC,CAACD,EAAE,CAAC,EAAE;QACvChjB,EAAE,CAACpI,IAAI,CAACwjB,MAAM,EAAE6G,KAAK,CAAC,CAAC;IACzB;AACF,CAAC;AAGM,MAAMjR,QAAQ,GAAA,WAAA,yJAKjBhe,OAAAA,AAAI,EACN,CAAC,EACD,CAAiBqF,IAA4B,EAAE4Y,SAAsC,iKACnFxc,IAAI,CAAC4W,iBAAAA,AAAmB,GAAEC,OAAO,iKAC/B7W,IAAI,CAACyuB,cAAAA,AAAgB,EAAC5X,OAAO,CAACjT,IAAI,CAAC,EAAE;YACnCsa,SAAS,GAAGwQ,MAAM,gKAChB1uB,IAAI,CAACyuB,eAAgB,AAAhBA,EAAiBjS,SAAS,EAAE;oBAC/B0B,SAAS,GAAG6P,MAAM,iKAAK/tB,IAAI,CAACkU,OAAAA,AAAS,gKAACxU,aAAa,CAAC2G,AAAU,EAACqoB,MAAM,EAAEX,MAAM,CAAC,CAAC;oBAC/E5P,SAAS,EAAEA,CAAA,iKAAMne,IAAI,CAACkU,OAAAA,AAAS,EAACwa,MAAM;iBACvC,CAAC;YACJvQ,SAAS,GAAG9B,CAAC,GAAKrc,IAAI,CAACmb,8JAAAA,AAAE,EAACqB,SAAS,EAAEH,CAAC;SACvC,CAAC,CACH,CACJ;AAGM,MAAMpV,mBAAmB,GAICA,CAC/BrD,IAA4B,EAC5B2X,OAAsC,EACtCkL,WAAwB,gKAExBzmB,IAAI,CAACgqB,UAAAA,AAAW,GAAEpgB,EAAE,iKAClB5J,IAAI,CAAC4F,KAAAA,AAAO,MACV5F,IAAI,CAAC4F,+JAAAA,AAAO,EACVkR,UAAU,+JAAC9W,IAAI,CAACgE,WAAAA,AAAa,EAACJ,IAAI,CAAC,CAAC,GACnC+qB,UAAU,iKACT3uB,IAAI,CAACsL,GAAAA,AAAK,GAAWC,EAAE,IAAI;gBACzB,MAAMqjB,MAAM,GAAGrT,OAAO,CAACjV,GAAG,EAAE/D,CAAC,GAAKA,CAAC,CAAC6E,SAAS,CAACnF,KAAK,CAAC;gBACpD,MAAM4sB,SAAS,GAAGA,CAAA,KAAK;oBACrB,IAAID,MAAM,CAACE,KAAK,EAAE7sB,KAAK,GAAKA,KAAK,KAAK,CAAC,CAAC,EAAE;wBACxC,IACEsZ,OAAO,CAACuT,KAAK,EAAEvsB,CAAC,IAAI;4BAClB,IAAIA,CAAC,CAACwsB,MAAM,CAACtqB,KAAK,CAACuqB,OAAO,CAACtf,IAAI,KAAK,SAAS,EAAE;gCAC7C,OAAO,IAAI;4BACb,CAAC,MAAM,IACLnN,CAAC,CAACwsB,MAAM,CAACtqB,KAAK,CAACuqB,OAAO,CAACtf,IAAI,KAAK,MAAM,kKACtC1P,IAAI,CAACivB,QAAAA,AAAU,EAAC1sB,CAAC,CAACwsB,MAAM,CAACtqB,KAAK,CAACuqB,OAAO,CAACllB,MAAM,CAAC,IAC9CvH,CAAC,CAACwsB,MAAM,CAACtqB,KAAK,CAACuqB,OAAO,CAACllB,MAAM,CAAC4F,IAAI,KAAK,SAAS,mKAChDhQ,aAAa,CAACuE,EAAAA,AAAa,EAAC1B,CAAC,CAACwsB,MAAM,CAACtqB,KAAK,CAACuqB,OAAO,CAACllB,MAAM,CAAC5E,KAAK,CAAC,EAChE;gCACA,OAAO,IAAI;4BACb,CAAC,MAAM;gCACL,OAAO,KAAK;4BACd;wBACF,CAAC,CAAC,EACF;4BACAgqB,OAAO,CAAC/nB,OAAO,EAAEyD,CAAC,GAAKA,CAAC,EAAE,CAAC;4BAC3B6b,WAAW,GAAE,CAAE;4BACflb,EAAE,EAACvL,IAAI,CAACib,yKAAAA,AAAc,EAAC0T,UAAU,CAAC,CAAC;wBACrC;oBACF;gBACF,CAAC;gBACDA,UAAU,CAACjjB,WAAW,EAAEF,IAAI,IAAI;oBAC9B0jB,OAAO,CAAC/nB,OAAO,EAAEyD,CAAC,GAAKA,CAAC,EAAE,CAAC;oBAC3BW,EAAE,CAACC,IAAI,CAAC;gBACV,CAAC,CAAC;gBACF,MAAM0jB,OAAO,GAAG3T,OAAO,CAACjV,GAAG,CAAC,CAACoa,CAAC,EAAEtP,CAAC,KAAI;oBACnC,MAAMhE,QAAQ,IAAInL,KAAa,IAAI;wBACjC2sB,MAAM,CAACxd,CAAC,CAAC,GAAGnP,KAAK;wBACjB4sB,SAAS,EAAE;oBACb,CAAC;oBACDnO,CAAC,CAACtZ,SAAS,CAACsE,WAAW,CAAC0B,QAAQ,CAAC;oBACjC,OAAO,IAAMsT,CAAC,CAACtZ,SAAS,CAACuE,cAAc,CAACyB,QAAQ,CAAC;gBACnD,CAAC,CAAC;gBACFyhB,SAAS,EAAE;gBACX,OAAO7uB,IAAI,CAACkL,gKAAAA,AAAI,EAAC,MAAK;oBACpBgkB,OAAO,CAAC/nB,OAAO,EAAEyD,CAAC,GAAKA,CAAC,EAAE,CAAC;gBAC7B,CAAC,CAAC;YACJ,CAAC,CAAC,CACL,EACD,QACE5K,IAAI,CAAC6K,+JAAAA,AAAO,EAAC,MAAK;gBAChB,MAAMwX,QAAQ,GAAG9G,OAAO,CAAC3V,OAAO,EAAEgB,KAAK,IAAI;oBACzC,IAAI,CAACA,KAAK,CAACnC,KAAK,CAAC0qB,SAAS,EAAE;wBAC1B,OAAO;4BAACvoB,KAAK;yBAAC;oBAChB;oBACA,OAAO,EAAE;gBACX,CAAC,CAAC;gBACF,qKAAO5G,IAAI,CAAC+F,sBAAAA,AAAwB,EAClCsc,QAAQ,GACPzb,KAAK,oKAAK9F,WAAAA,AAAQ,EAAC8F,KAAK,CAACE,OAAc,gKAAE9G,IAAI,CAACkqB,WAAAA,AAAa,EAACtgB,EAAE,CAAC,CAAC,CAClE;YACH,CAAC,CAAC,CACL,CACF;AAGI,MAAMwlB,qBAAqB,GAAA,WAAA,IAAG7wB,4JAAAA,AAAI,EAQvC,CAAC,EAAE,CAACqF,IAAI,EAAEoc,GAAG,iKACbhgB,IAAI,CAACgsB,aAAAA,AAAe,2KAClBpsB,oBAAiB,GAChB0G,GAAG,iKACFtG,IAAI,CAAC6K,KAAAA,AAAO,EAAC,MAAK;YAChB,MAAM0Q,OAAO,sJAAG3d,EAAE,CAAC6gB,YAAAA,AAAY,EAACuB,GAAG,CAAC,CAACpa,OAAO,EAAErD,CAAC,GAAK+D,GAAG,CAACsH,GAAG,CAACrL,CAAC,CAAC,GAAG;oBAAC+D,GAAG,CAACuH,GAAG,CAACtL,CAAC,CAAE;iBAAC,GAAG,EAAE,CAAC;YACpF,OAAO0E,mBAAmB,CAACrD,IAAI,EAAE2X,OAAO,CAAC;QAC3C,CAAC,CAAC,CACL,CAAC;AAKG,MAAM8T,cAAc,GAAGA,CAC5B/U,IAAY,EACZH,OAAwC,KACU;IAClDA,OAAO,mKAAGhZ,MAAM,CAACmuB,aAAAA,AAAiB,EAACnV,OAAO,CAAC;IAC3C,qKAAOna,IAAI,CAACmc,aAAAA,AAAe,GACzBnc,IAAI,CAAC6L,2KAAAA,AAAgB,GAAEvB,KAAK,IAAI;QAC9B,MAAMH,KAAK,wJAAGpM,OAAO,CAAC+W,IAAAA,AAAS,EAACxK,KAAK,CAAChB,WAAW,2JAACtJ,IAAI,CAAC8I,YAAc,CAAC,EAAEue,QAAQ,CAAC;QACjF,MAAM9N,IAAI,IAAGxZ,cAAc,CAACwvB,yKAAAA,AAAc,EAACjlB,KAAK,EAAEgQ,IAAI,EAAEH,OAAO,CAAC;QAChE,MAAMqV,aAAa,GAAGllB,KAAK,CAAChB,WAAW,2JAACtJ,IAAI,CAACyvB,wBAA0B,CAAC;QACxE,MAAMC,MAAM,wJAAG3xB,MAAW,AAAH8P,CAAD,CAAKvD,AAAJuD,KAAS,CAACvE,WAAW,sKAACrJ,eAAe,CAAC+N,EAAe,CAAC,6JAAErO,KAAK,CAAC0S,KAAQ,CAAC;QAC9F,qKAAOrS,IAAI,CAACmb,AAAE,gKACZnb,IAAI,CAAC4c,mBAAAA,AAAqB,EAACzS,KAAK,GAAGqB,IAAI,2KAAKzL,UAAe4vB,AAAO,EAACpW,EAAT,CAACoW,CAAY,EAAEnkB,IAAI,EAAEkkB,MAAM,EAAEF,aAAa,CAAC,CAAC,EACtGjW,IAAI,CACL;IACH,CAAC,CAAC,CACH;AACH,CAAC;AAGM,MAAMqW,gBAAgB,IAAIxsB,KAAoB,GACnDkY,yBAAyB,sKAACrb,eAAe,CAAC+N,EAAe,uJAAEjQ,MAAQwQ,AAAG,CAAJ,CAACA,2JAAIpN,MAAM,CAAC+M,MAAS,EAAE9K,KAAK,CAAC,CAAC;AAG3F,MAAMysB,cAAc,GAUvB,SAAAA,CAAA;IACF,MAAMC,SAAS,GAAG,OAAOzU,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ;IAClD,MAAMf,IAAI,GAAGwV,SAAS,GAAGzU,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC;IACpD,MAAMlB,OAAO,GAAGhZ,MAAM,CAACmuB,6KAAAA,AAAiB,EAACQ,SAAS,GAAGzU,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC;IACjF,IAAIyU,SAAS,EAAE;QACb,MAAMlsB,IAAI,GAAGyX,SAAS,CAAC,CAAC,CAAC;QACzB,QAAOrb,IAAI,CAAC4F,kKAAAA,AAAO,EACjBypB,cAAc,CAAC/U,IAAI,kKAAEnZ,MAAM,CAACmuB,aAAAA,AAAiB,EAACnV,OAAO,CAAC,CAAC,GACtDZ,IAAI,2KAAKxZ,cAAc,CAACgwB,EAAc,AAAdA,EAAensB,IAAI,6JAAEzC,MAAM,CAACiN,IAAO,EAAEmL,IAAI,CAAC,CACpE;IACH;IACA,QAAQ3V,IAAkC,iKACxC5D,IAAI,CAAC4F,KAAAA,AAAO,EACVypB,cAAc,CAAC/U,IAAI,kKAAEnZ,MAAM,CAACmuB,aAAAA,AAAiB,EAACnV,OAAO,CAAC,CAAC,GACtDZ,IAAI,2KAAKxZ,cAAc,CAACgwB,EAAAA,AAAc,EAACnsB,IAAI,8JAAEzC,MAAM,CAACiN,GAAO,EAAEmL,IAAI,CAAC,CACpE;AACL,CAAQ", "ignoreList": [0], "debugId": null}}]}