{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/animejs/lib/anime.esm.js"], "sourcesContent": ["/**\n * anime.js - ESM\n * @version v4.0.2\n * <AUTHOR>\n * @license MIT\n * @copyright (c) 2025 <PERSON>\n * @see https://animejs.com\n */\n\n/**\n * @typedef {Object} DefaultsParams\n * @property {Number|String} [id]\n * @property {PercentageKeyframes|DurationKeyframes} [keyframes]\n * @property {EasingParam} [playbackEase]\n * @property {Number} [playbackRate]\n * @property {Number} [frameRate]\n * @property {Number|Boolean} [loop]\n * @property {Boolean} [reversed]\n * @property {Boolean} [alternate]\n * @property {Boolean|ScrollObserver} [autoplay]\n * @property {Number|FunctionValue} [duration]\n * @property {Number|FunctionValue} [delay]\n * @property {Number} [loopDelay]\n * @property {EasingParam} [ease]\n * @property {'none'|'replace'|'blend'|compositionTypes} [composition]\n * @property {(v: any) => any} [modifier]\n * @property {(tickable: Tickable) => void} [onBegin]\n * @property {(tickable: Tickable) => void} [onBeforeUpdate]\n * @property {(tickable: Tickable) => void} [onUpdate]\n * @property {(tickable: Tickable) => void} [onLoop]\n * @property {(tickable: Tickable) => void} [onPause]\n * @property {(tickable: Tickable) => void} [onComplete]\n * @property {(renderable: Renderable) => void} [onRender]\n */\n\n/** @typedef {JSAnimation|Timeline} Renderable */\n/** @typedef {Timer|Renderable} Tickable */\n/** @typedef {Timer&JSAnimation&Timeline} CallbackArgument */\n/** @typedef {Animatable|Tickable|Draggable|ScrollObserver|Scope} Revertible */\n\n/**\n * @typedef {Object} DraggableAxisParam\n * @property {String} [mapTo]\n * @property {TweenModifier} [modifier]\n * @property {TweenComposition} [composition]\n * @property {Number|Array<Number>|((draggable: Draggable) => Number|Array<Number>)} [snap]\n */\n\n/**\n * @typedef {Object} DraggableCursorParams\n * @property {String} [onHover]\n * @property {String} [onGrab]\n */\n\n/**\n * @typedef {Object} DraggableParams\n * @property {DOMTargetSelector} [trigger]\n * @property {DOMTargetSelector|Array<Number>|((draggable: Draggable) => DOMTargetSelector|Array<Number>)} [container]\n * @property {Boolean|DraggableAxisParam} [x]\n * @property {Boolean|DraggableAxisParam} [y]\n * @property {TweenModifier} [modifier]\n * @property {Number|Array<Number>|((draggable: Draggable) => Number|Array<Number>)} [snap]\n * @property {Number|Array<Number>|((draggable: Draggable) => Number|Array<Number>)} [containerPadding]\n * @property {Number|((draggable: Draggable) => Number)} [containerFriction]\n * @property {Number|((draggable: Draggable) => Number)} [releaseContainerFriction]\n * @property {Number|((draggable: Draggable) => Number)} [dragSpeed]\n * @property {Number|((draggable: Draggable) => Number)} [scrollSpeed]\n * @property {Number|((draggable: Draggable) => Number)} [scrollThreshold]\n * @property {Number|((draggable: Draggable) => Number)} [minVelocity]\n * @property {Number|((draggable: Draggable) => Number)} [maxVelocity]\n * @property {Number|((draggable: Draggable) => Number)} [velocityMultiplier]\n * @property {Number} [releaseMass]\n * @property {Number} [releaseStiffness]\n * @property {Number} [releaseDamping]\n * @property {Boolean} [releaseDamping]\n * @property {EasingParam} [releaseEase]\n * @property {Boolean|DraggableCursorParams|((draggable: Draggable) => Boolean|DraggableCursorParams)} [cursor]\n * @property {Callback<Draggable>} [onGrab]\n * @property {Callback<Draggable>} [onDrag]\n * @property {Callback<Draggable>} [onRelease]\n * @property {Callback<Draggable>} [onUpdate]\n * @property {Callback<Draggable>} [onSettle]\n * @property {Callback<Draggable>} [onSnap]\n * @property {Callback<Draggable>} [onResize]\n * @property {Callback<Draggable>} [onAfterResize]\n */\n\n/**\n * @typedef {SVGGeometryElement & {\n *   setAttribute(name: 'draw', value: `${number} ${number}`): void;\n *   draw: `${number} ${number}`;\n * }} DrawableSVGGeometry\n */\n\n/**\n * @callback EasingFunction\n * @param {Number} time\n * @return {Number}\n */\n\n/**\n * @typedef {('linear'|'linear(x1, x2 25%, x3)'|'in'|'out'|'inOut'|'inQuad'|'outQuad'|'inOutQuad'|'inCubic'|'outCubic'|'inOutCubic'|'inQuart'|'outQuart'|'inOutQuart'|'inQuint'|'outQuint'|'inOutQuint'|'inSine'|'outSine'|'inOutSine'|'inCirc'|'outCirc'|'inOutCirc'|'inExpo'|'outExpo'|'inOutExpo'|'inBounce'|'outBounce'|'inOutBounce'|'inBack'|'outBack'|'inOutBack'|'inElastic'|'outElastic'|'inOutElastic'|'irregular'|'cubicBezier'|'steps'|'in(p = 1.675)'|'out(p = 1.675)'|'inOut(p = 1.675)'|'inBack(overshoot = 1.70158)'|'outBack(overshoot = 1.70158)'|'inOutBack(overshoot = 1.70158)'|'inElastic(amplitude = 1, period = .3)'|'outElastic(amplitude = 1, period = .3)'|'inOutElastic(amplitude = 1, period = .3)'|'irregular(length = 10, randomness = 1)'|'cubicBezier(x1, y1, x2, y2)'|'steps(steps = 10)')} EaseStringParamNames\n */\n\n// A hack to get both ease names suggestions AND allow any strings\n// https://github.com/microsoft/TypeScript/issues/29729#issuecomment-460346421\n/** @typedef {(String & {})|EaseStringParamNames|EasingFunction|Spring} EasingParam */\n\n/** @typedef {HTMLElement|SVGElement} DOMTarget */\n/** @typedef {Record<String, any>} JSTarget */\n/** @typedef {DOMTarget|JSTarget} Target */\n/** @typedef {Target|NodeList|String} TargetSelector */\n/** @typedef {DOMTarget|NodeList|String} DOMTargetSelector */\n/** @typedef {Array.<DOMTargetSelector>|DOMTargetSelector} DOMTargetsParam */\n/** @typedef {Array.<DOMTarget>} DOMTargetsArray */\n/** @typedef {Array.<JSTarget>|JSTarget} JSTargetsParam */\n/** @typedef {Array.<JSTarget>} JSTargetsArray */\n/** @typedef {Array.<TargetSelector>|TargetSelector} TargetsParam */\n/** @typedef {Array.<Target>} TargetsArray */\n\n/**\n * @callback FunctionValue\n * @param {Target} target - The animated target\n * @param {Number} index - The target index\n * @param {Number} length - The total number of animated targets\n * @return {Number|String|TweenObjectValue|Array.<Number|String|TweenObjectValue>}\n */\n\n/**\n * @callback TweenModifier\n * @param {Number} value - The animated value\n * @return {Number|String}\n */\n\n/** @typedef {[Number, Number, Number, Number]} ColorArray */\n\n/**\n * @template T\n * @callback Callback\n * @param {T} self - Returns itself\n * @param {PointerEvent} [e]\n * @return {*}\n */\n\n/**\n * @template {object} T\n * @typedef {Object} TickableCallbacks\n * @property {Callback<T>} [onBegin]\n * @property {Callback<T>} [onBeforeUpdate]\n * @property {Callback<T>} [onUpdate]\n * @property {Callback<T>} [onLoop]\n * @property {Callback<T>} [onPause]\n * @property {Callback<T>} [onComplete]\n */\n\n/**\n * @template {object} T\n * @typedef {Object} RenderableCallbacks\n * @property {Callback<T>} [onRender]\n */\n\n/**\n * @typedef {Object} Tween\n * @property {Number} id\n * @property {JSAnimation} parent\n * @property {String} property\n * @property {Target} target\n * @property {String|Number} _value\n * @property {Function|null} _func\n * @property {EasingFunction} _ease\n * @property {Array.<Number>} _fromNumbers\n * @property {Array.<Number>} _toNumbers\n * @property {Array.<String>} _strings\n * @property {Number} _fromNumber\n * @property {Number} _toNumber\n * @property {Array.<Number>} _numbers\n * @property {Number} _number\n * @property {String} _unit\n * @property {TweenModifier} _modifier\n * @property {Number} _currentTime\n * @property {Number} _delay\n * @property {Number} _updateDuration\n * @property {Number} _startTime\n * @property {Number} _changeDuration\n * @property {Number} _absoluteStartTime\n * @property {tweenTypes} _tweenType\n * @property {valueTypes} _valueType\n * @property {Number} _composition\n * @property {Number} _isOverlapped\n * @property {Number} _isOverridden\n * @property {Number} _renderTransforms\n * @property {Tween} _prevRep\n * @property {Tween} _nextRep\n * @property {Tween} _prevAdd\n * @property {Tween} _nextAdd\n * @property {Tween} _prev\n * @property {Tween} _next\n */\n\n/**\n * @typedef TweenDecomposedValue\n * @property {Number} t - Type\n * @property {Number} n - Single number value\n * @property {String} u - Value unit\n * @property {String} o - Value operator\n * @property {Array.<Number>} d - Array of Numbers (in case of complex value type)\n * @property {Array.<String>} s - Strings (in case of complex value type)\n */\n\n/** @typedef {{_head: null|Tween, _tail: null|Tween}} TweenPropertySiblings */\n/** @typedef {Record<String, TweenPropertySiblings>} TweenLookups */\n/** @typedef {WeakMap.<Target, TweenLookups>} TweenReplaceLookups */\n/** @typedef {Map.<Target, TweenLookups>} TweenAdditiveLookups */\n\n/**\n * @typedef {Object} TimerOptions\n * @property {Number|String} [id]\n * @property {TweenParamValue} [duration]\n * @property {TweenParamValue} [delay]\n * @property {Number} [loopDelay]\n * @property {Boolean} [reversed]\n * @property {Boolean} [alternate]\n * @property {Boolean|Number} [loop]\n * @property {Boolean|ScrollObserver} [autoplay]\n * @property {Number} [frameRate]\n * @property {Number} [playbackRate]\n */\n\n/**\n\n/**\n * @typedef {TimerOptions & TickableCallbacks<Timer>} TimerParams\n */\n\n/**\n * @typedef {Number|String|FunctionValue} TweenParamValue\n */\n\n/**\n * @typedef {TweenParamValue|[TweenParamValue, TweenParamValue]} TweenPropValue\n */\n\n/**\n * @typedef {(String & {})|'none'|'replace'|'blend'|compositionTypes} TweenComposition\n */\n\n/**\n * @typedef {Object} TweenParamsOptions\n * @property {TweenParamValue} [duration]\n * @property {TweenParamValue} [delay]\n * @property {EasingParam} [ease]\n * @property {TweenModifier} [modifier]\n * @property {TweenComposition} [composition]\n */\n\n/**\n * @typedef {Object} TweenValues\n * @property {TweenParamValue} [from]\n * @property {TweenPropValue} [to]\n * @property {TweenPropValue} [fromTo]\n */\n\n/**\n * @typedef {TweenParamsOptions & TweenValues} TweenKeyValue\n */\n\n/**\n * @typedef {Array.<TweenKeyValue|TweenPropValue>} ArraySyntaxValue\n */\n\n/**\n * @typedef {TweenParamValue|ArraySyntaxValue|TweenKeyValue} TweenOptions\n */\n\n/**\n * @typedef {Partial<{to: TweenParamValue|Array.<TweenParamValue>; from: TweenParamValue|Array.<TweenParamValue>; fromTo: TweenParamValue|Array.<TweenParamValue>;}>} TweenObjectValue\n */\n\n/**\n * @typedef {Object} PercentageKeyframeOptions\n * @property {EasingParam} [ease]\n */\n\n/**\n * @typedef {Record<String, TweenParamValue>} PercentageKeyframeParams\n */\n\n/**\n * @typedef {Record<String, PercentageKeyframeParams & PercentageKeyframeOptions>} PercentageKeyframes\n */\n\n/**\n * @typedef {Array<Record<String, TweenOptions | TweenModifier | boolean> & TweenParamsOptions>} DurationKeyframes\n */\n\n/**\n * @typedef {Object} AnimationOptions\n * @property {PercentageKeyframes|DurationKeyframes} [keyframes]\n * @property {EasingParam} [playbackEase]\n */\n\n// TODO: Currently setting TweenModifier to the intersected Record<> makes the FunctionValue type target param any if only one parameter is set\n/**\n * @typedef {Record<String, TweenOptions | Callback<JSAnimation> | TweenModifier | boolean | PercentageKeyframes | DurationKeyframes | ScrollObserver> & TimerOptions & AnimationOptions & TweenParamsOptions & TickableCallbacks<JSAnimation> & RenderableCallbacks<JSAnimation>} AnimationParams\n */\n\n/**\n * @typedef {Object} TimelineOptions\n * @property {DefaultsParams} [defaults]\n * @property {EasingParam} [playbackEase]\n */\n\n/**\n * @typedef {TimerOptions & TimelineOptions & TickableCallbacks<Timeline> & RenderableCallbacks<Timeline>} TimelineParams\n */\n\n/**\n * @callback AnimatablePropertySetter\n * @param  {Number|Array.<Number>} to\n * @param  {Number} [duration]\n * @param  {EasingParam} [ease]\n * @return {AnimatableObject}\n */\n\n/**\n * @callback AnimatablePropertyGetter\n * @return {Number|Array.<Number>}\n */\n\n/**\n * @typedef {AnimatablePropertySetter & AnimatablePropertyGetter} AnimatableProperty\n */\n\n/**\n * @typedef {Animatable & Record<String, AnimatableProperty>} AnimatableObject\n */\n\n/**\n * @typedef {Object} AnimatablePropertyParamsOptions\n * @property {String} [unit]\n * @property {TweenParamValue} [duration]\n * @property {EasingParam} [ease]\n * @property {TweenModifier} [modifier]\n * @property {TweenComposition} [composition]\n */\n\n/**\n * @typedef {Record<String, TweenParamValue | EasingParam | TweenModifier | TweenComposition | AnimatablePropertyParamsOptions> & AnimatablePropertyParamsOptions} AnimatableParams\n */\n\n\n// Environments\n\n// TODO: Do we need to check if we're running inside a worker ?\nconst isBrowser = typeof window !== 'undefined';\n\n/** @type {Object|Null} */\nconst win = isBrowser ? window : null;\n\n/** @type {Document} */\nconst doc = isBrowser ? document : null;\n\n// Enums\n\n/** @enum {Number} */\nconst tweenTypes = {\n  OBJECT: 0,\n  ATTRIBUTE: 1,\n  CSS: 2,\n  TRANSFORM: 3,\n  CSS_VAR: 4,\n};\n\n/** @enum {Number} */\nconst valueTypes = {\n  NUMBER: 0,\n  UNIT: 1,\n  COLOR: 2,\n  COMPLEX: 3,\n};\n\n/** @enum {Number} */\nconst tickModes = {\n  NONE: 0,\n  AUTO: 1,\n  FORCE: 2,\n};\n\n/** @enum {Number} */\nconst compositionTypes = {\n  replace: 0,\n  none: 1,\n  blend: 2,\n};\n\n// Cache symbols\n\nconst isRegisteredTargetSymbol = Symbol();\nconst isDomSymbol = Symbol();\nconst isSvgSymbol = Symbol();\nconst transformsSymbol = Symbol();\nconst morphPointsSymbol = Symbol();\nconst proxyTargetSymbol = Symbol();\n\n// Numbers\n\nconst minValue = 1e-11;\nconst maxValue = 1e12;\nconst K = 1e3;\nconst maxFps = 120;\n\n// Strings\n\nconst emptyString = '';\nconst shortTransforms = new Map();\n\nshortTransforms.set('x', 'translateX');\nshortTransforms.set('y', 'translateY');\nshortTransforms.set('z', 'translateZ');\n\nconst validTransforms = [\n  'translateX',\n  'translateY',\n  'translateZ',\n  'rotate',\n  'rotateX',\n  'rotateY',\n  'rotateZ',\n  'scale',\n  'scaleX',\n  'scaleY',\n  'scaleZ',\n  'skew',\n  'skewX',\n  'skewY',\n  'perspective',\n  'matrix',\n  'matrix3d',\n];\n\nconst transformsFragmentStrings = validTransforms.reduce((a, v) => ({...a, [v]: v + '('}), {});\n\n// Functions\n\n/** @return {void} */\nconst noop = () => {};\n\n// Regex\n\nconst hexTestRgx = /(^#([\\da-f]{3}){1,2}$)|(^#([\\da-f]{4}){1,2}$)/i;\nconst rgbExecRgx = /rgb\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)/i;\nconst rgbaExecRgx = /rgba\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(-?\\d+|-?\\d*.\\d+)\\s*\\)/i;\nconst hslExecRgx = /hsl\\(\\s*(-?\\d+|-?\\d*.\\d+)\\s*,\\s*(-?\\d+|-?\\d*.\\d+)%\\s*,\\s*(-?\\d+|-?\\d*.\\d+)%\\s*\\)/i;\nconst hslaExecRgx = /hsla\\(\\s*(-?\\d+|-?\\d*.\\d+)\\s*,\\s*(-?\\d+|-?\\d*.\\d+)%\\s*,\\s*(-?\\d+|-?\\d*.\\d+)%\\s*,\\s*(-?\\d+|-?\\d*.\\d+)\\s*\\)/i;\n// export const digitWithExponentRgx = /[-+]?\\d*\\.?\\d+(?:[eE][-+]?\\d+)?/g;\nconst digitWithExponentRgx = /[-+]?\\d*\\.?\\d+(?:e[-+]?\\d)?/gi;\n// export const unitsExecRgx = /^([-+]?\\d*\\.?\\d+(?:[eE][-+]?\\d+)?)+([a-z]+|%)$/i;\nconst unitsExecRgx = /^([-+]?\\d*\\.?\\d+(?:e[-+]?\\d+)?)([a-z]+|%)$/i;\nconst lowerCaseRgx = /([a-z])([A-Z])/g;\nconst transformsExecRgx = /(\\w+)(\\([^)]+\\)+)/g; // Match inline transforms with cacl() values, returns the value wrapped in ()\nconst relativeValuesExecRgx = /(\\*=|\\+=|-=)/;\n\n\n\n\n/** @type {DefaultsParams} */\nconst defaults = {\n  id: null,\n  keyframes: null,\n  playbackEase: null,\n  playbackRate: 1,\n  frameRate: maxFps,\n  loop: 0,\n  reversed: false,\n  alternate: false,\n  autoplay: true,\n  duration: K,\n  delay: 0,\n  loopDelay: 0,\n  ease: 'out(2)',\n  composition: compositionTypes.replace,\n  modifier: v => v,\n  onBegin: noop,\n  onBeforeUpdate: noop,\n  onUpdate: noop,\n  onLoop: noop,\n  onPause: noop,\n  onComplete: noop,\n  onRender: noop,\n};\n\nconst globals = {\n  /** @type {DefaultsParams} */\n  defaults,\n  /** @type {Document|DOMTarget} */\n  root: doc,\n  /** @type {Scope} */\n  scope: null,\n  /** @type {Number} */\n  precision: 4,\n  /** @type {Number} */\n  timeScale: 1,\n  /** @type {Number} */\n  tickThreshold: 200,\n};\n\nconst globalVersions = { version: '4.0.2', engine: null };\n\nif (isBrowser) {\n  if (!win.AnimeJS) win.AnimeJS = [];\n  win.AnimeJS.push(globalVersions);\n}\n\n// Strings\n\n/**\n * @param  {String} str\n * @return {String}\n */\nconst toLowerCase = str => str.replace(lowerCaseRgx, '$1-$2').toLowerCase();\n\n/**\n * Prioritize this method instead of regex when possible\n * @param  {String} str\n * @param  {String} sub\n * @return {Boolean}\n */\nconst stringStartsWith = (str, sub) => str.indexOf(sub) === 0;\n\n// Time\n// Note: Date.now is used instead of performance.now since it is precise enough for timings calculations, performs slightly faster and works in Node.js environement.\nconst now = Date.now;\n\n// Types checkers\n\nconst isArr = Array.isArray;\n/**@param {any} a @return {a is Record<String, any>} */\nconst isObj = a => a && a.constructor === Object;\n/**@param {any} a @return {a is Number} */\nconst isNum = a => typeof a === 'number' && !isNaN(a);\n/**@param {any} a @return {a is String} */\nconst isStr = a => typeof a === 'string';\n/**@param {any} a @return {a is Function} */\nconst isFnc = a => typeof a === 'function';\n/**@param {any} a @return {a is undefined} */\nconst isUnd = a => typeof a === 'undefined';\n/**@param {any} a @return {a is null | undefined} */\nconst isNil = a => isUnd(a) || a === null;\n/**@param {any} a @return {a is SVGElement} */\nconst isSvg = a => isBrowser && a instanceof SVGElement;\n/**@param {any} a @return {Boolean} */\nconst isHex = a => hexTestRgx.test(a);\n/**@param {any} a @return {Boolean} */\nconst isRgb = a => stringStartsWith(a, 'rgb');\n/**@param {any} a @return {Boolean} */\nconst isHsl = a => stringStartsWith(a, 'hsl');\n/**@param {any} a @return {Boolean} */\nconst isCol = a => isHex(a) || isRgb(a) || isHsl(a);\n/**@param {any} a @return {Boolean} */\nconst isKey = a => !globals.defaults.hasOwnProperty(a);\n\n// Number\n\n/**\n * @param  {Number|String} str\n * @return {Number}\n */\nconst parseNumber = str => isStr(str) ?\n  parseFloat(/** @type {String} */(str)) :\n  /** @type {Number} */(str);\n\n// Math\n\nconst pow = Math.pow;\nconst sqrt = Math.sqrt;\nconst sin = Math.sin;\nconst cos = Math.cos;\nconst abs = Math.abs;\nconst exp = Math.exp;\nconst ceil = Math.ceil;\nconst floor = Math.floor;\nconst asin = Math.asin;\nconst max = Math.max;\nconst atan2 = Math.atan2;\nconst PI = Math.PI;\nconst _round = Math.round;\n\n/**\n * @param  {Number} v\n * @param  {Number} min\n * @param  {Number} max\n * @return {Number}\n */\nconst clamp = (v, min, max) => v < min ? min : v > max ? max : v;\n\nconst powCache = {};\n\n/**\n * @param  {Number} v\n * @param  {Number} decimalLength\n * @return {Number}\n */\nconst round = (v, decimalLength) => {\n  if (decimalLength < 0) return v;\n  if (!decimalLength) return _round(v);\n  let p = powCache[decimalLength];\n  if (!p) p = powCache[decimalLength] = 10 ** decimalLength;\n  return _round(v * p) / p;\n};\n\n/**\n * @param  {Number} v\n * @param  {Number|Array<Number>} increment\n * @return {Number}\n */\nconst snap = (v, increment) => isArr(increment) ? increment.reduce((closest, cv) => (abs(cv - v) < abs(closest - v) ? cv : closest)) : increment ? _round(v / increment) * increment : v;\n\n/**\n * @param  {Number} start\n * @param  {Number} end\n * @param  {Number} progress\n * @return {Number}\n */\nconst interpolate = (start, end, progress) => start + (end - start) * progress;\n\n/**\n * @param  {Number} v\n * @return {Number}\n */\nconst clampInfinity = v => v === Infinity ? maxValue : v === -Infinity ? -1e12 : v;\n\n/**\n * @param  {Number} v\n * @return {Number}\n */\nconst normalizeTime = v => v <= minValue ? minValue : clampInfinity(round(v, 11));\n\n// Arrays\n\n/**\n * @template T\n * @param {T[]} a\n * @return {T[]}\n */\nconst cloneArray = a => isArr(a) ? [ ...a ] : a;\n\n// Objects\n\n/**\n * @template T\n * @template U\n * @param {T} o1\n * @param {U} o2\n * @return {T & U}\n */\nconst mergeObjects = (o1, o2) => {\n  const merged = /** @type {T & U} */({ ...o1 });\n  for (let p in o2) {\n    const o1p = /** @type {T & U} */(o1)[p];\n    merged[p] = isUnd(o1p) ? /** @type {T & U} */(o2)[p] : o1p;\n  }  return merged;\n};\n\n// Linked lists\n\n/**\n * @param {Object} parent\n * @param {Function} callback\n * @param {Boolean} [reverse]\n * @param {String} [prevProp]\n * @param {String} [nextProp]\n * @return {void}\n */\nconst forEachChildren = (parent, callback, reverse, prevProp = '_prev', nextProp = '_next') => {\n  let next = parent._head;\n  let adjustedNextProp = nextProp;\n  if (reverse) {\n    next = parent._tail;\n    adjustedNextProp = prevProp;\n  }\n  while (next) {\n    const currentNext = next[adjustedNextProp];\n    callback(next);\n    next = currentNext;\n  }\n};\n\n/**\n * @param  {Object} parent\n * @param  {Object} child\n * @param  {String} [prevProp]\n * @param  {String} [nextProp]\n * @return {void}\n */\nconst removeChild = (parent, child, prevProp = '_prev', nextProp = '_next') => {\n  const prev = child[prevProp];\n  const next = child[nextProp];\n  prev ? prev[nextProp] = next : parent._head = next;\n  next ? next[prevProp] = prev : parent._tail = prev;\n  child[prevProp] = null;\n  child[nextProp] = null;\n};\n\n/**\n * @param  {Object} parent\n * @param  {Object} child\n * @param  {Function} [sortMethod]\n * @param  {String} prevProp\n * @param  {String} nextProp\n * @return {void}\n */\nconst addChild = (parent, child, sortMethod, prevProp = '_prev', nextProp = '_next') => {\n  let prev = parent._tail;\n  while (prev && sortMethod && sortMethod(prev, child)) prev = prev[prevProp];\n  const next = prev ? prev[nextProp] : parent._head;\n  prev ? prev[nextProp] = child : parent._head = child;\n  next ? next[prevProp] = child : parent._tail = child;\n  child[prevProp] = prev;\n  child[nextProp] = next;\n};\n\n/*\n * Base class to control framerate and playback rate.\n * Inherited by Engine, Timer, Animation and Timeline.\n */\nclass Clock {\n\n  /** @param {Number} [initTime] */\n  constructor(initTime = 0) {\n    /** @type {Number} */\n    this.deltaTime = 0;\n    /** @type {Number} */\n    this._currentTime = initTime;\n    /** @type {Number} */\n    this._elapsedTime = initTime;\n    /** @type {Number} */\n    this._startTime = initTime;\n    /** @type {Number} */\n    this._lastTime = initTime;\n    /** @type {Number} */\n    this._scheduledTime = 0;\n    /** @type {Number} */\n    this._frameDuration = round(K / maxFps, 0);\n    /** @type {Number} */\n    this._fps = maxFps;\n    /** @type {Number} */\n    this._speed = 1;\n    /** @type {Boolean} */\n    this._hasChildren = false;\n    /** @type {Tickable|Tween} */\n    this._head = null;\n    /** @type {Tickable|Tween} */\n    this._tail = null;\n  }\n\n  get fps() {\n    return this._fps;\n  }\n\n  set fps(frameRate) {\n    const previousFrameDuration = this._frameDuration;\n    const fr = +frameRate;\n    const fps = fr < minValue ? minValue : fr;\n    const frameDuration = round(K / fps, 0);\n    this._fps = fps;\n    this._frameDuration = frameDuration;\n    this._scheduledTime += frameDuration - previousFrameDuration;\n  }\n\n  get speed() {\n    return this._speed;\n  }\n\n  set speed(playbackRate) {\n    const pbr = +playbackRate;\n    this._speed = pbr < minValue ? minValue : pbr;\n  }\n\n  /**\n   * @param  {Number} time\n   * @return {tickModes}\n   */\n  requestTick(time) {\n    const scheduledTime = this._scheduledTime;\n    const elapsedTime = this._elapsedTime;\n    this._elapsedTime += (time - elapsedTime);\n    // If the elapsed time is lower than the scheduled time\n    // this means not enough time has passed to hit one frameDuration\n    // so skip that frame\n    if (elapsedTime < scheduledTime) return tickModes.NONE;\n    const frameDuration = this._frameDuration;\n    const frameDelta = elapsedTime - scheduledTime;\n    // Ensures that _scheduledTime progresses in steps of at least 1 frameDuration.\n    // Skips ahead if the actual elapsed time is higher.\n    this._scheduledTime += frameDelta < frameDuration ? frameDuration : frameDelta;\n    return tickModes.AUTO;\n  }\n\n  /**\n   * @param  {Number} time\n   * @return {Number}\n   */\n  computeDeltaTime(time) {\n    const delta = time - this._lastTime;\n    this.deltaTime = delta;\n    this._lastTime = time;\n    return delta;\n  }\n\n}\n\n\n\n\n/**\n * @param  {Tickable} tickable\n * @param  {Number} time\n * @param  {Number} muteCallbacks\n * @param  {Number} internalRender\n * @param  {tickModes} tickMode\n * @return {Number}\n */\nconst render = (tickable, time, muteCallbacks, internalRender, tickMode) => {\n\n  const parent = tickable.parent;\n  const duration = tickable.duration;\n  const completed = tickable.completed;\n  const iterationDuration = tickable.iterationDuration;\n  const iterationCount = tickable.iterationCount;\n  const _currentIteration = tickable._currentIteration;\n  const _loopDelay = tickable._loopDelay;\n  const _reversed = tickable._reversed;\n  const _alternate = tickable._alternate;\n  const _hasChildren = tickable._hasChildren;\n  const tickableDelay = tickable._delay;\n  const tickablePrevAbsoluteTime = tickable._currentTime; // TODO: rename ._currentTime to ._absoluteCurrentTime\n\n  const tickableEndTime = tickableDelay + iterationDuration;\n  const tickableAbsoluteTime = time - tickableDelay;\n  const tickablePrevTime = clamp(tickablePrevAbsoluteTime, -tickableDelay, duration);\n  const tickableCurrentTime = clamp(tickableAbsoluteTime, -tickableDelay, duration);\n  const deltaTime = tickableAbsoluteTime - tickablePrevAbsoluteTime;\n  const isCurrentTimeAboveZero = tickableCurrentTime > 0;\n  const isCurrentTimeEqualOrAboveDuration = tickableCurrentTime >= duration;\n  const isSetter = duration <= minValue;\n  const forcedTick = tickMode === tickModes.FORCE;\n\n  let isOdd = 0;\n  let iterationElapsedTime = tickableAbsoluteTime;\n  // Render checks\n  // Used to also check if the children have rendered in order to trigger the onRender callback on the parent timer\n  let hasRendered = 0;\n\n  // Execute the \"expensive\" iterations calculations only when necessary\n  if (iterationCount > 1) {\n    // bitwise NOT operator seems to be generally faster than Math.floor() across browsers\n    const currentIteration = ~~(tickableCurrentTime / (iterationDuration + (isCurrentTimeEqualOrAboveDuration ? 0 : _loopDelay)));\n    tickable._currentIteration = clamp(currentIteration, 0, iterationCount);\n    // Prevent the iteration count to go above the max iterations when reaching the end of the animation\n    if (isCurrentTimeEqualOrAboveDuration) tickable._currentIteration--;\n    isOdd = tickable._currentIteration % 2;\n    iterationElapsedTime = tickableCurrentTime % (iterationDuration + _loopDelay) || 0;\n  }\n\n  // Checks if exactly one of _reversed and (_alternate && isOdd) is true\n  const isReversed = _reversed ^ (_alternate && isOdd);\n  const _ease = /** @type {Renderable} */(tickable)._ease;\n  let iterationTime = isCurrentTimeEqualOrAboveDuration ? isReversed ? 0 : duration : isReversed ? iterationDuration - iterationElapsedTime : iterationElapsedTime;\n  if (_ease) iterationTime = iterationDuration * _ease(iterationTime / iterationDuration) || 0;\n  const isRunningBackwards = (parent ? parent.backwards : tickableAbsoluteTime < tickablePrevAbsoluteTime) ? !isReversed : !!isReversed;\n\n  tickable._currentTime = tickableAbsoluteTime;\n  tickable._iterationTime = iterationTime;\n  tickable.backwards = isRunningBackwards;\n\n  if (isCurrentTimeAboveZero && !tickable.began) {\n    tickable.began = true;\n    if (!muteCallbacks && !(parent && (isRunningBackwards || !parent.began))) {\n      tickable.onBegin(/** @type {CallbackArgument} */(tickable));\n    }\n  } else if (tickableAbsoluteTime <= 0) {\n    tickable.began = false;\n  }\n\n  // Only triggers onLoop for tickable without children, otherwise call the the onLoop callback in the tick function\n  // Make sure to trigger the onLoop before rendering to allow .refresh() to pickup the current values\n  if (!muteCallbacks && !_hasChildren && isCurrentTimeAboveZero && tickable._currentIteration !== _currentIteration) {\n    tickable.onLoop(/** @type {CallbackArgument} */(tickable));\n  }\n\n  if (\n    forcedTick ||\n    tickMode === tickModes.AUTO && (\n      time >= tickableDelay && time <= tickableEndTime || // Normal render\n      time <= tickableDelay && tickablePrevTime > tickableDelay || // Playhead is before the animation start time so make sure the animation is at its initial state\n      time >= tickableEndTime && tickablePrevTime !== duration // Playhead is after the animation end time so make sure the animation is at its end state\n    ) ||\n    iterationTime >= tickableEndTime && tickablePrevTime !== duration ||\n    iterationTime <= tickableDelay && tickablePrevTime > 0 ||\n    time <= tickablePrevTime && tickablePrevTime === duration && completed || // Force a render if a seek occurs on an completed animation\n    isCurrentTimeEqualOrAboveDuration && !completed && isSetter // This prevents 0 duration tickables to be skipped\n  ) {\n\n    if (isCurrentTimeAboveZero) {\n      // Trigger onUpdate callback before rendering\n      tickable.computeDeltaTime(tickablePrevTime);\n      if (!muteCallbacks) tickable.onBeforeUpdate(/** @type {CallbackArgument} */(tickable));\n    }\n\n    // Start tweens rendering\n    if (!_hasChildren) {\n\n      // Time has jumped more than globals.tickThreshold so consider this tick manual\n      const forcedRender = forcedTick || (isRunningBackwards ? deltaTime * -1 : deltaTime) >= globals.tickThreshold;\n      const absoluteTime = tickable._offset + (parent ? parent._offset : 0) + tickableDelay + iterationTime;\n\n      // Only Animation can have tweens, Timer returns undefined\n      let tween = /** @type {Tween} */(/** @type {JSAnimation} */(tickable)._head);\n      let tweenTarget;\n      let tweenStyle;\n      let tweenTargetTransforms;\n      let tweenTargetTransformsProperties;\n      let tweenTransformsNeedUpdate = 0;\n\n      while (tween) {\n\n        const tweenComposition = tween._composition;\n        const tweenCurrentTime = tween._currentTime;\n        const tweenChangeDuration = tween._changeDuration;\n        const tweenAbsEndTime = tween._absoluteStartTime + tween._changeDuration;\n        const tweenNextRep = tween._nextRep;\n        const tweenPrevRep = tween._prevRep;\n        const tweenHasComposition = tweenComposition !== compositionTypes.none;\n\n        if ((forcedRender || (\n            (tweenCurrentTime !== tweenChangeDuration || absoluteTime <= tweenAbsEndTime + (tweenNextRep ? tweenNextRep._delay : 0)) &&\n            (tweenCurrentTime !== 0 || absoluteTime >= tween._absoluteStartTime)\n          )) && (!tweenHasComposition || (\n            !tween._isOverridden &&\n            (!tween._isOverlapped || absoluteTime <= tweenAbsEndTime) &&\n            (!tweenNextRep || (tweenNextRep._isOverridden || absoluteTime <= tweenNextRep._absoluteStartTime)) &&\n            (!tweenPrevRep || (tweenPrevRep._isOverridden || (absoluteTime >= (tweenPrevRep._absoluteStartTime + tweenPrevRep._changeDuration) + tween._delay)))\n          ))\n        ) {\n\n          const tweenNewTime = tween._currentTime = clamp(iterationTime - tween._startTime, 0, tweenChangeDuration);\n          const tweenProgress = tween._ease(tweenNewTime / tween._updateDuration);\n          const tweenModifier = tween._modifier;\n          const tweenValueType = tween._valueType;\n          const tweenType = tween._tweenType;\n          const tweenIsObject = tweenType === tweenTypes.OBJECT;\n          const tweenIsNumber = tweenValueType === valueTypes.NUMBER;\n          // Only round the in-between frames values if the final value is a string\n          const tweenPrecision = (tweenIsNumber && tweenIsObject) || tweenProgress === 0 || tweenProgress === 1 ? -1 : globals.precision;\n\n          // Recompose tween value\n          /** @type {String|Number} */\n          let value;\n          /** @type {Number} */\n          let number;\n\n          if (tweenIsNumber) {\n            value = number = /** @type {Number} */(tweenModifier(round(interpolate(tween._fromNumber, tween._toNumber,  tweenProgress), tweenPrecision )));\n          } else if (tweenValueType === valueTypes.UNIT) {\n            // Rounding the values speed up string composition\n            number = /** @type {Number} */(tweenModifier(round(interpolate(tween._fromNumber, tween._toNumber,  tweenProgress), tweenPrecision)));\n            value = `${number}${tween._unit}`;\n          } else if (tweenValueType === valueTypes.COLOR) {\n            const fn = tween._fromNumbers;\n            const tn = tween._toNumbers;\n            const r = round(clamp(/** @type {Number} */(tweenModifier(interpolate(fn[0], tn[0], tweenProgress))), 0, 255), 0);\n            const g = round(clamp(/** @type {Number} */(tweenModifier(interpolate(fn[1], tn[1], tweenProgress))), 0, 255), 0);\n            const b = round(clamp(/** @type {Number} */(tweenModifier(interpolate(fn[2], tn[2], tweenProgress))), 0, 255), 0);\n            const a = clamp(/** @type {Number} */(tweenModifier(round(interpolate(fn[3], tn[3], tweenProgress), tweenPrecision))), 0, 1);\n            value = `rgba(${r},${g},${b},${a})`;\n            if (tweenHasComposition) {\n              const ns = tween._numbers;\n              ns[0] = r;\n              ns[1] = g;\n              ns[2] = b;\n              ns[3] = a;\n            }\n          } else if (tweenValueType === valueTypes.COMPLEX) {\n            value = tween._strings[0];\n            for (let j = 0, l = tween._toNumbers.length; j < l; j++) {\n              const n = /** @type {Number} */(tweenModifier(round(interpolate(tween._fromNumbers[j], tween._toNumbers[j], tweenProgress), tweenPrecision)));\n              const s = tween._strings[j + 1];\n              value += `${s ? n + s : n}`;\n              if (tweenHasComposition) {\n                tween._numbers[j] = n;\n              }\n            }\n          }\n\n          // For additive tweens and Animatables\n          if (tweenHasComposition) {\n            tween._number = number;\n          }\n\n          if (!internalRender && tweenComposition !== compositionTypes.blend) {\n\n            const tweenProperty = tween.property;\n            tweenTarget = tween.target;\n\n            if (tweenIsObject) {\n              tweenTarget[tweenProperty] = value;\n            } else if (tweenType === tweenTypes.ATTRIBUTE) {\n              /** @type {DOMTarget} */(tweenTarget).setAttribute(tweenProperty, /** @type {String} */(value));\n            } else {\n              tweenStyle = /** @type {DOMTarget} */(tweenTarget).style;\n              if (tweenType === tweenTypes.TRANSFORM) {\n                if (tweenTarget !== tweenTargetTransforms) {\n                  tweenTargetTransforms = tweenTarget;\n                  // NOTE: Referencing the cachedTransforms in the tween property directly can be a little bit faster but appears to increase memory usage.\n                  tweenTargetTransformsProperties = tweenTarget[transformsSymbol];\n                }\n                tweenTargetTransformsProperties[tweenProperty] = value;\n                tweenTransformsNeedUpdate = 1;\n              } else if (tweenType === tweenTypes.CSS) {\n                tweenStyle[tweenProperty] = value;\n              } else if (tweenType === tweenTypes.CSS_VAR) {\n                tweenStyle.setProperty(tweenProperty,/** @type {String} */(value));\n              }\n            }\n\n            if (isCurrentTimeAboveZero) hasRendered = 1;\n\n          } else {\n            // Used for composing timeline tweens without having to do a real render\n            tween._value = value;\n          }\n\n        }\n\n        // NOTE: Possible improvement: Use translate(x,y) / translate3d(x,y,z) syntax\n        // to reduce memory usage on string composition\n        if (tweenTransformsNeedUpdate && tween._renderTransforms) {\n          let str = emptyString;\n          for (let key in tweenTargetTransformsProperties) {\n            str += `${transformsFragmentStrings[key]}${tweenTargetTransformsProperties[key]}) `;\n          }\n          tweenStyle.transform = str;\n          tweenTransformsNeedUpdate = 0;\n        }\n\n        tween = tween._next;\n      }\n\n      if (!muteCallbacks && hasRendered) {\n        /** @type {JSAnimation} */(tickable).onRender(/** @type {JSAnimation} */(tickable));\n      }\n    }\n\n    if (!muteCallbacks && isCurrentTimeAboveZero) {\n      tickable.onUpdate(/** @type {CallbackArgument} */(tickable));\n    }\n\n  }\n\n  // End tweens rendering\n\n  // Handle setters on timeline differently and allow re-trigering the onComplete callback when seeking backwards\n  if (parent && isSetter) {\n    if (!muteCallbacks && (\n      (parent.began && !isRunningBackwards && tickableAbsoluteTime >= duration && !completed) ||\n      (isRunningBackwards && tickableAbsoluteTime <= minValue && completed)\n    )) {\n      tickable.onComplete(/** @type {CallbackArgument} */(tickable));\n      tickable.completed = !isRunningBackwards;\n    }\n  // If currentTime is both above 0 and at least equals to duration, handles normal onComplete or infinite loops\n  } else if (isCurrentTimeAboveZero && isCurrentTimeEqualOrAboveDuration) {\n    if (iterationCount === Infinity) {\n      // Offset the tickable _startTime with its duration to reset _currentTime to 0 and continue the infinite timer\n      tickable._startTime += tickable.duration;\n    } else if (tickable._currentIteration >= iterationCount - 1) {\n      // By setting paused to true, we tell the engine loop to not render this tickable and removes it from the list on the next tick\n      tickable.paused = true;\n      if (!completed && !_hasChildren) {\n        // If the tickable has children, triggers onComplete() only when all children have completed in the tick function\n        tickable.completed = true;\n        if (!muteCallbacks && !(parent && (isRunningBackwards || !parent.began))) {\n          tickable.onComplete(/** @type {CallbackArgument} */(tickable));\n          tickable._resolve(/** @type {CallbackArgument} */(tickable));\n        }\n      }\n    }\n  // Otherwise set the completed flag to false\n  } else {\n    tickable.completed = false;\n  }\n\n  // NOTE: hasRendered * direction (negative for backwards) this way we can remove the tickable.backwards property completly ?\n  return hasRendered;\n};\n\n/**\n * @param  {Tickable} tickable\n * @param  {Number} time\n * @param  {Number} muteCallbacks\n * @param  {Number} internalRender\n * @param  {Number} tickMode\n * @return {void}\n */\nconst tick = (tickable, time, muteCallbacks, internalRender, tickMode) => {\n  const _currentIteration = tickable._currentIteration;\n  render(tickable, time, muteCallbacks, internalRender, tickMode);\n  if (tickable._hasChildren) {\n    const tl = /** @type {Timeline} */(tickable);\n    const tlIsRunningBackwards = tl.backwards;\n    const tlChildrenTime = internalRender ? time : tl._iterationTime;\n    const tlCildrenTickTime = now();\n\n    let tlChildrenHasRendered = 0;\n    let tlChildrenHaveCompleted = true;\n\n    // If the timeline has looped forward, we need to manually triggers children skipped callbacks\n    if (!internalRender && tl._currentIteration !== _currentIteration) {\n      const tlIterationDuration = tl.iterationDuration;\n      forEachChildren(tl, (/** @type {JSAnimation} */child) => {\n        if (!tlIsRunningBackwards) {\n          // Force an internal render to trigger the callbacks if the child has not completed on loop\n          if (!child.completed && !child.backwards && child._currentTime < child.iterationDuration) {\n            render(child, tlIterationDuration, muteCallbacks, 1, tickModes.FORCE);\n          }\n          // Reset their began and completed flags to allow retrigering callbacks on the next iteration\n          child.began = false;\n          child.completed = false;\n        } else {\n          const childDuration = child.duration;\n          const childStartTime = child._offset + child._delay;\n          const childEndTime = childStartTime + childDuration;\n          // Triggers the onComplete callback on reverse for children on the edges of the timeline\n          if (!muteCallbacks && childDuration <= minValue && (!childStartTime || childEndTime === tlIterationDuration)) {\n            child.onComplete(child);\n          }\n        }\n      });\n      if (!muteCallbacks) tl.onLoop(/** @type {CallbackArgument} */(tl));\n    }\n\n    forEachChildren(tl, (/** @type {JSAnimation} */child) => {\n      const childTime = round((tlChildrenTime - child._offset) * child._speed, 12); // Rounding is needed when using seconds\n      const childTickMode = child._fps < tl._fps ? child.requestTick(tlCildrenTickTime) : tickMode;\n      tlChildrenHasRendered += render(child, childTime, muteCallbacks, internalRender, childTickMode);\n      if (!child.completed && tlChildrenHaveCompleted) tlChildrenHaveCompleted = false;\n    }, tlIsRunningBackwards);\n\n    // Renders on timeline are triggered by its children so it needs to be set after rendering the children\n    if (!muteCallbacks && tlChildrenHasRendered) tl.onRender(/** @type {CallbackArgument} */(tl));\n\n    // Triggers the timeline onComplete() once all chindren all completed and the current time has reached the end\n    if (tlChildrenHaveCompleted && tl._currentTime >= tl.duration) {\n      // Make sure the paused flag is false in case it has been skipped in the render function\n      tl.paused = true;\n      if (!tl.completed) {\n        tl.completed = true;\n        if (!muteCallbacks) {\n          tl.onComplete(/** @type {CallbackArgument} */(tl));\n          tl._resolve(/** @type {CallbackArgument} */(tl));\n        }\n      }\n    }\n  }\n};\n\n\n\n\nconst additive = {\n  animation: null,\n  update: noop,\n};\n\n/**\n * @typedef AdditiveAnimation\n * @property {Number} duration\n * @property {Number} _offset\n * @property {Number} _delay\n * @property {Tween} _head\n * @property {Tween} _tail\n */\n\n/**\n * @param  {TweenAdditiveLookups} lookups\n * @return {AdditiveAnimation}\n */\nconst addAdditiveAnimation = lookups => {\n  let animation = additive.animation;\n  if (!animation) {\n    animation = {\n      duration: minValue,\n      computeDeltaTime: noop,\n      _offset: 0,\n      _delay: 0,\n      _head: null,\n      _tail: null,\n    };\n    additive.animation = animation;\n    additive.update = () => {\n      lookups.forEach(propertyAnimation => {\n        for (let propertyName in propertyAnimation) {\n          const tweens = propertyAnimation[propertyName];\n          const lookupTween = tweens._head;\n          if (lookupTween) {\n            const valueType = lookupTween._valueType;\n            const additiveValues = valueType === valueTypes.COMPLEX || valueType === valueTypes.COLOR ? cloneArray(lookupTween._fromNumbers) : null;\n            let additiveValue = lookupTween._fromNumber;\n            let tween = tweens._tail;\n            while (tween && tween !== lookupTween) {\n              if (additiveValues) {\n                for (let i = 0, l = tween._numbers.length; i < l; i++) additiveValues[i] += tween._numbers[i];\n              } else {\n                additiveValue += tween._number;\n              }\n              tween = tween._prevAdd;\n            }\n            lookupTween._toNumber = additiveValue;\n            lookupTween._toNumbers = additiveValues;\n          }\n        }\n      });\n      // TODO: Avoid polymorphism here, idealy the additive animation should be a regular animation with a higher priority in the render loop\n      render(animation, 1, 1, 0, tickModes.FORCE);\n    };\n  }\n  return animation;\n};\n\nconst engineTickMethod = isBrowser ? requestAnimationFrame : setImmediate;\nconst engineCancelMethod = isBrowser ? cancelAnimationFrame : clearImmediate;\n\nclass Engine extends Clock {\n\n  /** @param {Number} [initTime] */\n  constructor(initTime) {\n    super(initTime);\n    this.useDefaultMainLoop = true;\n    this.pauseOnDocumentHidden = true;\n    /** @type {DefaultsParams} */\n    this.defaults = defaults;\n    this.paused = isBrowser && doc.hidden ? true  : false;\n    /** @type {Number|NodeJS.Immediate} */\n    this.reqId = null;\n  }\n\n  update() {\n    const time = this._currentTime = now();\n    if (this.requestTick(time)) {\n      this.computeDeltaTime(time);\n      const engineSpeed = this._speed;\n      const engineFps = this._fps;\n      let activeTickable = /** @type {Tickable} */(this._head);\n      while (activeTickable) {\n        const nextTickable = activeTickable._next;\n        if (!activeTickable.paused) {\n          tick(\n            activeTickable,\n            (time - activeTickable._startTime) * activeTickable._speed * engineSpeed,\n            0, // !muteCallbacks\n            0, // !internalRender\n            activeTickable._fps < engineFps ? activeTickable.requestTick(time) : tickModes.AUTO\n          );\n        } else {\n          removeChild(this, activeTickable);\n          this._hasChildren = !!this._tail;\n          activeTickable._running = false;\n          if (activeTickable.completed && !activeTickable._cancelled) {\n            activeTickable.cancel();\n          }\n        }\n        activeTickable = nextTickable;\n      }\n      additive.update();\n    }\n  }\n\n  wake() {\n    if (this.useDefaultMainLoop && !this.reqId && !this.paused) {\n      this.reqId = engineTickMethod(tickEngine);\n    }\n    return this;\n  }\n\n  pause() {\n    this.paused = true;\n    return killEngine();\n  }\n\n  resume() {\n    if (!this.paused) return;\n    this.paused = false;\n    forEachChildren(this, (/** @type {Tickable} */child) => child.resetTime());\n    return this.wake();\n  }\n\n  // Getter and setter for speed\n  get speed() {\n    return this._speed * (globals.timeScale === 1 ? 1 : K);\n  }\n\n  set speed(playbackRate) {\n    this._speed = playbackRate * globals.timeScale;\n    forEachChildren(this, (/** @type {Tickable} */child) => child.speed = child._speed);\n  }\n\n  // Getter and setter for timeUnit\n  get timeUnit() {\n    return globals.timeScale === 1 ? 'ms' : 's';\n  };\n\n  set timeUnit(unit) {\n    const secondsScale = 0.001;\n    const isSecond = unit === 's';\n    const newScale = isSecond ? secondsScale : 1;\n    if (globals.timeScale !== newScale) {\n      globals.timeScale = newScale;\n      globals.tickThreshold = 200 * newScale;\n      const scaleFactor = isSecond ? secondsScale : K;\n      /** @type {Number} */\n      (this.defaults.duration) *= scaleFactor;\n      this._speed *= scaleFactor;\n    }\n  }\n\n  // Getter and setter for precision\n  get precision() {\n    return globals.precision;\n  }\n\n  set precision(precision) {\n    globals.precision = precision;\n  }\n\n}\nconst engine = /*#__PURE__*/(() => {\n  const engine = new Engine(now());\n  if (isBrowser) {\n    globalVersions.engine = engine;\n    doc.addEventListener('visibilitychange', () => {\n      if (!engine.pauseOnDocumentHidden) return;\n      doc.hidden ? engine.pause() : engine.resume();\n    });\n  }\n  return engine;\n})();\n\n\nconst tickEngine = () => {\n  if (engine._head) {\n    engine.reqId = engineTickMethod(tickEngine);\n    engine.update();\n  } else {\n    engine.reqId = 0;\n  }\n};\n\nconst killEngine = () => {\n  engineCancelMethod(/** @type {NodeJS.Immediate & Number} */(engine.reqId));\n  engine.reqId = 0;\n  return engine;\n};\n\n\n\n\n/**\n * @param  {DOMTarget} target\n * @param  {String} propName\n * @param  {Object} animationInlineStyles\n * @return {String}\n */\nconst parseInlineTransforms = (target, propName, animationInlineStyles) => {\n  const inlineTransforms = target.style.transform;\n  let inlinedStylesPropertyValue;\n  if (inlineTransforms) {\n    const cachedTransforms = target[transformsSymbol];\n    let t; while (t = transformsExecRgx.exec(inlineTransforms)) {\n      const inlinePropertyName = t[1];\n      // const inlinePropertyValue = t[2];\n      const inlinePropertyValue = t[2].slice(1, -1);\n      cachedTransforms[inlinePropertyName] = inlinePropertyValue;\n      if (inlinePropertyName === propName) {\n        inlinedStylesPropertyValue = inlinePropertyValue;\n        // Store the new parsed inline styles if animationInlineStyles is provided\n        if (animationInlineStyles) {\n          animationInlineStyles[propName] = inlinePropertyValue;\n        }\n      }\n    }\n  }\n  return inlineTransforms && !isUnd(inlinedStylesPropertyValue) ? inlinedStylesPropertyValue :\n    stringStartsWith(propName, 'scale') ? '1' :\n    stringStartsWith(propName, 'rotate') || stringStartsWith(propName, 'skew') ? '0deg' : '0px';\n};\n\n\n\n\n/**\n * @param  {DOMTargetsParam|TargetsParam} v\n * @return {NodeList|HTMLCollection}\n */\nfunction getNodeList(v) {\n  const n = isStr(v) ? globals.root.querySelectorAll(v) : v;\n  if (n instanceof NodeList || n instanceof HTMLCollection) return n;\n}\n\n/**\n * @overload\n * @param  {DOMTargetsParam} targets\n * @return {DOMTargetsArray}\n *\n * @overload\n * @param  {JSTargetsParam} targets\n * @return {JSTargetsArray}\n *\n * @overload\n * @param  {TargetsParam} targets\n * @return {TargetsArray}\n *\n * @param  {DOMTargetsParam|JSTargetsParam|TargetsParam} targets\n */\nfunction parseTargets(targets) {\n  if (isNil(targets)) return /** @type {TargetsArray} */([]);\n  if (isArr(targets)) {\n    const flattened = targets.flat(Infinity);\n    /** @type {TargetsArray} */\n    const parsed = [];\n    for (let i = 0, l = flattened.length; i < l; i++) {\n      const item = flattened[i];\n      if (!isNil(item)) {\n        const nodeList = getNodeList(item);\n        if (nodeList) {\n          for (let j = 0, jl = nodeList.length; j < jl; j++) {\n            const subItem = nodeList[j];\n            if (!isNil(subItem)) {\n              let isDuplicate = false;\n              for (let k = 0, kl = parsed.length; k < kl; k++) {\n                if (parsed[k] === subItem) {\n                  isDuplicate = true;\n                  break;\n                }\n              }\n              if (!isDuplicate) {\n                parsed.push(subItem);\n              }\n            }\n          }\n        } else {\n          let isDuplicate = false;\n          for (let j = 0, jl = parsed.length; j < jl; j++) {\n            if (parsed[j] === item) {\n              isDuplicate = true;\n              break;\n            }\n          }\n          if (!isDuplicate) {\n            parsed.push(item);\n          }\n        }\n      }\n    }\n    return parsed;\n  }\n  if (!isBrowser) return /** @type {JSTargetsArray} */([targets]);\n  const nodeList = getNodeList(targets);\n  if (nodeList) return /** @type {DOMTargetsArray} */(Array.from(nodeList));\n  return /** @type {TargetsArray} */([targets]);\n}\n\n/**\n * @overload\n * @param  {DOMTargetsParam} targets\n * @return {DOMTargetsArray}\n *\n * @overload\n * @param  {JSTargetsParam} targets\n * @return {JSTargetsArray}\n *\n * @overload\n * @param  {TargetsParam} targets\n * @return {TargetsArray}\n *\n * @param  {DOMTargetsParam|JSTargetsParam|TargetsParam} targets\n */\nfunction registerTargets(targets) {\n  const parsedTargetsArray = parseTargets(targets);\n  const parsedTargetsLength = parsedTargetsArray.length;\n  if (parsedTargetsLength) {\n    for (let i = 0; i < parsedTargetsLength; i++) {\n      const target = parsedTargetsArray[i];\n      if (!target[isRegisteredTargetSymbol]) {\n        target[isRegisteredTargetSymbol] = true;\n        const isSvgType = isSvg(target);\n        const isDom = /** @type {DOMTarget} */(target).nodeType || isSvgType;\n        if (isDom) {\n          target[isDomSymbol] = true;\n          target[isSvgSymbol] = isSvgType;\n          target[transformsSymbol] = {};\n        }\n      }\n    }\n  }\n  return parsedTargetsArray;\n}\n\n\n\n\n/**\n * @param  {TargetsParam} path\n * @return {SVGGeometryElement|undefined}\n */\nconst getPath = path => {\n  const parsedTargets = parseTargets(path);\n  const $parsedSvg = /** @type {SVGGeometryElement} */(parsedTargets[0]);\n  if (!$parsedSvg || !isSvg($parsedSvg)) return;\n  return $parsedSvg;\n};\n\n/**\n * @param  {TargetsParam} path2\n * @param  {Number} [precision]\n * @return {FunctionValue}\n */\nconst morphTo = (path2, precision = .33) => ($path1) => {\n  const $path2 = /** @type {SVGGeometryElement} */(getPath(path2));\n  if (!$path2) return;\n  const isPath = $path1.tagName === 'path';\n  const separator = isPath ? ' ' : ',';\n  const previousPoints = $path1[morphPointsSymbol];\n  if (previousPoints) $path1.setAttribute(isPath ? 'd' : 'points', previousPoints);\n\n  let v1 = '', v2 = '';\n\n  if (!precision) {\n    v1 = $path1.getAttribute(isPath ? 'd' : 'points');\n    v2 = $path2.getAttribute(isPath ? 'd' : 'points');\n  } else {\n    const length1 = /** @type {SVGGeometryElement} */($path1).getTotalLength();\n    const length2 = $path2.getTotalLength();\n    const maxPoints = Math.max(Math.ceil(length1 * precision), Math.ceil(length2 * precision));\n    for (let i = 0; i < maxPoints; i++) {\n      const t = i / (maxPoints - 1);\n      const pointOnPath1 = /** @type {SVGGeometryElement} */($path1).getPointAtLength(length1 * t);\n      const pointOnPath2 = $path2.getPointAtLength(length2 * t);\n      const prefix = isPath ? (i === 0 ? 'M' : 'L') : '';\n      v1 += prefix + round(pointOnPath1.x, 3) + separator + pointOnPath1.y + ' ';\n      v2 += prefix + round(pointOnPath2.x, 3) + separator + pointOnPath2.y + ' ';\n    }\n  }\n\n  $path1[morphPointsSymbol] = v2;\n\n  return [v1, v2];\n};\n\n/**\n * @param {SVGGeometryElement} [$el]\n * @return {Number}\n */\nconst getScaleFactor = $el => {\n  let scaleFactor = 1;\n  if ($el && $el.getCTM) {\n    const ctm = $el.getCTM();\n    if (ctm) {\n      const scaleX = sqrt(ctm.a * ctm.a + ctm.b * ctm.b);\n      const scaleY = sqrt(ctm.c * ctm.c + ctm.d * ctm.d);\n      scaleFactor = (scaleX + scaleY) / 2;\n    }\n  }\n  return scaleFactor;\n};\n\n/**\n * Creates a proxy that wraps an SVGGeometryElement and adds drawing functionality.\n * @param {SVGGeometryElement} $el - The SVG element to transform into a drawable\n * @param {number} start - Starting position (0-1)\n * @param {number} end - Ending position (0-1)\n * @return {DrawableSVGGeometry} - Returns a proxy that preserves the original element's type with additional 'draw' attribute functionality\n */\nconst createDrawableProxy = ($el, start, end) => {\n  const pathLength = K;\n  const computedStyles = getComputedStyle($el);\n  const strokeLineCap = computedStyles.strokeLinecap;\n  // @ts-ignore\n  const $scalled = computedStyles.vectorEffect === 'non-scaling-stroke' ? $el : null;\n  let currentCap = strokeLineCap;\n\n  const proxy = new Proxy($el, {\n    get(target, property) {\n      const value = target[property];\n      if (property === proxyTargetSymbol) return target;\n      if (property === 'setAttribute') {\n        return (...args) => {\n          if (args[0] === 'draw') {\n            const value = args[1];\n            const values = value.split(' ');\n            const v1 = +values[0];\n            const v2 = +values[1];\n            // TOTO: Benchmark if performing two slices is more performant than one split\n            // const spaceIndex = value.indexOf(' ');\n            // const v1 = round(+value.slice(0, spaceIndex), precision);\n            // const v2 = round(+value.slice(spaceIndex + 1), precision);\n            const scaleFactor = getScaleFactor($scalled);\n            const os = v1 * -1e3 * scaleFactor;\n            const d1 = (v2 * pathLength * scaleFactor) + os;\n            const d2 = (pathLength * scaleFactor +\n                      ((v1 === 0 && v2 === 1) || (v1 === 1 && v2 === 0) ? 0 : 10 * scaleFactor) - d1);\n            if (strokeLineCap !== 'butt') {\n              const newCap = v1 === v2 ? 'butt' : strokeLineCap;\n              if (currentCap !== newCap) {\n                target.style.strokeLinecap = `${newCap}`;\n                currentCap = newCap;\n              }\n            }\n            target.setAttribute('stroke-dashoffset', `${os}`);\n            target.setAttribute('stroke-dasharray', `${d1} ${d2}`);\n          }\n          return Reflect.apply(value, target, args);\n        };\n      }\n\n      if (isFnc(value)) {\n        return (...args) => Reflect.apply(value, target, args);\n      } else {\n        return value;\n      }\n    }\n  });\n\n  if ($el.getAttribute('pathLength') !== `${pathLength}`) {\n    $el.setAttribute('pathLength', `${pathLength}`);\n    proxy.setAttribute('draw', `${start} ${end}`);\n  }\n\n  return /** @type {DrawableSVGGeometry} */(proxy);\n};\n\n/**\n * Creates drawable proxies for multiple SVG elements.\n * @param {TargetsParam} selector - CSS selector, SVG element, or array of elements and selectors\n * @param {number} [start=0] - Starting position (0-1)\n * @param {number} [end=0] - Ending position (0-1)\n * @return {Array<DrawableSVGGeometry>} - Array of proxied elements with drawing functionality\n */\nconst createDrawable = (selector, start = 0, end = 0) => {\n  const els = parseTargets(selector);\n  return els.map($el => createDrawableProxy(\n    /** @type {SVGGeometryElement} */($el),\n    start,\n    end\n  ));\n};\n\n// Motion path animation\n\n/**\n * @param {SVGGeometryElement} $path\n * @param {Number} progress\n * @param {Number}lookup\n * @return {DOMPoint}\n */\nconst getPathPoint = ($path, progress, lookup = 0) => {\n  return $path.getPointAtLength(progress + lookup >= 1 ? progress + lookup : 0);\n};\n\n/**\n * @param {SVGGeometryElement} $path\n * @param {String} pathProperty\n * @return {FunctionValue}\n */\nconst getPathProgess = ($path, pathProperty) => {\n  return $el => {\n    const totalLength = +($path.getTotalLength());\n    const inSvg = $el[isSvgSymbol];\n    const ctm = $path.getCTM();\n    /** @type {TweenObjectValue} */\n    return {\n      from: 0,\n      to: totalLength,\n      /** @type {TweenModifier} */\n      modifier: progress => {\n        if (pathProperty === 'a') {\n          const p0 = getPathPoint($path, progress, -1);\n          const p1 = getPathPoint($path, progress, 1);\n          return atan2(p1.y - p0.y, p1.x - p0.x) * 180 / PI;\n        } else {\n          const p = getPathPoint($path, progress, 0);\n          return pathProperty === 'x' ?\n            inSvg || !ctm ? p.x : p.x * ctm.a + p.y * ctm.c + ctm.e :\n            inSvg || !ctm ? p.y : p.x * ctm.b + p.y * ctm.d + ctm.f\n        }\n      }\n    }\n  }\n};\n\n/**\n * @param {TargetsParam} path\n */\nconst createMotionPath = path => {\n  const $path = getPath(path);\n  if (!$path) return;\n  return {\n    translateX: getPathProgess($path, 'x'),\n    translateY: getPathProgess($path, 'y'),\n    rotate: getPathProgess($path, 'a'),\n  }\n};\n\n// Check for valid SVG attribute\n\nconst cssReservedProperties = ['opacity', 'rotate', 'overflow', 'color'];\n\n/**\n * @param  {Target} el\n * @param  {String} propertyName\n * @return {Boolean}\n */\nconst isValidSVGAttribute = (el, propertyName) => {\n  // Return early and use CSS opacity animation instead (already better default values (opacity: 1 instead of 0)) and rotate should be considered a transform\n  if (cssReservedProperties.includes(propertyName)) return false;\n  if (el.getAttribute(propertyName) || propertyName in el) {\n    if (propertyName === 'scale') { // Scale\n      const elParentNode = /** @type {SVGGeometryElement} */(/** @type {DOMTarget} */(el).parentNode);\n      // Only consider scale as a valid SVG attribute on filter element\n      return elParentNode && elParentNode.tagName === 'filter';\n    }\n    return true;\n  }\n};\n\nconst svg = {\n  morphTo,\n  createMotionPath,\n  createDrawable,\n};\n\n\n\n\n/**\n * RGB / RGBA Color value string -> RGBA values array\n * @param  {String} rgbValue\n * @return {ColorArray}\n */\nconst rgbToRgba = rgbValue => {\n  const rgba = rgbExecRgx.exec(rgbValue) || rgbaExecRgx.exec(rgbValue);\n  const a = !isUnd(rgba[4]) ? +rgba[4] : 1;\n  return [\n    +rgba[1],\n    +rgba[2],\n    +rgba[3],\n    a\n  ]\n};\n\n/**\n * HEX3 / HEX3A / HEX6 / HEX6A Color value string -> RGBA values array\n * @param  {String} hexValue\n * @return {ColorArray}\n */\nconst hexToRgba = hexValue => {\n  const hexLength = hexValue.length;\n  const isShort = hexLength === 4 || hexLength === 5;\n  return [\n    +('0x' + hexValue[1] + hexValue[isShort ? 1 : 2]),\n    +('0x' + hexValue[isShort ? 2 : 3] + hexValue[isShort ? 2 : 4]),\n    +('0x' + hexValue[isShort ? 3 : 5] + hexValue[isShort ? 3 : 6]),\n    ((hexLength === 5 || hexLength === 9) ? +(+('0x' + hexValue[isShort ? 4 : 7] + hexValue[isShort ? 4 : 8]) / 255).toFixed(3) : 1)\n  ]\n};\n\n/**\n * @param  {Number} p\n * @param  {Number} q\n * @param  {Number} t\n * @return {Number}\n */\nconst hue2rgb = (p, q, t) => {\n  if (t < 0) t += 1;\n  if (t > 1) t -= 1;\n  return t < 1 / 6 ? p + (q - p) * 6 * t :\n         t < 1 / 2 ? q :\n         t < 2 / 3 ? p + (q - p) * (2 / 3 - t) * 6 :\n         p;\n};\n\n/**\n * HSL / HSLA Color value string -> RGBA values array\n * @param  {String} hslValue\n * @return {ColorArray}\n */\nconst hslToRgba = hslValue => {\n  const hsla = hslExecRgx.exec(hslValue) || hslaExecRgx.exec(hslValue);\n  const h = +hsla[1] / 360;\n  const s = +hsla[2] / 100;\n  const l = +hsla[3] / 100;\n  const a = !isUnd(hsla[4]) ? +hsla[4] : 1;\n  let r, g, b;\n  if (s === 0) {\n    r = g = b = l;\n  } else {\n    const q = l < .5 ? l * (1 + s) : l + s - l * s;\n    const p = 2 * l - q;\n    r = round(hue2rgb(p, q, h + 1 / 3) * 255, 0);\n    g = round(hue2rgb(p, q, h) * 255, 0);\n    b = round(hue2rgb(p, q, h - 1 / 3) * 255, 0);\n  }\n  return [r, g, b, a];\n};\n\n/**\n * All in one color converter that converts a color string value into an array of RGBA values\n * @param  {String} colorString\n * @return {ColorArray}\n */\nconst convertColorStringValuesToRgbaArray = colorString => {\n  return isRgb(colorString) ? rgbToRgba(colorString) :\n         isHex(colorString) ? hexToRgba(colorString) :\n         isHsl(colorString) ? hslToRgba(colorString) :\n         [0, 0, 0, 1];\n};\n\n\n\n\n/**\n * @template T, D\n * @param {T|undefined} targetValue\n * @param {D} defaultValue\n * @return {T|D}\n */\nconst setValue = (targetValue, defaultValue) => {\n  return isUnd(targetValue) ? defaultValue : targetValue;\n};\n\n/**\n * @param  {TweenPropValue} value\n * @param  {Target} target\n * @param  {Number} index\n * @param  {Number} total\n * @param  {Object} [store]\n * @return {any}\n */\nconst getFunctionValue = (value, target, index, total, store) => {\n  if (isFnc(value)) {\n    const func = () => {\n      const computed = /** @type {Function} */(value)(target, index, total);\n      // Fallback to 0 if the function returns undefined / NaN / null / false / 0\n      return !isNaN(+computed) ? +computed : computed || 0;\n    };\n    if (store) {\n      store.func = func;\n    }\n    return func();\n  } else {\n    return value;\n  }\n};\n\n/**\n * @param  {Target} target\n * @param  {String} prop\n * @return {tweenTypes}\n */\nconst getTweenType = (target, prop) => {\n  return !target[isDomSymbol] ? tweenTypes.OBJECT :\n    // Handle SVG attributes\n    target[isSvgSymbol] && isValidSVGAttribute(target, prop) ? tweenTypes.ATTRIBUTE :\n    // Handle CSS Transform properties differently than CSS to allow individual animations\n    validTransforms.includes(prop) || shortTransforms.get(prop) ? tweenTypes.TRANSFORM :\n    // CSS variables\n    stringStartsWith(prop, '--') ? tweenTypes.CSS_VAR :\n    // All other CSS properties\n    prop in /** @type {DOMTarget} */(target).style ? tweenTypes.CSS :\n    // Handle other DOM Attributes\n    prop in target ? tweenTypes.OBJECT :\n    tweenTypes.ATTRIBUTE;\n};\n\n/**\n * @param  {DOMTarget} target\n * @param  {String} propName\n * @param  {Object} animationInlineStyles\n * @return {String}\n */\nconst getCSSValue = (target, propName, animationInlineStyles) => {\n  const inlineStyles = target.style[propName];\n  if (inlineStyles && animationInlineStyles) {\n    animationInlineStyles[propName] = inlineStyles;\n  }\n  const value = inlineStyles || getComputedStyle(target[proxyTargetSymbol] || target).getPropertyValue(propName);\n  return value === 'auto' ? '0' : value;\n};\n\n/**\n * @param {Target} target\n * @param {String} propName\n * @param {tweenTypes} [tweenType]\n * @param {Object|void} [animationInlineStyles]\n * @return {String|Number}\n */\nconst getOriginalAnimatableValue = (target, propName, tweenType, animationInlineStyles) => {\n  const type = !isUnd(tweenType) ? tweenType : getTweenType(target, propName);\n  return type === tweenTypes.OBJECT ? target[propName] || 0 :\n         type === tweenTypes.ATTRIBUTE ? /** @type {DOMTarget} */(target).getAttribute(propName) :\n         type === tweenTypes.TRANSFORM ? parseInlineTransforms(/** @type {DOMTarget} */(target), propName, animationInlineStyles) :\n         type === tweenTypes.CSS_VAR ? getCSSValue(/** @type {DOMTarget} */(target), propName, animationInlineStyles).trimStart() :\n         getCSSValue(/** @type {DOMTarget} */(target), propName, animationInlineStyles);\n};\n\n/**\n * @param  {Number} x\n * @param  {Number} y\n * @param  {String} operator\n * @return {Number}\n */\nconst getRelativeValue = (x, y, operator) => {\n  return operator === '-' ? x - y :\n         operator === '+' ? x + y :\n         x * y;\n};\n\n/** @return {TweenDecomposedValue} */\nconst createDecomposedValueTargetObject = () => {\n  return {\n    /** @type {valueTypes} */\n    t: valueTypes.NUMBER,\n    n: 0,\n    u: null,\n    o: null,\n    d: null,\n    s: null,\n  }\n};\n\n/**\n * @param  {String|Number} rawValue\n * @param  {TweenDecomposedValue} targetObject\n * @return {TweenDecomposedValue}\n */\nconst decomposeRawValue = (rawValue, targetObject) => {\n  /** @type {valueTypes} */\n  targetObject.t = valueTypes.NUMBER;\n  targetObject.n = 0;\n  targetObject.u = null;\n  targetObject.o = null;\n  targetObject.d = null;\n  targetObject.s = null;\n  if (!rawValue) return targetObject;\n  const num = +rawValue;\n  if (!isNaN(num)) {\n    // It's a number\n    targetObject.n = num;\n    return targetObject;\n  } else {\n    // let str = /** @type {String} */(rawValue).trim();\n    let str = /** @type {String} */(rawValue);\n    // Parsing operators (+=, -=, *=) manually is much faster than using regex here\n    if (str[1] === '=') {\n      targetObject.o = str[0];\n      str = str.slice(2);\n    }\n    // Skip exec regex if the value type is complex or color to avoid long regex backtracking\n    const unitMatch = str.includes(' ') ? false : unitsExecRgx.exec(str);\n    if (unitMatch) {\n      // Has a number and a unit\n      targetObject.t = valueTypes.UNIT;\n      targetObject.n = +unitMatch[1];\n      targetObject.u = unitMatch[2];\n      return targetObject;\n    } else if (targetObject.o) {\n      // Has an operator (+=, -=, *=)\n      targetObject.n = +str;\n      return targetObject;\n    } else if (isCol(str)) {\n      // Is a color\n      targetObject.t = valueTypes.COLOR;\n      targetObject.d = convertColorStringValuesToRgbaArray(str);\n      return targetObject;\n    } else {\n      // Is a more complex string (generally svg coords, calc() or filters CSS values)\n      const matchedNumbers = str.match(digitWithExponentRgx);\n      targetObject.t = valueTypes.COMPLEX;\n      targetObject.d = matchedNumbers ? matchedNumbers.map(Number) : [];\n      targetObject.s = str.split(digitWithExponentRgx) || [];\n      return targetObject;\n    }\n  }\n};\n\n/**\n * @param  {Tween} tween\n * @param  {TweenDecomposedValue} targetObject\n * @return {TweenDecomposedValue}\n */\nconst decomposeTweenValue = (tween, targetObject) => {\n  targetObject.t = tween._valueType;\n  targetObject.n = tween._toNumber;\n  targetObject.u = tween._unit;\n  targetObject.o = null;\n  targetObject.d = cloneArray(tween._toNumbers);\n  targetObject.s = cloneArray(tween._strings);\n  return targetObject;\n};\n\nconst decomposedOriginalValue = createDecomposedValueTargetObject();\n\n\n\n\nconst lookups = {\n  /** @type {TweenReplaceLookups} */\n  _rep: new WeakMap(),\n  /** @type {TweenAdditiveLookups} */\n  _add: new Map(),\n};\n\n/**\n * @param  {Target} target\n * @param  {String} property\n * @param  {String} lookup\n * @return {TweenPropertySiblings}\n */\nconst getTweenSiblings = (target, property, lookup = '_rep') => {\n  const lookupMap = lookups[lookup];\n  let targetLookup = lookupMap.get(target);\n  if (!targetLookup) {\n    targetLookup = {};\n    lookupMap.set(target, targetLookup);\n  }\n  return targetLookup[property] ? targetLookup[property] : targetLookup[property] = {\n    _head: null,\n    _tail: null,\n  }\n};\n\n/**\n * @param  {Tween} p\n * @param  {Tween} c\n * @return {Number|Boolean}\n */\nconst addTweenSortMethod = (p, c) => {\n  return p._isOverridden || p._absoluteStartTime > c._absoluteStartTime;\n};\n\n/**\n * @param {Tween} tween\n */\nconst overrideTween = tween => {\n  tween._isOverlapped = 1;\n  tween._isOverridden = 1;\n  tween._changeDuration = minValue;\n  tween._currentTime = minValue;\n};\n\n/**\n * @param  {Tween} tween\n * @param  {TweenPropertySiblings} siblings\n * @return {Tween}\n */\nconst composeTween = (tween, siblings) => {\n\n  const tweenCompositionType = tween._composition;\n\n  // Handle replaced tweens\n\n  if (tweenCompositionType === compositionTypes.replace) {\n\n    const tweenAbsStartTime = tween._absoluteStartTime;\n\n    addChild(siblings, tween, addTweenSortMethod, '_prevRep', '_nextRep');\n\n    const prevSibling = tween._prevRep;\n\n    // Update the previous siblings for composition replace tweens\n\n    if (prevSibling) {\n\n      const prevParent = prevSibling.parent;\n      const prevAbsEndTime = prevSibling._absoluteStartTime + prevSibling._changeDuration;\n\n      // Handle looped animations tween\n\n      if (\n        // Check if the previous tween is from a different animation\n        tween.parent.id !== prevParent.id &&\n        // Check if the animation has loops\n        prevParent.iterationCount> 1 &&\n        // Check if _absoluteChangeEndTime of last loop overlaps the current tween\n        prevAbsEndTime + (prevParent.duration - prevParent.iterationDuration) > tweenAbsStartTime\n      ) {\n\n        // TODO: Find a way to only override the iterations overlapping with the tween\n        overrideTween(prevSibling);\n\n        let prevPrevSibling = prevSibling._prevRep;\n\n        // If the tween was part of a set of keyframes, override its siblings\n        while (prevPrevSibling && prevPrevSibling.parent.id === prevParent.id) {\n          overrideTween(prevPrevSibling);\n          prevPrevSibling = prevPrevSibling._prevRep;\n        }\n\n      }\n\n      const absoluteUpdateStartTime = tweenAbsStartTime - tween._delay;\n\n      if (prevAbsEndTime > absoluteUpdateStartTime) {\n\n        const prevChangeStartTime = prevSibling._startTime;\n        const prevTLOffset = prevAbsEndTime - (prevChangeStartTime + prevSibling._updateDuration);\n\n        prevSibling._changeDuration = absoluteUpdateStartTime - prevTLOffset - prevChangeStartTime;\n        prevSibling._currentTime = prevSibling._changeDuration;\n        prevSibling._isOverlapped = 1;\n\n        if (prevSibling._changeDuration < minValue) {\n          overrideTween(prevSibling);\n        }\n      }\n\n      // Pause (and cancel) the parent if it only contains overlapped tweens\n\n      let pausePrevParentAnimation = true;\n\n      forEachChildren(prevParent, (/** @type Tween */t) => {\n        if (!t._isOverlapped) pausePrevParentAnimation = false;\n      });\n\n      if (pausePrevParentAnimation) {\n        const prevParentTL = prevParent.parent;\n        if (prevParentTL) {\n          let pausePrevParentTL = true;\n          forEachChildren(prevParentTL, (/** @type JSAnimation */a) => {\n            if (a !== prevParent) {\n              forEachChildren(a, (/** @type Tween */t) => {\n                if (!t._isOverlapped) pausePrevParentTL = false;\n              });\n            }\n          });\n          if (pausePrevParentTL) {\n            prevParentTL.cancel();\n          }\n        } else {\n          prevParent.cancel();\n          // Previously, calling .cancel() on a timeline child would affect the render order of other children\n          // Worked around this by marking it as .completed and using .pause() for safe removal in the engine loop\n          // This is no longer needed since timeline tween composition is now handled separatly\n          // Keeping this here for reference\n          // prevParent.completed = true;\n          // prevParent.pause();\n        }\n      }\n\n    }\n\n    // let nextSibling = tween._nextRep;\n\n    // // All the next siblings are automatically overridden\n\n    // if (nextSibling && nextSibling._absoluteStartTime >= tweenAbsStartTime) {\n    //   while (nextSibling) {\n    //     overrideTween(nextSibling);\n    //     nextSibling = nextSibling._nextRep;\n    //   }\n    // }\n\n    // if (nextSibling && nextSibling._absoluteStartTime < tweenAbsStartTime) {\n    //   while (nextSibling) {\n    //     overrideTween(nextSibling);\n    //     console.log(tween.id, nextSibling.id);\n    //     nextSibling = nextSibling._nextRep;\n    //   }\n    // }\n\n  // Handle additive tweens composition\n\n  } else if (tweenCompositionType === compositionTypes.blend) {\n\n    const additiveTweenSiblings = getTweenSiblings(tween.target, tween.property, '_add');\n    const additiveAnimation = addAdditiveAnimation(lookups._add);\n\n    let lookupTween = additiveTweenSiblings._head;\n\n    if (!lookupTween) {\n      lookupTween = { ...tween };\n      lookupTween._composition = compositionTypes.replace;\n      lookupTween._updateDuration = minValue;\n      lookupTween._startTime = 0;\n      lookupTween._numbers = cloneArray(tween._fromNumbers);\n      lookupTween._number = 0;\n      lookupTween._next = null;\n      lookupTween._prev = null;\n      addChild(additiveTweenSiblings, lookupTween);\n      addChild(additiveAnimation, lookupTween);\n    }\n\n    // Convert the values of TO to FROM and set TO to 0\n\n    const toNumber = tween._toNumber;\n    tween._fromNumber = lookupTween._fromNumber - toNumber;\n    tween._toNumber = 0;\n    tween._numbers = cloneArray(tween._fromNumbers);\n    tween._number = 0;\n    lookupTween._fromNumber = toNumber;\n\n    if (tween._toNumbers) {\n      const toNumbers = cloneArray(tween._toNumbers);\n      if (toNumbers) {\n        toNumbers.forEach((value, i) => {\n          tween._fromNumbers[i] = lookupTween._fromNumbers[i] - value;\n          tween._toNumbers[i] = 0;\n        });\n      }\n      lookupTween._fromNumbers = toNumbers;\n    }\n\n    addChild(additiveTweenSiblings, tween, null, '_prevAdd', '_nextAdd');\n\n  }\n\n  return tween;\n\n};\n\n/**\n * @param  {Tween} tween\n * @return {Tween}\n */\nconst removeTweenSliblings = tween => {\n  const tweenComposition = tween._composition;\n  if (tweenComposition !== compositionTypes.none) {\n    const tweenTarget = tween.target;\n    const tweenProperty = tween.property;\n    const replaceTweensLookup = lookups._rep;\n    const replaceTargetProps = replaceTweensLookup.get(tweenTarget);\n    const tweenReplaceSiblings = replaceTargetProps[tweenProperty];\n    removeChild(tweenReplaceSiblings, tween, '_prevRep', '_nextRep');\n    if (tweenComposition === compositionTypes.blend) {\n      const addTweensLookup = lookups._add;\n      const addTargetProps = addTweensLookup.get(tweenTarget);\n      if (!addTargetProps) return;\n      const additiveTweenSiblings = addTargetProps[tweenProperty];\n      const additiveAnimation = additive.animation;\n      removeChild(additiveTweenSiblings, tween, '_prevAdd', '_nextAdd');\n      // If only one tween is left in the additive lookup, it's the tween lookup\n      const lookupTween = additiveTweenSiblings._head;\n      if (lookupTween && lookupTween === additiveTweenSiblings._tail) {\n        removeChild(additiveTweenSiblings, lookupTween, '_prevAdd', '_nextAdd');\n        removeChild(additiveAnimation, lookupTween);\n        let shouldClean = true;\n        for (let prop in addTargetProps) {\n          if (addTargetProps[prop]._head) {\n            shouldClean = false;\n            break;\n          }\n        }\n        if (shouldClean) {\n          addTweensLookup.delete(tweenTarget);\n        }\n      }\n    }\n  }\n  return tween;\n};\n\n\n\n\n/**\n * @param  {Timer} timer\n * @return {Timer}\n */\nconst resetTimerProperties = timer => {\n  timer.paused = true;\n  timer.began = false;\n  timer.completed = false;\n  return timer;\n};\n\n/**\n * @param  {Timer} timer\n * @return {Timer}\n */\nconst reviveTimer = timer => {\n  if (!timer._cancelled) return timer;\n  if (timer._hasChildren) {\n    forEachChildren(timer, reviveTimer);\n  } else {\n    forEachChildren(timer, (/** @type {Tween} tween*/tween) => {\n      if (tween._composition !== compositionTypes.none) {\n        composeTween(tween, getTweenSiblings(tween.target, tween.property));\n      }\n    });\n  }\n  timer._cancelled = 0;\n  return timer;\n};\n\nlet timerId = 0;\n\n/**\n * Base class used to create Timers, Animations and Timelines\n */\nclass Timer extends Clock {\n  /**\n   * @param {TimerParams} [parameters]\n   * @param {Timeline} [parent]\n   * @param {Number} [parentPosition]\n   */\n  constructor(parameters = {}, parent = null, parentPosition = 0) {\n\n    super(0);\n\n    const {\n      id,\n      delay,\n      duration,\n      reversed,\n      alternate,\n      loop,\n      loopDelay,\n      autoplay,\n      frameRate,\n      playbackRate,\n      onComplete,\n      onLoop,\n      onPause,\n      onBegin,\n      onBeforeUpdate,\n      onUpdate,\n    } = parameters;\n\n    if (globals.scope) globals.scope.revertibles.push(this);\n\n    const timerInitTime = parent ? 0 : engine._elapsedTime;\n    const timerDefaults = parent ? parent.defaults : globals.defaults;\n    const timerDelay = /** @type {Number} */(isFnc(delay) || isUnd(delay) ? timerDefaults.delay : +delay);\n    const timerDuration = isFnc(duration) || isUnd(duration) ? Infinity : +duration;\n    const timerLoop = setValue(loop, timerDefaults.loop);\n    const timerLoopDelay = setValue(loopDelay, timerDefaults.loopDelay);\n    const timerIterationCount = timerLoop === true ||\n                                timerLoop === Infinity ||\n                                /** @type {Number} */(timerLoop) < 0 ? Infinity :\n                                /** @type {Number} */(timerLoop) + 1;\n\n    let offsetPosition = 0;\n\n    if (parent) {\n      offsetPosition = parentPosition;\n    } else {\n      let startTime = now();\n      // Make sure to tick the engine once if suspended to avoid big gaps with the following offsetPosition calculation\n      if (engine.paused) {\n        engine.requestTick(startTime);\n        startTime = engine._elapsedTime;\n      }\n      offsetPosition = startTime - engine._startTime;\n    }\n\n    // Timer's parameters\n    this.id = !isUnd(id) ? id : ++timerId;\n    /** @type {Timeline} */\n    this.parent = parent;\n    // Total duration of the timer\n    this.duration = clampInfinity(((timerDuration + timerLoopDelay) * timerIterationCount) - timerLoopDelay) || minValue;\n    /** @type {Boolean} */\n    this.backwards = false;\n    /** @type {Boolean} */\n    this.paused = true;\n    /** @type {Boolean} */\n    this.began = false;\n    /** @type {Boolean} */\n    this.completed = false;\n    /** @type {Callback<this>} */\n    this.onBegin = onBegin || timerDefaults.onBegin;\n    /** @type {Callback<this>} */\n    this.onBeforeUpdate = onBeforeUpdate || timerDefaults.onBeforeUpdate;\n    /** @type {Callback<this>} */\n    this.onUpdate = onUpdate || timerDefaults.onUpdate;\n    /** @type {Callback<this>} */\n    this.onLoop = onLoop || timerDefaults.onLoop;\n    /** @type {Callback<this>} */\n    this.onPause = onPause || timerDefaults.onPause;\n    /** @type {Callback<this>} */\n    this.onComplete = onComplete || timerDefaults.onComplete;\n    /** @type {Number} */\n    this.iterationDuration = timerDuration; // Duration of one loop\n    /** @type {Number} */\n    this.iterationCount = timerIterationCount; // Number of loops\n    /** @type {Boolean|ScrollObserver} */\n    this._autoplay = parent ? false : setValue(autoplay, timerDefaults.autoplay);\n    /** @type {Number} */\n    this._offset = offsetPosition;\n    /** @type {Number} */\n    this._delay = timerDelay;\n    /** @type {Number} */\n    this._loopDelay = timerLoopDelay;\n    /** @type {Number} */\n    this._iterationTime = 0;\n    /** @type {Number} */\n    this._currentIteration = 0; // Current loop index\n    /** @type {Function} */\n    this._resolve = noop; // Used by .then()\n    /** @type {Boolean} */\n    this._running = false;\n    /** @type {Number} */\n    this._reversed = +setValue(reversed, timerDefaults.reversed);\n    /** @type {Number} */\n    this._reverse = this._reversed;\n    /** @type {Number} */\n    this._cancelled = 0;\n    /** @type {Boolean} */\n    this._alternate = setValue(alternate, timerDefaults.alternate);\n    /** @type {Renderable} */\n    this._prev = null;\n    /** @type {Renderable} */\n    this._next = null;\n\n    // Clock's parameters\n    /** @type {Number} */\n    this._elapsedTime = timerInitTime;\n    /** @type {Number} */\n    this._startTime = timerInitTime;\n    /** @type {Number} */\n    this._lastTime = timerInitTime;\n    /** @type {Number} */\n    this._fps = setValue(frameRate, timerDefaults.frameRate);\n    /** @type {Number} */\n    this._speed = setValue(playbackRate, timerDefaults.playbackRate);\n  }\n\n  get cancelled() {\n    return !!this._cancelled;\n  }\n\n  /** @param {Boolean} cancelled  */\n  set cancelled(cancelled) {\n    cancelled ? this.cancel() : this.reset(1).play();\n  }\n\n  get currentTime() {\n    return clamp(round(this._currentTime, globals.precision), -this._delay, this.duration);\n  }\n\n  /** @param {Number} time  */\n  set currentTime(time) {\n    const paused = this.paused;\n    // Pausing the timer is necessary to avoid time jumps on a running instance\n    this.pause().seek(+time);\n    if (!paused) this.resume();\n  }\n\n  get iterationCurrentTime() {\n    return round(this._iterationTime, globals.precision);\n  }\n\n  /** @param {Number} time  */\n  set iterationCurrentTime(time) {\n    this.currentTime = (this.iterationDuration * this._currentIteration) + time;\n  }\n\n  get progress() {\n    return clamp(round(this._currentTime / this.duration, 5), 0, 1);\n  }\n\n  /** @param {Number} progress  */\n  set progress(progress) {\n    this.currentTime = this.duration * progress;\n  }\n\n  get iterationProgress() {\n    return clamp(round(this._iterationTime / this.iterationDuration, 5), 0, 1);\n  }\n\n  /** @param {Number} progress  */\n  set iterationProgress(progress) {\n    const iterationDuration = this.iterationDuration;\n    this.currentTime = (iterationDuration * this._currentIteration) + (iterationDuration * progress);\n  }\n\n  get currentIteration() {\n    return this._currentIteration;\n  }\n\n  /** @param {Number} iterationCount  */\n  set currentIteration(iterationCount) {\n    this.currentTime = (this.iterationDuration * clamp(+iterationCount, 0, this.iterationCount - 1));\n  }\n\n  get reversed() {\n    return !!this._reversed;\n  }\n\n  /** @param {Boolean} reverse  */\n  set reversed(reverse) {\n    reverse ? this.reverse() : this.play();\n  }\n\n  get speed() {\n    return super.speed;\n  }\n\n  /** @param {Number} playbackRate  */\n  set speed(playbackRate) {\n    super.speed = playbackRate;\n    this.resetTime();\n  }\n\n  /**\n   * @param  {Number} internalRender\n   * @return {this}\n   */\n  reset(internalRender = 0) {\n    // If cancelled, revive the timer before rendering in order to have propertly composed tweens siblings\n    reviveTimer(this);\n    if (this._reversed && !this._reverse) this.reversed = false;\n    // Rendering before updating the completed flag to prevent skips and to make sure the properties are not overridden\n    // Setting the iterationTime at the end to force the rendering to happend backwards, otherwise calling .reset() on Timelines might not render children in the right order\n    // NOTE: This is only required for Timelines and might be better to move to the Timeline class?\n    this._iterationTime = this.iterationDuration;\n    // Set tickMode to tickModes.FORCE to force rendering\n    tick(this, 0, 1, internalRender, tickModes.FORCE);\n    // Reset timer properties after revive / render to make sure the props are not updated again\n    resetTimerProperties(this);\n    // Also reset children properties\n    if (this._hasChildren) {\n      forEachChildren(this, resetTimerProperties);\n    }\n    return this;\n  }\n\n  /**\n   * @param  {Number} internalRender\n   * @return {this}\n   */\n  init(internalRender = 0) {\n    this.fps = this._fps;\n    this.speed = this._speed;\n    // Manually calling .init() on timelines should render all children intial state\n    // Forces all children to render once then render to 0 when reseted\n    if (!internalRender && this._hasChildren) {\n      tick(this, this.duration, 1, internalRender, tickModes.FORCE);\n    }\n    this.reset(internalRender);\n    // Make sure to set autoplay to false to child timers so it doesn't attempt to autoplay / link\n    const autoplay = this._autoplay;\n    if (autoplay === true) {\n      this.resume();\n    } else if (autoplay && !isUnd(/** @type {ScrollObserver} */(autoplay).linked)) {\n      /** @type {ScrollObserver} */(autoplay).link(this);\n    }\n    return this;\n  }\n\n  /** @return {this} */\n  resetTime() {\n    const timeScale = 1 / (this._speed * engine._speed);\n    this._startTime = now() - (this._currentTime + this._delay) * timeScale;\n    return this;\n  }\n\n  /** @return {this} */\n  pause() {\n    if (this.paused) return this;\n    this.paused = true;\n    this.onPause(this);\n    return this;\n  }\n\n  /** @return {this} */\n  resume() {\n    if (!this.paused) return this;\n    this.paused = false;\n    // We can safely imediatly render a timer that has no duration and no children\n    if (this.duration <= minValue && !this._hasChildren) {\n      tick(this, minValue, 0, 0, tickModes.FORCE);\n    } else {\n      if (!this._running) {\n        addChild(engine, this);\n        engine._hasChildren = true;\n        this._running = true;\n      }\n      this.resetTime();\n      // Forces the timer to advance by at least one frame when the next tick occurs\n      this._startTime -= 12;\n      engine.wake();\n    }\n    return this;\n  }\n\n  /** @return {this} */\n  restart() {\n    return this.reset(0).resume();\n  }\n\n  /**\n   * @param  {Number} time\n   * @param  {Boolean|Number} [muteCallbacks]\n   * @param  {Boolean|Number} [internalRender]\n   * @return {this}\n   */\n  seek(time, muteCallbacks = 0, internalRender = 0) {\n    // Recompose the tween siblings in case the timer has been cancelled\n    reviveTimer(this);\n    // If you seek a completed animation, otherwise the next play will starts at 0\n    this.completed = false;\n    const isPaused = this.paused;\n    this.paused = true;\n    // timer, time, muteCallbacks, internalRender, tickMode\n    tick(this, time + this._delay, ~~muteCallbacks, ~~internalRender, tickModes.AUTO);\n    return isPaused ? this : this.resume();\n  }\n\n  /** @return {this} */\n  alternate() {\n    const reversed = this._reversed;\n    const count = this.iterationCount;\n    const duration = this.iterationDuration;\n    // Calculate the maximum iterations possible given the iteration duration\n    const iterations = count === Infinity ? floor(maxValue / duration) : count;\n    this._reversed = +(this._alternate && !(iterations % 2) ? reversed : !reversed);\n    if (count === Infinity) {\n      // Handle infinite loops to loop on themself\n      this.iterationProgress = this._reversed ? 1 - this.iterationProgress : this.iterationProgress;\n    } else {\n      this.seek((duration * iterations) - this._currentTime);\n    }\n    this.resetTime();\n    return this;\n  }\n\n  /** @return {this} */\n  play() {\n    if (this._reversed) this.alternate();\n    return this.resume();\n  }\n\n  /** @return {this} */\n  reverse() {\n    if (!this._reversed) this.alternate();\n    return this.resume();\n  }\n\n  // TODO: Move all the animation / tweens / children related code to Animation / Timeline\n\n  /** @return {this} */\n  cancel() {\n    if (this._hasChildren) {\n      forEachChildren(this, (/** @type {Renderable} */child) => child.cancel(), true);\n    } else {\n      forEachChildren(this, removeTweenSliblings);\n    }\n    this._cancelled = 1;\n    // Pausing the timer removes it from the engine\n    return this.pause();\n  }\n\n  /**\n   * @param  {Number} newDuration\n   * @return {this}\n   */\n  stretch(newDuration) {\n    const currentDuration = this.duration;\n    const normlizedDuration = normalizeTime(newDuration);\n    if (currentDuration === normlizedDuration) return this;\n    const timeScale = newDuration / currentDuration;\n    const isSetter = newDuration <= minValue;\n    this.duration = isSetter ? minValue : normlizedDuration;\n    this.iterationDuration = isSetter ? minValue : normalizeTime(this.iterationDuration * timeScale);\n    this._offset *= timeScale;\n    this._delay *= timeScale;\n    this._loopDelay *= timeScale;\n    return this;\n  }\n\n /**\n   * Cancels the timer by seeking it back to 0 and reverting the attached scroller if necessary\n   * @return {this}\n   */\n  revert() {\n    tick(this, 0, 1, 0, tickModes.AUTO);\n    const ap = /** @type {ScrollObserver} */(this._autoplay);\n    if (ap && ap.linked && ap.linked === this) ap.revert();\n    return this.cancel();\n  }\n\n /**\n   * Imediatly completes the timer, cancels it and triggers the onComplete callback\n   * @return {this}\n   */\n  complete() {\n    return this.seek(this.duration).cancel();\n  }\n\n  /**\n   * @param  {Callback<this>} [callback]\n   * @return {Promise}\n   */\n  then(callback = noop) {\n    const then = this.then;\n    const onResolve = () => {\n      // this.then = null prevents infinite recursion if returned by an async function\n      // https://github.com/juliangarnierorg/anime-beta/issues/26\n      this.then = null;\n      callback(this);\n      this.then = then;\n      this._resolve = noop;\n    };\n    return new Promise(r => {\n      this._resolve = () => r(onResolve());\n      // Make sure to resolve imediatly if the timer has already completed\n      if (this.completed) this._resolve();\n      return this;\n    });\n  }\n\n}\n\n\n/**\n * @param {TimerParams} [parameters]\n * @return {Timer}\n */\nconst createTimer = parameters => new Timer(parameters, null, 0).init();\n\n\n\n\n/** @type {EasingFunction} */\nconst none = t => t;\n\n// Cubic Bezier solver adapted from https://github.com/gre/bezier-ease © Gaëtan Renaudeau\n\n/**\n * @param  {Number} aT\n * @param  {Number} aA1\n * @param  {Number} aA2\n * @return {Number}\n */\nconst calcBezier = (aT, aA1, aA2) => (((1 - 3 * aA2 + 3 * aA1) * aT + (3 * aA2 - 6 * aA1)) * aT + (3 * aA1)) * aT;\n\n/**\n * @param  {Number} aX\n * @param  {Number} mX1\n * @param  {Number} mX2\n * @return {Number}\n */\nconst binarySubdivide = (aX, mX1, mX2) => {\n  let aA = 0, aB = 1, currentX, currentT, i = 0;\n  do {\n    currentT = aA + (aB - aA) / 2;\n    currentX = calcBezier(currentT, mX1, mX2) - aX;\n    if (currentX > 0) {\n      aB = currentT;\n    } else {\n      aA = currentT;\n    }\n  } while (abs(currentX) > .0000001 && ++i < 100);\n  return currentT;\n};\n\n/**\n * @param  {Number} [mX1]\n * @param  {Number} [mY1]\n * @param  {Number} [mX2]\n * @param  {Number} [mY2]\n * @return {EasingFunction}\n */\n\nconst cubicBezier = (mX1 = 0.5, mY1 = 0.0, mX2 = 0.5, mY2 = 1.0) => (mX1 === mY1 && mX2 === mY2) ? none :\n  t => t === 0 || t === 1 ? t :\n  calcBezier(binarySubdivide(t, mX1, mX2), mY1, mY2);\n\n/**\n * Steps ease implementation https://developer.mozilla.org/fr/docs/Web/CSS/transition-timing-function\n * Only covers 'end' and 'start' jumpterms\n * @param  {Number} steps\n * @param  {Boolean} [fromStart]\n * @return {EasingFunction}\n */\nconst steps = (steps = 10, fromStart) => {\n  const roundMethod = fromStart ? ceil : floor;\n  return t => roundMethod(clamp(t, 0, 1) * steps) * (1 / steps);\n};\n\n/**\n * Without parameters, the linear function creates a non-eased transition.\n * Parameters, if used, creates a piecewise linear easing by interpolating linearly between the specified points.\n * @param  {...String|Number} [args] - Points\n * @return {EasingFunction}\n */\nconst linear = (...args) => {\n  const argsLength = args.length;\n  if (!argsLength) return none;\n  const totalPoints = argsLength - 1;\n  const firstArg = args[0];\n  const lastArg = args[totalPoints];\n  const xPoints = [0];\n  const yPoints = [parseNumber(firstArg)];\n  for (let i = 1; i < totalPoints; i++) {\n    const arg = args[i];\n    const splitValue = isStr(arg) ?\n    /** @type {String} */(arg).trim().split(' ') :\n    [arg];\n    const value = splitValue[0];\n    const percent = splitValue[1];\n    xPoints.push(!isUnd(percent) ? parseNumber(percent) / 100 : i / totalPoints);\n    yPoints.push(parseNumber(value));\n  }\n  yPoints.push(parseNumber(lastArg));\n  xPoints.push(1);\n  return function easeLinear(t) {\n    for (let i = 1, l = xPoints.length; i < l; i++) {\n      const currentX = xPoints[i];\n      if (t <= currentX) {\n        const prevX = xPoints[i - 1];\n        const prevY = yPoints[i - 1];\n        return prevY + (yPoints[i] - prevY) * (t - prevX) / (currentX - prevX);\n      }\n    }\n    return yPoints[yPoints.length - 1];\n  }\n};\n\n/**\n * Generate random steps\n * @param  {Number} [length] - The number of steps\n * @param  {Number} [randomness] - How strong the randomness is\n * @return {EasingFunction}\n */\nconst irregular = (length = 10, randomness = 1) => {\n  const values = [0];\n  const total = length - 1;\n  for (let i = 1; i < total; i++) {\n    const previousValue = values[i - 1];\n    const spacing = i / total;\n    const segmentEnd = (i + 1) / total;\n    const randomVariation = spacing + (segmentEnd - spacing) * Math.random();\n    // Mix the even spacing and random variation based on the randomness parameter\n    const randomValue = spacing * (1 - randomness) + randomVariation * randomness;\n    values.push(clamp(randomValue, previousValue, 1));\n  }\n  values.push(1);\n  return linear(...values);\n};\n\n// Easing functions adapted from http://www.robertpenner.com/ease © Robert Penner\n\n/**\n * @callback PowerEasing\n * @param {Number|String} [power=1.675]\n * @return {EasingFunction}\n */\n\n/**\n * @callback BackEasing\n * @param {Number|String} [overshoot=1.70158]\n * @return {EasingFunction}\n */\n\n/**\n * @callback ElasticEasing\n * @param {Number|String} [amplitude=1]\n * @param {Number|String} [period=.3]\n * @return {EasingFunction}\n */\n\n/**\n * @callback EaseFactory\n * @param {Number|String} [paramA]\n * @param {Number|String} [paramB]\n * @return {EasingFunction|Number}\n */\n\n/** @typedef {PowerEasing|BackEasing|ElasticEasing} EasesFactory */\n\nconst halfPI = PI / 2;\nconst doublePI = PI * 2;\n/** @type {PowerEasing} */\nconst easeInPower = (p = 1.68) => t => pow(t, +p);\n\n/** @type {Record<String, EasesFactory|EasingFunction>} */\nconst easeInFunctions = {\n  [emptyString]: easeInPower,\n  Quad: easeInPower(2),\n  Cubic: easeInPower(3),\n  Quart: easeInPower(4),\n  Quint: easeInPower(5),\n  /** @type {EasingFunction} */\n  Sine: t => 1 - cos(t * halfPI),\n  /** @type {EasingFunction} */\n  Circ: t => 1 - sqrt(1 - t * t),\n  /** @type {EasingFunction} */\n  Expo: t => t ? pow(2, 10 * t - 10) : 0,\n  /** @type {EasingFunction} */\n  Bounce: t => {\n    let pow2, b = 4;\n    while (t < ((pow2 = pow(2, --b)) - 1) / 11);\n    return 1 / pow(4, 3 - b) - 7.5625 * pow((pow2 * 3 - 2) / 22 - t, 2);\n  },\n  /** @type {BackEasing} */\n  Back: (overshoot = 1.70158) => t => (+overshoot + 1) * t * t * t - +overshoot * t * t,\n  /** @type {ElasticEasing} */\n  Elastic: (amplitude = 1, period = .3) => {\n    const a = clamp(+amplitude, 1, 10);\n    const p = clamp(+period, minValue, 2);\n    const s = (p / doublePI) * asin(1 / a);\n    const e = doublePI / p;\n    return t => t === 0 || t === 1 ? t : -a * pow(2, -10 * (1 - t)) * sin(((1 - t) - s) * e);\n  }\n};\n\n/**\n * @callback EaseType\n * @param {EasingFunction} Ease\n * @return {EasingFunction}\n */\n\n/** @type {Record<String, EaseType>} */\nconst easeTypes = {\n  in: easeIn => t => easeIn(t),\n  out: easeIn => t => 1 - easeIn(1 - t),\n  inOut: easeIn => t => t < .5 ? easeIn(t * 2) / 2 : 1 - easeIn(t * -2 + 2) / 2,\n  outIn: easeIn => t => t < .5 ? (1 - easeIn(1 - t * 2)) / 2 : (easeIn(t * 2 - 1) + 1) / 2,\n};\n\n/**\n * @param  {String} string\n * @param  {Record<String, EasesFactory|EasingFunction>} easesFunctions\n * @param  {Object} easesLookups\n * @return {EasingFunction}\n */\nconst parseEaseString = (string, easesFunctions, easesLookups) => {\n  if (easesLookups[string]) return easesLookups[string];\n  if (string.indexOf('(') <= -1) {\n    const hasParams = easeTypes[string] || string.includes('Back') || string.includes('Elastic');\n    const parsedFn = /** @type {EasingFunction} */(hasParams ? /** @type {EasesFactory} */(easesFunctions[string])() : easesFunctions[string]);\n    return parsedFn ? easesLookups[string] = parsedFn : none;\n  } else {\n    const split = string.slice(0, -1).split('(');\n    const parsedFn = /** @type {EasesFactory} */(easesFunctions[split[0]]);\n    return parsedFn ? easesLookups[string] = parsedFn(...split[1].split(',')) : none;\n  }\n};\n\n/**\n * @typedef  {Object} EasesFunctions\n * @property {typeof linear} linear\n * @property {typeof irregular} irregular\n * @property {typeof steps} steps\n * @property {typeof cubicBezier} cubicBezier\n * @property {PowerEasing} in\n * @property {PowerEasing} out\n * @property {PowerEasing} inOut\n * @property {PowerEasing} outIn\n * @property {EasingFunction} inQuad\n * @property {EasingFunction} outQuad\n * @property {EasingFunction} inOutQuad\n * @property {EasingFunction} outInQuad\n * @property {EasingFunction} inCubic\n * @property {EasingFunction} outCubic\n * @property {EasingFunction} inOutCubic\n * @property {EasingFunction} outInCubic\n * @property {EasingFunction} inQuart\n * @property {EasingFunction} outQuart\n * @property {EasingFunction} inOutQuart\n * @property {EasingFunction} outInQuart\n * @property {EasingFunction} inQuint\n * @property {EasingFunction} outQuint\n * @property {EasingFunction} inOutQuint\n * @property {EasingFunction} outInQuint\n * @property {EasingFunction} inSine\n * @property {EasingFunction} outSine\n * @property {EasingFunction} inOutSine\n * @property {EasingFunction} outInSine\n * @property {EasingFunction} inCirc\n * @property {EasingFunction} outCirc\n * @property {EasingFunction} inOutCirc\n * @property {EasingFunction} outInCirc\n * @property {EasingFunction} inExpo\n * @property {EasingFunction} outExpo\n * @property {EasingFunction} inOutExpo\n * @property {EasingFunction} outInExpo\n * @property {EasingFunction} inBounce\n * @property {EasingFunction} outBounce\n * @property {EasingFunction} inOutBounce\n * @property {EasingFunction} outInBounce\n * @property {BackEasing} inBack\n * @property {BackEasing} outBack\n * @property {BackEasing} inOutBack\n * @property {BackEasing} outInBack\n * @property {ElasticEasing} inElastic\n * @property {ElasticEasing} outElastic\n * @property {ElasticEasing} inOutElastic\n * @property {ElasticEasing} outInElastic\n */\n\nconst eases = (/*#__PURE__*/ (() => {\n  const list = { linear, irregular, steps, cubicBezier };\n  for (let type in easeTypes) {\n    for (let name in easeInFunctions) {\n      const easeIn = easeInFunctions[name];\n      const easeType = easeTypes[type];\n      list[type + name] = /** @type {EasesFactory|EasingFunction} */(\n        name === emptyString || name === 'Back' || name === 'Elastic' ?\n        (a, b) => easeType(/** @type {EasesFactory} */(easeIn)(a, b)) :\n        easeType(/** @type {EasingFunction} */(easeIn))\n      );\n    }\n  }\n  return /** @type {EasesFunctions} */(list);\n})());\n\n/** @type {Record<String, EasingFunction>} */\nconst JSEasesLookups = { linear: none };\n\n/**\n * @param  {EasingParam} ease\n * @return {EasingFunction}\n */\nconst parseEasings = ease => isFnc(ease) ? ease :\n  isStr(ease) ? parseEaseString(/** @type {String} */(ease), eases, JSEasesLookups) :\n  none;\n\n\n\n\nconst propertyNamesCache = {};\n\n/**\n * @param  {String} propertyName\n * @param  {Target} target\n * @param  {tweenTypes} tweenType\n * @return {String}\n */\nconst sanitizePropertyName = (propertyName, target, tweenType) => {\n  if (tweenType === tweenTypes.TRANSFORM) {\n    const t = shortTransforms.get(propertyName);\n    return t ? t : propertyName;\n  } else if (\n    tweenType === tweenTypes.CSS ||\n    // Handle special cases where properties like \"strokeDashoffset\" needs to be set as \"stroke-dashoffset\"\n    // but properties like \"baseFrequency\" should stay in lowerCamelCase\n    (tweenType === tweenTypes.ATTRIBUTE && (isSvg(target) && propertyName in /** @type {DOMTarget} */(target).style))\n  ) {\n    const cachedPropertyName = propertyNamesCache[propertyName];\n    if (cachedPropertyName) {\n      return cachedPropertyName;\n    } else {\n      const lowerCaseName = propertyName ? toLowerCase(propertyName) : propertyName;\n      propertyNamesCache[propertyName] = lowerCaseName;\n      return lowerCaseName;\n    }\n  } else {\n    return propertyName;\n  }\n};\n\n\n\n\nconst angleUnitsMap = { 'deg': 1, 'rad': 180 / PI, 'turn': 360 };\nconst convertedValuesCache = {};\n\n/**\n * @param  {DOMTarget} el\n * @param  {TweenDecomposedValue} decomposedValue\n * @param  {String} unit\n * @param  {Boolean} [force]\n * @return {TweenDecomposedValue}\n */\nconst convertValueUnit = (el, decomposedValue, unit, force = false) => {\n  const currentUnit = decomposedValue.u;\n  const currentNumber = decomposedValue.n;\n  if (decomposedValue.t === valueTypes.UNIT && currentUnit === unit) { // TODO: Check if checking against the same unit string is necessary\n    return decomposedValue;\n  }\n  const cachedKey = currentNumber + currentUnit + unit;\n  const cached = convertedValuesCache[cachedKey];\n  if (!isUnd(cached) && !force) {\n    decomposedValue.n = cached;\n  } else {\n    let convertedValue;\n    if (currentUnit in angleUnitsMap) {\n      convertedValue = currentNumber * angleUnitsMap[currentUnit] / angleUnitsMap[unit];\n    } else {\n      const baseline = 100;\n      const tempEl = /** @type {DOMTarget} */(el.cloneNode());\n      const parentNode = el.parentNode;\n      const parentEl = (parentNode && (parentNode !== doc)) ? parentNode : doc.body;\n      parentEl.appendChild(tempEl);\n      const elStyle = tempEl.style;\n      elStyle.width = baseline + currentUnit;\n      const currentUnitWidth = /** @type {HTMLElement} */(tempEl).offsetWidth || baseline;\n      elStyle.width = baseline + unit;\n      const newUnitWidth = /** @type {HTMLElement} */(tempEl).offsetWidth || baseline;\n      const factor = currentUnitWidth / newUnitWidth;\n      parentEl.removeChild(tempEl);\n      convertedValue = factor * currentNumber;\n    }\n    decomposedValue.n = convertedValue;\n    convertedValuesCache[cachedKey] = convertedValue;\n  }\n  decomposedValue.t === valueTypes.UNIT;\n  decomposedValue.u = unit;\n  return decomposedValue;\n};\n\n\n\n\n/**\n * @template {Renderable} T\n * @param {T} renderable\n * @return {T}\n */\nconst cleanInlineStyles = renderable => {\n  // Allow cleanInlineStyles() to be called on timelines\n  if (renderable._hasChildren) {\n    forEachChildren(renderable, cleanInlineStyles, true);\n  } else {\n    const animation = /** @type {JSAnimation} */(renderable);\n    animation.pause();\n    forEachChildren(animation, (/** @type {Tween} */tween) => {\n      const tweenProperty = tween.property;\n      const tweenTarget = tween.target;\n      if (tweenTarget[isDomSymbol]) {\n        const targetStyle = /** @type {DOMTarget} */(tweenTarget).style;\n        const originalInlinedValue = animation._inlineStyles[tweenProperty];\n        if (tween._tweenType === tweenTypes.TRANSFORM) {\n          const cachedTransforms = tweenTarget[transformsSymbol];\n          if (isUnd(originalInlinedValue) || originalInlinedValue === emptyString) {\n            delete cachedTransforms[tweenProperty];\n          } else {\n            cachedTransforms[tweenProperty] = originalInlinedValue;\n          }\n          if (tween._renderTransforms) {\n            if (!Object.keys(cachedTransforms).length) {\n              targetStyle.removeProperty('transform');\n            } else {\n              let str = emptyString;\n              for (let key in cachedTransforms) {\n                str += transformsFragmentStrings[key] + cachedTransforms[key] + ') ';\n              }\n              targetStyle.transform = str;\n            }\n          }\n        } else {\n          if (isUnd(originalInlinedValue) || originalInlinedValue === emptyString) {\n            targetStyle.removeProperty(tweenProperty);\n          } else {\n            targetStyle[tweenProperty] = originalInlinedValue;\n          }\n        }\n        if (animation._tail === tween) {\n          animation.targets.forEach(t => {\n            if (t.getAttribute && t.getAttribute('style') === emptyString) {\n              t.removeAttribute('style');\n            }          });\n        }\n      }\n    });\n  }\n  return renderable;\n};\n\n// Defines decomposed values target objects only once and mutate their properties later to avoid GC\n// TODO: Maybe move the objects creation to values.js and use the decompose function to create the base object\nconst fromTargetObject = createDecomposedValueTargetObject();\nconst toTargetObject = createDecomposedValueTargetObject();\nconst toFunctionStore = { func: null };\nconst keyframesTargetArray = [null];\nconst fastSetValuesArray = [null, null];\n/** @type {TweenKeyValue} */\nconst keyObjectTarget = { to: null };\n\nlet tweenId = 0;\nlet keyframes;\n/** @type {TweenParamsOptions & TweenValues} */\nlet key;\n\n/**\n * @param {DurationKeyframes | PercentageKeyframes} keyframes\n * @param {AnimationParams} parameters\n * @return {AnimationParams}\n */\nconst generateKeyframes = (keyframes, parameters) => {\n  /** @type {AnimationParams} */\n  const properties = {};\n  if (isArr(keyframes)) {\n    const propertyNames = [].concat(.../** @type {DurationKeyframes} */(keyframes).map(key => Object.keys(key))).filter(isKey);\n    for (let i = 0, l = propertyNames.length; i < l; i++) {\n      const propName = propertyNames[i];\n      const propArray = /** @type {DurationKeyframes} */(keyframes).map(key => {\n        /** @type {TweenKeyValue} */\n        const newKey = {};\n        for (let p in key) {\n          const keyValue = /** @type {TweenPropValue} */(key[p]);\n          if (isKey(p)) {\n            if (p === propName) {\n              newKey.to = keyValue;\n            }\n          } else {\n            newKey[p] = keyValue;\n          }\n        }\n        return newKey;\n      });\n      properties[propName] = /** @type {ArraySyntaxValue} */(propArray);\n    }\n\n  } else {\n    const totalDuration = /** @type {Number} */(setValue(parameters.duration, globals.defaults.duration));\n    const keys = Object.keys(keyframes)\n    .map(key => { return {o: parseFloat(key) / 100, p: keyframes[key]} })\n    .sort((a, b) => a.o - b.o);\n    keys.forEach(key => {\n      const offset = key.o;\n      const prop = key.p;\n      for (let name in prop) {\n        if (isKey(name)) {\n          let propArray = /** @type {Array} */(properties[name]);\n          if (!propArray) propArray = properties[name] = [];\n          const duration = offset * totalDuration;\n          let length = propArray.length;\n          let prevKey = propArray[length - 1];\n          const keyObj = { to: prop[name] };\n          let durProgress = 0;\n          for (let i = 0; i < length; i++) {\n            durProgress += propArray[i].duration;\n          }\n          if (length === 1) {\n            keyObj.from = prevKey.to;\n          }\n          if (prop.ease) {\n            keyObj.ease = prop.ease;\n          }\n          keyObj.duration = duration - (length ? durProgress : 0);\n          propArray.push(keyObj);\n        }\n      }\n      return key;\n    });\n\n    for (let name in properties) {\n      const propArray = /** @type {Array} */(properties[name]);\n      let prevEase;\n      // let durProgress = 0\n      for (let i = 0, l = propArray.length; i < l; i++) {\n        const prop = propArray[i];\n        // Emulate WAPPI easing parameter position\n        const currentEase = prop.ease;\n        prop.ease = prevEase ? prevEase : undefined;\n        prevEase = currentEase;\n        // durProgress += prop.duration;\n        // if (i === l - 1 && durProgress !== totalDuration) {\n        //   propArray.push({ from: prop.to, ease: prop.ease, duration: totalDuration - durProgress })\n        // }\n      }\n      if (!propArray[0].duration) {\n        propArray.shift();\n      }\n    }\n\n  }\n\n  return properties;\n};\n\nclass JSAnimation extends Timer {\n  /**\n   * @param {TargetsParam} targets\n   * @param {AnimationParams} parameters\n   * @param {Timeline} [parent]\n   * @param {Number} [parentPosition]\n   * @param {Boolean} [fastSet=false]\n   * @param {Number} [index=0]\n   * @param {Number} [length=0]\n   */\n  constructor(\n    targets,\n    parameters,\n    parent,\n    parentPosition,\n    fastSet = false,\n    index = 0,\n    length = 0\n  ) {\n\n    super(/** @type {TimerParams&AnimationParams} */(parameters), parent, parentPosition);\n\n    const parsedTargets = registerTargets(targets);\n    const targetsLength = parsedTargets.length;\n\n    // If the parameters object contains a \"keyframes\" property, convert all the keyframes values to regular properties\n\n    const kfParams = /** @type {AnimationParams} */(parameters).keyframes;\n    const params = /** @type {AnimationParams} */(kfParams ? mergeObjects(generateKeyframes(/** @type {DurationKeyframes} */(kfParams), parameters), parameters) : parameters);\n\n    const {\n      delay,\n      duration,\n      ease,\n      playbackEase,\n      modifier,\n      composition,\n      onRender,\n    } = params;\n\n    const animDefaults = parent ? parent.defaults : globals.defaults;\n    const animaPlaybackEase = setValue(playbackEase, animDefaults.playbackEase);\n    const animEase = animaPlaybackEase ? parseEasings(animaPlaybackEase) : null;\n    const hasSpring = !isUnd(ease) && !isUnd(/** @type {Spring} */(ease).ease);\n    const tEasing = hasSpring ? /** @type {Spring} */(ease).ease : setValue(ease, animEase ? 'linear' : animDefaults.ease);\n    const tDuration = hasSpring ? /** @type {Spring} */(ease).duration : setValue(duration, animDefaults.duration);\n    const tDelay = setValue(delay, animDefaults.delay);\n    const tModifier = modifier || animDefaults.modifier;\n    // If no composition is defined and the targets length is high (>= 1000) set the composition to 'none' (0) for faster tween creation\n    const tComposition = isUnd(composition) && targetsLength >= K ? compositionTypes.none : !isUnd(composition) ? composition : animDefaults.composition;\n    // TODO: Do not create an empty object until we know the animation will generate inline styles\n    const animInlineStyles = {};\n    // const absoluteOffsetTime = this._offset;\n    const absoluteOffsetTime = this._offset + (parent ? parent._offset : 0);\n\n    let iterationDuration = NaN;\n    let iterationDelay = NaN;\n    let animationAnimationLength = 0;\n    let shouldTriggerRender = 0;\n\n    for (let targetIndex = 0; targetIndex < targetsLength; targetIndex++) {\n\n      const target = parsedTargets[targetIndex];\n      const ti = index || targetIndex;\n      const tl = length || targetsLength;\n\n      let lastTransformGroupIndex = NaN;\n      let lastTransformGroupLength = NaN;\n\n      for (let p in params) {\n\n        if (isKey(p)) {\n\n          const tweenType = getTweenType(target, p);\n\n          const propName = sanitizePropertyName(p, target, tweenType);\n\n          let propValue = params[p];\n\n          const isPropValueArray = isArr(propValue);\n\n          if (fastSet && !isPropValueArray) {\n            fastSetValuesArray[0] = propValue;\n            fastSetValuesArray[1] = propValue;\n            propValue = fastSetValuesArray;\n          }\n\n          // TODO: Allow nested keyframes inside ObjectValue value (prop: { to: [.5, 1, .75, 2, 3] })\n          // Normalize property values to valid keyframe syntax:\n          // [x, y] to [{to: [x, y]}] or {to: x} to [{to: x}] or keep keys syntax [{}, {}, {}...]\n          // const keyframes = isArr(propValue) ? propValue.length === 2 && !isObj(propValue[0]) ? [{ to: propValue }] : propValue : [propValue];\n          if (isPropValueArray) {\n            const arrayLength = /** @type {Array} */(propValue).length;\n            const isNotObjectValue = !isObj(propValue[0]);\n            // Convert [x, y] to [{to: [x, y]}]\n            if (arrayLength === 2 && isNotObjectValue) {\n              keyObjectTarget.to = /** @type {TweenParamValue} */(/** @type {unknown} */(propValue));\n              keyframesTargetArray[0] = keyObjectTarget;\n              keyframes = keyframesTargetArray;\n            // Convert [x, y, z] to [[x, y], z]\n            } else if (arrayLength > 2 && isNotObjectValue) {\n              keyframes = [];\n              /** @type {Array.<Number>} */(propValue).forEach((v, i) => {\n                if (!i) {\n                  fastSetValuesArray[0] = v;\n                } else if (i === 1) {\n                  fastSetValuesArray[1] = v;\n                  keyframes.push(fastSetValuesArray);\n                } else {\n                  keyframes.push(v);\n                }\n              });\n            } else {\n              keyframes = /** @type {Array.<TweenKeyValue>} */(propValue);\n            }\n          } else {\n            keyframesTargetArray[0] = propValue;\n            keyframes = keyframesTargetArray;\n          }\n\n          let siblings = null;\n          let prevTween = null;\n          let firstTweenChangeStartTime = NaN;\n          let lastTweenChangeEndTime = 0;\n          let tweenIndex = 0;\n\n          for (let l = keyframes.length; tweenIndex < l; tweenIndex++) {\n\n            const keyframe = keyframes[tweenIndex];\n\n            if (isObj(keyframe)) {\n              key = keyframe;\n            } else {\n              keyObjectTarget.to = /** @type {TweenParamValue} */(keyframe);\n              key = keyObjectTarget;\n            }\n\n            toFunctionStore.func = null;\n\n            const computedToValue = getFunctionValue(key.to, target, ti, tl, toFunctionStore);\n\n            let tweenToValue;\n            // Allows function based values to return an object syntax value ({to: v})\n            if (isObj(computedToValue) && !isUnd(computedToValue.to)) {\n              key = computedToValue;\n              tweenToValue = computedToValue.to;\n            } else {\n              tweenToValue = computedToValue;\n            }\n            const tweenFromValue = getFunctionValue(key.from, target, ti, tl);\n            const keyEasing = key.ease;\n            const hasSpring = !isUnd(keyEasing) && !isUnd(/** @type {Spring} */(keyEasing).ease);\n            // Easing are treated differently and don't accept function based value to prevent having to pass a function wrapper that returns an other function all the time\n            const tweenEasing = hasSpring ? /** @type {Spring} */(keyEasing).ease : keyEasing || tEasing;\n            // Calculate default individual keyframe duration by dividing the tl of keyframes\n            const tweenDuration = hasSpring ? /** @type {Spring} */(keyEasing).duration : getFunctionValue(setValue(key.duration, (l > 1 ? getFunctionValue(tDuration, target, ti, tl) / l : tDuration)), target, ti, tl);\n            // Default delay value should only be applied to the first tween\n            const tweenDelay = getFunctionValue(setValue(key.delay, (!tweenIndex ? tDelay : 0)), target, ti, tl);\n            const computedComposition = getFunctionValue(setValue(key.composition, tComposition), target, ti, tl);\n            const tweenComposition = isNum(computedComposition) ? computedComposition : compositionTypes[computedComposition];\n            // Modifiers are treated differently and don't accept function based value to prevent having to pass a function wrapper\n            const tweenModifier = key.modifier || tModifier;\n            const hasFromvalue = !isUnd(tweenFromValue);\n            const hasToValue = !isUnd(tweenToValue);\n            const isFromToArray = isArr(tweenToValue);\n            const isFromToValue = isFromToArray || (hasFromvalue && hasToValue);\n            const tweenStartTime = prevTween ? lastTweenChangeEndTime + tweenDelay : tweenDelay;\n            const absoluteStartTime = absoluteOffsetTime + tweenStartTime;\n\n            // Force a onRender callback if the animation contains at least one from value and autoplay is set to false\n            if (!shouldTriggerRender && (hasFromvalue || isFromToArray)) shouldTriggerRender = 1;\n\n            let prevSibling = prevTween;\n\n            if (tweenComposition !== compositionTypes.none) {\n              if (!siblings) siblings = getTweenSiblings(target, propName);\n              let nextSibling = siblings._head;\n              // Iterate trough all the next siblings until we find a sibling with an equal or inferior start time\n              while (nextSibling && !nextSibling._isOverridden && nextSibling._absoluteStartTime <= absoluteStartTime) {\n                prevSibling = nextSibling;\n                nextSibling = nextSibling._nextRep;\n                // Overrides all the next siblings if the next sibling starts at the same time of after as the new tween start time\n                if (nextSibling && nextSibling._absoluteStartTime >= absoluteStartTime) {\n                  while (nextSibling) {\n                    overrideTween(nextSibling);\n                    // This will ends both the current while loop and the upper one once all the next sibllings have been overriden\n                    nextSibling = nextSibling._nextRep;\n                  }\n                }\n              }\n            }\n\n            // Decompose values\n            if (isFromToValue) {\n              decomposeRawValue(isFromToArray ? getFunctionValue(tweenToValue[0], target, ti, tl) : tweenFromValue, fromTargetObject);\n              decomposeRawValue(isFromToArray ? getFunctionValue(tweenToValue[1], target, ti, tl, toFunctionStore) : tweenToValue, toTargetObject);\n              if (fromTargetObject.t === valueTypes.NUMBER) {\n                if (prevSibling) {\n                  if (prevSibling._valueType === valueTypes.UNIT) {\n                    fromTargetObject.t = valueTypes.UNIT;\n                    fromTargetObject.u = prevSibling._unit;\n                  }\n                } else {\n                  decomposeRawValue(\n                    getOriginalAnimatableValue(target, propName, tweenType, animInlineStyles),\n                    decomposedOriginalValue\n                  );\n                  if (decomposedOriginalValue.t === valueTypes.UNIT) {\n                    fromTargetObject.t = valueTypes.UNIT;\n                    fromTargetObject.u = decomposedOriginalValue.u;\n                  }\n                }\n              }\n            } else {\n              if (hasToValue) {\n                decomposeRawValue(tweenToValue, toTargetObject);\n              } else {\n                if (prevTween) {\n                  decomposeTweenValue(prevTween, toTargetObject);\n                } else {\n                  // No need to get and parse the original value if the tween is part of a timeline and has a previous sibling part of the same timeline\n                  decomposeRawValue(parent && prevSibling && prevSibling.parent.parent === parent ? prevSibling._value :\n                  getOriginalAnimatableValue(target, propName, tweenType, animInlineStyles), toTargetObject);\n                }\n              }\n              if (hasFromvalue) {\n                decomposeRawValue(tweenFromValue, fromTargetObject);\n              } else {\n                if (prevTween) {\n                  decomposeTweenValue(prevTween, fromTargetObject);\n                } else {\n                  decomposeRawValue(parent && prevSibling && prevSibling.parent.parent === parent ? prevSibling._value :\n                  // No need to get and parse the original value if the tween is part of a timeline and has a previous sibling part of the same timeline\n                  getOriginalAnimatableValue(target, propName, tweenType, animInlineStyles), fromTargetObject);\n                }\n              }\n            }\n\n            // Apply operators\n            if (fromTargetObject.o) {\n              fromTargetObject.n = getRelativeValue(\n                !prevSibling ? decomposeRawValue(\n                  getOriginalAnimatableValue(target, propName, tweenType, animInlineStyles),\n                  decomposedOriginalValue\n                ).n : prevSibling._toNumber,\n                fromTargetObject.n,\n                fromTargetObject.o\n              );\n            }\n\n            if (toTargetObject.o) {\n              toTargetObject.n = getRelativeValue(fromTargetObject.n, toTargetObject.n, toTargetObject.o);\n            }\n\n            // Values omogenisation in cases of type difference between \"from\" and \"to\"\n            if (fromTargetObject.t !== toTargetObject.t) {\n              if (fromTargetObject.t === valueTypes.COMPLEX || toTargetObject.t === valueTypes.COMPLEX) {\n                const complexValue = fromTargetObject.t === valueTypes.COMPLEX ? fromTargetObject : toTargetObject;\n                const notComplexValue = fromTargetObject.t === valueTypes.COMPLEX ? toTargetObject : fromTargetObject;\n                notComplexValue.t = valueTypes.COMPLEX;\n                notComplexValue.s = cloneArray(complexValue.s);\n                notComplexValue.d = complexValue.d.map(() => notComplexValue.n);\n              } else if (fromTargetObject.t === valueTypes.UNIT || toTargetObject.t === valueTypes.UNIT) {\n                const unitValue = fromTargetObject.t === valueTypes.UNIT ? fromTargetObject : toTargetObject;\n                const notUnitValue = fromTargetObject.t === valueTypes.UNIT ? toTargetObject : fromTargetObject;\n                notUnitValue.t = valueTypes.UNIT;\n                notUnitValue.u = unitValue.u;\n              } else if (fromTargetObject.t === valueTypes.COLOR || toTargetObject.t === valueTypes.COLOR) {\n                const colorValue = fromTargetObject.t === valueTypes.COLOR ? fromTargetObject : toTargetObject;\n                const notColorValue = fromTargetObject.t === valueTypes.COLOR ? toTargetObject : fromTargetObject;\n                notColorValue.t = valueTypes.COLOR;\n                notColorValue.s = colorValue.s;\n                notColorValue.d = [0, 0, 0, 1];\n              }\n            }\n\n            // Unit conversion\n            if (fromTargetObject.u !== toTargetObject.u) {\n              let valueToConvert = toTargetObject.u ? fromTargetObject : toTargetObject;\n              valueToConvert = convertValueUnit(/** @type {DOMTarget} */(target), valueToConvert, toTargetObject.u ? toTargetObject.u : fromTargetObject.u, false);\n              // TODO:\n              // convertValueUnit(target, to.u ? from : to, to.u ? to.u : from.u);\n            }\n\n            // Fill in non existing complex values\n            if (toTargetObject.d && fromTargetObject.d && (toTargetObject.d.length !== fromTargetObject.d.length)) {\n              const longestValue = fromTargetObject.d.length > toTargetObject.d.length ? fromTargetObject : toTargetObject;\n              const shortestValue = longestValue === fromTargetObject ? toTargetObject : fromTargetObject;\n              // TODO: Check if n should be used instead of 0 for default complex values\n              shortestValue.d = longestValue.d.map((_, i) => isUnd(shortestValue.d[i]) ? 0 : shortestValue.d[i]);\n              shortestValue.s = cloneArray(longestValue.s);\n            }\n\n            // Tween factory\n\n            // Rounding is necessary here to minimize floating point errors\n            const tweenUpdateDuration = round(+tweenDuration || minValue, 12);\n\n            /** @type {Tween} */\n            const tween = {\n              parent: this,\n              id: tweenId++,\n              property: propName,\n              target: target,\n              _value: null,\n              _func: toFunctionStore.func,\n              _ease: parseEasings(tweenEasing),\n              _fromNumbers: cloneArray(fromTargetObject.d),\n              _toNumbers: cloneArray(toTargetObject.d),\n              _strings: cloneArray(toTargetObject.s),\n              _fromNumber: fromTargetObject.n,\n              _toNumber: toTargetObject.n,\n              _numbers: cloneArray(fromTargetObject.d), // For additive tween and animatables\n              _number: fromTargetObject.n, // For additive tween and animatables\n              _unit: toTargetObject.u,\n              _modifier: tweenModifier,\n              _currentTime: 0,\n              _startTime: tweenStartTime,\n              _delay: +tweenDelay,\n              _updateDuration: tweenUpdateDuration,\n              _changeDuration: tweenUpdateDuration,\n              _absoluteStartTime: absoluteStartTime,\n              // NOTE: Investigate bit packing to stores ENUM / BOOL\n              _tweenType: tweenType,\n              _valueType: toTargetObject.t,\n              _composition: tweenComposition,\n              _isOverlapped: 0,\n              _isOverridden: 0,\n              _renderTransforms: 0,\n              _prevRep: null, // For replaced tween\n              _nextRep: null, // For replaced tween\n              _prevAdd: null, // For additive tween\n              _nextAdd: null, // For additive tween\n              _prev: null,\n              _next: null,\n            };\n\n            if (tweenComposition !== compositionTypes.none) {\n              composeTween(tween, siblings);\n            }\n\n            if (isNaN(firstTweenChangeStartTime)) {\n              firstTweenChangeStartTime = tween._startTime;\n            }\n            // Rounding is necessary here to minimize floating point errors\n            lastTweenChangeEndTime = round(tweenStartTime + tweenUpdateDuration, 12);\n            prevTween = tween;\n            animationAnimationLength++;\n\n            addChild(this, tween);\n\n          }\n\n          // Update animation timings with the added tweens properties\n\n          if (isNaN(iterationDelay) || firstTweenChangeStartTime < iterationDelay) {\n            iterationDelay = firstTweenChangeStartTime;\n          }\n\n          if (isNaN(iterationDuration) || lastTweenChangeEndTime > iterationDuration) {\n            iterationDuration = lastTweenChangeEndTime;\n          }\n\n          // TODO: Find a way to inline tween._renderTransforms = 1 here\n          if (tweenType === tweenTypes.TRANSFORM) {\n            lastTransformGroupIndex = animationAnimationLength - tweenIndex;\n            lastTransformGroupLength = animationAnimationLength;\n          }\n\n        }\n\n      }\n\n      // Set _renderTransforms to last transform property to correctly render the transforms list\n      if (!isNaN(lastTransformGroupIndex)) {\n        let i = 0;\n        forEachChildren(this, (/** @type {Tween} */tween) => {\n          if (i >= lastTransformGroupIndex && i < lastTransformGroupLength) {\n            tween._renderTransforms = 1;\n            if (tween._composition === compositionTypes.blend) {\n              forEachChildren(additive.animation, (/** @type {Tween} */additiveTween) => {\n                if (additiveTween.id === tween.id) {\n                  additiveTween._renderTransforms = 1;\n                }\n              });\n            }\n          }\n          i++;\n        });\n      }\n\n    }\n\n    if (!targetsLength) {\n      console.warn(`No target found. Make sure the element you're trying to animate is accessible before creating your animation.`);\n    }\n\n    if (iterationDelay) {\n      forEachChildren(this, (/** @type {Tween} */tween) => {\n        // If (startTime - delay) equals 0, this means the tween is at the begining of the animation so we need to trim the delay too\n        if (!(tween._startTime - tween._delay)) {\n          tween._delay -= iterationDelay;\n        }\n        tween._startTime -= iterationDelay;\n      });\n      iterationDuration -= iterationDelay;\n    } else {\n      iterationDelay = 0;\n    }\n\n    // Prevents iterationDuration to be NaN if no valid animatable props have been provided\n    // Prevents _iterationCount to be NaN if no valid animatable props have been provided\n    if (!iterationDuration) {\n      iterationDuration = minValue;\n      this.iterationCount = 0;\n    }\n    /** @type {TargetsArray} */\n    this.targets = parsedTargets;\n    /** @type {Number} */\n    this.duration = iterationDuration === minValue ? minValue : clampInfinity(((iterationDuration + this._loopDelay) * this.iterationCount) - this._loopDelay) || minValue;\n    /** @type {Callback<this>} */\n    this.onRender = onRender || animDefaults.onRender;\n    /** @type {EasingFunction} */\n    this._ease = animEase;\n    /** @type {Number} */\n    this._delay = iterationDelay;\n    // NOTE: I'm keeping delay values separated from offsets in timelines because delays can override previous tweens and it could be confusing to debug a timeline with overridden tweens and no associated visible delays.\n    // this._delay = parent ? 0 : iterationDelay;\n    // this._offset += parent ? iterationDelay : 0;\n    /** @type {Number} */\n    this.iterationDuration = iterationDuration;\n    /** @type {{}} */\n    this._inlineStyles = animInlineStyles;\n\n    if (!this._autoplay && shouldTriggerRender) this.onRender(this);\n  }\n\n  /**\n   * @param  {Number} newDuration\n   * @return {this}\n   */\n  stretch(newDuration) {\n    const currentDuration = this.duration;\n    if (currentDuration === normalizeTime(newDuration)) return this;\n    const timeScale = newDuration / currentDuration;\n    // NOTE: Find a better way to handle the stretch of an animation after stretch = 0\n    forEachChildren(this, (/** @type {Tween} */tween) => {\n      // Rounding is necessary here to minimize floating point errors\n      tween._updateDuration = normalizeTime(tween._updateDuration * timeScale);\n      tween._changeDuration = normalizeTime(tween._changeDuration * timeScale);\n      tween._currentTime *= timeScale;\n      tween._startTime *= timeScale;\n      tween._absoluteStartTime *= timeScale;\n    });\n    return super.stretch(newDuration);\n  }\n\n  /**\n   * @return {this}\n   */\n  refresh() {\n    forEachChildren(this, (/** @type {Tween} */tween) => {\n      const ogValue = getOriginalAnimatableValue(tween.target, tween.property, tween._tweenType);\n      decomposeRawValue(ogValue, decomposedOriginalValue);\n      tween._fromNumbers = cloneArray(decomposedOriginalValue.d);\n      tween._fromNumber = decomposedOriginalValue.n;\n      if (tween._func) {\n        decomposeRawValue(tween._func(), toTargetObject);\n        tween._toNumbers = cloneArray(toTargetObject.d);\n        tween._strings = cloneArray(toTargetObject.s);\n        tween._toNumber = toTargetObject.n;\n      }\n    });\n    return this;\n  }\n\n  /**\n   * Cancel the animation and revert all the values affected by this animation to their original state\n   * @return {this}\n   */\n  revert() {\n    super.revert();\n    return cleanInlineStyles(this);\n  }\n\n  /**\n   * @param  {Callback<this>} [callback]\n   * @return {Promise}\n   */\n  then(callback) {\n    return super.then(callback);\n  }\n\n}\n\n/**\n * @param {TargetsParam} targets\n * @param {AnimationParams} parameters\n * @return {JSAnimation}\n */\nconst animate = (targets, parameters) => new JSAnimation(targets, parameters, null, 0, false).init();\n\n\n\n\n/**\n * Converts an easing function into a valid CSS linear() timing function string\n * @param {EasingFunction} fn\n * @param {number} [samples=100]\n * @returns {string} CSS linear() timing function\n */\nconst easingToLinear = (fn, samples = 100) => {\n  const points = [];\n  for (let i = 0; i <= samples; i++) points.push(fn(i / samples));\n  return `linear(${points.join(', ')})`;\n};\n\nconst WAAPIEasesLookups = {\n  in: 'ease-in',\n  out: 'ease-out',\n  inOut: 'ease-in-out',\n};\n\nconst WAAPIeases = /*#__PURE__*/(() => {\n  const list = {};\n  for (let type in easeTypes) list[type] = a => easeTypes[type](easeInPower(a));\n  return /** @type {Record<String, EasingFunction>} */(list);\n})();\n\n/**\n * @param  {EasingParam} ease\n * @return {String}\n */\nconst parseWAAPIEasing = (ease) => {\n  let parsedEase = WAAPIEasesLookups[ease];\n  if (parsedEase) return parsedEase;\n  parsedEase = 'linear';\n  if (isStr(ease)) {\n    if (\n      stringStartsWith(ease, 'linear') ||\n      stringStartsWith(ease, 'cubic-') ||\n      stringStartsWith(ease, 'steps') ||\n      stringStartsWith(ease, 'ease')\n    ) {\n      parsedEase = ease;\n    } else if (stringStartsWith(ease, 'cubicB')) {\n      parsedEase = toLowerCase(ease);\n    } else {\n      const parsed = parseEaseString(ease, WAAPIeases, WAAPIEasesLookups);\n      if (isFnc(parsed)) parsedEase = parsed === none ? 'linear' : easingToLinear(parsed);\n    }\n    WAAPIEasesLookups[ease] = parsedEase;\n  } else if (isFnc(ease)) {\n    const easing = easingToLinear(ease);\n    if (easing) parsedEase = easing;\n  } else if (/** @type {Spring} */(ease).ease) {\n    parsedEase = easingToLinear(/** @type {Spring} */(ease).ease);\n  }\n  return parsedEase;\n};\n\n/**\n * @typedef {String|Number|Array<String>|Array<Number>} WAAPITweenValue\n */\n\n/**\n * @callback WAAPIFunctionvalue\n * @param {DOMTarget} target - The animated target\n * @param {Number} index - The target index\n * @param {Number} length - The total number of animated targets\n * @return {WAAPITweenValue}\n */\n\n/**\n * @typedef {WAAPITweenValue|WAAPIFunctionvalue|Array<String|Number|WAAPIFunctionvalue>} WAAPIKeyframeValue\n */\n\n/**\n * @typedef {(animation: WAAPIAnimation) => void} WAAPICallback\n */\n\n/**\n * @typedef {Object} WAAPITweenOptions\n * @property {WAAPIKeyframeValue} [to]\n * @property {WAAPIKeyframeValue} [from]\n * @property {Number|WAAPIFunctionvalue} [duration]\n * @property {Number|WAAPIFunctionvalue} [delay]\n * @property {EasingParam} [ease]\n * @property {CompositeOperation} [composition]\n */\n\n/**\n * @typedef {Object} WAAPIAnimationOptions\n * @property {Number|Boolean} [loop]\n * @property {Boolean} [Reversed]\n * @property {Boolean} [Alternate]\n * @property {Boolean|ScrollObserver} [autoplay]\n * @property {Number} [playbackRate]\n * @property {Number|WAAPIFunctionvalue} [duration]\n * @property {Number|WAAPIFunctionvalue} [delay]\n * @property {EasingParam} [ease]\n * @property {CompositeOperation} [composition]\n * @property {WAAPICallback} [onComplete]\n */\n\n/**\n * @typedef {Record<String, WAAPIKeyframeValue | WAAPIAnimationOptions | Boolean | ScrollObserver | WAAPICallback | EasingParam | WAAPITweenOptions> & WAAPIAnimationOptions} WAAPIAnimationParams\n */\n\nconst transformsShorthands = ['x', 'y', 'z'];\nconst commonDefaultPXProperties = [\n  'perspective',\n  'width',\n  'height',\n  'margin',\n  'padding',\n  'top',\n  'right',\n  'bottom',\n  'left',\n  'borderWidth',\n  'fontSize',\n  'borderRadius',\n  ...transformsShorthands\n];\n\nconst validIndividualTransforms = [...transformsShorthands, ...validTransforms.filter(t => ['X', 'Y', 'Z'].some(axis => t.endsWith(axis)))];\n\n// Setting it to true in case CSS.registerProperty is not supported will automatically skip the registration and fallback to no animation\nlet transformsPropertiesRegistered = isBrowser && (isUnd(CSS) || !Object.hasOwnProperty.call(CSS, 'registerProperty'));\n\nconst registerTransformsProperties = () => {\n  if (transformsPropertiesRegistered) return;\n  validTransforms.forEach(t => {\n    const isSkew = stringStartsWith(t, 'skew');\n    const isScale = stringStartsWith(t, 'scale');\n    const isRotate = stringStartsWith(t, 'rotate');\n    const isTranslate = stringStartsWith(t, 'translate');\n    const isAngle = isRotate || isSkew;\n    const syntax = isAngle ? '<angle>' : isScale ? \"<number>\" : isTranslate ? \"<length-percentage>\" : \"*\";\n    try {\n      CSS.registerProperty({\n        name: '--' + t,\n        syntax,\n        inherits: false,\n        initialValue: isTranslate ? '0px' : isAngle ? '0deg' : isScale ? '1' : '0',\n      });\n    } catch {}  });\n  transformsPropertiesRegistered = true;\n};\n\nconst WAAPIAnimationsLookups = {\n  _head: null,\n  _tail: null,\n};\n\n/**\n * @param {DOMTarget} $el\n * @param {String} [property]\n * @param {WAAPIAnimation} [parent]\n */\nconst removeWAAPIAnimation = ($el, property, parent) => {\n  let nextLookup = WAAPIAnimationsLookups._head;\n  while (nextLookup) {\n    const next = nextLookup._next;\n    const matchTarget = nextLookup.$el === $el;\n    const matchProperty = !property || nextLookup.property === property;\n    const matchParent = !parent || nextLookup.parent === parent;\n    if (matchTarget && matchProperty && matchParent) {\n      const anim = nextLookup.animation;\n      try { anim.commitStyles(); } catch {}      anim.cancel();\n      removeChild(WAAPIAnimationsLookups, nextLookup);\n      const lookupParent = nextLookup.parent;\n      if (lookupParent) {\n        lookupParent._completed++;\n        if (lookupParent.animations.length === lookupParent._completed) {\n          lookupParent.completed = true;\n          if (!lookupParent.muteCallbacks) {\n            lookupParent.paused = true;\n            lookupParent.onComplete(lookupParent);\n            lookupParent._resolve(lookupParent);\n          }\n        }\n      }\n    }\n    nextLookup = next;\n  }\n};\n\n/**\n * @param {WAAPIAnimation} parent\n * @param {DOMTarget} $el\n * @param {String} property\n * @param {PropertyIndexedKeyframes} keyframes\n * @param {KeyframeAnimationOptions} params\n * @retun {Animation}\n */\nconst addWAAPIAnimation = (parent, $el, property, keyframes, params) => {\n  const animation = $el.animate(keyframes, params);\n  const animTotalDuration = params.delay + (+params.duration * params.iterations);\n  animation.playbackRate = parent._speed;\n  if (parent.paused) animation.pause();\n  if (parent.duration < animTotalDuration) {\n    parent.duration = animTotalDuration;\n    parent.controlAnimation = animation;\n  }\n  parent.animations.push(animation);\n  removeWAAPIAnimation($el, property);\n  addChild(WAAPIAnimationsLookups, { parent, animation, $el, property, _next: null, _prev: null });\n  const handleRemove = () => { removeWAAPIAnimation($el, property, parent); };\n  animation.onremove = handleRemove;\n  animation.onfinish = handleRemove;\n  return animation;\n};\n\n/**\n * @param  {String} propName\n * @param  {WAAPIKeyframeValue} value\n * @param  {DOMTarget} $el\n * @param  {Number} i\n * @param  {Number} targetsLength\n * @return {String}\n */\nconst normalizeTweenValue = (propName, value, $el, i, targetsLength) => {\n  let v = getFunctionValue(/** @type {any} */(value), $el, i, targetsLength);\n  if (!isNum(v)) return v;\n  if (commonDefaultPXProperties.includes(propName) || stringStartsWith(propName, 'translate')) return `${v}px`;\n  if (stringStartsWith(propName, 'rotate') || stringStartsWith(propName, 'skew')) return `${v}deg`;\n  return `${v}`;\n};\n\n/**\n * @param  {DOMTarget} $el\n * @param  {String} propName\n * @param  {WAAPIKeyframeValue} from\n * @param  {WAAPIKeyframeValue} to\n * @param  {Number} i\n * @param  {Number} targetsLength\n * @return {WAAPITweenValue}\n */\nconst parseIndividualTweenValue = ($el, propName, from, to, i, targetsLength) => {\n  /** @type {WAAPITweenValue} */\n  let tweenValue = '0';\n  const computedTo = !isUnd(to) ? normalizeTweenValue(propName, to, $el, i, targetsLength) : getComputedStyle($el)[propName];\n  if (!isUnd(from)) {\n    const computedFrom = normalizeTweenValue(propName, from, $el, i, targetsLength);\n    tweenValue = [computedFrom, computedTo];\n  } else {\n    tweenValue = isArr(to) ? to.map((/** @type {any} */v) => normalizeTweenValue(propName, v, $el, i, targetsLength)) : computedTo;\n  }\n  return tweenValue;\n};\n\nclass WAAPIAnimation {\n/**\n * @param {DOMTargetsParam} targets\n * @param {WAAPIAnimationParams} params\n */\n  constructor(targets, params) {\n\n    if (globals.scope) globals.scope.revertibles.push(this);\n\n    registerTransformsProperties();\n\n    const parsedTargets = registerTargets(targets);\n    const targetsLength = parsedTargets.length;\n\n    if (!targetsLength) {\n      console.warn(`No target found. Make sure the element you're trying to animate is accessible before creating your animation.`);\n    }\n\n    const ease = setValue(params.ease, parseWAAPIEasing(globals.defaults.ease));\n    const spring = /** @type {Spring} */(ease).ease && ease;\n    const autoplay = setValue(params.autoplay, globals.defaults.autoplay);\n    const scroll = autoplay && /** @type {ScrollObserver} */(autoplay).link ? autoplay : false;\n    const alternate = params.alternate && /** @type {Boolean} */(params.alternate) === true;\n    const reversed = params.reversed && /** @type {Boolean} */(params.reversed) === true;\n    const loop = setValue(params.loop, globals.defaults.loop);\n    const iterations = /** @type {Number} */((loop === true || loop === Infinity) ? Infinity : isNum(loop) ? loop + 1 : 1);\n    /** @type {PlaybackDirection} */\n    const direction = alternate ? reversed ? 'alternate-reverse' : 'alternate' : reversed ? 'reverse' : 'normal';\n    /** @type {FillMode} */\n    const fill = 'forwards';\n    /** @type {String} */\n    const easing = parseWAAPIEasing(ease);\n    const timeScale = (globals.timeScale === 1 ? 1 : K);\n\n    /** @type {DOMTargetsArray}] */\n    this.targets = parsedTargets;\n    /** @type {Array<globalThis.Animation>}] */\n    this.animations = [];\n    /** @type {globalThis.Animation}] */\n    this.controlAnimation = null;\n    /** @type {Callback<this>} */\n    this.onComplete = params.onComplete || noop;\n    /** @type {Number} */\n    this.duration = 0;\n    /** @type {Boolean} */\n    this.muteCallbacks = false;\n    /** @type {Boolean} */\n    this.completed = false;\n    /** @type {Boolean} */\n    this.paused = !autoplay || scroll !== false;\n    /** @type {Boolean} */\n    this.reversed = reversed;\n    /** @type {Boolean|ScrollObserver} */\n    this.autoplay = autoplay;\n    /** @type {Number} */\n    this._speed = setValue(params.playbackRate, globals.defaults.playbackRate);\n    /** @type {Function} */\n    this._resolve = noop; // Used by .then()\n    /** @type {Number} */\n    this._completed = 0;\n    /** @type {Array<Object>}] */\n    this._inlineStyles = parsedTargets.map($el => $el.getAttribute('style'));\n\n    parsedTargets.forEach(($el, i) => {\n\n      const cachedTransforms = $el[transformsSymbol];\n\n      const hasIndividualTransforms = validIndividualTransforms.some(t => params.hasOwnProperty(t));\n\n      /** @type {Number} */\n      const duration = (spring ? /** @type {Spring} */(spring).duration : getFunctionValue(setValue(params.duration, globals.defaults.duration), $el, i, targetsLength)) * timeScale;\n      /** @type {Number} */\n      const delay = getFunctionValue(setValue(params.delay, globals.defaults.delay), $el, i, targetsLength) * timeScale;\n      /** @type {CompositeOperation} */\n      const composite = /** @type {CompositeOperation} */(setValue(params.composition, 'replace'));\n\n      for (let name in params) {\n        if (!isKey(name)) continue;\n        /** @type {PropertyIndexedKeyframes} */\n        const keyframes = {};\n        /** @type {KeyframeAnimationOptions} */\n        const tweenParams = { iterations, direction, fill, easing, duration, delay, composite };\n        const propertyValue = params[name];\n        const individualTransformProperty = hasIndividualTransforms ? validTransforms.includes(name) ? name : shortTransforms.get(name) : false;\n        let parsedPropertyValue;\n        if (isObj(propertyValue)) {\n          const tweenOptions = /** @type {WAAPITweenOptions} */(propertyValue);\n          const tweenOptionsEase = setValue(tweenOptions.ease, ease);\n          const tweenOptionsSpring = /** @type {Spring} */(tweenOptionsEase).ease && tweenOptionsEase;\n          const to = /** @type {WAAPITweenOptions} */(tweenOptions).to;\n          const from = /** @type {WAAPITweenOptions} */(tweenOptions).from;\n          /** @type {Number} */\n          tweenParams.duration = (tweenOptionsSpring ? /** @type {Spring} */(tweenOptionsSpring).duration : getFunctionValue(setValue(tweenOptions.duration, duration), $el, i, targetsLength)) * timeScale;\n          /** @type {Number} */\n          tweenParams.delay = getFunctionValue(setValue(tweenOptions.delay, delay), $el, i, targetsLength) * timeScale;\n          /** @type {CompositeOperation} */\n          tweenParams.composite = /** @type {CompositeOperation} */(setValue(tweenOptions.composition, composite));\n          /** @type {String} */\n          tweenParams.easing = parseWAAPIEasing(tweenOptionsEase);\n          parsedPropertyValue = parseIndividualTweenValue($el, name, from, to, i, targetsLength);\n          if (individualTransformProperty) {\n            keyframes[`--${individualTransformProperty}`] = parsedPropertyValue;\n            cachedTransforms[individualTransformProperty] = parsedPropertyValue;\n          } else {\n            keyframes[name] = parseIndividualTweenValue($el, name, from, to, i, targetsLength);\n          }\n          addWAAPIAnimation(this, $el, name, keyframes, tweenParams);\n          if (!isUnd(from)) {\n            if (!individualTransformProperty) {\n              $el.style[name] = keyframes[name][0];\n            } else {\n              const key = `--${individualTransformProperty}`;\n              $el.style.setProperty(key, keyframes[key][0]);\n            }\n          }\n        } else {\n          parsedPropertyValue = isArr(propertyValue) ?\n                                propertyValue.map((/** @type {any} */v) => normalizeTweenValue(name, v, $el, i, targetsLength)) :\n                                normalizeTweenValue(name, /** @type {any} */(propertyValue), $el, i, targetsLength);\n          if (individualTransformProperty) {\n            keyframes[`--${individualTransformProperty}`] = parsedPropertyValue;\n            cachedTransforms[individualTransformProperty] = parsedPropertyValue;\n          } else {\n            keyframes[name] = parsedPropertyValue;\n          }\n          addWAAPIAnimation(this, $el, name, keyframes, tweenParams);\n        }\n      }\n      if (hasIndividualTransforms) {\n        let transforms = emptyString;\n        for (let t in cachedTransforms) {\n          transforms += `${transformsFragmentStrings[t]}var(--${t})) `;\n        }\n        $el.style.transform = transforms;\n      }\n    });\n\n    if (scroll) {\n      /** @type {ScrollObserver} */(this.autoplay).link(this);\n    }\n  }\n\n  /**\n   * @callback forEachCallback\n   * @param {globalThis.Animation} animation\n   */\n\n  /**\n   * @param  {forEachCallback|String} callback\n   * @return {this}\n   */\n  forEach(callback) {\n    const cb = isStr(callback) ? a => a[callback]() : callback;\n    this.animations.forEach(cb);\n    return this;\n  }\n\n  get speed() {\n    return this._speed;\n  }\n\n  /** @param {Number} speed */\n  set speed(speed) {\n    this._speed = +speed;\n    this.forEach(anim => anim.playbackRate = speed);\n  }\n\n  get currentTime() {\n    const controlAnimation = this.controlAnimation;\n    const timeScale = globals.timeScale;\n    return this.completed ? this.duration : controlAnimation ? +controlAnimation.currentTime * (timeScale === 1 ? 1 : timeScale) : 0;\n  }\n\n  /** @param {Number} time */\n  set currentTime(time) {\n    const t = time * (globals.timeScale === 1 ? 1 : K);\n    this.forEach(anim => {\n      // Make sure the animation playState is not 'paused' in order to properly trigger an onfinish callback.\n      // The \"paused\" play state supersedes the \"finished\" play state; if the animation is both paused and finished, the \"paused\" state is the one that will be reported.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Animation/finish_event\n      if (t >= this.duration) anim.play();\n      anim.currentTime = t;\n    });\n  }\n\n  get progress() {\n    return this.currentTime / this.duration;\n  }\n\n  /** @param {Number} progress */\n  set progress(progress) {\n    this.forEach(anim => anim.currentTime = progress * this.duration || 0);\n  }\n\n  resume() {\n    if (!this.paused) return this;\n    this.paused = false;\n    // TODO: Store the current time, and seek back to the last position\n    return this.forEach('play');\n  }\n\n  pause() {\n    if (this.paused) return this;\n    this.paused = true;\n    return this.forEach('pause');\n  }\n\n  alternate() {\n    this.reversed = !this.reversed;\n    this.forEach('reverse');\n    if (this.paused) this.forEach('pause');\n    return this;\n  }\n\n  play() {\n    if (this.reversed) this.alternate();\n    return this.resume();\n  }\n\n  reverse() {\n    if (!this.reversed) this.alternate();\n    return this.resume();\n  }\n\n /**\n  * @param {Number} time\n  * @param {Boolean} muteCallbacks\n  */\n  seek(time, muteCallbacks = false) {\n    if (muteCallbacks) this.muteCallbacks = true;\n    if (time < this.duration) this.completed = false;\n    this.currentTime = time;\n    this.muteCallbacks = false;\n    if (this.paused) this.pause();\n    return this;\n  }\n\n  restart() {\n    this.completed = false;\n    return this.seek(0, true).resume();\n  }\n\n  commitStyles() {\n    return this.forEach('commitStyles');\n  }\n\n  complete() {\n    return this.seek(this.duration);\n  }\n\n  cancel() {\n    this.forEach('cancel');\n    return this.pause();\n  }\n\n  revert() {\n    this.cancel();\n    this.targets.forEach(($el, i) => $el.setAttribute('style', this._inlineStyles[i]) );\n    return this;\n  }\n\n  /**\n   * @param  {WAAPICallback} [callback]\n   * @return {Promise}\n   */\n  then(callback = noop) {\n    const then = this.then;\n    const onResolve = () => {\n      this.then = null;\n      callback(this);\n      this.then = then;\n      this._resolve = noop;\n    };\n    return new Promise(r => {\n      this._resolve = () => r(onResolve());\n      if (this.completed) this._resolve();\n      return this;\n    });\n  }\n}\n\nconst waapi = {\n/**\n * @param {DOMTargetsParam} targets\n * @param {WAAPIAnimationParams} params\n * @return {WAAPIAnimation}\n */\n  animate: (targets, params) => new WAAPIAnimation(targets, params),\n  convertEase: easingToLinear\n};\n\n\n\n\n/**\n * @param  {Callback<Timer>} [callback]\n * @return {Timer}\n */\nconst sync = (callback = noop) => {\n  return new Timer({ duration: 1 * globals.timeScale, onComplete: callback }, null, 0).resume();\n};\n\n/**\n * @overload\n * @param  {DOMTargetSelector} targetSelector\n * @param  {String}            propName\n * @return {String}\n *\n * @overload\n * @param  {JSTargetsParam} targetSelector\n * @param  {String}         propName\n * @return {Number|String}\n *\n * @overload\n * @param  {DOMTargetsParam} targetSelector\n * @param  {String}          propName\n * @param  {String}          unit\n * @return {String}\n *\n * @overload\n * @param  {TargetsParam} targetSelector\n * @param  {String}       propName\n * @param  {Boolean}      unit\n * @return {Number}\n *\n * @param  {TargetsParam}   targetSelector\n * @param  {String}         propName\n * @param  {String|Boolean} [unit]\n */\nfunction getTargetValue(targetSelector, propName, unit) {\n  const targets = registerTargets(targetSelector);\n  if (!targets.length) return;\n  const [ target ] = targets;\n  const tweenType = getTweenType(target, propName);\n  const normalizePropName = sanitizePropertyName(propName, target, tweenType);\n  let originalValue = getOriginalAnimatableValue(target, normalizePropName);\n  if (isUnd(unit)) {\n    return originalValue;\n  } else {\n    decomposeRawValue(originalValue, decomposedOriginalValue);\n    if (decomposedOriginalValue.t === valueTypes.NUMBER || decomposedOriginalValue.t === valueTypes.UNIT) {\n      if (unit === false) {\n        return decomposedOriginalValue.n;\n      } else {\n        const convertedValue = convertValueUnit(/** @type {DOMTarget} */(target), decomposedOriginalValue, /** @type {String} */(unit), false);\n        return `${round(convertedValue.n, globals.precision)}${convertedValue.u}`;\n      }\n    }\n  }\n}\n\n/**\n * @param  {TargetsParam}    targets\n * @param  {AnimationParams} parameters\n * @return {JSAnimation}\n */\nconst setTargetValues = (targets, parameters) => {\n  if (isUnd(parameters)) return;\n  parameters.duration = minValue;\n  // Do not overrides currently active tweens by default\n  parameters.composition = setValue(parameters.composition, compositionTypes.none);\n  // Skip init() and force rendering by playing the animation\n  return new JSAnimation(targets, parameters, null, 0, true).resume();\n};\n\n/**\n * @param  {TargetsArray} targetsArray\n * @param  {JSAnimation}    animation\n * @param  {String}       [propertyName]\n * @return {Boolean}\n */\nconst removeTargetsFromAnimation = (targetsArray, animation, propertyName) => {\n  let tweensMatchesTargets = false;\n  forEachChildren(animation, (/**@type {Tween} */tween) => {\n    const tweenTarget = tween.target;\n    if (targetsArray.includes(tweenTarget)) {\n      const tweenName = tween.property;\n      const tweenType = tween._tweenType;\n      const normalizePropName = sanitizePropertyName(propertyName, tweenTarget, tweenType);\n      if (!normalizePropName || normalizePropName && normalizePropName === tweenName) {\n        // Make sure to flag the previous CSS transform tween to renderTransform\n        if (tween.parent._tail === tween &&\n            tween._tweenType === tweenTypes.TRANSFORM &&\n            tween._prev &&\n            tween._prev._tweenType === tweenTypes.TRANSFORM\n        ) {\n          tween._prev._renderTransforms = 1;\n        }\n        // Removes the tween from the selected animation\n        removeChild(animation, tween);\n        // Detach the tween from its siblings to make sure blended tweens are correctlly removed\n        removeTweenSliblings(tween);\n        tweensMatchesTargets = true;\n      }\n    }\n  }, true);\n  return tweensMatchesTargets;\n};\n\n/**\n * @param  {TargetsParam} targets\n * @param  {Renderable|WAAPIAnimation} [renderable]\n * @param  {String}                    [propertyName]\n * @return {TargetsArray}\n */\nconst remove = (targets, renderable, propertyName) => {\n  const targetsArray = parseTargets(targets);\n  const parent = /** @type {Renderable|typeof engine} **/(renderable ? renderable : engine);\n  const waapiAnimation = renderable && /** @type {WAAPIAnimation} */(renderable).controlAnimation && /** @type {WAAPIAnimation} */(renderable);\n  for (let i = 0, l = targetsArray.length; i < l; i++) {\n    const $el = /** @type {DOMTarget}  */(targetsArray[i]);\n    removeWAAPIAnimation($el, propertyName, waapiAnimation);\n  }\n  let removeMatches;\n  if (parent._hasChildren) {\n    let iterationDuration = 0;\n    forEachChildren(parent, (/** @type {Renderable} */child) => {\n      if (!child._hasChildren) {\n        removeMatches = removeTargetsFromAnimation(targetsArray, /** @type {JSAnimation} */(child), propertyName);\n        // Remove the child from its parent if no tweens and no children left after the removal\n        if (removeMatches && !child._head) {\n          child.cancel();\n          removeChild(parent, child);\n        } else {\n          // Calculate the new iterationDuration value to handle onComplete with last child in render()\n          const childTLOffset = child._offset + child._delay;\n          const childDur = childTLOffset + child.duration;\n          if (childDur > iterationDuration) {\n            iterationDuration = childDur;\n          }\n        }\n      }\n      // Make sure to also remove engine's children targets\n      // NOTE: Avoid recursion?\n      if (child._head) {\n        remove(targets, child, propertyName);\n      } else {\n        child._hasChildren = false;\n      }\n    }, true);\n    // Update iterationDuration value to handle onComplete with last child in render()\n    if (!isUnd(/** @type {Renderable} */(parent).iterationDuration)) {\n      /** @type {Renderable} */(parent).iterationDuration = iterationDuration;\n    }\n  } else {\n    removeMatches = removeTargetsFromAnimation(\n      targetsArray,\n      /** @type {JSAnimation} */(parent),\n      propertyName\n    );\n  }\n\n  if (removeMatches && !parent._head) {\n    parent._hasChildren = false;\n    // Cancel the parent if there are no tweens and no children left after the removal\n    // We have to check if the .cancel() method exist to handle cases where the parent is the engine itself\n    if (/** @type {Renderable} */(parent).cancel) /** @type {Renderable} */(parent).cancel();\n  }\n\n  return targetsArray;\n};\n\n/**\n * @param  {Number} min\n * @param  {Number} max\n * @param  {Number} [decimalLength]\n * @return {Number}\n */\nconst random = (min, max, decimalLength) => { const m = 10 ** (decimalLength || 0); return floor((Math.random() * (max - min + (1 / m)) + min) * m) / m };\n\n/**\n * @param  {String|Array} items\n * @return {any}\n */\nconst randomPick = items => items[random(0, items.length - 1)];\n\n/**\n * Adapted from https://bost.ocks.org/mike/shuffle/\n * @param  {Array} items\n * @return {Array}\n */\nconst shuffle = items => {\n  let m = items.length, t, i;\n  while (m) { i = random(0, --m); t = items[m]; items[m] = items[i]; items[i] = t; }\n  return items;\n};\n\n/**\n * @param  {Number|String} v\n * @param  {Number} decimalLength\n * @return {String}\n */\nconst roundPad = (v, decimalLength) => (+v).toFixed(decimalLength);\n\n/**\n * @param  {Number} v\n * @param  {Number} totalLength\n * @param  {String} padString\n * @return {String}\n */\nconst padStart = (v, totalLength, padString) => `${v}`.padStart(totalLength, padString);\n\n/**\n * @param  {Number} v\n * @param  {Number} totalLength\n * @param  {String} padString\n * @return {String}\n */\nconst padEnd = (v, totalLength, padString) => `${v}`.padEnd(totalLength, padString);\n\n/**\n * @param  {Number} v\n * @param  {Number} min\n * @param  {Number} max\n * @return {Number}\n */\nconst wrap = (v, min, max) => (((v - min) % (max - min) + (max - min)) % (max - min)) + min;\n\n/**\n * @param  {Number} value\n * @param  {Number} inLow\n * @param  {Number} inHigh\n * @param  {Number} outLow\n * @param  {Number} outHigh\n * @return {Number}\n */\nconst mapRange = (value, inLow, inHigh, outLow, outHigh) => outLow + ((value - inLow) / (inHigh - inLow)) * (outHigh - outLow);\n\n/**\n * @param  {Number} degrees\n * @return {Number}\n */\nconst degToRad = degrees => degrees * PI / 180;\n\n/**\n * @param  {Number} radians\n * @return {Number}\n */\nconst radToDeg = radians => radians * 180 / PI;\n\n/**\n * https://www.rorydriscoll.com/2016/03/07/frame-rate-independent-damping-using-lerp/\n * @param  {Number} start\n * @param  {Number} end\n * @param  {Number} amount\n * @param  {Renderable|Boolean} [renderable]\n * @return {Number}\n */\nconst lerp = (start, end, amount, renderable) => {\n  let dt = K / globals.defaults.frameRate;\n  if (renderable !== false) {\n    const ticker = /** @type Renderable */\n                   (renderable) ||\n                   (engine._hasChildren && engine);\n    if (ticker && ticker.deltaTime) {\n      dt = ticker.deltaTime;\n    }\n  }\n  const t = 1 - Math.exp(-amount * dt * .1);\n  return !amount ? start : amount === 1 ? end : (1 - t) * start + t * end;\n};\n\n// Chain-able utilities\n\n/**\n * @callback UtilityFunction\n * @param {...*} args\n * @return {Number|String}\n *\n * @param {UtilityFunction} fn\n * @param {Number} [last=0]\n * @return {function(...(Number|String)): function(Number|String): (Number|String)}\n */\nconst curry = (fn, last = 0) => (...args) => last ? v => fn(...args, v) : v => fn(v, ...args);\n\n/**\n * @param {Function} fn\n * @return {function(...(Number|String))}\n */\nconst chain = fn => {\n   return (...args) => {\n    const result = fn(...args);\n    return new Proxy(noop, {\n      apply: (_, __, [v]) => result(v),\n      get: (_, prop) => chain(/**@param {...Number|String} nextArgs */(...nextArgs) => {\n        const nextResult = utils[prop](...nextArgs);\n        return (/**@type {Number|String} */v) => nextResult(result(v));\n      })\n    });\n  }\n};\n\n/**\n * @param {UtilityFunction} fn\n * @param {Number} [right]\n * @return {function(...(Number|String)): UtilityFunction}\n */\nconst makeChainable = (fn, right = 0) => (...args) => (args.length < fn.length ? chain(curry(fn, right)) : fn)(...args);\n\n/**\n * @callback ChainedUtilsResult\n * @param {Number} value\n * @return {Number}\n *\n * @typedef {Object} ChainableUtils\n * @property {ChainedClamp} clamp\n * @property {ChainedRound} round\n * @property {ChainedSnap} snap\n * @property {ChainedWrap} wrap\n * @property {ChainedInterpolate} interpolate\n * @property {ChainedMapRange} mapRange\n * @property {ChainedRoundPad} roundPad\n * @property {ChainedPadStart} padStart\n * @property {ChainedPadEnd} padEnd\n * @property {ChainedDegToRad} degToRad\n * @property {ChainedRadToDeg} radToDeg\n *\n * @typedef {ChainableUtils & ChainedUtilsResult} ChainableUtil\n *\n * @callback ChainedClamp\n * @param {Number} min\n * @param {Number} max\n * @return {ChainableUtil}\n *\n * @callback ChainedRound\n * @param {Number} decimalLength\n * @return {ChainableUtil}\n *\n * @callback ChainedSnap\n * @param {Number} increment\n * @return {ChainableUtil}\n *\n * @callback ChainedWrap\n * @param {Number} min\n * @param {Number} max\n * @return {ChainableUtil}\n *\n * @callback ChainedInterpolate\n * @param {Number} start\n * @param {Number} end\n * @return {ChainableUtil}\n *\n * @callback ChainedMapRange\n * @param {Number} inLow\n * @param {Number} inHigh\n * @param {Number} outLow\n * @param {Number} outHigh\n * @return {ChainableUtil}\n *\n * @callback ChainedRoundPad\n * @param {Number} decimalLength\n * @return {ChainableUtil}\n *\n * @callback ChainedPadStart\n * @param {Number} totalLength\n * @param {String} padString\n * @return {ChainableUtil}\n *\n * @callback ChainedPadEnd\n * @param {Number} totalLength\n * @param {String} padString\n * @return {ChainableUtil}\n *\n * @callback ChainedDegToRad\n * @return {ChainableUtil}\n *\n * @callback ChainedRadToDeg\n * @return {ChainableUtil}\n */\n\nconst utils = {\n  $: registerTargets,\n  get: getTargetValue,\n  set: setTargetValues,\n  remove,\n  cleanInlineStyles,\n  random,\n  randomPick,\n  shuffle,\n  lerp,\n  sync,\n  clamp: /** @type {typeof clamp & ChainedClamp} */(makeChainable(clamp)),\n  round: /** @type {typeof round & ChainedRound} */(makeChainable(round)),\n  snap: /** @type {typeof snap & ChainedSnap} */(makeChainable(snap)),\n  wrap: /** @type {typeof wrap & ChainedWrap} */(makeChainable(wrap)),\n  interpolate: /** @type {typeof interpolate & ChainedInterpolate} */(makeChainable(interpolate, 1)),\n  mapRange: /** @type {typeof mapRange & ChainedMapRange} */(makeChainable(mapRange)),\n  roundPad: /** @type {typeof roundPad & ChainedRoundPad} */(makeChainable(roundPad)),\n  padStart: /** @type {typeof padStart & ChainedPadStart} */(makeChainable(padStart)),\n  padEnd: /** @type {typeof padEnd & ChainedPadEnd} */(makeChainable(padEnd)),\n  degToRad: /** @type {typeof degToRad & ChainedDegToRad} */(makeChainable(degToRad)),\n  radToDeg: /** @type {typeof radToDeg & ChainedRadToDeg} */(makeChainable(radToDeg)),\n};\n\n\n\n\n/**\n * @typedef {Number|String|Function} TimePosition\n */\n\n/**\n * Timeline's children offsets positions parser\n * @param  {Timeline} timeline\n * @param  {String} timePosition\n * @return {Number}\n */\nconst getPrevChildOffset = (timeline, timePosition) => {\n  if (stringStartsWith(timePosition, '<')) {\n    const goToPrevAnimationOffset = timePosition[1] === '<';\n    const prevAnimation = /** @type {Tickable} */(timeline._tail);\n    const prevOffset = prevAnimation ? prevAnimation._offset + prevAnimation._delay : 0;\n    return goToPrevAnimationOffset ? prevOffset : prevOffset + prevAnimation.duration;\n  }\n};\n\n/**\n * @param  {Timeline} timeline\n * @param  {TimePosition} [timePosition]\n * @return {Number}\n */\nconst parseTimelinePosition = (timeline, timePosition) => {\n  let tlDuration = timeline.iterationDuration;\n  if (tlDuration === minValue) tlDuration = 0;\n  if (isUnd(timePosition)) return tlDuration;\n  if (isNum(+timePosition)) return +timePosition;\n  const timePosStr = /** @type {String} */(timePosition);\n  const tlLabels = timeline ? timeline.labels : null;\n  const hasLabels = !isNil(tlLabels);\n  const prevOffset = getPrevChildOffset(timeline, timePosStr);\n  const hasSibling = !isUnd(prevOffset);\n  const matchedRelativeOperator = relativeValuesExecRgx.exec(timePosStr);\n  if (matchedRelativeOperator) {\n    const fullOperator = matchedRelativeOperator[0];\n    const split = timePosStr.split(fullOperator);\n    const labelOffset = hasLabels && split[0] ? tlLabels[split[0]] : tlDuration;\n    const parsedOffset = hasSibling ? prevOffset : hasLabels ? labelOffset : tlDuration;\n    const parsedNumericalOffset = +split[1];\n    return getRelativeValue(parsedOffset, parsedNumericalOffset, fullOperator[0]);\n  } else {\n    return hasSibling ? prevOffset :\n           hasLabels ? !isUnd(tlLabels[timePosStr]) ? tlLabels[timePosStr] :\n           tlDuration : tlDuration;\n  }\n};\n\n/**\n * @param {Timeline} tl\n * @return {Number}\n */\nfunction getTimelineTotalDuration(tl) {\n  return clampInfinity(((tl.iterationDuration + tl._loopDelay) * tl.iterationCount) - tl._loopDelay) || minValue;\n}\n\n/**\n * @overload\n * @param  {TimerParams} childParams\n * @param  {Timeline} tl\n * @param  {Number} timePosition\n * @return {Timeline}\n *\n * @overload\n * @param  {AnimationParams} childParams\n * @param  {Timeline} tl\n * @param  {Number} timePosition\n * @param  {TargetsParam} targets\n * @param  {Number} [index]\n * @param  {Number} [length]\n * @return {Timeline}\n *\n * @param  {TimerParams|AnimationParams} childParams\n * @param  {Timeline} tl\n * @param  {Number} timePosition\n * @param  {TargetsParam} [targets]\n * @param  {Number} [index]\n * @param  {Number} [length]\n */\nfunction addTlChild(childParams, tl, timePosition, targets, index, length) {\n  const isSetter = isNum(childParams.duration) && /** @type {Number} */(childParams.duration) <= minValue;\n  // Offset the tl position with -minValue for 0 duration animations or .set() calls in order to align their end value with the defined position\n  const adjustedPosition = isSetter ? timePosition - minValue : timePosition;\n  tick(tl, adjustedPosition, 1, 1, tickModes.AUTO);\n  const tlChild = targets ?\n    new JSAnimation(targets,/** @type {AnimationParams} */(childParams), tl, adjustedPosition, false, index, length) :\n    new Timer(/** @type {TimerParams} */(childParams), tl, adjustedPosition);\n  tlChild.init(1);\n  // TODO: Might be better to insert at a position relative to startTime?\n  addChild(tl, tlChild);\n  forEachChildren(tl, (/** @type {Renderable} */child) => {\n    const childTLOffset = child._offset + child._delay;\n    const childDur = childTLOffset + child.duration;\n    if (childDur > tl.iterationDuration) tl.iterationDuration = childDur;\n  });\n  tl.duration = getTimelineTotalDuration(tl);\n  return tl;\n}\n\nclass Timeline extends Timer {\n\n  /**\n   * @param {TimelineParams} [parameters]\n   */\n  constructor(parameters = {}) {\n    super(/** @type {TimerParams&TimelineParams} */(parameters), null, 0);\n    /** @type {Number} */\n    this.duration = 0; // TL duration starts at 0 and grows when adding children\n    /** @type {Record<String, Number>} */\n    this.labels = {};\n    const defaultsParams = parameters.defaults;\n    const globalDefaults = globals.defaults;\n    /** @type {DefaultsParams} */\n    this.defaults = defaultsParams ? mergeObjects(defaultsParams, globalDefaults) : globalDefaults;\n    /** @type {Callback<this>} */\n    this.onRender = parameters.onRender || globalDefaults.onRender;\n    const tlPlaybackEase = setValue(parameters.playbackEase, globalDefaults.playbackEase);\n    this._ease = tlPlaybackEase ? parseEasings(tlPlaybackEase) : null;\n    /** @type {Number} */\n    this.iterationDuration = 0;\n  }\n\n  /**\n   * @overload\n   * @param {TargetsParam} a1\n   * @param {AnimationParams} a2\n   * @param {TimePosition} [a3]\n   * @return {this}\n   *\n   * @overload\n   * @param {TimerParams} a1\n   * @param {TimePosition} [a2]\n   * @return {this}\n   *\n   * @param {TargetsParam|TimerParams} a1\n   * @param {AnimationParams|TimePosition} a2\n   * @param {TimePosition} [a3]\n   */\n  add(a1, a2, a3) {\n    const isAnim = isObj(a2);\n    const isTimer = isObj(a1);\n    if (isAnim || isTimer) {\n      this._hasChildren = true;\n      if (isAnim) {\n        const childParams = /** @type {AnimationParams} */(a2);\n        // Check for function for children stagger positions\n        if (isFnc(a3)) {\n          const staggeredPosition = /** @type {Function} */(a3);\n          const parsedTargetsArray = parseTargets(/** @type {TargetsParam} */(a1));\n          // Store initial duration before adding new children that will change the duration\n          const tlDuration = this.duration;\n          // Store initial _iterationDuration before adding new children that will change the duration\n          const tlIterationDuration = this.iterationDuration;\n          // Store the original id in order to add specific indexes to the new animations ids\n          const id = childParams.id;\n          let i = 0;\n          const parsedLength = parsedTargetsArray.length;\n          parsedTargetsArray.forEach((/** @type {Target} */target) => {\n            // Create a new parameter object for each staggered children\n            const staggeredChildParams = { ...childParams };\n            // Reset the duration of the timeline iteration before each stagger to prevent wrong start value calculation\n            this.duration = tlDuration;\n            this.iterationDuration = tlIterationDuration;\n            if (!isUnd(id)) staggeredChildParams.id = id + '-' + i;\n            addTlChild(\n              staggeredChildParams,\n              this,\n              staggeredPosition(target, i, parsedLength, this),\n              target,\n              i,\n              parsedLength\n            );\n            i++;\n          });\n        } else {\n          addTlChild(\n            childParams,\n            this,\n            parseTimelinePosition(this, a3),\n            /** @type {TargetsParam} */(a1),\n          );\n        }\n      } else {\n        // It's a Timer\n        addTlChild(\n          /** @type TimerParams */(a1),\n          this,\n          parseTimelinePosition(this,/** @type TimePosition */(a2)),\n        );\n      }\n      return this.init(1); // 1 = internalRender\n    }\n  }\n\n  /**\n   * @overload\n   * @param {Tickable} [synced]\n   * @param {TimePosition} [position]\n   * @return {this}\n   *\n   * @overload\n   * @param {globalThis.Animation} [synced]\n   * @param {TimePosition} [position]\n   * @return {this}\n   *\n   * @overload\n   * @param {WAAPIAnimation} [synced]\n   * @param {TimePosition} [position]\n   * @return {this}\n   *\n   * @param {Tickable|WAAPIAnimation|globalThis.Animation} [synced]\n   * @param {TimePosition} [position]\n   */\n  sync(synced, position) {\n    if (isUnd(synced) || synced && isUnd(synced.pause)) return this;\n    synced.pause();\n    const duration = +(/** @type {globalThis.Animation} */(synced).effect ? /** @type {globalThis.Animation} */(synced).effect.getTiming().duration : /** @type {Tickable} */(synced).duration);\n    return this.add(synced, { currentTime: [0, duration], duration, ease: 'linear' }, position);\n  }\n\n  /**\n   * @param  {TargetsParam} targets\n   * @param  {AnimationParams} parameters\n   * @param  {TimePosition} [position]\n   * @return {this}\n   */\n  set(targets, parameters, position) {\n    if (isUnd(parameters)) return this;\n    parameters.duration = minValue;\n    parameters.composition = compositionTypes.replace;\n    return this.add(targets, parameters, position);\n  }\n\n  /**\n   * @param {Callback<Timer>} callback\n   * @param {TimePosition} [position]\n   * @return {this}\n   */\n  call(callback, position) {\n    if (isUnd(callback) || callback && !isFnc(callback)) return this;\n    return this.add({ duration: 0, onComplete: () => callback(this) }, position);\n  }\n\n  /**\n   * @param {String} labelName\n   * @param {TimePosition} [position]\n   * @return {this}\n   *\n   */\n  label(labelName, position) {\n    if (isUnd(labelName) || labelName && !isStr(labelName)) return this;\n    this.labels[labelName] = parseTimelinePosition(this,/** @type TimePosition */(position));\n    return this;\n  }\n\n  /**\n   * @param  {TargetsParam} targets\n   * @param  {String} [propertyName]\n   * @return {this}\n   */\n  remove(targets, propertyName) {\n    remove(targets, this, propertyName);\n    return this;\n  }\n\n  /**\n   * @param  {Number} newDuration\n   * @return {this}\n   */\n  stretch(newDuration) {\n    const currentDuration = this.duration;\n    if (currentDuration === normalizeTime(newDuration)) return this;\n    const timeScale = newDuration / currentDuration;\n    const labels = this.labels;\n    forEachChildren(this, (/** @type {JSAnimation} */child) => child.stretch(child.duration * timeScale));\n    for (let labelName in labels) labels[labelName] *= timeScale;\n    return super.stretch(newDuration);\n  }\n\n  /**\n   * @return {this}\n   */\n  refresh() {\n    forEachChildren(this, (/** @type {JSAnimation} */child) => {\n      if (child.refresh) child.refresh();\n    });\n    return this;\n  }\n\n  /**\n   * @return {this}\n   */\n  revert() {\n    super.revert();\n    forEachChildren(this, (/** @type {JSAnimation} */child) => child.revert, true);\n    return cleanInlineStyles(this);\n  }\n\n  /**\n   * @param  {Callback<this>} [callback]\n   * @return {Promise}\n   */\n  then(callback) {\n    return super.then(callback);\n  }\n}\n\n/**\n * @param {TimelineParams} [parameters]\n * @return {Timeline}\n */\nconst createTimeline = parameters => new Timeline(parameters).init();\n\n\n\n\nclass Animatable {\n  /**\n   * @param {TargetsParam} targets\n   * @param {AnimatableParams} parameters\n   */\n  constructor(targets, parameters) {\n    if (globals.scope) globals.scope.revertibles.push(this);\n    /** @type {AnimationParams} */\n    const globalParams = {};\n    const properties = {};\n    this.targets = [];\n    this.animations = {};\n    if (isUnd(targets) || isUnd(parameters)) return;\n    for (let propName in parameters) {\n      const paramValue = parameters[propName];\n      if (isKey(propName)) {\n        properties[propName] = paramValue;\n      } else {\n        globalParams[propName] = paramValue;\n      }\n    }\n    for (let propName in properties) {\n      const propValue = properties[propName];\n      const isObjValue = isObj(propValue);\n      /** @type {TweenParamsOptions} */\n      let propParams = {};\n      let to = '+=0';\n      if (isObjValue) {\n        const unit = propValue.unit;\n        if (isStr(unit)) to += unit;\n      } else {\n        propParams.duration = propValue;\n      }\n      propParams[propName] = isObjValue ? mergeObjects({ to }, propValue) : to;\n      const animParams = mergeObjects(globalParams, propParams);\n      animParams.composition = compositionTypes.replace;\n      animParams.autoplay = false;\n      const animation = this.animations[propName] = new JSAnimation(targets, animParams, null, 0, false).init();\n      if (!this.targets.length) this.targets.push(...animation.targets);\n      /** @type {AnimatableProperty} */\n      this[propName] = (to, duration, ease) => {\n        const tween = /** @type {Tween} */(animation._head);\n        if (isUnd(to) && tween) {\n          const numbers = tween._numbers;\n          if (numbers && numbers.length) {\n            return numbers;\n          } else {\n            return tween._modifier(tween._number);\n          }\n        } else {\n          forEachChildren(animation, (/** @type {Tween} */tween) => {\n            if (isArr(to)) {\n              for (let i = 0, l = /** @type {Array} */(to).length; i < l; i++) {\n                if (!isUnd(tween._numbers[i])) {\n                  tween._fromNumbers[i] = /** @type {Number} */(tween._modifier(tween._numbers[i]));\n                  tween._toNumbers[i] = to[i];\n                }\n              }\n            } else {\n              tween._fromNumber = /** @type {Number} */(tween._modifier(tween._number));\n              tween._toNumber = /** @type {Number} */(to);\n            }\n            if (!isUnd(ease)) tween._ease = parseEasings(ease);\n            tween._currentTime = 0;\n          });\n          if (!isUnd(duration)) animation.stretch(duration);\n          animation.reset(1).resume();\n          return this;\n        }\n      };\n    }\n  }\n\n  revert() {\n    for (let propName in this.animations) {\n      this[propName] = noop;\n      this.animations[propName].revert();\n    }\n    this.animations = {};\n    this.targets.length = 0;\n    return this;\n  }\n}\n\n/**\n * @param {TargetsParam} targets\n * @param {AnimatableParams} parameters\n * @return {AnimatableObject}\n */\nconst createAnimatable = (targets, parameters) => /** @type {AnimatableObject} */(new Animatable(targets, parameters));\n\n\n\n\n/*\n * Spring ease solver adapted from https://webkit.org/demos/spring/spring.js\n * Webkit Copyright © 2016 Apple Inc\n */\n\n/**\n * @typedef {Object} SpringParams\n * @property {Number} [mass=1] - Mass, default 1\n * @property {Number} [stiffness=100] - Stiffness, default 100\n * @property {Number} [damping=10] - Damping, default 10\n * @property {Number} [velocity=0] - Initial velocity, default 0\n */\n\nclass Spring {\n  /**\n   * @param {SpringParams} [parameters]\n   */\n  constructor(parameters = {}) {\n    this.timeStep = .02; // Interval fed to the solver to calculate duration\n    this.restThreshold = .0005; // Values below this threshold are considered resting position\n    this.restDuration = 200; // Duration in ms used to check if the spring is resting after reaching restThreshold\n    this.maxDuration = 60000; // The maximum allowed spring duration in ms (default 1 min)\n    this.maxRestSteps = this.restDuration / this.timeStep / K; // How many steps allowed after reaching restThreshold before stopping the duration calculation\n    this.maxIterations = this.maxDuration / this.timeStep / K; // Calculate the maximum iterations allowed based on maxDuration\n    this.m = clamp(setValue(parameters.mass, 1), 0, K);\n    this.s = clamp(setValue(parameters.stiffness, 100), 1, K);\n    this.d = clamp(setValue(parameters.damping, 10), .1, K);\n    this.v = clamp(setValue(parameters.velocity, 0), -1e3, K);\n    this.w0 = 0;\n    this.zeta = 0;\n    this.wd = 0;\n    this.b = 0;\n    this.solverDuration = 0;\n    this.duration = 0;\n    this.compute();\n    /** @type {EasingFunction} */\n    this.ease = t => t === 0 || t === 1 ? t : this.solve(t * this.solverDuration);\n  }\n\n  /** @type {EasingFunction} */\n  solve(time) {\n    const { zeta, w0, wd, b } = this;\n    let t = time;\n    if (zeta < 1) {\n      t = exp(-t * zeta * w0) * (1 * cos(wd * t) + b * sin(wd * t));\n    } else {\n      t = (1 + b * t) * exp(-t * w0);\n    }\n    return 1 - t;\n  }\n\n  compute() {\n    const { maxRestSteps, maxIterations, restThreshold, timeStep, m, d, s, v } = this;\n    const w0 = this.w0 = clamp(sqrt(s / m), minValue, K);\n    const zeta = this.zeta = d / (2 * sqrt(s * m));\n    const wd = this.wd = zeta < 1 ? w0 * sqrt(1 - zeta * zeta) : 0;\n    this.b = zeta < 1 ? (zeta * w0 + -v) / wd : -v + w0;\n    let solverTime = 0;\n    let restSteps = 0;\n    let iterations = 0;\n    while (restSteps < maxRestSteps && iterations < maxIterations) {\n      if (abs(1 - this.solve(solverTime)) < restThreshold) {\n        restSteps++;\n      } else {\n        restSteps = 0;\n      }\n      this.solverDuration = solverTime;\n      solverTime += timeStep;\n      iterations++;\n    }\n    this.duration = round(this.solverDuration * K, 0) * globals.timeScale;\n  }\n\n  get mass() {\n    return this.m;\n  }\n\n  set mass(v) {\n    this.m = clamp(setValue(v, 1), 0, K);\n    this.compute();\n  }\n\n  get stiffness() {\n    return this.s;\n  }\n\n  set stiffness(v) {\n    this.s = clamp(setValue(v, 100), 1, K);\n    this.compute();\n  }\n\n  get damping() {\n    return this.d;\n  }\n\n  set damping(v) {\n    this.d = clamp(setValue(v, 10), .1, K);\n    this.compute();\n  }\n\n  get velocity() {\n    return this.v;\n  }\n\n  set velocity(v) {\n    this.v = clamp(setValue(v, 0), -1e3, K);\n    this.compute();\n  }\n}\n\n/**\n * @param {SpringParams} [parameters]\n * @returns {Spring}\n */\nconst createSpring = (parameters) => new Spring(parameters);\n\n\n\n\n/**\n * @param {Event} e\n */\nconst preventDefault = e => {\n  if (e.cancelable) e.preventDefault();\n};\n\nclass DOMProxy {\n  /** @param {Object} el */\n  constructor(el) {\n    this.el = el;\n    this.zIndex = 0;\n    this.parentElement = null;\n    this.classList = {\n      add: noop,\n      remove: noop,\n    };\n  }\n\n  get x() { return this.el.x || 0 };\n  set x(v) { this.el.x = v; };\n\n  get y() { return this.el.y || 0 };\n  set y(v) { this.el.y = v; };\n\n  get width() { return this.el.width || 0 };\n  set width(v) { this.el.width = v; };\n\n  get height() { return this.el.height || 0 };\n  set height(v) { this.el.height = v; };\n\n  getBoundingClientRect() {\n    return {\n      top: this.y,\n      right: this.x,\n      bottom: this.y + this.height,\n      left: this.x + this.width,\n    }\n  }\n}\n\nclass Transforms {\n  /**\n   * @param {DOMTarget|DOMProxy} $el\n   */\n  constructor($el) {\n    this.$el = $el;\n    this.inlineTransforms = [];\n    this.point = new DOMPoint();\n    this.inversedMatrix = this.getMatrix().inverse();\n  }\n\n  /**\n   * @param {Number} x\n   * @param {Number} y\n   * @return {DOMPoint}\n   */\n  normalizePoint(x, y) {\n    this.point.x = x;\n    this.point.y = y;\n    return this.point.matrixTransform(this.inversedMatrix);\n  }\n\n  /**\n   * @callback TraverseParentsCallback\n   * @param {DOMTarget} $el\n   * @param {Number} i\n   */\n\n  /**\n   * @param {TraverseParentsCallback} cb\n   */\n  traverseUp(cb) {\n    let $el = /** @type {DOMTarget|Document} */(this.$el.parentElement), i = 0;\n    while ($el && $el !== doc) {\n      cb(/** @type {DOMTarget} */($el), i);\n      $el = /** @type {DOMTarget} */($el.parentElement);\n      i++;\n    }\n  }\n\n  getMatrix() {\n    const matrix = new DOMMatrix();\n    this.traverseUp($el => {\n      const transformValue = getComputedStyle($el).transform;\n      if (transformValue) {\n        const elMatrix = new DOMMatrix(transformValue);\n        matrix.preMultiplySelf(elMatrix);\n      }\n    });\n    return matrix;\n  }\n\n  remove() {\n    this.traverseUp(($el, i) => {\n      this.inlineTransforms[i] = $el.style.transform;\n      $el.style.transform = 'none';\n    });\n  }\n\n  revert() {\n    this.traverseUp(($el, i) => {\n      const ct = this.inlineTransforms[i];\n      if (ct === '') {\n        $el.style.removeProperty('transform');\n      } else {\n        $el.style.transform = ct;\n      }\n    });\n  }\n}\n\n/**\n * @template {Array<Number>|DOMTargetSelector|String|Number|Boolean|Function|DraggableCursorParams} T\n * @param {T | ((draggable: Draggable) => T)} value\n * @param {Draggable} draggable\n * @return {T}\n */\nconst parseDraggableFunctionParameter = (value, draggable) => value && isFnc(value) ? /** @type {Function} */(value)(draggable) : value;\n\nlet zIndex = 0;\n\nclass Draggable {\n  /**\n   * @param {TargetsParam} target\n   * @param {DraggableParams} [parameters]\n   */\n  constructor(target, parameters = {}) {\n    if (!target) return;\n    if (globals.scope) globals.scope.revertibles.push(this);\n    const paramX = parameters.x;\n    const paramY = parameters.y;\n    const trigger = parameters.trigger;\n    const modifier = parameters.modifier;\n    const ease = parameters.releaseEase;\n    const customEase = ease && parseEasings(ease);\n    const hasSpring = !isUnd(ease) && !isUnd(/** @type {Spring} */(ease).ease);\n    const xProp = /** @type {String} */(isObj(paramX) && !isUnd(/** @type {Object} */(paramX).mapTo) ? /** @type {Object} */(paramX).mapTo : 'translateX');\n    const yProp = /** @type {String} */(isObj(paramY) && !isUnd(/** @type {Object} */(paramY).mapTo) ? /** @type {Object} */(paramY).mapTo : 'translateY');\n    const container = parseDraggableFunctionParameter(parameters.container, this);\n    this.containerArray = isArr(container) ? container : null;\n    this.$container = /** @type {HTMLElement} */(container && !this.containerArray ? parseTargets(/** @type {DOMTarget} */(container))[0] : doc.body);\n    this.useWin = this.$container === doc.body;\n    /** @type {Window | HTMLElement} */\n    this.$scrollContainer = this.useWin ? win : this.$container;\n    this.$target = /** @type {HTMLElement} */(isObj(target) ? new DOMProxy(target) : parseTargets(target)[0]);\n    this.$trigger = /** @type {HTMLElement} */(parseTargets(trigger ? trigger : target)[0]);\n    this.fixed = getTargetValue(this.$target, 'position') === 'fixed';\n    // Refreshable parameters\n    this.isFinePointer = true;\n    /** @type {[Number, Number, Number, Number]} */\n    this.containerPadding = [0, 0, 0, 0];\n    /** @type {Number} */\n    this.containerFriction = 0;\n    /** @type {Number} */\n    this.releaseContainerFriction = 0;\n    /** @type {Number|Array<Number>} */\n    this.snapX = 0;\n    /** @type {Number|Array<Number>} */\n    this.snapY = 0;\n    /** @type {Number} */\n    this.scrollSpeed = 0;\n    /** @type {Number} */\n    this.scrollThreshold = 0;\n    /** @type {Number} */\n    this.dragSpeed = 0;\n    /** @type {Number} */\n    this.maxVelocity = 0;\n    /** @type {Number} */\n    this.minVelocity = 0;\n    /** @type {Number} */\n    this.velocityMultiplier = 0;\n    /** @type {Boolean|DraggableCursorParams} */\n    this.cursor = false;\n    /** @type {Spring} */\n    this.releaseXSpring = hasSpring ? /** @type {Spring} */(ease) : createSpring({\n      mass: setValue(parameters.releaseMass, 1),\n      stiffness: setValue(parameters.releaseStiffness, 80),\n      damping: setValue(parameters.releaseDamping, 20),\n    });\n    /** @type {Spring} */\n    this.releaseYSpring = hasSpring ? /** @type {Spring} */(ease) : createSpring({\n      mass: setValue(parameters.releaseMass, 1),\n      stiffness: setValue(parameters.releaseStiffness, 80),\n      damping: setValue(parameters.releaseDamping, 20),\n    });\n    /** @type {EasingFunction} */\n    this.releaseEase = customEase || eases.outQuint;\n    /** @type {Boolean} */\n    this.hasReleaseSpring = hasSpring;\n    /** @type {Callback<this>} */\n    this.onGrab = parameters.onGrab || noop;\n    /** @type {Callback<this>} */\n    this.onDrag = parameters.onDrag || noop;\n    /** @type {Callback<this>} */\n    this.onRelease = parameters.onRelease || noop;\n    /** @type {Callback<this>} */\n    this.onUpdate = parameters.onUpdate || noop;\n    /** @type {Callback<this>} */\n    this.onSettle = parameters.onSettle || noop;\n    /** @type {Callback<this>} */\n    this.onSnap = parameters.onSnap || noop;\n    /** @type {Callback<this>} */\n    this.onResize = parameters.onResize || noop;\n    /** @type {Callback<this>} */\n    this.onAfterResize = parameters.onAfterResize || noop;\n    /** @type {[Number, Number]} */\n    this.disabled = [0, 0];\n    /** @type {AnimatableParams} */\n    const animatableParams = {};\n    if (modifier) animatableParams.modifier = modifier;\n    if (isUnd(paramX) || paramX === true) {\n      animatableParams[xProp] = 0;\n    } else if (isObj(paramX)) {\n      const paramXObject = /** @type {DraggableAxisParam} */(paramX);\n      const animatableXParams = {};\n      if (paramXObject.modifier) animatableXParams.modifier = paramXObject.modifier;\n      if (paramXObject.composition) animatableXParams.composition = paramXObject.composition;\n      animatableParams[xProp] = animatableXParams;\n    } else if (paramX === false) {\n      animatableParams[xProp] = 0;\n      this.disabled[0] = 1;\n    }\n    if (isUnd(paramY) || paramY === true) {\n      animatableParams[yProp] = 0;\n    } else if (isObj(paramY)) {\n      const paramYObject = /** @type {DraggableAxisParam} */(paramY);\n      const animatableYParams = {};\n      if (paramYObject.modifier) animatableYParams.modifier = paramYObject.modifier;\n      if (paramYObject.composition) animatableYParams.composition = paramYObject.composition;\n      animatableParams[yProp] = animatableYParams;\n    } else if (paramY === false) {\n      animatableParams[yProp] = 0;\n      this.disabled[1] = 1;\n    }\n    /** @type {AnimatableObject} */\n    this.animate = /** @type {AnimatableObject} */(new Animatable(this.$target, animatableParams));\n    // Internal props\n    this.xProp = xProp;\n    this.yProp = yProp;\n    this.destX = 0;\n    this.destY = 0;\n    this.deltaX = 0;\n    this.deltaY = 0;\n    this.scroll = {x: 0, y: 0};\n    /** @type {[Number, Number, Number, Number]} */\n    this.coords = [this.x, this.y, 0, 0]; // x, y, temp x, temp y\n    /** @type {[Number, Number]} */\n    this.snapped = [0, 0]; // x, y\n    /** @type {[Number, Number, Number, Number, Number, Number, Number, Number]} */\n    this.pointer = [0, 0, 0, 0, 0, 0, 0, 0]; // x1, y1, x2, y2, temp x1, temp y1, temp x2, temp y2\n    /** @type {[Number, Number]} */\n    this.scrollView = [0, 0]; // w, h\n    /** @type {[Number, Number, Number, Number]} */\n    this.dragArea = [0, 0, 0, 0]; // x, y, w, h\n    /** @type {[Number, Number, Number, Number]} */\n    this.containerBounds = [-1e12, maxValue, maxValue, -1e12]; // t, r, b, l\n    /** @type {[Number, Number, Number, Number]} */\n    this.scrollBounds = [0, 0, 0, 0]; // t, r, b, l\n    /** @type {[Number, Number, Number, Number]} */\n    this.targetBounds = [0, 0, 0, 0]; // t, r, b, l\n    /** @type {[Number, Number]} */\n    this.window = [0, 0]; // w, h\n    /** @type {[Number, Number, Number]} */\n    this.velocityStack = [0, 0, 0];\n    /** @type {Number} */\n    this.velocityStackIndex = 0;\n    /** @type {Number} */\n    this.velocityTime = now();\n    /** @type {Number} */\n    this.velocity = 0;\n    /** @type {Number} */\n    this.angle = 0;\n    /** @type {JSAnimation} */\n    this.cursorStyles = null;\n    /** @type {JSAnimation} */\n    this.triggerStyles = null;\n    /** @type {JSAnimation} */\n    this.bodyStyles = null;\n    /** @type {JSAnimation} */\n    this.targetStyles = null;\n    /** @type {JSAnimation} */\n    this.touchActionStyles = null;\n    this.transforms = new Transforms(this.$target);\n    this.overshootCoords = { x: 0, y: 0 };\n    this.overshootXTicker = new Timer({ autoplay: false }, null, 0).init();\n    this.overshootYTicker = new Timer({ autoplay: false }, null, 0).init();\n    this.updateTicker = new Timer({ autoplay: false }, null, 0).init();\n    this.overshootXTicker.onUpdate = () => {\n      if (this.disabled[0]) return;\n      this.updated = true;\n      this.manual = true;\n      this.animate[this.xProp](this.overshootCoords.x, 0);\n    };\n    this.overshootXTicker.onComplete = () => {\n      if (this.disabled[0]) return;\n      this.manual = false;\n      this.animate[this.xProp](this.overshootCoords.x, 0);\n    };\n    this.overshootYTicker.onUpdate = () => {\n      if (this.disabled[1]) return;\n      this.updated = true;\n      this.manual = true;\n      this.animate[this.yProp](this.overshootCoords.y, 0);\n    };\n    this.overshootYTicker.onComplete = () => {\n      if (this.disabled[1]) return;\n      this.manual = false;\n      this.animate[this.yProp](this.overshootCoords.y, 0);\n    };\n    this.updateTicker.onUpdate = () => this.update();\n    this.contained = !isUnd(container);\n    this.manual = false;\n    this.grabbed = false;\n    this.dragged = false;\n    this.updated = false;\n    this.released = false;\n    this.canScroll = false;\n    this.enabled = false;\n    this.initialized = false;\n    this.activeProp = this.disabled[1] ? xProp : yProp;\n    this.animate.animations[this.activeProp].onRender = () => {\n      const hasUpdated = this.updated;\n      const hasMoved = this.grabbed && hasUpdated;\n      const hasReleased = !hasMoved && this.released;\n      const x = this.x;\n      const y = this.y;\n      const dx = x - this.coords[2];\n      const dy = y - this.coords[3];\n      this.deltaX = dx;\n      this.deltaY = dy;\n      this.coords[2] = x;\n      this.coords[3] = y;\n      if (hasUpdated) {\n        this.onUpdate(this);\n      }\n      if (!hasReleased) {\n        this.updated = false;\n      } else {\n        this.computeVelocity(dx, dy);\n        this.angle = atan2(dy, dx);\n      }\n    };\n    this.animate.animations[this.activeProp].onComplete = () => {\n      if ((!this.grabbed && this.released)) {\n        // Set eleased to false before calling onSettle to avoid recursion\n        this.released = false;\n      }\n      if (!this.manual) {\n        this.deltaX = 0;\n        this.deltaY = 0;\n        this.velocity = 0;\n        this.velocityStack[0] = 0;\n        this.velocityStack[1] = 0;\n        this.velocityStack[2] = 0;\n        this.velocityStackIndex = 0;\n        this.onSettle(this);\n      }\n    };\n    this.resizeTicker = new Timer({\n      autoplay: false,\n      duration: 150 * globals.timeScale,\n      onComplete: () => {\n        this.onResize(this);\n        this.refresh();\n        this.onAfterResize(this);\n      },\n    }).init();\n    this.parameters = parameters;\n    this.resizeObserver = new ResizeObserver(() => {\n      if (this.initialized) {\n        this.resizeTicker.restart();\n      } else {\n        this.initialized = true;\n      }\n    });\n    this.enable();\n    this.refresh();\n    this.resizeObserver.observe(this.$container);\n    if (!isObj(target)) this.resizeObserver.observe(this.$target);\n  }\n\n  /**\n   * @param  {Number} dx\n   * @param  {Number} dy\n   * @return {Number}\n   */\n  computeVelocity(dx, dy) {\n    const prevTime = this.velocityTime;\n    const curTime = now();\n    const elapsed = curTime - prevTime;\n    if (elapsed < 17) return this.velocity;\n    this.velocityTime = curTime;\n    const velocityStack = this.velocityStack;\n    const vMul = this.velocityMultiplier;\n    const minV = this.minVelocity;\n    const maxV = this.maxVelocity;\n    const vi = this.velocityStackIndex;\n    velocityStack[vi] = round(clamp((sqrt(dx * dx + dy * dy) / elapsed) * vMul, minV, maxV), 5);\n    const velocity = max(velocityStack[0], velocityStack[1], velocityStack[2]);\n    this.velocity = velocity;\n    this.velocityStackIndex = (vi + 1) % 3;\n    return velocity;\n  }\n\n  /**\n   * @param {Number}  x\n   * @param {Boolean} [muteUpdateCallback]\n   * @return {this}\n   */\n  setX(x, muteUpdateCallback = false) {\n    if (this.disabled[0]) return;\n    const v = round(x, 5);\n    this.overshootXTicker.pause();\n    this.manual = true;\n    this.updated = !muteUpdateCallback;\n    this.destX = v;\n    this.snapped[0] = snap(v, this.snapX);\n    this.animate[this.xProp](v, 0);\n    this.manual = false;\n    return this;\n  }\n\n  /**\n   * @param {Number}  y\n   * @param {Boolean} [muteUpdateCallback]\n   * @return {this}\n   */\n  setY(y, muteUpdateCallback = false) {\n    if (this.disabled[1]) return;\n    const v = round(y, 5);\n    this.overshootYTicker.pause();\n    this.manual = true;\n    this.updated = !muteUpdateCallback;\n    this.destY = v;\n    this.snapped[1] = snap(v, this.snapY);\n    this.animate[this.yProp](v, 0);\n    this.manual = false;\n    return this;\n  }\n\n  get x() {\n    return round(/** @type {Number} */(this.animate[this.xProp]()), globals.precision);\n  }\n\n  set x(x) {\n    this.setX(x, false);\n  }\n\n  get y() {\n    return round(/** @type {Number} */(this.animate[this.yProp]()), globals.precision);\n  }\n\n  set y(y) {\n    this.setY(y, false);\n  }\n\n  get progressX() {\n    return mapRange(this.x, this.containerBounds[3], this.containerBounds[1], 0, 1);\n  }\n\n  set progressX(x) {\n    this.setX(mapRange(x, 0, 1, this.containerBounds[3], this.containerBounds[1]), false);\n  }\n\n  get progressY() {\n    return mapRange(this.y, this.containerBounds[0], this.containerBounds[2], 0, 1);\n  }\n\n  set progressY(y) {\n    this.setY(mapRange(y, 0, 1, this.containerBounds[0], this.containerBounds[2]), false);\n  }\n\n  updateScrollCoords() {\n    const sx = round(this.useWin ? win.scrollX : this.$container.scrollLeft, 0);\n    const sy = round(this.useWin ? win.scrollY : this.$container.scrollTop, 0);\n    const [ cpt, cpr, cpb, cpl ] = this.containerPadding;\n    const threshold = this.scrollThreshold;\n    this.scroll.x = sx;\n    this.scroll.y = sy;\n    this.scrollBounds[0] = sy - this.targetBounds[0] + cpt - threshold;\n    this.scrollBounds[1] = sx - this.targetBounds[1] - cpr + threshold;\n    this.scrollBounds[2] = sy - this.targetBounds[2] - cpb + threshold;\n    this.scrollBounds[3] = sx - this.targetBounds[3] + cpl - threshold;\n  }\n\n  updateBoundingValues() {\n    const $container = this.$container;\n    const cx = this.x;\n    const cy = this.y;\n    const cx2 = this.coords[2];\n    const cy2 =  this.coords[3];\n    // Prevents interfering with the scroll area in cases the target is outside of the container\n    // Make sure the temp coords are also adjuset to prevents wrong delta calculation on updates\n    this.coords[2] = 0;\n    this.coords[3] = 0;\n    this.setX(0, true);\n    this.setY(0, true);\n    this.transforms.remove();\n    const iw = this.window[0] = win.innerWidth;\n    const ih = this.window[1] = win.innerHeight;\n    const uw = this.useWin;\n    const sw = $container.scrollWidth;\n    const sh = $container.scrollHeight;\n    const fx = this.fixed;\n    const transformContainerRect = $container.getBoundingClientRect();\n    const [ cpt, cpr, cpb, cpl ] = this.containerPadding;\n    this.dragArea[0] = uw ? 0 : transformContainerRect.left;\n    this.dragArea[1] = uw ? 0 : transformContainerRect.top;\n    this.scrollView[0] = uw ? clamp(sw, iw, sw) : sw;\n    this.scrollView[1] = uw ? clamp(sh, ih, sh) : sh;\n    this.updateScrollCoords();\n    const { width, height, left, top, right, bottom } = $container.getBoundingClientRect();\n    this.dragArea[2] = round(uw ? clamp(width, iw, iw) : width, 0);\n    this.dragArea[3] = round(uw ? clamp(height, ih, ih) : height, 0);\n    const containerOverflow = getTargetValue($container, 'overflow');\n    const visibleOverflow = containerOverflow === 'visible';\n    const hiddenOverflow = containerOverflow === 'hidden';\n    this.canScroll = fx ? false :\n      this.contained &&\n      (($container === doc.body && visibleOverflow) || (!hiddenOverflow && !visibleOverflow)) &&\n      (sw > this.dragArea[2] + cpl - cpr || sh > this.dragArea[3] + cpt - cpb) &&\n      (!this.containerArray || (this.containerArray && !isArr(this.containerArray)));\n    if (this.contained) {\n      const sx = this.scroll.x;\n      const sy = this.scroll.y;\n      const canScroll = this.canScroll;\n      const targetRect = this.$target.getBoundingClientRect();\n      const hiddenLeft = canScroll ? uw ? 0 : $container.scrollLeft : 0;\n      const hiddenTop = canScroll ? uw ? 0 : $container.scrollTop : 0;\n      const hiddenRight = canScroll ? this.scrollView[0] - hiddenLeft - width : 0;\n      const hiddenBottom = canScroll ? this.scrollView[1] - hiddenTop - height : 0;\n      this.targetBounds[0] = round((targetRect.top + sy) - (uw ? 0 : top), 0);\n      this.targetBounds[1] = round((targetRect.right + sx) - (uw ? iw : right), 0);\n      this.targetBounds[2] = round((targetRect.bottom + sy) - (uw ? ih : bottom), 0);\n      this.targetBounds[3] = round((targetRect.left + sx) - (uw ? 0 : left), 0);\n      if (this.containerArray) {\n        this.containerBounds[0] = this.containerArray[0] + cpt;\n        this.containerBounds[1] = this.containerArray[1] - cpr;\n        this.containerBounds[2] = this.containerArray[2] - cpb;\n        this.containerBounds[3] = this.containerArray[3] + cpl;\n      } else {\n        this.containerBounds[0] = -round(targetRect.top - (fx ? clamp(top, 0, ih) : top) + hiddenTop - cpt, 0);\n        this.containerBounds[1] = -round(targetRect.right - (fx ? clamp(right, 0, iw) : right) - hiddenRight + cpr, 0);\n        this.containerBounds[2] = -round(targetRect.bottom - (fx ? clamp(bottom, 0, ih) : bottom) - hiddenBottom + cpb, 0);\n        this.containerBounds[3] = -round(targetRect.left - (fx ? clamp(left, 0, iw) : left) + hiddenLeft - cpl, 0);\n      }\n    }\n    this.transforms.revert();\n    // Restore coordinates\n    this.coords[2] = cx2;\n    this.coords[3] = cy2;\n    this.setX(cx, true);\n    this.setY(cy, true);\n  }\n\n  /**\n   * Returns 0 if not OB, 1 if x is OB, 2 if y is OB, 3 if both x and y are OB\n   *\n   * @param  {Array} bounds\n   * @param  {Number} x\n   * @param  {Number} y\n   * @return {Number}\n   */\n  isOutOfBounds(bounds, x, y) {\n    if (!this.contained) return 0;\n    const [ bt, br, bb, bl ] = bounds;\n    const [ dx, dy ] = this.disabled;\n    const obx = !dx && x < bl || !dx && x > br;\n    const oby = !dy && y < bt || !dy && y > bb;\n    return obx && !oby ? 1 : !obx && oby ? 2 : obx && oby ? 3 : 0;\n  }\n\n  refresh() {\n    const params = this.parameters;\n    const paramX = params.x;\n    const paramY = params.y;\n    const container = parseDraggableFunctionParameter(params.container, this);\n    const cp = parseDraggableFunctionParameter(params.containerPadding, this) || 0;\n    const containerPadding = /** @type {[Number, Number, Number, Number]} */(isArr(cp) ? cp : [cp, cp, cp, cp]);\n    const cx = this.x;\n    const cy = this.y;\n    const parsedCursorStyles = parseDraggableFunctionParameter(params.cursor, this);\n    const cursorStyles = { onHover: 'grab', onGrab: 'grabbing' };\n    if (parsedCursorStyles) {\n      const { onHover, onGrab } = /** @type {DraggableCursorParams} */(parsedCursorStyles);\n      if (onHover) cursorStyles.onHover = onHover;\n      if (onGrab) cursorStyles.onGrab = onGrab;\n    }\n    this.containerArray = isArr(container) ? container : null;\n    this.$container = /** @type {HTMLElement} */(container && !this.containerArray ? parseTargets(/** @type {DOMTarget} */(container))[0] : doc.body);\n    this.useWin = this.$container === doc.body;\n    /** @type {Window | HTMLElement} */\n    this.$scrollContainer = this.useWin ? win : this.$container;\n    this.isFinePointer = matchMedia('(pointer:fine)').matches;\n    this.containerPadding = setValue(containerPadding, [0, 0, 0, 0]);\n    this.containerFriction = clamp(setValue(parseDraggableFunctionParameter(params.containerFriction, this), .8), 0, 1);\n    this.releaseContainerFriction = clamp(setValue(parseDraggableFunctionParameter(params.releaseContainerFriction, this), this.containerFriction), 0, 1);\n    this.snapX = parseDraggableFunctionParameter(isObj(paramX) && !isUnd(paramX.snap) ? paramX.snap : params.snap, this);\n    this.snapY = parseDraggableFunctionParameter(isObj(paramY) && !isUnd(paramY.snap) ? paramY.snap : params.snap, this);\n    this.scrollSpeed = setValue(parseDraggableFunctionParameter(params.scrollSpeed, this), 1.5);\n    this.scrollThreshold = setValue(parseDraggableFunctionParameter(params.scrollThreshold, this), 20);\n    this.dragSpeed = setValue(parseDraggableFunctionParameter(params.dragSpeed, this), 1);\n    this.minVelocity = setValue(parseDraggableFunctionParameter(params.minVelocity, this), 0);\n    this.maxVelocity = setValue(parseDraggableFunctionParameter(params.maxVelocity, this), 50);\n    this.velocityMultiplier = setValue(parseDraggableFunctionParameter(params.velocityMultiplier, this), 1);\n    this.cursor = parsedCursorStyles === false ? false : cursorStyles;\n    this.updateBoundingValues();\n\n    // const ob = this.isOutOfBounds(this.containerBounds, this.x, this.y);\n    // if (ob === 1 || ob === 3) this.progressX = px;\n    // if (ob === 2 || ob === 3) this.progressY = py;\n\n    // if (this.initialized && this.contained) {\n    //   if (this.progressX !== px) this.progressX = px;\n    //   if (this.progressY !== py) this.progressY = py;\n    // }\n\n    const [ bt, br, bb, bl ] = this.containerBounds;\n    this.setX(clamp(cx, bl, br), true);\n    this.setY(clamp(cy, bt, bb), true);\n  }\n\n  update() {\n    this.updateScrollCoords();\n    if (this.canScroll) {\n      const [ cpt, cpr, cpb, cpl ] = this.containerPadding;\n      const [ sw, sh ] = this.scrollView;\n      const daw = this.dragArea[2];\n      const dah = this.dragArea[3];\n      const csx = this.scroll.x;\n      const csy = this.scroll.y;\n      const nsw = this.$container.scrollWidth;\n      const nsh = this.$container.scrollHeight;\n      const csw = this.useWin ? clamp(nsw, this.window[0], nsw) : nsw;\n      const csh = this.useWin ? clamp(nsh, this.window[1], nsh) : nsh;\n      const swd = sw - csw;\n      const shd = sh - csh;\n      // Handle cases where the scrollarea dimensions changes during drag\n      if (this.dragged && swd > 0) {\n        this.coords[0] -= swd;\n        this.scrollView[0] = csw;\n      }\n      if (this.dragged && shd > 0) {\n        this.coords[1] -= shd;\n        this.scrollView[1] = csh;\n      }\n      // Handle autoscroll when target is at the edges of the scroll bounds\n      const s = this.scrollSpeed * 10;\n      const threshold = this.scrollThreshold;\n      const [ x, y ] = this.coords;\n      const [ st, sr, sb, sl ] = this.scrollBounds;\n      const t = round(clamp((y - st + cpt) / threshold, -1, 0) * s, 0);\n      const r = round(clamp((x - sr - cpr) / threshold, 0, 1) * s, 0);\n      const b = round(clamp((y - sb - cpb) / threshold, 0, 1) * s, 0);\n      const l = round(clamp((x - sl + cpl) / threshold, -1, 0) * s, 0);\n      if (t || b || l || r) {\n        const [nx, ny] = this.disabled;\n        let scrollX = csx;\n        let scrollY = csy;\n        if (!nx) {\n          scrollX = round(clamp(csx + (l || r), 0, sw - daw), 0);\n          this.coords[0] -= csx - scrollX;\n        }\n        if (!ny) {\n          scrollY = round(clamp(csy + (t || b), 0, sh - dah), 0);\n          this.coords[1] -= csy - scrollY;\n        }\n        // Note: Safari mobile requires to use different scroll methods depending if using the window or not\n        if (this.useWin) {\n          this.$scrollContainer.scrollBy(-(csx - scrollX), -(csy - scrollY));\n        } else {\n          this.$scrollContainer.scrollTo(scrollX, scrollY);\n        }\n      }\n    }\n    const [ ct, cr, cb, cl ] = this.containerBounds;\n    const [ px1, py1, px2, py2, px3, py3 ] = this.pointer;\n    this.coords[0] += (px1 - px3) * this.dragSpeed;\n    this.coords[1] += (py1 - py3) * this.dragSpeed;\n    this.pointer[4] = px1;\n    this.pointer[5] = py1;\n    const [ cx, cy ] = this.coords;\n    const [ sx, sy ] = this.snapped;\n    const cf = (1 - this.containerFriction) * this.dragSpeed;\n    this.setX(cx > cr ? cr + (cx - cr) * cf : cx < cl ? cl + (cx - cl) * cf : cx, false);\n    this.setY(cy > cb ? cb + (cy - cb) * cf : cy < ct ? ct + (cy - ct) * cf : cy, false);\n    this.computeVelocity(px1 - px3, py1 - py3);\n    this.angle = atan2(py1 - py2, px1 - px2);\n    const [ nsx, nsy ] = this.snapped;\n    if (nsx !== sx && this.snapX || nsy !== sy && this.snapY) {\n      this.onSnap(this);\n    }\n  }\n\n  stop() {\n    this.updateTicker.pause();\n    this.overshootXTicker.pause();\n    this.overshootYTicker.pause();\n    // Pauses the in bounds onRelease animations\n    for (let prop in this.animate.animations) this.animate.animations[prop].pause();\n    remove(this, null, 'x');\n    remove(this, null, 'y');\n    remove(this, null, 'progressX');\n    remove(this, null, 'progressY');\n    remove(this.scroll); // Removes any active animations on the container scroll\n    remove(this.overshootCoords); // Removes active overshoot animations\n    return this;\n  }\n\n  /**\n   * @param {Number} [duration]\n   * @param {Number} [gap]\n   * @param {EasingParam} [ease]\n   * @return {this}\n   */\n  scrollInView(duration, gap = 0, ease = eases.inOutQuad) {\n    this.updateScrollCoords();\n    const x = this.destX;\n    const y = this.destY;\n    const scroll = this.scroll;\n    const scrollBounds = this.scrollBounds;\n    const canScroll = this.canScroll;\n    if (!this.containerArray && this.isOutOfBounds(scrollBounds, x, y)) {\n      const [ st, sr, sb, sl ] = scrollBounds;\n      const t = round(clamp(y - st, -1e12, 0), 0);\n      const r = round(clamp(x - sr, 0, maxValue), 0);\n      const b = round(clamp(y - sb, 0, maxValue), 0);\n      const l = round(clamp(x - sl, -1e12, 0), 0);\n      new JSAnimation(scroll, {\n        x: round(scroll.x + (l ? l - gap : r ? r + gap : 0), 0),\n        y: round(scroll.y + (t ? t - gap : b ? b + gap : 0), 0),\n        duration: isUnd(duration) ? 350 * globals.timeScale : duration,\n        ease,\n        onUpdate: () => {\n          this.canScroll = false;\n          this.$scrollContainer.scrollTo(scroll.x, scroll.y);\n        }\n      }).init().then(() => {\n        this.canScroll = canScroll;\n      });\n    }\n    return this;\n  }\n\n  handleHover() {\n    if (this.isFinePointer && this.cursor && !this.cursorStyles) {\n      this.cursorStyles = setTargetValues(this.$trigger, {\n        cursor: /** @type {DraggableCursorParams} */(this.cursor).onHover\n      });\n    }\n  }\n\n  /**\n   * @param  {Number} [duration]\n   * @param  {Number} [gap]\n   * @param  {EasingParam} [ease]\n   * @return {this}\n   */\n  animateInView(duration, gap = 0, ease = eases.inOutQuad) {\n    this.stop();\n    this.updateBoundingValues();\n    const x = this.x;\n    const y = this.y;\n    const [ cpt, cpr, cpb, cpl ] = this.containerPadding;\n    const bt = this.scroll.y - this.targetBounds[0] + cpt + gap;\n    const br = this.scroll.x - this.targetBounds[1] - cpr - gap;\n    const bb = this.scroll.y - this.targetBounds[2] - cpb - gap;\n    const bl = this.scroll.x - this.targetBounds[3] + cpl + gap;\n    const ob = this.isOutOfBounds([bt, br, bb, bl], x, y);\n    if (ob) {\n      const [ disabledX, disabledY ] = this.disabled;\n      const destX = clamp(snap(x, this.snapX), bl, br);\n      const destY = clamp(snap(y, this.snapY), bt, bb);\n      const dur = isUnd(duration) ? 350 * globals.timeScale : duration;\n      if (!disabledX && (ob === 1 || ob === 3)) this.animate[this.xProp](destX, dur, ease);\n      if (!disabledY && (ob === 2 || ob === 3)) this.animate[this.yProp](destY, dur, ease);\n    }\n    return this;\n  }\n\n  /**\n   * @param {MouseEvent|TouchEvent} e\n   */\n  handleDown(e) {\n    const $eTarget = /** @type {HTMLElement} */(e.target);\n    if (this.grabbed || /** @type {HTMLInputElement}  */($eTarget).type === 'range') return;\n\n    e.stopPropagation();\n\n    this.grabbed = true;\n    this.released = false;\n    this.stop();\n    this.updateBoundingValues();\n    const touches = /** @type {TouchEvent} */(e).changedTouches;\n    const eventX = touches ? touches[0].clientX : /** @type {MouseEvent} */(e).clientX;\n    const eventY = touches ? touches[0].clientY : /** @type {MouseEvent} */(e).clientY;\n    const { x, y } = this.transforms.normalizePoint(eventX, eventY);\n    const [ ct, cr, cb, cl ] = this.containerBounds;\n    const cf = (1 - this.containerFriction) * this.dragSpeed;\n    const cx = this.x;\n    const cy = this.y;\n    this.coords[0] = this.coords[2] = !cf ? cx : cx > cr ? cr + (cx - cr) / cf : cx < cl ? cl + (cx - cl) / cf : cx;\n    this.coords[1] = this.coords[3] = !cf ? cy : cy > cb ? cb + (cy - cb) / cf : cy < ct ? ct + (cy - ct) / cf : cy;\n    this.pointer[0] = x;\n    this.pointer[1] = y;\n    this.pointer[2] = x;\n    this.pointer[3] = y;\n    this.pointer[4] = x;\n    this.pointer[5] = y;\n    this.pointer[6] = x;\n    this.pointer[7] = y;\n    this.deltaX = 0;\n    this.deltaY = 0;\n    this.velocity = 0;\n    this.velocityStack[0] = 0;\n    this.velocityStack[1] = 0;\n    this.velocityStack[2] = 0;\n    this.velocityStackIndex = 0;\n    this.angle = 0;\n    if (this.targetStyles) {\n      this.targetStyles.revert();\n      this.targetStyles = null;\n    }\n    const z = /** @type {Number} */(getTargetValue(this.$target, 'zIndex', false));\n    zIndex = (z > zIndex ? z : zIndex) + 1;\n    this.targetStyles = setTargetValues(this.$target, { zIndex });\n    if (this.triggerStyles) {\n      this.triggerStyles.revert();\n      this.triggerStyles = null;\n    }\n    if (this.cursorStyles) {\n      this.cursorStyles.revert();\n      this.cursorStyles = null;\n    }\n    if (this.isFinePointer && this.cursor) {\n      this.bodyStyles = setTargetValues(doc.body, {\n        cursor: /** @type {DraggableCursorParams} */(this.cursor).onGrab\n      });\n    }\n    this.scrollInView(100, 0, eases.out(3));\n    this.onGrab(this);\n\n    doc.addEventListener('touchmove', this);\n    doc.addEventListener('touchend', this);\n    doc.addEventListener('touchcancel', this);\n    doc.addEventListener('mousemove', this);\n    doc.addEventListener('mouseup', this);\n    doc.addEventListener('selectstart', this);\n  }\n\n  /**\n   * @param {MouseEvent|TouchEvent} e\n   */\n  handleMove(e) {\n    if (!this.grabbed) return;\n    const touches = /** @type {TouchEvent} */(e).changedTouches;\n    const eventX = touches ? touches[0].clientX : /** @type {MouseEvent} */(e).clientX;\n    const eventY = touches ? touches[0].clientY : /** @type {MouseEvent} */(e).clientY;\n    const { x, y } = this.transforms.normalizePoint(eventX, eventY);\n    const movedX = x - this.pointer[6];\n    const movedY = y - this.pointer[7];\n\n    let $parent = /** @type {HTMLElement} */(e.target);\n    let isAtTop = false;\n    let isAtBottom = false;\n    let canTouchScroll = false;\n\n    while (touches && $parent && $parent !== this.$trigger) {\n      const overflowY = getTargetValue($parent, 'overflow-y');\n      if (overflowY !== 'hidden' && overflowY !== 'visible') {\n        const { scrollTop, scrollHeight, clientHeight } = $parent;\n        if (scrollHeight > clientHeight) {\n          canTouchScroll = true;\n          isAtTop = scrollTop <= 3;\n          isAtBottom = scrollTop >= (scrollHeight - clientHeight) - 3;\n          break;\n        }\n      }\n      $parent = /** @type {HTMLElement} */($parent.parentNode);\n    }\n\n    if (canTouchScroll && ((!isAtTop && !isAtBottom) || (isAtTop && movedY < 0) || (isAtBottom && movedY > 0))) {\n\n      this.pointer[0] = x;\n      this.pointer[1] = y;\n      this.pointer[2] = x;\n      this.pointer[3] = y;\n      this.pointer[4] = x;\n      this.pointer[5] = y;\n      this.pointer[6] = x;\n      this.pointer[7] = y;\n\n    } else {\n\n      preventDefault(e);\n\n      // Needed to prevents click on handleUp\n      if (!this.triggerStyles) this.triggerStyles = setTargetValues(this.$trigger, { pointerEvents: 'none' });\n      // Needed to prevent page scroll while dragging on touch devvice\n      this.$trigger.addEventListener('touchstart', preventDefault, { passive: false });\n      this.$trigger.addEventListener('touchmove', preventDefault, { passive: false });\n      this.$trigger.addEventListener('touchend', preventDefault);\n\n\n      if ((!this.disabled[0] && abs(movedX) > 3) || (!this.disabled[1] && abs(movedY) > 3)) {\n\n        this.updateTicker.resume();\n        this.pointer[2] = this.pointer[0];\n        this.pointer[3] = this.pointer[1];\n        this.pointer[0] = x;\n        this.pointer[1] = y;\n        this.dragged = true;\n        this.released = false;\n        this.onDrag(this);\n      }\n    }\n  }\n\n  handleUp() {\n\n    if (!this.grabbed) return;\n\n    this.updateTicker.pause();\n\n    if (this.triggerStyles) {\n      this.triggerStyles.revert();\n      this.triggerStyles = null;\n    }\n\n    if (this.bodyStyles) {\n      this.bodyStyles.revert();\n      this.bodyStyles = null;\n    }\n\n    const [ disabledX, disabledY ] = this.disabled;\n    const [ px1, py1, px2, py2, px3, py3 ] = this.pointer;\n    const [ ct, cr, cb, cl ] = this.containerBounds;\n    const [ sx, sy ] = this.snapped;\n    const springX = this.releaseXSpring;\n    const springY = this.releaseYSpring;\n    const releaseEase = this.releaseEase;\n    const hasReleaseSpring = this.hasReleaseSpring;\n    const overshootCoords = this.overshootCoords;\n    const cx = this.x;\n    const cy = this.y;\n    const pv = this.computeVelocity(px1 - px3, py1 - py3);\n    const pa = this.angle = atan2(py1 - py2, px1 - px2);\n    const ds = pv * 150;\n    const cf = (1 - this.releaseContainerFriction) * this.dragSpeed;\n    const nx = cx + (cos(pa) * ds);\n    const ny = cy + (sin(pa) * ds);\n    const bx = nx > cr ? cr + (nx - cr) * cf : nx < cl ? cl + (nx - cl) * cf : nx;\n    const by = ny > cb ? cb + (ny - cb) * cf : ny < ct ? ct + (ny - ct) * cf : ny;\n    const dx = this.destX = clamp(round(snap(bx, this.snapX), 5), cl, cr);\n    const dy = this.destY = clamp(round(snap(by, this.snapY), 5), ct, cb);\n    const ob = this.isOutOfBounds(this.containerBounds, nx, ny);\n\n    let durationX = 0;\n    let durationY = 0;\n    let easeX = releaseEase;\n    let easeY = releaseEase;\n    let longestReleaseDuration = 0;\n\n    overshootCoords.x = cx;\n    overshootCoords.y = cy;\n\n    if (!disabledX) {\n      const directionX = dx === cr ? cx > cr ? -1 : 1 : cx < cl ? -1 : 1;\n      const distanceX = round(cx - dx, 0);\n      springX.velocity = disabledY && hasReleaseSpring ? distanceX ? (ds * directionX) / abs(distanceX) : 0 : pv;\n      const { ease, duration, restDuration } = springX;\n      durationX = cx === dx ? 0 : hasReleaseSpring ? duration : duration - (restDuration * globals.timeScale);\n      if (hasReleaseSpring) easeX = ease;\n      if (durationX > longestReleaseDuration) longestReleaseDuration = durationX;\n    }\n\n    if (!disabledY) {\n      const directionY = dy === cb ? cy > cb ? -1 : 1 : cy < ct ? -1 : 1;\n      const distanceY = round(cy - dy, 0);\n      springY.velocity = disabledX && hasReleaseSpring ? distanceY ? (ds * directionY) / abs(distanceY) : 0 : pv;\n      const { ease, duration, restDuration } = springY;\n      durationY = cy === dy ? 0 : hasReleaseSpring ? duration : duration - (restDuration * globals.timeScale);\n      if (hasReleaseSpring) easeY = ease;\n      if (durationY > longestReleaseDuration) longestReleaseDuration = durationY;\n    }\n\n    if (!hasReleaseSpring && ob && cf && (durationX || durationY)) {\n\n        const composition = compositionTypes.blend;\n\n        new JSAnimation(overshootCoords, {\n          x: { to: bx, duration: durationX * .65 },\n          y: { to: by, duration: durationY * .65 },\n          ease: releaseEase,\n          composition,\n        }).init();\n\n        new JSAnimation(overshootCoords, {\n          x: { to: dx, duration: durationX },\n          y: { to: dy, duration: durationY },\n          ease: releaseEase,\n          composition,\n        }).init();\n\n        this.overshootXTicker.stretch(durationX).restart();\n        this.overshootYTicker.stretch(durationY).restart();\n\n    } else {\n\n      if (!disabledX) this.animate[this.xProp](dx, durationX, easeX);\n      if (!disabledY) this.animate[this.yProp](dy, durationY, easeY);\n\n    }\n\n    this.scrollInView(longestReleaseDuration, this.scrollThreshold, releaseEase);\n\n    let hasSnapped = false;\n\n    if (dx !== sx) {\n      this.snapped[0] = dx;\n      if (this.snapX) hasSnapped = true;\n    }\n\n    if (dy !== sy && this.snapY) {\n      this.snapped[1] = dy;\n      if (this.snapY) hasSnapped = true;\n    }\n\n    if (hasSnapped) this.onSnap(this);\n\n    this.grabbed = false;\n    this.dragged = false;\n    this.updated = true;\n    this.released = true;\n\n    // It's important to trigger the callback after the release animations to be able to cancel them\n    this.onRelease(this);\n\n    this.$trigger.removeEventListener('touchstart', preventDefault);\n    this.$trigger.removeEventListener('touchmove', preventDefault);\n    this.$trigger.removeEventListener('touchend', preventDefault);\n\n    doc.removeEventListener('touchmove', this);\n    doc.removeEventListener('touchend', this);\n    doc.removeEventListener('touchcancel', this);\n    doc.removeEventListener('mousemove', this);\n    doc.removeEventListener('mouseup', this);\n    doc.removeEventListener('selectstart', this);\n  }\n\n  reset() {\n    this.stop();\n    this.resizeTicker.pause();\n    this.grabbed = false;\n    this.dragged = false;\n    this.updated = false;\n    this.released = false;\n    this.canScroll = false;\n    this.setX(0, true);\n    this.setY(0, true);\n    this.coords[0] = 0;\n    this.coords[1] = 0;\n    this.pointer[0] = 0;\n    this.pointer[1] = 0;\n    this.pointer[2] = 0;\n    this.pointer[3] = 0;\n    this.pointer[4] = 0;\n    this.pointer[5] = 0;\n    this.pointer[6] = 0;\n    this.pointer[7] = 0;\n    this.velocity = 0;\n    this.velocityStack[0] = 0;\n    this.velocityStack[1] = 0;\n    this.velocityStack[2] = 0;\n    this.velocityStackIndex = 0;\n    this.angle = 0;\n    return this;\n  }\n\n  enable() {\n    if (!this.enabled) {\n      this.enabled = true;\n      this.$target.classList.remove('is-disabled');\n      this.touchActionStyles = setTargetValues(this.$trigger, {\n        touchAction: this.disabled[0] ? 'pan-x' : this.disabled[1] ? 'pan-y' : 'none'\n      });\n      this.$trigger.addEventListener('touchstart', this, { passive: true });\n      this.$trigger.addEventListener('mousedown', this, { passive: true });\n      this.$trigger.addEventListener('mouseenter', this);\n    }\n    return this;\n  }\n\n  disable() {\n    this.enabled = false;\n    this.grabbed = false;\n    this.dragged = false;\n    this.updated = false;\n    this.released = false;\n    this.canScroll = false;\n    this.touchActionStyles.revert();\n    if (this.cursorStyles) {\n      this.cursorStyles.revert();\n      this.cursorStyles = null;\n    }\n    if (this.triggerStyles) {\n      this.triggerStyles.revert();\n      this.triggerStyles = null;\n    }\n    if (this.bodyStyles) {\n      this.bodyStyles.revert();\n      this.bodyStyles = null;\n    }\n    if (this.targetStyles) {\n      this.targetStyles.revert();\n      this.targetStyles = null;\n    }\n    this.stop();\n    this.$target.classList.add('is-disabled');\n    this.$trigger.removeEventListener('touchstart', this);\n    this.$trigger.removeEventListener('mousedown', this);\n    this.$trigger.removeEventListener('mouseenter', this);\n    doc.removeEventListener('touchmove', this);\n    doc.removeEventListener('touchend', this);\n    doc.removeEventListener('touchcancel', this);\n    doc.removeEventListener('mousemove', this);\n    doc.removeEventListener('mouseup', this);\n    doc.removeEventListener('selectstart', this);\n    return this;\n  }\n\n  revert() {\n    this.reset();\n    this.disable();\n    this.$target.classList.remove('is-disabled');\n    this.updateTicker.revert();\n    this.overshootXTicker.revert();\n    this.overshootYTicker.revert();\n    this.resizeTicker.revert();\n    this.animate.revert();\n    return this;\n  }\n\n  /**\n   * @param {Event} e\n   */\n  handleEvent(e) {\n    switch (e.type) {\n      case 'mousedown':\n        this.handleDown(/** @type {MouseEvent} */(e));\n        break;\n      case 'touchstart':\n        this.handleDown(/** @type {TouchEvent} */(e));\n        break;\n      case 'mousemove':\n        this.handleMove(/** @type {MouseEvent} */(e));\n        break;\n      case 'touchmove':\n        this.handleMove(/** @type {TouchEvent} */(e));\n        break;\n      case 'mouseup':\n        this.handleUp();\n        break;\n      case 'touchend':\n        this.handleUp();\n        break;\n      case 'touchcancel':\n        this.handleUp();\n        break;\n      case 'mouseenter':\n        this.handleHover();\n        break;\n      case 'selectstart':\n        preventDefault(e);\n        break;\n    }\n  }\n}\n\n/**\n * @param {TargetsParam} target\n * @param {DraggableParams} [parameters]\n * @return {Draggable}\n */\nconst createDraggable = (target, parameters) => new Draggable(target, parameters);\n\n\n\n\n/**\n * @typedef {Object} ReactRef\n * @property {HTMLElement|SVGElement|null} [current]\n */\n\n/**\n * @typedef {Object} AngularRef\n * @property {HTMLElement|SVGElement} [nativeElement]\n */\n\n/**\n * @typedef {Object} ScopeParams\n * @property {DOMTargetSelector|ReactRef|AngularRef} [root]\n * @property {DefaultsParams} [defaults]\n * @property {Record<String, String>} [mediaQueries]\n */\n\n/**\n * @callback ScopeCleanup\n * @param {Scope} [scope]\n */\n\n/**\n * @callback ScopeConstructor\n * @param {Scope} [scope]\n * @return {ScopeCleanup|void}\n */\n\n/**\n * @callback ScopeMethod\n * @param {...*} args\n * @return {ScopeCleanup|void}\n */\n\nclass Scope {\n  /** @param {ScopeParams} [parameters] */\n  constructor(parameters = {}) {\n    if (globals.scope) globals.scope.revertibles.push(this);\n    const rootParam = parameters.root;\n    /** @type {Document|DOMTarget} */\n    let root = doc;\n    if (rootParam) {\n      root = /** @type {ReactRef} */(rootParam).current ||\n             /** @type {AngularRef} */(rootParam).nativeElement ||\n             parseTargets(/** @type {DOMTargetSelector} */(rootParam))[0] ||\n             doc;\n    }\n    const scopeDefaults = parameters.defaults;\n    const globalDefault = globals.defaults;\n    const mediaQueries = parameters.mediaQueries;\n    /** @type {DefaultsParams} */\n    this.defaults = scopeDefaults ? mergeObjects(scopeDefaults, globalDefault) : globalDefault;\n    /** @type {Document|DOMTarget} */\n    this.root = root;\n    /** @type {Array<ScopeConstructor>} */\n    this.constructors = [];\n    /** @type {Array<Function>} */\n    this.revertConstructors = [];\n    /** @type {Array<Revertible>} */\n    this.revertibles = [];\n    /** @type {Record<String, Function>} */\n    this.methods = {};\n    /** @type {Record<String, Boolean>} */\n    this.matches = {};\n    /** @type {Record<String, MediaQueryList>} */\n    this.mediaQueryLists = {};\n    /** @type {Record<String, any>} */\n    this.data = {};\n    if (mediaQueries) {\n      for (let mq in mediaQueries) {\n        const _mq = win.matchMedia(mediaQueries[mq]);\n        this.mediaQueryLists[mq] = _mq;\n        _mq.addEventListener('change', this);\n      }\n    }\n  }\n\n  /**\n   * @callback ScoppedCallback\n   * @param {this} scope\n   * @return {any}\n   *\n   * @param {ScoppedCallback} cb\n   * @return {this}\n   */\n  execute(cb) {\n    let activeScope = globals.scope;\n    let activeRoot = globals.root;\n    let activeDefaults = globals.defaults;\n    globals.scope = this;\n    globals.root = this.root;\n    globals.defaults = this.defaults;\n    const mqs = this.mediaQueryLists;\n    for (let mq in mqs) this.matches[mq] = mqs[mq].matches;\n    const returned = cb(this);\n    globals.scope = activeScope;\n    globals.root = activeRoot;\n    globals.defaults = activeDefaults;\n    return returned;\n  }\n\n  /**\n   * @return {this}\n   */\n  refresh() {\n    this.execute(() => {\n      let i = this.revertibles.length;\n      let y = this.revertConstructors.length;\n      while (i--) this.revertibles[i].revert();\n      while (y--) this.revertConstructors[y](this);\n      this.revertibles.length = 0;\n      this.revertConstructors.length = 0;\n      this.constructors.forEach( constructor => {\n        const revertConstructor = constructor(this);\n        if (revertConstructor) {\n          this.revertConstructors.push(revertConstructor);\n        }\n      });\n    });\n    return this;\n  }\n\n  /**\n   * @callback contructorCallback\n   * @param {this} self\n   *\n   * @overload\n   * @param {String} a1\n   * @param {ScopeMethod} a2\n   * @return {this}\n   *\n   * @overload\n   * @param {contructorCallback} a1\n   * @return {this}\n   *\n   * @param {String|contructorCallback} a1\n   * @param {ScopeMethod} [a2]\n   */\n  add(a1, a2) {\n    if (isFnc(a1)) {\n      const constructor = /** @type {contructorCallback} */(a1);\n      this.constructors.push(constructor);\n      this.execute(() => {\n        const revertConstructor = constructor(this);\n        if (revertConstructor) {\n          this.revertConstructors.push(revertConstructor);\n        }\n      });\n    } else {\n      this.methods[/** @type {String} */(a1)] = (/** @type {any} */...args) => this.execute(() => a2(...args));\n    }\n    return this;\n  }\n\n  /**\n   * @param {Event} e\n   */\n  handleEvent(e) {\n    switch (e.type) {\n      case 'change':\n        this.refresh();\n        break;\n    }\n  }\n\n  revert() {\n    const revertibles = this.revertibles;\n    const revertConstructors = this.revertConstructors;\n    const mqs = this.mediaQueryLists;\n    let i = revertibles.length;\n    let y = revertConstructors.length;\n    while (i--) revertibles[i].revert();\n    while (y--) revertConstructors[y](this);\n    for (let mq in mqs) mqs[mq].removeEventListener('change', this);\n    revertibles.length = 0;\n    revertConstructors.length = 0;\n    this.constructors.length = 0;\n    this.matches = {};\n    this.methods = {};\n    this.mediaQueryLists = {};\n    this.data = {};\n  }\n}\n\n/**\n * @param {ScopeParams} [params]\n * @return {Scope}\n */\nconst createScope = params => new Scope(params);\n\n/**\n * @typedef {String|Number} ScrollThresholdValue\n */\n\n/**\n * @return {Number}\n */\nconst getMaxViewHeight = () => {\n  const $el = document.createElement('div');\n  doc.body.appendChild($el);\n  $el.style.height = '100lvh';\n  const height = $el.offsetHeight;\n  doc.body.removeChild($el);\n  return height;\n};\n\n/**\n * @template {ScrollThresholdValue|String|Number|Boolean|Function|Object} T\n * @param {T | ((observer: ScrollObserver) => T)} value\n * @param {ScrollObserver} scroller\n * @return {T}\n */\nconst parseScrollObserverFunctionParameter = (value, scroller) => value && isFnc(value) ? /** @type {Function} */(value)(scroller) : value;\n\nconst scrollContainers = new Map();\n\nclass ScrollContainer {\n  /**\n   * @param {HTMLElement} $el\n   */\n  constructor($el) {\n    /** @type {HTMLElement} */\n    this.element = $el;\n    /** @type {Boolean} */\n    this.useWin = this.element === doc.body;\n    /** @type {Number} */\n    this.winWidth = 0;\n    /** @type {Number} */\n    this.winHeight = 0;\n    /** @type {Number} */\n    this.width = 0;\n    /** @type {Number} */\n    this.height = 0;\n    /** @type {Number} */\n    this.left = 0;\n    /** @type {Number} */\n    this.top = 0;\n    /** @type {Number} */\n    this.zIndex = 0;\n    /** @type {Number} */\n    this.scrollX = 0;\n    /** @type {Number} */\n    this.scrollY = 0;\n    /** @type {Number} */\n    this.prevScrollX = 0;\n    /** @type {Number} */\n    this.prevScrollY = 0;\n    /** @type {Number} */\n    this.scrollWidth = 0;\n    /** @type {Number} */\n    this.scrollHeight = 0;\n    /** @type {Number} */\n    this.velocity = 0;\n    /** @type {Boolean} */\n    this.backwardX = false;\n    /** @type {Boolean} */\n    this.backwardY = false;\n    /** @type {Timer} */\n    this.scrollTicker = new Timer({\n      autoplay: false,\n      onBegin: () => this.dataTimer.resume(),\n      onUpdate: () => {\n        const backwards = this.backwardX || this.backwardY;\n        forEachChildren(this, (/** @type {ScrollObserver} */child) => child.handleScroll(), backwards);\n      },\n      onComplete: () => this.dataTimer.pause()\n    }).init();\n    /** @type {Timer} */\n    this.dataTimer = new Timer({\n      autoplay: false,\n      frameRate: 30,\n      onUpdate: self => {\n        const dt = self.deltaTime;\n        const px = this.prevScrollX;\n        const py = this.prevScrollY;\n        const nx = this.scrollX;\n        const ny = this.scrollY;\n        const dx = px - nx;\n        const dy = py - ny;\n        this.prevScrollX = nx;\n        this.prevScrollY = ny;\n        if (dx) this.backwardX = px > nx;\n        if (dy) this.backwardY = py > ny;\n        this.velocity = round(dt > 0 ? Math.sqrt(dx * dx + dy * dy) / dt : 0, 5);\n      }\n    }).init();\n    /** @type {Timer} */\n    this.resizeTicker = new Timer({\n      autoplay: false,\n      duration: 250 * globals.timeScale,\n      onComplete: () => {\n        this.updateWindowBounds();\n        this.refreshScrollObservers();\n        this.handleScroll();\n      }\n    }).init();\n    /** @type {Timer} */\n    this.wakeTicker = new Timer({\n      autoplay: false,\n      duration: 500 * globals.timeScale,\n      onBegin: () => {\n        this.scrollTicker.resume();\n      },\n      onComplete: () => {\n        this.scrollTicker.pause();\n      }\n    }).init();\n    /** @type {ScrollObserver} */\n    this._head = null;\n    /** @type {ScrollObserver} */\n    this._tail = null;\n    this.updateScrollCoords();\n    this.updateWindowBounds();\n    this.updateBounds();\n    this.refreshScrollObservers();\n    this.handleScroll();\n    this.resizeObserver = new ResizeObserver(() => this.resizeTicker.restart());\n    this.resizeObserver.observe(this.element);\n    (this.useWin ? win : this.element).addEventListener('scroll', this, false);\n  }\n\n  updateScrollCoords() {\n    const useWin = this.useWin;\n    const $el = this.element;\n    this.scrollX = round(useWin ? win.scrollX : $el.scrollLeft, 0);\n    this.scrollY = round(useWin ? win.scrollY : $el.scrollTop, 0);\n  }\n\n  updateWindowBounds() {\n    this.winWidth = win.innerWidth;\n    this.winHeight = getMaxViewHeight();\n  }\n\n  updateBounds() {\n    const style = getComputedStyle(this.element);\n    const $el = this.element;\n    this.scrollWidth = $el.scrollWidth + parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    this.scrollHeight = $el.scrollHeight + parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n    this.updateWindowBounds();\n    let width, height;\n    if (this.useWin) {\n      width = this.winWidth;\n      height = this.winHeight;\n    } else {\n      const elRect = $el.getBoundingClientRect();\n      width = elRect.width;\n      height = elRect.height;\n      this.top = elRect.top;\n      this.left = elRect.left;\n    }\n    this.width = width;\n    this.height = height;\n  }\n\n  refreshScrollObservers() {\n    forEachChildren(this, (/** @type {ScrollObserver} */child) => {\n      if (child._debug) {\n        child.removeDebug();\n      }\n    });\n    this.updateBounds();\n    forEachChildren(this, (/** @type {ScrollObserver} */child) => {\n      child.refresh();\n      if (child._debug) {\n        child.debug();\n      }\n    });\n  }\n\n  refresh() {\n    this.updateWindowBounds();\n    this.updateBounds();\n    this.refreshScrollObservers();\n    this.handleScroll();\n  }\n\n  handleScroll() {\n    this.updateScrollCoords();\n    this.wakeTicker.restart();\n  }\n\n  /**\n   * @param {Event} e\n   */\n  handleEvent(e) {\n    switch (e.type) {\n      case 'scroll':\n        this.handleScroll();\n        break;\n    }\n  }\n\n  revert() {\n    this.scrollTicker.cancel();\n    this.dataTimer.cancel();\n    this.resizeTicker.cancel();\n    this.wakeTicker.cancel();\n    this.resizeObserver.unobserve(this.element);\n    (this.useWin ? win : this.element).removeEventListener('scroll', this);\n    scrollContainers.delete(this.element);\n  }\n}\n\n/**\n * @param {TargetsParam} target\n * @return {ScrollContainer}\n */\nconst registerAndGetScrollContainer = target => {\n  const $el = /** @type {HTMLElement} */(target ? parseTargets(target)[0] || doc.body : doc.body);\n  let scrollContainer = scrollContainers.get($el);\n  if (!scrollContainer) {\n    scrollContainer = new ScrollContainer($el);\n    scrollContainers.set($el, scrollContainer);\n  }\n  return scrollContainer;\n};\n\n/**\n * @param {HTMLElement} $el\n * @param {Number|string} v\n * @param {Number} size\n * @param {Number} [under]\n * @param {Number} [over]\n * @return {Number}\n */\nconst convertValueToPx = ($el, v, size, under, over) => {\n  const clampMin = v === 'min';\n  const clampMax = v === 'max';\n  const value = v === 'top' || v === 'left' || v === 'start' || clampMin ? 0 :\n                v === 'bottom' || v === 'right' || v === 'end' || clampMax ? '100%' :\n                v === 'center' ? '50%' :\n                v;\n  const { n, u } = decomposeRawValue(value, decomposedOriginalValue);\n  let px = n;\n  if (u === '%') {\n    px = (n / 100) * size;\n  } else if (u) {\n    px = convertValueUnit($el, decomposedOriginalValue, 'px', true).n;\n  }\n  if (clampMax && under < 0) px += under;\n  if (clampMin && over > 0) px += over;\n  return px;\n};\n\n/**\n * @param {HTMLElement} $el\n * @param {ScrollThresholdValue} v\n * @param {Number} size\n * @param {Number} [under]\n * @param {Number} [over]\n * @return {Number}\n */\nconst parseBoundValue = ($el, v, size, under, over) => {\n  /** @type {Number} */\n  let value;\n  if (isStr(v)) {\n    const matchedOperator = relativeValuesExecRgx.exec(/** @type {String} */(v));\n    if (matchedOperator) {\n      const splitter = matchedOperator[0];\n      const operator = splitter[0];\n      const splitted = /** @type {String} */(v).split(splitter);\n      const clampMin = splitted[0] === 'min';\n      const clampMax = splitted[0] === 'max';\n      const valueAPx = convertValueToPx($el, splitted[0], size, under, over);\n      const valueBPx = convertValueToPx($el, splitted[1], size, under, over);\n      if (clampMin) {\n        const min = getRelativeValue(convertValueToPx($el, 'min', size), valueBPx, operator);\n        value = min < valueAPx ? valueAPx : min;\n      } else if (clampMax) {\n        const max = getRelativeValue(convertValueToPx($el, 'max', size), valueBPx, operator);\n        value = max > valueAPx ? valueAPx : max;\n      } else {\n        value = getRelativeValue(valueAPx, valueBPx, operator);\n      }\n    } else {\n      value = convertValueToPx($el, v, size, under, over);\n    }\n  } else {\n    value = /** @type {Number} */(v);\n  }\n  return round(value, 0);\n};\n\n/**\n * @param {JSAnimation} linked\n * @return {HTMLElement}\n */\nconst getAnimationDomTarget = linked => {\n  let $linkedTarget;\n  const linkedTargets = linked.targets;\n  for (let i = 0, l = linkedTargets.length; i < l; i++) {\n    const target = linkedTargets[i];\n    if (target[isDomSymbol]) {\n      $linkedTarget = /** @type {HTMLElement} */(target);\n      break;\n    }\n  }\n  return $linkedTarget;\n};\n\nlet scrollerIndex = 0;\n\nconst debugColors = ['#FF4B4B','#FF971B','#FFC730','#F9F640','#7AFF5A','#18FF74','#17E09B','#3CFFEC','#05DBE9','#33B3F1','#638CF9','#C563FE','#FF4FCF','#F93F8A'];\n\n/**\n * @typedef {Object} ScrollThresholdParam\n * @property {ScrollThresholdValue} [target]\n * @property {ScrollThresholdValue} [container]\n */\n\n/**\n * @callback ScrollObserverAxisCallback\n * @param {ScrollObserver} self\n * @return {'x'|'y'}\n */\n\n/**\n * @callback ScrollThresholdCallback\n * @param {ScrollObserver} self\n * @return {ScrollThresholdValue|ScrollThresholdParam}\n */\n\n/**\n * @typedef {Object} ScrollObserverParams\n * @property {Number|String} [id]\n * @property {Boolean|Number|String|EasingParam} [sync]\n * @property {TargetsParam} [container]\n * @property {TargetsParam} [target]\n * @property {'x'|'y'|ScrollObserverAxisCallback|((observer: ScrollObserver) => 'x'|'y'|ScrollObserverAxisCallback)} [axis]\n * @property {ScrollThresholdValue|ScrollThresholdParam|ScrollThresholdCallback|((observer: ScrollObserver) => ScrollThresholdValue|ScrollThresholdParam|ScrollThresholdCallback)} [enter]\n * @property {ScrollThresholdValue|ScrollThresholdParam|ScrollThresholdCallback|((observer: ScrollObserver) => ScrollThresholdValue|ScrollThresholdParam|ScrollThresholdCallback)} [leave]\n * @property {Boolean|((observer: ScrollObserver) => Boolean)} [repeat]\n * @property {Boolean} [debug]\n * @property {Callback<ScrollObserver>} [onEnter]\n * @property {Callback<ScrollObserver>} [onLeave]\n * @property {Callback<ScrollObserver>} [onEnterForward]\n * @property {Callback<ScrollObserver>} [onLeaveForward]\n * @property {Callback<ScrollObserver>} [onEnterBackward]\n * @property {Callback<ScrollObserver>} [onLeaveBackward]\n * @property {Callback<ScrollObserver>} [onUpdate]\n * @property {Callback<ScrollObserver>} [onSyncComplete]\n */\n\nclass ScrollObserver {\n  /**\n   * @param {ScrollObserverParams} parameters\n   */\n  constructor(parameters = {}) {\n    if (globals.scope) globals.scope.revertibles.push(this);\n    const syncMode = setValue(parameters.sync, 'play pause');\n    const ease = syncMode ? parseEasings(/** @type {EasingParam} */(syncMode)) : null;\n    const isLinear = syncMode && (syncMode === 'linear' || syncMode === none);\n    const isEase = syncMode && !(ease === none && !isLinear);\n    const isSmooth = syncMode && (isNum(syncMode) || syncMode === true || isLinear);\n    const isMethods = syncMode && (isStr(syncMode) && !isEase && !isSmooth);\n    const syncMethods = isMethods ? /** @type {String} */(syncMode).split(' ').map(\n      (/** @type {String} */m) => () => {\n        const linked = this.linked;\n        return linked && linked[m] ? linked[m]() : null;\n      }\n    ) : null;\n    const biDirSync = isMethods && syncMethods.length > 2;\n    /** @type {Number} */\n    this.index = scrollerIndex++;\n    /** @type {String|Number} */\n    this.id = !isUnd(parameters.id) ? parameters.id : this.index;\n    /** @type {ScrollContainer} */\n    this.container = registerAndGetScrollContainer(parameters.container);\n    /** @type {HTMLElement} */\n    this.target = null;\n    /** @type {Tickable|WAAPIAnimation} */\n    this.linked = null;\n    /** @type {Boolean} */\n    this.repeat = null;\n    /** @type {Boolean} */\n    this.horizontal = null;\n    /** @type {ScrollThresholdParam|ScrollThresholdValue|ScrollThresholdCallback} */\n    this.enter = null;\n    /** @type {ScrollThresholdParam|ScrollThresholdValue|ScrollThresholdCallback} */\n    this.leave = null;\n    /** @type {Boolean} */\n    this.sync = isEase || isSmooth || !!syncMethods;\n    /** @type {EasingFunction} */\n    this.syncEase = isEase ? ease : null;\n    /** @type {Number} */\n    this.syncSmooth = isSmooth ? syncMode === true || isLinear ? 1 : /** @type {Number} */(syncMode) : null;\n    /** @type {Callback<ScrollObserver>} */\n    this.onSyncEnter = syncMethods && !biDirSync && syncMethods[0] ? syncMethods[0] : noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onSyncLeave = syncMethods && !biDirSync && syncMethods[1] ? syncMethods[1] : noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onSyncEnterForward = syncMethods && biDirSync && syncMethods[0] ? syncMethods[0] : noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onSyncLeaveForward = syncMethods && biDirSync && syncMethods[1] ? syncMethods[1] : noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onSyncEnterBackward = syncMethods && biDirSync && syncMethods[2] ? syncMethods[2] : noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onSyncLeaveBackward = syncMethods && biDirSync && syncMethods[3] ? syncMethods[3] : noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onEnter = parameters.onEnter || noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onLeave = parameters.onLeave || noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onEnterForward = parameters.onEnterForward || noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onLeaveForward = parameters.onLeaveForward || noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onEnterBackward = parameters.onEnterBackward || noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onLeaveBackward = parameters.onLeaveBackward || noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onUpdate = parameters.onUpdate || noop;\n    /** @type {Callback<ScrollObserver>} */\n    this.onSyncComplete = parameters.onSyncComplete || noop;\n    /** @type {Boolean} */\n    this.reverted = false;\n    /** @type {Boolean} */\n    this.completed = false;\n    /** @type {Boolean} */\n    this.began = false;\n    /** @type {Boolean} */\n    this.isInView = false;\n    /** @type {Boolean} */\n    this.forceEnter = false;\n    /** @type {Boolean} */\n    this.hasEntered = false;\n    /** @type {Array.<Number>} */\n    this.offsets = [];\n    /** @type {Number} */\n    this.offset = 0;\n    /** @type {Number} */\n    this.offsetStart = 0;\n    /** @type {Number} */\n    this.offsetEnd = 0;\n    /** @type {Number} */\n    this.distance = 0;\n    /** @type {Number} */\n    this.prevProgress = 0;\n    /** @type {Array} */\n    this.thresholds = ['start', 'end', 'end', 'start'];\n    /** @type {[Number, Number, Number, Number]} */\n    this.coords = [0, 0, 0, 0];\n    /** @type {JSAnimation} */\n    this.debugStyles = null;\n    /** @type {HTMLElement} */\n    this.$debug = null;\n    /** @type {ScrollObserverParams} */\n    this._params = parameters;\n    /** @type {Boolean} */\n    this._debug = setValue(parameters.debug, false);\n    /** @type {ScrollObserver} */\n    this._next = null;\n    /** @type {ScrollObserver} */\n    this._prev = null;\n    addChild(this.container, this);\n    // Wait for the next frame to add to the container in order to handle calls to link()\n    sync(() => {\n      if (this.reverted) return;\n      if (!this.target) {\n        const target = /** @type {HTMLElement} */(parseTargets(parameters.target)[0]);\n        this.target = target || doc.body;\n        this.refresh();\n      }\n      if (this._debug) this.debug();\n    });\n  }\n\n  /**\n   * @param {Tickable|WAAPIAnimation} linked\n   */\n  link(linked) {\n    if (linked) {\n      // Make sure to pause the linked object in case it's added later\n      linked.pause();\n      this.linked = linked;\n      // Try to use a target of the linked object if no target parameters specified\n      if (!this._params.target) {\n        /** @type {HTMLElement} */\n        let $linkedTarget;\n        if (!isUnd(/** @type {JSAnimation} */(linked).targets)) {\n          $linkedTarget = getAnimationDomTarget(/** @type {JSAnimation} */(linked));\n        } else {\n          forEachChildren(/** @type {Timeline} */(linked), (/** @type {JSAnimation} */child) => {\n            if (child.targets && !$linkedTarget) {\n              $linkedTarget = getAnimationDomTarget(/** @type {JSAnimation} */(child));\n            }\n          });\n        }\n        // Fallback to body if no target found\n        this.target = $linkedTarget || doc.body;\n        this.refresh();\n      }\n    }\n    return this;\n  }\n\n  get velocity() {\n    return this.container.velocity;\n  }\n\n  get backward() {\n    return this.horizontal ? this.container.backwardX : this.container.backwardY;\n  }\n\n  get scroll() {\n    return this.horizontal ? this.container.scrollX : this.container.scrollY;\n  }\n\n  get progress() {\n    const p = (this.scroll - this.offsetStart) / this.distance;\n    return p === Infinity || isNaN(p) ? 0 : round(clamp(p, 0, 1), 6);\n  }\n\n  refresh() {\n    this.reverted = false;\n    const params = this._params;\n    this.repeat = setValue(parseScrollObserverFunctionParameter(params.repeat, this), true);\n    this.horizontal = setValue(parseScrollObserverFunctionParameter(params.axis, this), 'y') === 'x';\n    this.enter = setValue(parseScrollObserverFunctionParameter(params.enter, this), 'end start');\n    this.leave = setValue(parseScrollObserverFunctionParameter(params.leave, this), 'start end');\n    this.updateBounds();\n    this.handleScroll();\n    return this;\n  }\n\n  removeDebug() {\n    if (this.$debug) {\n      this.$debug.parentNode.removeChild(this.$debug);\n      this.$debug = null;\n    }\n    if (this.debugStyles) {\n      this.debugStyles.revert();\n      this.$debug = null;\n    }\n    return this;\n  }\n\n  debug() {\n    this.removeDebug();\n    const container = this.container;\n    const isHori = this.horizontal;\n    const $existingDebug = container.element.querySelector(':scope > .animejs-onscroll-debug');\n    const $debug = doc.createElement('div');\n    const $thresholds = doc.createElement('div');\n    const $triggers = doc.createElement('div');\n    const color = debugColors[this.index % debugColors.length];\n    const useWin = container.useWin;\n    const containerWidth = useWin ? container.winWidth : container.width;\n    const containerHeight = useWin ? container.winHeight : container.height;\n    const scrollWidth = container.scrollWidth;\n    const scrollHeight = container.scrollHeight;\n    const size = this.container.width > 360 ? 320 : 260;\n    const offLeft = isHori ? 0 : 10;\n    const offTop = isHori ? 10 : 0;\n    const half = isHori ? 24 : size / 2;\n    const labelHeight = isHori ? half : 15;\n    const labelWidth = isHori ? 60 : half;\n    const labelSize = isHori ? labelWidth : labelHeight;\n    const repeat = isHori ? 'repeat-x' : 'repeat-y';\n    /**\n     * @param {Number} v\n     * @return {String}\n     */\n    const gradientOffset = v => isHori ? '0px '+(v)+'px' : (v)+'px'+' 2px';\n    /**\n     * @param {String} c\n     * @return {String}\n     */\n    const lineCSS = (c) => `linear-gradient(${isHori ? 90 : 0}deg, ${c} 2px, transparent 1px)`;\n    /**\n     * @param {String} p\n     * @param {Number} l\n     * @param {Number} t\n     * @param {Number} w\n     * @param {Number} h\n     * @return {String}\n     */\n    const baseCSS = (p, l, t, w, h) => `position:${p};left:${l}px;top:${t}px;width:${w}px;height:${h}px;`;\n    $debug.style.cssText = `${baseCSS('absolute', offLeft, offTop, isHori ? scrollWidth : size, isHori ? size : scrollHeight)}\n      pointer-events: none;\n      z-index: ${this.container.zIndex++};\n      display: flex;\n      flex-direction: ${isHori ? 'column' : 'row'};\n      filter: drop-shadow(0px 1px 0px rgba(0,0,0,.75));\n    `;\n    $thresholds.style.cssText = `${baseCSS('sticky', 0, 0, isHori ? containerWidth : half, isHori ? half : containerHeight)}`;\n    if (!$existingDebug) {\n      $thresholds.style.cssText += `background:\n        ${lineCSS('#FFFF')}${gradientOffset(half-10)} / ${isHori ? '100px 100px' : '100px 100px'} ${repeat},\n        ${lineCSS('#FFF8')}${gradientOffset(half-10)} / ${isHori ? '10px 10px' : '10px 10px'} ${repeat};\n      `;\n    }\n    $triggers.style.cssText = `${baseCSS('relative', 0, 0, isHori ? scrollWidth : half, isHori ? half : scrollHeight)}`;\n    if (!$existingDebug) {\n      $triggers.style.cssText += `background:\n        ${lineCSS('#FFFF')}${gradientOffset(0)} / ${isHori ? '100px 10px' : '10px 100px'} ${repeat},\n        ${lineCSS('#FFF8')}${gradientOffset(0)} / ${isHori ? '10px 0px' : '0px 10px'} ${repeat};\n      `;\n    }\n    const labels = [' enter: ', ' leave: '];\n    this.coords.forEach((v, i) => {\n      const isView = i > 1;\n      const value = (isView ? 0 : this.offset) + v;\n      const isTail = i % 2;\n      const isFirst = value < labelSize;\n      const isOver = value > (isView ? isHori ? containerWidth : containerHeight : isHori ? scrollWidth : scrollHeight) - labelSize;\n      const isFlip = (isView ? isTail && !isFirst : !isTail && !isFirst) || isOver;\n      const $label = doc.createElement('div');\n      const $text = doc.createElement('div');\n      const dirProp = isHori ? isFlip ? 'right' : 'left' : isFlip ? 'bottom' : 'top';\n      const flipOffset = isFlip ? (isHori ? labelWidth : labelHeight) + (!isView ? isHori ? -1 : -2 : isHori ? -1 : isOver ? 0 : -2) : !isView ? isHori ? 1 : 0 : isHori ? 1 : 0;\n      // $text.innerHTML = `${!isView ? '' : labels[isTail] + ' '}${this.id}: ${this.thresholds[i]} ${isView ? '' : labels[isTail]}`;\n      $text.innerHTML = `${this.id}${labels[isTail]}${this.thresholds[i]}`;\n      $label.style.cssText = `${baseCSS('absolute', 0, 0, labelWidth, labelHeight)}\n        display: flex;\n        flex-direction: ${isHori ? 'column' : 'row'};\n        justify-content: flex-${isView ? 'start' : 'end'};\n        align-items: flex-${isFlip ? 'end' : 'start'};\n        border-${dirProp}: 2px ${isTail ? 'solid' : 'solid'} ${color};\n      `;\n      $text.style.cssText = `\n        overflow: hidden;\n        max-width: ${(size / 2) - 10}px;\n        height: ${labelHeight};\n        margin-${isHori ? isFlip ? 'right' : 'left' : isFlip ? 'bottom' : 'top'}: -2px;\n        padding: 1px;\n        font-family: ui-monospace, monospace;\n        font-size: 10px;\n        letter-spacing: -.025em;\n        line-height: 9px;\n        font-weight: 600;\n        text-align: ${isHori && isFlip || !isHori && !isView ? 'right' : 'left'};\n        white-space: pre;\n        text-overflow: ellipsis;\n        color: ${isTail ? color : 'rgba(0,0,0,.75)'};\n        background-color: ${isTail ? 'rgba(0,0,0,.65)' : color};\n        border: 2px solid ${isTail ? color : 'transparent'};\n        border-${isHori ? isFlip ? 'top-left' : 'top-right' : isFlip ? 'top-left' : 'bottom-left'}-radius: 5px;\n        border-${isHori ? isFlip ? 'bottom-left' : 'bottom-right' : isFlip ? 'top-right' : 'bottom-right'}-radius: 5px;\n      `;\n      $label.appendChild($text);\n      let position = value - flipOffset + (isHori ? 1 : 0);\n      $label.style[isHori ? 'left' : 'top'] = `${position}px`;\n      // $label.style[isHori ? 'left' : 'top'] = value - flipOffset + (!isFlip && isFirst && !isView ? 1 : isFlip ? 0 : -2) + 'px';\n      (isView ? $thresholds : $triggers).appendChild($label);\n    });\n\n    $debug.appendChild($thresholds);\n    $debug.appendChild($triggers);\n    container.element.appendChild($debug);\n\n    if (!$existingDebug) $debug.classList.add('animejs-onscroll-debug');\n    this.$debug = $debug;\n    const containerPosition = getTargetValue(container.element, 'position');\n    if (containerPosition === 'static') {\n      this.debugStyles = setTargetValues(container.element, { position: 'relative '});\n    }\n\n  }\n\n  updateBounds() {\n    if (this._debug) {\n      this.removeDebug();\n    }\n    let stickys;\n    const $target = this.target;\n    const container = this.container;\n    const isHori = this.horizontal;\n    const linked = this.linked;\n    let linkedTime;\n    let $el = $target;\n    let offsetX = 0;\n    let offsetY = 0;\n    /** @type {Element} */\n    let $offsetParent = $el;\n    if (linked) {\n      linkedTime = linked.currentTime;\n      linked.seek(0, true);\n    }\n    const isContainerStatic = getTargetValue(container.element, 'position') === 'static' ? setTargetValues(container.element, { position: 'relative '}) : false;\n    while ($el && $el !== container.element && $el !== doc.body) {\n      const isSticky = getTargetValue($el, 'position') === 'sticky' ?\n                       setTargetValues($el, { position: 'static' }) :\n                       false;\n      if ($el === $offsetParent) {\n        offsetX += $el.offsetLeft || 0;\n        offsetY += $el.offsetTop || 0;\n        $offsetParent = $el.offsetParent;\n      }\n      $el = /** @type {HTMLElement} */($el.parentElement);\n      if (isSticky) {\n        if (!stickys) stickys = [];\n        stickys.push(isSticky);\n      }\n    }\n    if (isContainerStatic) isContainerStatic.revert();\n    const offset = isHori ? offsetX : offsetY;\n    const targetSize = isHori ? $target.offsetWidth : $target.offsetHeight;\n    const containerSize = isHori ? container.width : container.height;\n    const scrollSize = isHori ? container.scrollWidth : container.scrollHeight;\n    const maxScroll = scrollSize - containerSize;\n    const enter = this.enter;\n    const leave = this.leave;\n\n    /** @type {ScrollThresholdValue} */\n    let enterTarget = 'start';\n    /** @type {ScrollThresholdValue} */\n    let leaveTarget = 'end';\n    /** @type {ScrollThresholdValue} */\n    let enterContainer = 'end';\n    /** @type {ScrollThresholdValue} */\n    let leaveContainer = 'start';\n\n    if (isStr(enter)) {\n      const splitted = /** @type {String} */(enter).split(' ');\n      enterContainer = splitted[0];\n      enterTarget = splitted.length > 1 ? splitted[1] : enterTarget;\n    } else if (isObj(enter)) {\n      const e = /** @type {ScrollThresholdParam} */(enter);\n      if (!isUnd(e.container)) enterContainer = e.container;\n      if (!isUnd(e.target)) enterTarget = e.target;\n    } else if (isNum(enter)) {\n      enterContainer = /** @type {Number} */(enter);\n    }\n\n    if (isStr(leave)) {\n      const splitted = /** @type {String} */(leave).split(' ');\n      leaveContainer = splitted[0];\n      leaveTarget = splitted.length > 1 ? splitted[1] : leaveTarget;\n    } else if (isObj(leave)) {\n      const t = /** @type {ScrollThresholdParam} */(leave);\n      if (!isUnd(t.container)) leaveContainer = t.container;\n      if (!isUnd(t.target)) leaveTarget = t.target;\n    } else if (isNum(leave)) {\n      leaveContainer = /** @type {Number} */(leave);\n    }\n\n    const parsedEnterTarget = parseBoundValue($target, enterTarget, targetSize);\n    const parsedLeaveTarget = parseBoundValue($target, leaveTarget, targetSize);\n    const under = (parsedEnterTarget + offset) - containerSize;\n    const over = (parsedLeaveTarget + offset) - maxScroll;\n    const parsedEnterContainer = parseBoundValue($target, enterContainer, containerSize, under, over);\n    const parsedLeaveContainer = parseBoundValue($target, leaveContainer, containerSize, under, over);\n    const offsetStart = parsedEnterTarget + offset - parsedEnterContainer;\n    const offsetEnd = parsedLeaveTarget + offset - parsedLeaveContainer;\n    const scrollDelta = offsetEnd - offsetStart;\n    this.offsets[0] = offsetX;\n    this.offsets[1] = offsetY;\n    this.offset = offset;\n    this.offsetStart = offsetStart;\n    this.offsetEnd = offsetEnd;\n    this.distance = scrollDelta <= 0 ? 0 : scrollDelta;\n    this.thresholds = [enterTarget, leaveTarget, enterContainer, leaveContainer];\n    this.coords = [parsedEnterTarget, parsedLeaveTarget, parsedEnterContainer, parsedLeaveContainer];\n    if (stickys) {\n      stickys.forEach(sticky => sticky.revert());\n    }\n    if (linked) {\n      linked.seek(linkedTime, true);\n    }\n    if (this._debug) {\n      this.debug();\n    }\n  }\n\n  handleScroll() {\n    const linked = this.linked;\n    const sync = this.sync;\n    const syncEase = this.syncEase;\n    const syncSmooth = this.syncSmooth;\n    const shouldSeek = linked && (syncEase || syncSmooth);\n    const isHori = this.horizontal;\n    const container = this.container;\n    const scroll = this.scroll;\n    const isBefore = scroll <= this.offsetStart;\n    const isAfter = scroll >= this.offsetEnd;\n    const isInView = !isBefore && !isAfter;\n    const isOnTheEdge = scroll === this.offsetStart || scroll === this.offsetEnd;\n    const forceEnter = !this.hasEntered && isOnTheEdge;\n    const $debug = this._debug && this.$debug;\n    let hasUpdated = false;\n    let syncCompleted = false;\n    let p = this.progress;\n\n    if (isBefore && this.began) {\n      this.began = false;\n    }\n\n    if (p > 0 && !this.began) {\n      this.began = true;\n    }\n\n    if (shouldSeek) {\n      const lp = linked.progress;\n      if (syncSmooth && isNum(syncSmooth)) {\n        if (/** @type {Number} */(syncSmooth) < 1) {\n          const step = 0.0001;\n          const snap = lp < p && p === 1 ? step : lp > p && !p ? -1e-4 : 0;\n          p = round(lerp(lp, p, interpolate(.01, .2, /** @type {Number} */(syncSmooth)), false) + snap, 6);\n        }\n      } else if (syncEase) {\n        p = syncEase(p);\n      }\n      hasUpdated = p !== this.prevProgress;\n      syncCompleted = lp === 1;\n      if (hasUpdated && !syncCompleted && (syncSmooth && lp)) {\n        container.wakeTicker.restart();\n      }\n    }\n\n    if ($debug) {\n      const sticky = isHori ? container.scrollY : container.scrollX;\n      $debug.style[isHori ? 'top' : 'left'] = sticky + 10 + 'px';\n    }\n\n    // Trigger enter callbacks if already in view or when entering the view\n    if ((isInView && !this.isInView) || (forceEnter && !this.forceEnter && !this.hasEntered)) {\n      if (isInView) this.isInView = true;\n      if (!this.forceEnter || !this.hasEntered) {\n        if ($debug && isInView) $debug.style.zIndex = `${this.container.zIndex++}`;\n        this.onSyncEnter(this);\n        this.onEnter(this);\n        if (this.backward) {\n          this.onSyncEnterBackward(this);\n          this.onEnterBackward(this);\n        } else {\n          this.onSyncEnterForward(this);\n          this.onEnterForward(this);\n        }\n        this.hasEntered = true;\n        if (forceEnter) this.forceEnter = true;\n      } else if (isInView) {\n        this.forceEnter = false;\n      }\n    }\n\n    if (isInView || !isInView && this.isInView) {\n      hasUpdated = true;\n    }\n\n    if (hasUpdated) {\n      if (shouldSeek) linked.seek(linked.duration * p);\n      this.onUpdate(this);\n    }\n\n    if (!isInView && this.isInView) {\n      this.isInView = false;\n      this.onSyncLeave(this);\n      this.onLeave(this);\n      if (this.backward) {\n        this.onSyncLeaveBackward(this);\n        this.onLeaveBackward(this);\n      } else {\n        this.onSyncLeaveForward(this);\n        this.onLeaveForward(this);\n      }\n      if (sync && !syncSmooth) {\n        syncCompleted = true;\n      }\n    }\n\n    if (p >= 1 && this.began && !this.completed && (sync && syncCompleted || !sync)) {\n      if (sync) {\n        this.onSyncComplete(this);\n      }\n      this.completed = true;\n      if ((!this.repeat && !linked) || (!this.repeat && linked && linked.completed)) {\n        this.revert();\n      }\n    }\n\n    if (p < 1 && this.completed) {\n      this.completed = false;\n    }\n\n    this.prevProgress = p;\n  }\n\n  revert() {\n    if (this.reverted) return;\n    const container = this.container;\n    removeChild(container, this);\n    if (!container._head) {\n      container.revert();\n    }\n    if (this._debug) {\n      this.removeDebug();\n    }\n    this.reverted = true;\n    return this;\n  }\n\n}\n\n/**\n * @param {ScrollObserverParams} [parameters={}]\n * @return {ScrollObserver}\n */\nconst onScroll = (parameters = {}) => new ScrollObserver(parameters);\n\n\n\n\n/**\n * @typedef  {Object} StaggerParameters\n * @property {Number|String} [start]\n * @property {Number|'first'|'center'|'last'} [from]\n * @property {Boolean} [reversed]\n * @property {Array.<Number>} [grid]\n * @property {('x'|'y')} [axis]\n * @property {EasingParam} [ease]\n * @property {TweenModifier} [modifier]\n */\n\n/**\n * @callback StaggerFunction\n * @param {Target} [target]\n * @param {Number} [index]\n * @param {Number} [length]\n * @param {Timeline} [tl]\n * @return {Number|String}\n */\n\n/**\n * @param  {Number|String|[Number|String,Number|String]} val\n * @param  {StaggerParameters} params\n * @return {StaggerFunction}\n */\nconst stagger = (val, params = {}) => {\n  let values = [];\n  let maxValue = 0;\n  const from = params.from;\n  const reversed = params.reversed;\n  const ease = params.ease;\n  const hasEasing = !isUnd(ease);\n  const hasSpring = hasEasing && !isUnd(/** @type {Spring} */(ease).ease);\n  const staggerEase = hasSpring ? /** @type {Spring} */(ease).ease : hasEasing ? parseEasings(ease) : null;\n  const grid = params.grid;\n  const axis = params.axis;\n  const fromFirst = isUnd(from) || from === 0 || from === 'first';\n  const fromCenter = from === 'center';\n  const fromLast = from === 'last';\n  const isRange = isArr(val);\n  const val1 = isRange ? parseNumber(val[0]) : parseNumber(val);\n  const val2 = isRange ? parseNumber(val[1]) : 0;\n  const unitMatch = unitsExecRgx.exec((isRange ? val[1] : val) + emptyString);\n  const start = params.start || 0 + (isRange ? val1 : 0);\n  let fromIndex = fromFirst ? 0 : isNum(from) ? from : 0;\n  return (_, i, t, tl) => {\n    if (fromCenter) fromIndex = (t - 1) / 2;\n    if (fromLast) fromIndex = t - 1;\n    if (!values.length) {\n      for (let index = 0; index < t; index++) {\n        if (!grid) {\n          values.push(abs(fromIndex - index));\n        } else {\n          const fromX = !fromCenter ? fromIndex % grid[0] : (grid[0] - 1) / 2;\n          const fromY = !fromCenter ? floor(fromIndex / grid[0]) : (grid[1] - 1) / 2;\n          const toX = index % grid[0];\n          const toY = floor(index / grid[0]);\n          const distanceX = fromX - toX;\n          const distanceY = fromY - toY;\n          let value = sqrt(distanceX * distanceX + distanceY * distanceY);\n          if (axis === 'x') value = -distanceX;\n          if (axis === 'y') value = -distanceY;\n          values.push(value);\n        }\n        maxValue = max(...values);\n      }\n      if (staggerEase) values = values.map(val => staggerEase(val / maxValue) * maxValue);\n      if (reversed) values = values.map(val => axis ? (val < 0) ? val * -1 : -val : abs(maxValue - val));\n    }\n    const spacing = isRange ? (val2 - val1) / maxValue : val1;\n    const offset = tl ? parseTimelinePosition(tl, isUnd(params.start) ? tl.iterationDuration : start) : /** @type {Number} */(start);\n    /** @type {String|Number} */\n    let output = offset + ((spacing * round(values[i], 2)) || 0);\n    if (params.modifier) output = params.modifier(output);\n    if (unitMatch) output = `${output}${unitMatch[2]}`;\n    return output;\n  }\n};\n\nexport { Animatable, Draggable, JSAnimation, Scope, ScrollObserver, Spring, Timeline, Timer, WAAPIAnimation, animate, createAnimatable, createDraggable, createScope, createSpring, createTimeline, createTimer, eases, engine, onScroll, scrollContainers, stagger, svg, utils, waapi };\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GAED,+CAA+C,GAC/C,yCAAyC,GACzC,2DAA2D,GAC3D,6EAA6E,GAE7E;;;;;;CAMC,GAED;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+BC,GAED;;;;;CAKC,GAED;;;;CAIC,GAED;;CAEC,GAED,kEAAkE;AAClE,8EAA8E;AAC9E,oFAAoF,GAEpF,gDAAgD,GAChD,4CAA4C,GAC5C,yCAAyC,GACzC,qDAAqD,GACrD,2DAA2D,GAC3D,2EAA2E,GAC3E,iDAAiD,GACjD,wDAAwD,GACxD,+CAA+C,GAC/C,kEAAkE,GAClE,2CAA2C,GAE3C;;;;;;CAMC,GAED;;;;CAIC,GAED,2DAA2D,GAE3D;;;;;;CAMC,GAED;;;;;;;;;CASC,GAED;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC,GAED;;;;;;;;CAQC,GAED,4EAA4E,GAC5E,kEAAkE,GAClE,kEAAkE,GAClE,+DAA+D,GAE/D;;;;;;;;;;;;CAYC,GAED;;;;CAIC,GAED;;CAEC,GAED;;CAEC,GAED;;CAEC,GAED;;;;;;;CAOC,GAED;;;;;CAKC,GAED;;CAEC,GAED;;CAEC,GAED;;CAEC,GAED;;CAEC,GAED;;;CAGC,GAED;;CAEC,GAED;;CAEC,GAED;;CAEC,GAED;;;;CAIC,GAED,+IAA+I;AAC/I;;CAEC,GAED;;;;CAIC,GAED;;CAEC,GAED;;;;;;CAMC,GAED;;;CAGC,GAED;;CAEC,GAED;;CAEC,GAED;;;;;;;CAOC,GAED;;CAEC,GAGD,eAAe;AAEf,+DAA+D;;;;;;;;;;;;;;;;;;;;;;;;;;;AAC/D,MAAM,YAAY,OAAO,WAAW;AAEpC,wBAAwB,GACxB,MAAM,MAAM,YAAY,SAAS;AAEjC,qBAAqB,GACrB,MAAM,MAAM,YAAY,WAAW;AAEnC,QAAQ;AAER,mBAAmB,GACnB,MAAM,aAAa;IACjB,QAAQ;IACR,WAAW;IACX,KAAK;IACL,WAAW;IACX,SAAS;AACX;AAEA,mBAAmB,GACnB,MAAM,aAAa;IACjB,QAAQ;IACR,MAAM;IACN,OAAO;IACP,SAAS;AACX;AAEA,mBAAmB,GACnB,MAAM,YAAY;IAChB,MAAM;IACN,MAAM;IACN,OAAO;AACT;AAEA,mBAAmB,GACnB,MAAM,mBAAmB;IACvB,SAAS;IACT,MAAM;IACN,OAAO;AACT;AAEA,gBAAgB;AAEhB,MAAM,2BAA2B;AACjC,MAAM,cAAc;AACpB,MAAM,cAAc;AACpB,MAAM,mBAAmB;AACzB,MAAM,oBAAoB;AAC1B,MAAM,oBAAoB;AAE1B,UAAU;AAEV,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,IAAI;AACV,MAAM,SAAS;AAEf,UAAU;AAEV,MAAM,cAAc;AACpB,MAAM,kBAAkB,IAAI;AAE5B,gBAAgB,GAAG,CAAC,KAAK;AACzB,gBAAgB,GAAG,CAAC,KAAK;AACzB,gBAAgB,GAAG,CAAC,KAAK;AAEzB,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,4BAA4B,gBAAgB,MAAM,CAAC,CAAC,GAAG,IAAM,CAAC;QAAC,GAAG,CAAC;QAAE,CAAC,EAAE,EAAE,IAAI;IAAG,CAAC,GAAG,CAAC;AAE5F,YAAY;AAEZ,mBAAmB,GACnB,MAAM,OAAO,KAAO;AAEpB,QAAQ;AAER,MAAM,aAAa;AACnB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,0EAA0E;AAC1E,MAAM,uBAAuB;AAC7B,iFAAiF;AACjF,MAAM,eAAe;AACrB,MAAM,eAAe;AACrB,MAAM,oBAAoB,sBAAsB,8EAA8E;AAC9H,MAAM,wBAAwB;AAK9B,2BAA2B,GAC3B,MAAM,WAAW;IACf,IAAI;IACJ,WAAW;IACX,cAAc;IACd,cAAc;IACd,WAAW;IACX,MAAM;IACN,UAAU;IACV,WAAW;IACX,UAAU;IACV,UAAU;IACV,OAAO;IACP,WAAW;IACX,MAAM;IACN,aAAa,iBAAiB,OAAO;IACrC,UAAU,CAAA,IAAK;IACf,SAAS;IACT,gBAAgB;IAChB,UAAU;IACV,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,UAAU;AACZ;AAEA,MAAM,UAAU;IACd,2BAA2B,GAC3B;IACA,+BAA+B,GAC/B,MAAM;IACN,kBAAkB,GAClB,OAAO;IACP,mBAAmB,GACnB,WAAW;IACX,mBAAmB,GACnB,WAAW;IACX,mBAAmB,GACnB,eAAe;AACjB;AAEA,MAAM,iBAAiB;IAAE,SAAS;IAAS,QAAQ;AAAK;AAExD,IAAI,WAAW;IACb,IAAI,CAAC,IAAI,OAAO,EAAE,IAAI,OAAO,GAAG,EAAE;IAClC,IAAI,OAAO,CAAC,IAAI,CAAC;AACnB;AAEA,UAAU;AAEV;;;CAGC,GACD,MAAM,cAAc,CAAA,MAAO,IAAI,OAAO,CAAC,cAAc,SAAS,WAAW;AAEzE;;;;;CAKC,GACD,MAAM,mBAAmB,CAAC,KAAK,MAAQ,IAAI,OAAO,CAAC,SAAS;AAE5D,OAAO;AACP,qKAAqK;AACrK,MAAM,MAAM,KAAK,GAAG;AAEpB,iBAAiB;AAEjB,MAAM,QAAQ,MAAM,OAAO;AAC3B,qDAAqD,GACrD,MAAM,QAAQ,CAAA,IAAK,KAAK,EAAE,WAAW,KAAK;AAC1C,wCAAwC,GACxC,MAAM,QAAQ,CAAA,IAAK,OAAO,MAAM,YAAY,CAAC,MAAM;AACnD,wCAAwC,GACxC,MAAM,QAAQ,CAAA,IAAK,OAAO,MAAM;AAChC,0CAA0C,GAC1C,MAAM,QAAQ,CAAA,IAAK,OAAO,MAAM;AAChC,2CAA2C,GAC3C,MAAM,QAAQ,CAAA,IAAK,OAAO,MAAM;AAChC,kDAAkD,GAClD,MAAM,QAAQ,CAAA,IAAK,MAAM,MAAM,MAAM;AACrC,4CAA4C,GAC5C,MAAM,QAAQ,CAAA,IAAK,aAAa,aAAa;AAC7C,oCAAoC,GACpC,MAAM,QAAQ,CAAA,IAAK,WAAW,IAAI,CAAC;AACnC,oCAAoC,GACpC,MAAM,QAAQ,CAAA,IAAK,iBAAiB,GAAG;AACvC,oCAAoC,GACpC,MAAM,QAAQ,CAAA,IAAK,iBAAiB,GAAG;AACvC,oCAAoC,GACpC,MAAM,QAAQ,CAAA,IAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACjD,oCAAoC,GACpC,MAAM,QAAQ,CAAA,IAAK,CAAC,QAAQ,QAAQ,CAAC,cAAc,CAAC;AAEpD,SAAS;AAET;;;CAGC,GACD,MAAM,cAAc,CAAA,MAAO,MAAM,OAC/B,WAAiC,OACX;AAExB,OAAO;AAEP,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,OAAO,KAAK,IAAI;AACtB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,OAAO,KAAK,IAAI;AACtB,MAAM,QAAQ,KAAK,KAAK;AACxB,MAAM,OAAO,KAAK,IAAI;AACtB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,QAAQ,KAAK,KAAK;AACxB,MAAM,KAAK,KAAK,EAAE;AAClB,MAAM,SAAS,KAAK,KAAK;AAEzB;;;;;CAKC,GACD,MAAM,QAAQ,CAAC,GAAG,KAAK,MAAQ,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM;AAE/D,MAAM,WAAW,CAAC;AAElB;;;;CAIC,GACD,MAAM,QAAQ,CAAC,GAAG;IAChB,IAAI,gBAAgB,GAAG,OAAO;IAC9B,IAAI,CAAC,eAAe,OAAO,OAAO;IAClC,IAAI,IAAI,QAAQ,CAAC,cAAc;IAC/B,IAAI,CAAC,GAAG,IAAI,QAAQ,CAAC,cAAc,GAAG,MAAM;IAC5C,OAAO,OAAO,IAAI,KAAK;AACzB;AAEA;;;;CAIC,GACD,MAAM,OAAO,CAAC,GAAG,YAAc,MAAM,aAAa,UAAU,MAAM,CAAC,CAAC,SAAS,KAAQ,IAAI,KAAK,KAAK,IAAI,UAAU,KAAK,KAAK,WAAY,YAAY,OAAO,IAAI,aAAa,YAAY;AAEvL;;;;;CAKC,GACD,MAAM,cAAc,CAAC,OAAO,KAAK,WAAa,QAAQ,CAAC,MAAM,KAAK,IAAI;AAEtE;;;CAGC,GACD,MAAM,gBAAgB,CAAA,IAAK,MAAM,WAAW,WAAW,MAAM,CAAC,WAAW,CAAC,OAAO;AAEjF;;;CAGC,GACD,MAAM,gBAAgB,CAAA,IAAK,KAAK,WAAW,WAAW,cAAc,MAAM,GAAG;AAE7E,SAAS;AAET;;;;CAIC,GACD,MAAM,aAAa,CAAA,IAAK,MAAM,KAAK;WAAK;KAAG,GAAG;AAE9C,UAAU;AAEV;;;;;;CAMC,GACD,MAAM,eAAe,CAAC,IAAI;IACxB,MAAM,SAA8B;QAAE,GAAG,EAAE;IAAC;IAC5C,IAAK,IAAI,KAAK,GAAI;QAChB,MAAM,MAAM,kBAAkB,GAAE,AAAC,EAAG,CAAC,EAAE;QACvC,MAAM,CAAC,EAAE,GAAG,MAAM,OAAO,kBAAkB,GAAE,AAAC,EAAG,CAAC,EAAE,GAAG;IACzD;IAAG,OAAO;AACZ;AAEA,eAAe;AAEf;;;;;;;CAOC,GACD,MAAM,kBAAkB,CAAC,QAAQ,UAAU,SAAS,WAAW,OAAO,EAAE,WAAW,OAAO;IACxF,IAAI,OAAO,OAAO,KAAK;IACvB,IAAI,mBAAmB;IACvB,IAAI,SAAS;QACX,OAAO,OAAO,KAAK;QACnB,mBAAmB;IACrB;IACA,MAAO,KAAM;QACX,MAAM,cAAc,IAAI,CAAC,iBAAiB;QAC1C,SAAS;QACT,OAAO;IACT;AACF;AAEA;;;;;;CAMC,GACD,MAAM,cAAc,CAAC,QAAQ,OAAO,WAAW,OAAO,EAAE,WAAW,OAAO;IACxE,MAAM,OAAO,KAAK,CAAC,SAAS;IAC5B,MAAM,OAAO,KAAK,CAAC,SAAS;IAC5B,OAAO,IAAI,CAAC,SAAS,GAAG,OAAO,OAAO,KAAK,GAAG;IAC9C,OAAO,IAAI,CAAC,SAAS,GAAG,OAAO,OAAO,KAAK,GAAG;IAC9C,KAAK,CAAC,SAAS,GAAG;IAClB,KAAK,CAAC,SAAS,GAAG;AACpB;AAEA;;;;;;;CAOC,GACD,MAAM,WAAW,CAAC,QAAQ,OAAO,YAAY,WAAW,OAAO,EAAE,WAAW,OAAO;IACjF,IAAI,OAAO,OAAO,KAAK;IACvB,MAAO,QAAQ,cAAc,WAAW,MAAM,OAAQ,OAAO,IAAI,CAAC,SAAS;IAC3E,MAAM,OAAO,OAAO,IAAI,CAAC,SAAS,GAAG,OAAO,KAAK;IACjD,OAAO,IAAI,CAAC,SAAS,GAAG,QAAQ,OAAO,KAAK,GAAG;IAC/C,OAAO,IAAI,CAAC,SAAS,GAAG,QAAQ,OAAO,KAAK,GAAG;IAC/C,KAAK,CAAC,SAAS,GAAG;IAClB,KAAK,CAAC,SAAS,GAAG;AACpB;AAEA;;;CAGC,GACD,MAAM;IAEJ,+BAA+B,GAC/B,YAAY,WAAW,CAAC,CAAE;QACxB,mBAAmB,GACnB,IAAI,CAAC,SAAS,GAAG;QACjB,mBAAmB,GACnB,IAAI,CAAC,YAAY,GAAG;QACpB,mBAAmB,GACnB,IAAI,CAAC,YAAY,GAAG;QACpB,mBAAmB,GACnB,IAAI,CAAC,UAAU,GAAG;QAClB,mBAAmB,GACnB,IAAI,CAAC,SAAS,GAAG;QACjB,mBAAmB,GACnB,IAAI,CAAC,cAAc,GAAG;QACtB,mBAAmB,GACnB,IAAI,CAAC,cAAc,GAAG,MAAM,IAAI,QAAQ;QACxC,mBAAmB,GACnB,IAAI,CAAC,IAAI,GAAG;QACZ,mBAAmB,GACnB,IAAI,CAAC,MAAM,GAAG;QACd,oBAAoB,GACpB,IAAI,CAAC,YAAY,GAAG;QACpB,2BAA2B,GAC3B,IAAI,CAAC,KAAK,GAAG;QACb,2BAA2B,GAC3B,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,IAAI;IAClB;IAEA,IAAI,IAAI,SAAS,EAAE;QACjB,MAAM,wBAAwB,IAAI,CAAC,cAAc;QACjD,MAAM,KAAK,CAAC;QACZ,MAAM,MAAM,KAAK,WAAW,WAAW;QACvC,MAAM,gBAAgB,MAAM,IAAI,KAAK;QACrC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,cAAc,IAAI,gBAAgB;IACzC;IAEA,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,IAAI,MAAM,YAAY,EAAE;QACtB,MAAM,MAAM,CAAC;QACb,IAAI,CAAC,MAAM,GAAG,MAAM,WAAW,WAAW;IAC5C;IAEA;;;GAGC,GACD,YAAY,IAAI,EAAE;QAChB,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,cAAc,IAAI,CAAC,YAAY;QACrC,IAAI,CAAC,YAAY,IAAK,OAAO;QAC7B,uDAAuD;QACvD,iEAAiE;QACjE,qBAAqB;QACrB,IAAI,cAAc,eAAe,OAAO,UAAU,IAAI;QACtD,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,aAAa,cAAc;QACjC,+EAA+E;QAC/E,oDAAoD;QACpD,IAAI,CAAC,cAAc,IAAI,aAAa,gBAAgB,gBAAgB;QACpE,OAAO,UAAU,IAAI;IACvB;IAEA;;;GAGC,GACD,iBAAiB,IAAI,EAAE;QACrB,MAAM,QAAQ,OAAO,IAAI,CAAC,SAAS;QACnC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO;IACT;AAEF;AAKA;;;;;;;CAOC,GACD,MAAM,SAAS,CAAC,UAAU,MAAM,eAAe,gBAAgB;IAE7D,MAAM,SAAS,SAAS,MAAM;IAC9B,MAAM,WAAW,SAAS,QAAQ;IAClC,MAAM,YAAY,SAAS,SAAS;IACpC,MAAM,oBAAoB,SAAS,iBAAiB;IACpD,MAAM,iBAAiB,SAAS,cAAc;IAC9C,MAAM,oBAAoB,SAAS,iBAAiB;IACpD,MAAM,aAAa,SAAS,UAAU;IACtC,MAAM,YAAY,SAAS,SAAS;IACpC,MAAM,aAAa,SAAS,UAAU;IACtC,MAAM,eAAe,SAAS,YAAY;IAC1C,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,2BAA2B,SAAS,YAAY,EAAE,sDAAsD;IAE9G,MAAM,kBAAkB,gBAAgB;IACxC,MAAM,uBAAuB,OAAO;IACpC,MAAM,mBAAmB,MAAM,0BAA0B,CAAC,eAAe;IACzE,MAAM,sBAAsB,MAAM,sBAAsB,CAAC,eAAe;IACxE,MAAM,YAAY,uBAAuB;IACzC,MAAM,yBAAyB,sBAAsB;IACrD,MAAM,oCAAoC,uBAAuB;IACjE,MAAM,WAAW,YAAY;IAC7B,MAAM,aAAa,aAAa,UAAU,KAAK;IAE/C,IAAI,QAAQ;IACZ,IAAI,uBAAuB;IAC3B,gBAAgB;IAChB,iHAAiH;IACjH,IAAI,cAAc;IAElB,sEAAsE;IACtE,IAAI,iBAAiB,GAAG;QACtB,sFAAsF;QACtF,MAAM,mBAAmB,CAAC,CAAC,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,oCAAoC,IAAI,UAAU,CAAC,CAAC;QAC5H,SAAS,iBAAiB,GAAG,MAAM,kBAAkB,GAAG;QACxD,oGAAoG;QACpG,IAAI,mCAAmC,SAAS,iBAAiB;QACjE,QAAQ,SAAS,iBAAiB,GAAG;QACrC,uBAAuB,sBAAsB,CAAC,oBAAoB,UAAU,KAAK;IACnF;IAEA,uEAAuE;IACvE,MAAM,aAAa,YAAY,CAAC,cAAc,KAAK;IACnD,MAAM,QAAQ,uBAAuB,GAAE,AAAC,SAAU,KAAK;IACvD,IAAI,gBAAgB,oCAAoC,aAAa,IAAI,WAAW,aAAa,oBAAoB,uBAAuB;IAC5I,IAAI,OAAO,gBAAgB,oBAAoB,MAAM,gBAAgB,sBAAsB;IAC3F,MAAM,qBAAqB,CAAC,SAAS,OAAO,SAAS,GAAG,uBAAuB,wBAAwB,IAAI,CAAC,aAAa,CAAC,CAAC;IAE3H,SAAS,YAAY,GAAG;IACxB,SAAS,cAAc,GAAG;IAC1B,SAAS,SAAS,GAAG;IAErB,IAAI,0BAA0B,CAAC,SAAS,KAAK,EAAE;QAC7C,SAAS,KAAK,GAAG;QACjB,IAAI,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,sBAAsB,CAAC,OAAO,KAAK,CAAC,GAAG;YACxE,SAAS,OAAO,CAAiC;QACnD;IACF,OAAO,IAAI,wBAAwB,GAAG;QACpC,SAAS,KAAK,GAAG;IACnB;IAEA,kHAAkH;IAClH,oGAAoG;IACpG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,0BAA0B,SAAS,iBAAiB,KAAK,mBAAmB;QACjH,SAAS,MAAM,CAAiC;IAClD;IAEA,IACE,cACA,aAAa,UAAU,IAAI,IAAI,CAC7B,QAAQ,iBAAiB,QAAQ,mBAAmB,gBAAgB;IACpE,QAAQ,iBAAiB,mBAAmB,iBAAiB,iGAAiG;IAC9J,QAAQ,mBAAmB,qBAAqB,SAAS,0FAA0F;IACrJ,KACA,iBAAiB,mBAAmB,qBAAqB,YACzD,iBAAiB,iBAAiB,mBAAmB,KACrD,QAAQ,oBAAoB,qBAAqB,YAAY,aAAa,4DAA4D;IACtI,qCAAqC,CAAC,aAAa,SAAS,mDAAmD;MAC/G;QAEA,IAAI,wBAAwB;YAC1B,6CAA6C;YAC7C,SAAS,gBAAgB,CAAC;YAC1B,IAAI,CAAC,eAAe,SAAS,cAAc,CAAiC;QAC9E;QAEA,yBAAyB;QACzB,IAAI,CAAC,cAAc;YAEjB,+EAA+E;YAC/E,MAAM,eAAe,cAAc,CAAC,qBAAqB,YAAY,CAAC,IAAI,SAAS,KAAK,QAAQ,aAAa;YAC7G,MAAM,eAAe,SAAS,OAAO,GAAG,CAAC,SAAS,OAAO,OAAO,GAAG,CAAC,IAAI,gBAAgB;YAExF,0DAA0D;YAC1D,IAAI,QAA6B,wBAAwB,GAAE,AAAC,SAAU,KAAK;YAC3E,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI,4BAA4B;YAEhC,MAAO,MAAO;gBAEZ,MAAM,mBAAmB,MAAM,YAAY;gBAC3C,MAAM,mBAAmB,MAAM,YAAY;gBAC3C,MAAM,sBAAsB,MAAM,eAAe;gBACjD,MAAM,kBAAkB,MAAM,kBAAkB,GAAG,MAAM,eAAe;gBACxE,MAAM,eAAe,MAAM,QAAQ;gBACnC,MAAM,eAAe,MAAM,QAAQ;gBACnC,MAAM,sBAAsB,qBAAqB,iBAAiB,IAAI;gBAEtE,IAAI,CAAC,gBACD,CAAC,qBAAqB,uBAAuB,gBAAgB,kBAAkB,CAAC,eAAe,aAAa,MAAM,GAAG,CAAC,CAAC,KACvH,CAAC,qBAAqB,KAAK,gBAAgB,MAAM,kBAAkB,CACpE,KAAK,CAAC,CAAC,uBACN,CAAC,MAAM,aAAa,IACpB,CAAC,CAAC,MAAM,aAAa,IAAI,gBAAgB,eAAe,KACxD,CAAC,CAAC,gBAAiB,aAAa,aAAa,IAAI,gBAAgB,aAAa,kBAAkB,AAAC,KACjG,CAAC,CAAC,gBAAiB,aAAa,aAAa,IAAK,gBAAgB,AAAC,aAAa,kBAAkB,GAAG,aAAa,eAAe,GAAI,MAAM,MAAM,AAAE,CACpJ,GACD;oBAEA,MAAM,eAAe,MAAM,YAAY,GAAG,MAAM,gBAAgB,MAAM,UAAU,EAAE,GAAG;oBACrF,MAAM,gBAAgB,MAAM,KAAK,CAAC,eAAe,MAAM,eAAe;oBACtE,MAAM,gBAAgB,MAAM,SAAS;oBACrC,MAAM,iBAAiB,MAAM,UAAU;oBACvC,MAAM,YAAY,MAAM,UAAU;oBAClC,MAAM,gBAAgB,cAAc,WAAW,MAAM;oBACrD,MAAM,gBAAgB,mBAAmB,WAAW,MAAM;oBAC1D,yEAAyE;oBACzE,MAAM,iBAAiB,AAAC,iBAAiB,iBAAkB,kBAAkB,KAAK,kBAAkB,IAAI,CAAC,IAAI,QAAQ,SAAS;oBAE9H,wBAAwB;oBACxB,0BAA0B,GAC1B,IAAI;oBACJ,mBAAmB,GACnB,IAAI;oBAEJ,IAAI,eAAe;wBACjB,QAAQ,SAA+B,cAAc,MAAM,YAAY,MAAM,WAAW,EAAE,MAAM,SAAS,EAAG,gBAAgB;oBAC9H,OAAO,IAAI,mBAAmB,WAAW,IAAI,EAAE;wBAC7C,kDAAkD;wBAClD,SAA+B,cAAc,MAAM,YAAY,MAAM,WAAW,EAAE,MAAM,SAAS,EAAG,gBAAgB;wBACpH,QAAQ,GAAG,SAAS,MAAM,KAAK,EAAE;oBACnC,OAAO,IAAI,mBAAmB,WAAW,KAAK,EAAE;wBAC9C,MAAM,KAAK,MAAM,YAAY;wBAC7B,MAAM,KAAK,MAAM,UAAU;wBAC3B,MAAM,IAAI,MAAM,MAA4B,cAAc,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,iBAAkB,GAAG,MAAM;wBAC/G,MAAM,IAAI,MAAM,MAA4B,cAAc,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,iBAAkB,GAAG,MAAM;wBAC/G,MAAM,IAAI,MAAM,MAA4B,cAAc,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,iBAAkB,GAAG,MAAM;wBAC/G,MAAM,IAAI,MAA4B,cAAc,MAAM,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,gBAAgB,kBAAmB,GAAG;wBAC1H,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;wBACnC,IAAI,qBAAqB;4BACvB,MAAM,KAAK,MAAM,QAAQ;4BACzB,EAAE,CAAC,EAAE,GAAG;4BACR,EAAE,CAAC,EAAE,GAAG;4BACR,EAAE,CAAC,EAAE,GAAG;4BACR,EAAE,CAAC,EAAE,GAAG;wBACV;oBACF,OAAO,IAAI,mBAAmB,WAAW,OAAO,EAAE;wBAChD,QAAQ,MAAM,QAAQ,CAAC,EAAE;wBACzB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,UAAU,CAAC,MAAM,EAAE,IAAI,GAAG,IAAK;4BACvD,MAAM,IAA0B,cAAc,MAAM,YAAY,MAAM,YAAY,CAAC,EAAE,EAAE,MAAM,UAAU,CAAC,EAAE,EAAE,gBAAgB;4BAC5H,MAAM,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE;4BAC/B,SAAS,GAAG,IAAI,IAAI,IAAI,GAAG;4BAC3B,IAAI,qBAAqB;gCACvB,MAAM,QAAQ,CAAC,EAAE,GAAG;4BACtB;wBACF;oBACF;oBAEA,sCAAsC;oBACtC,IAAI,qBAAqB;wBACvB,MAAM,OAAO,GAAG;oBAClB;oBAEA,IAAI,CAAC,kBAAkB,qBAAqB,iBAAiB,KAAK,EAAE;wBAElE,MAAM,gBAAgB,MAAM,QAAQ;wBACpC,cAAc,MAAM,MAAM;wBAE1B,IAAI,eAAe;4BACjB,WAAW,CAAC,cAAc,GAAG;wBAC/B,OAAO,IAAI,cAAc,WAAW,SAAS,EAAE;4BAC7C,sBAAsB,GAAE,AAAC,YAAa,YAAY,CAAC,eAAqC;wBAC1F,OAAO;4BACL,aAAa,sBAAsB,GAAE,AAAC,YAAa,KAAK;4BACxD,IAAI,cAAc,WAAW,SAAS,EAAE;gCACtC,IAAI,gBAAgB,uBAAuB;oCACzC,wBAAwB;oCACxB,yIAAyI;oCACzI,kCAAkC,WAAW,CAAC,iBAAiB;gCACjE;gCACA,+BAA+B,CAAC,cAAc,GAAG;gCACjD,4BAA4B;4BAC9B,OAAO,IAAI,cAAc,WAAW,GAAG,EAAE;gCACvC,UAAU,CAAC,cAAc,GAAG;4BAC9B,OAAO,IAAI,cAAc,WAAW,OAAO,EAAE;gCAC3C,WAAW,WAAW,CAAC,eAAoC;4BAC7D;wBACF;wBAEA,IAAI,wBAAwB,cAAc;oBAE5C,OAAO;wBACL,wEAAwE;wBACxE,MAAM,MAAM,GAAG;oBACjB;gBAEF;gBAEA,6EAA6E;gBAC7E,+CAA+C;gBAC/C,IAAI,6BAA6B,MAAM,iBAAiB,EAAE;oBACxD,IAAI,MAAM;oBACV,IAAK,IAAI,OAAO,gCAAiC;wBAC/C,OAAO,GAAG,yBAAyB,CAAC,IAAI,GAAG,+BAA+B,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrF;oBACA,WAAW,SAAS,GAAG;oBACvB,4BAA4B;gBAC9B;gBAEA,QAAQ,MAAM,KAAK;YACrB;YAEA,IAAI,CAAC,iBAAiB,aAAa;gBACjC,wBAAwB,GAAE,AAAC,SAAU,QAAQ,CAA4B;YAC3E;QACF;QAEA,IAAI,CAAC,iBAAiB,wBAAwB;YAC5C,SAAS,QAAQ,CAAiC;QACpD;IAEF;IAEA,uBAAuB;IAEvB,+GAA+G;IAC/G,IAAI,UAAU,UAAU;QACtB,IAAI,CAAC,iBAAiB,CACpB,AAAC,OAAO,KAAK,IAAI,CAAC,sBAAsB,wBAAwB,YAAY,CAAC,aAC5E,sBAAsB,wBAAwB,YAAY,SAC7D,GAAG;YACD,SAAS,UAAU,CAAiC;YACpD,SAAS,SAAS,GAAG,CAAC;QACxB;IACF,8GAA8G;IAC9G,OAAO,IAAI,0BAA0B,mCAAmC;QACtE,IAAI,mBAAmB,UAAU;YAC/B,8GAA8G;YAC9G,SAAS,UAAU,IAAI,SAAS,QAAQ;QAC1C,OAAO,IAAI,SAAS,iBAAiB,IAAI,iBAAiB,GAAG;YAC3D,+HAA+H;YAC/H,SAAS,MAAM,GAAG;YAClB,IAAI,CAAC,aAAa,CAAC,cAAc;gBAC/B,iHAAiH;gBACjH,SAAS,SAAS,GAAG;gBACrB,IAAI,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,sBAAsB,CAAC,OAAO,KAAK,CAAC,GAAG;oBACxE,SAAS,UAAU,CAAiC;oBACpD,SAAS,QAAQ,CAAiC;gBACpD;YACF;QACF;IACF,4CAA4C;IAC5C,OAAO;QACL,SAAS,SAAS,GAAG;IACvB;IAEA,4HAA4H;IAC5H,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,MAAM,OAAO,CAAC,UAAU,MAAM,eAAe,gBAAgB;IAC3D,MAAM,oBAAoB,SAAS,iBAAiB;IACpD,OAAO,UAAU,MAAM,eAAe,gBAAgB;IACtD,IAAI,SAAS,YAAY,EAAE;QACzB,MAAM,KAA6B;QACnC,MAAM,uBAAuB,GAAG,SAAS;QACzC,MAAM,iBAAiB,iBAAiB,OAAO,GAAG,cAAc;QAChE,MAAM,oBAAoB;QAE1B,IAAI,wBAAwB;QAC5B,IAAI,0BAA0B;QAE9B,8FAA8F;QAC9F,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,KAAK,mBAAmB;YACjE,MAAM,sBAAsB,GAAG,iBAAiB;YAChD,gBAAgB,IAAI,CAAC,wBAAwB,GAAE;gBAC7C,IAAI,CAAC,sBAAsB;oBACzB,2FAA2F;oBAC3F,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,SAAS,IAAI,MAAM,YAAY,GAAG,MAAM,iBAAiB,EAAE;wBACxF,OAAO,OAAO,qBAAqB,eAAe,GAAG,UAAU,KAAK;oBACtE;oBACA,6FAA6F;oBAC7F,MAAM,KAAK,GAAG;oBACd,MAAM,SAAS,GAAG;gBACpB,OAAO;oBACL,MAAM,gBAAgB,MAAM,QAAQ;oBACpC,MAAM,iBAAiB,MAAM,OAAO,GAAG,MAAM,MAAM;oBACnD,MAAM,eAAe,iBAAiB;oBACtC,wFAAwF;oBACxF,IAAI,CAAC,iBAAiB,iBAAiB,YAAY,CAAC,CAAC,kBAAkB,iBAAiB,mBAAmB,GAAG;wBAC5G,MAAM,UAAU,CAAC;oBACnB;gBACF;YACF;YACA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAiC;QAChE;QAEA,gBAAgB,IAAI,CAAC,wBAAwB,GAAE;YAC7C,MAAM,YAAY,MAAM,CAAC,iBAAiB,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE,KAAK,wCAAwC;YACtH,MAAM,gBAAgB,MAAM,IAAI,GAAG,GAAG,IAAI,GAAG,MAAM,WAAW,CAAC,qBAAqB;YACpF,yBAAyB,OAAO,OAAO,WAAW,eAAe,gBAAgB;YACjF,IAAI,CAAC,MAAM,SAAS,IAAI,yBAAyB,0BAA0B;QAC7E,GAAG;QAEH,uGAAuG;QACvG,IAAI,CAAC,iBAAiB,uBAAuB,GAAG,QAAQ,CAAiC;QAEzF,8GAA8G;QAC9G,IAAI,2BAA2B,GAAG,YAAY,IAAI,GAAG,QAAQ,EAAE;YAC7D,wFAAwF;YACxF,GAAG,MAAM,GAAG;YACZ,IAAI,CAAC,GAAG,SAAS,EAAE;gBACjB,GAAG,SAAS,GAAG;gBACf,IAAI,CAAC,eAAe;oBAClB,GAAG,UAAU,CAAiC;oBAC9C,GAAG,QAAQ,CAAiC;gBAC9C;YACF;QACF;IACF;AACF;AAKA,MAAM,WAAW;IACf,WAAW;IACX,QAAQ;AACV;AAEA;;;;;;;CAOC,GAED;;;CAGC,GACD,MAAM,uBAAuB,CAAA;IAC3B,IAAI,YAAY,SAAS,SAAS;IAClC,IAAI,CAAC,WAAW;QACd,YAAY;YACV,UAAU;YACV,kBAAkB;YAClB,SAAS;YACT,QAAQ;YACR,OAAO;YACP,OAAO;QACT;QACA,SAAS,SAAS,GAAG;QACrB,SAAS,MAAM,GAAG;YAChB,QAAQ,OAAO,CAAC,CAAA;gBACd,IAAK,IAAI,gBAAgB,kBAAmB;oBAC1C,MAAM,SAAS,iBAAiB,CAAC,aAAa;oBAC9C,MAAM,cAAc,OAAO,KAAK;oBAChC,IAAI,aAAa;wBACf,MAAM,YAAY,YAAY,UAAU;wBACxC,MAAM,iBAAiB,cAAc,WAAW,OAAO,IAAI,cAAc,WAAW,KAAK,GAAG,WAAW,YAAY,YAAY,IAAI;wBACnI,IAAI,gBAAgB,YAAY,WAAW;wBAC3C,IAAI,QAAQ,OAAO,KAAK;wBACxB,MAAO,SAAS,UAAU,YAAa;4BACrC,IAAI,gBAAgB;gCAClB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,QAAQ,CAAC,MAAM,EAAE,IAAI,GAAG,IAAK,cAAc,CAAC,EAAE,IAAI,MAAM,QAAQ,CAAC,EAAE;4BAC/F,OAAO;gCACL,iBAAiB,MAAM,OAAO;4BAChC;4BACA,QAAQ,MAAM,QAAQ;wBACxB;wBACA,YAAY,SAAS,GAAG;wBACxB,YAAY,UAAU,GAAG;oBAC3B;gBACF;YACF;YACA,uIAAuI;YACvI,OAAO,WAAW,GAAG,GAAG,GAAG,UAAU,KAAK;QAC5C;IACF;IACA,OAAO;AACT;AAEA,MAAM,mBAAmB,YAAY,wBAAwB;AAC7D,MAAM,qBAAqB,YAAY,uBAAuB;AAE9D,MAAM,eAAe;IAEnB,+BAA+B,GAC/B,YAAY,QAAQ,CAAE;QACpB,KAAK,CAAC;QACN,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,qBAAqB,GAAG;QAC7B,2BAA2B,GAC3B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG,aAAa,IAAI,MAAM,GAAG,OAAQ;QAChD,oCAAoC,GACpC,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,YAAY,GAAG;QACjC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO;YAC1B,IAAI,CAAC,gBAAgB,CAAC;YACtB,MAAM,cAAc,IAAI,CAAC,MAAM;YAC/B,MAAM,YAAY,IAAI,CAAC,IAAI;YAC3B,IAAI,iBAAyC,IAAI,CAAC,KAAK;YACvD,MAAO,eAAgB;gBACrB,MAAM,eAAe,eAAe,KAAK;gBACzC,IAAI,CAAC,eAAe,MAAM,EAAE;oBAC1B,KACE,gBACA,CAAC,OAAO,eAAe,UAAU,IAAI,eAAe,MAAM,GAAG,aAC7D,GACA,GACA,eAAe,IAAI,GAAG,YAAY,eAAe,WAAW,CAAC,QAAQ,UAAU,IAAI;gBAEvF,OAAO;oBACL,YAAY,IAAI,EAAE;oBAClB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK;oBAChC,eAAe,QAAQ,GAAG;oBAC1B,IAAI,eAAe,SAAS,IAAI,CAAC,eAAe,UAAU,EAAE;wBAC1D,eAAe,MAAM;oBACvB;gBACF;gBACA,iBAAiB;YACnB;YACA,SAAS,MAAM;QACjB;IACF;IAEA,OAAO;QACL,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAC1D,IAAI,CAAC,KAAK,GAAG,iBAAiB;QAChC;QACA,OAAO,IAAI;IACb;IAEA,QAAQ;QACN,IAAI,CAAC,MAAM,GAAG;QACd,OAAO;IACT;IAEA,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,gBAAgB,IAAI,EAAE,CAAC,qBAAqB,GAAE,QAAU,MAAM,SAAS;QACvE,OAAO,IAAI,CAAC,IAAI;IAClB;IAEA,8BAA8B;IAC9B,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,QAAQ,SAAS,KAAK,IAAI,IAAI,CAAC;IACvD;IAEA,IAAI,MAAM,YAAY,EAAE;QACtB,IAAI,CAAC,MAAM,GAAG,eAAe,QAAQ,SAAS;QAC9C,gBAAgB,IAAI,EAAE,CAAC,qBAAqB,GAAE,QAAU,MAAM,KAAK,GAAG,MAAM,MAAM;IACpF;IAEA,iCAAiC;IACjC,IAAI,WAAW;QACb,OAAO,QAAQ,SAAS,KAAK,IAAI,OAAO;IAC1C;IAEA,IAAI,SAAS,IAAI,EAAE;QACjB,MAAM,eAAe;QACrB,MAAM,WAAW,SAAS;QAC1B,MAAM,WAAW,WAAW,eAAe;QAC3C,IAAI,QAAQ,SAAS,KAAK,UAAU;YAClC,QAAQ,SAAS,GAAG;YACpB,QAAQ,aAAa,GAAG,MAAM;YAC9B,MAAM,cAAc,WAAW,eAAe;YAC9C,mBAAmB,GAClB,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAK;YAC5B,IAAI,CAAC,MAAM,IAAI;QACjB;IACF;IAEA,kCAAkC;IAClC,IAAI,YAAY;QACd,OAAO,QAAQ,SAAS;IAC1B;IAEA,IAAI,UAAU,SAAS,EAAE;QACvB,QAAQ,SAAS,GAAG;IACtB;AAEF;AACA,MAAM,SAAS,WAAW,GAAE,CAAC;IAC3B,MAAM,SAAS,IAAI,OAAO;IAC1B,IAAI,WAAW;QACb,eAAe,MAAM,GAAG;QACxB,IAAI,gBAAgB,CAAC,oBAAoB;YACvC,IAAI,CAAC,OAAO,qBAAqB,EAAE;YACnC,IAAI,MAAM,GAAG,OAAO,KAAK,KAAK,OAAO,MAAM;QAC7C;IACF;IACA,OAAO;AACT,CAAC;AAGD,MAAM,aAAa;IACjB,IAAI,OAAO,KAAK,EAAE;QAChB,OAAO,KAAK,GAAG,iBAAiB;QAChC,OAAO,MAAM;IACf,OAAO;QACL,OAAO,KAAK,GAAG;IACjB;AACF;AAEA,MAAM,aAAa;IACjB,mBAA4D,OAAO,KAAK;IACxE,OAAO,KAAK,GAAG;IACf,OAAO;AACT;AAKA;;;;;CAKC,GACD,MAAM,wBAAwB,CAAC,QAAQ,UAAU;IAC/C,MAAM,mBAAmB,OAAO,KAAK,CAAC,SAAS;IAC/C,IAAI;IACJ,IAAI,kBAAkB;QACpB,MAAM,mBAAmB,MAAM,CAAC,iBAAiB;QACjD,IAAI;QAAG,MAAO,IAAI,kBAAkB,IAAI,CAAC,kBAAmB;YAC1D,MAAM,qBAAqB,CAAC,CAAC,EAAE;YAC/B,oCAAoC;YACpC,MAAM,sBAAsB,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;YAC3C,gBAAgB,CAAC,mBAAmB,GAAG;YACvC,IAAI,uBAAuB,UAAU;gBACnC,6BAA6B;gBAC7B,0EAA0E;gBAC1E,IAAI,uBAAuB;oBACzB,qBAAqB,CAAC,SAAS,GAAG;gBACpC;YACF;QACF;IACF;IACA,OAAO,oBAAoB,CAAC,MAAM,8BAA8B,6BAC9D,iBAAiB,UAAU,WAAW,MACtC,iBAAiB,UAAU,aAAa,iBAAiB,UAAU,UAAU,SAAS;AAC1F;AAKA;;;CAGC,GACD,SAAS,YAAY,CAAC;IACpB,MAAM,IAAI,MAAM,KAAK,QAAQ,IAAI,CAAC,gBAAgB,CAAC,KAAK;IACxD,IAAI,aAAa,YAAY,aAAa,gBAAgB,OAAO;AACnE;AAEA;;;;;;;;;;;;;;CAcC,GACD,SAAS,aAAa,OAAO;IAC3B,IAAI,MAAM,UAAU,OAAmC,EAAE;IACzD,IAAI,MAAM,UAAU;QAClB,MAAM,YAAY,QAAQ,IAAI,CAAC;QAC/B,yBAAyB,GACzB,MAAM,SAAS,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YAChD,MAAM,OAAO,SAAS,CAAC,EAAE;YACzB,IAAI,CAAC,MAAM,OAAO;gBAChB,MAAM,WAAW,YAAY;gBAC7B,IAAI,UAAU;oBACZ,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,MAAM,EAAE,IAAI,IAAI,IAAK;wBACjD,MAAM,UAAU,QAAQ,CAAC,EAAE;wBAC3B,IAAI,CAAC,MAAM,UAAU;4BACnB,IAAI,cAAc;4BAClB,IAAK,IAAI,IAAI,GAAG,KAAK,OAAO,MAAM,EAAE,IAAI,IAAI,IAAK;gCAC/C,IAAI,MAAM,CAAC,EAAE,KAAK,SAAS;oCACzB,cAAc;oCACd;gCACF;4BACF;4BACA,IAAI,CAAC,aAAa;gCAChB,OAAO,IAAI,CAAC;4BACd;wBACF;oBACF;gBACF,OAAO;oBACL,IAAI,cAAc;oBAClB,IAAK,IAAI,IAAI,GAAG,KAAK,OAAO,MAAM,EAAE,IAAI,IAAI,IAAK;wBAC/C,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;4BACtB,cAAc;4BACd;wBACF;oBACF;oBACA,IAAI,CAAC,aAAa;wBAChB,OAAO,IAAI,CAAC;oBACd;gBACF;YACF;QACF;QACA,OAAO;IACT;IACA,IAAI,CAAC,WAAW,OAAqC;QAAC;KAAQ;IAC9D,MAAM,WAAW,YAAY;IAC7B,IAAI,UAAU,OAAsC,MAAM,IAAI,CAAC;IAC/D,OAAmC;QAAC;KAAQ;AAC9C;AAEA;;;;;;;;;;;;;;CAcC,GACD,SAAS,gBAAgB,OAAO;IAC9B,MAAM,qBAAqB,aAAa;IACxC,MAAM,sBAAsB,mBAAmB,MAAM;IACrD,IAAI,qBAAqB;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,qBAAqB,IAAK;YAC5C,MAAM,SAAS,kBAAkB,CAAC,EAAE;YACpC,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE;gBACrC,MAAM,CAAC,yBAAyB,GAAG;gBACnC,MAAM,YAAY,MAAM;gBACxB,MAAM,QAAQ,sBAAsB,GAAE,AAAC,OAAQ,QAAQ,IAAI;gBAC3D,IAAI,OAAO;oBACT,MAAM,CAAC,YAAY,GAAG;oBACtB,MAAM,CAAC,YAAY,GAAG;oBACtB,MAAM,CAAC,iBAAiB,GAAG,CAAC;gBAC9B;YACF;QACF;IACF;IACA,OAAO;AACT;AAKA;;;CAGC,GACD,MAAM,UAAU,CAAA;IACd,MAAM,gBAAgB,aAAa;IACnC,MAAM,aAA+C,aAAa,CAAC,EAAE;IACrE,IAAI,CAAC,cAAc,CAAC,MAAM,aAAa;IACvC,OAAO;AACT;AAEA;;;;CAIC,GACD,MAAM,UAAU,CAAC,OAAO,YAAY,GAAG,GAAK,CAAC;QAC3C,MAAM,SAA2C,QAAQ;QACzD,IAAI,CAAC,QAAQ;QACb,MAAM,SAAS,OAAO,OAAO,KAAK;QAClC,MAAM,YAAY,SAAS,MAAM;QACjC,MAAM,iBAAiB,MAAM,CAAC,kBAAkB;QAChD,IAAI,gBAAgB,OAAO,YAAY,CAAC,SAAS,MAAM,UAAU;QAEjE,IAAI,KAAK,IAAI,KAAK;QAElB,IAAI,CAAC,WAAW;YACd,KAAK,OAAO,YAAY,CAAC,SAAS,MAAM;YACxC,KAAK,OAAO,YAAY,CAAC,SAAS,MAAM;QAC1C,OAAO;YACL,MAAM,UAAU,+BAA+B,GAAE,AAAC,OAAQ,cAAc;YACxE,MAAM,UAAU,OAAO,cAAc;YACrC,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,UAAU,YAAY,KAAK,IAAI,CAAC,UAAU;YAC/E,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;gBAClC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC;gBAC5B,MAAM,eAAe,+BAA+B,GAAE,AAAC,OAAQ,gBAAgB,CAAC,UAAU;gBAC1F,MAAM,eAAe,OAAO,gBAAgB,CAAC,UAAU;gBACvD,MAAM,SAAS,SAAU,MAAM,IAAI,MAAM,MAAO;gBAChD,MAAM,SAAS,MAAM,aAAa,CAAC,EAAE,KAAK,YAAY,aAAa,CAAC,GAAG;gBACvE,MAAM,SAAS,MAAM,aAAa,CAAC,EAAE,KAAK,YAAY,aAAa,CAAC,GAAG;YACzE;QACF;QAEA,MAAM,CAAC,kBAAkB,GAAG;QAE5B,OAAO;YAAC;YAAI;SAAG;IACjB;AAEA;;;CAGC,GACD,MAAM,iBAAiB,CAAA;IACrB,IAAI,cAAc;IAClB,IAAI,OAAO,IAAI,MAAM,EAAE;QACrB,MAAM,MAAM,IAAI,MAAM;QACtB,IAAI,KAAK;YACP,MAAM,SAAS,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACjD,MAAM,SAAS,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACjD,cAAc,CAAC,SAAS,MAAM,IAAI;QACpC;IACF;IACA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,MAAM,sBAAsB,CAAC,KAAK,OAAO;IACvC,MAAM,aAAa;IACnB,MAAM,iBAAiB,iBAAiB;IACxC,MAAM,gBAAgB,eAAe,aAAa;IAClD,aAAa;IACb,MAAM,WAAW,eAAe,YAAY,KAAK,uBAAuB,MAAM;IAC9E,IAAI,aAAa;IAEjB,MAAM,QAAQ,IAAI,MAAM,KAAK;QAC3B,KAAI,MAAM,EAAE,QAAQ;YAClB,MAAM,QAAQ,MAAM,CAAC,SAAS;YAC9B,IAAI,aAAa,mBAAmB,OAAO;YAC3C,IAAI,aAAa,gBAAgB;gBAC/B,OAAO,CAAC,GAAG;oBACT,IAAI,IAAI,CAAC,EAAE,KAAK,QAAQ;wBACtB,MAAM,QAAQ,IAAI,CAAC,EAAE;wBACrB,MAAM,SAAS,MAAM,KAAK,CAAC;wBAC3B,MAAM,KAAK,CAAC,MAAM,CAAC,EAAE;wBACrB,MAAM,KAAK,CAAC,MAAM,CAAC,EAAE;wBACrB,6EAA6E;wBAC7E,yCAAyC;wBACzC,4DAA4D;wBAC5D,6DAA6D;wBAC7D,MAAM,cAAc,eAAe;wBACnC,MAAM,KAAK,KAAK,CAAC,MAAM;wBACvB,MAAM,KAAK,AAAC,KAAK,aAAa,cAAe;wBAC7C,MAAM,KAAM,aAAa,cACf,CAAC,AAAC,OAAO,KAAK,OAAO,KAAO,OAAO,KAAK,OAAO,IAAK,IAAI,KAAK,WAAW,IAAI;wBACtF,IAAI,kBAAkB,QAAQ;4BAC5B,MAAM,SAAS,OAAO,KAAK,SAAS;4BACpC,IAAI,eAAe,QAAQ;gCACzB,OAAO,KAAK,CAAC,aAAa,GAAG,GAAG,QAAQ;gCACxC,aAAa;4BACf;wBACF;wBACA,OAAO,YAAY,CAAC,qBAAqB,GAAG,IAAI;wBAChD,OAAO,YAAY,CAAC,oBAAoB,GAAG,GAAG,CAAC,EAAE,IAAI;oBACvD;oBACA,OAAO,QAAQ,KAAK,CAAC,OAAO,QAAQ;gBACtC;YACF;YAEA,IAAI,MAAM,QAAQ;gBAChB,OAAO,CAAC,GAAG,OAAS,QAAQ,KAAK,CAAC,OAAO,QAAQ;YACnD,OAAO;gBACL,OAAO;YACT;QACF;IACF;IAEA,IAAI,IAAI,YAAY,CAAC,kBAAkB,GAAG,YAAY,EAAE;QACtD,IAAI,YAAY,CAAC,cAAc,GAAG,YAAY;QAC9C,MAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,CAAC,EAAE,KAAK;IAC9C;IAEA,OAA0C;AAC5C;AAEA;;;;;;CAMC,GACD,MAAM,iBAAiB,CAAC,UAAU,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClD,MAAM,MAAM,aAAa;IACzB,OAAO,IAAI,GAAG,CAAC,CAAA,MAAO,oBACc,KAClC,OACA;AAEJ;AAEA,wBAAwB;AAExB;;;;;CAKC,GACD,MAAM,eAAe,CAAC,OAAO,UAAU,SAAS,CAAC;IAC/C,OAAO,MAAM,gBAAgB,CAAC,WAAW,UAAU,IAAI,WAAW,SAAS;AAC7E;AAEA;;;;CAIC,GACD,MAAM,iBAAiB,CAAC,OAAO;IAC7B,OAAO,CAAA;QACL,MAAM,cAAc,CAAE,MAAM,cAAc;QAC1C,MAAM,QAAQ,GAAG,CAAC,YAAY;QAC9B,MAAM,MAAM,MAAM,MAAM;QACxB,6BAA6B,GAC7B,OAAO;YACL,MAAM;YACN,IAAI;YACJ,0BAA0B,GAC1B,UAAU,CAAA;gBACR,IAAI,iBAAiB,KAAK;oBACxB,MAAM,KAAK,aAAa,OAAO,UAAU,CAAC;oBAC1C,MAAM,KAAK,aAAa,OAAO,UAAU;oBACzC,OAAO,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,MAAM;gBACjD,OAAO;oBACL,MAAM,IAAI,aAAa,OAAO,UAAU;oBACxC,OAAO,iBAAiB,MACtB,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GACvD,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBAC3D;YACF;QACF;IACF;AACF;AAEA;;CAEC,GACD,MAAM,mBAAmB,CAAA;IACvB,MAAM,QAAQ,QAAQ;IACtB,IAAI,CAAC,OAAO;IACZ,OAAO;QACL,YAAY,eAAe,OAAO;QAClC,YAAY,eAAe,OAAO;QAClC,QAAQ,eAAe,OAAO;IAChC;AACF;AAEA,gCAAgC;AAEhC,MAAM,wBAAwB;IAAC;IAAW;IAAU;IAAY;CAAQ;AAExE;;;;CAIC,GACD,MAAM,sBAAsB,CAAC,IAAI;IAC/B,2JAA2J;IAC3J,IAAI,sBAAsB,QAAQ,CAAC,eAAe,OAAO;IACzD,IAAI,GAAG,YAAY,CAAC,iBAAiB,gBAAgB,IAAI;QACvD,IAAI,iBAAiB,SAAS;YAC5B,MAAM,eAAiD,sBAAsB,GAAE,AAAC,GAAI,UAAU;YAC9F,iEAAiE;YACjE,OAAO,gBAAgB,aAAa,OAAO,KAAK;QAClD;QACA,OAAO;IACT;AACF;AAEA,MAAM,MAAM;IACV;IACA;IACA;AACF;AAKA;;;;CAIC,GACD,MAAM,YAAY,CAAA;IAChB,MAAM,OAAO,WAAW,IAAI,CAAC,aAAa,YAAY,IAAI,CAAC;IAC3D,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;IACvC,OAAO;QACL,CAAC,IAAI,CAAC,EAAE;QACR,CAAC,IAAI,CAAC,EAAE;QACR,CAAC,IAAI,CAAC,EAAE;QACR;KACD;AACH;AAEA;;;;CAIC,GACD,MAAM,YAAY,CAAA;IAChB,MAAM,YAAY,SAAS,MAAM;IACjC,MAAM,UAAU,cAAc,KAAK,cAAc;IACjD,OAAO;QACL,CAAC,CAAC,OAAO,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE;QAChD,CAAC,CAAC,OAAO,QAAQ,CAAC,UAAU,IAAI,EAAE,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE;QAC9D,CAAC,CAAC,OAAO,QAAQ,CAAC,UAAU,IAAI,EAAE,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE;QAC5D,cAAc,KAAK,cAAc,IAAK,CAAC,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,UAAU,IAAI,EAAE,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE,IAAI,GAAG,EAAE,OAAO,CAAC,KAAK;KAC/H;AACH;AAEA;;;;;CAKC,GACD,MAAM,UAAU,CAAC,GAAG,GAAG;IACrB,IAAI,IAAI,GAAG,KAAK;IAChB,IAAI,IAAI,GAAG,KAAK;IAChB,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAC9B,IAAI,IAAI,IAAI,IACZ,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IACxC;AACT;AAEA;;;;CAIC,GACD,MAAM,YAAY,CAAA;IAChB,MAAM,OAAO,WAAW,IAAI,CAAC,aAAa,YAAY,IAAI,CAAC;IAC3D,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;IACrB,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;IACrB,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;IACrB,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;IACvC,IAAI,GAAG,GAAG;IACV,IAAI,MAAM,GAAG;QACX,IAAI,IAAI,IAAI;IACd,OAAO;QACL,MAAM,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI;QAC7C,MAAM,IAAI,IAAI,IAAI;QAClB,IAAI,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,KAAK,KAAK;QAC1C,IAAI,MAAM,QAAQ,GAAG,GAAG,KAAK,KAAK;QAClC,IAAI,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,KAAK,KAAK;IAC5C;IACA,OAAO;QAAC;QAAG;QAAG;QAAG;KAAE;AACrB;AAEA;;;;CAIC,GACD,MAAM,sCAAsC,CAAA;IAC1C,OAAO,MAAM,eAAe,UAAU,eAC/B,MAAM,eAAe,UAAU,eAC/B,MAAM,eAAe,UAAU,eAC/B;QAAC;QAAG;QAAG;QAAG;KAAE;AACrB;AAKA;;;;;CAKC,GACD,MAAM,WAAW,CAAC,aAAa;IAC7B,OAAO,MAAM,eAAe,eAAe;AAC7C;AAEA;;;;;;;CAOC,GACD,MAAM,mBAAmB,CAAC,OAAO,QAAQ,OAAO,OAAO;IACrD,IAAI,MAAM,QAAQ;QAChB,MAAM,OAAO;YACX,MAAM,WAAW,qBAAqB,GAAE,AAAC,MAAO,QAAQ,OAAO;YAC/D,2EAA2E;YAC3E,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,YAAY;QACrD;QACA,IAAI,OAAO;YACT,MAAM,IAAI,GAAG;QACf;QACA,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,QAAQ;IAC5B,OAAO,CAAC,MAAM,CAAC,YAAY,GAAG,WAAW,MAAM,GAC7C,wBAAwB;IACxB,MAAM,CAAC,YAAY,IAAI,oBAAoB,QAAQ,QAAQ,WAAW,SAAS,GAC/E,sFAAsF;IACtF,gBAAgB,QAAQ,CAAC,SAAS,gBAAgB,GAAG,CAAC,QAAQ,WAAW,SAAS,GAClF,gBAAgB;IAChB,iBAAiB,MAAM,QAAQ,WAAW,OAAO,GACjD,2BAA2B;IAC3B,QAAQ,sBAAsB,GAAE,AAAC,OAAQ,KAAK,GAAG,WAAW,GAAG,GAC/D,8BAA8B;IAC9B,QAAQ,SAAS,WAAW,MAAM,GAClC,WAAW,SAAS;AACxB;AAEA;;;;;CAKC,GACD,MAAM,cAAc,CAAC,QAAQ,UAAU;IACrC,MAAM,eAAe,OAAO,KAAK,CAAC,SAAS;IAC3C,IAAI,gBAAgB,uBAAuB;QACzC,qBAAqB,CAAC,SAAS,GAAG;IACpC;IACA,MAAM,QAAQ,gBAAgB,iBAAiB,MAAM,CAAC,kBAAkB,IAAI,QAAQ,gBAAgB,CAAC;IACrG,OAAO,UAAU,SAAS,MAAM;AAClC;AAEA;;;;;;CAMC,GACD,MAAM,6BAA6B,CAAC,QAAQ,UAAU,WAAW;IAC/D,MAAM,OAAO,CAAC,MAAM,aAAa,YAAY,aAAa,QAAQ;IAClE,OAAO,SAAS,WAAW,MAAM,GAAG,MAAM,CAAC,SAAS,IAAI,IACjD,SAAS,WAAW,SAAS,GAAG,sBAAsB,GAAE,AAAC,OAAQ,YAAY,CAAC,YAC9E,SAAS,WAAW,SAAS,GAAG,sBAA+C,QAAS,UAAU,yBAClG,SAAS,WAAW,OAAO,GAAG,YAAqC,QAAS,UAAU,uBAAuB,SAAS,KACtH,YAAqC,QAAS,UAAU;AACjE;AAEA;;;;;CAKC,GACD,MAAM,mBAAmB,CAAC,GAAG,GAAG;IAC9B,OAAO,aAAa,MAAM,IAAI,IACvB,aAAa,MAAM,IAAI,IACvB,IAAI;AACb;AAEA,mCAAmC,GACnC,MAAM,oCAAoC;IACxC,OAAO;QACL,uBAAuB,GACvB,GAAG,WAAW,MAAM;QACpB,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;AACF;AAEA;;;;CAIC,GACD,MAAM,oBAAoB,CAAC,UAAU;IACnC,uBAAuB,GACvB,aAAa,CAAC,GAAG,WAAW,MAAM;IAClC,aAAa,CAAC,GAAG;IACjB,aAAa,CAAC,GAAG;IACjB,aAAa,CAAC,GAAG;IACjB,aAAa,CAAC,GAAG;IACjB,aAAa,CAAC,GAAG;IACjB,IAAI,CAAC,UAAU,OAAO;IACtB,MAAM,MAAM,CAAC;IACb,IAAI,CAAC,MAAM,MAAM;QACf,gBAAgB;QAChB,aAAa,CAAC,GAAG;QACjB,OAAO;IACT,OAAO;QACL,oDAAoD;QACpD,IAAI,MAA4B;QAChC,+EAA+E;QAC/E,IAAI,GAAG,CAAC,EAAE,KAAK,KAAK;YAClB,aAAa,CAAC,GAAG,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC;QAClB;QACA,yFAAyF;QACzF,MAAM,YAAY,IAAI,QAAQ,CAAC,OAAO,QAAQ,aAAa,IAAI,CAAC;QAChE,IAAI,WAAW;YACb,0BAA0B;YAC1B,aAAa,CAAC,GAAG,WAAW,IAAI;YAChC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YAC9B,aAAa,CAAC,GAAG,SAAS,CAAC,EAAE;YAC7B,OAAO;QACT,OAAO,IAAI,aAAa,CAAC,EAAE;YACzB,+BAA+B;YAC/B,aAAa,CAAC,GAAG,CAAC;YAClB,OAAO;QACT,OAAO,IAAI,MAAM,MAAM;YACrB,aAAa;YACb,aAAa,CAAC,GAAG,WAAW,KAAK;YACjC,aAAa,CAAC,GAAG,oCAAoC;YACrD,OAAO;QACT,OAAO;YACL,gFAAgF;YAChF,MAAM,iBAAiB,IAAI,KAAK,CAAC;YACjC,aAAa,CAAC,GAAG,WAAW,OAAO;YACnC,aAAa,CAAC,GAAG,iBAAiB,eAAe,GAAG,CAAC,UAAU,EAAE;YACjE,aAAa,CAAC,GAAG,IAAI,KAAK,CAAC,yBAAyB,EAAE;YACtD,OAAO;QACT;IACF;AACF;AAEA;;;;CAIC,GACD,MAAM,sBAAsB,CAAC,OAAO;IAClC,aAAa,CAAC,GAAG,MAAM,UAAU;IACjC,aAAa,CAAC,GAAG,MAAM,SAAS;IAChC,aAAa,CAAC,GAAG,MAAM,KAAK;IAC5B,aAAa,CAAC,GAAG;IACjB,aAAa,CAAC,GAAG,WAAW,MAAM,UAAU;IAC5C,aAAa,CAAC,GAAG,WAAW,MAAM,QAAQ;IAC1C,OAAO;AACT;AAEA,MAAM,0BAA0B;AAKhC,MAAM,UAAU;IACd,gCAAgC,GAChC,MAAM,IAAI;IACV,iCAAiC,GACjC,MAAM,IAAI;AACZ;AAEA;;;;;CAKC,GACD,MAAM,mBAAmB,CAAC,QAAQ,UAAU,SAAS,MAAM;IACzD,MAAM,YAAY,OAAO,CAAC,OAAO;IACjC,IAAI,eAAe,UAAU,GAAG,CAAC;IACjC,IAAI,CAAC,cAAc;QACjB,eAAe,CAAC;QAChB,UAAU,GAAG,CAAC,QAAQ;IACxB;IACA,OAAO,YAAY,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,GAAG;QAChF,OAAO;QACP,OAAO;IACT;AACF;AAEA;;;;CAIC,GACD,MAAM,qBAAqB,CAAC,GAAG;IAC7B,OAAO,EAAE,aAAa,IAAI,EAAE,kBAAkB,GAAG,EAAE,kBAAkB;AACvE;AAEA;;CAEC,GACD,MAAM,gBAAgB,CAAA;IACpB,MAAM,aAAa,GAAG;IACtB,MAAM,aAAa,GAAG;IACtB,MAAM,eAAe,GAAG;IACxB,MAAM,YAAY,GAAG;AACvB;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,OAAO;IAE3B,MAAM,uBAAuB,MAAM,YAAY;IAE/C,yBAAyB;IAEzB,IAAI,yBAAyB,iBAAiB,OAAO,EAAE;QAErD,MAAM,oBAAoB,MAAM,kBAAkB;QAElD,SAAS,UAAU,OAAO,oBAAoB,YAAY;QAE1D,MAAM,cAAc,MAAM,QAAQ;QAElC,8DAA8D;QAE9D,IAAI,aAAa;YAEf,MAAM,aAAa,YAAY,MAAM;YACrC,MAAM,iBAAiB,YAAY,kBAAkB,GAAG,YAAY,eAAe;YAEnF,iCAAiC;YAEjC,IACE,4DAA4D;YAC5D,MAAM,MAAM,CAAC,EAAE,KAAK,WAAW,EAAE,IACjC,mCAAmC;YACnC,WAAW,cAAc,GAAE,KAC3B,0EAA0E;YAC1E,iBAAiB,CAAC,WAAW,QAAQ,GAAG,WAAW,iBAAiB,IAAI,mBACxE;gBAEA,8EAA8E;gBAC9E,cAAc;gBAEd,IAAI,kBAAkB,YAAY,QAAQ;gBAE1C,qEAAqE;gBACrE,MAAO,mBAAmB,gBAAgB,MAAM,CAAC,EAAE,KAAK,WAAW,EAAE,CAAE;oBACrE,cAAc;oBACd,kBAAkB,gBAAgB,QAAQ;gBAC5C;YAEF;YAEA,MAAM,0BAA0B,oBAAoB,MAAM,MAAM;YAEhE,IAAI,iBAAiB,yBAAyB;gBAE5C,MAAM,sBAAsB,YAAY,UAAU;gBAClD,MAAM,eAAe,iBAAiB,CAAC,sBAAsB,YAAY,eAAe;gBAExF,YAAY,eAAe,GAAG,0BAA0B,eAAe;gBACvE,YAAY,YAAY,GAAG,YAAY,eAAe;gBACtD,YAAY,aAAa,GAAG;gBAE5B,IAAI,YAAY,eAAe,GAAG,UAAU;oBAC1C,cAAc;gBAChB;YACF;YAEA,sEAAsE;YAEtE,IAAI,2BAA2B;YAE/B,gBAAgB,YAAY,CAAC,gBAAgB,GAAE;gBAC7C,IAAI,CAAC,EAAE,aAAa,EAAE,2BAA2B;YACnD;YAEA,IAAI,0BAA0B;gBAC5B,MAAM,eAAe,WAAW,MAAM;gBACtC,IAAI,cAAc;oBAChB,IAAI,oBAAoB;oBACxB,gBAAgB,cAAc,CAAC,sBAAsB,GAAE;wBACrD,IAAI,MAAM,YAAY;4BACpB,gBAAgB,GAAG,CAAC,gBAAgB,GAAE;gCACpC,IAAI,CAAC,EAAE,aAAa,EAAE,oBAAoB;4BAC5C;wBACF;oBACF;oBACA,IAAI,mBAAmB;wBACrB,aAAa,MAAM;oBACrB;gBACF,OAAO;oBACL,WAAW,MAAM;gBACjB,oGAAoG;gBACpG,wGAAwG;gBACxG,qFAAqF;gBACrF,kCAAkC;gBAClC,+BAA+B;gBAC/B,sBAAsB;gBACxB;YACF;QAEF;IAEA,oCAAoC;IAEpC,wDAAwD;IAExD,4EAA4E;IAC5E,0BAA0B;IAC1B,kCAAkC;IAClC,0CAA0C;IAC1C,MAAM;IACN,IAAI;IAEJ,2EAA2E;IAC3E,0BAA0B;IAC1B,kCAAkC;IAClC,6CAA6C;IAC7C,0CAA0C;IAC1C,MAAM;IACN,IAAI;IAEN,qCAAqC;IAErC,OAAO,IAAI,yBAAyB,iBAAiB,KAAK,EAAE;QAE1D,MAAM,wBAAwB,iBAAiB,MAAM,MAAM,EAAE,MAAM,QAAQ,EAAE;QAC7E,MAAM,oBAAoB,qBAAqB,QAAQ,IAAI;QAE3D,IAAI,cAAc,sBAAsB,KAAK;QAE7C,IAAI,CAAC,aAAa;YAChB,cAAc;gBAAE,GAAG,KAAK;YAAC;YACzB,YAAY,YAAY,GAAG,iBAAiB,OAAO;YACnD,YAAY,eAAe,GAAG;YAC9B,YAAY,UAAU,GAAG;YACzB,YAAY,QAAQ,GAAG,WAAW,MAAM,YAAY;YACpD,YAAY,OAAO,GAAG;YACtB,YAAY,KAAK,GAAG;YACpB,YAAY,KAAK,GAAG;YACpB,SAAS,uBAAuB;YAChC,SAAS,mBAAmB;QAC9B;QAEA,mDAAmD;QAEnD,MAAM,WAAW,MAAM,SAAS;QAChC,MAAM,WAAW,GAAG,YAAY,WAAW,GAAG;QAC9C,MAAM,SAAS,GAAG;QAClB,MAAM,QAAQ,GAAG,WAAW,MAAM,YAAY;QAC9C,MAAM,OAAO,GAAG;QAChB,YAAY,WAAW,GAAG;QAE1B,IAAI,MAAM,UAAU,EAAE;YACpB,MAAM,YAAY,WAAW,MAAM,UAAU;YAC7C,IAAI,WAAW;gBACb,UAAU,OAAO,CAAC,CAAC,OAAO;oBACxB,MAAM,YAAY,CAAC,EAAE,GAAG,YAAY,YAAY,CAAC,EAAE,GAAG;oBACtD,MAAM,UAAU,CAAC,EAAE,GAAG;gBACxB;YACF;YACA,YAAY,YAAY,GAAG;QAC7B;QAEA,SAAS,uBAAuB,OAAO,MAAM,YAAY;IAE3D;IAEA,OAAO;AAET;AAEA;;;CAGC,GACD,MAAM,uBAAuB,CAAA;IAC3B,MAAM,mBAAmB,MAAM,YAAY;IAC3C,IAAI,qBAAqB,iBAAiB,IAAI,EAAE;QAC9C,MAAM,cAAc,MAAM,MAAM;QAChC,MAAM,gBAAgB,MAAM,QAAQ;QACpC,MAAM,sBAAsB,QAAQ,IAAI;QACxC,MAAM,qBAAqB,oBAAoB,GAAG,CAAC;QACnD,MAAM,uBAAuB,kBAAkB,CAAC,cAAc;QAC9D,YAAY,sBAAsB,OAAO,YAAY;QACrD,IAAI,qBAAqB,iBAAiB,KAAK,EAAE;YAC/C,MAAM,kBAAkB,QAAQ,IAAI;YACpC,MAAM,iBAAiB,gBAAgB,GAAG,CAAC;YAC3C,IAAI,CAAC,gBAAgB;YACrB,MAAM,wBAAwB,cAAc,CAAC,cAAc;YAC3D,MAAM,oBAAoB,SAAS,SAAS;YAC5C,YAAY,uBAAuB,OAAO,YAAY;YACtD,0EAA0E;YAC1E,MAAM,cAAc,sBAAsB,KAAK;YAC/C,IAAI,eAAe,gBAAgB,sBAAsB,KAAK,EAAE;gBAC9D,YAAY,uBAAuB,aAAa,YAAY;gBAC5D,YAAY,mBAAmB;gBAC/B,IAAI,cAAc;gBAClB,IAAK,IAAI,QAAQ,eAAgB;oBAC/B,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE;wBAC9B,cAAc;wBACd;oBACF;gBACF;gBACA,IAAI,aAAa;oBACf,gBAAgB,MAAM,CAAC;gBACzB;YACF;QACF;IACF;IACA,OAAO;AACT;AAKA;;;CAGC,GACD,MAAM,uBAAuB,CAAA;IAC3B,MAAM,MAAM,GAAG;IACf,MAAM,KAAK,GAAG;IACd,MAAM,SAAS,GAAG;IAClB,OAAO;AACT;AAEA;;;CAGC,GACD,MAAM,cAAc,CAAA;IAClB,IAAI,CAAC,MAAM,UAAU,EAAE,OAAO;IAC9B,IAAI,MAAM,YAAY,EAAE;QACtB,gBAAgB,OAAO;IACzB,OAAO;QACL,gBAAgB,OAAO,CAAC,uBAAuB,GAAE;YAC/C,IAAI,MAAM,YAAY,KAAK,iBAAiB,IAAI,EAAE;gBAChD,aAAa,OAAO,iBAAiB,MAAM,MAAM,EAAE,MAAM,QAAQ;YACnE;QACF;IACF;IACA,MAAM,UAAU,GAAG;IACnB,OAAO;AACT;AAEA,IAAI,UAAU;AAEd;;CAEC,GACD,MAAM,cAAc;IAClB;;;;GAIC,GACD,YAAY,aAAa,CAAC,CAAC,EAAE,SAAS,IAAI,EAAE,iBAAiB,CAAC,CAAE;QAE9D,KAAK,CAAC;QAEN,MAAM,EACJ,EAAE,EACF,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,UAAU,EACV,MAAM,EACN,OAAO,EACP,OAAO,EACP,cAAc,EACd,QAAQ,EACT,GAAG;QAEJ,IAAI,QAAQ,KAAK,EAAE,QAAQ,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAEtD,MAAM,gBAAgB,SAAS,IAAI,OAAO,YAAY;QACtD,MAAM,gBAAgB,SAAS,OAAO,QAAQ,GAAG,QAAQ,QAAQ;QACjE,MAAM,aAAmC,MAAM,UAAU,MAAM,SAAS,cAAc,KAAK,GAAG,CAAC;QAC/F,MAAM,gBAAgB,MAAM,aAAa,MAAM,YAAY,WAAW,CAAC;QACvE,MAAM,YAAY,SAAS,MAAM,cAAc,IAAI;QACnD,MAAM,iBAAiB,SAAS,WAAW,cAAc,SAAS;QAClE,MAAM,sBAAsB,cAAc,QACd,cAAc,YACd,mBAAmB,GAAE,AAAC,YAAa,IAAI,WACvC,mBAAmB,GAAE,AAAC,YAAa;QAE/D,IAAI,iBAAiB;QAErB,IAAI,QAAQ;YACV,iBAAiB;QACnB,OAAO;YACL,IAAI,YAAY;YAChB,iHAAiH;YACjH,IAAI,OAAO,MAAM,EAAE;gBACjB,OAAO,WAAW,CAAC;gBACnB,YAAY,OAAO,YAAY;YACjC;YACA,iBAAiB,YAAY,OAAO,UAAU;QAChD;QAEA,qBAAqB;QACrB,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,MAAM,KAAK,EAAE;QAC9B,qBAAqB,GACrB,IAAI,CAAC,MAAM,GAAG;QACd,8BAA8B;QAC9B,IAAI,CAAC,QAAQ,GAAG,cAAc,AAAC,CAAC,gBAAgB,cAAc,IAAI,sBAAuB,mBAAmB;QAC5G,oBAAoB,GACpB,IAAI,CAAC,SAAS,GAAG;QACjB,oBAAoB,GACpB,IAAI,CAAC,MAAM,GAAG;QACd,oBAAoB,GACpB,IAAI,CAAC,KAAK,GAAG;QACb,oBAAoB,GACpB,IAAI,CAAC,SAAS,GAAG;QACjB,2BAA2B,GAC3B,IAAI,CAAC,OAAO,GAAG,WAAW,cAAc,OAAO;QAC/C,2BAA2B,GAC3B,IAAI,CAAC,cAAc,GAAG,kBAAkB,cAAc,cAAc;QACpE,2BAA2B,GAC3B,IAAI,CAAC,QAAQ,GAAG,YAAY,cAAc,QAAQ;QAClD,2BAA2B,GAC3B,IAAI,CAAC,MAAM,GAAG,UAAU,cAAc,MAAM;QAC5C,2BAA2B,GAC3B,IAAI,CAAC,OAAO,GAAG,WAAW,cAAc,OAAO;QAC/C,2BAA2B,GAC3B,IAAI,CAAC,UAAU,GAAG,cAAc,cAAc,UAAU;QACxD,mBAAmB,GACnB,IAAI,CAAC,iBAAiB,GAAG,eAAe,uBAAuB;QAC/D,mBAAmB,GACnB,IAAI,CAAC,cAAc,GAAG,qBAAqB,kBAAkB;QAC7D,mCAAmC,GACnC,IAAI,CAAC,SAAS,GAAG,SAAS,QAAQ,SAAS,UAAU,cAAc,QAAQ;QAC3E,mBAAmB,GACnB,IAAI,CAAC,OAAO,GAAG;QACf,mBAAmB,GACnB,IAAI,CAAC,MAAM,GAAG;QACd,mBAAmB,GACnB,IAAI,CAAC,UAAU,GAAG;QAClB,mBAAmB,GACnB,IAAI,CAAC,cAAc,GAAG;QACtB,mBAAmB,GACnB,IAAI,CAAC,iBAAiB,GAAG,GAAG,qBAAqB;QACjD,qBAAqB,GACrB,IAAI,CAAC,QAAQ,GAAG,MAAM,kBAAkB;QACxC,oBAAoB,GACpB,IAAI,CAAC,QAAQ,GAAG;QAChB,mBAAmB,GACnB,IAAI,CAAC,SAAS,GAAG,CAAC,SAAS,UAAU,cAAc,QAAQ;QAC3D,mBAAmB,GACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS;QAC9B,mBAAmB,GACnB,IAAI,CAAC,UAAU,GAAG;QAClB,oBAAoB,GACpB,IAAI,CAAC,UAAU,GAAG,SAAS,WAAW,cAAc,SAAS;QAC7D,uBAAuB,GACvB,IAAI,CAAC,KAAK,GAAG;QACb,uBAAuB,GACvB,IAAI,CAAC,KAAK,GAAG;QAEb,qBAAqB;QACrB,mBAAmB,GACnB,IAAI,CAAC,YAAY,GAAG;QACpB,mBAAmB,GACnB,IAAI,CAAC,UAAU,GAAG;QAClB,mBAAmB,GACnB,IAAI,CAAC,SAAS,GAAG;QACjB,mBAAmB,GACnB,IAAI,CAAC,IAAI,GAAG,SAAS,WAAW,cAAc,SAAS;QACvD,mBAAmB,GACnB,IAAI,CAAC,MAAM,GAAG,SAAS,cAAc,cAAc,YAAY;IACjE;IAEA,IAAI,YAAY;QACd,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU;IAC1B;IAEA,gCAAgC,GAChC,IAAI,UAAU,SAAS,EAAE;QACvB,YAAY,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI;IAChD;IAEA,IAAI,cAAc;QAChB,OAAO,MAAM,MAAM,IAAI,CAAC,YAAY,EAAE,QAAQ,SAAS,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ;IACvF;IAEA,0BAA0B,GAC1B,IAAI,YAAY,IAAI,EAAE;QACpB,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,2EAA2E;QAC3E,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM;IAC1B;IAEA,IAAI,uBAAuB;QACzB,OAAO,MAAM,IAAI,CAAC,cAAc,EAAE,QAAQ,SAAS;IACrD;IAEA,0BAA0B,GAC1B,IAAI,qBAAqB,IAAI,EAAE;QAC7B,IAAI,CAAC,WAAW,GAAG,AAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,GAAI;IACzE;IAEA,IAAI,WAAW;QACb,OAAO,MAAM,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,GAAG;IAC/D;IAEA,8BAA8B,GAC9B,IAAI,SAAS,QAAQ,EAAE;QACrB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG;IACrC;IAEA,IAAI,oBAAoB;QACtB,OAAO,MAAM,MAAM,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,IAAI,GAAG;IAC1E;IAEA,8BAA8B,GAC9B,IAAI,kBAAkB,QAAQ,EAAE;QAC9B,MAAM,oBAAoB,IAAI,CAAC,iBAAiB;QAChD,IAAI,CAAC,WAAW,GAAG,AAAC,oBAAoB,IAAI,CAAC,iBAAiB,GAAK,oBAAoB;IACzF;IAEA,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA,oCAAoC,GACpC,IAAI,iBAAiB,cAAc,EAAE;QACnC,IAAI,CAAC,WAAW,GAAI,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,GAAG;IAC/F;IAEA,IAAI,WAAW;QACb,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS;IACzB;IAEA,8BAA8B,GAC9B,IAAI,SAAS,OAAO,EAAE;QACpB,UAAU,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI;IACtC;IAEA,IAAI,QAAQ;QACV,OAAO,KAAK,CAAC;IACf;IAEA,kCAAkC,GAClC,IAAI,MAAM,YAAY,EAAE;QACtB,KAAK,CAAC,QAAQ;QACd,IAAI,CAAC,SAAS;IAChB;IAEA;;;GAGC,GACD,MAAM,iBAAiB,CAAC,EAAE;QACxB,sGAAsG;QACtG,YAAY,IAAI;QAChB,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,GAAG;QACtD,mHAAmH;QACnH,yKAAyK;QACzK,+FAA+F;QAC/F,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB;QAC5C,qDAAqD;QACrD,KAAK,IAAI,EAAE,GAAG,GAAG,gBAAgB,UAAU,KAAK;QAChD,4FAA4F;QAC5F,qBAAqB,IAAI;QACzB,iCAAiC;QACjC,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,gBAAgB,IAAI,EAAE;QACxB;QACA,OAAO,IAAI;IACb;IAEA;;;GAGC,GACD,KAAK,iBAAiB,CAAC,EAAE;QACvB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM;QACxB,gFAAgF;QAChF,mEAAmE;QACnE,IAAI,CAAC,kBAAkB,IAAI,CAAC,YAAY,EAAE;YACxC,KAAK,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,gBAAgB,UAAU,KAAK;QAC9D;QACA,IAAI,CAAC,KAAK,CAAC;QACX,8FAA8F;QAC9F,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,IAAI,aAAa,MAAM;YACrB,IAAI,CAAC,MAAM;QACb,OAAO,IAAI,YAAY,CAAC,MAAM,2BAA2B,GAAE,AAAC,SAAU,MAAM,GAAG;YAC7E,2BAA2B,GAAE,AAAC,SAAU,IAAI,CAAC,IAAI;QACnD;QACA,OAAO,IAAI;IACb;IAEA,mBAAmB,GACnB,YAAY;QACV,MAAM,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,MAAM;QAClD,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,IAAI;QAC9D,OAAO,IAAI;IACb;IAEA,mBAAmB,GACnB,QAAQ;QACN,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI;QAC5B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,CAAC,IAAI;QACjB,OAAO,IAAI;IACb;IAEA,mBAAmB,GACnB,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI;QAC7B,IAAI,CAAC,MAAM,GAAG;QACd,8EAA8E;QAC9E,IAAI,IAAI,CAAC,QAAQ,IAAI,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE;YACnD,KAAK,IAAI,EAAE,UAAU,GAAG,GAAG,UAAU,KAAK;QAC5C,OAAO;YACL,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,SAAS,QAAQ,IAAI;gBACrB,OAAO,YAAY,GAAG;gBACtB,IAAI,CAAC,QAAQ,GAAG;YAClB;YACA,IAAI,CAAC,SAAS;YACd,8EAA8E;YAC9E,IAAI,CAAC,UAAU,IAAI;YACnB,OAAO,IAAI;QACb;QACA,OAAO,IAAI;IACb;IAEA,mBAAmB,GACnB,UAAU;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM;IAC7B;IAEA;;;;;GAKC,GACD,KAAK,IAAI,EAAE,gBAAgB,CAAC,EAAE,iBAAiB,CAAC,EAAE;QAChD,oEAAoE;QACpE,YAAY,IAAI;QAChB,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG;QACjB,MAAM,WAAW,IAAI,CAAC,MAAM;QAC5B,IAAI,CAAC,MAAM,GAAG;QACd,uDAAuD;QACvD,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,gBAAgB,UAAU,IAAI;QAChF,OAAO,WAAW,IAAI,GAAG,IAAI,CAAC,MAAM;IACtC;IAEA,mBAAmB,GACnB,YAAY;QACV,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,MAAM,QAAQ,IAAI,CAAC,cAAc;QACjC,MAAM,WAAW,IAAI,CAAC,iBAAiB;QACvC,yEAAyE;QACzE,MAAM,aAAa,UAAU,WAAW,MAAM,WAAW,YAAY;QACrE,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,QAAQ;QAC9E,IAAI,UAAU,UAAU;YACtB,4CAA4C;YAC5C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB;QAC/F,OAAO;YACL,IAAI,CAAC,IAAI,CAAC,AAAC,WAAW,aAAc,IAAI,CAAC,YAAY;QACvD;QACA,IAAI,CAAC,SAAS;QACd,OAAO,IAAI;IACb;IAEA,mBAAmB,GACnB,OAAO;QACL,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS;QAClC,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,mBAAmB,GACnB,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS;QACnC,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,wFAAwF;IAExF,mBAAmB,GACnB,SAAS;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,gBAAgB,IAAI,EAAE,CAAC,uBAAuB,GAAE,QAAU,MAAM,MAAM,IAAI;QAC5E,OAAO;YACL,gBAAgB,IAAI,EAAE;QACxB;QACA,IAAI,CAAC,UAAU,GAAG;QAClB,+CAA+C;QAC/C,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA;;;GAGC,GACD,QAAQ,WAAW,EAAE;QACnB,MAAM,kBAAkB,IAAI,CAAC,QAAQ;QACrC,MAAM,oBAAoB,cAAc;QACxC,IAAI,oBAAoB,mBAAmB,OAAO,IAAI;QACtD,MAAM,YAAY,cAAc;QAChC,MAAM,WAAW,eAAe;QAChC,IAAI,CAAC,QAAQ,GAAG,WAAW,WAAW;QACtC,IAAI,CAAC,iBAAiB,GAAG,WAAW,WAAW,cAAc,IAAI,CAAC,iBAAiB,GAAG;QACtF,IAAI,CAAC,OAAO,IAAI;QAChB,IAAI,CAAC,MAAM,IAAI;QACf,IAAI,CAAC,UAAU,IAAI;QACnB,OAAO,IAAI;IACb;IAED;;;GAGE,GACD,SAAS;QACP,KAAK,IAAI,EAAE,GAAG,GAAG,GAAG,UAAU,IAAI;QAClC,MAAM,KAAmC,IAAI,CAAC,SAAS;QACvD,IAAI,MAAM,GAAG,MAAM,IAAI,GAAG,MAAM,KAAK,IAAI,EAAE,GAAG,MAAM;QACpD,OAAO,IAAI,CAAC,MAAM;IACpB;IAED;;;GAGE,GACD,WAAW;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM;IACxC;IAEA;;;GAGC,GACD,KAAK,WAAW,IAAI,EAAE;QACpB,MAAM,OAAO,IAAI,CAAC,IAAI;QACtB,MAAM,YAAY;YAChB,gFAAgF;YAChF,2DAA2D;YAC3D,IAAI,CAAC,IAAI,GAAG;YACZ,SAAS,IAAI;YACb,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,QAAQ,GAAG;QAClB;QACA,OAAO,IAAI,QAAQ,CAAA;YACjB,IAAI,CAAC,QAAQ,GAAG,IAAM,EAAE;YACxB,oEAAoE;YACpE,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ;YACjC,OAAO,IAAI;QACb;IACF;AAEF;AAGA;;;CAGC,GACD,MAAM,cAAc,CAAA,aAAc,IAAI,MAAM,YAAY,MAAM,GAAG,IAAI;AAKrE,2BAA2B,GAC3B,MAAM,OAAO,CAAA,IAAK;AAElB,yFAAyF;AAEzF;;;;;CAKC,GACD,MAAM,aAAa,CAAC,IAAI,KAAK,MAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,KAAM,IAAI,GAAI,IAAI;AAE/G;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,IAAI,KAAK;IAChC,IAAI,KAAK,GAAG,KAAK,GAAG,UAAU,UAAU,IAAI;IAC5C,GAAG;QACD,WAAW,KAAK,CAAC,KAAK,EAAE,IAAI;QAC5B,WAAW,WAAW,UAAU,KAAK,OAAO;QAC5C,IAAI,WAAW,GAAG;YAChB,KAAK;QACP,OAAO;YACL,KAAK;QACP;IACF,QAAS,IAAI,YAAY,YAAY,EAAE,IAAI,IAAK;IAChD,OAAO;AACT;AAEA;;;;;;CAMC,GAED,MAAM,cAAc,CAAC,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG,GAAK,AAAC,QAAQ,OAAO,QAAQ,MAAO,OACjG,CAAA,IAAK,MAAM,KAAK,MAAM,IAAI,IAC1B,WAAW,gBAAgB,GAAG,KAAK,MAAM,KAAK;AAEhD;;;;;;CAMC,GACD,MAAM,QAAQ,CAAC,QAAQ,EAAE,EAAE;IACzB,MAAM,cAAc,YAAY,OAAO;IACvC,OAAO,CAAA,IAAK,YAAY,MAAM,GAAG,GAAG,KAAK,SAAS,CAAC,IAAI,KAAK;AAC9D;AAEA;;;;;CAKC,GACD,MAAM,SAAS,CAAC,GAAG;IACjB,MAAM,aAAa,KAAK,MAAM;IAC9B,IAAI,CAAC,YAAY,OAAO;IACxB,MAAM,cAAc,aAAa;IACjC,MAAM,WAAW,IAAI,CAAC,EAAE;IACxB,MAAM,UAAU,IAAI,CAAC,YAAY;IACjC,MAAM,UAAU;QAAC;KAAE;IACnB,MAAM,UAAU;QAAC,YAAY;KAAU;IACvC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;QACpC,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,MAAM,aAAa,MAAM,OACzB,mBAAmB,GAAE,AAAC,IAAK,IAAI,GAAG,KAAK,CAAC,OACxC;YAAC;SAAI;QACL,MAAM,QAAQ,UAAU,CAAC,EAAE;QAC3B,MAAM,UAAU,UAAU,CAAC,EAAE;QAC7B,QAAQ,IAAI,CAAC,CAAC,MAAM,WAAW,YAAY,WAAW,MAAM,IAAI;QAChE,QAAQ,IAAI,CAAC,YAAY;IAC3B;IACA,QAAQ,IAAI,CAAC,YAAY;IACzB,QAAQ,IAAI,CAAC;IACb,OAAO,SAAS,WAAW,CAAC;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAI,GAAG,IAAK;YAC9C,MAAM,WAAW,OAAO,CAAC,EAAE;YAC3B,IAAI,KAAK,UAAU;gBACjB,MAAM,QAAQ,OAAO,CAAC,IAAI,EAAE;gBAC5B,MAAM,QAAQ,OAAO,CAAC,IAAI,EAAE;gBAC5B,OAAO,QAAQ,CAAC,OAAO,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,KAAK;YACvE;QACF;QACA,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;IACpC;AACF;AAEA;;;;;CAKC,GACD,MAAM,YAAY,CAAC,SAAS,EAAE,EAAE,aAAa,CAAC;IAC5C,MAAM,SAAS;QAAC;KAAE;IAClB,MAAM,QAAQ,SAAS;IACvB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;QAC9B,MAAM,gBAAgB,MAAM,CAAC,IAAI,EAAE;QACnC,MAAM,UAAU,IAAI;QACpB,MAAM,aAAa,CAAC,IAAI,CAAC,IAAI;QAC7B,MAAM,kBAAkB,UAAU,CAAC,aAAa,OAAO,IAAI,KAAK,MAAM;QACtE,8EAA8E;QAC9E,MAAM,cAAc,UAAU,CAAC,IAAI,UAAU,IAAI,kBAAkB;QACnE,OAAO,IAAI,CAAC,MAAM,aAAa,eAAe;IAChD;IACA,OAAO,IAAI,CAAC;IACZ,OAAO,UAAU;AACnB;AAEA,iFAAiF;AAEjF;;;;CAIC,GAED;;;;CAIC,GAED;;;;;CAKC,GAED;;;;;CAKC,GAED,iEAAiE,GAEjE,MAAM,SAAS,KAAK;AACpB,MAAM,WAAW,KAAK;AACtB,wBAAwB,GACxB,MAAM,cAAc,CAAC,IAAI,IAAI,GAAK,CAAA,IAAK,IAAI,GAAG,CAAC;AAE/C,wDAAwD,GACxD,MAAM,kBAAkB;IACtB,CAAC,YAAY,EAAE;IACf,MAAM,YAAY;IAClB,OAAO,YAAY;IACnB,OAAO,YAAY;IACnB,OAAO,YAAY;IACnB,2BAA2B,GAC3B,MAAM,CAAA,IAAK,IAAI,IAAI,IAAI;IACvB,2BAA2B,GAC3B,MAAM,CAAA,IAAK,IAAI,KAAK,IAAI,IAAI;IAC5B,2BAA2B,GAC3B,MAAM,CAAA,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,MAAM;IACrC,2BAA2B,GAC3B,QAAQ,CAAA;QACN,IAAI,MAAM,IAAI;QACd,MAAO,IAAI,CAAC,CAAC,OAAO,IAAI,GAAG,EAAE,EAAE,IAAI,CAAC,IAAI;QACxC,OAAO,IAAI,IAAI,GAAG,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,KAAK,GAAG;IACnE;IACA,uBAAuB,GACvB,MAAM,CAAC,YAAY,OAAO,GAAK,CAAA,IAAK,CAAC,CAAC,YAAY,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,IAAI;IACpF,0BAA0B,GAC1B,SAAS,CAAC,YAAY,CAAC,EAAE,SAAS,EAAE;QAClC,MAAM,IAAI,MAAM,CAAC,WAAW,GAAG;QAC/B,MAAM,IAAI,MAAM,CAAC,QAAQ,UAAU;QACnC,MAAM,IAAI,AAAC,IAAI,WAAY,KAAK,IAAI;QACpC,MAAM,IAAI,WAAW;QACrB,OAAO,CAAA,IAAK,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,AAAC,IAAI,IAAK,CAAC,IAAI;IACxF;AACF;AAEA;;;;CAIC,GAED,qCAAqC,GACrC,MAAM,YAAY;IAChB,IAAI,CAAA,SAAU,CAAA,IAAK,OAAO;IAC1B,KAAK,CAAA,SAAU,CAAA,IAAK,IAAI,OAAO,IAAI;IACnC,OAAO,CAAA,SAAU,CAAA,IAAK,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK;IAC5E,OAAO,CAAA,SAAU,CAAA,IAAK,IAAI,KAAK,CAAC,IAAI,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,CAAC,IAAI;AACzF;AAEA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,QAAQ,gBAAgB;IAC/C,IAAI,YAAY,CAAC,OAAO,EAAE,OAAO,YAAY,CAAC,OAAO;IACrD,IAAI,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG;QAC7B,MAAM,YAAY,SAAS,CAAC,OAAO,IAAI,OAAO,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC;QAClF,MAAM,WAAyC,YAAY,yBAAyB,GAAE,AAAC,cAAc,CAAC,OAAO,KAAM,cAAc,CAAC,OAAO;QACzI,OAAO,WAAW,YAAY,CAAC,OAAO,GAAG,WAAW;IACtD,OAAO;QACL,MAAM,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACxC,MAAM,WAAuC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;QACrE,OAAO,WAAW,YAAY,CAAC,OAAO,GAAG,YAAY,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ;IAC9E;AACF;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkDC,GAED,MAAM,QAAS,WAAW,GAAG,CAAC;IAC5B,MAAM,OAAO;QAAE;QAAQ;QAAW;QAAO;IAAY;IACrD,IAAK,IAAI,QAAQ,UAAW;QAC1B,IAAK,IAAI,QAAQ,gBAAiB;YAChC,MAAM,SAAS,eAAe,CAAC,KAAK;YACpC,MAAM,WAAW,SAAS,CAAC,KAAK;YAChC,IAAI,CAAC,OAAO,KAAK,GACf,SAAS,eAAe,SAAS,UAAU,SAAS,YACpD,CAAC,GAAG,IAAM,SAAS,yBAAyB,GAAE,AAAC,OAAQ,GAAG,MAC1D,SAAuC;QAE3C;IACF;IACA,OAAqC;AACvC,CAAC;AAED,2CAA2C,GAC3C,MAAM,iBAAiB;IAAE,QAAQ;AAAK;AAEtC;;;CAGC,GACD,MAAM,eAAe,CAAA,OAAQ,MAAM,QAAQ,OACzC,MAAM,QAAQ,gBAAsC,MAAO,OAAO,kBAClE;AAKF,MAAM,qBAAqB,CAAC;AAE5B;;;;;CAKC,GACD,MAAM,uBAAuB,CAAC,cAAc,QAAQ;IAClD,IAAI,cAAc,WAAW,SAAS,EAAE;QACtC,MAAM,IAAI,gBAAgB,GAAG,CAAC;QAC9B,OAAO,IAAI,IAAI;IACjB,OAAO,IACL,cAAc,WAAW,GAAG,IAG3B,cAAc,WAAW,SAAS,IAAK,MAAM,WAAW,gBAAgB,sBAAsB,GAAE,AAAC,OAAQ,KAAK,EAC/G;QACA,MAAM,qBAAqB,kBAAkB,CAAC,aAAa;QAC3D,IAAI,oBAAoB;YACtB,OAAO;QACT,OAAO;YACL,MAAM,gBAAgB,eAAe,YAAY,gBAAgB;YACjE,kBAAkB,CAAC,aAAa,GAAG;YACnC,OAAO;QACT;IACF,OAAO;QACL,OAAO;IACT;AACF;AAKA,MAAM,gBAAgB;IAAE,OAAO;IAAG,OAAO,MAAM;IAAI,QAAQ;AAAI;AAC/D,MAAM,uBAAuB,CAAC;AAE9B;;;;;;CAMC,GACD,MAAM,mBAAmB,CAAC,IAAI,iBAAiB,MAAM,QAAQ,KAAK;IAChE,MAAM,cAAc,gBAAgB,CAAC;IACrC,MAAM,gBAAgB,gBAAgB,CAAC;IACvC,IAAI,gBAAgB,CAAC,KAAK,WAAW,IAAI,IAAI,gBAAgB,MAAM;QACjE,OAAO;IACT;IACA,MAAM,YAAY,gBAAgB,cAAc;IAChD,MAAM,SAAS,oBAAoB,CAAC,UAAU;IAC9C,IAAI,CAAC,MAAM,WAAW,CAAC,OAAO;QAC5B,gBAAgB,CAAC,GAAG;IACtB,OAAO;QACL,IAAI;QACJ,IAAI,eAAe,eAAe;YAChC,iBAAiB,gBAAgB,aAAa,CAAC,YAAY,GAAG,aAAa,CAAC,KAAK;QACnF,OAAO;YACL,MAAM,WAAW;YACjB,MAAM,SAAkC,GAAG,SAAS;YACpD,MAAM,aAAa,GAAG,UAAU;YAChC,MAAM,WAAW,AAAC,cAAe,eAAe,MAAQ,aAAa,IAAI,IAAI;YAC7E,SAAS,WAAW,CAAC;YACrB,MAAM,UAAU,OAAO,KAAK;YAC5B,QAAQ,KAAK,GAAG,WAAW;YAC3B,MAAM,mBAAmB,wBAAwB,GAAE,AAAC,OAAQ,WAAW,IAAI;YAC3E,QAAQ,KAAK,GAAG,WAAW;YAC3B,MAAM,eAAe,wBAAwB,GAAE,AAAC,OAAQ,WAAW,IAAI;YACvE,MAAM,SAAS,mBAAmB;YAClC,SAAS,WAAW,CAAC;YACrB,iBAAiB,SAAS;QAC5B;QACA,gBAAgB,CAAC,GAAG;QACpB,oBAAoB,CAAC,UAAU,GAAG;IACpC;IACA,gBAAgB,CAAC,KAAK,WAAW,IAAI;IACrC,gBAAgB,CAAC,GAAG;IACpB,OAAO;AACT;AAKA;;;;CAIC,GACD,MAAM,oBAAoB,CAAA;IACxB,sDAAsD;IACtD,IAAI,WAAW,YAAY,EAAE;QAC3B,gBAAgB,YAAY,mBAAmB;IACjD,OAAO;QACL,MAAM,YAAuC;QAC7C,UAAU,KAAK;QACf,gBAAgB,WAAW,CAAC,kBAAkB,GAAE;YAC9C,MAAM,gBAAgB,MAAM,QAAQ;YACpC,MAAM,cAAc,MAAM,MAAM;YAChC,IAAI,WAAW,CAAC,YAAY,EAAE;gBAC5B,MAAM,cAAc,sBAAsB,GAAE,AAAC,YAAa,KAAK;gBAC/D,MAAM,uBAAuB,UAAU,aAAa,CAAC,cAAc;gBACnE,IAAI,MAAM,UAAU,KAAK,WAAW,SAAS,EAAE;oBAC7C,MAAM,mBAAmB,WAAW,CAAC,iBAAiB;oBACtD,IAAI,MAAM,yBAAyB,yBAAyB,aAAa;wBACvE,OAAO,gBAAgB,CAAC,cAAc;oBACxC,OAAO;wBACL,gBAAgB,CAAC,cAAc,GAAG;oBACpC;oBACA,IAAI,MAAM,iBAAiB,EAAE;wBAC3B,IAAI,CAAC,OAAO,IAAI,CAAC,kBAAkB,MAAM,EAAE;4BACzC,YAAY,cAAc,CAAC;wBAC7B,OAAO;4BACL,IAAI,MAAM;4BACV,IAAK,IAAI,OAAO,iBAAkB;gCAChC,OAAO,yBAAyB,CAAC,IAAI,GAAG,gBAAgB,CAAC,IAAI,GAAG;4BAClE;4BACA,YAAY,SAAS,GAAG;wBAC1B;oBACF;gBACF,OAAO;oBACL,IAAI,MAAM,yBAAyB,yBAAyB,aAAa;wBACvE,YAAY,cAAc,CAAC;oBAC7B,OAAO;wBACL,WAAW,CAAC,cAAc,GAAG;oBAC/B;gBACF;gBACA,IAAI,UAAU,KAAK,KAAK,OAAO;oBAC7B,UAAU,OAAO,CAAC,OAAO,CAAC,CAAA;wBACxB,IAAI,EAAE,YAAY,IAAI,EAAE,YAAY,CAAC,aAAa,aAAa;4BAC7D,EAAE,eAAe,CAAC;wBACpB;oBAAW;gBACf;YACF;QACF;IACF;IACA,OAAO;AACT;AAEA,mGAAmG;AACnG,8GAA8G;AAC9G,MAAM,mBAAmB;AACzB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;IAAE,MAAM;AAAK;AACrC,MAAM,uBAAuB;IAAC;CAAK;AACnC,MAAM,qBAAqB;IAAC;IAAM;CAAK;AACvC,0BAA0B,GAC1B,MAAM,kBAAkB;IAAE,IAAI;AAAK;AAEnC,IAAI,UAAU;AACd,IAAI;AACJ,6CAA6C,GAC7C,IAAI;AAEJ;;;;CAIC,GACD,MAAM,oBAAoB,CAAC,WAAW;IACpC,4BAA4B,GAC5B,MAAM,aAAa,CAAC;IACpB,IAAI,MAAM,YAAY;QACpB,MAAM,gBAAgB,EAAE,CAAC,MAAM,IAAI,8BAA8B,GAAE,AAAC,UAAW,GAAG,CAAC,CAAA,MAAO,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC;QACpH,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAI,GAAG,IAAK;YACpD,MAAM,WAAW,aAAa,CAAC,EAAE;YACjC,MAAM,YAAY,8BAA8B,GAAE,AAAC,UAAW,GAAG,CAAC,CAAA;gBAChE,0BAA0B,GAC1B,MAAM,SAAS,CAAC;gBAChB,IAAK,IAAI,KAAK,IAAK;oBACjB,MAAM,WAAyC,GAAG,CAAC,EAAE;oBACrD,IAAI,MAAM,IAAI;wBACZ,IAAI,MAAM,UAAU;4BAClB,OAAO,EAAE,GAAG;wBACd;oBACF,OAAO;wBACL,MAAM,CAAC,EAAE,GAAG;oBACd;gBACF;gBACA,OAAO;YACT;YACA,UAAU,CAAC,SAAS,GAAmC;QACzD;IAEF,OAAO;QACL,MAAM,gBAAsC,SAAS,WAAW,QAAQ,EAAE,QAAQ,QAAQ,CAAC,QAAQ;QACnG,MAAM,OAAO,OAAO,IAAI,CAAC,WACxB,GAAG,CAAC,CAAA;YAAS,OAAO;gBAAC,GAAG,WAAW,OAAO;gBAAK,GAAG,SAAS,CAAC,IAAI;YAAA;QAAE,GAClE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC;QACzB,KAAK,OAAO,CAAC,CAAA;YACX,MAAM,SAAS,IAAI,CAAC;YACpB,MAAM,OAAO,IAAI,CAAC;YAClB,IAAK,IAAI,QAAQ,KAAM;gBACrB,IAAI,MAAM,OAAO;oBACf,IAAI,YAAiC,UAAU,CAAC,KAAK;oBACrD,IAAI,CAAC,WAAW,YAAY,UAAU,CAAC,KAAK,GAAG,EAAE;oBACjD,MAAM,WAAW,SAAS;oBAC1B,IAAI,SAAS,UAAU,MAAM;oBAC7B,IAAI,UAAU,SAAS,CAAC,SAAS,EAAE;oBACnC,MAAM,SAAS;wBAAE,IAAI,IAAI,CAAC,KAAK;oBAAC;oBAChC,IAAI,cAAc;oBAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;wBAC/B,eAAe,SAAS,CAAC,EAAE,CAAC,QAAQ;oBACtC;oBACA,IAAI,WAAW,GAAG;wBAChB,OAAO,IAAI,GAAG,QAAQ,EAAE;oBAC1B;oBACA,IAAI,KAAK,IAAI,EAAE;wBACb,OAAO,IAAI,GAAG,KAAK,IAAI;oBACzB;oBACA,OAAO,QAAQ,GAAG,WAAW,CAAC,SAAS,cAAc,CAAC;oBACtD,UAAU,IAAI,CAAC;gBACjB;YACF;YACA,OAAO;QACT;QAEA,IAAK,IAAI,QAAQ,WAAY;YAC3B,MAAM,YAAiC,UAAU,CAAC,KAAK;YACvD,IAAI;YACJ,sBAAsB;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;gBAChD,MAAM,OAAO,SAAS,CAAC,EAAE;gBACzB,0CAA0C;gBAC1C,MAAM,cAAc,KAAK,IAAI;gBAC7B,KAAK,IAAI,GAAG,WAAW,WAAW;gBAClC,WAAW;YACX,gCAAgC;YAChC,sDAAsD;YACtD,8FAA8F;YAC9F,IAAI;YACN;YACA,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE;gBAC1B,UAAU,KAAK;YACjB;QACF;IAEF;IAEA,OAAO;AACT;AAEA,MAAM,oBAAoB;IACxB;;;;;;;;GAQC,GACD,YACE,OAAO,EACP,UAAU,EACV,MAAM,EACN,cAAc,EACd,UAAU,KAAK,EACf,QAAQ,CAAC,EACT,SAAS,CAAC,CACV;QAEA,KAAK,CAA4C,YAAa,QAAQ;QAEtE,MAAM,gBAAgB,gBAAgB;QACtC,MAAM,gBAAgB,cAAc,MAAM;QAE1C,mHAAmH;QAEnH,MAAM,WAAW,4BAA4B,GAAE,AAAC,WAAY,SAAS;QACrE,MAAM,SAAwC,WAAW,aAAa,kBAAmD,UAAW,aAAa,cAAc;QAE/J,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,QAAQ,EACT,GAAG;QAEJ,MAAM,eAAe,SAAS,OAAO,QAAQ,GAAG,QAAQ,QAAQ;QAChE,MAAM,oBAAoB,SAAS,cAAc,aAAa,YAAY;QAC1E,MAAM,WAAW,oBAAoB,aAAa,qBAAqB;QACvE,MAAM,YAAY,CAAC,MAAM,SAAS,CAAC,MAAM,mBAAmB,GAAE,AAAC,KAAM,IAAI;QACzE,MAAM,UAAU,YAAY,mBAAmB,GAAE,AAAC,KAAM,IAAI,GAAG,SAAS,MAAM,WAAW,WAAW,aAAa,IAAI;QACrH,MAAM,YAAY,YAAY,mBAAmB,GAAE,AAAC,KAAM,QAAQ,GAAG,SAAS,UAAU,aAAa,QAAQ;QAC7G,MAAM,SAAS,SAAS,OAAO,aAAa,KAAK;QACjD,MAAM,YAAY,YAAY,aAAa,QAAQ;QACnD,oIAAoI;QACpI,MAAM,eAAe,MAAM,gBAAgB,iBAAiB,IAAI,iBAAiB,IAAI,GAAG,CAAC,MAAM,eAAe,cAAc,aAAa,WAAW;QACpJ,8FAA8F;QAC9F,MAAM,mBAAmB,CAAC;QAC1B,2CAA2C;QAC3C,MAAM,qBAAqB,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS,OAAO,OAAO,GAAG,CAAC;QAEtE,IAAI,oBAAoB;QACxB,IAAI,iBAAiB;QACrB,IAAI,2BAA2B;QAC/B,IAAI,sBAAsB;QAE1B,IAAK,IAAI,cAAc,GAAG,cAAc,eAAe,cAAe;YAEpE,MAAM,SAAS,aAAa,CAAC,YAAY;YACzC,MAAM,KAAK,SAAS;YACpB,MAAM,KAAK,UAAU;YAErB,IAAI,0BAA0B;YAC9B,IAAI,2BAA2B;YAE/B,IAAK,IAAI,KAAK,OAAQ;gBAEpB,IAAI,MAAM,IAAI;oBAEZ,MAAM,YAAY,aAAa,QAAQ;oBAEvC,MAAM,WAAW,qBAAqB,GAAG,QAAQ;oBAEjD,IAAI,YAAY,MAAM,CAAC,EAAE;oBAEzB,MAAM,mBAAmB,MAAM;oBAE/B,IAAI,WAAW,CAAC,kBAAkB;wBAChC,kBAAkB,CAAC,EAAE,GAAG;wBACxB,kBAAkB,CAAC,EAAE,GAAG;wBACxB,YAAY;oBACd;oBAEA,2FAA2F;oBAC3F,sDAAsD;oBACtD,uFAAuF;oBACvF,uIAAuI;oBACvI,IAAI,kBAAkB;wBACpB,MAAM,cAAc,kBAAkB,GAAE,AAAC,UAAW,MAAM;wBAC1D,MAAM,mBAAmB,CAAC,MAAM,SAAS,CAAC,EAAE;wBAC5C,mCAAmC;wBACnC,IAAI,gBAAgB,KAAK,kBAAkB;4BACzC,gBAAgB,EAAE,GAAyD;4BAC3E,oBAAoB,CAAC,EAAE,GAAG;4BAC1B,YAAY;wBACd,mCAAmC;wBACnC,OAAO,IAAI,cAAc,KAAK,kBAAkB;4BAC9C,YAAY,EAAE;4BACd,2BAA2B,GAAE,AAAC,UAAW,OAAO,CAAC,CAAC,GAAG;gCACnD,IAAI,CAAC,GAAG;oCACN,kBAAkB,CAAC,EAAE,GAAG;gCAC1B,OAAO,IAAI,MAAM,GAAG;oCAClB,kBAAkB,CAAC,EAAE,GAAG;oCACxB,UAAU,IAAI,CAAC;gCACjB,OAAO;oCACL,UAAU,IAAI,CAAC;gCACjB;4BACF;wBACF,OAAO;4BACL,YAAiD;wBACnD;oBACF,OAAO;wBACL,oBAAoB,CAAC,EAAE,GAAG;wBAC1B,YAAY;oBACd;oBAEA,IAAI,WAAW;oBACf,IAAI,YAAY;oBAChB,IAAI,4BAA4B;oBAChC,IAAI,yBAAyB;oBAC7B,IAAI,aAAa;oBAEjB,IAAK,IAAI,IAAI,UAAU,MAAM,EAAE,aAAa,GAAG,aAAc;wBAE3D,MAAM,WAAW,SAAS,CAAC,WAAW;wBAEtC,IAAI,MAAM,WAAW;4BACnB,MAAM;wBACR,OAAO;4BACL,gBAAgB,EAAE,GAAkC;4BACpD,MAAM;wBACR;wBAEA,gBAAgB,IAAI,GAAG;wBAEvB,MAAM,kBAAkB,iBAAiB,IAAI,EAAE,EAAE,QAAQ,IAAI,IAAI;wBAEjE,IAAI;wBACJ,0EAA0E;wBAC1E,IAAI,MAAM,oBAAoB,CAAC,MAAM,gBAAgB,EAAE,GAAG;4BACxD,MAAM;4BACN,eAAe,gBAAgB,EAAE;wBACnC,OAAO;4BACL,eAAe;wBACjB;wBACA,MAAM,iBAAiB,iBAAiB,IAAI,IAAI,EAAE,QAAQ,IAAI;wBAC9D,MAAM,YAAY,IAAI,IAAI;wBAC1B,MAAM,YAAY,CAAC,MAAM,cAAc,CAAC,MAAM,mBAAmB,GAAE,AAAC,UAAW,IAAI;wBACnF,gKAAgK;wBAChK,MAAM,cAAc,YAAY,mBAAmB,GAAE,AAAC,UAAW,IAAI,GAAG,aAAa;wBACrF,iFAAiF;wBACjF,MAAM,gBAAgB,YAAY,mBAAmB,GAAE,AAAC,UAAW,QAAQ,GAAG,iBAAiB,SAAS,IAAI,QAAQ,EAAG,IAAI,IAAI,iBAAiB,WAAW,QAAQ,IAAI,MAAM,IAAI,YAAa,QAAQ,IAAI;wBAC1M,gEAAgE;wBAChE,MAAM,aAAa,iBAAiB,SAAS,IAAI,KAAK,EAAG,CAAC,aAAa,SAAS,IAAK,QAAQ,IAAI;wBACjG,MAAM,sBAAsB,iBAAiB,SAAS,IAAI,WAAW,EAAE,eAAe,QAAQ,IAAI;wBAClG,MAAM,mBAAmB,MAAM,uBAAuB,sBAAsB,gBAAgB,CAAC,oBAAoB;wBACjH,uHAAuH;wBACvH,MAAM,gBAAgB,IAAI,QAAQ,IAAI;wBACtC,MAAM,eAAe,CAAC,MAAM;wBAC5B,MAAM,aAAa,CAAC,MAAM;wBAC1B,MAAM,gBAAgB,MAAM;wBAC5B,MAAM,gBAAgB,iBAAkB,gBAAgB;wBACxD,MAAM,iBAAiB,YAAY,yBAAyB,aAAa;wBACzE,MAAM,oBAAoB,qBAAqB;wBAE/C,2GAA2G;wBAC3G,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,aAAa,GAAG,sBAAsB;wBAEnF,IAAI,cAAc;wBAElB,IAAI,qBAAqB,iBAAiB,IAAI,EAAE;4BAC9C,IAAI,CAAC,UAAU,WAAW,iBAAiB,QAAQ;4BACnD,IAAI,cAAc,SAAS,KAAK;4BAChC,oGAAoG;4BACpG,MAAO,eAAe,CAAC,YAAY,aAAa,IAAI,YAAY,kBAAkB,IAAI,kBAAmB;gCACvG,cAAc;gCACd,cAAc,YAAY,QAAQ;gCAClC,mHAAmH;gCACnH,IAAI,eAAe,YAAY,kBAAkB,IAAI,mBAAmB;oCACtE,MAAO,YAAa;wCAClB,cAAc;wCACd,+GAA+G;wCAC/G,cAAc,YAAY,QAAQ;oCACpC;gCACF;4BACF;wBACF;wBAEA,mBAAmB;wBACnB,IAAI,eAAe;4BACjB,kBAAkB,gBAAgB,iBAAiB,YAAY,CAAC,EAAE,EAAE,QAAQ,IAAI,MAAM,gBAAgB;4BACtG,kBAAkB,gBAAgB,iBAAiB,YAAY,CAAC,EAAE,EAAE,QAAQ,IAAI,IAAI,mBAAmB,cAAc;4BACrH,IAAI,iBAAiB,CAAC,KAAK,WAAW,MAAM,EAAE;gCAC5C,IAAI,aAAa;oCACf,IAAI,YAAY,UAAU,KAAK,WAAW,IAAI,EAAE;wCAC9C,iBAAiB,CAAC,GAAG,WAAW,IAAI;wCACpC,iBAAiB,CAAC,GAAG,YAAY,KAAK;oCACxC;gCACF,OAAO;oCACL,kBACE,2BAA2B,QAAQ,UAAU,WAAW,mBACxD;oCAEF,IAAI,wBAAwB,CAAC,KAAK,WAAW,IAAI,EAAE;wCACjD,iBAAiB,CAAC,GAAG,WAAW,IAAI;wCACpC,iBAAiB,CAAC,GAAG,wBAAwB,CAAC;oCAChD;gCACF;4BACF;wBACF,OAAO;4BACL,IAAI,YAAY;gCACd,kBAAkB,cAAc;4BAClC,OAAO;gCACL,IAAI,WAAW;oCACb,oBAAoB,WAAW;gCACjC,OAAO;oCACL,sIAAsI;oCACtI,kBAAkB,UAAU,eAAe,YAAY,MAAM,CAAC,MAAM,KAAK,SAAS,YAAY,MAAM,GACpG,2BAA2B,QAAQ,UAAU,WAAW,mBAAmB;gCAC7E;4BACF;4BACA,IAAI,cAAc;gCAChB,kBAAkB,gBAAgB;4BACpC,OAAO;gCACL,IAAI,WAAW;oCACb,oBAAoB,WAAW;gCACjC,OAAO;oCACL,kBAAkB,UAAU,eAAe,YAAY,MAAM,CAAC,MAAM,KAAK,SAAS,YAAY,MAAM,GACpG,sIAAsI;oCACtI,2BAA2B,QAAQ,UAAU,WAAW,mBAAmB;gCAC7E;4BACF;wBACF;wBAEA,kBAAkB;wBAClB,IAAI,iBAAiB,CAAC,EAAE;4BACtB,iBAAiB,CAAC,GAAG,iBACnB,CAAC,cAAc,kBACb,2BAA2B,QAAQ,UAAU,WAAW,mBACxD,yBACA,CAAC,GAAG,YAAY,SAAS,EAC3B,iBAAiB,CAAC,EAClB,iBAAiB,CAAC;wBAEtB;wBAEA,IAAI,eAAe,CAAC,EAAE;4BACpB,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,CAAC,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC;wBAC5F;wBAEA,2EAA2E;wBAC3E,IAAI,iBAAiB,CAAC,KAAK,eAAe,CAAC,EAAE;4BAC3C,IAAI,iBAAiB,CAAC,KAAK,WAAW,OAAO,IAAI,eAAe,CAAC,KAAK,WAAW,OAAO,EAAE;gCACxF,MAAM,eAAe,iBAAiB,CAAC,KAAK,WAAW,OAAO,GAAG,mBAAmB;gCACpF,MAAM,kBAAkB,iBAAiB,CAAC,KAAK,WAAW,OAAO,GAAG,iBAAiB;gCACrF,gBAAgB,CAAC,GAAG,WAAW,OAAO;gCACtC,gBAAgB,CAAC,GAAG,WAAW,aAAa,CAAC;gCAC7C,gBAAgB,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,CAAC,IAAM,gBAAgB,CAAC;4BAChE,OAAO,IAAI,iBAAiB,CAAC,KAAK,WAAW,IAAI,IAAI,eAAe,CAAC,KAAK,WAAW,IAAI,EAAE;gCACzF,MAAM,YAAY,iBAAiB,CAAC,KAAK,WAAW,IAAI,GAAG,mBAAmB;gCAC9E,MAAM,eAAe,iBAAiB,CAAC,KAAK,WAAW,IAAI,GAAG,iBAAiB;gCAC/E,aAAa,CAAC,GAAG,WAAW,IAAI;gCAChC,aAAa,CAAC,GAAG,UAAU,CAAC;4BAC9B,OAAO,IAAI,iBAAiB,CAAC,KAAK,WAAW,KAAK,IAAI,eAAe,CAAC,KAAK,WAAW,KAAK,EAAE;gCAC3F,MAAM,aAAa,iBAAiB,CAAC,KAAK,WAAW,KAAK,GAAG,mBAAmB;gCAChF,MAAM,gBAAgB,iBAAiB,CAAC,KAAK,WAAW,KAAK,GAAG,iBAAiB;gCACjF,cAAc,CAAC,GAAG,WAAW,KAAK;gCAClC,cAAc,CAAC,GAAG,WAAW,CAAC;gCAC9B,cAAc,CAAC,GAAG;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE;4BAChC;wBACF;wBAEA,kBAAkB;wBAClB,IAAI,iBAAiB,CAAC,KAAK,eAAe,CAAC,EAAE;4BAC3C,IAAI,iBAAiB,eAAe,CAAC,GAAG,mBAAmB;4BAC3D,iBAAiB,iBAA0C,QAAS,gBAAgB,eAAe,CAAC,GAAG,eAAe,CAAC,GAAG,iBAAiB,CAAC,EAAE;wBAC9I,QAAQ;wBACR,oEAAoE;wBACtE;wBAEA,sCAAsC;wBACtC,IAAI,eAAe,CAAC,IAAI,iBAAiB,CAAC,IAAK,eAAe,CAAC,CAAC,MAAM,KAAK,iBAAiB,CAAC,CAAC,MAAM,EAAG;4BACrG,MAAM,eAAe,iBAAiB,CAAC,CAAC,MAAM,GAAG,eAAe,CAAC,CAAC,MAAM,GAAG,mBAAmB;4BAC9F,MAAM,gBAAgB,iBAAiB,mBAAmB,iBAAiB;4BAC3E,0EAA0E;4BAC1E,cAAc,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAM,MAAM,cAAc,CAAC,CAAC,EAAE,IAAI,IAAI,cAAc,CAAC,CAAC,EAAE;4BACjG,cAAc,CAAC,GAAG,WAAW,aAAa,CAAC;wBAC7C;wBAEA,gBAAgB;wBAEhB,+DAA+D;wBAC/D,MAAM,sBAAsB,MAAM,CAAC,iBAAiB,UAAU;wBAE9D,kBAAkB,GAClB,MAAM,QAAQ;4BACZ,QAAQ,IAAI;4BACZ,IAAI;4BACJ,UAAU;4BACV,QAAQ;4BACR,QAAQ;4BACR,OAAO,gBAAgB,IAAI;4BAC3B,OAAO,aAAa;4BACpB,cAAc,WAAW,iBAAiB,CAAC;4BAC3C,YAAY,WAAW,eAAe,CAAC;4BACvC,UAAU,WAAW,eAAe,CAAC;4BACrC,aAAa,iBAAiB,CAAC;4BAC/B,WAAW,eAAe,CAAC;4BAC3B,UAAU,WAAW,iBAAiB,CAAC;4BACvC,SAAS,iBAAiB,CAAC;4BAC3B,OAAO,eAAe,CAAC;4BACvB,WAAW;4BACX,cAAc;4BACd,YAAY;4BACZ,QAAQ,CAAC;4BACT,iBAAiB;4BACjB,iBAAiB;4BACjB,oBAAoB;4BACpB,sDAAsD;4BACtD,YAAY;4BACZ,YAAY,eAAe,CAAC;4BAC5B,cAAc;4BACd,eAAe;4BACf,eAAe;4BACf,mBAAmB;4BACnB,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,UAAU;4BACV,OAAO;4BACP,OAAO;wBACT;wBAEA,IAAI,qBAAqB,iBAAiB,IAAI,EAAE;4BAC9C,aAAa,OAAO;wBACtB;wBAEA,IAAI,MAAM,4BAA4B;4BACpC,4BAA4B,MAAM,UAAU;wBAC9C;wBACA,+DAA+D;wBAC/D,yBAAyB,MAAM,iBAAiB,qBAAqB;wBACrE,YAAY;wBACZ;wBAEA,SAAS,IAAI,EAAE;oBAEjB;oBAEA,4DAA4D;oBAE5D,IAAI,MAAM,mBAAmB,4BAA4B,gBAAgB;wBACvE,iBAAiB;oBACnB;oBAEA,IAAI,MAAM,sBAAsB,yBAAyB,mBAAmB;wBAC1E,oBAAoB;oBACtB;oBAEA,8DAA8D;oBAC9D,IAAI,cAAc,WAAW,SAAS,EAAE;wBACtC,0BAA0B,2BAA2B;wBACrD,2BAA2B;oBAC7B;gBAEF;YAEF;YAEA,2FAA2F;YAC3F,IAAI,CAAC,MAAM,0BAA0B;gBACnC,IAAI,IAAI;gBACR,gBAAgB,IAAI,EAAE,CAAC,kBAAkB,GAAE;oBACzC,IAAI,KAAK,2BAA2B,IAAI,0BAA0B;wBAChE,MAAM,iBAAiB,GAAG;wBAC1B,IAAI,MAAM,YAAY,KAAK,iBAAiB,KAAK,EAAE;4BACjD,gBAAgB,SAAS,SAAS,EAAE,CAAC,kBAAkB,GAAE;gCACvD,IAAI,cAAc,EAAE,KAAK,MAAM,EAAE,EAAE;oCACjC,cAAc,iBAAiB,GAAG;gCACpC;4BACF;wBACF;oBACF;oBACA;gBACF;YACF;QAEF;QAEA,IAAI,CAAC,eAAe;YAClB,QAAQ,IAAI,CAAC,CAAC,6GAA6G,CAAC;QAC9H;QAEA,IAAI,gBAAgB;YAClB,gBAAgB,IAAI,EAAE,CAAC,kBAAkB,GAAE;gBACzC,6HAA6H;gBAC7H,IAAI,CAAC,CAAC,MAAM,UAAU,GAAG,MAAM,MAAM,GAAG;oBACtC,MAAM,MAAM,IAAI;gBAClB;gBACA,MAAM,UAAU,IAAI;YACtB;YACA,qBAAqB;QACvB,OAAO;YACL,iBAAiB;QACnB;QAEA,uFAAuF;QACvF,qFAAqF;QACrF,IAAI,CAAC,mBAAmB;YACtB,oBAAoB;YACpB,IAAI,CAAC,cAAc,GAAG;QACxB;QACA,yBAAyB,GACzB,IAAI,CAAC,OAAO,GAAG;QACf,mBAAmB,GACnB,IAAI,CAAC,QAAQ,GAAG,sBAAsB,WAAW,WAAW,cAAc,AAAC,CAAC,oBAAoB,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,GAAI,IAAI,CAAC,UAAU,KAAK;QAC9J,2BAA2B,GAC3B,IAAI,CAAC,QAAQ,GAAG,YAAY,aAAa,QAAQ;QACjD,2BAA2B,GAC3B,IAAI,CAAC,KAAK,GAAG;QACb,mBAAmB,GACnB,IAAI,CAAC,MAAM,GAAG;QACd,wNAAwN;QACxN,6CAA6C;QAC7C,+CAA+C;QAC/C,mBAAmB,GACnB,IAAI,CAAC,iBAAiB,GAAG;QACzB,eAAe,GACf,IAAI,CAAC,aAAa,GAAG;QAErB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,qBAAqB,IAAI,CAAC,QAAQ,CAAC,IAAI;IAChE;IAEA;;;GAGC,GACD,QAAQ,WAAW,EAAE;QACnB,MAAM,kBAAkB,IAAI,CAAC,QAAQ;QACrC,IAAI,oBAAoB,cAAc,cAAc,OAAO,IAAI;QAC/D,MAAM,YAAY,cAAc;QAChC,kFAAkF;QAClF,gBAAgB,IAAI,EAAE,CAAC,kBAAkB,GAAE;YACzC,+DAA+D;YAC/D,MAAM,eAAe,GAAG,cAAc,MAAM,eAAe,GAAG;YAC9D,MAAM,eAAe,GAAG,cAAc,MAAM,eAAe,GAAG;YAC9D,MAAM,YAAY,IAAI;YACtB,MAAM,UAAU,IAAI;YACpB,MAAM,kBAAkB,IAAI;QAC9B;QACA,OAAO,KAAK,CAAC,QAAQ;IACvB;IAEA;;GAEC,GACD,UAAU;QACR,gBAAgB,IAAI,EAAE,CAAC,kBAAkB,GAAE;YACzC,MAAM,UAAU,2BAA2B,MAAM,MAAM,EAAE,MAAM,QAAQ,EAAE,MAAM,UAAU;YACzF,kBAAkB,SAAS;YAC3B,MAAM,YAAY,GAAG,WAAW,wBAAwB,CAAC;YACzD,MAAM,WAAW,GAAG,wBAAwB,CAAC;YAC7C,IAAI,MAAM,KAAK,EAAE;gBACf,kBAAkB,MAAM,KAAK,IAAI;gBACjC,MAAM,UAAU,GAAG,WAAW,eAAe,CAAC;gBAC9C,MAAM,QAAQ,GAAG,WAAW,eAAe,CAAC;gBAC5C,MAAM,SAAS,GAAG,eAAe,CAAC;YACpC;QACF;QACA,OAAO,IAAI;IACb;IAEA;;;GAGC,GACD,SAAS;QACP,KAAK,CAAC;QACN,OAAO,kBAAkB,IAAI;IAC/B;IAEA;;;GAGC,GACD,KAAK,QAAQ,EAAE;QACb,OAAO,KAAK,CAAC,KAAK;IACpB;AAEF;AAEA;;;;CAIC,GACD,MAAM,UAAU,CAAC,SAAS,aAAe,IAAI,YAAY,SAAS,YAAY,MAAM,GAAG,OAAO,IAAI;AAKlG;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC,IAAI,UAAU,GAAG;IACvC,MAAM,SAAS,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,IAAK,OAAO,IAAI,CAAC,GAAG,IAAI;IACtD,OAAO,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC;AAEA,MAAM,oBAAoB;IACxB,IAAI;IACJ,KAAK;IACL,OAAO;AACT;AAEA,MAAM,aAAa,WAAW,GAAE,CAAC;IAC/B,MAAM,OAAO,CAAC;IACd,IAAK,IAAI,QAAQ,UAAW,IAAI,CAAC,KAAK,GAAG,CAAA,IAAK,SAAS,CAAC,KAAK,CAAC,YAAY;IAC1E,OAAqD;AACvD,CAAC;AAED;;;CAGC,GACD,MAAM,mBAAmB,CAAC;IACxB,IAAI,aAAa,iBAAiB,CAAC,KAAK;IACxC,IAAI,YAAY,OAAO;IACvB,aAAa;IACb,IAAI,MAAM,OAAO;QACf,IACE,iBAAiB,MAAM,aACvB,iBAAiB,MAAM,aACvB,iBAAiB,MAAM,YACvB,iBAAiB,MAAM,SACvB;YACA,aAAa;QACf,OAAO,IAAI,iBAAiB,MAAM,WAAW;YAC3C,aAAa,YAAY;QAC3B,OAAO;YACL,MAAM,SAAS,gBAAgB,MAAM,YAAY;YACjD,IAAI,MAAM,SAAS,aAAa,WAAW,OAAO,WAAW,eAAe;QAC9E;QACA,iBAAiB,CAAC,KAAK,GAAG;IAC5B,OAAO,IAAI,MAAM,OAAO;QACtB,MAAM,SAAS,eAAe;QAC9B,IAAI,QAAQ,aAAa;IAC3B,OAAO,IAAI,mBAAmB,GAAE,AAAC,KAAM,IAAI,EAAE;QAC3C,aAAa,eAAe,mBAAmB,GAAE,AAAC,KAAM,IAAI;IAC9D;IACA,OAAO;AACT;AAEA;;CAEC,GAED;;;;;;CAMC,GAED;;CAEC,GAED;;CAEC,GAED;;;;;;;;CAQC,GAED;;;;;;;;;;;;CAYC,GAED;;CAEC,GAED,MAAM,uBAAuB;IAAC;IAAK;IAAK;CAAI;AAC5C,MAAM,4BAA4B;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;OACG;CACJ;AAED,MAAM,4BAA4B;OAAI;OAAyB,gBAAgB,MAAM,CAAC,CAAA,IAAK;YAAC;YAAK;YAAK;SAAI,CAAC,IAAI,CAAC,CAAA,OAAQ,EAAE,QAAQ,CAAC;CAAQ;AAE3I,yIAAyI;AACzI,IAAI,iCAAiC,aAAa,CAAC,MAAM,QAAQ,CAAC,OAAO,cAAc,CAAC,IAAI,CAAC,KAAK,mBAAmB;AAErH,MAAM,+BAA+B;IACnC,IAAI,gCAAgC;IACpC,gBAAgB,OAAO,CAAC,CAAA;QACtB,MAAM,SAAS,iBAAiB,GAAG;QACnC,MAAM,UAAU,iBAAiB,GAAG;QACpC,MAAM,WAAW,iBAAiB,GAAG;QACrC,MAAM,cAAc,iBAAiB,GAAG;QACxC,MAAM,UAAU,YAAY;QAC5B,MAAM,SAAS,UAAU,YAAY,UAAU,aAAa,cAAc,wBAAwB;QAClG,IAAI;YACF,IAAI,gBAAgB,CAAC;gBACnB,MAAM,OAAO;gBACb;gBACA,UAAU;gBACV,cAAc,cAAc,QAAQ,UAAU,SAAS,UAAU,MAAM;YACzE;QACF,EAAE,OAAM,CAAC;IAAG;IACd,iCAAiC;AACnC;AAEA,MAAM,yBAAyB;IAC7B,OAAO;IACP,OAAO;AACT;AAEA;;;;CAIC,GACD,MAAM,uBAAuB,CAAC,KAAK,UAAU;IAC3C,IAAI,aAAa,uBAAuB,KAAK;IAC7C,MAAO,WAAY;QACjB,MAAM,OAAO,WAAW,KAAK;QAC7B,MAAM,cAAc,WAAW,GAAG,KAAK;QACvC,MAAM,gBAAgB,CAAC,YAAY,WAAW,QAAQ,KAAK;QAC3D,MAAM,cAAc,CAAC,UAAU,WAAW,MAAM,KAAK;QACrD,IAAI,eAAe,iBAAiB,aAAa;YAC/C,MAAM,OAAO,WAAW,SAAS;YACjC,IAAI;gBAAE,KAAK,YAAY;YAAI,EAAE,OAAM,CAAC;YAAO,KAAK,MAAM;YACtD,YAAY,wBAAwB;YACpC,MAAM,eAAe,WAAW,MAAM;YACtC,IAAI,cAAc;gBAChB,aAAa,UAAU;gBACvB,IAAI,aAAa,UAAU,CAAC,MAAM,KAAK,aAAa,UAAU,EAAE;oBAC9D,aAAa,SAAS,GAAG;oBACzB,IAAI,CAAC,aAAa,aAAa,EAAE;wBAC/B,aAAa,MAAM,GAAG;wBACtB,aAAa,UAAU,CAAC;wBACxB,aAAa,QAAQ,CAAC;oBACxB;gBACF;YACF;QACF;QACA,aAAa;IACf;AACF;AAEA;;;;;;;CAOC,GACD,MAAM,oBAAoB,CAAC,QAAQ,KAAK,UAAU,WAAW;IAC3D,MAAM,YAAY,IAAI,OAAO,CAAC,WAAW;IACzC,MAAM,oBAAoB,OAAO,KAAK,GAAI,CAAC,OAAO,QAAQ,GAAG,OAAO,UAAU;IAC9E,UAAU,YAAY,GAAG,OAAO,MAAM;IACtC,IAAI,OAAO,MAAM,EAAE,UAAU,KAAK;IAClC,IAAI,OAAO,QAAQ,GAAG,mBAAmB;QACvC,OAAO,QAAQ,GAAG;QAClB,OAAO,gBAAgB,GAAG;IAC5B;IACA,OAAO,UAAU,CAAC,IAAI,CAAC;IACvB,qBAAqB,KAAK;IAC1B,SAAS,wBAAwB;QAAE;QAAQ;QAAW;QAAK;QAAU,OAAO;QAAM,OAAO;IAAK;IAC9F,MAAM,eAAe;QAAQ,qBAAqB,KAAK,UAAU;IAAS;IAC1E,UAAU,QAAQ,GAAG;IACrB,UAAU,QAAQ,GAAG;IACrB,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,MAAM,sBAAsB,CAAC,UAAU,OAAO,KAAK,GAAG;IACpD,IAAI,IAAI,iBAAoC,OAAQ,KAAK,GAAG;IAC5D,IAAI,CAAC,MAAM,IAAI,OAAO;IACtB,IAAI,0BAA0B,QAAQ,CAAC,aAAa,iBAAiB,UAAU,cAAc,OAAO,GAAG,EAAE,EAAE,CAAC;IAC5G,IAAI,iBAAiB,UAAU,aAAa,iBAAiB,UAAU,SAAS,OAAO,GAAG,EAAE,GAAG,CAAC;IAChG,OAAO,GAAG,GAAG;AACf;AAEA;;;;;;;;CAQC,GACD,MAAM,4BAA4B,CAAC,KAAK,UAAU,MAAM,IAAI,GAAG;IAC7D,4BAA4B,GAC5B,IAAI,aAAa;IACjB,MAAM,aAAa,CAAC,MAAM,MAAM,oBAAoB,UAAU,IAAI,KAAK,GAAG,iBAAiB,iBAAiB,IAAI,CAAC,SAAS;IAC1H,IAAI,CAAC,MAAM,OAAO;QAChB,MAAM,eAAe,oBAAoB,UAAU,MAAM,KAAK,GAAG;QACjE,aAAa;YAAC;YAAc;SAAW;IACzC,OAAO;QACL,aAAa,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,gBAAgB,GAAE,IAAM,oBAAoB,UAAU,GAAG,KAAK,GAAG,kBAAkB;IACtH;IACA,OAAO;AACT;AAEA,MAAM;IACN;;;CAGC,GACC,YAAY,OAAO,EAAE,MAAM,CAAE;QAE3B,IAAI,QAAQ,KAAK,EAAE,QAAQ,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAEtD;QAEA,MAAM,gBAAgB,gBAAgB;QACtC,MAAM,gBAAgB,cAAc,MAAM;QAE1C,IAAI,CAAC,eAAe;YAClB,QAAQ,IAAI,CAAC,CAAC,6GAA6G,CAAC;QAC9H;QAEA,MAAM,OAAO,SAAS,OAAO,IAAI,EAAE,iBAAiB,QAAQ,QAAQ,CAAC,IAAI;QACzE,MAAM,SAAS,mBAAmB,GAAE,AAAC,KAAM,IAAI,IAAI;QACnD,MAAM,WAAW,SAAS,OAAO,QAAQ,EAAE,QAAQ,QAAQ,CAAC,QAAQ;QACpE,MAAM,SAAS,YAAY,2BAA2B,GAAE,AAAC,SAAU,IAAI,GAAG,WAAW;QACrF,MAAM,YAAY,OAAO,SAAS,IAAI,oBAAoB,GAAE,AAAC,OAAO,SAAS,KAAM;QACnF,MAAM,WAAW,OAAO,QAAQ,IAAI,oBAAoB,GAAE,AAAC,OAAO,QAAQ,KAAM;QAChF,MAAM,OAAO,SAAS,OAAO,IAAI,EAAE,QAAQ,QAAQ,CAAC,IAAI;QACxD,MAAM,aAAmC,AAAC,SAAS,QAAQ,SAAS,WAAY,WAAW,MAAM,QAAQ,OAAO,IAAI;QACpH,8BAA8B,GAC9B,MAAM,YAAY,YAAY,WAAW,sBAAsB,cAAc,WAAW,YAAY;QACpG,qBAAqB,GACrB,MAAM,OAAO;QACb,mBAAmB,GACnB,MAAM,SAAS,iBAAiB;QAChC,MAAM,YAAa,QAAQ,SAAS,KAAK,IAAI,IAAI;QAEjD,6BAA6B,GAC7B,IAAI,CAAC,OAAO,GAAG;QACf,yCAAyC,GACzC,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,kCAAkC,GAClC,IAAI,CAAC,gBAAgB,GAAG;QACxB,2BAA2B,GAC3B,IAAI,CAAC,UAAU,GAAG,OAAO,UAAU,IAAI;QACvC,mBAAmB,GACnB,IAAI,CAAC,QAAQ,GAAG;QAChB,oBAAoB,GACpB,IAAI,CAAC,aAAa,GAAG;QACrB,oBAAoB,GACpB,IAAI,CAAC,SAAS,GAAG;QACjB,oBAAoB,GACpB,IAAI,CAAC,MAAM,GAAG,CAAC,YAAY,WAAW;QACtC,oBAAoB,GACpB,IAAI,CAAC,QAAQ,GAAG;QAChB,mCAAmC,GACnC,IAAI,CAAC,QAAQ,GAAG;QAChB,mBAAmB,GACnB,IAAI,CAAC,MAAM,GAAG,SAAS,OAAO,YAAY,EAAE,QAAQ,QAAQ,CAAC,YAAY;QACzE,qBAAqB,GACrB,IAAI,CAAC,QAAQ,GAAG,MAAM,kBAAkB;QACxC,mBAAmB,GACnB,IAAI,CAAC,UAAU,GAAG;QAClB,2BAA2B,GAC3B,IAAI,CAAC,aAAa,GAAG,cAAc,GAAG,CAAC,CAAA,MAAO,IAAI,YAAY,CAAC;QAE/D,cAAc,OAAO,CAAC,CAAC,KAAK;YAE1B,MAAM,mBAAmB,GAAG,CAAC,iBAAiB;YAE9C,MAAM,0BAA0B,0BAA0B,IAAI,CAAC,CAAA,IAAK,OAAO,cAAc,CAAC;YAE1F,mBAAmB,GACnB,MAAM,WAAW,CAAC,SAAS,mBAAmB,GAAE,AAAC,OAAQ,QAAQ,GAAG,iBAAiB,SAAS,OAAO,QAAQ,EAAE,QAAQ,QAAQ,CAAC,QAAQ,GAAG,KAAK,GAAG,cAAc,IAAI;YACrK,mBAAmB,GACnB,MAAM,QAAQ,iBAAiB,SAAS,OAAO,KAAK,EAAE,QAAQ,QAAQ,CAAC,KAAK,GAAG,KAAK,GAAG,iBAAiB;YACxG,+BAA+B,GAC/B,MAAM,YAA8C,SAAS,OAAO,WAAW,EAAE;YAEjF,IAAK,IAAI,QAAQ,OAAQ;gBACvB,IAAI,CAAC,MAAM,OAAO;gBAClB,qCAAqC,GACrC,MAAM,YAAY,CAAC;gBACnB,qCAAqC,GACrC,MAAM,cAAc;oBAAE;oBAAY;oBAAW;oBAAM;oBAAQ;oBAAU;oBAAO;gBAAU;gBACtF,MAAM,gBAAgB,MAAM,CAAC,KAAK;gBAClC,MAAM,8BAA8B,0BAA0B,gBAAgB,QAAQ,CAAC,QAAQ,OAAO,gBAAgB,GAAG,CAAC,QAAQ;gBAClI,IAAI;gBACJ,IAAI,MAAM,gBAAgB;oBACxB,MAAM,eAAgD;oBACtD,MAAM,mBAAmB,SAAS,aAAa,IAAI,EAAE;oBACrD,MAAM,qBAAqB,mBAAmB,GAAE,AAAC,iBAAkB,IAAI,IAAI;oBAC3E,MAAM,KAAK,8BAA8B,GAAE,AAAC,aAAc,EAAE;oBAC5D,MAAM,OAAO,8BAA8B,GAAE,AAAC,aAAc,IAAI;oBAChE,mBAAmB,GACnB,YAAY,QAAQ,GAAG,CAAC,qBAAqB,mBAAmB,GAAE,AAAC,mBAAoB,QAAQ,GAAG,iBAAiB,SAAS,aAAa,QAAQ,EAAE,WAAW,KAAK,GAAG,cAAc,IAAI;oBACxL,mBAAmB,GACnB,YAAY,KAAK,GAAG,iBAAiB,SAAS,aAAa,KAAK,EAAE,QAAQ,KAAK,GAAG,iBAAiB;oBACnG,+BAA+B,GAC/B,YAAY,SAAS,GAAqC,SAAS,aAAa,WAAW,EAAE;oBAC7F,mBAAmB,GACnB,YAAY,MAAM,GAAG,iBAAiB;oBACtC,sBAAsB,0BAA0B,KAAK,MAAM,MAAM,IAAI,GAAG;oBACxE,IAAI,6BAA6B;wBAC/B,SAAS,CAAC,CAAC,EAAE,EAAE,6BAA6B,CAAC,GAAG;wBAChD,gBAAgB,CAAC,4BAA4B,GAAG;oBAClD,OAAO;wBACL,SAAS,CAAC,KAAK,GAAG,0BAA0B,KAAK,MAAM,MAAM,IAAI,GAAG;oBACtE;oBACA,kBAAkB,IAAI,EAAE,KAAK,MAAM,WAAW;oBAC9C,IAAI,CAAC,MAAM,OAAO;wBAChB,IAAI,CAAC,6BAA6B;4BAChC,IAAI,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,EAAE;wBACtC,OAAO;4BACL,MAAM,MAAM,CAAC,EAAE,EAAE,6BAA6B;4BAC9C,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,EAAE;wBAC9C;oBACF;gBACF,OAAO;oBACL,sBAAsB,MAAM,iBACN,cAAc,GAAG,CAAC,CAAC,gBAAgB,GAAE,IAAM,oBAAoB,MAAM,GAAG,KAAK,GAAG,kBAChF,oBAAoB,MAAyB,eAAgB,KAAK,GAAG;oBAC3F,IAAI,6BAA6B;wBAC/B,SAAS,CAAC,CAAC,EAAE,EAAE,6BAA6B,CAAC,GAAG;wBAChD,gBAAgB,CAAC,4BAA4B,GAAG;oBAClD,OAAO;wBACL,SAAS,CAAC,KAAK,GAAG;oBACpB;oBACA,kBAAkB,IAAI,EAAE,KAAK,MAAM,WAAW;gBAChD;YACF;YACA,IAAI,yBAAyB;gBAC3B,IAAI,aAAa;gBACjB,IAAK,IAAI,KAAK,iBAAkB;oBAC9B,cAAc,GAAG,yBAAyB,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC;gBAC9D;gBACA,IAAI,KAAK,CAAC,SAAS,GAAG;YACxB;QACF;QAEA,IAAI,QAAQ;YACV,2BAA2B,GAAE,AAAC,IAAI,CAAC,QAAQ,CAAE,IAAI,CAAC,IAAI;QACxD;IACF;IAEA;;;GAGC,GAED;;;GAGC,GACD,QAAQ,QAAQ,EAAE;QAChB,MAAM,KAAK,MAAM,YAAY,CAAA,IAAK,CAAC,CAAC,SAAS,KAAK;QAClD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxB,OAAO,IAAI;IACb;IAEA,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,0BAA0B,GAC1B,IAAI,MAAM,KAAK,EAAE;QACf,IAAI,CAAC,MAAM,GAAG,CAAC;QACf,IAAI,CAAC,OAAO,CAAC,CAAA,OAAQ,KAAK,YAAY,GAAG;IAC3C;IAEA,IAAI,cAAc;QAChB,MAAM,mBAAmB,IAAI,CAAC,gBAAgB;QAC9C,MAAM,YAAY,QAAQ,SAAS;QACnC,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,iBAAiB,WAAW,GAAG,CAAC,cAAc,IAAI,IAAI,SAAS,IAAI;IACjI;IAEA,yBAAyB,GACzB,IAAI,YAAY,IAAI,EAAE;QACpB,MAAM,IAAI,OAAO,CAAC,QAAQ,SAAS,KAAK,IAAI,IAAI,CAAC;QACjD,IAAI,CAAC,OAAO,CAAC,CAAA;YACX,uGAAuG;YACvG,mKAAmK;YACnK,0EAA0E;YAC1E,IAAI,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI;YACjC,KAAK,WAAW,GAAG;QACrB;IACF;IAEA,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ;IACzC;IAEA,6BAA6B,GAC7B,IAAI,SAAS,QAAQ,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,CAAA,OAAQ,KAAK,WAAW,GAAG,WAAW,IAAI,CAAC,QAAQ,IAAI;IACtE;IAEA,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI;QAC7B,IAAI,CAAC,MAAM,GAAG;QACd,mEAAmE;QACnE,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,QAAQ;QACN,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI;QAC5B,IAAI,CAAC,MAAM,GAAG;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,YAAY;QACV,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ;QAC9B,IAAI,CAAC,OAAO,CAAC;QACb,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC;QAC9B,OAAO,IAAI;IACb;IAEA,OAAO;QACL,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS;QACjC,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS;QAClC,OAAO,IAAI,CAAC,MAAM;IACpB;IAED;;;EAGC,GACA,KAAK,IAAI,EAAE,gBAAgB,KAAK,EAAE;QAChC,IAAI,eAAe,IAAI,CAAC,aAAa,GAAG;QACxC,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG;QAC3C,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK;QAC3B,OAAO,IAAI;IACb;IAEA,UAAU;QACR,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,MAAM;IAClC;IAEA,eAAe;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,WAAW;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;IAChC;IAEA,SAAS;QACP,IAAI,CAAC,OAAO,CAAC;QACb,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,SAAS;QACP,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,IAAM,IAAI,YAAY,CAAC,SAAS,IAAI,CAAC,aAAa,CAAC,EAAE;QAChF,OAAO,IAAI;IACb;IAEA;;;GAGC,GACD,KAAK,WAAW,IAAI,EAAE;QACpB,MAAM,OAAO,IAAI,CAAC,IAAI;QACtB,MAAM,YAAY;YAChB,IAAI,CAAC,IAAI,GAAG;YACZ,SAAS,IAAI;YACb,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,QAAQ,GAAG;QAClB;QACA,OAAO,IAAI,QAAQ,CAAA;YACjB,IAAI,CAAC,QAAQ,GAAG,IAAM,EAAE;YACxB,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ;YACjC,OAAO,IAAI;QACb;IACF;AACF;AAEA,MAAM,QAAQ;IACd;;;;CAIC,GACC,SAAS,CAAC,SAAS,SAAW,IAAI,eAAe,SAAS;IAC1D,aAAa;AACf;AAKA;;;CAGC,GACD,MAAM,OAAO,CAAC,WAAW,IAAI;IAC3B,OAAO,IAAI,MAAM;QAAE,UAAU,IAAI,QAAQ,SAAS;QAAE,YAAY;IAAS,GAAG,MAAM,GAAG,MAAM;AAC7F;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;CA0BC,GACD,SAAS,eAAe,cAAc,EAAE,QAAQ,EAAE,IAAI;IACpD,MAAM,UAAU,gBAAgB;IAChC,IAAI,CAAC,QAAQ,MAAM,EAAE;IACrB,MAAM,CAAE,OAAQ,GAAG;IACnB,MAAM,YAAY,aAAa,QAAQ;IACvC,MAAM,oBAAoB,qBAAqB,UAAU,QAAQ;IACjE,IAAI,gBAAgB,2BAA2B,QAAQ;IACvD,IAAI,MAAM,OAAO;QACf,OAAO;IACT,OAAO;QACL,kBAAkB,eAAe;QACjC,IAAI,wBAAwB,CAAC,KAAK,WAAW,MAAM,IAAI,wBAAwB,CAAC,KAAK,WAAW,IAAI,EAAE;YACpG,IAAI,SAAS,OAAO;gBAClB,OAAO,wBAAwB,CAAC;YAClC,OAAO;gBACL,MAAM,iBAAiB,iBAA0C,QAAS,yBAA+C,MAAO;gBAChI,OAAO,GAAG,MAAM,eAAe,CAAC,EAAE,QAAQ,SAAS,IAAI,eAAe,CAAC,EAAE;YAC3E;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,MAAM,kBAAkB,CAAC,SAAS;IAChC,IAAI,MAAM,aAAa;IACvB,WAAW,QAAQ,GAAG;IACtB,sDAAsD;IACtD,WAAW,WAAW,GAAG,SAAS,WAAW,WAAW,EAAE,iBAAiB,IAAI;IAC/E,2DAA2D;IAC3D,OAAO,IAAI,YAAY,SAAS,YAAY,MAAM,GAAG,MAAM,MAAM;AACnE;AAEA;;;;;CAKC,GACD,MAAM,6BAA6B,CAAC,cAAc,WAAW;IAC3D,IAAI,uBAAuB;IAC3B,gBAAgB,WAAW,CAAC,iBAAiB,GAAE;QAC7C,MAAM,cAAc,MAAM,MAAM;QAChC,IAAI,aAAa,QAAQ,CAAC,cAAc;YACtC,MAAM,YAAY,MAAM,QAAQ;YAChC,MAAM,YAAY,MAAM,UAAU;YAClC,MAAM,oBAAoB,qBAAqB,cAAc,aAAa;YAC1E,IAAI,CAAC,qBAAqB,qBAAqB,sBAAsB,WAAW;gBAC9E,wEAAwE;gBACxE,IAAI,MAAM,MAAM,CAAC,KAAK,KAAK,SACvB,MAAM,UAAU,KAAK,WAAW,SAAS,IACzC,MAAM,KAAK,IACX,MAAM,KAAK,CAAC,UAAU,KAAK,WAAW,SAAS,EACjD;oBACA,MAAM,KAAK,CAAC,iBAAiB,GAAG;gBAClC;gBACA,gDAAgD;gBAChD,YAAY,WAAW;gBACvB,wFAAwF;gBACxF,qBAAqB;gBACrB,uBAAuB;YACzB;QACF;IACF,GAAG;IACH,OAAO;AACT;AAEA;;;;;CAKC,GACD,MAAM,SAAS,CAAC,SAAS,YAAY;IACnC,MAAM,eAAe,aAAa;IAClC,MAAM,SAAkD,aAAa,aAAa;IAClF,MAAM,iBAAiB,cAAc,2BAA2B,GAAE,AAAC,WAAY,gBAAgB,IAAkC;IACjI,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAI,GAAG,IAAK;QACnD,MAAM,MAAgC,YAAY,CAAC,EAAE;QACrD,qBAAqB,KAAK,cAAc;IAC1C;IACA,IAAI;IACJ,IAAI,OAAO,YAAY,EAAE;QACvB,IAAI,oBAAoB;QACxB,gBAAgB,QAAQ,CAAC,uBAAuB,GAAE;YAChD,IAAI,CAAC,MAAM,YAAY,EAAE;gBACvB,gBAAgB,2BAA2B,cAAyC,OAAQ;gBAC5F,uFAAuF;gBACvF,IAAI,iBAAiB,CAAC,MAAM,KAAK,EAAE;oBACjC,MAAM,MAAM;oBACZ,YAAY,QAAQ;gBACtB,OAAO;oBACL,6FAA6F;oBAC7F,MAAM,gBAAgB,MAAM,OAAO,GAAG,MAAM,MAAM;oBAClD,MAAM,WAAW,gBAAgB,MAAM,QAAQ;oBAC/C,IAAI,WAAW,mBAAmB;wBAChC,oBAAoB;oBACtB;gBACF;YACF;YACA,qDAAqD;YACrD,yBAAyB;YACzB,IAAI,MAAM,KAAK,EAAE;gBACf,OAAO,SAAS,OAAO;YACzB,OAAO;gBACL,MAAM,YAAY,GAAG;YACvB;QACF,GAAG;QACH,kFAAkF;QAClF,IAAI,CAAC,MAAM,uBAAuB,GAAE,AAAC,OAAQ,iBAAiB,GAAG;YAC/D,uBAAuB,GAAE,AAAC,OAAQ,iBAAiB,GAAG;QACxD;IACF,OAAO;QACL,gBAAgB,2BACd,cAC2B,QAC3B;IAEJ;IAEA,IAAI,iBAAiB,CAAC,OAAO,KAAK,EAAE;QAClC,OAAO,YAAY,GAAG;QACtB,kFAAkF;QAClF,uGAAuG;QACvG,IAAI,uBAAuB,GAAE,AAAC,OAAQ,MAAM,EAAE,uBAAuB,GAAE,AAAC,OAAQ,MAAM;IACxF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,MAAM,SAAS,CAAC,KAAK,KAAK;IAAoB,MAAM,IAAI,MAAM,CAAC,iBAAiB,CAAC;IAAG,OAAO,MAAM,CAAC,KAAK,MAAM,KAAK,CAAC,MAAM,MAAO,IAAI,CAAE,IAAI,GAAG,IAAI,KAAK;AAAE;AAExJ;;;CAGC,GACD,MAAM,aAAa,CAAA,QAAS,KAAK,CAAC,OAAO,GAAG,MAAM,MAAM,GAAG,GAAG;AAE9D;;;;CAIC,GACD,MAAM,UAAU,CAAA;IACd,IAAI,IAAI,MAAM,MAAM,EAAE,GAAG;IACzB,MAAO,EAAG;QAAE,IAAI,OAAO,GAAG,EAAE;QAAI,IAAI,KAAK,CAAC,EAAE;QAAE,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;QAAE,KAAK,CAAC,EAAE,GAAG;IAAG;IACjF,OAAO;AACT;AAEA;;;;CAIC,GACD,MAAM,WAAW,CAAC,GAAG,gBAAkB,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;AAEpD;;;;;CAKC,GACD,MAAM,WAAW,CAAC,GAAG,aAAa,YAAc,GAAG,GAAG,CAAC,QAAQ,CAAC,aAAa;AAE7E;;;;;CAKC,GACD,MAAM,SAAS,CAAC,GAAG,aAAa,YAAc,GAAG,GAAG,CAAC,MAAM,CAAC,aAAa;AAEzE;;;;;CAKC,GACD,MAAM,OAAO,CAAC,GAAG,KAAK,MAAQ,AAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAK;AAExF;;;;;;;CAOC,GACD,MAAM,WAAW,CAAC,OAAO,OAAO,QAAQ,QAAQ,UAAY,SAAS,AAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,KAAK,IAAK,CAAC,UAAU,MAAM;AAE7H;;;CAGC,GACD,MAAM,WAAW,CAAA,UAAW,UAAU,KAAK;AAE3C;;;CAGC,GACD,MAAM,WAAW,CAAA,UAAW,UAAU,MAAM;AAE5C;;;;;;;CAOC,GACD,MAAM,OAAO,CAAC,OAAO,KAAK,QAAQ;IAChC,IAAI,KAAK,IAAI,QAAQ,QAAQ,CAAC,SAAS;IACvC,IAAI,eAAe,OAAO;QACxB,MAAM,SAAS,qBAAqB,GACrB,AAAC,cACA,OAAO,YAAY,IAAI;QACvC,IAAI,UAAU,OAAO,SAAS,EAAE;YAC9B,KAAK,OAAO,SAAS;QACvB;IACF;IACA,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,SAAS,KAAK;IACtC,OAAO,CAAC,SAAS,QAAQ,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,QAAQ,IAAI;AACtE;AAEA,uBAAuB;AAEvB;;;;;;;;CAQC,GACD,MAAM,QAAQ,CAAC,IAAI,OAAO,CAAC,GAAK,CAAC,GAAG,OAAS,OAAO,CAAA,IAAK,MAAM,MAAM,KAAK,CAAA,IAAK,GAAG,MAAM;AAExF;;;CAGC,GACD,MAAM,QAAQ,CAAA;IACX,OAAO,CAAC,GAAG;QACV,MAAM,SAAS,MAAM;QACrB,OAAO,IAAI,MAAM,MAAM;YACrB,OAAO,CAAC,GAAG,IAAI,CAAC,EAAE,GAAK,OAAO;YAC9B,KAAK,CAAC,GAAG,OAAS,MAAM,sCAAsC,GAAE,CAAC,GAAG;oBAClE,MAAM,aAAa,KAAK,CAAC,KAAK,IAAI;oBAClC,OAAO,CAAC,yBAAyB,GAAE,IAAM,WAAW,OAAO;gBAC7D;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,MAAM,gBAAgB,CAAC,IAAI,QAAQ,CAAC,GAAK,CAAC,GAAG,OAAS,CAAC,KAAK,MAAM,GAAG,GAAG,MAAM,GAAG,MAAM,MAAM,IAAI,UAAU,EAAE,KAAK;AAElH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqEC,GAED,MAAM,QAAQ;IACZ,GAAG;IACH,KAAK;IACL,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAkD,cAAc;IAChE,OAAkD,cAAc;IAChE,MAA+C,cAAc;IAC7D,MAA+C,cAAc;IAC7D,aAAoE,cAAc,aAAa;IAC/F,UAA2D,cAAc;IACzE,UAA2D,cAAc;IACzE,UAA2D,cAAc;IACzE,QAAqD,cAAc;IACnE,UAA2D,cAAc;IACzE,UAA2D,cAAc;AAC3E;AAKA;;CAEC,GAED;;;;;CAKC,GACD,MAAM,qBAAqB,CAAC,UAAU;IACpC,IAAI,iBAAiB,cAAc,MAAM;QACvC,MAAM,0BAA0B,YAAY,CAAC,EAAE,KAAK;QACpD,MAAM,gBAAwC,SAAS,KAAK;QAC5D,MAAM,aAAa,gBAAgB,cAAc,OAAO,GAAG,cAAc,MAAM,GAAG;QAClF,OAAO,0BAA0B,aAAa,aAAa,cAAc,QAAQ;IACnF;AACF;AAEA;;;;CAIC,GACD,MAAM,wBAAwB,CAAC,UAAU;IACvC,IAAI,aAAa,SAAS,iBAAiB;IAC3C,IAAI,eAAe,UAAU,aAAa;IAC1C,IAAI,MAAM,eAAe,OAAO;IAChC,IAAI,MAAM,CAAC,eAAe,OAAO,CAAC;IAClC,MAAM,aAAmC;IACzC,MAAM,WAAW,WAAW,SAAS,MAAM,GAAG;IAC9C,MAAM,YAAY,CAAC,MAAM;IACzB,MAAM,aAAa,mBAAmB,UAAU;IAChD,MAAM,aAAa,CAAC,MAAM;IAC1B,MAAM,0BAA0B,sBAAsB,IAAI,CAAC;IAC3D,IAAI,yBAAyB;QAC3B,MAAM,eAAe,uBAAuB,CAAC,EAAE;QAC/C,MAAM,QAAQ,WAAW,KAAK,CAAC;QAC/B,MAAM,cAAc,aAAa,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;QACjE,MAAM,eAAe,aAAa,aAAa,YAAY,cAAc;QACzE,MAAM,wBAAwB,CAAC,KAAK,CAAC,EAAE;QACvC,OAAO,iBAAiB,cAAc,uBAAuB,YAAY,CAAC,EAAE;IAC9E,OAAO;QACL,OAAO,aAAa,aACb,YAAY,CAAC,MAAM,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW,GAC/D,aAAa;IACtB;AACF;AAEA;;;CAGC,GACD,SAAS,yBAAyB,EAAE;IAClC,OAAO,cAAc,AAAC,CAAC,GAAG,iBAAiB,GAAG,GAAG,UAAU,IAAI,GAAG,cAAc,GAAI,GAAG,UAAU,KAAK;AACxG;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,WAAW,WAAW,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM;IACvE,MAAM,WAAW,MAAM,YAAY,QAAQ,KAAK,mBAAmB,GAAE,AAAC,YAAY,QAAQ,IAAK;IAC/F,8IAA8I;IAC9I,MAAM,mBAAmB,WAAW,eAAe,WAAW;IAC9D,KAAK,IAAI,kBAAkB,GAAG,GAAG,UAAU,IAAI;IAC/C,MAAM,UAAU,UACd,IAAI,YAAY,SAAuC,aAAc,IAAI,kBAAkB,OAAO,OAAO,UACzG,IAAI,MAAiC,aAAc,IAAI;IACzD,QAAQ,IAAI,CAAC;IACb,uEAAuE;IACvE,SAAS,IAAI;IACb,gBAAgB,IAAI,CAAC,uBAAuB,GAAE;QAC5C,MAAM,gBAAgB,MAAM,OAAO,GAAG,MAAM,MAAM;QAClD,MAAM,WAAW,gBAAgB,MAAM,QAAQ;QAC/C,IAAI,WAAW,GAAG,iBAAiB,EAAE,GAAG,iBAAiB,GAAG;IAC9D;IACA,GAAG,QAAQ,GAAG,yBAAyB;IACvC,OAAO;AACT;AAEA,MAAM,iBAAiB;IAErB;;GAEC,GACD,YAAY,aAAa,CAAC,CAAC,CAAE;QAC3B,KAAK,CAA2C,YAAa,MAAM;QACnE,mBAAmB,GACnB,IAAI,CAAC,QAAQ,GAAG,GAAG,yDAAyD;QAC5E,mCAAmC,GACnC,IAAI,CAAC,MAAM,GAAG,CAAC;QACf,MAAM,iBAAiB,WAAW,QAAQ;QAC1C,MAAM,iBAAiB,QAAQ,QAAQ;QACvC,2BAA2B,GAC3B,IAAI,CAAC,QAAQ,GAAG,iBAAiB,aAAa,gBAAgB,kBAAkB;QAChF,2BAA2B,GAC3B,IAAI,CAAC,QAAQ,GAAG,WAAW,QAAQ,IAAI,eAAe,QAAQ;QAC9D,MAAM,iBAAiB,SAAS,WAAW,YAAY,EAAE,eAAe,YAAY;QACpF,IAAI,CAAC,KAAK,GAAG,iBAAiB,aAAa,kBAAkB;QAC7D,mBAAmB,GACnB,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IAEA;;;;;;;;;;;;;;;GAeC,GACD,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACd,MAAM,SAAS,MAAM;QACrB,MAAM,UAAU,MAAM;QACtB,IAAI,UAAU,SAAS;YACrB,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,QAAQ;gBACV,MAAM,cAA6C;gBACnD,oDAAoD;gBACpD,IAAI,MAAM,KAAK;oBACb,MAAM,oBAA4C;oBAClD,MAAM,qBAAqB,aAAyC;oBACpE,kFAAkF;oBAClF,MAAM,aAAa,IAAI,CAAC,QAAQ;oBAChC,4FAA4F;oBAC5F,MAAM,sBAAsB,IAAI,CAAC,iBAAiB;oBAClD,mFAAmF;oBACnF,MAAM,KAAK,YAAY,EAAE;oBACzB,IAAI,IAAI;oBACR,MAAM,eAAe,mBAAmB,MAAM;oBAC9C,mBAAmB,OAAO,CAAC,CAAC,mBAAmB,GAAE;wBAC/C,4DAA4D;wBAC5D,MAAM,uBAAuB;4BAAE,GAAG,WAAW;wBAAC;wBAC9C,4GAA4G;wBAC5G,IAAI,CAAC,QAAQ,GAAG;wBAChB,IAAI,CAAC,iBAAiB,GAAG;wBACzB,IAAI,CAAC,MAAM,KAAK,qBAAqB,EAAE,GAAG,KAAK,MAAM;wBACrD,WACE,sBACA,IAAI,EACJ,kBAAkB,QAAQ,GAAG,cAAc,IAAI,GAC/C,QACA,GACA;wBAEF;oBACF;gBACF,OAAO;oBACL,WACE,aACA,IAAI,EACJ,sBAAsB,IAAI,EAAE,KACA;gBAEhC;YACF,OAAO;gBACL,eAAe;gBACf,WAC2B,IACzB,IAAI,EACJ,sBAAsB,IAAI,EAA2B;YAEzD;YACA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,qBAAqB;QAC5C;IACF;IAEA;;;;;;;;;;;;;;;;;;GAkBC,GACD,KAAK,MAAM,EAAE,QAAQ,EAAE;QACrB,IAAI,MAAM,WAAW,UAAU,MAAM,OAAO,KAAK,GAAG,OAAO,IAAI;QAC/D,OAAO,KAAK;QACZ,MAAM,WAAW,CAAC,CAAC,iCAAiC,GAAE,AAAC,OAAQ,MAAM,GAAG,iCAAiC,GAAE,AAAC,OAAQ,MAAM,CAAC,SAAS,GAAG,QAAQ,GAAG,qBAAqB,GAAE,AAAC,OAAQ,QAAQ;QAC1L,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ;YAAE,aAAa;gBAAC;gBAAG;aAAS;YAAE;YAAU,MAAM;QAAS,GAAG;IACpF;IAEA;;;;;GAKC,GACD,IAAI,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE;QACjC,IAAI,MAAM,aAAa,OAAO,IAAI;QAClC,WAAW,QAAQ,GAAG;QACtB,WAAW,WAAW,GAAG,iBAAiB,OAAO;QACjD,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,YAAY;IACvC;IAEA;;;;GAIC,GACD,KAAK,QAAQ,EAAE,QAAQ,EAAE;QACvB,IAAI,MAAM,aAAa,YAAY,CAAC,MAAM,WAAW,OAAO,IAAI;QAChE,OAAO,IAAI,CAAC,GAAG,CAAC;YAAE,UAAU;YAAG,YAAY,IAAM,SAAS,IAAI;QAAE,GAAG;IACrE;IAEA;;;;;GAKC,GACD,MAAM,SAAS,EAAE,QAAQ,EAAE;QACzB,IAAI,MAAM,cAAc,aAAa,CAAC,MAAM,YAAY,OAAO,IAAI;QACnE,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,sBAAsB,IAAI,EAA2B;QAC9E,OAAO,IAAI;IACb;IAEA;;;;GAIC,GACD,OAAO,OAAO,EAAE,YAAY,EAAE;QAC5B,OAAO,SAAS,IAAI,EAAE;QACtB,OAAO,IAAI;IACb;IAEA;;;GAGC,GACD,QAAQ,WAAW,EAAE;QACnB,MAAM,kBAAkB,IAAI,CAAC,QAAQ;QACrC,IAAI,oBAAoB,cAAc,cAAc,OAAO,IAAI;QAC/D,MAAM,YAAY,cAAc;QAChC,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,gBAAgB,IAAI,EAAE,CAAC,wBAAwB,GAAE,QAAU,MAAM,OAAO,CAAC,MAAM,QAAQ,GAAG;QAC1F,IAAK,IAAI,aAAa,OAAQ,MAAM,CAAC,UAAU,IAAI;QACnD,OAAO,KAAK,CAAC,QAAQ;IACvB;IAEA;;GAEC,GACD,UAAU;QACR,gBAAgB,IAAI,EAAE,CAAC,wBAAwB,GAAE;YAC/C,IAAI,MAAM,OAAO,EAAE,MAAM,OAAO;QAClC;QACA,OAAO,IAAI;IACb;IAEA;;GAEC,GACD,SAAS;QACP,KAAK,CAAC;QACN,gBAAgB,IAAI,EAAE,CAAC,wBAAwB,GAAE,QAAU,MAAM,MAAM,EAAE;QACzE,OAAO,kBAAkB,IAAI;IAC/B;IAEA;;;GAGC,GACD,KAAK,QAAQ,EAAE;QACb,OAAO,KAAK,CAAC,KAAK;IACpB;AACF;AAEA;;;CAGC,GACD,MAAM,iBAAiB,CAAA,aAAc,IAAI,SAAS,YAAY,IAAI;AAKlE,MAAM;IACJ;;;GAGC,GACD,YAAY,OAAO,EAAE,UAAU,CAAE;QAC/B,IAAI,QAAQ,KAAK,EAAE,QAAQ,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QACtD,4BAA4B,GAC5B,MAAM,eAAe,CAAC;QACtB,MAAM,aAAa,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB,IAAI,MAAM,YAAY,MAAM,aAAa;QACzC,IAAK,IAAI,YAAY,WAAY;YAC/B,MAAM,aAAa,UAAU,CAAC,SAAS;YACvC,IAAI,MAAM,WAAW;gBACnB,UAAU,CAAC,SAAS,GAAG;YACzB,OAAO;gBACL,YAAY,CAAC,SAAS,GAAG;YAC3B;QACF;QACA,IAAK,IAAI,YAAY,WAAY;YAC/B,MAAM,YAAY,UAAU,CAAC,SAAS;YACtC,MAAM,aAAa,MAAM;YACzB,+BAA+B,GAC/B,IAAI,aAAa,CAAC;YAClB,IAAI,KAAK;YACT,IAAI,YAAY;gBACd,MAAM,OAAO,UAAU,IAAI;gBAC3B,IAAI,MAAM,OAAO,MAAM;YACzB,OAAO;gBACL,WAAW,QAAQ,GAAG;YACxB;YACA,UAAU,CAAC,SAAS,GAAG,aAAa,aAAa;gBAAE;YAAG,GAAG,aAAa;YACtE,MAAM,aAAa,aAAa,cAAc;YAC9C,WAAW,WAAW,GAAG,iBAAiB,OAAO;YACjD,WAAW,QAAQ,GAAG;YACtB,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,YAAY,SAAS,YAAY,MAAM,GAAG,OAAO,IAAI;YACvG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,UAAU,OAAO;YAChE,+BAA+B,GAC/B,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,UAAU;gBAC9B,MAAM,QAA6B,UAAU,KAAK;gBAClD,IAAI,MAAM,OAAO,OAAO;oBACtB,MAAM,UAAU,MAAM,QAAQ;oBAC9B,IAAI,WAAW,QAAQ,MAAM,EAAE;wBAC7B,OAAO;oBACT,OAAO;wBACL,OAAO,MAAM,SAAS,CAAC,MAAM,OAAO;oBACtC;gBACF,OAAO;oBACL,gBAAgB,WAAW,CAAC,kBAAkB,GAAE;wBAC9C,IAAI,MAAM,KAAK;4BACb,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,GAAE,AAAC,GAAI,MAAM,EAAE,IAAI,GAAG,IAAK;gCAC/D,IAAI,CAAC,MAAM,MAAM,QAAQ,CAAC,EAAE,GAAG;oCAC7B,MAAM,YAAY,CAAC,EAAE,GAAyB,MAAM,SAAS,CAAC,MAAM,QAAQ,CAAC,EAAE;oCAC/E,MAAM,UAAU,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;gCAC7B;4BACF;wBACF,OAAO;4BACL,MAAM,WAAW,GAAyB,MAAM,SAAS,CAAC,MAAM,OAAO;4BACvE,MAAM,SAAS,GAAyB;wBAC1C;wBACA,IAAI,CAAC,MAAM,OAAO,MAAM,KAAK,GAAG,aAAa;wBAC7C,MAAM,YAAY,GAAG;oBACvB;oBACA,IAAI,CAAC,MAAM,WAAW,UAAU,OAAO,CAAC;oBACxC,UAAU,KAAK,CAAC,GAAG,MAAM;oBACzB,OAAO,IAAI;gBACb;YACF;QACF;IACF;IAEA,SAAS;QACP,IAAK,IAAI,YAAY,IAAI,CAAC,UAAU,CAAE;YACpC,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM;QAClC;QACA,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;QACtB,OAAO,IAAI;IACb;AACF;AAEA;;;;CAIC,GACD,MAAM,mBAAmB,CAAC,SAAS,aAA+C,IAAI,WAAW,SAAS;AAK1G;;;CAGC,GAED;;;;;;CAMC,GAED,MAAM;IACJ;;GAEC,GACD,YAAY,aAAa,CAAC,CAAC,CAAE;QAC3B,IAAI,CAAC,QAAQ,GAAG,KAAK,mDAAmD;QACxE,IAAI,CAAC,aAAa,GAAG,OAAO,8DAA8D;QAC1F,IAAI,CAAC,YAAY,GAAG,KAAK,qFAAqF;QAC9G,IAAI,CAAC,WAAW,GAAG,OAAO,4DAA4D;QACtF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,+FAA+F;QAC1J,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,gEAAgE;QAC3H,IAAI,CAAC,CAAC,GAAG,MAAM,SAAS,WAAW,IAAI,EAAE,IAAI,GAAG;QAChD,IAAI,CAAC,CAAC,GAAG,MAAM,SAAS,WAAW,SAAS,EAAE,MAAM,GAAG;QACvD,IAAI,CAAC,CAAC,GAAG,MAAM,SAAS,WAAW,OAAO,EAAE,KAAK,IAAI;QACrD,IAAI,CAAC,CAAC,GAAG,MAAM,SAAS,WAAW,QAAQ,EAAE,IAAI,CAAC,KAAK;QACvD,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO;QACZ,2BAA2B,GAC3B,IAAI,CAAC,IAAI,GAAG,CAAA,IAAK,MAAM,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc;IAC9E;IAEA,2BAA2B,GAC3B,MAAM,IAAI,EAAE;QACV,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,IAAI;QAChC,IAAI,IAAI;QACR,IAAI,OAAO,GAAG;YACZ,IAAI,IAAI,CAAC,IAAI,OAAO,MAAM,CAAC,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,EAAE;QAC9D,OAAO;YACL,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;QAC7B;QACA,OAAO,IAAI;IACb;IAEA,UAAU;QACR,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI;QACjF,MAAM,KAAK,IAAI,CAAC,EAAE,GAAG,MAAM,KAAK,IAAI,IAAI,UAAU;QAClD,MAAM,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;QAC7C,MAAM,KAAK,IAAI,CAAC,EAAE,GAAG,OAAO,IAAI,KAAK,KAAK,IAAI,OAAO,QAAQ;QAC7D,IAAI,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,OAAO,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI;QACjD,IAAI,aAAa;QACjB,IAAI,YAAY;QAChB,IAAI,aAAa;QACjB,MAAO,YAAY,gBAAgB,aAAa,cAAe;YAC7D,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,eAAe;gBACnD;YACF,OAAO;gBACL,YAAY;YACd;YACA,IAAI,CAAC,cAAc,GAAG;YACtB,cAAc;YACd;QACF;QACA,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,GAAG,GAAG,KAAK,QAAQ,SAAS;IACvE;IAEA,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,CAAC;IACf;IAEA,IAAI,KAAK,CAAC,EAAE;QACV,IAAI,CAAC,CAAC,GAAG,MAAM,SAAS,GAAG,IAAI,GAAG;QAClC,IAAI,CAAC,OAAO;IACd;IAEA,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,CAAC;IACf;IAEA,IAAI,UAAU,CAAC,EAAE;QACf,IAAI,CAAC,CAAC,GAAG,MAAM,SAAS,GAAG,MAAM,GAAG;QACpC,IAAI,CAAC,OAAO;IACd;IAEA,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,CAAC;IACf;IAEA,IAAI,QAAQ,CAAC,EAAE;QACb,IAAI,CAAC,CAAC,GAAG,MAAM,SAAS,GAAG,KAAK,IAAI;QACpC,IAAI,CAAC,OAAO;IACd;IAEA,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,CAAC;IACf;IAEA,IAAI,SAAS,CAAC,EAAE;QACd,IAAI,CAAC,CAAC,GAAG,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK;QACrC,IAAI,CAAC,OAAO;IACd;AACF;AAEA;;;CAGC,GACD,MAAM,eAAe,CAAC,aAAe,IAAI,OAAO;AAKhD;;CAEC,GACD,MAAM,iBAAiB,CAAA;IACrB,IAAI,EAAE,UAAU,EAAE,EAAE,cAAc;AACpC;AAEA,MAAM;IACJ,uBAAuB,GACvB,YAAY,EAAE,CAAE;QACd,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,SAAS,GAAG;YACf,KAAK;YACL,QAAQ;QACV;IACF;IAEA,IAAI,IAAI;QAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI;IAAE;IAChC,IAAI,EAAE,CAAC,EAAE;QAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG;IAAG;IAE1B,IAAI,IAAI;QAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI;IAAE;IAChC,IAAI,EAAE,CAAC,EAAE;QAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG;IAAG;IAE1B,IAAI,QAAQ;QAAE,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI;IAAE;IACxC,IAAI,MAAM,CAAC,EAAE;QAAE,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG;IAAG;IAElC,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC,EAAE,CAAC,MAAM,IAAI;IAAE;IAC1C,IAAI,OAAO,CAAC,EAAE;QAAE,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG;IAAG;IAEpC,wBAAwB;QACtB,OAAO;YACL,KAAK,IAAI,CAAC,CAAC;YACX,OAAO,IAAI,CAAC,CAAC;YACb,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM;YAC5B,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK;QAC3B;IACF;AACF;AAEA,MAAM;IACJ;;GAEC,GACD,YAAY,GAAG,CAAE;QACf,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAAC,KAAK,GAAG,IAAI;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,GAAG,OAAO;IAChD;IAEA;;;;GAIC,GACD,eAAe,CAAC,EAAE,CAAC,EAAE;QACnB,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG;QACf,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc;IACvD;IAEA;;;;GAIC,GAED;;GAEC,GACD,WAAW,EAAE,EAAE;QACb,IAAI,MAAwC,IAAI,CAAC,GAAG,CAAC,aAAa,EAAG,IAAI;QACzE,MAAO,OAAO,QAAQ,IAAK;YACzB,GAA4B,KAAM;YAClC,MAA+B,IAAI,aAAa;YAChD;QACF;IACF;IAEA,YAAY;QACV,MAAM,SAAS,IAAI;QACnB,IAAI,CAAC,UAAU,CAAC,CAAA;YACd,MAAM,iBAAiB,iBAAiB,KAAK,SAAS;YACtD,IAAI,gBAAgB;gBAClB,MAAM,WAAW,IAAI,UAAU;gBAC/B,OAAO,eAAe,CAAC;YACzB;QACF;QACA,OAAO;IACT;IAEA,SAAS;QACP,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK;YACpB,IAAI,CAAC,gBAAgB,CAAC,EAAE,GAAG,IAAI,KAAK,CAAC,SAAS;YAC9C,IAAI,KAAK,CAAC,SAAS,GAAG;QACxB;IACF;IAEA,SAAS;QACP,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK;YACpB,MAAM,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE;YACnC,IAAI,OAAO,IAAI;gBACb,IAAI,KAAK,CAAC,cAAc,CAAC;YAC3B,OAAO;gBACL,IAAI,KAAK,CAAC,SAAS,GAAG;YACxB;QACF;IACF;AACF;AAEA;;;;;CAKC,GACD,MAAM,kCAAkC,CAAC,OAAO,YAAc,SAAS,MAAM,SAAS,qBAAqB,GAAE,AAAC,MAAO,aAAa;AAElI,IAAI,SAAS;AAEb,MAAM;IACJ;;;GAGC,GACD,YAAY,MAAM,EAAE,aAAa,CAAC,CAAC,CAAE;QACnC,IAAI,CAAC,QAAQ;QACb,IAAI,QAAQ,KAAK,EAAE,QAAQ,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QACtD,MAAM,SAAS,WAAW,CAAC;QAC3B,MAAM,SAAS,WAAW,CAAC;QAC3B,MAAM,UAAU,WAAW,OAAO;QAClC,MAAM,WAAW,WAAW,QAAQ;QACpC,MAAM,OAAO,WAAW,WAAW;QACnC,MAAM,aAAa,QAAQ,aAAa;QACxC,MAAM,YAAY,CAAC,MAAM,SAAS,CAAC,MAAM,mBAAmB,GAAE,AAAC,KAAM,IAAI;QACzE,MAAM,QAA8B,MAAM,WAAW,CAAC,MAAM,mBAAmB,GAAE,AAAC,OAAQ,KAAK,IAAI,mBAAmB,GAAE,AAAC,OAAQ,KAAK,GAAG;QACzI,MAAM,QAA8B,MAAM,WAAW,CAAC,MAAM,mBAAmB,GAAE,AAAC,OAAQ,KAAK,IAAI,mBAAmB,GAAE,AAAC,OAAQ,KAAK,GAAG;QACzI,MAAM,YAAY,gCAAgC,WAAW,SAAS,EAAE,IAAI;QAC5E,IAAI,CAAC,cAAc,GAAG,MAAM,aAAa,YAAY;QACrD,IAAI,CAAC,UAAU,GAA8B,aAAa,CAAC,IAAI,CAAC,cAAc,GAAG,aAAsC,UAAW,CAAC,EAAE,GAAG,IAAI,IAAI;QAChJ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI;QAC1C,iCAAiC,GACjC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU;QAC3D,IAAI,CAAC,OAAO,GAA8B,MAAM,UAAU,IAAI,SAAS,UAAU,aAAa,OAAO,CAAC,EAAE;QACxG,IAAI,CAAC,QAAQ,GAA8B,aAAa,UAAU,UAAU,OAAO,CAAC,EAAE;QACtF,IAAI,CAAC,KAAK,GAAG,eAAe,IAAI,CAAC,OAAO,EAAE,gBAAgB;QAC1D,yBAAyB;QACzB,IAAI,CAAC,aAAa,GAAG;QACrB,6CAA6C,GAC7C,IAAI,CAAC,gBAAgB,GAAG;YAAC;YAAG;YAAG;YAAG;SAAE;QACpC,mBAAmB,GACnB,IAAI,CAAC,iBAAiB,GAAG;QACzB,mBAAmB,GACnB,IAAI,CAAC,wBAAwB,GAAG;QAChC,iCAAiC,GACjC,IAAI,CAAC,KAAK,GAAG;QACb,iCAAiC,GACjC,IAAI,CAAC,KAAK,GAAG;QACb,mBAAmB,GACnB,IAAI,CAAC,WAAW,GAAG;QACnB,mBAAmB,GACnB,IAAI,CAAC,eAAe,GAAG;QACvB,mBAAmB,GACnB,IAAI,CAAC,SAAS,GAAG;QACjB,mBAAmB,GACnB,IAAI,CAAC,WAAW,GAAG;QACnB,mBAAmB,GACnB,IAAI,CAAC,WAAW,GAAG;QACnB,mBAAmB,GACnB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,0CAA0C,GAC1C,IAAI,CAAC,MAAM,GAAG;QACd,mBAAmB,GACnB,IAAI,CAAC,cAAc,GAAG,YAAkC,OAAQ,aAAa;YAC3E,MAAM,SAAS,WAAW,WAAW,EAAE;YACvC,WAAW,SAAS,WAAW,gBAAgB,EAAE;YACjD,SAAS,SAAS,WAAW,cAAc,EAAE;QAC/C;QACA,mBAAmB,GACnB,IAAI,CAAC,cAAc,GAAG,YAAkC,OAAQ,aAAa;YAC3E,MAAM,SAAS,WAAW,WAAW,EAAE;YACvC,WAAW,SAAS,WAAW,gBAAgB,EAAE;YACjD,SAAS,SAAS,WAAW,cAAc,EAAE;QAC/C;QACA,2BAA2B,GAC3B,IAAI,CAAC,WAAW,GAAG,cAAc,MAAM,QAAQ;QAC/C,oBAAoB,GACpB,IAAI,CAAC,gBAAgB,GAAG;QACxB,2BAA2B,GAC3B,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,IAAI;QACnC,2BAA2B,GAC3B,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,IAAI;QACnC,2BAA2B,GAC3B,IAAI,CAAC,SAAS,GAAG,WAAW,SAAS,IAAI;QACzC,2BAA2B,GAC3B,IAAI,CAAC,QAAQ,GAAG,WAAW,QAAQ,IAAI;QACvC,2BAA2B,GAC3B,IAAI,CAAC,QAAQ,GAAG,WAAW,QAAQ,IAAI;QACvC,2BAA2B,GAC3B,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,IAAI;QACnC,2BAA2B,GAC3B,IAAI,CAAC,QAAQ,GAAG,WAAW,QAAQ,IAAI;QACvC,2BAA2B,GAC3B,IAAI,CAAC,aAAa,GAAG,WAAW,aAAa,IAAI;QACjD,6BAA6B,GAC7B,IAAI,CAAC,QAAQ,GAAG;YAAC;YAAG;SAAE;QACtB,6BAA6B,GAC7B,MAAM,mBAAmB,CAAC;QAC1B,IAAI,UAAU,iBAAiB,QAAQ,GAAG;QAC1C,IAAI,MAAM,WAAW,WAAW,MAAM;YACpC,gBAAgB,CAAC,MAAM,GAAG;QAC5B,OAAO,IAAI,MAAM,SAAS;YACxB,MAAM,eAAiD;YACvD,MAAM,oBAAoB,CAAC;YAC3B,IAAI,aAAa,QAAQ,EAAE,kBAAkB,QAAQ,GAAG,aAAa,QAAQ;YAC7E,IAAI,aAAa,WAAW,EAAE,kBAAkB,WAAW,GAAG,aAAa,WAAW;YACtF,gBAAgB,CAAC,MAAM,GAAG;QAC5B,OAAO,IAAI,WAAW,OAAO;YAC3B,gBAAgB,CAAC,MAAM,GAAG;YAC1B,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;QACrB;QACA,IAAI,MAAM,WAAW,WAAW,MAAM;YACpC,gBAAgB,CAAC,MAAM,GAAG;QAC5B,OAAO,IAAI,MAAM,SAAS;YACxB,MAAM,eAAiD;YACvD,MAAM,oBAAoB,CAAC;YAC3B,IAAI,aAAa,QAAQ,EAAE,kBAAkB,QAAQ,GAAG,aAAa,QAAQ;YAC7E,IAAI,aAAa,WAAW,EAAE,kBAAkB,WAAW,GAAG,aAAa,WAAW;YACtF,gBAAgB,CAAC,MAAM,GAAG;QAC5B,OAAO,IAAI,WAAW,OAAO;YAC3B,gBAAgB,CAAC,MAAM,GAAG;YAC1B,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;QACrB;QACA,6BAA6B,GAC7B,IAAI,CAAC,OAAO,GAAmC,IAAI,WAAW,IAAI,CAAC,OAAO,EAAE;QAC5E,iBAAiB;QACjB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;YAAC,GAAG;YAAG,GAAG;QAAC;QACzB,6CAA6C,GAC7C,IAAI,CAAC,MAAM,GAAG;YAAC,IAAI,CAAC,CAAC;YAAE,IAAI,CAAC,CAAC;YAAE;YAAG;SAAE,EAAE,uBAAuB;QAC7D,6BAA6B,GAC7B,IAAI,CAAC,OAAO,GAAG;YAAC;YAAG;SAAE,EAAE,OAAO;QAC9B,6EAA6E,GAC7E,IAAI,CAAC,OAAO,GAAG;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE,EAAE,qDAAqD;QAC9F,6BAA6B,GAC7B,IAAI,CAAC,UAAU,GAAG;YAAC;YAAG;SAAE,EAAE,OAAO;QACjC,6CAA6C,GAC7C,IAAI,CAAC,QAAQ,GAAG;YAAC;YAAG;YAAG;YAAG;SAAE,EAAE,aAAa;QAC3C,6CAA6C,GAC7C,IAAI,CAAC,eAAe,GAAG;YAAC,CAAC;YAAM;YAAU;YAAU,CAAC;SAAK,EAAE,aAAa;QACxE,6CAA6C,GAC7C,IAAI,CAAC,YAAY,GAAG;YAAC;YAAG;YAAG;YAAG;SAAE,EAAE,aAAa;QAC/C,6CAA6C,GAC7C,IAAI,CAAC,YAAY,GAAG;YAAC;YAAG;YAAG;YAAG;SAAE,EAAE,aAAa;QAC/C,6BAA6B,GAC7B,IAAI,CAAC,MAAM,GAAG;YAAC;YAAG;SAAE,EAAE,OAAO;QAC7B,qCAAqC,GACrC,IAAI,CAAC,aAAa,GAAG;YAAC;YAAG;YAAG;SAAE;QAC9B,mBAAmB,GACnB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,mBAAmB,GACnB,IAAI,CAAC,YAAY,GAAG;QACpB,mBAAmB,GACnB,IAAI,CAAC,QAAQ,GAAG;QAChB,mBAAmB,GACnB,IAAI,CAAC,KAAK,GAAG;QACb,wBAAwB,GACxB,IAAI,CAAC,YAAY,GAAG;QACpB,wBAAwB,GACxB,IAAI,CAAC,aAAa,GAAG;QACrB,wBAAwB,GACxB,IAAI,CAAC,UAAU,GAAG;QAClB,wBAAwB,GACxB,IAAI,CAAC,YAAY,GAAG;QACpB,wBAAwB,GACxB,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,WAAW,IAAI,CAAC,OAAO;QAC7C,IAAI,CAAC,eAAe,GAAG;YAAE,GAAG;YAAG,GAAG;QAAE;QACpC,IAAI,CAAC,gBAAgB,GAAG,IAAI,MAAM;YAAE,UAAU;QAAM,GAAG,MAAM,GAAG,IAAI;QACpE,IAAI,CAAC,gBAAgB,GAAG,IAAI,MAAM;YAAE,UAAU;QAAM,GAAG,MAAM,GAAG,IAAI;QACpE,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM;YAAE,UAAU;QAAM,GAAG,MAAM,GAAG,IAAI;QAChE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG;YAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;YACtB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE;QACnD;QACA,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG;YACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;YACtB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE;QACnD;QACA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG;YAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;YACtB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE;QACnD;QACA,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG;YACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;YACtB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE;QACnD;QACA,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAM,IAAI,CAAC,MAAM;QAC9C,IAAI,CAAC,SAAS,GAAG,CAAC,MAAM;QACxB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,QAAQ;QAC7C,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,GAAG;YAClD,MAAM,aAAa,IAAI,CAAC,OAAO;YAC/B,MAAM,WAAW,IAAI,CAAC,OAAO,IAAI;YACjC,MAAM,cAAc,CAAC,YAAY,IAAI,CAAC,QAAQ;YAC9C,MAAM,IAAI,IAAI,CAAC,CAAC;YAChB,MAAM,IAAI,IAAI,CAAC,CAAC;YAChB,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;YAC7B,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;YAC7B,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;YACjB,IAAI,YAAY;gBACd,IAAI,CAAC,QAAQ,CAAC,IAAI;YACpB;YACA,IAAI,CAAC,aAAa;gBAChB,IAAI,CAAC,OAAO,GAAG;YACjB,OAAO;gBACL,IAAI,CAAC,eAAe,CAAC,IAAI;gBACzB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;YACzB;QACF;QACA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,UAAU,GAAG;YACpD,IAAK,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAG;gBACpC,kEAAkE;gBAClE,IAAI,CAAC,QAAQ,GAAG;YAClB;YACA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG;gBACxB,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG;gBACxB,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG;gBACxB,IAAI,CAAC,kBAAkB,GAAG;gBAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI;YACpB;QACF;QACA,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM;YAC5B,UAAU;YACV,UAAU,MAAM,QAAQ,SAAS;YACjC,YAAY;gBACV,IAAI,CAAC,QAAQ,CAAC,IAAI;gBAClB,IAAI,CAAC,OAAO;gBACZ,IAAI,CAAC,aAAa,CAAC,IAAI;YACzB;QACF,GAAG,IAAI;QACP,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,cAAc,GAAG,IAAI,eAAe;YACvC,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,YAAY,CAAC,OAAO;YAC3B,OAAO;gBACL,IAAI,CAAC,WAAW,GAAG;YACrB;QACF;QACA,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU;QAC3C,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO;IAC9D;IAEA;;;;GAIC,GACD,gBAAgB,EAAE,EAAE,EAAE,EAAE;QACtB,MAAM,WAAW,IAAI,CAAC,YAAY;QAClC,MAAM,UAAU;QAChB,MAAM,UAAU,UAAU;QAC1B,IAAI,UAAU,IAAI,OAAO,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAC,YAAY,GAAG;QACpB,MAAM,gBAAgB,IAAI,CAAC,aAAa;QACxC,MAAM,OAAO,IAAI,CAAC,kBAAkB;QACpC,MAAM,OAAO,IAAI,CAAC,WAAW;QAC7B,MAAM,OAAO,IAAI,CAAC,WAAW;QAC7B,MAAM,KAAK,IAAI,CAAC,kBAAkB;QAClC,aAAa,CAAC,GAAG,GAAG,MAAM,MAAM,AAAC,KAAK,KAAK,KAAK,KAAK,MAAM,UAAW,MAAM,MAAM,OAAO;QACzF,MAAM,WAAW,IAAI,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE;QACzE,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,kBAAkB,GAAG,CAAC,KAAK,CAAC,IAAI;QACrC,OAAO;IACT;IAEA;;;;GAIC,GACD,KAAK,CAAC,EAAE,qBAAqB,KAAK,EAAE;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;QACtB,MAAM,IAAI,MAAM,GAAG;QACnB,IAAI,CAAC,gBAAgB,CAAC,KAAK;QAC3B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK;QACpC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG;QAC5B,IAAI,CAAC,MAAM,GAAG;QACd,OAAO,IAAI;IACb;IAEA;;;;GAIC,GACD,KAAK,CAAC,EAAE,qBAAqB,KAAK,EAAE;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;QACtB,MAAM,IAAI,MAAM,GAAG;QACnB,IAAI,CAAC,gBAAgB,CAAC,KAAK;QAC3B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK;QACpC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG;QAC5B,IAAI,CAAC,MAAM,GAAG;QACd,OAAO,IAAI;IACb;IAEA,IAAI,IAAI;QACN,OAAO,MAA4B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAK,QAAQ,SAAS;IACnF;IAEA,IAAI,EAAE,CAAC,EAAE;QACP,IAAI,CAAC,IAAI,CAAC,GAAG;IACf;IAEA,IAAI,IAAI;QACN,OAAO,MAA4B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAK,QAAQ,SAAS;IACnF;IAEA,IAAI,EAAE,CAAC,EAAE;QACP,IAAI,CAAC,IAAI,CAAC,GAAG;IACf;IAEA,IAAI,YAAY;QACd,OAAO,SAAS,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,GAAG;IAC/E;IAEA,IAAI,UAAU,CAAC,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG;IACjF;IAEA,IAAI,YAAY;QACd,OAAO,SAAS,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,GAAG;IAC/E;IAEA,IAAI,UAAU,CAAC,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG;IACjF;IAEA,qBAAqB;QACnB,MAAM,KAAK,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;QACzE,MAAM,KAAK,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;QACxE,MAAM,CAAE,KAAK,KAAK,KAAK,IAAK,GAAG,IAAI,CAAC,gBAAgB;QACpD,MAAM,YAAY,IAAI,CAAC,eAAe;QACtC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG;QAChB,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG;QAChB,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM;QACzD,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM;QACzD,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM;QACzD,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM;IAC3D;IAEA,uBAAuB;QACrB,MAAM,aAAa,IAAI,CAAC,UAAU;QAClC,MAAM,KAAK,IAAI,CAAC,CAAC;QACjB,MAAM,KAAK,IAAI,CAAC,CAAC;QACjB,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE;QAC1B,MAAM,MAAO,IAAI,CAAC,MAAM,CAAC,EAAE;QAC3B,4FAA4F;QAC5F,4FAA4F;QAC5F,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;QACjB,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;QACjB,IAAI,CAAC,IAAI,CAAC,GAAG;QACb,IAAI,CAAC,IAAI,CAAC,GAAG;QACb,IAAI,CAAC,UAAU,CAAC,MAAM;QACtB,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,UAAU;QAC1C,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,WAAW;QAC3C,MAAM,KAAK,IAAI,CAAC,MAAM;QACtB,MAAM,KAAK,WAAW,WAAW;QACjC,MAAM,KAAK,WAAW,YAAY;QAClC,MAAM,KAAK,IAAI,CAAC,KAAK;QACrB,MAAM,yBAAyB,WAAW,qBAAqB;QAC/D,MAAM,CAAE,KAAK,KAAK,KAAK,IAAK,GAAG,IAAI,CAAC,gBAAgB;QACpD,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,KAAK,IAAI,uBAAuB,IAAI;QACvD,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,KAAK,IAAI,uBAAuB,GAAG;QACtD,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,KAAK,MAAM,IAAI,IAAI,MAAM;QAC9C,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,KAAK,MAAM,IAAI,IAAI,MAAM;QAC9C,IAAI,CAAC,kBAAkB;QACvB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,WAAW,qBAAqB;QACpF,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,MAAM,KAAK,MAAM,OAAO,IAAI,MAAM,OAAO;QAC5D,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,MAAM,KAAK,MAAM,QAAQ,IAAI,MAAM,QAAQ;QAC9D,MAAM,oBAAoB,eAAe,YAAY;QACrD,MAAM,kBAAkB,sBAAsB;QAC9C,MAAM,iBAAiB,sBAAsB;QAC7C,IAAI,CAAC,SAAS,GAAG,KAAK,QACpB,IAAI,CAAC,SAAS,IACd,CAAC,AAAC,eAAe,IAAI,IAAI,IAAI,mBAAqB,CAAC,kBAAkB,CAAC,eAAgB,KACtF,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,MAAM,OAAO,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,MAAM,GAAG,KACvE,CAAC,CAAC,IAAI,CAAC,cAAc,IAAK,IAAI,CAAC,cAAc,IAAI,CAAC,MAAM,IAAI,CAAC,cAAc,CAAE;QAC/E,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;YACxB,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;YACxB,MAAM,YAAY,IAAI,CAAC,SAAS;YAChC,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC,qBAAqB;YACrD,MAAM,aAAa,YAAY,KAAK,IAAI,WAAW,UAAU,GAAG;YAChE,MAAM,YAAY,YAAY,KAAK,IAAI,WAAW,SAAS,GAAG;YAC9D,MAAM,cAAc,YAAY,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,aAAa,QAAQ;YAC1E,MAAM,eAAe,YAAY,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,YAAY,SAAS;YAC3E,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM,AAAC,WAAW,GAAG,GAAG,KAAM,CAAC,KAAK,IAAI,GAAG,GAAG;YACrE,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM,AAAC,WAAW,KAAK,GAAG,KAAM,CAAC,KAAK,KAAK,KAAK,GAAG;YAC1E,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM,AAAC,WAAW,MAAM,GAAG,KAAM,CAAC,KAAK,KAAK,MAAM,GAAG;YAC5E,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM,AAAC,WAAW,IAAI,GAAG,KAAM,CAAC,KAAK,IAAI,IAAI,GAAG;YACvE,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,GAAG;gBACnD,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,GAAG;gBACnD,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,GAAG;gBACnD,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,GAAG;YACrD,OAAO;gBACL,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,MAAM,GAAG,IAAI,YAAY,KAAK;gBACpG,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,MAAM,OAAO,GAAG,MAAM,KAAK,IAAI,cAAc,KAAK;gBAC5G,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC,MAAM,WAAW,MAAM,GAAG,CAAC,KAAK,MAAM,QAAQ,GAAG,MAAM,MAAM,IAAI,eAAe,KAAK;gBAChH,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC,MAAM,WAAW,IAAI,GAAG,CAAC,KAAK,MAAM,MAAM,GAAG,MAAM,IAAI,IAAI,aAAa,KAAK;YAC1G;QACF;QACA,IAAI,CAAC,UAAU,CAAC,MAAM;QACtB,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;QACjB,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;QACjB,IAAI,CAAC,IAAI,CAAC,IAAI;QACd,IAAI,CAAC,IAAI,CAAC,IAAI;IAChB;IAEA;;;;;;;GAOC,GACD,cAAc,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO;QAC5B,MAAM,CAAE,IAAI,IAAI,IAAI,GAAI,GAAG;QAC3B,MAAM,CAAE,IAAI,GAAI,GAAG,IAAI,CAAC,QAAQ;QAChC,MAAM,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI;QACxC,MAAM,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI;QACxC,OAAO,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI;IAC9D;IAEA,UAAU;QACR,MAAM,SAAS,IAAI,CAAC,UAAU;QAC9B,MAAM,SAAS,OAAO,CAAC;QACvB,MAAM,SAAS,OAAO,CAAC;QACvB,MAAM,YAAY,gCAAgC,OAAO,SAAS,EAAE,IAAI;QACxE,MAAM,KAAK,gCAAgC,OAAO,gBAAgB,EAAE,IAAI,KAAK;QAC7E,MAAM,mBAAmE,MAAM,MAAM,KAAK;YAAC;YAAI;YAAI;YAAI;SAAG;QAC1G,MAAM,KAAK,IAAI,CAAC,CAAC;QACjB,MAAM,KAAK,IAAI,CAAC,CAAC;QACjB,MAAM,qBAAqB,gCAAgC,OAAO,MAAM,EAAE,IAAI;QAC9E,MAAM,eAAe;YAAE,SAAS;YAAQ,QAAQ;QAAW;QAC3D,IAAI,oBAAoB;YACtB,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAwC;YACjE,IAAI,SAAS,aAAa,OAAO,GAAG;YACpC,IAAI,QAAQ,aAAa,MAAM,GAAG;QACpC;QACA,IAAI,CAAC,cAAc,GAAG,MAAM,aAAa,YAAY;QACrD,IAAI,CAAC,UAAU,GAA8B,aAAa,CAAC,IAAI,CAAC,cAAc,GAAG,aAAsC,UAAW,CAAC,EAAE,GAAG,IAAI,IAAI;QAChJ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI;QAC1C,iCAAiC,GACjC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU;QAC3D,IAAI,CAAC,aAAa,GAAG,WAAW,kBAAkB,OAAO;QACzD,IAAI,CAAC,gBAAgB,GAAG,SAAS,kBAAkB;YAAC;YAAG;YAAG;YAAG;SAAE;QAC/D,IAAI,CAAC,iBAAiB,GAAG,MAAM,SAAS,gCAAgC,OAAO,iBAAiB,EAAE,IAAI,GAAG,KAAK,GAAG;QACjH,IAAI,CAAC,wBAAwB,GAAG,MAAM,SAAS,gCAAgC,OAAO,wBAAwB,EAAE,IAAI,GAAG,IAAI,CAAC,iBAAiB,GAAG,GAAG;QACnJ,IAAI,CAAC,KAAK,GAAG,gCAAgC,MAAM,WAAW,CAAC,MAAM,OAAO,IAAI,IAAI,OAAO,IAAI,GAAG,OAAO,IAAI,EAAE,IAAI;QACnH,IAAI,CAAC,KAAK,GAAG,gCAAgC,MAAM,WAAW,CAAC,MAAM,OAAO,IAAI,IAAI,OAAO,IAAI,GAAG,OAAO,IAAI,EAAE,IAAI;QACnH,IAAI,CAAC,WAAW,GAAG,SAAS,gCAAgC,OAAO,WAAW,EAAE,IAAI,GAAG;QACvF,IAAI,CAAC,eAAe,GAAG,SAAS,gCAAgC,OAAO,eAAe,EAAE,IAAI,GAAG;QAC/F,IAAI,CAAC,SAAS,GAAG,SAAS,gCAAgC,OAAO,SAAS,EAAE,IAAI,GAAG;QACnF,IAAI,CAAC,WAAW,GAAG,SAAS,gCAAgC,OAAO,WAAW,EAAE,IAAI,GAAG;QACvF,IAAI,CAAC,WAAW,GAAG,SAAS,gCAAgC,OAAO,WAAW,EAAE,IAAI,GAAG;QACvF,IAAI,CAAC,kBAAkB,GAAG,SAAS,gCAAgC,OAAO,kBAAkB,EAAE,IAAI,GAAG;QACrG,IAAI,CAAC,MAAM,GAAG,uBAAuB,QAAQ,QAAQ;QACrD,IAAI,CAAC,oBAAoB;QAEzB,uEAAuE;QACvE,iDAAiD;QACjD,iDAAiD;QAEjD,4CAA4C;QAC5C,oDAAoD;QACpD,oDAAoD;QACpD,IAAI;QAEJ,MAAM,CAAE,IAAI,IAAI,IAAI,GAAI,GAAG,IAAI,CAAC,eAAe;QAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK;QAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK;IAC/B;IAEA,SAAS;QACP,IAAI,CAAC,kBAAkB;QACvB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,CAAE,KAAK,KAAK,KAAK,IAAK,GAAG,IAAI,CAAC,gBAAgB;YACpD,MAAM,CAAE,IAAI,GAAI,GAAG,IAAI,CAAC,UAAU;YAClC,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC5B,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC5B,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC;YACzB,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC;YACzB,MAAM,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW;YACvC,MAAM,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY;YACxC,MAAM,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO;YAC5D,MAAM,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO;YAC5D,MAAM,MAAM,KAAK;YACjB,MAAM,MAAM,KAAK;YACjB,mEAAmE;YACnE,IAAI,IAAI,CAAC,OAAO,IAAI,MAAM,GAAG;gBAC3B,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI;gBAClB,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG;YACvB;YACA,IAAI,IAAI,CAAC,OAAO,IAAI,MAAM,GAAG;gBAC3B,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI;gBAClB,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG;YACvB;YACA,qEAAqE;YACrE,MAAM,IAAI,IAAI,CAAC,WAAW,GAAG;YAC7B,MAAM,YAAY,IAAI,CAAC,eAAe;YACtC,MAAM,CAAE,GAAG,EAAG,GAAG,IAAI,CAAC,MAAM;YAC5B,MAAM,CAAE,IAAI,IAAI,IAAI,GAAI,GAAG,IAAI,CAAC,YAAY;YAC5C,MAAM,IAAI,MAAM,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,WAAW,CAAC,GAAG,KAAK,GAAG;YAC9D,MAAM,IAAI,MAAM,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,WAAW,GAAG,KAAK,GAAG;YAC7D,MAAM,IAAI,MAAM,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,WAAW,GAAG,KAAK,GAAG;YAC7D,MAAM,IAAI,MAAM,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,WAAW,CAAC,GAAG,KAAK,GAAG;YAC9D,IAAI,KAAK,KAAK,KAAK,GAAG;gBACpB,MAAM,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ;gBAC9B,IAAI,UAAU;gBACd,IAAI,UAAU;gBACd,IAAI,CAAC,IAAI;oBACP,UAAU,MAAM,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,MAAM;oBACpD,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,MAAM;gBAC1B;gBACA,IAAI,CAAC,IAAI;oBACP,UAAU,MAAM,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,MAAM;oBACpD,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,MAAM;gBAC1B;gBACA,oGAAoG;gBACpG,IAAI,IAAI,CAAC,MAAM,EAAE;oBACf,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,OAAO,GAAG,CAAC,CAAC,MAAM,OAAO;gBAClE,OAAO;oBACL,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS;gBAC1C;YACF;QACF;QACA,MAAM,CAAE,IAAI,IAAI,IAAI,GAAI,GAAG,IAAI,CAAC,eAAe;QAC/C,MAAM,CAAE,KAAK,KAAK,KAAK,KAAK,KAAK,IAAK,GAAG,IAAI,CAAC,OAAO;QACrD,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS;QAC9C,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS;QAC9C,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,MAAM,CAAE,IAAI,GAAI,GAAG,IAAI,CAAC,MAAM;QAC9B,MAAM,CAAE,IAAI,GAAI,GAAG,IAAI,CAAC,OAAO;QAC/B,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,SAAS;QACxD,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,IAAI;QAC9E,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,IAAI;QAC9E,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,MAAM;QACtC,IAAI,CAAC,KAAK,GAAG,MAAM,MAAM,KAAK,MAAM;QACpC,MAAM,CAAE,KAAK,IAAK,GAAG,IAAI,CAAC,OAAO;QACjC,IAAI,QAAQ,MAAM,IAAI,CAAC,KAAK,IAAI,QAAQ,MAAM,IAAI,CAAC,KAAK,EAAE;YACxD,IAAI,CAAC,MAAM,CAAC,IAAI;QAClB;IACF;IAEA,OAAO;QACL,IAAI,CAAC,YAAY,CAAC,KAAK;QACvB,IAAI,CAAC,gBAAgB,CAAC,KAAK;QAC3B,IAAI,CAAC,gBAAgB,CAAC,KAAK;QAC3B,4CAA4C;QAC5C,IAAK,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,CAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK;QAC7E,OAAO,IAAI,EAAE,MAAM;QACnB,OAAO,IAAI,EAAE,MAAM;QACnB,OAAO,IAAI,EAAE,MAAM;QACnB,OAAO,IAAI,EAAE,MAAM;QACnB,OAAO,IAAI,CAAC,MAAM,GAAG,wDAAwD;QAC7E,OAAO,IAAI,CAAC,eAAe,GAAG,sCAAsC;QACpE,OAAO,IAAI;IACb;IAEA;;;;;GAKC,GACD,aAAa,QAAQ,EAAE,MAAM,CAAC,EAAE,OAAO,MAAM,SAAS,EAAE;QACtD,IAAI,CAAC,kBAAkB;QACvB,MAAM,IAAI,IAAI,CAAC,KAAK;QACpB,MAAM,IAAI,IAAI,CAAC,KAAK;QACpB,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,MAAM,eAAe,IAAI,CAAC,YAAY;QACtC,MAAM,YAAY,IAAI,CAAC,SAAS;QAChC,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,IAAI;YAClE,MAAM,CAAE,IAAI,IAAI,IAAI,GAAI,GAAG;YAC3B,MAAM,IAAI,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI;YACzC,MAAM,IAAI,MAAM,MAAM,IAAI,IAAI,GAAG,WAAW;YAC5C,MAAM,IAAI,MAAM,MAAM,IAAI,IAAI,GAAG,WAAW;YAC5C,MAAM,IAAI,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI;YACzC,IAAI,YAAY,QAAQ;gBACtB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,CAAC,GAAG;gBACrD,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,CAAC,GAAG;gBACrD,UAAU,MAAM,YAAY,MAAM,QAAQ,SAAS,GAAG;gBACtD;gBACA,UAAU;oBACR,IAAI,CAAC,SAAS,GAAG;oBACjB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;gBACnD;YACF,GAAG,IAAI,GAAG,IAAI,CAAC;gBACb,IAAI,CAAC,SAAS,GAAG;YACnB;QACF;QACA,OAAO,IAAI;IACb;IAEA,cAAc;QACZ,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YAC3D,IAAI,CAAC,YAAY,GAAG,gBAAgB,IAAI,CAAC,QAAQ,EAAE;gBACjD,QAAQ,kCAAkC,GAAE,AAAC,IAAI,CAAC,MAAM,CAAE,OAAO;YACnE;QACF;IACF;IAEA;;;;;GAKC,GACD,cAAc,QAAQ,EAAE,MAAM,CAAC,EAAE,OAAO,MAAM,SAAS,EAAE;QACvD,IAAI,CAAC,IAAI;QACT,IAAI,CAAC,oBAAoB;QACzB,MAAM,IAAI,IAAI,CAAC,CAAC;QAChB,MAAM,IAAI,IAAI,CAAC,CAAC;QAChB,MAAM,CAAE,KAAK,KAAK,KAAK,IAAK,GAAG,IAAI,CAAC,gBAAgB;QACpD,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM;QACxD,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM;QACxD,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM;QACxD,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,MAAM;QACxD,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC;YAAC;YAAI;YAAI;YAAI;SAAG,EAAE,GAAG;QACnD,IAAI,IAAI;YACN,MAAM,CAAE,WAAW,UAAW,GAAG,IAAI,CAAC,QAAQ;YAC9C,MAAM,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI;YAC7C,MAAM,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI;YAC7C,MAAM,MAAM,MAAM,YAAY,MAAM,QAAQ,SAAS,GAAG;YACxD,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK;YAC/E,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK;QACjF;QACA,OAAO,IAAI;IACb;IAEA;;GAEC,GACD,WAAW,CAAC,EAAE;QACZ,MAAM,WAAsC,EAAE,MAAM;QACpD,IAAI,IAAI,CAAC,OAAO,IAAI,8BAA8B,GAAE,AAAC,SAAU,IAAI,KAAK,SAAS;QAEjF,EAAE,eAAe;QAEjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI;QACT,IAAI,CAAC,oBAAoB;QACzB,MAAM,UAAU,uBAAuB,GAAE,AAAC,EAAG,cAAc;QAC3D,MAAM,SAAS,UAAU,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,uBAAuB,GAAE,AAAC,EAAG,OAAO;QAClF,MAAM,SAAS,UAAU,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,uBAAuB,GAAE,AAAC,EAAG,OAAO;QAClF,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ;QACxD,MAAM,CAAE,IAAI,IAAI,IAAI,GAAI,GAAG,IAAI,CAAC,eAAe;QAC/C,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,SAAS;QACxD,MAAM,KAAK,IAAI,CAAC,CAAC;QACjB,MAAM,KAAK,IAAI,CAAC,CAAC;QACjB,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK;QAC7G,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK;QAC7G,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG;QACxB,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG;QACxB,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG;QACxB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,MAAM;YACxB,IAAI,CAAC,YAAY,GAAG;QACtB;QACA,MAAM,IAA0B,eAAe,IAAI,CAAC,OAAO,EAAE,UAAU;QACvE,SAAS,CAAC,IAAI,SAAS,IAAI,MAAM,IAAI;QACrC,IAAI,CAAC,YAAY,GAAG,gBAAgB,IAAI,CAAC,OAAO,EAAE;YAAE;QAAO;QAC3D,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,MAAM;YACzB,IAAI,CAAC,aAAa,GAAG;QACvB;QACA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,MAAM;YACxB,IAAI,CAAC,YAAY,GAAG;QACtB;QACA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,EAAE;YACrC,IAAI,CAAC,UAAU,GAAG,gBAAgB,IAAI,IAAI,EAAE;gBAC1C,QAAQ,kCAAkC,GAAE,AAAC,IAAI,CAAC,MAAM,CAAE,MAAM;YAClE;QACF;QACA,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,IAAI;QAEhB,IAAI,gBAAgB,CAAC,aAAa,IAAI;QACtC,IAAI,gBAAgB,CAAC,YAAY,IAAI;QACrC,IAAI,gBAAgB,CAAC,eAAe,IAAI;QACxC,IAAI,gBAAgB,CAAC,aAAa,IAAI;QACtC,IAAI,gBAAgB,CAAC,WAAW,IAAI;QACpC,IAAI,gBAAgB,CAAC,eAAe,IAAI;IAC1C;IAEA;;GAEC,GACD,WAAW,CAAC,EAAE;QACZ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACnB,MAAM,UAAU,uBAAuB,GAAE,AAAC,EAAG,cAAc;QAC3D,MAAM,SAAS,UAAU,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,uBAAuB,GAAE,AAAC,EAAG,OAAO;QAClF,MAAM,SAAS,UAAU,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,uBAAuB,GAAE,AAAC,EAAG,OAAO;QAClF,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ;QACxD,MAAM,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;QAClC,MAAM,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;QAElC,IAAI,UAAqC,EAAE,MAAM;QACjD,IAAI,UAAU;QACd,IAAI,aAAa;QACjB,IAAI,iBAAiB;QAErB,MAAO,WAAW,WAAW,YAAY,IAAI,CAAC,QAAQ,CAAE;YACtD,MAAM,YAAY,eAAe,SAAS;YAC1C,IAAI,cAAc,YAAY,cAAc,WAAW;gBACrD,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;gBAClD,IAAI,eAAe,cAAc;oBAC/B,iBAAiB;oBACjB,UAAU,aAAa;oBACvB,aAAa,aAAa,AAAC,eAAe,eAAgB;oBAC1D;gBACF;YACF;YACA,UAAqC,QAAQ,UAAU;QACzD;QAEA,IAAI,kBAAkB,CAAC,AAAC,CAAC,WAAW,CAAC,cAAgB,WAAW,SAAS,KAAO,cAAc,SAAS,CAAE,GAAG;YAE1G,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;YAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;YAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;YAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;YAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;YAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;YAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;YAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAEpB,OAAO;YAEL,eAAe;YAEf,uCAAuC;YACvC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,GAAG,gBAAgB,IAAI,CAAC,QAAQ,EAAE;gBAAE,eAAe;YAAO;YACrG,gEAAgE;YAChE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,cAAc,gBAAgB;gBAAE,SAAS;YAAM;YAC9E,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,gBAAgB;gBAAE,SAAS;YAAM;YAC7E,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY;YAG3C,IAAI,AAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,IAAI,UAAU,KAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,IAAI,UAAU,GAAI;gBAEpF,IAAI,CAAC,YAAY,CAAC,MAAM;gBACxB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE;gBACjC,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE;gBACjC,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;gBAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;gBAClB,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,MAAM,CAAC,IAAI;YAClB;QACF;IACF;IAEA,WAAW;QAET,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QAEnB,IAAI,CAAC,YAAY,CAAC,KAAK;QAEvB,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,MAAM;YACzB,IAAI,CAAC,aAAa,GAAG;QACvB;QAEA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,MAAM;YACtB,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,MAAM,CAAE,WAAW,UAAW,GAAG,IAAI,CAAC,QAAQ;QAC9C,MAAM,CAAE,KAAK,KAAK,KAAK,KAAK,KAAK,IAAK,GAAG,IAAI,CAAC,OAAO;QACrD,MAAM,CAAE,IAAI,IAAI,IAAI,GAAI,GAAG,IAAI,CAAC,eAAe;QAC/C,MAAM,CAAE,IAAI,GAAI,GAAG,IAAI,CAAC,OAAO;QAC/B,MAAM,UAAU,IAAI,CAAC,cAAc;QACnC,MAAM,UAAU,IAAI,CAAC,cAAc;QACnC,MAAM,cAAc,IAAI,CAAC,WAAW;QACpC,MAAM,mBAAmB,IAAI,CAAC,gBAAgB;QAC9C,MAAM,kBAAkB,IAAI,CAAC,eAAe;QAC5C,MAAM,KAAK,IAAI,CAAC,CAAC;QACjB,MAAM,KAAK,IAAI,CAAC,CAAC;QACjB,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,MAAM;QACjD,MAAM,KAAK,IAAI,CAAC,KAAK,GAAG,MAAM,MAAM,KAAK,MAAM;QAC/C,MAAM,KAAK,KAAK;QAChB,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,SAAS;QAC/D,MAAM,KAAK,KAAM,IAAI,MAAM;QAC3B,MAAM,KAAK,KAAM,IAAI,MAAM;QAC3B,MAAM,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK;QAC3E,MAAM,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK;QAC3E,MAAM,KAAK,IAAI,CAAC,KAAK,GAAG,MAAM,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI;QAClE,MAAM,KAAK,IAAI,CAAC,KAAK,GAAG,MAAM,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI;QAClE,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI;QAExD,IAAI,YAAY;QAChB,IAAI,YAAY;QAChB,IAAI,QAAQ;QACZ,IAAI,QAAQ;QACZ,IAAI,yBAAyB;QAE7B,gBAAgB,CAAC,GAAG;QACpB,gBAAgB,CAAC,GAAG;QAEpB,IAAI,CAAC,WAAW;YACd,MAAM,aAAa,OAAO,KAAK,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI;YACjE,MAAM,YAAY,MAAM,KAAK,IAAI;YACjC,QAAQ,QAAQ,GAAG,aAAa,mBAAmB,YAAY,AAAC,KAAK,aAAc,IAAI,aAAa,IAAI;YACxG,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG;YACzC,YAAY,OAAO,KAAK,IAAI,mBAAmB,WAAW,WAAY,eAAe,QAAQ,SAAS;YACtG,IAAI,kBAAkB,QAAQ;YAC9B,IAAI,YAAY,wBAAwB,yBAAyB;QACnE;QAEA,IAAI,CAAC,WAAW;YACd,MAAM,aAAa,OAAO,KAAK,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI;YACjE,MAAM,YAAY,MAAM,KAAK,IAAI;YACjC,QAAQ,QAAQ,GAAG,aAAa,mBAAmB,YAAY,AAAC,KAAK,aAAc,IAAI,aAAa,IAAI;YACxG,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG;YACzC,YAAY,OAAO,KAAK,IAAI,mBAAmB,WAAW,WAAY,eAAe,QAAQ,SAAS;YACtG,IAAI,kBAAkB,QAAQ;YAC9B,IAAI,YAAY,wBAAwB,yBAAyB;QACnE;QAEA,IAAI,CAAC,oBAAoB,MAAM,MAAM,CAAC,aAAa,SAAS,GAAG;YAE3D,MAAM,cAAc,iBAAiB,KAAK;YAE1C,IAAI,YAAY,iBAAiB;gBAC/B,GAAG;oBAAE,IAAI;oBAAI,UAAU,YAAY;gBAAI;gBACvC,GAAG;oBAAE,IAAI;oBAAI,UAAU,YAAY;gBAAI;gBACvC,MAAM;gBACN;YACF,GAAG,IAAI;YAEP,IAAI,YAAY,iBAAiB;gBAC/B,GAAG;oBAAE,IAAI;oBAAI,UAAU;gBAAU;gBACjC,GAAG;oBAAE,IAAI;oBAAI,UAAU;gBAAU;gBACjC,MAAM;gBACN;YACF,GAAG,IAAI;YAEP,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,OAAO;YAChD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,OAAO;QAEpD,OAAO;YAEL,IAAI,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,WAAW;YACxD,IAAI,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,WAAW;QAE1D;QAEA,IAAI,CAAC,YAAY,CAAC,wBAAwB,IAAI,CAAC,eAAe,EAAE;QAEhE,IAAI,aAAa;QAEjB,IAAI,OAAO,IAAI;YACb,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;YAClB,IAAI,IAAI,CAAC,KAAK,EAAE,aAAa;QAC/B;QAEA,IAAI,OAAO,MAAM,IAAI,CAAC,KAAK,EAAE;YAC3B,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;YAClB,IAAI,IAAI,CAAC,KAAK,EAAE,aAAa;QAC/B;QAEA,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI;QAEhC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;QAEhB,gGAAgG;QAChG,IAAI,CAAC,SAAS,CAAC,IAAI;QAEnB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,cAAc;QAChD,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,aAAa;QAC/C,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,YAAY;QAE9C,IAAI,mBAAmB,CAAC,aAAa,IAAI;QACzC,IAAI,mBAAmB,CAAC,YAAY,IAAI;QACxC,IAAI,mBAAmB,CAAC,eAAe,IAAI;QAC3C,IAAI,mBAAmB,CAAC,aAAa,IAAI;QACzC,IAAI,mBAAmB,CAAC,WAAW,IAAI;QACvC,IAAI,mBAAmB,CAAC,eAAe,IAAI;IAC7C;IAEA,QAAQ;QACN,IAAI,CAAC,IAAI;QACT,IAAI,CAAC,YAAY,CAAC,KAAK;QACvB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,IAAI,CAAC,GAAG;QACb,IAAI,CAAC,IAAI,CAAC,GAAG;QACb,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;QACjB,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG;QACjB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG;QACxB,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG;QACxB,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG;QACxB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,KAAK,GAAG;QACb,OAAO,IAAI;IACb;IAEA,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC;YAC9B,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,IAAI,CAAC,QAAQ,EAAE;gBACtD,aAAa,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,UAAU,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,UAAU;YACzE;YACA,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,cAAc,IAAI,EAAE;gBAAE,SAAS;YAAK;YACnE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,IAAI,EAAE;gBAAE,SAAS;YAAK;YAClE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,cAAc,IAAI;QACnD;QACA,OAAO,IAAI;IACb;IAEA,UAAU;QACR,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,iBAAiB,CAAC,MAAM;QAC7B,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,MAAM;YACxB,IAAI,CAAC,YAAY,GAAG;QACtB;QACA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,MAAM;YACzB,IAAI,CAAC,aAAa,GAAG;QACvB;QACA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,MAAM;YACtB,IAAI,CAAC,UAAU,GAAG;QACpB;QACA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,MAAM;YACxB,IAAI,CAAC,YAAY,GAAG;QACtB;QACA,IAAI,CAAC,IAAI;QACT,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC;QAC3B,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,cAAc,IAAI;QACpD,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,aAAa,IAAI;QACnD,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,cAAc,IAAI;QACpD,IAAI,mBAAmB,CAAC,aAAa,IAAI;QACzC,IAAI,mBAAmB,CAAC,YAAY,IAAI;QACxC,IAAI,mBAAmB,CAAC,eAAe,IAAI;QAC3C,IAAI,mBAAmB,CAAC,aAAa,IAAI;QACzC,IAAI,mBAAmB,CAAC,WAAW,IAAI;QACvC,IAAI,mBAAmB,CAAC,eAAe,IAAI;QAC3C,OAAO,IAAI;IACb;IAEA,SAAS;QACP,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,YAAY,CAAC,MAAM;QACxB,IAAI,CAAC,gBAAgB,CAAC,MAAM;QAC5B,IAAI,CAAC,gBAAgB,CAAC,MAAM;QAC5B,IAAI,CAAC,YAAY,CAAC,MAAM;QACxB,IAAI,CAAC,OAAO,CAAC,MAAM;QACnB,OAAO,IAAI;IACb;IAEA;;GAEC,GACD,YAAY,CAAC,EAAE;QACb,OAAQ,EAAE,IAAI;YACZ,KAAK;gBACH,IAAI,CAAC,UAAU,CAA2B;gBAC1C;YACF,KAAK;gBACH,IAAI,CAAC,UAAU,CAA2B;gBAC1C;YACF,KAAK;gBACH,IAAI,CAAC,UAAU,CAA2B;gBAC1C;YACF,KAAK;gBACH,IAAI,CAAC,UAAU,CAA2B;gBAC1C;YACF,KAAK;gBACH,IAAI,CAAC,QAAQ;gBACb;YACF,KAAK;gBACH,IAAI,CAAC,QAAQ;gBACb;YACF,KAAK;gBACH,IAAI,CAAC,QAAQ;gBACb;YACF,KAAK;gBACH,IAAI,CAAC,WAAW;gBAChB;YACF,KAAK;gBACH,eAAe;gBACf;QACJ;IACF;AACF;AAEA;;;;CAIC,GACD,MAAM,kBAAkB,CAAC,QAAQ,aAAe,IAAI,UAAU,QAAQ;AAKtE;;;CAGC,GAED;;;CAGC,GAED;;;;;CAKC,GAED;;;CAGC,GAED;;;;CAIC,GAED;;;;CAIC,GAED,MAAM;IACJ,sCAAsC,GACtC,YAAY,aAAa,CAAC,CAAC,CAAE;QAC3B,IAAI,QAAQ,KAAK,EAAE,QAAQ,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QACtD,MAAM,YAAY,WAAW,IAAI;QACjC,+BAA+B,GAC/B,IAAI,OAAO;QACX,IAAI,WAAW;YACb,OAAO,qBAAqB,GAAE,AAAC,UAAW,OAAO,IAC1C,uBAAuB,GAAE,AAAC,UAAW,aAAa,IAClD,aAA8C,UAAW,CAAC,EAAE,IAC5D;QACT;QACA,MAAM,gBAAgB,WAAW,QAAQ;QACzC,MAAM,gBAAgB,QAAQ,QAAQ;QACtC,MAAM,eAAe,WAAW,YAAY;QAC5C,2BAA2B,GAC3B,IAAI,CAAC,QAAQ,GAAG,gBAAgB,aAAa,eAAe,iBAAiB;QAC7E,+BAA+B,GAC/B,IAAI,CAAC,IAAI,GAAG;QACZ,oCAAoC,GACpC,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,4BAA4B,GAC5B,IAAI,CAAC,kBAAkB,GAAG,EAAE;QAC5B,8BAA8B,GAC9B,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,qCAAqC,GACrC,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,oCAAoC,GACpC,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,2CAA2C,GAC3C,IAAI,CAAC,eAAe,GAAG,CAAC;QACxB,gCAAgC,GAChC,IAAI,CAAC,IAAI,GAAG,CAAC;QACb,IAAI,cAAc;YAChB,IAAK,IAAI,MAAM,aAAc;gBAC3B,MAAM,MAAM,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG;gBAC3C,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG;gBAC3B,IAAI,gBAAgB,CAAC,UAAU,IAAI;YACrC;QACF;IACF;IAEA;;;;;;;GAOC,GACD,QAAQ,EAAE,EAAE;QACV,IAAI,cAAc,QAAQ,KAAK;QAC/B,IAAI,aAAa,QAAQ,IAAI;QAC7B,IAAI,iBAAiB,QAAQ,QAAQ;QACrC,QAAQ,KAAK,GAAG,IAAI;QACpB,QAAQ,IAAI,GAAG,IAAI,CAAC,IAAI;QACxB,QAAQ,QAAQ,GAAG,IAAI,CAAC,QAAQ;QAChC,MAAM,MAAM,IAAI,CAAC,eAAe;QAChC,IAAK,IAAI,MAAM,IAAK,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO;QACtD,MAAM,WAAW,GAAG,IAAI;QACxB,QAAQ,KAAK,GAAG;QAChB,QAAQ,IAAI,GAAG;QACf,QAAQ,QAAQ,GAAG;QACnB,OAAO;IACT;IAEA;;GAEC,GACD,UAAU;QACR,IAAI,CAAC,OAAO,CAAC;YACX,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM;YAC/B,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM;YACtC,MAAO,IAAK,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,MAAM;YACtC,MAAO,IAAK,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,IAAI;YAC3C,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;YAC1B,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG;YACjC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAE,CAAA;gBACzB,MAAM,oBAAoB,YAAY,IAAI;gBAC1C,IAAI,mBAAmB;oBACrB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBAC/B;YACF;QACF;QACA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;;;;;GAeC,GACD,IAAI,EAAE,EAAE,EAAE,EAAE;QACV,IAAI,MAAM,KAAK;YACb,MAAM,cAAgD;YACtD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC;gBACX,MAAM,oBAAoB,YAAY,IAAI;gBAC1C,IAAI,mBAAmB;oBACrB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBAC/B;YACF;QACF,OAAO;YACL,IAAI,CAAC,OAAO,CAAuB,GAAI,GAAG,CAAmB,GAAG,OAAS,IAAI,CAAC,OAAO,CAAC,IAAM,MAAM;QACpG;QACA,OAAO,IAAI;IACb;IAEA;;GAEC,GACD,YAAY,CAAC,EAAE;QACb,OAAQ,EAAE,IAAI;YACZ,KAAK;gBACH,IAAI,CAAC,OAAO;gBACZ;QACJ;IACF;IAEA,SAAS;QACP,MAAM,cAAc,IAAI,CAAC,WAAW;QACpC,MAAM,qBAAqB,IAAI,CAAC,kBAAkB;QAClD,MAAM,MAAM,IAAI,CAAC,eAAe;QAChC,IAAI,IAAI,YAAY,MAAM;QAC1B,IAAI,IAAI,mBAAmB,MAAM;QACjC,MAAO,IAAK,WAAW,CAAC,EAAE,CAAC,MAAM;QACjC,MAAO,IAAK,kBAAkB,CAAC,EAAE,CAAC,IAAI;QACtC,IAAK,IAAI,MAAM,IAAK,GAAG,CAAC,GAAG,CAAC,mBAAmB,CAAC,UAAU,IAAI;QAC9D,YAAY,MAAM,GAAG;QACrB,mBAAmB,MAAM,GAAG;QAC5B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;QAC3B,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,IAAI,CAAC,eAAe,GAAG,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,CAAC;IACf;AACF;AAEA;;;CAGC,GACD,MAAM,cAAc,CAAA,SAAU,IAAI,MAAM;AAExC;;CAEC,GAED;;CAEC,GACD,MAAM,mBAAmB;IACvB,MAAM,MAAM,SAAS,aAAa,CAAC;IACnC,IAAI,IAAI,CAAC,WAAW,CAAC;IACrB,IAAI,KAAK,CAAC,MAAM,GAAG;IACnB,MAAM,SAAS,IAAI,YAAY;IAC/B,IAAI,IAAI,CAAC,WAAW,CAAC;IACrB,OAAO;AACT;AAEA;;;;;CAKC,GACD,MAAM,uCAAuC,CAAC,OAAO,WAAa,SAAS,MAAM,SAAS,qBAAqB,GAAE,AAAC,MAAO,YAAY;AAErI,MAAM,mBAAmB,IAAI;AAE7B,MAAM;IACJ;;GAEC,GACD,YAAY,GAAG,CAAE;QACf,wBAAwB,GACxB,IAAI,CAAC,OAAO,GAAG;QACf,oBAAoB,GACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI;QACvC,mBAAmB,GACnB,IAAI,CAAC,QAAQ,GAAG;QAChB,mBAAmB,GACnB,IAAI,CAAC,SAAS,GAAG;QACjB,mBAAmB,GACnB,IAAI,CAAC,KAAK,GAAG;QACb,mBAAmB,GACnB,IAAI,CAAC,MAAM,GAAG;QACd,mBAAmB,GACnB,IAAI,CAAC,IAAI,GAAG;QACZ,mBAAmB,GACnB,IAAI,CAAC,GAAG,GAAG;QACX,mBAAmB,GACnB,IAAI,CAAC,MAAM,GAAG;QACd,mBAAmB,GACnB,IAAI,CAAC,OAAO,GAAG;QACf,mBAAmB,GACnB,IAAI,CAAC,OAAO,GAAG;QACf,mBAAmB,GACnB,IAAI,CAAC,WAAW,GAAG;QACnB,mBAAmB,GACnB,IAAI,CAAC,WAAW,GAAG;QACnB,mBAAmB,GACnB,IAAI,CAAC,WAAW,GAAG;QACnB,mBAAmB,GACnB,IAAI,CAAC,YAAY,GAAG;QACpB,mBAAmB,GACnB,IAAI,CAAC,QAAQ,GAAG;QAChB,oBAAoB,GACpB,IAAI,CAAC,SAAS,GAAG;QACjB,oBAAoB,GACpB,IAAI,CAAC,SAAS,GAAG;QACjB,kBAAkB,GAClB,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM;YAC5B,UAAU;YACV,SAAS,IAAM,IAAI,CAAC,SAAS,CAAC,MAAM;YACpC,UAAU;gBACR,MAAM,YAAY,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS;gBAClD,gBAAgB,IAAI,EAAE,CAAC,2BAA2B,GAAE,QAAU,MAAM,YAAY,IAAI;YACtF;YACA,YAAY,IAAM,IAAI,CAAC,SAAS,CAAC,KAAK;QACxC,GAAG,IAAI;QACP,kBAAkB,GAClB,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM;YACzB,UAAU;YACV,WAAW;YACX,UAAU,CAAA;gBACR,MAAM,KAAK,KAAK,SAAS;gBACzB,MAAM,KAAK,IAAI,CAAC,WAAW;gBAC3B,MAAM,KAAK,IAAI,CAAC,WAAW;gBAC3B,MAAM,KAAK,IAAI,CAAC,OAAO;gBACvB,MAAM,KAAK,IAAI,CAAC,OAAO;gBACvB,MAAM,KAAK,KAAK;gBAChB,MAAM,KAAK,KAAK;gBAChB,IAAI,CAAC,WAAW,GAAG;gBACnB,IAAI,CAAC,WAAW,GAAG;gBACnB,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK;gBAC9B,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK;gBAC9B,IAAI,CAAC,QAAQ,GAAG,MAAM,KAAK,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK,MAAM,KAAK,GAAG;YACxE;QACF,GAAG,IAAI;QACP,kBAAkB,GAClB,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM;YAC5B,UAAU;YACV,UAAU,MAAM,QAAQ,SAAS;YACjC,YAAY;gBACV,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,sBAAsB;gBAC3B,IAAI,CAAC,YAAY;YACnB;QACF,GAAG,IAAI;QACP,kBAAkB,GAClB,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM;YAC1B,UAAU;YACV,UAAU,MAAM,QAAQ,SAAS;YACjC,SAAS;gBACP,IAAI,CAAC,YAAY,CAAC,MAAM;YAC1B;YACA,YAAY;gBACV,IAAI,CAAC,YAAY,CAAC,KAAK;YACzB;QACF,GAAG,IAAI;QACP,2BAA2B,GAC3B,IAAI,CAAC,KAAK,GAAG;QACb,2BAA2B,GAC3B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,eAAe,IAAM,IAAI,CAAC,YAAY,CAAC,OAAO;QACxE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO;QACxC,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,gBAAgB,CAAC,UAAU,IAAI,EAAE;IACtE;IAEA,qBAAqB;QACnB,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,MAAM,MAAM,IAAI,CAAC,OAAO;QACxB,IAAI,CAAC,OAAO,GAAG,MAAM,SAAS,IAAI,OAAO,GAAG,IAAI,UAAU,EAAE;QAC5D,IAAI,CAAC,OAAO,GAAG,MAAM,SAAS,IAAI,OAAO,GAAG,IAAI,SAAS,EAAE;IAC7D;IAEA,qBAAqB;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QAC9B,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA,eAAe;QACb,MAAM,QAAQ,iBAAiB,IAAI,CAAC,OAAO;QAC3C,MAAM,MAAM,IAAI,CAAC,OAAO;QACxB,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,GAAG,WAAW,MAAM,UAAU,IAAI,WAAW,MAAM,WAAW;QAChG,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,GAAG,WAAW,MAAM,SAAS,IAAI,WAAW,MAAM,YAAY;QAClG,IAAI,CAAC,kBAAkB;QACvB,IAAI,OAAO;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,QAAQ,IAAI,CAAC,QAAQ;YACrB,SAAS,IAAI,CAAC,SAAS;QACzB,OAAO;YACL,MAAM,SAAS,IAAI,qBAAqB;YACxC,QAAQ,OAAO,KAAK;YACpB,SAAS,OAAO,MAAM;YACtB,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG;YACrB,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI;QACzB;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,yBAAyB;QACvB,gBAAgB,IAAI,EAAE,CAAC,2BAA2B,GAAE;YAClD,IAAI,MAAM,MAAM,EAAE;gBAChB,MAAM,WAAW;YACnB;QACF;QACA,IAAI,CAAC,YAAY;QACjB,gBAAgB,IAAI,EAAE,CAAC,2BAA2B,GAAE;YAClD,MAAM,OAAO;YACb,IAAI,MAAM,MAAM,EAAE;gBAChB,MAAM,KAAK;YACb;QACF;IACF;IAEA,UAAU;QACR,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,YAAY;IACnB;IAEA,eAAe;QACb,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,UAAU,CAAC,OAAO;IACzB;IAEA;;GAEC,GACD,YAAY,CAAC,EAAE;QACb,OAAQ,EAAE,IAAI;YACZ,KAAK;gBACH,IAAI,CAAC,YAAY;gBACjB;QACJ;IACF;IAEA,SAAS;QACP,IAAI,CAAC,YAAY,CAAC,MAAM;QACxB,IAAI,CAAC,SAAS,CAAC,MAAM;QACrB,IAAI,CAAC,YAAY,CAAC,MAAM;QACxB,IAAI,CAAC,UAAU,CAAC,MAAM;QACtB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO;QAC1C,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC,UAAU,IAAI;QACrE,iBAAiB,MAAM,CAAC,IAAI,CAAC,OAAO;IACtC;AACF;AAEA;;;CAGC,GACD,MAAM,gCAAgC,CAAA;IACpC,MAAM,MAAiC,SAAS,aAAa,OAAO,CAAC,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI;IAC9F,IAAI,kBAAkB,iBAAiB,GAAG,CAAC;IAC3C,IAAI,CAAC,iBAAiB;QACpB,kBAAkB,IAAI,gBAAgB;QACtC,iBAAiB,GAAG,CAAC,KAAK;IAC5B;IACA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,MAAM,mBAAmB,CAAC,KAAK,GAAG,MAAM,OAAO;IAC7C,MAAM,WAAW,MAAM;IACvB,MAAM,WAAW,MAAM;IACvB,MAAM,QAAQ,MAAM,SAAS,MAAM,UAAU,MAAM,WAAW,WAAW,IAC3D,MAAM,YAAY,MAAM,WAAW,MAAM,SAAS,WAAW,SAC7D,MAAM,WAAW,QACjB;IACd,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,kBAAkB,OAAO;IAC1C,IAAI,KAAK;IACT,IAAI,MAAM,KAAK;QACb,KAAK,AAAC,IAAI,MAAO;IACnB,OAAO,IAAI,GAAG;QACZ,KAAK,iBAAiB,KAAK,yBAAyB,MAAM,MAAM,CAAC;IACnE;IACA,IAAI,YAAY,QAAQ,GAAG,MAAM;IACjC,IAAI,YAAY,OAAO,GAAG,MAAM;IAChC,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,MAAM,kBAAkB,CAAC,KAAK,GAAG,MAAM,OAAO;IAC5C,mBAAmB,GACnB,IAAI;IACJ,IAAI,MAAM,IAAI;QACZ,MAAM,kBAAkB,sBAAsB,IAAI,CAAuB;QACzE,IAAI,iBAAiB;YACnB,MAAM,WAAW,eAAe,CAAC,EAAE;YACnC,MAAM,WAAW,QAAQ,CAAC,EAAE;YAC5B,MAAM,WAAW,mBAAmB,GAAE,AAAC,EAAG,KAAK,CAAC;YAChD,MAAM,WAAW,QAAQ,CAAC,EAAE,KAAK;YACjC,MAAM,WAAW,QAAQ,CAAC,EAAE,KAAK;YACjC,MAAM,WAAW,iBAAiB,KAAK,QAAQ,CAAC,EAAE,EAAE,MAAM,OAAO;YACjE,MAAM,WAAW,iBAAiB,KAAK,QAAQ,CAAC,EAAE,EAAE,MAAM,OAAO;YACjE,IAAI,UAAU;gBACZ,MAAM,MAAM,iBAAiB,iBAAiB,KAAK,OAAO,OAAO,UAAU;gBAC3E,QAAQ,MAAM,WAAW,WAAW;YACtC,OAAO,IAAI,UAAU;gBACnB,MAAM,MAAM,iBAAiB,iBAAiB,KAAK,OAAO,OAAO,UAAU;gBAC3E,QAAQ,MAAM,WAAW,WAAW;YACtC,OAAO;gBACL,QAAQ,iBAAiB,UAAU,UAAU;YAC/C;QACF,OAAO;YACL,QAAQ,iBAAiB,KAAK,GAAG,MAAM,OAAO;QAChD;IACF,OAAO;QACL,QAA8B;IAChC;IACA,OAAO,MAAM,OAAO;AACtB;AAEA;;;CAGC,GACD,MAAM,wBAAwB,CAAA;IAC5B,IAAI;IACJ,MAAM,gBAAgB,OAAO,OAAO;IACpC,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAI,GAAG,IAAK;QACpD,MAAM,SAAS,aAAa,CAAC,EAAE;QAC/B,IAAI,MAAM,CAAC,YAAY,EAAE;YACvB,gBAA2C;YAC3C;QACF;IACF;IACA,OAAO;AACT;AAEA,IAAI,gBAAgB;AAEpB,MAAM,cAAc;IAAC;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;IAAU;CAAU;AAEjK;;;;CAIC,GAED;;;;CAIC,GAED;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;CAmBC,GAED,MAAM;IACJ;;GAEC,GACD,YAAY,aAAa,CAAC,CAAC,CAAE;QAC3B,IAAI,QAAQ,KAAK,EAAE,QAAQ,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QACtD,MAAM,WAAW,SAAS,WAAW,IAAI,EAAE;QAC3C,MAAM,OAAO,WAAW,aAAwC,YAAa;QAC7E,MAAM,WAAW,YAAY,CAAC,aAAa,YAAY,aAAa,IAAI;QACxE,MAAM,SAAS,YAAY,CAAC,CAAC,SAAS,QAAQ,CAAC,QAAQ;QACvD,MAAM,WAAW,YAAY,CAAC,MAAM,aAAa,aAAa,QAAQ,QAAQ;QAC9E,MAAM,YAAY,YAAa,MAAM,aAAa,CAAC,UAAU,CAAC;QAC9D,MAAM,cAAc,YAAY,mBAAmB,GAAE,AAAC,SAAU,KAAK,CAAC,KAAK,GAAG,CAC5E,CAAC,mBAAmB,GAAE,IAAM;gBAC1B,MAAM,SAAS,IAAI,CAAC,MAAM;gBAC1B,OAAO,UAAU,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,KAAK;YAC7C,KACE;QACJ,MAAM,YAAY,aAAa,YAAY,MAAM,GAAG;QACpD,mBAAmB,GACnB,IAAI,CAAC,KAAK,GAAG;QACb,0BAA0B,GAC1B,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,WAAW,EAAE,IAAI,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK;QAC5D,4BAA4B,GAC5B,IAAI,CAAC,SAAS,GAAG,8BAA8B,WAAW,SAAS;QACnE,wBAAwB,GACxB,IAAI,CAAC,MAAM,GAAG;QACd,oCAAoC,GACpC,IAAI,CAAC,MAAM,GAAG;QACd,oBAAoB,GACpB,IAAI,CAAC,MAAM,GAAG;QACd,oBAAoB,GACpB,IAAI,CAAC,UAAU,GAAG;QAClB,8EAA8E,GAC9E,IAAI,CAAC,KAAK,GAAG;QACb,8EAA8E,GAC9E,IAAI,CAAC,KAAK,GAAG;QACb,oBAAoB,GACpB,IAAI,CAAC,IAAI,GAAG,UAAU,YAAY,CAAC,CAAC;QACpC,2BAA2B,GAC3B,IAAI,CAAC,QAAQ,GAAG,SAAS,OAAO;QAChC,mBAAmB,GACnB,IAAI,CAAC,UAAU,GAAG,WAAW,aAAa,QAAQ,WAAW,IAA0B,WAAY;QACnG,qCAAqC,GACrC,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC,aAAa,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG;QAClF,qCAAqC,GACrC,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC,aAAa,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG;QAClF,qCAAqC,GACrC,IAAI,CAAC,kBAAkB,GAAG,eAAe,aAAa,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG;QACxF,qCAAqC,GACrC,IAAI,CAAC,kBAAkB,GAAG,eAAe,aAAa,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG;QACxF,qCAAqC,GACrC,IAAI,CAAC,mBAAmB,GAAG,eAAe,aAAa,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG;QACzF,qCAAqC,GACrC,IAAI,CAAC,mBAAmB,GAAG,eAAe,aAAa,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG;QACzF,qCAAqC,GACrC,IAAI,CAAC,OAAO,GAAG,WAAW,OAAO,IAAI;QACrC,qCAAqC,GACrC,IAAI,CAAC,OAAO,GAAG,WAAW,OAAO,IAAI;QACrC,qCAAqC,GACrC,IAAI,CAAC,cAAc,GAAG,WAAW,cAAc,IAAI;QACnD,qCAAqC,GACrC,IAAI,CAAC,cAAc,GAAG,WAAW,cAAc,IAAI;QACnD,qCAAqC,GACrC,IAAI,CAAC,eAAe,GAAG,WAAW,eAAe,IAAI;QACrD,qCAAqC,GACrC,IAAI,CAAC,eAAe,GAAG,WAAW,eAAe,IAAI;QACrD,qCAAqC,GACrC,IAAI,CAAC,QAAQ,GAAG,WAAW,QAAQ,IAAI;QACvC,qCAAqC,GACrC,IAAI,CAAC,cAAc,GAAG,WAAW,cAAc,IAAI;QACnD,oBAAoB,GACpB,IAAI,CAAC,QAAQ,GAAG;QAChB,oBAAoB,GACpB,IAAI,CAAC,SAAS,GAAG;QACjB,oBAAoB,GACpB,IAAI,CAAC,KAAK,GAAG;QACb,oBAAoB,GACpB,IAAI,CAAC,QAAQ,GAAG;QAChB,oBAAoB,GACpB,IAAI,CAAC,UAAU,GAAG;QAClB,oBAAoB,GACpB,IAAI,CAAC,UAAU,GAAG;QAClB,2BAA2B,GAC3B,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,mBAAmB,GACnB,IAAI,CAAC,MAAM,GAAG;QACd,mBAAmB,GACnB,IAAI,CAAC,WAAW,GAAG;QACnB,mBAAmB,GACnB,IAAI,CAAC,SAAS,GAAG;QACjB,mBAAmB,GACnB,IAAI,CAAC,QAAQ,GAAG;QAChB,mBAAmB,GACnB,IAAI,CAAC,YAAY,GAAG;QACpB,kBAAkB,GAClB,IAAI,CAAC,UAAU,GAAG;YAAC;YAAS;YAAO;YAAO;SAAQ;QAClD,6CAA6C,GAC7C,IAAI,CAAC,MAAM,GAAG;YAAC;YAAG;YAAG;YAAG;SAAE;QAC1B,wBAAwB,GACxB,IAAI,CAAC,WAAW,GAAG;QACnB,wBAAwB,GACxB,IAAI,CAAC,MAAM,GAAG;QACd,iCAAiC,GACjC,IAAI,CAAC,OAAO,GAAG;QACf,oBAAoB,GACpB,IAAI,CAAC,MAAM,GAAG,SAAS,WAAW,KAAK,EAAE;QACzC,2BAA2B,GAC3B,IAAI,CAAC,KAAK,GAAG;QACb,2BAA2B,GAC3B,IAAI,CAAC,KAAK,GAAG;QACb,SAAS,IAAI,CAAC,SAAS,EAAE,IAAI;QAC7B,qFAAqF;QACrF,KAAK;YACH,IAAI,IAAI,CAAC,QAAQ,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,SAAoC,aAAa,WAAW,MAAM,CAAC,CAAC,EAAE;gBAC5E,IAAI,CAAC,MAAM,GAAG,UAAU,IAAI,IAAI;gBAChC,IAAI,CAAC,OAAO;YACd;YACA,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK;QAC7B;IACF;IAEA;;GAEC,GACD,KAAK,MAAM,EAAE;QACX,IAAI,QAAQ;YACV,gEAAgE;YAChE,OAAO,KAAK;YACZ,IAAI,CAAC,MAAM,GAAG;YACd,6EAA6E;YAC7E,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACxB,wBAAwB,GACxB,IAAI;gBACJ,IAAI,CAAC,MAAM,wBAAwB,GAAE,AAAC,OAAQ,OAAO,GAAG;oBACtD,gBAAgB,sBAAiD;gBACnE,OAAO;oBACL,gBAAwC,QAAS,CAAC,wBAAwB,GAAE;wBAC1E,IAAI,MAAM,OAAO,IAAI,CAAC,eAAe;4BACnC,gBAAgB,sBAAiD;wBACnE;oBACF;gBACF;gBACA,sCAAsC;gBACtC,IAAI,CAAC,MAAM,GAAG,iBAAiB,IAAI,IAAI;gBACvC,IAAI,CAAC,OAAO;YACd;QACF;QACA,OAAO,IAAI;IACb;IAEA,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;IAChC;IAEA,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS;IAC9E;IAEA,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO;IAC1E;IAEA,IAAI,WAAW;QACb,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ;QAC1D,OAAO,MAAM,YAAY,MAAM,KAAK,IAAI,MAAM,MAAM,GAAG,GAAG,IAAI;IAChE;IAEA,UAAU;QACR,IAAI,CAAC,QAAQ,GAAG;QAChB,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,IAAI,CAAC,MAAM,GAAG,SAAS,qCAAqC,OAAO,MAAM,EAAE,IAAI,GAAG;QAClF,IAAI,CAAC,UAAU,GAAG,SAAS,qCAAqC,OAAO,IAAI,EAAE,IAAI,GAAG,SAAS;QAC7F,IAAI,CAAC,KAAK,GAAG,SAAS,qCAAqC,OAAO,KAAK,EAAE,IAAI,GAAG;QAChF,IAAI,CAAC,KAAK,GAAG,SAAS,qCAAqC,OAAO,KAAK,EAAE,IAAI,GAAG;QAChF,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,YAAY;QACjB,OAAO,IAAI;IACb;IAEA,cAAc;QACZ,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM;YAC9C,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,MAAM;YACvB,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,OAAO,IAAI;IACb;IAEA,QAAQ;QACN,IAAI,CAAC,WAAW;QAChB,MAAM,YAAY,IAAI,CAAC,SAAS;QAChC,MAAM,SAAS,IAAI,CAAC,UAAU;QAC9B,MAAM,iBAAiB,UAAU,OAAO,CAAC,aAAa,CAAC;QACvD,MAAM,SAAS,IAAI,aAAa,CAAC;QACjC,MAAM,cAAc,IAAI,aAAa,CAAC;QACtC,MAAM,YAAY,IAAI,aAAa,CAAC;QACpC,MAAM,QAAQ,WAAW,CAAC,IAAI,CAAC,KAAK,GAAG,YAAY,MAAM,CAAC;QAC1D,MAAM,SAAS,UAAU,MAAM;QAC/B,MAAM,iBAAiB,SAAS,UAAU,QAAQ,GAAG,UAAU,KAAK;QACpE,MAAM,kBAAkB,SAAS,UAAU,SAAS,GAAG,UAAU,MAAM;QACvE,MAAM,cAAc,UAAU,WAAW;QACzC,MAAM,eAAe,UAAU,YAAY;QAC3C,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,MAAM,MAAM;QAChD,MAAM,UAAU,SAAS,IAAI;QAC7B,MAAM,SAAS,SAAS,KAAK;QAC7B,MAAM,OAAO,SAAS,KAAK,OAAO;QAClC,MAAM,cAAc,SAAS,OAAO;QACpC,MAAM,aAAa,SAAS,KAAK;QACjC,MAAM,YAAY,SAAS,aAAa;QACxC,MAAM,SAAS,SAAS,aAAa;QACrC;;;KAGC,GACD,MAAM,iBAAiB,CAAA,IAAK,SAAS,SAAQ,IAAG,OAAO,AAAC,IAAG,OAAK;QAChE;;;KAGC,GACD,MAAM,UAAU,CAAC,IAAM,CAAC,gBAAgB,EAAE,SAAS,KAAK,EAAE,KAAK,EAAE,EAAE,sBAAsB,CAAC;QAC1F;;;;;;;KAOC,GACD,MAAM,UAAU,CAAC,GAAG,GAAG,GAAG,GAAG,IAAM,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,EAAE,GAAG,CAAC;QACrG,OAAO,KAAK,CAAC,OAAO,GAAG,GAAG,QAAQ,YAAY,SAAS,QAAQ,SAAS,cAAc,MAAM,SAAS,OAAO,cAAc;;eAE/G,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;;sBAEnB,EAAE,SAAS,WAAW,MAAM;;IAE9C,CAAC;QACD,YAAY,KAAK,CAAC,OAAO,GAAG,GAAG,QAAQ,UAAU,GAAG,GAAG,SAAS,iBAAiB,MAAM,SAAS,OAAO,kBAAkB;QACzH,IAAI,CAAC,gBAAgB;YACnB,YAAY,KAAK,CAAC,OAAO,IAAI,CAAC;QAC5B,EAAE,QAAQ,WAAW,eAAe,OAAK,IAAI,GAAG,EAAE,SAAS,gBAAgB,cAAc,CAAC,EAAE,OAAO;QACnG,EAAE,QAAQ,WAAW,eAAe,OAAK,IAAI,GAAG,EAAE,SAAS,cAAc,YAAY,CAAC,EAAE,OAAO;MACjG,CAAC;QACH;QACA,UAAU,KAAK,CAAC,OAAO,GAAG,GAAG,QAAQ,YAAY,GAAG,GAAG,SAAS,cAAc,MAAM,SAAS,OAAO,eAAe;QACnH,IAAI,CAAC,gBAAgB;YACnB,UAAU,KAAK,CAAC,OAAO,IAAI,CAAC;QAC1B,EAAE,QAAQ,WAAW,eAAe,GAAG,GAAG,EAAE,SAAS,eAAe,aAAa,CAAC,EAAE,OAAO;QAC3F,EAAE,QAAQ,WAAW,eAAe,GAAG,GAAG,EAAE,SAAS,aAAa,WAAW,CAAC,EAAE,OAAO;MACzF,CAAC;QACH;QACA,MAAM,SAAS;YAAC;YAAY;SAAW;QACvC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG;YACtB,MAAM,SAAS,IAAI;YACnB,MAAM,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,IAAI;YAC3C,MAAM,SAAS,IAAI;YACnB,MAAM,UAAU,QAAQ;YACxB,MAAM,SAAS,QAAQ,CAAC,SAAS,SAAS,iBAAiB,kBAAkB,SAAS,cAAc,YAAY,IAAI;YACpH,MAAM,SAAS,CAAC,SAAS,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,KAAK;YACtE,MAAM,SAAS,IAAI,aAAa,CAAC;YACjC,MAAM,QAAQ,IAAI,aAAa,CAAC;YAChC,MAAM,UAAU,SAAS,SAAS,UAAU,SAAS,SAAS,WAAW;YACzE,MAAM,aAAa,SAAS,CAAC,SAAS,aAAa,WAAW,IAAI,CAAC,CAAC,SAAS,SAAS,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,SAAS,IAAI,IAAI,SAAS,IAAI;YACzK,+HAA+H;YAC/H,MAAM,SAAS,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE;YACpE,OAAO,KAAK,CAAC,OAAO,GAAG,GAAG,QAAQ,YAAY,GAAG,GAAG,YAAY,aAAa;;wBAE3D,EAAE,SAAS,WAAW,MAAM;8BACtB,EAAE,SAAS,UAAU,MAAM;0BAC/B,EAAE,SAAS,QAAQ,QAAQ;eACtC,EAAE,QAAQ,MAAM,EAAE,SAAS,UAAU,QAAQ,CAAC,EAAE,MAAM;MAC/D,CAAC;YACD,MAAM,KAAK,CAAC,OAAO,GAAG,CAAC;;mBAEV,EAAE,AAAC,OAAO,IAAK,GAAG;gBACrB,EAAE,YAAY;eACf,EAAE,SAAS,SAAS,UAAU,SAAS,SAAS,WAAW,MAAM;;;;;;;oBAO5D,EAAE,UAAU,UAAU,CAAC,UAAU,CAAC,SAAS,UAAU,OAAO;;;eAGjE,EAAE,SAAS,QAAQ,kBAAkB;0BAC1B,EAAE,SAAS,oBAAoB,MAAM;0BACrC,EAAE,SAAS,QAAQ,cAAc;eAC5C,EAAE,SAAS,SAAS,aAAa,cAAc,SAAS,aAAa,cAAc;eACnF,EAAE,SAAS,SAAS,gBAAgB,iBAAiB,SAAS,cAAc,eAAe;MACpG,CAAC;YACD,OAAO,WAAW,CAAC;YACnB,IAAI,WAAW,QAAQ,aAAa,CAAC,SAAS,IAAI,CAAC;YACnD,OAAO,KAAK,CAAC,SAAS,SAAS,MAAM,GAAG,GAAG,SAAS,EAAE,CAAC;YACvD,6HAA6H;YAC7H,CAAC,SAAS,cAAc,SAAS,EAAE,WAAW,CAAC;QACjD;QAEA,OAAO,WAAW,CAAC;QACnB,OAAO,WAAW,CAAC;QACnB,UAAU,OAAO,CAAC,WAAW,CAAC;QAE9B,IAAI,CAAC,gBAAgB,OAAO,SAAS,CAAC,GAAG,CAAC;QAC1C,IAAI,CAAC,MAAM,GAAG;QACd,MAAM,oBAAoB,eAAe,UAAU,OAAO,EAAE;QAC5D,IAAI,sBAAsB,UAAU;YAClC,IAAI,CAAC,WAAW,GAAG,gBAAgB,UAAU,OAAO,EAAE;gBAAE,UAAU;YAAW;QAC/E;IAEF;IAEA,eAAe;QACb,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,WAAW;QAClB;QACA,IAAI;QACJ,MAAM,UAAU,IAAI,CAAC,MAAM;QAC3B,MAAM,YAAY,IAAI,CAAC,SAAS;QAChC,MAAM,SAAS,IAAI,CAAC,UAAU;QAC9B,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,IAAI;QACJ,IAAI,MAAM;QACV,IAAI,UAAU;QACd,IAAI,UAAU;QACd,oBAAoB,GACpB,IAAI,gBAAgB;QACpB,IAAI,QAAQ;YACV,aAAa,OAAO,WAAW;YAC/B,OAAO,IAAI,CAAC,GAAG;QACjB;QACA,MAAM,oBAAoB,eAAe,UAAU,OAAO,EAAE,gBAAgB,WAAW,gBAAgB,UAAU,OAAO,EAAE;YAAE,UAAU;QAAW,KAAK;QACtJ,MAAO,OAAO,QAAQ,UAAU,OAAO,IAAI,QAAQ,IAAI,IAAI,CAAE;YAC3D,MAAM,WAAW,eAAe,KAAK,gBAAgB,WACpC,gBAAgB,KAAK;gBAAE,UAAU;YAAS,KAC1C;YACjB,IAAI,QAAQ,eAAe;gBACzB,WAAW,IAAI,UAAU,IAAI;gBAC7B,WAAW,IAAI,SAAS,IAAI;gBAC5B,gBAAgB,IAAI,YAAY;YAClC;YACA,MAAiC,IAAI,aAAa;YAClD,IAAI,UAAU;gBACZ,IAAI,CAAC,SAAS,UAAU,EAAE;gBAC1B,QAAQ,IAAI,CAAC;YACf;QACF;QACA,IAAI,mBAAmB,kBAAkB,MAAM;QAC/C,MAAM,SAAS,SAAS,UAAU;QAClC,MAAM,aAAa,SAAS,QAAQ,WAAW,GAAG,QAAQ,YAAY;QACtE,MAAM,gBAAgB,SAAS,UAAU,KAAK,GAAG,UAAU,MAAM;QACjE,MAAM,aAAa,SAAS,UAAU,WAAW,GAAG,UAAU,YAAY;QAC1E,MAAM,YAAY,aAAa;QAC/B,MAAM,QAAQ,IAAI,CAAC,KAAK;QACxB,MAAM,QAAQ,IAAI,CAAC,KAAK;QAExB,iCAAiC,GACjC,IAAI,cAAc;QAClB,iCAAiC,GACjC,IAAI,cAAc;QAClB,iCAAiC,GACjC,IAAI,iBAAiB;QACrB,iCAAiC,GACjC,IAAI,iBAAiB;QAErB,IAAI,MAAM,QAAQ;YAChB,MAAM,WAAW,mBAAmB,GAAE,AAAC,MAAO,KAAK,CAAC;YACpD,iBAAiB,QAAQ,CAAC,EAAE;YAC5B,cAAc,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG;QACpD,OAAO,IAAI,MAAM,QAAQ;YACvB,MAAM,IAAwC;YAC9C,IAAI,CAAC,MAAM,EAAE,SAAS,GAAG,iBAAiB,EAAE,SAAS;YACrD,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,cAAc,EAAE,MAAM;QAC9C,OAAO,IAAI,MAAM,QAAQ;YACvB,iBAAuC;QACzC;QAEA,IAAI,MAAM,QAAQ;YAChB,MAAM,WAAW,mBAAmB,GAAE,AAAC,MAAO,KAAK,CAAC;YACpD,iBAAiB,QAAQ,CAAC,EAAE;YAC5B,cAAc,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG;QACpD,OAAO,IAAI,MAAM,QAAQ;YACvB,MAAM,IAAwC;YAC9C,IAAI,CAAC,MAAM,EAAE,SAAS,GAAG,iBAAiB,EAAE,SAAS;YACrD,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,cAAc,EAAE,MAAM;QAC9C,OAAO,IAAI,MAAM,QAAQ;YACvB,iBAAuC;QACzC;QAEA,MAAM,oBAAoB,gBAAgB,SAAS,aAAa;QAChE,MAAM,oBAAoB,gBAAgB,SAAS,aAAa;QAChE,MAAM,QAAQ,AAAC,oBAAoB,SAAU;QAC7C,MAAM,OAAO,AAAC,oBAAoB,SAAU;QAC5C,MAAM,uBAAuB,gBAAgB,SAAS,gBAAgB,eAAe,OAAO;QAC5F,MAAM,uBAAuB,gBAAgB,SAAS,gBAAgB,eAAe,OAAO;QAC5F,MAAM,cAAc,oBAAoB,SAAS;QACjD,MAAM,YAAY,oBAAoB,SAAS;QAC/C,MAAM,cAAc,YAAY;QAChC,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,QAAQ,GAAG,eAAe,IAAI,IAAI;QACvC,IAAI,CAAC,UAAU,GAAG;YAAC;YAAa;YAAa;YAAgB;SAAe;QAC5E,IAAI,CAAC,MAAM,GAAG;YAAC;YAAmB;YAAmB;YAAsB;SAAqB;QAChG,IAAI,SAAS;YACX,QAAQ,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM;QACzC;QACA,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,YAAY;QAC1B;QACA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,KAAK;QACZ;IACF;IAEA,eAAe;QACb,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,MAAM,OAAO,IAAI,CAAC,IAAI;QACtB,MAAM,WAAW,IAAI,CAAC,QAAQ;QAC9B,MAAM,aAAa,IAAI,CAAC,UAAU;QAClC,MAAM,aAAa,UAAU,CAAC,YAAY,UAAU;QACpD,MAAM,SAAS,IAAI,CAAC,UAAU;QAC9B,MAAM,YAAY,IAAI,CAAC,SAAS;QAChC,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,MAAM,WAAW,UAAU,IAAI,CAAC,WAAW;QAC3C,MAAM,UAAU,UAAU,IAAI,CAAC,SAAS;QACxC,MAAM,WAAW,CAAC,YAAY,CAAC;QAC/B,MAAM,cAAc,WAAW,IAAI,CAAC,WAAW,IAAI,WAAW,IAAI,CAAC,SAAS;QAC5E,MAAM,aAAa,CAAC,IAAI,CAAC,UAAU,IAAI;QACvC,MAAM,SAAS,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;QACzC,IAAI,aAAa;QACjB,IAAI,gBAAgB;QACpB,IAAI,IAAI,IAAI,CAAC,QAAQ;QAErB,IAAI,YAAY,IAAI,CAAC,KAAK,EAAE;YAC1B,IAAI,CAAC,KAAK,GAAG;QACf;QAEA,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE;YACxB,IAAI,CAAC,KAAK,GAAG;QACf;QAEA,IAAI,YAAY;YACd,MAAM,KAAK,OAAO,QAAQ;YAC1B,IAAI,cAAc,MAAM,aAAa;gBACnC,IAAI,mBAAmB,GAAE,AAAC,aAAc,GAAG;oBACzC,MAAM,OAAO;oBACb,MAAM,OAAO,KAAK,KAAK,MAAM,IAAI,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,OAAO;oBAC/D,IAAI,MAAM,KAAK,IAAI,GAAG,YAAY,KAAK,IAA0B,aAAc,SAAS,MAAM;gBAChG;YACF,OAAO,IAAI,UAAU;gBACnB,IAAI,SAAS;YACf;YACA,aAAa,MAAM,IAAI,CAAC,YAAY;YACpC,gBAAgB,OAAO;YACvB,IAAI,cAAc,CAAC,iBAAkB,cAAc,IAAK;gBACtD,UAAU,UAAU,CAAC,OAAO;YAC9B;QACF;QAEA,IAAI,QAAQ;YACV,MAAM,SAAS,SAAS,UAAU,OAAO,GAAG,UAAU,OAAO;YAC7D,OAAO,KAAK,CAAC,SAAS,QAAQ,OAAO,GAAG,SAAS,KAAK;QACxD;QAEA,uEAAuE;QACvE,IAAI,AAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,IAAM,cAAc,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,EAAG;YACxF,IAAI,UAAU,IAAI,CAAC,QAAQ,GAAG;YAC9B,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACxC,IAAI,UAAU,UAAU,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI;gBAC1E,IAAI,CAAC,WAAW,CAAC,IAAI;gBACrB,IAAI,CAAC,OAAO,CAAC,IAAI;gBACjB,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjB,IAAI,CAAC,mBAAmB,CAAC,IAAI;oBAC7B,IAAI,CAAC,eAAe,CAAC,IAAI;gBAC3B,OAAO;oBACL,IAAI,CAAC,kBAAkB,CAAC,IAAI;oBAC5B,IAAI,CAAC,cAAc,CAAC,IAAI;gBAC1B;gBACA,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,YAAY,IAAI,CAAC,UAAU,GAAG;YACpC,OAAO,IAAI,UAAU;gBACnB,IAAI,CAAC,UAAU,GAAG;YACpB;QACF;QAEA,IAAI,YAAY,CAAC,YAAY,IAAI,CAAC,QAAQ,EAAE;YAC1C,aAAa;QACf;QAEA,IAAI,YAAY;YACd,IAAI,YAAY,OAAO,IAAI,CAAC,OAAO,QAAQ,GAAG;YAC9C,IAAI,CAAC,QAAQ,CAAC,IAAI;QACpB;QAEA,IAAI,CAAC,YAAY,IAAI,CAAC,QAAQ,EAAE;YAC9B,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,WAAW,CAAC,IAAI;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI;YACjB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,mBAAmB,CAAC,IAAI;gBAC7B,IAAI,CAAC,eAAe,CAAC,IAAI;YAC3B,OAAO;gBACL,IAAI,CAAC,kBAAkB,CAAC,IAAI;gBAC5B,IAAI,CAAC,cAAc,CAAC,IAAI;YAC1B;YACA,IAAI,QAAQ,CAAC,YAAY;gBACvB,gBAAgB;YAClB;QACF;QAEA,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,iBAAiB,CAAC,IAAI,GAAG;YAC/E,IAAI,MAAM;gBACR,IAAI,CAAC,cAAc,CAAC,IAAI;YAC1B;YACA,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,AAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAY,CAAC,IAAI,CAAC,MAAM,IAAI,UAAU,OAAO,SAAS,EAAG;gBAC7E,IAAI,CAAC,MAAM;YACb;QACF;QAEA,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE;YAC3B,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,IAAI,CAAC,YAAY,GAAG;IACtB;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,QAAQ,EAAE;QACnB,MAAM,YAAY,IAAI,CAAC,SAAS;QAChC,YAAY,WAAW,IAAI;QAC3B,IAAI,CAAC,UAAU,KAAK,EAAE;YACpB,UAAU,MAAM;QAClB;QACA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,WAAW;QAClB;QACA,IAAI,CAAC,QAAQ,GAAG;QAChB,OAAO,IAAI;IACb;AAEF;AAEA;;;CAGC,GACD,MAAM,WAAW,CAAC,aAAa,CAAC,CAAC,GAAK,IAAI,eAAe;AAKzD;;;;;;;;;CASC,GAED;;;;;;;CAOC,GAED;;;;CAIC,GACD,MAAM,UAAU,CAAC,KAAK,SAAS,CAAC,CAAC;IAC/B,IAAI,SAAS,EAAE;IACf,IAAI,WAAW;IACf,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,WAAW,OAAO,QAAQ;IAChC,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,YAAY,CAAC,MAAM;IACzB,MAAM,YAAY,aAAa,CAAC,MAAM,mBAAmB,GAAE,AAAC,KAAM,IAAI;IACtE,MAAM,cAAc,YAAY,mBAAmB,GAAE,AAAC,KAAM,IAAI,GAAG,YAAY,aAAa,QAAQ;IACpG,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,YAAY,MAAM,SAAS,SAAS,KAAK,SAAS;IACxD,MAAM,aAAa,SAAS;IAC5B,MAAM,WAAW,SAAS;IAC1B,MAAM,UAAU,MAAM;IACtB,MAAM,OAAO,UAAU,YAAY,GAAG,CAAC,EAAE,IAAI,YAAY;IACzD,MAAM,OAAO,UAAU,YAAY,GAAG,CAAC,EAAE,IAAI;IAC7C,MAAM,YAAY,aAAa,IAAI,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI;IAC/D,MAAM,QAAQ,OAAO,KAAK,IAAI,IAAI,CAAC,UAAU,OAAO,CAAC;IACrD,IAAI,YAAY,YAAY,IAAI,MAAM,QAAQ,OAAO;IACrD,OAAO,CAAC,GAAG,GAAG,GAAG;QACf,IAAI,YAAY,YAAY,CAAC,IAAI,CAAC,IAAI;QACtC,IAAI,UAAU,YAAY,IAAI;QAC9B,IAAI,CAAC,OAAO,MAAM,EAAE;YAClB,IAAK,IAAI,QAAQ,GAAG,QAAQ,GAAG,QAAS;gBACtC,IAAI,CAAC,MAAM;oBACT,OAAO,IAAI,CAAC,IAAI,YAAY;gBAC9B,OAAO;oBACL,MAAM,QAAQ,CAAC,aAAa,YAAY,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI;oBAClE,MAAM,QAAQ,CAAC,aAAa,MAAM,YAAY,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI;oBACzE,MAAM,MAAM,QAAQ,IAAI,CAAC,EAAE;oBAC3B,MAAM,MAAM,MAAM,QAAQ,IAAI,CAAC,EAAE;oBACjC,MAAM,YAAY,QAAQ;oBAC1B,MAAM,YAAY,QAAQ;oBAC1B,IAAI,QAAQ,KAAK,YAAY,YAAY,YAAY;oBACrD,IAAI,SAAS,KAAK,QAAQ,CAAC;oBAC3B,IAAI,SAAS,KAAK,QAAQ,CAAC;oBAC3B,OAAO,IAAI,CAAC;gBACd;gBACA,WAAW,OAAO;YACpB;YACA,IAAI,aAAa,SAAS,OAAO,GAAG,CAAC,CAAA,MAAO,YAAY,MAAM,YAAY;YAC1E,IAAI,UAAU,SAAS,OAAO,GAAG,CAAC,CAAA,MAAO,OAAO,AAAC,MAAM,IAAK,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,WAAW;QAC/F;QACA,MAAM,UAAU,UAAU,CAAC,OAAO,IAAI,IAAI,WAAW;QACrD,MAAM,SAAS,KAAK,sBAAsB,IAAI,MAAM,OAAO,KAAK,IAAI,GAAG,iBAAiB,GAAG,SAA+B;QAC1H,0BAA0B,GAC1B,IAAI,SAAS,SAAS,CAAC,AAAC,UAAU,MAAM,MAAM,CAAC,EAAE,EAAE,MAAO,CAAC;QAC3D,IAAI,OAAO,QAAQ,EAAE,SAAS,OAAO,QAAQ,CAAC;QAC9C,IAAI,WAAW,SAAS,GAAG,SAAS,SAAS,CAAC,EAAE,EAAE;QAClD,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6500, "column": 0}, "map": {"version": 3, "file": "heart.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/lucide-react/src/icons/heart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z',\n      key: 'c3ymky',\n    },\n  ],\n];\n\n/**\n * @component @name Heart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTRjMS40OS0xLjQ2IDMtMy4yMSAzLTUuNUE1LjUgNS41IDAgMCAwIDE2LjUgM2MtMS43NiAwLTMgLjUtNC41IDItMS41LTEuNS0yLjc0LTItNC41LTJBNS41IDUuNSAwIDAgMCAyIDguNWMwIDIuMyAxLjUgNC4wNSAzIDUuNWw3IDdaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/heart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Heart = createLucideIcon('heart', __iconNode);\n\nexport default Heart;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6539, "column": 0}, "map": {"version": 3, "file": "star.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/lucide-react/src/icons/star.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z',\n      key: 'r04s7s',\n    },\n  ],\n];\n\n/**\n * @component @name Star\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNTI1IDIuMjk1YS41My41MyAwIDAgMSAuOTUgMGwyLjMxIDQuNjc5YTIuMTIzIDIuMTIzIDAgMCAwIDEuNTk1IDEuMTZsNS4xNjYuNzU2YS41My41MyAwIDAgMSAuMjk0LjkwNGwtMy43MzYgMy42MzhhMi4xMjMgMi4xMjMgMCAwIDAtLjYxMSAxLjg3OGwuODgyIDUuMTRhLjUzLjUzIDAgMCAxLS43NzEuNTZsLTQuNjE4LTIuNDI4YTIuMTIyIDIuMTIyIDAgMCAwLTEuOTczIDBMNi4zOTYgMjEuMDFhLjUzLjUzIDAgMCAxLS43Ny0uNTZsLjg4MS01LjEzOWEyLjEyMiAyLjEyMiAwIDAgMC0uNjExLTEuODc5TDIuMTYgOS43OTVhLjUzLjUzIDAgMCAxIC4yOTQtLjkwNmw1LjE2NS0uNzU1YTIuMTIyIDIuMTIyIDAgMCAwIDEuNTk3LTEuMTZ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/star\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Star = createLucideIcon('star', __iconNode);\n\nexport default Star;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}