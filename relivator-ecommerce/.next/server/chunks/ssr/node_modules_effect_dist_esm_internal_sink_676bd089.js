module.exports = {

"[project]/node_modules/effect/dist/esm/internal/sink.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SinkImpl": (()=>SinkImpl),
    "SinkTypeId": (()=>SinkTypeId),
    "as": (()=>as),
    "channelToSink": (()=>channelToSink),
    "collectAll": (()=>collectAll),
    "collectAllFrom": (()=>collectAllFrom),
    "collectAllN": (()=>collectAllN),
    "collectAllToMap": (()=>collectAllToMap),
    "collectAllToMapN": (()=>collectAllToMapN),
    "collectAllToSet": (()=>collectAllToSet),
    "collectAllToSetN": (()=>collectAllToSetN),
    "collectAllUntil": (()=>collectAllUntil),
    "collectAllUntilEffect": (()=>collectAllUntilEffect),
    "collectAllWhile": (()=>collectAllWhile),
    "collectAllWhileEffect": (()=>collectAllWhileEffect),
    "collectAllWhileWith": (()=>collectAllWhileWith),
    "collectLeftover": (()=>collectLeftover),
    "context": (()=>context),
    "contextWith": (()=>contextWith),
    "contextWithEffect": (()=>contextWithEffect),
    "contextWithSink": (()=>contextWithSink),
    "count": (()=>count),
    "die": (()=>die),
    "dieMessage": (()=>dieMessage),
    "dieSync": (()=>dieSync),
    "dimap": (()=>dimap),
    "dimapChunks": (()=>dimapChunks),
    "dimapChunksEffect": (()=>dimapChunksEffect),
    "dimapEffect": (()=>dimapEffect),
    "drain": (()=>drain),
    "drop": (()=>drop),
    "dropUntil": (()=>dropUntil),
    "dropUntilEffect": (()=>dropUntilEffect),
    "dropWhile": (()=>dropWhile),
    "dropWhileEffect": (()=>dropWhileEffect),
    "ensuring": (()=>ensuring),
    "ensuringWith": (()=>ensuringWith),
    "every": (()=>every),
    "fail": (()=>fail),
    "failCause": (()=>failCause),
    "failCauseSync": (()=>failCauseSync),
    "failSync": (()=>failSync),
    "filterInput": (()=>filterInput),
    "filterInputEffect": (()=>filterInputEffect),
    "findEffect": (()=>findEffect),
    "flatMap": (()=>flatMap),
    "fold": (()=>fold),
    "foldChunks": (()=>foldChunks),
    "foldChunksEffect": (()=>foldChunksEffect),
    "foldEffect": (()=>foldEffect),
    "foldLeft": (()=>foldLeft),
    "foldLeftChunks": (()=>foldLeftChunks),
    "foldLeftChunksEffect": (()=>foldLeftChunksEffect),
    "foldLeftEffect": (()=>foldLeftEffect),
    "foldSink": (()=>foldSink),
    "foldUntil": (()=>foldUntil),
    "foldUntilEffect": (()=>foldUntilEffect),
    "foldWeighted": (()=>foldWeighted),
    "foldWeightedDecompose": (()=>foldWeightedDecompose),
    "foldWeightedDecomposeEffect": (()=>foldWeightedDecomposeEffect),
    "foldWeightedEffect": (()=>foldWeightedEffect),
    "forEach": (()=>forEach),
    "forEachChunk": (()=>forEachChunk),
    "forEachChunkWhile": (()=>forEachChunkWhile),
    "forEachWhile": (()=>forEachWhile),
    "fromChannel": (()=>fromChannel),
    "fromEffect": (()=>fromEffect),
    "fromPubSub": (()=>fromPubSub),
    "fromPush": (()=>fromPush),
    "fromQueue": (()=>fromQueue),
    "head": (()=>head),
    "ignoreLeftover": (()=>ignoreLeftover),
    "isSink": (()=>isSink),
    "last": (()=>last),
    "leftover": (()=>leftover),
    "map": (()=>map),
    "mapEffect": (()=>mapEffect),
    "mapError": (()=>mapError),
    "mapInput": (()=>mapInput),
    "mapInputChunks": (()=>mapInputChunks),
    "mapInputChunksEffect": (()=>mapInputChunksEffect),
    "mapInputEffect": (()=>mapInputEffect),
    "mapLeftover": (()=>mapLeftover),
    "mkString": (()=>mkString),
    "never": (()=>never),
    "orElse": (()=>orElse),
    "provideContext": (()=>provideContext),
    "race": (()=>race),
    "raceBoth": (()=>raceBoth),
    "raceWith": (()=>raceWith),
    "refineOrDie": (()=>refineOrDie),
    "refineOrDieWith": (()=>refineOrDieWith),
    "service": (()=>service),
    "serviceWith": (()=>serviceWith),
    "serviceWithEffect": (()=>serviceWithEffect),
    "serviceWithSink": (()=>serviceWithSink),
    "some": (()=>some),
    "splitWhere": (()=>splitWhere),
    "succeed": (()=>succeed),
    "sum": (()=>sum),
    "summarized": (()=>summarized),
    "suspend": (()=>suspend),
    "sync": (()=>sync),
    "take": (()=>take),
    "timed": (()=>timed),
    "toChannel": (()=>toChannel),
    "unwrap": (()=>unwrap),
    "unwrapScoped": (()=>unwrapScoped),
    "unwrapScopedWith": (()=>unwrapScopedWith),
    "withDuration": (()=>withDuration),
    "zip": (()=>zip),
    "zipLeft": (()=>zipLeft),
    "zipRight": (()=>zipRight),
    "zipWith": (()=>zipWith)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Array.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Cause.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Chunk.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Clock$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Clock.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Duration.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Either.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Exit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/HashMap.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/HashSet.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Option.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Pipeable.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$PubSub$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/PubSub.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Queue.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Ref.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Scope.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/channel.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/channel/mergeDecision.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/core-stream.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const SinkTypeId = /*#__PURE__*/ Symbol.for("effect/Sink");
const sinkVariance = {
    /* c8 ignore next */ _A: (_)=>_,
    /* c8 ignore next */ _In: (_)=>_,
    /* c8 ignore next */ _L: (_)=>_,
    /* c8 ignore next */ _E: (_)=>_,
    /* c8 ignore next */ _R: (_)=>_
};
class SinkImpl {
    channel;
    [SinkTypeId] = sinkVariance;
    constructor(channel){
        this.channel = channel;
    }
    pipe() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
}
const isSink = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, SinkTypeId);
const suspend = (evaluate)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>toChannel(evaluate())));
const as = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, map(()=>a)));
const collectAll = ()=>new SinkImpl(collectAllLoop((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()));
/** @internal */ const collectAllLoop = (acc)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWithCause"])({
        onInput: (chunk)=>collectAllLoop((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])(chunk))),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
        onDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(acc)
    });
const collectAllN = (n)=>suspend(()=>fromChannel(collectAllNLoop(n, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])())));
/** @internal */ const collectAllNLoop = (n, acc)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWithCause"])({
        onInput: (chunk)=>{
            const [collected, leftovers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["splitAt"])(chunk, n);
            if (collected.length < n) {
                return collectAllNLoop(n - collected.length, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])(acc, collected));
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmpty"])(leftovers)) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])(acc, collected));
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(leftovers), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])(acc, collected)));
        },
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
        onDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(acc)
    });
const collectAllFrom = (self)=>collectAllWhileWith(self, {
        initial: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])(),
        while: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constTrue"],
        body: (chunk, a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(chunk, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["append"])(a))
    });
const collectAllToMap = (key, merge)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(foldLeftChunks((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])(), (map, chunk)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(chunk, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["reduce"])(map, (map, input)=>{
            const k = key(input);
            const v = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(map, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["has"])(k)) ? merge((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(map, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(k)), input) : input;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(map, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(k, v));
        }))));
};
const collectAllToMapN = (n, key, merge)=>{
    return foldWeighted({
        initial: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])(),
        maxCost: n,
        cost: (acc, input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["has"])(key(input))) ? 0 : 1,
        body: (acc, input)=>{
            const k = key(input);
            const v = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["has"])(k)) ? merge((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(k)), input) : input;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(k, v));
        }
    });
};
const collectAllToSet = ()=>foldLeftChunks((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])(), (acc, chunk)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(chunk, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["reduce"])(acc, (acc, input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(input)))));
const collectAllToSetN = (n)=>foldWeighted({
        initial: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])(),
        maxCost: n,
        cost: (acc, input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["has"])(acc, input) ? 0 : 1,
        body: (acc, input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(acc, input)
    });
const collectAllUntil = (p)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(fold([
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])(),
        true
    ], (tuple)=>tuple[1], ([chunk, _], input)=>[
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(chunk, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["append"])(input)),
            !p(input)
        ]), map((tuple)=>tuple[0]));
};
const collectAllUntilEffect = (p)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(foldEffect([
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])(),
        true
    ], (tuple)=>tuple[1], ([chunk, _], input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(p(input), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((bool)=>[
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(chunk, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["append"])(input)),
                !bool
            ]))), map((tuple)=>tuple[0]));
};
const collectAllWhile = (predicate)=>fromChannel(collectAllWhileReader(predicate, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()));
/** @internal */ const collectAllWhileReader = (predicate, done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>{
            const [collected, leftovers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toReadonlyArray"])(input), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["span"])(predicate));
            if (leftovers.length === 0) {
                return collectAllWhileReader(predicate, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(done, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(collected))));
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(leftovers)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(done, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(collected))))));
        },
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(done)
    });
const collectAllWhileEffect = (predicate)=>fromChannel(collectAllWhileEffectReader(predicate, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()));
/** @internal */ const collectAllWhileEffectReader = (predicate, done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["takeWhile"])(predicate), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"]))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((collected)=>{
                const leftovers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(collected.length));
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmpty"])(leftovers)) {
                    return collectAllWhileEffectReader(predicate, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(done, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])(collected)));
                }
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(leftovers), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(done, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])(collected)))));
            })),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(done)
    });
const collectAllWhileWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>{
    const refs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zip"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(false)));
    const newChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(refs), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(([leftoversRef, upstreamDoneRef])=>{
        const upstreamMarker = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
            onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(input), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>upstreamMarker)),
            onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
            onDone: (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(upstreamDoneRef, true)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(done))
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(upstreamMarker, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["bufferChunk"])(leftoversRef)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(collectAllWhileWithLoop(self, leftoversRef, upstreamDoneRef, options.initial, options.while, options.body)));
    }));
    return new SinkImpl(newChannel);
});
const collectAllWhileWithLoop = (self, leftoversRef, upstreamDoneRef, currentResult, p, f)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(toChannel(self), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["doneCollect"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["foldChannel"])({
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onSuccess: ([leftovers, doneValue])=>p(doneValue) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(leftoversRef, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(leftovers))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(upstreamDoneRef)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((upstreamDone)=>{
                    const accumulatedResult = f(currentResult, doneValue);
                    return upstreamDone ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(leftovers)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(accumulatedResult)) : collectAllWhileWithLoop(self, leftoversRef, upstreamDoneRef, accumulatedResult, p, f);
                })))) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(leftovers)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(currentResult))
    }));
};
const collectLeftover = (self)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["collectElements"])(toChannel(self)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(([chunks, z])=>[
            z,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(chunks)
        ])));
const mapInput = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, mapInputChunks((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(f))));
const mapInputEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>mapInputChunksEffect(self, (chunk)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEach"])(chunk, (v)=>f(v)), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])));
const mapInputChunks = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const loop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (chunk)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(f(chunk)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>loop)),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
    });
    return new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(loop, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(toChannel(self))));
});
const mapInputChunksEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const loop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (chunk)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f(chunk)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>loop)),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
    });
    return new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(loop, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeToOrFail"])(toChannel(self))));
});
const die = (defect)=>failCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(defect));
const dieMessage = (message)=>failCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuntimeException"](message)));
const dieSync = (evaluate)=>failCauseSync(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(evaluate()));
const dimap = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>map(mapInput(self, options.onInput), options.onDone));
const dimapEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>mapEffect(mapInputEffect(self, options.onInput), options.onDone));
const dimapChunks = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>map(mapInputChunks(self, options.onInput), options.onDone));
const dimapChunksEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>mapEffect(mapInputChunksEffect(self, options.onInput), options.onDone));
const drain = /*#__PURE__*/ new SinkImpl(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drain"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identityChannel"])()));
const drop = (n)=>suspend(()=>new SinkImpl(dropLoop(n)));
/** @internal */ const dropLoop = (n)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>{
            const dropped = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(n));
            const leftover = Math.max(n - input.length, 0);
            const more = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmpty"])(input) || leftover > 0;
            if (more) {
                return dropLoop(leftover);
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(dropped), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identityChannel"])()));
        },
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"]
    });
const dropUntil = (predicate)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(toChannel(dropWhile((input)=>!predicate(input))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeToOrFail"])(toChannel(drop(1)))));
const dropUntilEffect = (predicate)=>suspend(()=>new SinkImpl(dropUntilEffectReader(predicate)));
/** @internal */ const dropUntilEffectReader = (predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dropUntil"])(predicate), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((leftover)=>{
                const more = leftover.length === 0;
                return more ? dropUntilEffectReader(predicate) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(leftover)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identityChannel"])()));
            }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unwrap"]),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"]
    });
const dropWhile = (predicate)=>new SinkImpl(dropWhileReader(predicate));
/** @internal */ const dropWhileReader = (predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>{
            const out = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dropWhile"])(predicate));
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmpty"])(out)) {
                return dropWhileReader(predicate);
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(out), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identityChannel"])()));
        },
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
    });
const dropWhileEffect = (predicate)=>suspend(()=>new SinkImpl(dropWhileEffectReader(predicate)));
/** @internal */ const dropWhileEffectReader = (predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dropWhile"])(predicate), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((leftover)=>{
                const more = leftover.length === 0;
                return more ? dropWhileEffectReader(predicate) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(leftover)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identityChannel"])()));
            }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unwrap"]),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"]
    });
const ensuring = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, finalizer)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, toChannel, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensuring"])(finalizer))));
const ensuringWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, finalizer)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, toChannel, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensuringWith"])(finalizer))));
const context = ()=>fromEffect((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["context"])());
const contextWith = (f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(context(), map(f));
const contextWithEffect = (f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(context(), mapEffect(f));
const contextWithSink = (f)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unwrap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["contextWith"])((context)=>toChannel(f(context))))));
const every = (predicate)=>fold(true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"], (acc, input)=>acc && predicate(input));
const fail = (e)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(e));
const failSync = (evaluate)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failSync"])(evaluate));
const failCause = (cause)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause));
const failCauseSync = (evaluate)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCauseSync"])(evaluate));
const filterInput = (f)=>{
    return (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, mapInputChunks((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filter"])(f)));
};
const filterInputEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>mapInputChunksEffect(self, (chunk)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filter"])(chunk, f), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])));
const findEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const newChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zip"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(false)))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(([leftoversRef, upstreamDoneRef])=>{
        const upstreamMarker = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
            onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(input), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>upstreamMarker)),
            onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
            onDone: (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(upstreamDoneRef, true)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(done))
        });
        const loop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["foldChannel"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["collectElements"])(toChannel(self)), {
            onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
            onSuccess: ([leftovers, doneValue])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f(doneValue)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((satisfied)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(leftoversRef, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(leftovers))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(upstreamDoneRef)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((upstreamDone)=>{
                        if (satisfied) {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(leftovers)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(doneValue)));
                        }
                        if (upstreamDone) {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(leftovers)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])()));
                        }
                        return loop;
                    }))))))
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(upstreamMarker, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["bufferChunk"])(leftoversRef)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(loop));
    }));
    return new SinkImpl(newChannel);
});
const fold = (s, contFn, f)=>suspend(()=>new SinkImpl(foldReader(s, contFn, f)));
/** @internal */ const foldReader = (s, contFn, f)=>{
    if (!contFn(s)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(s);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>{
            const [nextS, leftovers] = foldChunkSplit(s, input, contFn, f, 0, input.length);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmpty"])(leftovers)) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(leftovers), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(nextS));
            }
            return foldReader(nextS, contFn, f);
        },
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(s)
    });
};
/** @internal */ const foldChunkSplit = (s, chunk, contFn, f, index, length)=>{
    if (index === length) {
        return [
            s,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()
        ];
    }
    const s1 = f(s, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(chunk, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(index)));
    if (contFn(s1)) {
        return foldChunkSplit(s1, chunk, contFn, f, index + 1, length);
    }
    return [
        s1,
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(chunk, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(index + 1))
    ];
};
const foldSink = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>{
    const newChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(toChannel(self), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["collectElements"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["foldChannel"])({
        onFailure: (error)=>toChannel(options.onFailure(error)),
        onSuccess: ([leftovers, z])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
                const leftoversRef = {
                    ref: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(leftovers, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filter"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmpty"]))
                };
                const refReader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>{
                    const ref = leftoversRef.ref;
                    leftoversRef.ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])();
                    return ref;
                }), // This cast is safe because of the L1 >: L <: In1 bound. It follows that
                // L <: In1 and therefore Chunk[L] can be safely cast to Chunk[In1].
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((chunk)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["writeChunk"])(chunk)));
                const passthrough = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identityChannel"])();
                const continuationSink = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(refReader, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(passthrough), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(toChannel(options.onSuccess(z))));
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["collectElements"])(continuationSink), ([newLeftovers, z1])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(leftoversRef.ref), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["writeChunk"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["writeChunk"])(newLeftovers)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(z1)));
            })
    }));
    return new SinkImpl(newChannel);
});
const foldChunks = (s, contFn, f)=>suspend(()=>new SinkImpl(foldChunksReader(s, contFn, f)));
/** @internal */ const foldChunksReader = (s, contFn, f)=>{
    if (!contFn(s)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(s);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>foldChunksReader(f(s, input), contFn, f),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(s)
    });
};
const foldChunksEffect = (s, contFn, f)=>suspend(()=>new SinkImpl(foldChunksEffectReader(s, contFn, f)));
/** @internal */ const foldChunksEffectReader = (s, contFn, f)=>{
    if (!contFn(s)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(s);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f(s, input)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((s)=>foldChunksEffectReader(s, contFn, f))),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(s)
    });
};
const foldEffect = (s, contFn, f)=>suspend(()=>new SinkImpl(foldEffectReader(s, contFn, f)));
/** @internal */ const foldEffectReader = (s, contFn, f)=>{
    if (!contFn(s)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(s);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(foldChunkSplitEffect(s, input, contFn, f)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(([nextS, leftovers])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(leftovers, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                    onNone: ()=>foldEffectReader(nextS, contFn, f),
                    onSome: (leftover)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(leftover), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(nextS))
                })))),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(s)
    });
};
/** @internal */ const foldChunkSplitEffect = (s, chunk, contFn, f)=>foldChunkSplitEffectInternal(s, chunk, 0, chunk.length, contFn, f);
/** @internal */ const foldChunkSplitEffectInternal = (s, chunk, index, length, contFn, f)=>{
    if (index === length) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])([
            s,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])()
        ]);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(f(s, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(chunk, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(index))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((s1)=>contFn(s1) ? foldChunkSplitEffectInternal(s1, chunk, index + 1, length, contFn, f) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])([
            s1,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(chunk, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(index + 1)))
        ])));
};
const foldLeft = (s, f)=>ignoreLeftover(fold(s, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constTrue"], f));
const foldLeftChunks = (s, f)=>foldChunks(s, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constTrue"], f);
const foldLeftChunksEffect = (s, f)=>ignoreLeftover(foldChunksEffect(s, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constTrue"], f));
const foldLeftEffect = (s, f)=>foldEffect(s, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constTrue"], f);
const foldUntil = (s, max, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(fold([
        s,
        0
    ], (tuple)=>tuple[1] < max, ([output, count], input)=>[
            f(output, input),
            count + 1
        ]), map((tuple)=>tuple[0]));
const foldUntilEffect = (s, max, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(foldEffect([
        s,
        0
    ], (tuple)=>tuple[1] < max, ([output, count], input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(f(output, input), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((s)=>[
                s,
                count + 1
            ]))), map((tuple)=>tuple[0]));
const foldWeighted = (options)=>foldWeightedDecompose({
        ...options,
        decompose: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["of"]
    });
const foldWeightedDecompose = (options)=>suspend(()=>new SinkImpl(foldWeightedDecomposeLoop(options.initial, 0, false, options.maxCost, options.cost, options.decompose, options.body)));
/** @internal */ const foldWeightedDecomposeLoop = (s, cost, dirty, max, costFn, decompose, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>{
            const [nextS, nextCost, nextDirty, leftovers] = foldWeightedDecomposeFold(input, 0, s, cost, dirty, max, costFn, decompose, f);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmpty"])(leftovers)) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(leftovers), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(nextS)));
            }
            if (cost > max) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(nextS);
            }
            return foldWeightedDecomposeLoop(nextS, nextCost, nextDirty, max, costFn, decompose, f);
        },
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(s)
    });
/** @internal */ const foldWeightedDecomposeFold = (input, index, s, cost, dirty, max, costFn, decompose, f)=>{
    if (index === input.length) {
        return [
            s,
            cost,
            dirty,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()
        ];
    }
    const elem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(index));
    const total = cost + costFn(s, elem);
    if (total <= max) {
        return foldWeightedDecomposeFold(input, index + 1, f(s, elem), total, true, max, costFn, decompose, f);
    }
    const decomposed = decompose(elem);
    if (decomposed.length <= 1 && !dirty) {
        // If `elem` cannot be decomposed, we need to cross the `max` threshold. To
        // minimize "injury", we only allow this when we haven't added anything else
        // to the aggregate (dirty = false).
        return [
            f(s, elem),
            total,
            true,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(index + 1))
        ];
    }
    if (decomposed.length <= 1 && dirty) {
        // If the state is dirty and `elem` cannot be decomposed, we stop folding
        // and include `elem` in the leftovers.
        return [
            s,
            cost,
            dirty,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(index))
        ];
    }
    // `elem` got decomposed, so we will recurse with the decomposed elements pushed
    // into the chunk we're processing and see if we can aggregate further.
    const next = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(decomposed, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(index + 1))));
    return foldWeightedDecomposeFold(next, 0, s, cost, dirty, max, costFn, decompose, f);
};
const foldWeightedDecomposeEffect = (options)=>suspend(()=>new SinkImpl(foldWeightedDecomposeEffectLoop(options.initial, options.maxCost, options.cost, options.decompose, options.body, 0, false)));
const foldWeightedEffect = (options)=>foldWeightedDecomposeEffect({
        ...options,
        decompose: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["of"])(input))
    });
const foldWeightedDecomposeEffectLoop = (s, max, costFn, decompose, f, cost, dirty)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(foldWeightedDecomposeEffectFold(s, max, costFn, decompose, f, input, dirty, cost, 0)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(([nextS, nextCost, nextDirty, leftovers])=>{
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmpty"])(leftovers)) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(leftovers), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(nextS)));
                }
                if (cost > max) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(nextS);
                }
                return foldWeightedDecomposeEffectLoop(nextS, max, costFn, decompose, f, nextCost, nextDirty);
            })),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(s)
    });
/** @internal */ const foldWeightedDecomposeEffectFold = (s, max, costFn, decompose, f, input, dirty, cost, index)=>{
    if (index === input.length) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])([
            s,
            cost,
            dirty,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()
        ]);
    }
    const elem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(index));
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(costFn(s, elem), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((newCost)=>cost + newCost), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((total)=>{
        if (total <= max) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(f(s, elem), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((s)=>foldWeightedDecomposeEffectFold(s, max, costFn, decompose, f, input, true, total, index + 1)));
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(decompose(elem), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((decomposed)=>{
            if (decomposed.length <= 1 && !dirty) {
                // If `elem` cannot be decomposed, we need to cross the `max` threshold. To
                // minimize "injury", we only allow this when we haven't added anything else
                // to the aggregate (dirty = false).
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(f(s, elem), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((s)=>[
                        s,
                        total,
                        true,
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(index + 1))
                    ]));
            }
            if (decomposed.length <= 1 && dirty) {
                // If the state is dirty and `elem` cannot be decomposed, we stop folding
                // and include `elem` in th leftovers.
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])([
                    s,
                    cost,
                    dirty,
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(index))
                ]);
            }
            // `elem` got decomposed, so we will recurse with the decomposed elements pushed
            // into the chunk we're processing and see if we can aggregate further.
            const next = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(decomposed, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(index + 1))));
            return foldWeightedDecomposeEffectFold(s, max, costFn, decompose, f, next, dirty, cost, 0);
        }));
    }));
};
const flatMap = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>foldSink(self, {
        onFailure: fail,
        onSuccess: f
    }));
const forEach = (f)=>{
    const process = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWithCause"])({
        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEach"])(input, (v)=>f(v), {
                discard: true
            })), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>process)),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
        onDone: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"]
    });
    return new SinkImpl(process);
};
const forEachChunk = (f)=>{
    const process = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWithCause"])({
        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f(input)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>process)),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
        onDone: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"]
    });
    return new SinkImpl(process);
};
const forEachWhile = (f)=>{
    const process = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWithCause"])({
        onInput: (input)=>forEachWhileReader(f, input, 0, input.length, process),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
        onDone: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"]
    });
    return new SinkImpl(process);
};
/** @internal */ const forEachWhileReader = (f, input, index, length, cont)=>{
    if (index === length) {
        return cont;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(index)))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((bool)=>bool ? forEachWhileReader(f, input, index + 1, length, cont) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(index)))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAll"])((error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drop"])(index))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(error)))));
};
const forEachChunkWhile = (f)=>{
    const reader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f(input)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((cont)=>cont ? reader : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"])),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"]
    });
    return new SinkImpl(reader);
};
const fromChannel = (channel)=>new SinkImpl(channel);
const fromEffect = (effect)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(effect));
const fromPubSub = (pubsub, options)=>fromQueue(pubsub, options);
const fromPush = (push)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unwrapScoped"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(push, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(fromPushPull))));
const fromPushPull = (push)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["foldChannel"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(input))), {
                onFailure: ([either, leftovers])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(either, {
                        onLeft: (error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(leftovers), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(error))),
                        onRight: (z)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(leftovers), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(z)))
                    }),
                onSuccess: ()=>fromPushPull(push)
            }),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["foldChannel"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])())), {
                onFailure: ([either, leftovers])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(either, {
                        onLeft: (error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(leftovers), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(error))),
                        onRight: (z)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(leftovers), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(z)))
                    }),
                onSuccess: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dieMessage"])("BUG: Sink.fromPush - please report an issue at https://github.com/Effect-TS/effect/issues"))
            })
    });
const fromQueue = (queue, options)=>options?.shutdown ? unwrapScoped((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["acquireRelease"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(queue), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["shutdown"]), fromQueue)) : forEachChunk((input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offerAll"])(queue, input)));
const head = ()=>fold((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNone"], (option, input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(option, {
            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(input),
            onSome: ()=>option
        }));
const ignoreLeftover = (self)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["drain"])(toChannel(self)));
const last = ()=>foldLeftChunks((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])(), (s, input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["orElse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["last"])(input), ()=>s));
const leftover = (chunk)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(chunk)));
const map = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    return new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(toChannel(self), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(f)));
});
const mapEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(toChannel(self), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapEffect"])(f))));
const mapError = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(toChannel(self), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapError"])(f))));
const mapLeftover = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(toChannel(self), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapOut"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(f)))));
const never = /*#__PURE__*/ fromEffect(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["never"]);
const orElse = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(toChannel(self), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["orElse"])(()=>toChannel(that())))));
const provideContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, context)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(toChannel(self), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provideContext"])(context))));
const race = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, raceBoth(that), map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["merge"])));
const raceBoth = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSink(args[1]), (self, that, options)=>raceWith(self, {
        other: that,
        onSelfDone: (selfDone)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(selfDone, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])),
        onOtherDone: (thatDone)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(thatDone, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"])),
        capacity: options?.capacity ?? 16
    }));
const raceWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>{
    function race(scope) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["gen"])(function*() {
            const pubsub = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$PubSub$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["bounded"])(options?.capacity ?? 16);
            const subscription1 = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extend"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$PubSub$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["subscribe"])(pubsub), scope);
            const subscription2 = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extend"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$PubSub$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["subscribe"])(pubsub), scope);
            const reader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toPubSub"])(pubsub);
            const writer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromQueue"])(subscription1).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(toChannel(self)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipLeft"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["shutdown"])(subscription1))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mergeWith"])({
                other: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromQueue"])(subscription2).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(toChannel(options.other)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipLeft"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["shutdown"])(subscription2)))),
                onSelfDone: options.onSelfDone,
                onOtherDone: options.onOtherDone
            }));
            const racedChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mergeWith"])(reader, {
                other: writer,
                onSelfDone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Await"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]),
                onOtherDone: (exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])(exit)
            });
            return new SinkImpl(racedChannel);
        });
    }
    return unwrapScopedWith(race);
});
const refineOrDie = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, refineOrDieWith(pf, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"])));
const refineOrDieWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, pf, f)=>{
    const newChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, toChannel, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAll"])((error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(pf(error), {
            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCauseSync"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(f(error))),
            onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"]
        })));
    return new SinkImpl(newChannel);
});
const service = (tag)=>serviceWith(tag, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]);
const serviceWith = (tag, f)=>fromEffect((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(tag, f));
const serviceWithEffect = (tag, f)=>fromEffect((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(tag, f));
const serviceWithSink = (tag, f)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(tag, (service)=>toChannel(f(service))), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unwrap"]));
const some = (predicate)=>fold(false, (bool)=>!bool, (acc, input)=>acc || predicate(input));
const splitWhere = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const newChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])())), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((ref)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(splitWhereSplitter(false, ref, f), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeToOrFail"])(toChannel(self)), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["collectElements"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(([leftovers, z])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(ref)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((leftover)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(leftover, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(leftovers)))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(z)))))))));
    return new SinkImpl(newChannel);
});
/** @internal */ const splitWhereSplitter = (written, leftovers, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWithCause"])({
        onInput: (input)=>{
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmpty"])(input)) {
                return splitWhereSplitter(written, leftovers, f);
            }
            if (written) {
                const index = indexWhere(input, f);
                if (index === -1) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(input), splitWhereSplitter(true, leftovers, f));
                }
                const [left, right] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["splitAt"])(input, index);
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(left), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(leftovers, right)));
            }
            const index = indexWhere(input, f, 1);
            if (index === -1) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(input), splitWhereSplitter(true, leftovers, f));
            }
            const [left, right] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(input, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["splitAt"])(Math.max(index, 1)));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(left), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(leftovers, right)));
        },
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
    });
/** @internal */ const indexWhere = (self, predicate, from = 0)=>{
    const iterator = self[Symbol.iterator]();
    let index = 0;
    let result = -1;
    let next;
    while(result < 0 && (next = iterator.next()) && !next.done){
        const a = next.value;
        if (index >= from && predicate(a)) {
            result = index;
        }
        index = index + 1;
    }
    return result;
};
const succeed = (a)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(a));
const sum = /*#__PURE__*/ foldLeftChunks(0, (acc, chunk)=>acc + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["reduce"])(chunk, 0, (s, a)=>s + a));
const summarized = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, summary, f)=>{
    const newChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(summary), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((start)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, toChannel, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(summary), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((end)=>[
                    done,
                    f(start, end)
                ]))))));
    return new SinkImpl(newChannel);
});
const sync = (evaluate)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(evaluate));
const take = (n)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(foldChunks((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])(), (chunk)=>chunk.length < n, (acc, chunk)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])(chunk))), flatMap((acc)=>{
        const [taken, leftover] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["splitAt"])(n));
        return new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(leftover), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(taken))));
    }));
const toChannel = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(self) ? toChannel(fromEffect(self)) : self.channel;
const unwrap = (effect)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unwrap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(effect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((sink)=>toChannel(sink)))));
const unwrapScoped = (effect)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unwrapScoped"])(effect.pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((sink)=>toChannel(sink)))));
const unwrapScopedWith = (f)=>new SinkImpl((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unwrapScopedWith"])((scope)=>f(scope).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((sink)=>toChannel(sink)))));
const withDuration = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, summarized(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Clock$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentTimeMillis"], (start, end)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["millis"])(end - start)));
const zip = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSink(args[1]), (self, that, options)=>zipWith(self, that, (z, z2)=>[
            z,
            z2
        ], options));
const zipLeft = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSink(args[1]), (self, that, options)=>zipWith(self, that, (z, _)=>z, options));
const zipRight = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSink(args[1]), (self, that, options)=>zipWith(self, that, (_, z2)=>z2, options));
const zipWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSink(args[1]), (self, that, f, options)=>options?.concurrent ? raceWith(self, {
        other: that,
        onSelfDone: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
            onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause)),
            onSuccess: (leftZ)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Await"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                    onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
                    onSuccess: (rightZ)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(f(leftZ, rightZ))
                }))
        }),
        onOtherDone: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
            onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause)),
            onSuccess: (rightZ)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Await"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                    onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
                    onSuccess: (leftZ)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(f(leftZ, rightZ))
                }))
        })
    }) : flatMap(self, (z)=>map(that, (z2)=>f(z, z2))));
const channelToSink = (self)=>new SinkImpl(self);
const count = /*#__PURE__*/ foldLeftChunks(0, (acc, chunk)=>acc + chunk.length);
const mkString = /*#__PURE__*/ suspend(()=>{
    const strings = [];
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(foldLeftChunks(void 0, (_, elems)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(elems, (elem)=>{
            strings.push(String(elem));
        })), map(()=>strings.join("")));
});
const timed = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(/*#__PURE__*/ withDuration(drain), /*#__PURE__*/ map((tuple)=>tuple[1])); //# sourceMappingURL=sink.js.map
}}),

};

//# sourceMappingURL=node_modules_effect_dist_esm_internal_sink_676bd089.js.map