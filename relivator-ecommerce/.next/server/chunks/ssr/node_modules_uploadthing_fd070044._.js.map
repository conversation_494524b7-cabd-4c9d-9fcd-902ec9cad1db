{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/shared-schemas.js"], "sourcesContent": ["import * as S from 'effect/Schema';\nimport { ValidContentDispositions, ValidACLs } from '@uploadthing/shared';\n\nconst ContentDispositionSchema = S.Literal(...ValidContentDispositions);\nconst ACLSchema = S.Literal(...ValidACLs);\n/**\n * Valid options for the `?actionType` query param\n */ const ActionType = S.Literal(\"upload\");\n/**\n * Valid options for the `uploadthing-hook` header\n * for requests coming from UT server\n */ const UploadThingHook = S.Literal(\"callback\", \"error\");\n/**\n * =============================================================================\n * =========================== Configuration ===================================\n * =============================================================================\n */ const DecodeString = S.transform(S.Uint8ArrayFromSelf, S.String, {\n    decode: (data)=>new TextDecoder().decode(data),\n    encode: (data)=>new TextEncoder().encode(data)\n});\nconst ParsedToken = S.Struct({\n    apiKey: S.Redacted(S.String.pipe(S.startsWith(\"sk_\"))),\n    appId: S.String,\n    regions: S.NonEmptyArray(S.String),\n    ingestHost: S.String.pipe(S.optionalWith({\n        default: ()=>\"ingest.uploadthing.com\"\n    }))\n});\nconst UploadThingToken = S.Uint8ArrayFromBase64.pipe(S.compose(DecodeString), S.compose(S.parseJson(ParsedToken)));\n/**\n * =============================================================================\n * ======================== File Type Hierarchy ===============================\n * =============================================================================\n */ /**\n * Properties from the web File object, this is what the client sends when initiating an upload\n */ class FileUploadData extends S.Class(\"FileUploadData\")({\n    name: S.String,\n    size: S.Number,\n    type: S.String,\n    lastModified: S.Number.pipe(S.optional)\n}) {\n}\n/**\n * `.middleware()` can add a customId to the incoming file data\n */ class FileUploadDataWithCustomId extends FileUploadData.extend(\"FileUploadDataWithCustomId\")({\n    customId: S.NullOr(S.String)\n}) {\n}\n/**\n * When files are uploaded, we get back\n * - a key\n * - URLs for the file\n * - the hash (md5-hex) of the uploaded file's contents\n */ class UploadedFileData extends FileUploadDataWithCustomId.extend(\"UploadedFileData\")({\n    key: S.String,\n    /**\n   * @deprecated\n   * This field will be removed in uploadthing v9. Use `ufsUrl` instead.\n   */ url: S.String,\n    /**\n   * @deprecated\n   * This field will be removed in uploadthing v9. Use `ufsUrl` instead.\n   */ appUrl: S.String,\n    ufsUrl: S.String,\n    fileHash: S.String\n}) {\n}\n/**\n * =============================================================================\n * ======================== Server Response Schemas ============================\n * =============================================================================\n */ class NewPresignedUrl extends S.Class(\"NewPresignedUrl\")({\n    url: S.String,\n    key: S.String,\n    customId: S.NullOr(S.String),\n    name: S.String\n}) {\n}\nclass MetadataFetchStreamPart extends S.Class(\"MetadataFetchStreamPart\")({\n    payload: S.String,\n    signature: S.String,\n    hook: UploadThingHook\n}) {\n}\nclass MetadataFetchResponse extends S.Class(\"MetadataFetchResponse\")({\n    ok: S.Boolean\n}) {\n}\nclass CallbackResultResponse extends S.Class(\"CallbackResultResponse\")({\n    ok: S.Boolean\n}) {\n}\n/**\n * =============================================================================\n * ======================== Client Action Payloads ============================\n * =============================================================================\n */ class UploadActionPayload extends S.Class(\"UploadActionPayload\")({\n    files: S.Array(FileUploadData),\n    input: S.Unknown\n}) {\n}\n\nexport { ACLSchema, ActionType, CallbackResultResponse, ContentDispositionSchema, FileUploadData, FileUploadDataWithCustomId, MetadataFetchResponse, MetadataFetchStreamPart, NewPresignedUrl, ParsedToken, UploadActionPayload, UploadThingHook, UploadThingToken, UploadedFileData };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEA,MAAM,2BAA2B,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,KAAK,wJAAA,CAAA,2BAAwB;AACtE,MAAM,YAAY,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,KAAK,wJAAA,CAAA,YAAS;AACxC;;CAEC,GAAG,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE;AACjC;;;CAGC,GAAG,MAAM,kBAAkB,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE,YAAY;AAClD;;;;CAIC,GAAG,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,YAAW,AAAD,EAAE,+JAAA,CAAA,qBAAoB,EAAE,+JAAA,CAAA,SAAQ,EAAE;IACjE,QAAQ,CAAC,OAAO,IAAI,cAAc,MAAM,CAAC;IACzC,QAAQ,CAAC,OAAO,IAAI,cAAc,MAAM,CAAC;AAC7C;AACA,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,SAAQ,AAAD,EAAE;IACzB,QAAQ,CAAA,GAAA,+JAAA,CAAA,WAAU,AAAD,EAAE,+JAAA,CAAA,SAAQ,CAAC,IAAI,CAAC,CAAA,GAAA,+JAAA,CAAA,aAAY,AAAD,EAAE;IAC9C,OAAO,+JAAA,CAAA,SAAQ;IACf,SAAS,CAAA,GAAA,+JAAA,CAAA,gBAAe,AAAD,EAAE,+JAAA,CAAA,SAAQ;IACjC,YAAY,+JAAA,CAAA,SAAQ,CAAC,IAAI,CAAC,CAAA,GAAA,+JAAA,CAAA,eAAc,AAAD,EAAE;QACrC,SAAS,IAAI;IACjB;AACJ;AACA,MAAM,mBAAmB,+JAAA,CAAA,uBAAsB,CAAC,IAAI,CAAC,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE,CAAA,GAAA,+JAAA,CAAA,YAAW,AAAD,EAAE;AACpG;;;;CAIC,GAAG;;CAEH,GAAG,MAAM,uBAAuB,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,kBAAkB;IACvD,MAAM,+JAAA,CAAA,SAAQ;IACd,MAAM,+JAAA,CAAA,SAAQ;IACd,MAAM,+JAAA,CAAA,SAAQ;IACd,cAAc,+JAAA,CAAA,SAAQ,CAAC,IAAI,CAAC,+JAAA,CAAA,WAAU;AAC1C;AACA;AACA;;CAEC,GAAG,MAAM,mCAAmC,eAAe,MAAM,CAAC,8BAA8B;IAC7F,UAAU,CAAA,GAAA,+JAAA,CAAA,SAAQ,AAAD,EAAE,+JAAA,CAAA,SAAQ;AAC/B;AACA;AACA;;;;;CAKC,GAAG,MAAM,yBAAyB,2BAA2B,MAAM,CAAC,oBAAoB;IACrF,KAAK,+JAAA,CAAA,SAAQ;IACb;;;GAGD,GAAG,KAAK,+JAAA,CAAA,SAAQ;IACf;;;GAGD,GAAG,QAAQ,+JAAA,CAAA,SAAQ;IAClB,QAAQ,+JAAA,CAAA,SAAQ;IAChB,UAAU,+JAAA,CAAA,SAAQ;AACtB;AACA;AACA;;;;CAIC,GAAG,MAAM,wBAAwB,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,mBAAmB;IACzD,KAAK,+JAAA,CAAA,SAAQ;IACb,KAAK,+JAAA,CAAA,SAAQ;IACb,UAAU,CAAA,GAAA,+JAAA,CAAA,SAAQ,AAAD,EAAE,+JAAA,CAAA,SAAQ;IAC3B,MAAM,+JAAA,CAAA,SAAQ;AAClB;AACA;AACA,MAAM,gCAAgC,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,2BAA2B;IACrE,SAAS,+JAAA,CAAA,SAAQ;IACjB,WAAW,+JAAA,CAAA,SAAQ;IACnB,MAAM;AACV;AACA;AACA,MAAM,8BAA8B,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,yBAAyB;IACjE,IAAI,+JAAA,CAAA,UAAS;AACjB;AACA;AACA,MAAM,+BAA+B,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,0BAA0B;IACnE,IAAI,+JAAA,CAAA,UAAS;AACjB;AACA;AACA;;;;CAIC,GAAG,MAAM,4BAA4B,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,uBAAuB;IACjE,OAAO,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE;IACf,OAAO,+JAAA,CAAA,UAAS;AACpB;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/config.js"], "sourcesContent": ["import * as Config from 'effect/Config';\nimport * as ConfigProvider from 'effect/ConfigProvider';\nimport * as Effect from 'effect/Effect';\nimport * as S from 'effect/Schema';\nimport { filterDefinedObjectValues, UploadThingError } from '@uploadthing/shared';\nimport { UploadThingToken } from './shared-schemas.js';\n\nvar version = \"7.7.2\";\n\n/**\n * Merge in `import.meta.env` to the built-in `process.env` provider\n * Prefix keys with `UPLOADTHING_` so we can reference just the name.\n * @example\n * process.env.UPLOADTHING_TOKEN = \"foo\"\n * Config.string(\"token\"); // Config<\"foo\">\n */ const envProvider = ConfigProvider.fromEnv().pipe(ConfigProvider.orElse(()=>ConfigProvider.fromMap(new Map(Object.entries(filterDefinedObjectValues(// fuck this I give up. import.meta is a mistake, someone else can fix it\n    import.meta?.env ?? {}))), {\n        pathDelim: \"_\"\n    })), ConfigProvider.nested(\"uploadthing\"), ConfigProvider.constantCase);\n/**\n * Config provider that merges the options from the object\n * and environment variables prefixed with `UPLOADTHING_`.\n * @remarks Options take precedence over environment variables.\n */ const configProvider = (options)=>ConfigProvider.fromJson(options ?? {}).pipe(ConfigProvider.orElse(()=>envProvider));\nconst IsDevelopment = Config.boolean(\"isDev\").pipe(Config.orElse(()=>Config.succeed(typeof process !== \"undefined\" ? process.env.NODE_ENV : undefined).pipe(Config.map((_)=>_ === \"development\"))), Config.withDefault(false));\nconst UTToken = S.Config(\"token\", UploadThingToken).pipe(Effect.catchTags({\n    ConfigError: (e)=>new UploadThingError({\n            code: e._op === \"InvalidData\" ? \"INVALID_SERVER_CONFIG\" : \"MISSING_ENV\",\n            message: e._op === \"InvalidData\" ? \"Invalid token. A token is a base64 encoded JSON object matching { apiKey: string, appId: string, regions: string[] }.\" : \"Missing token. Please set the `UPLOADTHING_TOKEN` environment variable or provide a token manually through config.\",\n            cause: e\n        })\n}));\nconst ApiUrl = Config.string(\"apiUrl\").pipe(Config.withDefault(\"https://api.uploadthing.com\"), Config.mapAttempt((_)=>new URL(_)), Config.map((url)=>url.href.replace(/\\/$/, \"\")));\nconst IngestUrl = Effect.fn(function*(preferredRegion) {\n    const { regions, ingestHost } = yield* UTToken;\n    const region = preferredRegion ? regions.find((r)=>r === preferredRegion) ?? regions[0] : regions[0];\n    return yield* Config.string(\"ingestUrl\").pipe(Config.withDefault(`https://${region}.${ingestHost}`), Config.mapAttempt((_)=>new URL(_)), Config.map((url)=>url.href.replace(/\\/$/, \"\")));\n});\nconst UtfsHost = Config.string(\"utfsHost\").pipe(Config.withDefault(\"utfs.io\"));\nconst UfsHost = Config.string(\"ufsHost\").pipe(Config.withDefault(\"ufs.sh\"));\n\nexport { ApiUrl, IngestUrl, IsDevelopment, version as UPLOADTHING_VERSION, UTToken, UfsHost, UtfsHost, configProvider };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEA,IAAI,UAAU;AAEd;;;;;;CAMC,GAAG,MAAM,cAAc,CAAA,GAAA,uJAAA,CAAA,UAAsB,AAAD,IAAI,IAAI,CAAC,CAAA,GAAA,uJAAA,CAAA,SAAqB,AAAD,EAAE,IAAI,CAAA,GAAA,uJAAA,CAAA,UAAsB,AAAD,EAAE,IAAI,IAAI,OAAO,OAAO,CAAC,CAAA,GAAA,wJAAA,CAAA,4BAAyB,AAAD,EAClJ,+BAAa,OAAO,CAAC,MAAM;QACvB,WAAW;IACf,KAAK,CAAA,GAAA,uJAAA,CAAA,SAAqB,AAAD,EAAE,gBAAgB,uJAAA,CAAA,eAA2B;AAC1E;;;;CAIC,GAAG,MAAM,iBAAiB,CAAC,UAAU,CAAA,GAAA,uJAAA,CAAA,WAAuB,AAAD,EAAE,WAAW,CAAC,GAAG,IAAI,CAAC,CAAA,GAAA,uJAAA,CAAA,SAAqB,AAAD,EAAE,IAAI;AAC5G,MAAM,gBAAgB,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,SAAS,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,SAAa,AAAD,EAAE,IAAI,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,OAAO,YAAY,gEAAqC,WAAW,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAC,IAAI,MAAM,kBAAkB,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;AACvN,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,SAAQ,AAAD,EAAE,SAAS,qKAAA,CAAA,mBAAgB,EAAE,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,YAAgB,AAAD,EAAE;IACtE,aAAa,CAAC,IAAI,IAAI,wJAAA,CAAA,mBAAgB,CAAC;YAC/B,MAAM,EAAE,GAAG,KAAK,gBAAgB,0BAA0B;YAC1D,SAAS,EAAE,GAAG,KAAK,gBAAgB,0HAA0H;YAC7J,OAAO;QACX;AACR;AACA,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,SAAa,AAAD,EAAE,UAAU,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE,gCAAgC,CAAA,GAAA,+IAAA,CAAA,aAAiB,AAAD,EAAE,CAAC,IAAI,IAAI,IAAI,KAAK,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO;AAC7K,MAAM,YAAY,CAAA,GAAA,+IAAA,CAAA,KAAS,AAAD,EAAE,UAAU,eAAe;IACjD,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,OAAO;IACvC,MAAM,SAAS,kBAAkB,QAAQ,IAAI,CAAC,CAAC,IAAI,MAAM,oBAAoB,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;IACpG,OAAO,OAAO,CAAA,GAAA,+IAAA,CAAA,SAAa,AAAD,EAAE,aAAa,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,CAAA,GAAA,+IAAA,CAAA,aAAiB,AAAD,EAAE,CAAC,IAAI,IAAI,IAAI,KAAK,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO;AACvL;AACA,MAAM,WAAW,CAAA,GAAA,+IAAA,CAAA,SAAa,AAAD,EAAE,YAAY,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;AACnE,MAAM,UAAU,CAAA,GAAA,+IAAA,CAAA,SAAa,AAAD,EAAE,WAAW,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/deprecations.js"], "sourcesContent": ["const logDeprecationWarning = (message)=>{\n    // eslint-disable-next-line no-console\n    console.warn(`⚠️ [uploadthing][deprecated] ${message}`);\n};\n\nexport { logDeprecationWarning };\n"], "names": [], "mappings": ";;;AAAA,MAAM,wBAAwB,CAAC;IAC3B,sCAAsC;IACtC,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,SAAS;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/error-formatter.js"], "sourcesContent": ["function defaultErrorFormatter(error) {\n    return {\n        message: error.message\n    };\n}\nfunction formatError(error, router) {\n    const firstSlug = Object.keys(router)[0];\n    const errorFormatter = firstSlug ? router[firstSlug]?.errorFormatter ?? defaultErrorFormatter : defaultErrorFormatter;\n    return errorFormatter(error);\n}\n\nexport { defaultErrorFormatter, formatError };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,sBAAsB,KAAK;IAChC,OAAO;QACH,SAAS,MAAM,OAAO;IAC1B;AACJ;AACA,SAAS,YAAY,KAAK,EAAE,MAAM;IAC9B,MAAM,YAAY,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IACxC,MAAM,iBAAiB,YAAY,MAAM,CAAC,UAAU,EAAE,kBAAkB,wBAAwB;IAChG,OAAO,eAAe;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/jsonl.js"], "sourcesContent": ["import * as Effect from 'effect/Effect';\nimport * as S from 'effect/Schema';\nimport * as Stream from 'effect/Stream';\n\nconst handleJsonLineStream = (schema, onChunk)=>(stream)=>{\n        let buf = \"\";\n        return stream.pipe(Stream.decodeText(), Stream.mapEffect((chunk)=>Effect.gen(function*() {\n                buf += chunk;\n                // Scan buffer for newlines\n                const parts = buf.split(\"\\n\");\n                const validChunks = [];\n                for (const part of parts){\n                    try {\n                        // Attempt to parse chunk as JSON\n                        validChunks.push(JSON.parse(part));\n                        // Advance buffer if parsing succeeded\n                        buf = buf.slice(part.length + 1);\n                    } catch  {\n                    //\n                    }\n                }\n                yield* Effect.logDebug(\"Received chunks\").pipe(Effect.annotateLogs(\"chunk\", chunk), Effect.annotateLogs(\"parsedChunks\", validChunks), Effect.annotateLogs(\"buf\", buf));\n                return validChunks;\n            })), Stream.mapEffect(S.decodeUnknown(S.Array(schema))), Stream.mapEffect(Effect.forEach((part)=>onChunk(part))), Stream.runDrain, Effect.withLogSpan(\"handleJsonLineStream\"));\n    };\n\nexport { handleJsonLineStream };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,uBAAuB,CAAC,QAAQ,UAAU,CAAC;QACzC,IAAI,MAAM;QACV,OAAO,OAAO,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,aAAiB,AAAD,KAAK,CAAA,GAAA,+IAAA,CAAA,YAAgB,AAAD,EAAE,CAAC,QAAQ,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;gBACrE,OAAO;gBACP,2BAA2B;gBAC3B,MAAM,QAAQ,IAAI,KAAK,CAAC;gBACxB,MAAM,cAAc,EAAE;gBACtB,KAAK,MAAM,QAAQ,MAAM;oBACrB,IAAI;wBACA,iCAAiC;wBACjC,YAAY,IAAI,CAAC,KAAK,KAAK,CAAC;wBAC5B,sCAAsC;wBACtC,MAAM,IAAI,KAAK,CAAC,KAAK,MAAM,GAAG;oBAClC,EAAE,OAAO;oBACT,EAAE;oBACF;gBACJ;gBACA,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,mBAAmB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,SAAS,QAAQ,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,gBAAgB,cAAc,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,OAAO;gBACjK,OAAO;YACX,KAAK,CAAA,GAAA,+IAAA,CAAA,YAAgB,AAAD,EAAE,CAAA,GAAA,+JAAA,CAAA,gBAAe,AAAD,EAAE,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,WAAW,CAAA,GAAA,+IAAA,CAAA,YAAgB,AAAD,EAAE,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,CAAC,OAAO,QAAQ,SAAS,+IAAA,CAAA,WAAe,EAAE,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;IAC9J", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/logger.js"], "sourcesContent": ["import * as Config from 'effect/Config';\nimport * as ConfigError from 'effect/ConfigError';\nimport * as Effect from 'effect/Effect';\nimport * as Either from 'effect/Either';\nimport * as Layer from 'effect/Layer';\nimport * as Logger from 'effect/Logger';\nimport * as LogLevel from 'effect/LogLevel';\nimport { UploadThingError } from '@uploadthing/shared';\nimport { IsDevelopment } from './config.js';\n\n/**\n * Config.logLevel counter-intuitively accepts LogLevel[\"label\"]\n * instead of a literal, ripping it and changing to accept literal\n * Effect 4.0 will change this to accept a literal and then we can\n * remove this and go back to the built-in validator.\n */ const ConfigLogLevel = (name)=>{\n    const config = Config.mapOrFail(Config.string(), (literal)=>{\n        const level = LogLevel.allLevels.find((level)=>level._tag === literal);\n        return level === undefined ? Either.left(ConfigError.InvalidData([], `Expected a log level but received ${literal}`)) : Either.right(level);\n    });\n    return name === undefined ? config : Config.nested(config, name);\n};\nconst withMinimalLogLevel = ConfigLogLevel(\"logLevel\").pipe(Config.withDefault(LogLevel.Info), Effect.andThen((level)=>Logger.minimumLogLevel(level)), Effect.tapError((e)=>Effect.logError(\"Invalid log level\").pipe(Effect.annotateLogs(\"error\", e))), Effect.catchTag(\"ConfigError\", (e)=>new UploadThingError({\n        code: \"INVALID_SERVER_CONFIG\",\n        message: \"Invalid server configuration\",\n        cause: e\n    })), Layer.unwrapEffect);\nconst LogFormat = Config.literal(\"json\", \"logFmt\", \"structured\", \"pretty\")(\"logFormat\");\nconst withLogFormat = Effect.gen(function*() {\n    const isDev = yield* IsDevelopment;\n    const logFormat = yield* LogFormat.pipe(Config.withDefault(isDev ? \"pretty\" : \"json\"));\n    return Logger[logFormat];\n}).pipe(Effect.catchTag(\"ConfigError\", (e)=>new UploadThingError({\n        code: \"INVALID_SERVER_CONFIG\",\n        message: \"Invalid server configuration\",\n        cause: e\n    })), Layer.unwrapEffect);\nconst logHttpClientResponse = (message, opts)=>{\n    const mixin = opts?.mixin ?? \"json\";\n    const level = LogLevel.fromLiteral(opts?.level ?? \"Debug\");\n    return (response)=>Effect.flatMap(mixin !== \"None\" ? response[mixin] : Effect.void, ()=>Effect.logWithLevel(level, `${message} (${response.status})`).pipe(Effect.annotateLogs(\"response\", response)));\n};\nconst logHttpClientError = (message)=>(err)=>err._tag === \"ResponseError\" ? logHttpClientResponse(message, {\n            level: \"Error\"\n        })(err.response) : Effect.logError(message).pipe(Effect.annotateLogs(\"error\", err));\n\nexport { LogFormat, logHttpClientError, logHttpClientResponse, withLogFormat, withMinimalLogLevel };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA;;;;;CAKC,GAAG,MAAM,iBAAiB,CAAC;IACxB,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,YAAgB,AAAD,EAAE,CAAA,GAAA,+IAAA,CAAA,SAAa,AAAD,KAAK,CAAC;QAC9C,MAAM,QAAQ,iJAAA,CAAA,YAAkB,CAAC,IAAI,CAAC,CAAC,QAAQ,MAAM,IAAI,KAAK;QAC9D,OAAO,UAAU,YAAY,CAAA,GAAA,+IAAA,CAAA,OAAW,AAAD,EAAE,CAAA,GAAA,oJAAA,CAAA,cAAuB,AAAD,EAAE,EAAE,EAAE,CAAC,kCAAkC,EAAE,SAAS,KAAK,CAAA,GAAA,+IAAA,CAAA,QAAY,AAAD,EAAE;IACzI;IACA,OAAO,SAAS,YAAY,SAAS,CAAA,GAAA,+IAAA,CAAA,SAAa,AAAD,EAAE,QAAQ;AAC/D;AACA,MAAM,sBAAsB,eAAe,YAAY,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE,iJAAA,CAAA,OAAa,GAAG,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,CAAC,QAAQ,gJAAO,eAAe,CAAC,SAAS,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,CAAC,IAAI,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,qBAAqB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,SAAS,MAAM,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,eAAe,CAAC,IAAI,IAAI,wJAAA,CAAA,mBAAgB,CAAC;QAC1S,MAAM;QACN,SAAS;QACT,OAAO;IACX,KAAK,8IAAA,CAAA,eAAkB;AAC3B,MAAM,YAAY,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,QAAQ,UAAU,cAAc,UAAU;AAC3E,MAAM,gBAAgB,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;IAC7B,MAAM,QAAQ,OAAO,0JAAA,CAAA,gBAAa;IAClC,MAAM,YAAY,OAAO,UAAU,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE,QAAQ,WAAW;IAC9E,OAAO,+IAAM,CAAC,UAAU;AAC5B,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,eAAe,CAAC,IAAI,IAAI,wJAAA,CAAA,mBAAgB,CAAC;QACzD,MAAM;QACN,SAAS;QACT,OAAO;IACX,KAAK,8IAAA,CAAA,eAAkB;AAC3B,MAAM,wBAAwB,CAAC,SAAS;IACpC,MAAM,QAAQ,MAAM,SAAS;IAC7B,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,cAAoB,AAAD,EAAE,MAAM,SAAS;IAClD,OAAO,CAAC,WAAW,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,UAAU,SAAS,QAAQ,CAAC,MAAM,GAAG,+IAAA,CAAA,OAAW,EAAE,IAAI,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,OAAO,GAAG,QAAQ,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,YAAY;AAC/L;AACA,MAAM,qBAAqB,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,KAAK,kBAAkB,sBAAsB,SAAS;YAC/F,OAAO;QACX,GAAG,IAAI,QAAQ,IAAI,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,SAAS,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,SAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/parser.js"], "sourcesContent": ["import * as Cause from 'effect/Cause';\nimport * as Data from 'effect/Data';\nimport * as Runtime from 'effect/Runtime';\nimport * as Schema from 'effect/Schema';\n\nclass ParserError extends Data.TaggedError(\"ParserError\") {\n    constructor(...args){\n        super(...args), this.message = \"Input validation failed. The original error with it's validation issues is in the error cause.\";\n    }\n}\nfunction getParseFn(parser) {\n    if (\"parseAsync\" in parser && typeof parser.parseAsync === \"function\") {\n        /**\n     * Zod\n     * TODO (next major): Consider wrapping ZodError in ParserError\n     */ return parser.parseAsync;\n    }\n    if (Schema.isSchema(parser)) {\n        /**\n     * Effect Schema\n     */ return (value)=>Schema.decodeUnknownPromise(parser)(value).catch((error)=>{\n                throw new ParserError({\n                    cause: Cause.squash(error[Runtime.FiberFailureCauseId])\n                });\n            });\n    }\n    if (\"~standard\" in parser) {\n        /**\n     * Standard Schema\n     * TODO (next major): Consider moving this to the top of the function\n     */ return async (value)=>{\n            const result = await parser[\"~standard\"].validate(value);\n            if (result.issues) {\n                throw new ParserError({\n                    cause: result.issues\n                });\n            }\n            return result.value;\n        };\n    }\n    throw new Error(\"Invalid parser\");\n}\n\nexport { ParserError, getParseFn };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,oBAAoB,CAAA,GAAA,6IAAA,CAAA,cAAgB,AAAD,EAAE;IACvC,YAAY,GAAG,IAAI,CAAC;QAChB,KAAK,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG;IACnC;AACJ;AACA,SAAS,WAAW,MAAM;IACtB,IAAI,gBAAgB,UAAU,OAAO,OAAO,UAAU,KAAK,YAAY;QACnE;;;KAGH,GAAG,OAAO,OAAO,UAAU;IAC5B;IACA,IAAI,CAAA,GAAA,+JAAA,CAAA,WAAe,AAAD,EAAE,SAAS;QACzB;;KAEH,GAAG,OAAO,CAAC,QAAQ,CAAA,GAAA,+JAAA,CAAA,uBAA2B,AAAD,EAAE,QAAQ,OAAO,KAAK,CAAC,CAAC;gBAC1D,MAAM,IAAI,YAAY;oBAClB,OAAO,CAAA,GAAA,8IAAA,CAAA,SAAY,AAAD,EAAE,KAAK,CAAC,gJAAA,CAAA,sBAA2B,CAAC;gBAC1D;YACJ;IACR;IACA,IAAI,eAAe,QAAQ;QACvB;;;KAGH,GAAG,OAAO,OAAO;YACV,MAAM,SAAS,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YAClD,IAAI,OAAO,MAAM,EAAE;gBACf,MAAM,IAAI,YAAY;oBAClB,OAAO,OAAO,MAAM;gBACxB;YACJ;YACA,OAAO,OAAO,KAAK;QACvB;IACJ;IACA,MAAM,IAAI,MAAM;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/route-config.js"], "sourcesContent": ["import * as Data from 'effect/Data';\nimport * as Effect from 'effect/Effect';\nimport { matchFileType, objectKeys, InvalidRouteConfigError, fileSizeToBytes, UploadThingError, fillInputRouteConfig, bytesToFileSize } from '@uploadthing/shared';\n\nclass FileSizeMismatch extends Data.Error {\n    constructor(type, max, actual){\n        const reason = `You uploaded a ${type} file that was ${bytesToFileSize(actual)}, but the limit for that type is ${max}`;\n        super({\n            reason\n        }), this._tag = \"FileSizeMismatch\", this.name = \"FileSizeMismatchError\";\n    }\n}\nclass FileCountMismatch extends Data.Error {\n    constructor(type, boundtype, bound, actual){\n        const reason = `You uploaded ${actual} file(s) of type '${type}', but the ${boundtype} for that type is ${bound}`;\n        super({\n            reason\n        }), this._tag = \"FileCountMismatch\", this.name = \"FileCountMismatchError\";\n    }\n}\n// Verify that the uploaded files doesn't violate the route config,\n// e.g. uploading more videos than allowed, or a file that is larger than allowed.\n// This is double-checked on infra side, but we want to fail early to avoid network latency.\nconst assertFilesMeetConfig = (files, routeConfig)=>Effect.gen(function*() {\n        const counts = {};\n        for (const file of files){\n            const type = yield* matchFileType(file, objectKeys(routeConfig));\n            counts[type] = (counts[type] ?? 0) + 1;\n            const sizeLimit = routeConfig[type]?.maxFileSize;\n            if (!sizeLimit) {\n                return yield* new InvalidRouteConfigError(type, \"maxFileSize\");\n            }\n            const sizeLimitBytes = yield* fileSizeToBytes(sizeLimit);\n            if (file.size > sizeLimitBytes) {\n                return yield* new FileSizeMismatch(type, sizeLimit, file.size);\n            }\n        }\n        for(const _key in counts){\n            const key = _key;\n            const config = routeConfig[key];\n            if (!config) return yield* new InvalidRouteConfigError(key);\n            const count = counts[key];\n            const min = config.minFileCount;\n            const max = config.maxFileCount;\n            if (min > max) {\n                return yield* new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"Invalid config during file count - minFileCount > maxFileCount\",\n                    cause: `minFileCount must be less than maxFileCount for key ${key}. got: ${min} > ${max}`\n                });\n            }\n            if (count != null && count < min) {\n                return yield* new FileCountMismatch(key, \"minimum\", min, count);\n            }\n            if (count != null && count > max) {\n                return yield* new FileCountMismatch(key, \"maximum\", max, count);\n            }\n        }\n        return null;\n    });\nconst extractRouterConfig = (router)=>Effect.forEach(objectKeys(router), (slug)=>Effect.map(fillInputRouteConfig(router[slug].routerConfig), (config)=>({\n                slug,\n                config\n            })));\n\nexport { assertFilesMeetConfig, extractRouterConfig };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,yBAAyB,6IAAA,CAAA,QAAU;IACrC,YAAY,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC;QAC1B,MAAM,SAAS,CAAC,eAAe,EAAE,KAAK,eAAe,EAAE,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,iCAAiC,EAAE,KAAK;QACvH,KAAK,CAAC;YACF;QACJ,IAAI,IAAI,CAAC,IAAI,GAAG,oBAAoB,IAAI,CAAC,IAAI,GAAG;IACpD;AACJ;AACA,MAAM,0BAA0B,6IAAA,CAAA,QAAU;IACtC,YAAY,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC;QACvC,MAAM,SAAS,CAAC,aAAa,EAAE,OAAO,kBAAkB,EAAE,KAAK,WAAW,EAAE,UAAU,kBAAkB,EAAE,OAAO;QACjH,KAAK,CAAC;YACF;QACJ,IAAI,IAAI,CAAC,IAAI,GAAG,qBAAqB,IAAI,CAAC,IAAI,GAAG;IACrD;AACJ;AACA,mEAAmE;AACnE,kFAAkF;AAClF,4FAA4F;AAC5F,MAAM,wBAAwB,CAAC,OAAO,cAAc,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;QACvD,MAAM,SAAS,CAAC;QAChB,KAAK,MAAM,QAAQ,MAAM;YACrB,MAAM,OAAO,OAAO,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD,EAAE;YACnD,MAAM,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI;YACrC,MAAM,YAAY,WAAW,CAAC,KAAK,EAAE;YACrC,IAAI,CAAC,WAAW;gBACZ,OAAO,OAAO,IAAI,wJAAA,CAAA,0BAAuB,CAAC,MAAM;YACpD;YACA,MAAM,iBAAiB,OAAO,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE;YAC9C,IAAI,KAAK,IAAI,GAAG,gBAAgB;gBAC5B,OAAO,OAAO,IAAI,iBAAiB,MAAM,WAAW,KAAK,IAAI;YACjE;QACJ;QACA,IAAI,MAAM,QAAQ,OAAO;YACrB,MAAM,MAAM;YACZ,MAAM,SAAS,WAAW,CAAC,IAAI;YAC/B,IAAI,CAAC,QAAQ,OAAO,OAAO,IAAI,wJAAA,CAAA,0BAAuB,CAAC;YACvD,MAAM,QAAQ,MAAM,CAAC,IAAI;YACzB,MAAM,MAAM,OAAO,YAAY;YAC/B,MAAM,MAAM,OAAO,YAAY;YAC/B,IAAI,MAAM,KAAK;gBACX,OAAO,OAAO,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBAC/B,MAAM;oBACN,SAAS;oBACT,OAAO,CAAC,oDAAoD,EAAE,IAAI,OAAO,EAAE,IAAI,GAAG,EAAE,KAAK;gBAC7F;YACJ;YACA,IAAI,SAAS,QAAQ,QAAQ,KAAK;gBAC9B,OAAO,OAAO,IAAI,kBAAkB,KAAK,WAAW,KAAK;YAC7D;YACA,IAAI,SAAS,QAAQ,QAAQ,KAAK;gBAC9B,OAAO,OAAO,IAAI,kBAAkB,KAAK,WAAW,KAAK;YAC7D;QACJ;QACA,OAAO;IACX;AACJ,MAAM,sBAAsB,CAAC,SAAS,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD,EAAE,SAAS,CAAC,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAA,GAAA,wJAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,SAAS,CAAC;gBACxI;gBACA;YACJ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/runtime.js"], "sourcesContent": ["import { FetchHttpClient, Headers } from '@effect/platform';\nimport * as FiberRef from 'effect/FiberRef';\nimport * as Layer from 'effect/Layer';\nimport * as ManagedRuntime from 'effect/ManagedRuntime';\nimport { configProvider } from './config.js';\nimport { withLogFormat, withMinimalLogLevel } from './logger.js';\n\nconst makeRuntime = (fetch, config)=>{\n    const fetchHttpClient = Layer.provideMerge(FetchHttpClient.layer, Layer.succeed(FetchHttpClient.Fetch, fetch));\n    const withRedactedHeaders = Layer.effectDiscard(FiberRef.update(Headers.currentRedactedNames, (_)=>_.concat([\n            \"x-uploadthing-api-key\"\n        ])));\n    const layer = Layer.provide(Layer.mergeAll(withLogFormat, withMinimalLogLevel, fetchHttpClient, withRedactedHeaders), Layer.setConfigProvider(configProvider(config)));\n    return ManagedRuntime.make(layer);\n};\n\nexport { makeRuntime };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,cAAc,CAAC,OAAO;IACxB,MAAM,kBAAkB,CAAA,GAAA,8IAAA,CAAA,eAAkB,AAAD,EAAE,iNAAA,CAAA,kBAAe,CAAC,KAAK,EAAE,CAAA,GAAA,8IAAA,CAAA,UAAa,AAAD,EAAE,iNAAA,CAAA,kBAAe,CAAC,KAAK,EAAE;IACvG,MAAM,sBAAsB,CAAA,GAAA,8IAAA,CAAA,gBAAmB,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,SAAe,AAAD,EAAE,iMAAA,CAAA,UAAO,CAAC,oBAAoB,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;YACpG;SACH;IACL,MAAM,QAAQ,CAAA,GAAA,8IAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAc,AAAD,EAAE,0JAAA,CAAA,gBAAa,EAAE,0JAAA,CAAA,sBAAmB,EAAE,iBAAiB,sBAAsB,CAAA,GAAA,8IAAA,CAAA,oBAAuB,AAAD,EAAE,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD,EAAE;IAC7J,OAAO,CAAA,GAAA,uJAAA,CAAA,OAAmB,AAAD,EAAE;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/types.js"], "sourcesContent": ["/**\n * Marker used to select the region based on the incoming request\n */ const UTRegion = Symbol(\"uploadthing-region-symbol\");\n/**\n * Marker used to append a `customId` to the incoming file data in `.middleware()`\n * @example\n * ```ts\n * .middleware((opts) => {\n *   return {\n *     [UTFiles]: opts.files.map((file) => ({\n *       ...file,\n *       customId: generateId(),\n *     }))\n *   };\n * })\n * ```\n */ const UTFiles = Symbol(\"uploadthing-custom-id-symbol\");\n\nexport { UTFiles, UTRegion };\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAAG,MAAM,WAAW,OAAO;AAC5B;;;;;;;;;;;;;CAaC,GAAG,MAAM,UAAU,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/handler.js"], "sourcesContent": ["import { HttpApp, HttpServerResponse, HttpServerRequest, HttpRouter, HttpClient, HttpClientRequest, HttpClientResponse, HttpBody } from '@effect/platform';\nimport * as Config from 'effect/Config';\nimport * as Context from 'effect/Context';\nimport * as Effect from 'effect/Effect';\nimport * as Match from 'effect/Match';\nimport * as Redacted from 'effect/Redacted';\nimport * as Schema from 'effect/Schema';\nimport { UploadThingError, getStatusCodeFromError, verifySignature, fillInputRouteConfig, matchFileType, objectKeys, generateKey, generateSignedURL } from '@uploadthing/shared';\nimport { IsDevelopment, UTToken, IngestUrl } from './config.js';\nimport { logDeprecationWarning } from './deprecations.js';\nimport { formatError } from './error-formatter.js';\nimport { handleJsonLineStream } from './jsonl.js';\nimport { logHttpClientError, logHttpClientResponse } from './logger.js';\nimport { getParseFn } from './parser.js';\nimport { extractRouterConfig, assertFilesMeetConfig } from './route-config.js';\nimport { makeRuntime } from './runtime.js';\nimport { UploadThingHook, ActionType, UploadedFileData, CallbackResultResponse, UploadActionPayload, MetadataFetchResponse, MetadataFetchStreamPart } from './shared-schemas.js';\nimport { UTFiles, UTRegion } from './types.js';\n\nvar version = \"7.7.2\";\n\nclass AdapterArguments extends Context.Tag(\"uploadthing/AdapterArguments\")() {\n}\n/**\n * Create a request handler adapter for any framework or server library.\n * Refer to the existing adapters for examples on how to use this function.\n * @public\n *\n * @param makeAdapterArgs - Function that takes the args from your framework and returns an Effect that resolves to the adapter args.\n * These args are passed to the `.middleware`, `.onUploadComplete`, and `.onUploadError` hooks.\n * @param toRequest - Function that takes the args from your framework and returns an Effect that resolves to a web Request object.\n * @param opts - The router config and other options that are normally passed to `createRequestHandler` of official adapters\n * @param beAdapter - [Optional] The adapter name of the adapter, used for telemetry purposes\n * @returns A function that takes the args from your framework and returns a promise that resolves to a Response object.\n */ const makeAdapterHandler = (makeAdapterArgs, toRequest, opts, beAdapter)=>{\n    const managed = makeRuntime(opts.config?.fetch, opts.config);\n    const handle = Effect.promise(()=>managed.runtime().then(HttpApp.toWebHandlerRuntime));\n    const app = (...args)=>Effect.map(Effect.promise(()=>managed.runPromise(createRequestHandler(opts, beAdapter ?? \"custom\"))), Effect.provideServiceEffect(AdapterArguments, makeAdapterArgs(...args)));\n    return async (...args)=>{\n        const result = await handle.pipe(Effect.ap(app(...args)), Effect.ap(toRequest(...args)), Effect.withLogSpan(\"requestHandler\"), managed.runPromise);\n        return result;\n    };\n};\nconst createRequestHandler = (opts, beAdapter)=>Effect.gen(function*() {\n        const isDevelopment = yield* IsDevelopment;\n        const routerConfig = yield* extractRouterConfig(opts.router);\n        const handleDaemon = (()=>{\n            if (opts.config?.handleDaemonPromise) {\n                return opts.config.handleDaemonPromise;\n            }\n            return isDevelopment ? \"void\" : \"await\";\n        })();\n        if (isDevelopment && handleDaemon === \"await\") {\n            return yield* new UploadThingError({\n                code: \"INVALID_SERVER_CONFIG\",\n                message: 'handleDaemonPromise: \"await\" is forbidden in development.'\n            });\n        }\n        const GET = Effect.gen(function*() {\n            return yield* HttpServerResponse.json(routerConfig);\n        });\n        const POST = Effect.gen(function*() {\n            const { \"uploadthing-hook\": uploadthingHook, \"x-uploadthing-package\": fePackage, \"x-uploadthing-version\": clientVersion } = yield* HttpServerRequest.schemaHeaders(Schema.Struct({\n                \"uploadthing-hook\": UploadThingHook.pipe(Schema.optional),\n                \"x-uploadthing-package\": Schema.String.pipe(Schema.optionalWith({\n                    default: ()=>\"unknown\"\n                })),\n                \"x-uploadthing-version\": Schema.String.pipe(Schema.optionalWith({\n                    default: ()=>version\n                }))\n            }));\n            if (clientVersion !== version) {\n                const serverVersion = version;\n                yield* Effect.logWarning(\"Client version mismatch. Things may not work as expected, please sync your versions to ensure compatibility.\").pipe(Effect.annotateLogs({\n                    clientVersion,\n                    serverVersion\n                }));\n            }\n            const { slug, actionType } = yield* HttpRouter.schemaParams(Schema.Struct({\n                actionType: ActionType.pipe(Schema.optional),\n                slug: Schema.String\n            }));\n            const uploadable = opts.router[slug];\n            if (!uploadable) {\n                const msg = `No file route found for slug ${slug}`;\n                yield* Effect.logError(msg);\n                return yield* new UploadThingError({\n                    code: \"NOT_FOUND\",\n                    message: msg\n                });\n            }\n            const { body, fiber } = yield* Match.value({\n                actionType,\n                uploadthingHook\n            }).pipe(Match.when({\n                actionType: \"upload\",\n                uploadthingHook: undefined\n            }, ()=>handleUploadAction({\n                    uploadable,\n                    fePackage,\n                    beAdapter,\n                    slug\n                })), Match.when({\n                actionType: undefined,\n                uploadthingHook: \"callback\"\n            }, ()=>handleCallbackRequest({\n                    uploadable,\n                    fePackage,\n                    beAdapter\n                })), Match.when({\n                actionType: undefined,\n                uploadthingHook: \"error\"\n            }, ()=>handleErrorRequest({\n                    uploadable\n                })), Match.orElse(()=>Effect.succeed({\n                    body: null,\n                    fiber: null\n                })));\n            if (fiber) {\n                yield* Effect.logDebug(\"Running fiber as daemon\").pipe(Effect.annotateLogs(\"handleDaemon\", handleDaemon));\n                if (handleDaemon === \"void\") ; else if (handleDaemon === \"await\") {\n                    yield* fiber.await;\n                } else if (typeof handleDaemon === \"function\") {\n                    handleDaemon(Effect.runPromise(fiber.await));\n                }\n            }\n            yield* Effect.logDebug(\"Sending response\").pipe(Effect.annotateLogs(\"body\", body));\n            return yield* HttpServerResponse.json(body);\n        }).pipe(Effect.catchTags({\n            ParseError: (e)=>HttpServerResponse.json(formatError(new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"Invalid input\",\n                    cause: e.message\n                }), opts.router), {\n                    status: 400\n                }),\n            UploadThingError: (e)=>// eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n                HttpServerResponse.json(formatError(e, opts.router), {\n                    status: getStatusCodeFromError(e)\n                })\n        }));\n        const appendResponseHeaders = Effect.map(HttpServerResponse.setHeader(\"x-uploadthing-version\", version));\n        return HttpRouter.empty.pipe(HttpRouter.get(\"*\", GET), HttpRouter.post(\"*\", POST), HttpRouter.use(appendResponseHeaders));\n    }).pipe(Effect.withLogSpan(\"createRequestHandler\"));\nconst handleErrorRequest = (opts)=>Effect.gen(function*() {\n        const { uploadable } = opts;\n        const request = yield* HttpServerRequest.HttpServerRequest;\n        const { apiKey } = yield* UTToken;\n        const verified = yield* verifySignature((yield* request.text), request.headers[\"x-uploadthing-signature\"] ?? null, apiKey);\n        yield* Effect.logDebug(`Signature verified: ${verified}`);\n        if (!verified) {\n            yield* Effect.logError(\"Invalid signature\");\n            return yield* new UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Invalid signature\"\n            });\n        }\n        const requestInput = yield* HttpServerRequest.schemaBodyJson(Schema.Struct({\n            fileKey: Schema.String,\n            error: Schema.String\n        }));\n        yield* Effect.logDebug(\"Handling error callback request with input:\").pipe(Effect.annotateLogs(\"json\", requestInput));\n        const adapterArgs = yield* AdapterArguments;\n        const fiber = yield* Effect.tryPromise({\n            try: async ()=>uploadable.onUploadError({\n                    ...adapterArgs,\n                    error: new UploadThingError({\n                        code: \"UPLOAD_FAILED\",\n                        message: `Upload failed for ${requestInput.fileKey}: ${requestInput.error}`\n                    }),\n                    fileKey: requestInput.fileKey\n                }),\n            catch: (error)=>new UploadThingError({\n                    code: \"INTERNAL_SERVER_ERROR\",\n                    message: \"Failed to run onUploadError\",\n                    cause: error\n                })\n        }).pipe(Effect.tapError((error)=>Effect.logError(\"Failed to run onUploadError. You probably shouldn't be throwing errors here.\").pipe(Effect.annotateLogs(\"error\", error)))).pipe(Effect.ignoreLogged, Effect.forkDaemon);\n        return {\n            body: null,\n            fiber\n        };\n    }).pipe(Effect.withLogSpan(\"handleErrorRequest\"));\nconst handleCallbackRequest = (opts)=>Effect.gen(function*() {\n        const { uploadable, fePackage, beAdapter } = opts;\n        const request = yield* HttpServerRequest.HttpServerRequest;\n        const { apiKey } = yield* UTToken;\n        const verified = yield* verifySignature((yield* request.text), request.headers[\"x-uploadthing-signature\"] ?? null, apiKey);\n        yield* Effect.logDebug(`Signature verified: ${verified}`);\n        if (!verified) {\n            yield* Effect.logError(\"Invalid signature\");\n            return yield* new UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Invalid signature\"\n            });\n        }\n        const requestInput = yield* HttpServerRequest.schemaBodyJson(Schema.Struct({\n            status: Schema.String,\n            file: UploadedFileData,\n            origin: Schema.String,\n            metadata: Schema.Record({\n                key: Schema.String,\n                value: Schema.Unknown\n            })\n        }));\n        yield* Effect.logDebug(\"Handling callback request with input:\").pipe(Effect.annotateLogs(\"json\", requestInput));\n        /**\n     * Run `.onUploadComplete` as a daemon to prevent the\n     * request from UT to potentially timeout.\n     */ const fiber = yield* Effect.gen(function*() {\n            const adapterArgs = yield* AdapterArguments;\n            const serverData = yield* Effect.tryPromise({\n                try: async ()=>uploadable.onUploadComplete({\n                        ...adapterArgs,\n                        file: {\n                            ...requestInput.file,\n                            get url () {\n                                logDeprecationWarning(\"`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                                return requestInput.file.url;\n                            },\n                            get appUrl () {\n                                logDeprecationWarning(\"`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                                return requestInput.file.appUrl;\n                            }\n                        },\n                        metadata: requestInput.metadata\n                    }),\n                catch: (error)=>new UploadThingError({\n                        code: \"INTERNAL_SERVER_ERROR\",\n                        message: \"Failed to run onUploadComplete. You probably shouldn't be throwing errors here.\",\n                        cause: error\n                    })\n            });\n            const payload = {\n                fileKey: requestInput.file.key,\n                callbackData: serverData ?? null\n            };\n            yield* Effect.logDebug(\"'onUploadComplete' callback finished. Sending response to UploadThing:\").pipe(Effect.annotateLogs(\"callbackData\", payload));\n            const httpClient = (yield* HttpClient.HttpClient).pipe(HttpClient.filterStatusOk);\n            yield* HttpClientRequest.post(`/callback-result`).pipe(HttpClientRequest.prependUrl(requestInput.origin), HttpClientRequest.setHeaders({\n                \"x-uploadthing-api-key\": Redacted.value(apiKey),\n                \"x-uploadthing-version\": version,\n                \"x-uploadthing-be-adapter\": beAdapter,\n                \"x-uploadthing-fe-package\": fePackage\n            }), HttpClientRequest.bodyJson(payload), Effect.flatMap(httpClient.execute), Effect.tapError(logHttpClientError(\"Failed to register callback result\")), Effect.flatMap(HttpClientResponse.schemaBodyJson(CallbackResultResponse)), Effect.tap(Effect.log(\"Sent callback result to UploadThing\")), Effect.scoped);\n        }).pipe(Effect.ignoreLogged, Effect.forkDaemon);\n        return {\n            body: null,\n            fiber\n        };\n    }).pipe(Effect.withLogSpan(\"handleCallbackRequest\"));\nconst runRouteMiddleware = (opts)=>Effect.gen(function*() {\n        const { json: { files, input }, uploadable } = opts;\n        yield* Effect.logDebug(\"Running middleware\");\n        const adapterArgs = yield* AdapterArguments;\n        const metadata = yield* Effect.tryPromise({\n            try: async ()=>uploadable.middleware({\n                    ...adapterArgs,\n                    input,\n                    files\n                }),\n            catch: (error)=>error instanceof UploadThingError ? error : new UploadThingError({\n                    code: \"INTERNAL_SERVER_ERROR\",\n                    message: \"Failed to run middleware\",\n                    cause: error\n                })\n        });\n        if (metadata[UTFiles] && metadata[UTFiles].length !== files.length) {\n            const msg = `Expected files override to have the same length as original files, got ${metadata[UTFiles].length} but expected ${files.length}`;\n            yield* Effect.logError(msg);\n            return yield* new UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Files override must have the same length as files\",\n                cause: msg\n            });\n        }\n        // Attach customIds from middleware to the files\n        const filesWithCustomIds = yield* Effect.forEach(files, (file, idx)=>Effect.gen(function*() {\n                const theirs = metadata[UTFiles]?.[idx];\n                if (theirs && theirs.size !== file.size) {\n                    yield* Effect.logWarning(\"File size mismatch. Reverting to original size\");\n                }\n                return {\n                    name: theirs?.name ?? file.name,\n                    size: file.size,\n                    type: file.type,\n                    customId: theirs?.customId,\n                    lastModified: theirs?.lastModified ?? Date.now()\n                };\n            }));\n        return {\n            metadata,\n            filesWithCustomIds,\n            preferredRegion: metadata[UTRegion]\n        };\n    }).pipe(Effect.withLogSpan(\"runRouteMiddleware\"));\nconst handleUploadAction = (opts)=>Effect.gen(function*() {\n        const httpClient = (yield* HttpClient.HttpClient).pipe(HttpClient.filterStatusOk);\n        const { uploadable, fePackage, beAdapter, slug } = opts;\n        const json = yield* HttpServerRequest.schemaBodyJson(UploadActionPayload);\n        yield* Effect.logDebug(\"Handling upload request\").pipe(Effect.annotateLogs(\"json\", json));\n        // validate the input\n        yield* Effect.logDebug(\"Parsing user input\");\n        const parsedInput = yield* Effect.tryPromise({\n            try: ()=>getParseFn(uploadable.inputParser)(json.input),\n            catch: (error)=>new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"Invalid input\",\n                    cause: error\n                })\n        });\n        yield* Effect.logDebug(\"Input parsed successfully\").pipe(Effect.annotateLogs(\"input\", parsedInput));\n        const { metadata, filesWithCustomIds, preferredRegion } = yield* runRouteMiddleware({\n            json: {\n                input: parsedInput,\n                files: json.files\n            },\n            uploadable\n        });\n        yield* Effect.logDebug(\"Parsing route config\").pipe(Effect.annotateLogs(\"routerConfig\", uploadable.routerConfig));\n        const parsedConfig = yield* fillInputRouteConfig(uploadable.routerConfig).pipe(Effect.catchTag(\"InvalidRouteConfig\", (err)=>new UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Invalid route config\",\n                cause: err\n            })));\n        yield* Effect.logDebug(\"Route config parsed successfully\").pipe(Effect.annotateLogs(\"routeConfig\", parsedConfig));\n        yield* Effect.logDebug(\"Validating files meet the config requirements\").pipe(Effect.annotateLogs(\"files\", json.files));\n        yield* assertFilesMeetConfig(json.files, parsedConfig).pipe(Effect.mapError((e)=>new UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: `Invalid config: ${e._tag}`,\n                cause: \"reason\" in e ? e.reason : e.message\n            })));\n        yield* Effect.logDebug(\"Files validated.\");\n        const fileUploadRequests = yield* Effect.forEach(filesWithCustomIds, (file)=>Effect.map(matchFileType(file, objectKeys(parsedConfig)), (type)=>({\n                    name: file.name,\n                    size: file.size,\n                    type: file.type || type,\n                    lastModified: file.lastModified,\n                    customId: file.customId,\n                    contentDisposition: parsedConfig[type]?.contentDisposition ?? \"inline\",\n                    acl: parsedConfig[type]?.acl\n                }))).pipe(Effect.catchTags({\n            /** Shouldn't happen since config is validated above so just dying is fine I think */ InvalidFileType: (e)=>Effect.die(e),\n            UnknownFileType: (e)=>Effect.die(e)\n        }));\n        const routeOptions = uploadable.routeOptions;\n        const { apiKey, appId } = yield* UTToken;\n        const ingestUrl = yield* IngestUrl(preferredRegion);\n        const isDev = yield* IsDevelopment;\n        yield* Effect.logDebug(\"Generating presigned URLs\").pipe(Effect.annotateLogs(\"fileUploadRequests\", fileUploadRequests), Effect.annotateLogs(\"ingestUrl\", ingestUrl));\n        const presignedUrls = yield* Effect.forEach(fileUploadRequests, (file)=>Effect.gen(function*() {\n                const key = yield* generateKey(file, appId, routeOptions.getFileHashParts);\n                const url = yield* generateSignedURL(`${ingestUrl}/${key}`, apiKey, {\n                    ttlInSeconds: routeOptions.presignedURLTTL,\n                    data: {\n                        \"x-ut-identifier\": appId,\n                        \"x-ut-file-name\": file.name,\n                        \"x-ut-file-size\": file.size,\n                        \"x-ut-file-type\": file.type,\n                        \"x-ut-slug\": slug,\n                        \"x-ut-custom-id\": file.customId,\n                        \"x-ut-content-disposition\": file.contentDisposition,\n                        \"x-ut-acl\": file.acl\n                    }\n                });\n                return {\n                    url,\n                    key\n                };\n            }), {\n            concurrency: \"unbounded\"\n        });\n        const serverReq = yield* HttpServerRequest.HttpServerRequest;\n        const requestUrl = yield* HttpServerRequest.toURL(serverReq);\n        const devHookRequest = yield* Config.string(\"callbackUrl\").pipe(Config.withDefault(requestUrl.origin + requestUrl.pathname), Effect.map((url)=>HttpClientRequest.post(url).pipe(HttpClientRequest.appendUrlParam(\"slug\", slug))));\n        const metadataRequest = HttpClientRequest.post(\"/route-metadata\").pipe(HttpClientRequest.prependUrl(ingestUrl), HttpClientRequest.setHeaders({\n            \"x-uploadthing-api-key\": Redacted.value(apiKey),\n            \"x-uploadthing-version\": version,\n            \"x-uploadthing-be-adapter\": beAdapter,\n            \"x-uploadthing-fe-package\": fePackage\n        }), HttpClientRequest.bodyJson({\n            fileKeys: presignedUrls.map(({ key })=>key),\n            metadata: metadata,\n            isDev,\n            callbackUrl: devHookRequest.url,\n            callbackSlug: slug,\n            awaitServerData: routeOptions.awaitServerData ?? true\n        }), Effect.flatMap(httpClient.execute));\n        const handleDevStreamError = Effect.fn(\"handleDevStreamError\")(function*(err, chunk) {\n            const schema = Schema.parseJson(Schema.Struct({\n                file: UploadedFileData\n            }));\n            const parsedChunk = yield* Schema.decodeUnknown(schema)(chunk);\n            const key = parsedChunk.file.key;\n            yield* Effect.logError(\"Failed to forward callback request from dev stream\").pipe(Effect.annotateLogs({\n                fileKey: key,\n                error: err.message\n            }));\n            const httpResponse = yield* HttpClientRequest.post(\"/callback-result\").pipe(HttpClientRequest.prependUrl(ingestUrl), HttpClientRequest.setHeaders({\n                \"x-uploadthing-api-key\": Redacted.value(apiKey),\n                \"x-uploadthing-version\": version,\n                \"x-uploadthing-be-adapter\": beAdapter,\n                \"x-uploadthing-fe-package\": fePackage\n            }), HttpClientRequest.bodyJson({\n                fileKey: key,\n                error: `Failed to forward callback request from dev stream: ${err.message}`\n            }), Effect.flatMap(httpClient.execute));\n            yield* logHttpClientResponse(\"Reported callback error to UploadThing\")(httpResponse);\n        });\n        // Send metadata to UT server (non blocking as a daemon)\n        // In dev, keep the stream open and simulate the callback requests as\n        // files complete uploading\n        const fiber = yield* Effect.if(isDev, {\n            onTrue: ()=>metadataRequest.pipe(Effect.tapBoth({\n                    onSuccess: logHttpClientResponse(\"Registered metadata\", {\n                        mixin: \"None\"\n                    }),\n                    onFailure: logHttpClientError(\"Failed to register metadata\")\n                }), HttpClientResponse.stream, handleJsonLineStream(MetadataFetchStreamPart, (chunk)=>devHookRequest.pipe(HttpClientRequest.setHeaders({\n                        \"uploadthing-hook\": chunk.hook,\n                        \"x-uploadthing-signature\": chunk.signature\n                    }), HttpClientRequest.setBody(HttpBody.text(chunk.payload, \"application/json\")), httpClient.execute, Effect.tap(logHttpClientResponse(\"Successfully forwarded callback request from dev stream\")), Effect.catchTag(\"ResponseError\", (err)=>handleDevStreamError(err, chunk.payload)), Effect.annotateLogs(chunk), Effect.asVoid, Effect.ignoreLogged, Effect.scoped))),\n            onFalse: ()=>metadataRequest.pipe(Effect.tapBoth({\n                    onSuccess: logHttpClientResponse(\"Registered metadata\"),\n                    onFailure: logHttpClientError(\"Failed to register metadata\")\n                }), Effect.flatMap(HttpClientResponse.schemaBodyJson(MetadataFetchResponse)), Effect.scoped)\n        }).pipe(Effect.forkDaemon);\n        const presigneds = presignedUrls.map((p, i)=>({\n                url: p.url,\n                key: p.key,\n                name: fileUploadRequests[i].name,\n                customId: fileUploadRequests[i].customId ?? null\n            }));\n        yield* Effect.logInfo(\"Sending presigned URLs to client\").pipe(Effect.annotateLogs(\"presignedUrls\", presigneds));\n        return {\n            body: presigneds,\n            fiber\n        };\n    }).pipe(Effect.withLogSpan(\"handleUploadAction\"));\n\nexport { AdapterArguments, createRequestHandler, makeAdapterHandler };\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAEA,IAAI,UAAU;AAEd,MAAM,yBAAyB,CAAA,GAAA,gJAAA,CAAA,MAAW,AAAD,EAAE;AAC3C;AACA;;;;;;;;;;;CAWC,GAAG,MAAM,qBAAqB,CAAC,iBAAiB,WAAW,MAAM;IAC9D,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,KAAK,MAAM,EAAE,OAAO,KAAK,MAAM;IAC3D,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,IAAI,QAAQ,OAAO,GAAG,IAAI,CAAC,iMAAA,CAAA,UAAO,CAAC,mBAAmB;IACpF,MAAM,MAAM,CAAC,GAAG,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,IAAI,QAAQ,UAAU,CAAC,qBAAqB,MAAM,aAAa,aAAa,CAAA,GAAA,+IAAA,CAAA,uBAA2B,AAAD,EAAE,kBAAkB,mBAAmB;IAC9L,OAAO,OAAO,GAAG;QACb,MAAM,SAAS,MAAM,OAAO,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,KAAS,AAAD,EAAE,OAAO,QAAQ,CAAA,GAAA,+IAAA,CAAA,KAAS,AAAD,EAAE,aAAa,QAAQ,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE,mBAAmB,QAAQ,UAAU;QACjJ,OAAO;IACX;AACJ;AACA,MAAM,uBAAuB,CAAC,MAAM,YAAY,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;QACnD,MAAM,gBAAgB,OAAO,0JAAA,CAAA,gBAAa;QAC1C,MAAM,eAAe,OAAO,CAAA,GAAA,mKAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,MAAM;QAC3D,MAAM,eAAe,CAAC;YAClB,IAAI,KAAK,MAAM,EAAE,qBAAqB;gBAClC,OAAO,KAAK,MAAM,CAAC,mBAAmB;YAC1C;YACA,OAAO,gBAAgB,SAAS;QACpC,CAAC;QACD,IAAI,iBAAiB,iBAAiB,SAAS;YAC3C,OAAO,OAAO,IAAI,wJAAA,CAAA,mBAAgB,CAAC;gBAC/B,MAAM;gBACN,SAAS;YACb;QACJ;QACA,MAAM,MAAM,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;YACnB,OAAO,OAAO,uNAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC;QAC1C;QACA,MAAM,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;YACpB,MAAM,EAAE,oBAAoB,eAAe,EAAE,yBAAyB,SAAS,EAAE,yBAAyB,aAAa,EAAE,GAAG,OAAO,qNAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,CAAA,GAAA,+JAAA,CAAA,SAAa,AAAD,EAAE;gBAC7K,oBAAoB,qKAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,+JAAA,CAAA,WAAe;gBACxD,yBAAyB,+JAAA,CAAA,SAAa,CAAC,IAAI,CAAC,CAAA,GAAA,+JAAA,CAAA,eAAmB,AAAD,EAAE;oBAC5D,SAAS,IAAI;gBACjB;gBACA,yBAAyB,+JAAA,CAAA,SAAa,CAAC,IAAI,CAAC,CAAA,GAAA,+JAAA,CAAA,eAAmB,AAAD,EAAE;oBAC5D,SAAS,IAAI;gBACjB;YACJ;YACA,IAAI,kBAAkB,SAAS;gBAC3B,MAAM,gBAAgB;gBACtB,OAAO,CAAA,GAAA,+IAAA,CAAA,aAAiB,AAAD,EAAE,gHAAgH,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE;oBAC9J;oBACA;gBACJ;YACJ;YACA,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,uMAAA,CAAA,aAAU,CAAC,YAAY,CAAC,CAAA,GAAA,+JAAA,CAAA,SAAa,AAAD,EAAE;gBACtE,YAAY,qKAAA,CAAA,aAAU,CAAC,IAAI,CAAC,+JAAA,CAAA,WAAe;gBAC3C,MAAM,+JAAA,CAAA,SAAa;YACvB;YACA,MAAM,aAAa,KAAK,MAAM,CAAC,KAAK;YACpC,IAAI,CAAC,YAAY;gBACb,MAAM,MAAM,CAAC,6BAA6B,EAAE,MAAM;gBAClD,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE;gBACvB,OAAO,OAAO,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBAC/B,MAAM;oBACN,SAAS;gBACb;YACJ;YACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAA,GAAA,8IAAA,CAAA,QAAW,AAAD,EAAE;gBACvC;gBACA;YACJ,GAAG,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,OAAU,AAAD,EAAE;gBACf,YAAY;gBACZ,iBAAiB;YACrB,GAAG,IAAI,mBAAmB;oBAClB;oBACA;oBACA;oBACA;gBACJ,KAAK,CAAA,GAAA,8IAAA,CAAA,OAAU,AAAD,EAAE;gBAChB,YAAY;gBACZ,iBAAiB;YACrB,GAAG,IAAI,sBAAsB;oBACrB;oBACA;oBACA;gBACJ,KAAK,CAAA,GAAA,8IAAA,CAAA,OAAU,AAAD,EAAE;gBAChB,YAAY;gBACZ,iBAAiB;YACrB,GAAG,IAAI,mBAAmB;oBAClB;gBACJ,KAAK,CAAA,GAAA,8IAAA,CAAA,SAAY,AAAD,EAAE,IAAI,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE;oBACjC,MAAM;oBACN,OAAO;gBACX;YACJ,IAAI,OAAO;gBACP,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,2BAA2B,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,gBAAgB;gBAC3F,IAAI,iBAAiB;qBAAe,IAAI,iBAAiB,SAAS;oBAC9D,OAAO,MAAM,KAAK;gBACtB,OAAO,IAAI,OAAO,iBAAiB,YAAY;oBAC3C,aAAa,CAAA,GAAA,+IAAA,CAAA,aAAiB,AAAD,EAAE,MAAM,KAAK;gBAC9C;YACJ;YACA,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,oBAAoB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,QAAQ;YAC5E,OAAO,OAAO,uNAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC;QAC1C,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,YAAgB,AAAD,EAAE;YACrB,YAAY,CAAC,IAAI,uNAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBAClE,MAAM;oBACN,SAAS;oBACT,OAAO,EAAE,OAAO;gBACpB,IAAI,KAAK,MAAM,GAAG;oBACd,QAAQ;gBACZ;YACJ,kBAAkB,CAAC,IACf,uNAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE,GAAG,KAAK,MAAM,GAAG;oBACjD,QAAQ,CAAA,GAAA,wJAAA,CAAA,yBAAsB,AAAD,EAAE;gBACnC;QACR;QACA,MAAM,wBAAwB,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,uNAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,yBAAyB;QAC/F,OAAO,uMAAA,CAAA,aAAU,CAAC,KAAK,CAAC,IAAI,CAAC,uMAAA,CAAA,aAAU,CAAC,GAAG,CAAC,KAAK,MAAM,uMAAA,CAAA,aAAU,CAAC,IAAI,CAAC,KAAK,OAAO,uMAAA,CAAA,aAAU,CAAC,GAAG,CAAC;IACtG,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;AAC/B,MAAM,qBAAqB,CAAC,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;QACtC,MAAM,EAAE,UAAU,EAAE,GAAG;QACvB,MAAM,UAAU,OAAO,qNAAA,CAAA,oBAAiB,CAAC,iBAAiB;QAC1D,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,0JAAA,CAAA,UAAO;QACjC,MAAM,WAAW,OAAO,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE,CAAC,OAAO,QAAQ,IAAI,GAAG,QAAQ,OAAO,CAAC,0BAA0B,IAAI,MAAM;QACnH,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,CAAC,oBAAoB,EAAE,UAAU;QACxD,IAAI,CAAC,UAAU;YACX,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE;YACvB,OAAO,OAAO,IAAI,wJAAA,CAAA,mBAAgB,CAAC;gBAC/B,MAAM;gBACN,SAAS;YACb;QACJ;QACA,MAAM,eAAe,OAAO,qNAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,CAAA,GAAA,+JAAA,CAAA,SAAa,AAAD,EAAE;YACvE,SAAS,+JAAA,CAAA,SAAa;YACtB,OAAO,+JAAA,CAAA,SAAa;QACxB;QACA,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,+CAA+C,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,QAAQ;QACvG,MAAM,cAAc,OAAO;QAC3B,MAAM,QAAQ,OAAO,CAAA,GAAA,+IAAA,CAAA,aAAiB,AAAD,EAAE;YACnC,KAAK,UAAU,WAAW,aAAa,CAAC;oBAChC,GAAG,WAAW;oBACd,OAAO,IAAI,wJAAA,CAAA,mBAAgB,CAAC;wBACxB,MAAM;wBACN,SAAS,CAAC,kBAAkB,EAAE,aAAa,OAAO,CAAC,EAAE,EAAE,aAAa,KAAK,EAAE;oBAC/E;oBACA,SAAS,aAAa,OAAO;gBACjC;YACJ,OAAO,CAAC,QAAQ,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBAC7B,MAAM;oBACN,SAAS;oBACT,OAAO;gBACX;QACR,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,CAAC,QAAQ,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,gFAAgF,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,SAAS,UAAU,IAAI,CAAC,+IAAA,CAAA,eAAmB,EAAE,+IAAA,CAAA,aAAiB;QACxN,OAAO;YACH,MAAM;YACN;QACJ;IACJ,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;AAC/B,MAAM,wBAAwB,CAAC,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;QACzC,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;QAC7C,MAAM,UAAU,OAAO,qNAAA,CAAA,oBAAiB,CAAC,iBAAiB;QAC1D,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,0JAAA,CAAA,UAAO;QACjC,MAAM,WAAW,OAAO,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE,CAAC,OAAO,QAAQ,IAAI,GAAG,QAAQ,OAAO,CAAC,0BAA0B,IAAI,MAAM;QACnH,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,CAAC,oBAAoB,EAAE,UAAU;QACxD,IAAI,CAAC,UAAU;YACX,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE;YACvB,OAAO,OAAO,IAAI,wJAAA,CAAA,mBAAgB,CAAC;gBAC/B,MAAM;gBACN,SAAS;YACb;QACJ;QACA,MAAM,eAAe,OAAO,qNAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,CAAA,GAAA,+JAAA,CAAA,SAAa,AAAD,EAAE;YACvE,QAAQ,+JAAA,CAAA,SAAa;YACrB,MAAM,qKAAA,CAAA,mBAAgB;YACtB,QAAQ,+JAAA,CAAA,SAAa;YACrB,UAAU,CAAA,GAAA,+JAAA,CAAA,SAAa,AAAD,EAAE;gBACpB,KAAK,+JAAA,CAAA,SAAa;gBAClB,OAAO,+JAAA,CAAA,UAAc;YACzB;QACJ;QACA,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,yCAAyC,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,QAAQ;QACjG;;;KAGH,GAAG,MAAM,QAAQ,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;YAC5B,MAAM,cAAc,OAAO;YAC3B,MAAM,aAAa,OAAO,CAAA,GAAA,+IAAA,CAAA,aAAiB,AAAD,EAAE;gBACxC,KAAK,UAAU,WAAW,gBAAgB,CAAC;wBACnC,GAAG,WAAW;wBACd,MAAM;4BACF,GAAG,aAAa,IAAI;4BACpB,IAAI,OAAO;gCACP,CAAA,GAAA,gKAAA,CAAA,wBAAqB,AAAD,EAAE;gCACtB,OAAO,aAAa,IAAI,CAAC,GAAG;4BAChC;4BACA,IAAI,UAAU;gCACV,CAAA,GAAA,gKAAA,CAAA,wBAAqB,AAAD,EAAE;gCACtB,OAAO,aAAa,IAAI,CAAC,MAAM;4BACnC;wBACJ;wBACA,UAAU,aAAa,QAAQ;oBACnC;gBACJ,OAAO,CAAC,QAAQ,IAAI,wJAAA,CAAA,mBAAgB,CAAC;wBAC7B,MAAM;wBACN,SAAS;wBACT,OAAO;oBACX;YACR;YACA,MAAM,UAAU;gBACZ,SAAS,aAAa,IAAI,CAAC,GAAG;gBAC9B,cAAc,cAAc;YAChC;YACA,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,0EAA0E,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,gBAAgB;YAC1I,MAAM,aAAa,CAAC,OAAO,uMAAA,CAAA,aAAU,CAAC,UAAU,EAAE,IAAI,CAAC,uMAAA,CAAA,aAAU,CAAC,cAAc;YAChF,OAAO,qNAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE,IAAI,CAAC,qNAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,aAAa,MAAM,GAAG,qNAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC;gBACnI,yBAAyB,CAAA,GAAA,iJAAA,CAAA,QAAc,AAAD,EAAE;gBACxC,yBAAyB;gBACzB,4BAA4B;gBAC5B,4BAA4B;YAChC,IAAI,qNAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,UAAU,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,WAAW,OAAO,GAAG,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD,EAAE,wCAAwC,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,uNAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,qKAAA,CAAA,yBAAsB,IAAI,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,yCAAyC,+IAAA,CAAA,SAAa;QACnT,GAAG,IAAI,CAAC,+IAAA,CAAA,eAAmB,EAAE,+IAAA,CAAA,aAAiB;QAC9C,OAAO;YACH,MAAM;YACN;QACJ;IACJ,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;AAC/B,MAAM,qBAAqB,CAAC,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;QACtC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,GAAG;QAC/C,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE;QACvB,MAAM,cAAc,OAAO;QAC3B,MAAM,WAAW,OAAO,CAAA,GAAA,+IAAA,CAAA,aAAiB,AAAD,EAAE;YACtC,KAAK,UAAU,WAAW,UAAU,CAAC;oBAC7B,GAAG,WAAW;oBACd;oBACA;gBACJ;YACJ,OAAO,CAAC,QAAQ,iBAAiB,wJAAA,CAAA,mBAAgB,GAAG,QAAQ,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBACzE,MAAM;oBACN,SAAS;oBACT,OAAO;gBACX;QACR;QACA,IAAI,QAAQ,CAAC,yJAAA,CAAA,UAAO,CAAC,IAAI,QAAQ,CAAC,yJAAA,CAAA,UAAO,CAAC,CAAC,MAAM,KAAK,MAAM,MAAM,EAAE;YAChE,MAAM,MAAM,CAAC,uEAAuE,EAAE,QAAQ,CAAC,yJAAA,CAAA,UAAO,CAAC,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,MAAM,EAAE;YAC7I,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE;YACvB,OAAO,OAAO,IAAI,wJAAA,CAAA,mBAAgB,CAAC;gBAC/B,MAAM;gBACN,SAAS;gBACT,OAAO;YACX;QACJ;QACA,gDAAgD;QAChD,MAAM,qBAAqB,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,OAAO,CAAC,MAAM,MAAM,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;gBACxE,MAAM,SAAS,QAAQ,CAAC,yJAAA,CAAA,UAAO,CAAC,EAAE,CAAC,IAAI;gBACvC,IAAI,UAAU,OAAO,IAAI,KAAK,KAAK,IAAI,EAAE;oBACrC,OAAO,CAAA,GAAA,+IAAA,CAAA,aAAiB,AAAD,EAAE;gBAC7B;gBACA,OAAO;oBACH,MAAM,QAAQ,QAAQ,KAAK,IAAI;oBAC/B,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,UAAU,QAAQ;oBAClB,cAAc,QAAQ,gBAAgB,KAAK,GAAG;gBAClD;YACJ;QACJ,OAAO;YACH;YACA;YACA,iBAAiB,QAAQ,CAAC,yJAAA,CAAA,WAAQ,CAAC;QACvC;IACJ,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;AAC/B,MAAM,qBAAqB,CAAC,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;QACtC,MAAM,aAAa,CAAC,OAAO,uMAAA,CAAA,aAAU,CAAC,UAAU,EAAE,IAAI,CAAC,uMAAA,CAAA,aAAU,CAAC,cAAc;QAChF,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG;QACnD,MAAM,OAAO,OAAO,qNAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,qKAAA,CAAA,sBAAmB;QACxE,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,2BAA2B,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,QAAQ;QACnF,qBAAqB;QACrB,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE;QACvB,MAAM,cAAc,OAAO,CAAA,GAAA,+IAAA,CAAA,aAAiB,AAAD,EAAE;YACzC,KAAK,IAAI,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,WAAW,EAAE,KAAK,KAAK;YACtD,OAAO,CAAC,QAAQ,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBAC7B,MAAM;oBACN,SAAS;oBACT,OAAO;gBACX;QACR;QACA,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,6BAA6B,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,SAAS;QACtF,MAAM,EAAE,QAAQ,EAAE,kBAAkB,EAAE,eAAe,EAAE,GAAG,OAAO,mBAAmB;YAChF,MAAM;gBACF,OAAO;gBACP,OAAO,KAAK,KAAK;YACrB;YACA;QACJ;QACA,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,wBAAwB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,gBAAgB,WAAW,YAAY;QAC/G,MAAM,eAAe,OAAO,CAAA,GAAA,wJAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,YAAY,EAAE,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,sBAAsB,CAAC,MAAM,IAAI,wJAAA,CAAA,mBAAgB,CAAC;gBACzI,MAAM;gBACN,SAAS;gBACT,OAAO;YACX;QACJ,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,oCAAoC,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,eAAe;QACnG,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,iDAAiD,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,SAAS,KAAK,KAAK;QACpH,OAAO,CAAA,GAAA,mKAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,KAAK,EAAE,cAAc,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,CAAC,IAAI,IAAI,wJAAA,CAAA,mBAAgB,CAAC;gBAC9F,MAAM;gBACN,SAAS,CAAC,gBAAgB,EAAE,EAAE,IAAI,EAAE;gBACpC,OAAO,YAAY,IAAI,EAAE,MAAM,GAAG,EAAE,OAAO;YAC/C;QACJ,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE;QACvB,MAAM,qBAAqB,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,oBAAoB,CAAC,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD,EAAE,gBAAgB,CAAC,OAAO,CAAC;oBACpI,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI,IAAI;oBACnB,cAAc,KAAK,YAAY;oBAC/B,UAAU,KAAK,QAAQ;oBACvB,oBAAoB,YAAY,CAAC,KAAK,EAAE,sBAAsB;oBAC9D,KAAK,YAAY,CAAC,KAAK,EAAE;gBAC7B,CAAC,IAAI,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,YAAgB,AAAD,EAAE;YAC/B,mFAAmF,GAAG,iBAAiB,CAAC,IAAI,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;YACvH,iBAAiB,CAAC,IAAI,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;QACrC;QACA,MAAM,eAAe,WAAW,YAAY;QAC5C,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,0JAAA,CAAA,UAAO;QACxC,MAAM,YAAY,OAAO,CAAA,GAAA,0JAAA,CAAA,YAAS,AAAD,EAAE;QACnC,MAAM,QAAQ,OAAO,0JAAA,CAAA,gBAAa;QAClC,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,6BAA6B,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,sBAAsB,qBAAqB,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,aAAa;QACzJ,MAAM,gBAAgB,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,oBAAoB,CAAC,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;gBAC3E,MAAM,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,MAAM,OAAO,aAAa,gBAAgB;gBACzE,MAAM,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,oBAAiB,AAAD,EAAE,GAAG,UAAU,CAAC,EAAE,KAAK,EAAE,QAAQ;oBAChE,cAAc,aAAa,eAAe;oBAC1C,MAAM;wBACF,mBAAmB;wBACnB,kBAAkB,KAAK,IAAI;wBAC3B,kBAAkB,KAAK,IAAI;wBAC3B,kBAAkB,KAAK,IAAI;wBAC3B,aAAa;wBACb,kBAAkB,KAAK,QAAQ;wBAC/B,4BAA4B,KAAK,kBAAkB;wBACnD,YAAY,KAAK,GAAG;oBACxB;gBACJ;gBACA,OAAO;oBACH;oBACA;gBACJ;YACJ,IAAI;YACJ,aAAa;QACjB;QACA,MAAM,YAAY,OAAO,qNAAA,CAAA,oBAAiB,CAAC,iBAAiB;QAC5D,MAAM,aAAa,OAAO,qNAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC;QAClD,MAAM,iBAAiB,OAAO,CAAA,GAAA,+IAAA,CAAA,SAAa,AAAD,EAAE,eAAe,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE,WAAW,MAAM,GAAG,WAAW,QAAQ,GAAG,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAC,MAAM,qNAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,qNAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,QAAQ;QACzN,MAAM,kBAAkB,qNAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,qNAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,YAAY,qNAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC;YACzI,yBAAyB,CAAA,GAAA,iJAAA,CAAA,QAAc,AAAD,EAAE;YACxC,yBAAyB;YACzB,4BAA4B;YAC5B,4BAA4B;QAChC,IAAI,qNAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC;YAC3B,UAAU,cAAc,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;YACvC,UAAU;YACV;YACA,aAAa,eAAe,GAAG;YAC/B,cAAc;YACd,iBAAiB,aAAa,eAAe,IAAI;QACrD,IAAI,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,WAAW,OAAO;QACrC,MAAM,uBAAuB,CAAA,GAAA,+IAAA,CAAA,KAAS,AAAD,EAAE,wBAAwB,UAAU,GAAG,EAAE,KAAK;YAC/E,MAAM,SAAS,CAAA,GAAA,+JAAA,CAAA,YAAgB,AAAD,EAAE,CAAA,GAAA,+JAAA,CAAA,SAAa,AAAD,EAAE;gBAC1C,MAAM,qKAAA,CAAA,mBAAgB;YAC1B;YACA,MAAM,cAAc,OAAO,CAAA,GAAA,+JAAA,CAAA,gBAAoB,AAAD,EAAE,QAAQ;YACxD,MAAM,MAAM,YAAY,IAAI,CAAC,GAAG;YAChC,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,sDAAsD,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE;gBAClG,SAAS;gBACT,OAAO,IAAI,OAAO;YACtB;YACA,MAAM,eAAe,OAAO,qNAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,qNAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,YAAY,qNAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC;gBAC9I,yBAAyB,CAAA,GAAA,iJAAA,CAAA,QAAc,AAAD,EAAE;gBACxC,yBAAyB;gBACzB,4BAA4B;gBAC5B,4BAA4B;YAChC,IAAI,qNAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC;gBAC3B,SAAS;gBACT,OAAO,CAAC,oDAAoD,EAAE,IAAI,OAAO,EAAE;YAC/E,IAAI,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,WAAW,OAAO;YACrC,OAAO,CAAA,GAAA,0JAAA,CAAA,wBAAqB,AAAD,EAAE,0CAA0C;QAC3E;QACA,wDAAwD;QACxD,qEAAqE;QACrE,2BAA2B;QAC3B,MAAM,QAAQ,OAAO,CAAA,GAAA,+IAAA,CAAA,KAAS,AAAD,EAAE,OAAO;YAClC,QAAQ,IAAI,gBAAgB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE;oBACxC,WAAW,CAAA,GAAA,0JAAA,CAAA,wBAAqB,AAAD,EAAE,uBAAuB;wBACpD,OAAO;oBACX;oBACA,WAAW,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD,EAAE;gBAClC,IAAI,uNAAA,CAAA,qBAAkB,CAAC,MAAM,EAAE,CAAA,GAAA,yJAAA,CAAA,uBAAoB,AAAD,EAAE,qKAAA,CAAA,0BAAuB,EAAE,CAAC,QAAQ,eAAe,IAAI,CAAC,qNAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC;wBAC/H,oBAAoB,MAAM,IAAI;wBAC9B,2BAA2B,MAAM,SAAS;oBAC9C,IAAI,qNAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,mMAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,OAAO,EAAE,sBAAsB,WAAW,OAAO,EAAE,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAA,GAAA,0JAAA,CAAA,wBAAqB,AAAD,EAAE,6DAA6D,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,iBAAiB,CAAC,MAAM,qBAAqB,KAAK,MAAM,OAAO,IAAI,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,QAAQ,+IAAA,CAAA,SAAa,EAAE,+IAAA,CAAA,eAAmB,EAAE,+IAAA,CAAA,SAAa;YAC3W,SAAS,IAAI,gBAAgB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE;oBACzC,WAAW,CAAA,GAAA,0JAAA,CAAA,wBAAqB,AAAD,EAAE;oBACjC,WAAW,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD,EAAE;gBAClC,IAAI,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,uNAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,qKAAA,CAAA,wBAAqB,IAAI,+IAAA,CAAA,SAAa;QACnG,GAAG,IAAI,CAAC,+IAAA,CAAA,aAAiB;QACzB,MAAM,aAAa,cAAc,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;gBACtC,KAAK,EAAE,GAAG;gBACV,KAAK,EAAE,GAAG;gBACV,MAAM,kBAAkB,CAAC,EAAE,CAAC,IAAI;gBAChC,UAAU,kBAAkB,CAAC,EAAE,CAAC,QAAQ,IAAI;YAChD,CAAC;QACL,OAAO,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,oCAAoC,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,iBAAiB;QACpG,OAAO;YACH,MAAM;YACN;QACJ;IACJ,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/upload-builder.js"], "sourcesContent": ["import { defaultErrorFormatter } from './error-formatter.js';\n\nfunction internalCreateBuilder(initDef = {}) {\n    const _def = {\n        $types: {},\n        // Default router config\n        routerConfig: {\n            image: {\n                maxFileSize: \"4MB\"\n            }\n        },\n        routeOptions: {\n            awaitServerData: true\n        },\n        inputParser: {\n            parseAsync: ()=>Promise.resolve(undefined),\n            _input: undefined,\n            _output: undefined\n        },\n        middleware: ()=>({}),\n        onUploadError: ()=>{\n        // noop\n        },\n        onUploadComplete: ()=>undefined,\n        errorFormatter: initDef.errorFormatter ?? defaultErrorFormatter,\n        // Overload with properties passed in\n        ...initDef\n    };\n    return {\n        input (userParser) {\n            return internalCreateBuilder({\n                ..._def,\n                inputParser: userParser\n            });\n        },\n        middleware (userMiddleware) {\n            return internalCreateBuilder({\n                ..._def,\n                middleware: userMiddleware\n            });\n        },\n        onUploadComplete (userUploadComplete) {\n            return {\n                ..._def,\n                onUploadComplete: userUploadComplete\n            };\n        },\n        onUploadError (userOnUploadError) {\n            return internalCreateBuilder({\n                ..._def,\n                onUploadError: userOnUploadError\n            });\n        }\n    };\n}\n/**\n * Create a builder for your backend adapter.\n * Refer to the existing adapters for examples on how to use this function.\n * @public\n *\n * @param opts - Options for the builder\n * @returns A file route builder for making UploadThing file routes\n */ function createBuilder(opts) {\n    return (input, config)=>{\n        return internalCreateBuilder({\n            routerConfig: input,\n            routeOptions: config ?? {},\n            ...opts\n        });\n    };\n}\n\nexport { createBuilder };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,sBAAsB,UAAU,CAAC,CAAC;IACvC,MAAM,OAAO;QACT,QAAQ,CAAC;QACT,wBAAwB;QACxB,cAAc;YACV,OAAO;gBACH,aAAa;YACjB;QACJ;QACA,cAAc;YACV,iBAAiB;QACrB;QACA,aAAa;YACT,YAAY,IAAI,QAAQ,OAAO,CAAC;YAChC,QAAQ;YACR,SAAS;QACb;QACA,YAAY,IAAI,CAAC,CAAC,CAAC;QACnB,eAAe;QACf,OAAO;QACP;QACA,kBAAkB,IAAI;QACtB,gBAAgB,QAAQ,cAAc,IAAI,sKAAA,CAAA,wBAAqB;QAC/D,qCAAqC;QACrC,GAAG,OAAO;IACd;IACA,OAAO;QACH,OAAO,UAAU;YACb,OAAO,sBAAsB;gBACzB,GAAG,IAAI;gBACP,aAAa;YACjB;QACJ;QACA,YAAY,cAAc;YACtB,OAAO,sBAAsB;gBACzB,GAAG,IAAI;gBACP,YAAY;YAChB;QACJ;QACA,kBAAkB,kBAAkB;YAChC,OAAO;gBACH,GAAG,IAAI;gBACP,kBAAkB;YACtB;QACJ;QACA,eAAe,iBAAiB;YAC5B,OAAO,sBAAsB;gBACzB,GAAG,IAAI;gBACP,eAAe;YACnB;QACJ;IACJ;AACJ;AACA;;;;;;;CAOC,GAAG,SAAS,cAAc,IAAI;IAC3B,OAAO,CAAC,OAAO;QACX,OAAO,sBAAsB;YACzB,cAAc;YACd,cAAc,UAAU,CAAC;YACzB,GAAG,IAAI;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1083, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/dist/_internal/upload-server.js"], "sourcesContent": ["import { HttpClient, HttpClientRequest } from '@effect/platform';\nimport * as Effect from 'effect/Effect';\nimport { unsafeCoerce } from 'effect/Function';\nimport { UploadThingError } from '@uploadthing/shared';\nimport { logDeprecationWarning } from './deprecations.js';\nimport { logHttpClientError } from './logger.js';\n\nvar version = \"7.7.2\";\n\nconst uploadWithoutProgress = (file, presigned)=>Effect.gen(function*() {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const httpClient = (yield* HttpClient.HttpClient).pipe(HttpClient.filterStatusOk);\n        const json = yield* HttpClientRequest.put(presigned.url).pipe(HttpClientRequest.bodyFormData(formData), HttpClientRequest.setHeader(\"Range\", \"bytes=0-\"), HttpClientRequest.setHeader(\"x-uploadthing-version\", version), httpClient.execute, Effect.tapError(logHttpClientError(\"Failed to upload file\")), Effect.mapError((e)=>new UploadThingError({\n                code: \"UPLOAD_FAILED\",\n                message: \"Failed to upload file\",\n                cause: e\n            })), Effect.andThen((_)=>_.json), Effect.andThen(unsafeCoerce), Effect.scoped);\n        yield* Effect.logDebug(`File ${file.name} uploaded successfully`).pipe(Effect.annotateLogs(\"json\", json));\n        return {\n            ...json,\n            get url () {\n                logDeprecationWarning(\"`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                return json.url;\n            },\n            get appUrl () {\n                logDeprecationWarning(\"`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                return json.appUrl;\n            }\n        };\n    });\n\nexport { uploadWithoutProgress };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,IAAI,UAAU;AAEd,MAAM,wBAAwB,CAAC,MAAM,YAAY,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;QACpD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,MAAM,aAAa,CAAC,OAAO,uMAAA,CAAA,aAAU,CAAC,UAAU,EAAE,IAAI,CAAC,uMAAA,CAAA,aAAU,CAAC,cAAc;QAChF,MAAM,OAAO,OAAO,qNAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE,IAAI,CAAC,qNAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,WAAW,qNAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,SAAS,aAAa,qNAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,yBAAyB,UAAU,WAAW,OAAO,EAAE,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD,EAAE,2BAA2B,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,CAAC,IAAI,IAAI,wJAAA,CAAA,mBAAgB,CAAC;gBAC7U,MAAM;gBACN,SAAS;gBACT,OAAO;YACX,KAAK,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,CAAC,IAAI,EAAE,IAAI,GAAG,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,iJAAA,CAAA,eAAY,GAAG,+IAAA,CAAA,SAAa;QACjF,OAAO,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,sBAAsB,CAAC,EAAE,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,QAAQ;QACnG,OAAO;YACH,GAAG,IAAI;YACP,IAAI,OAAO;gBACP,CAAA,GAAA,gKAAA,CAAA,wBAAqB,AAAD,EAAE;gBACtB,OAAO,KAAK,GAAG;YACnB;YACA,IAAI,UAAU;gBACV,CAAA,GAAA,gKAAA,CAAA,wBAAqB,AAAD,EAAE;gBACtB,OAAO,KAAK,MAAM;YACtB;QACJ;IACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1129, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/server/index.js"], "sourcesContent": ["import * as Effect from 'effect/Effect';\nimport { UploadThingError, generateKey, generateSignedURL, parseTimeToSeconds } from '@uploadthing/shared';\nexport { UploadThingError } from '@uploadthing/shared';\nimport { makeAdapterHandler } from '../dist/_internal/handler.js';\nexport { makeAdapterHandler } from '../dist/_internal/handler.js';\nimport { extractRouterConfig as extractRouterConfig$1 } from '../dist/_internal/route-config.js';\nimport { createBuilder } from '../dist/_internal/upload-builder.js';\nexport { createBuilder } from '../dist/_internal/upload-builder.js';\nexport { UTFiles, UTRegion as experimental_UTRegion } from '../dist/_internal/types.js';\nimport { HttpClient, HttpClientRequest, HttpClientResponse } from '@effect/platform';\nimport * as Arr from 'effect/Array';\nimport * as Cause from 'effect/Cause';\nimport * as Redacted from 'effect/Redacted';\nimport * as S from 'effect/Schema';\nimport { UTToken, IngestUrl, ApiUrl, UPLOADTHING_VERSION, UfsHost } from '../dist/_internal/config.js';\nimport { logHttpClientError, logHttpClientResponse } from '../dist/_internal/logger.js';\nimport { makeRuntime } from '../dist/_internal/runtime.js';\nimport { lookup } from '@uploadthing/mime-types';\nimport * as Predicate from 'effect/Predicate';\nimport { uploadWithoutProgress } from '../dist/_internal/upload-server.js';\n\n/**\n * Extension of the Blob class that simplifies setting the `name` and `customId` properties,\n * similar to the built-in File class from Node > 20.\n */ class UTFile extends Blob {\n    constructor(parts, name, options){\n        const optionsWithDefaults = {\n            ...options,\n            type: options?.type ?? (lookup(name) || \"application/octet-stream\"),\n            lastModified: options?.lastModified ?? Date.now()\n        };\n        super(parts, optionsWithDefaults);\n        this.name = name;\n        this.customId = optionsWithDefaults.customId;\n        this.lastModified = optionsWithDefaults.lastModified;\n    }\n}\n\nfunction guardServerOnly() {\n    if (typeof window !== \"undefined\") {\n        throw new UploadThingError({\n            code: \"INTERNAL_SERVER_ERROR\",\n            message: \"The `utapi` can only be used on the server.\"\n        });\n    }\n}\nconst downloadFile = (_url)=>Effect.gen(function*() {\n        let url = Predicate.isRecord(_url) ? _url.url : _url;\n        if (typeof url === \"string\") {\n            // since dataurls will result in name being too long, tell the user\n            // to use uploadFiles instead.\n            if (url.startsWith(\"data:\")) {\n                return yield* Effect.fail({\n                    code: \"BAD_REQUEST\",\n                    message: \"Please use uploadFiles() for data URLs. uploadFilesFromUrl() is intended for use with remote URLs only.\",\n                    data: undefined\n                });\n            }\n        }\n        url = new URL(url);\n        const { name = url.pathname.split(\"/\").pop() ?? \"unknown-filename\", customId = undefined } = Predicate.isRecord(_url) ? _url : {};\n        const httpClient = (yield* HttpClient.HttpClient).pipe(HttpClient.filterStatusOk);\n        const arrayBuffer = yield* HttpClientRequest.get(url).pipe(HttpClientRequest.modify({\n            headers: {}\n        }), httpClient.execute, Effect.flatMap((_)=>_.arrayBuffer), Effect.mapError((cause)=>{\n            return {\n                code: \"BAD_REQUEST\",\n                message: `Failed to download requested file: ${cause.message}`,\n                data: cause.toJSON()\n            };\n        }), Effect.scoped);\n        return new UTFile([\n            arrayBuffer\n        ], name, {\n            customId,\n            lastModified: Date.now()\n        });\n    }).pipe(Effect.withLogSpan(\"downloadFile\"));\nconst generatePresignedUrl = (file, cd, acl)=>Effect.gen(function*() {\n        const { apiKey, appId } = yield* UTToken;\n        const baseUrl = yield* IngestUrl(undefined);\n        const key = yield* generateKey(file, appId);\n        const url = yield* generateSignedURL(`${baseUrl}/${key}`, apiKey, {\n            // ttlInSeconds: routeOptions.presignedURLTTL,\n            data: {\n                \"x-ut-identifier\": appId,\n                \"x-ut-file-name\": file.name,\n                \"x-ut-file-size\": file.size,\n                \"x-ut-file-type\": file.type,\n                \"x-ut-custom-id\": file.customId,\n                \"x-ut-content-disposition\": cd,\n                \"x-ut-acl\": acl\n            }\n        });\n        return {\n            url,\n            key\n        };\n    }).pipe(Effect.withLogSpan(\"generatePresignedUrl\"));\nconst uploadFile = (file, opts)=>Effect.gen(function*() {\n        const presigned = yield* generatePresignedUrl(file, opts.contentDisposition ?? \"inline\", opts.acl).pipe(Effect.catchTag(\"UploadThingError\", (e)=>Effect.fail(UploadThingError.toObject(e))), Effect.catchTag(\"ConfigError\", ()=>Effect.fail({\n                code: \"INVALID_SERVER_CONFIG\",\n                message: \"Failed to generate presigned URL\"\n            })));\n        const response = yield* uploadWithoutProgress(file, presigned).pipe(Effect.catchTag(\"UploadThingError\", (e)=>Effect.fail(UploadThingError.toObject(e))), Effect.catchTag(\"ResponseError\", (e)=>Effect.fail({\n                code: \"UPLOAD_FAILED\",\n                message: \"Failed to upload file\",\n                data: e.toJSON()\n            })));\n        return {\n            key: presigned.key,\n            url: response.url,\n            appUrl: response.appUrl,\n            ufsUrl: response.ufsUrl,\n            lastModified: file.lastModified ?? Date.now(),\n            name: file.name,\n            size: file.size,\n            type: file.type,\n            customId: file.customId ?? null,\n            fileHash: response.fileHash\n        };\n    }).pipe(Effect.withLogSpan(\"uploadFile\"));\n\nclass UTApi {\n    constructor(options){\n        this.requestUploadThing = (pathname, body, responseSchema)=>Effect.gen(this, function*() {\n                const { apiKey } = yield* UTToken;\n                const baseUrl = yield* ApiUrl;\n                const httpClient = (yield* HttpClient.HttpClient).pipe(HttpClient.filterStatusOk);\n                return yield* HttpClientRequest.post(pathname).pipe(HttpClientRequest.prependUrl(baseUrl), HttpClientRequest.bodyUnsafeJson(body), HttpClientRequest.setHeaders({\n                    \"x-uploadthing-version\": UPLOADTHING_VERSION,\n                    \"x-uploadthing-be-adapter\": \"server-sdk\",\n                    \"x-uploadthing-api-key\": Redacted.value(apiKey)\n                }), httpClient.execute, Effect.tapBoth({\n                    onSuccess: logHttpClientResponse(\"UploadThing API Response\"),\n                    onFailure: logHttpClientError(\"Failed to request UploadThing API\")\n                }), Effect.flatMap(HttpClientResponse.schemaBodyJson(responseSchema)), Effect.scoped);\n            }).pipe(Effect.catchTag(\"ConfigError\", (e)=>new UploadThingError({\n                    code: \"INVALID_SERVER_CONFIG\",\n                    message: \"There was an error with the server configuration. More info can be found on this error's `cause` property\",\n                    cause: e\n                })), Effect.withLogSpan(\"utapi.#requestUploadThing\"));\n        this.executeAsync = async (program, signal)=>{\n            const exit = await program.pipe(Effect.withLogSpan(\"utapi.#executeAsync\"), (e)=>this.runtime.runPromiseExit(e, signal ? {\n                    signal\n                } : undefined));\n            if (exit._tag === \"Failure\") {\n                throw Cause.squash(exit.cause);\n            }\n            return exit.value;\n        };\n        /**\n   * Request to delete files from UploadThing storage.\n   * @param {string | string[]} fileKeys\n   *\n   * @example\n   * await deleteFiles(\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\");\n   *\n   * @example\n   * await deleteFiles([\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\",\"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\"])\n   *\n   * @example\n   * await deleteFiles(\"myCustomIdentifier\", { keyType: \"customId\" })\n   */ this.deleteFiles = async (keys, opts)=>{\n            guardServerOnly();\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            class DeleteFileResponse extends S.Class(\"DeleteFileResponse\")({\n                success: S.Boolean,\n                deletedCount: S.Number\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/deleteFiles\", keyType === \"fileKey\" ? {\n                fileKeys: Arr.ensure(keys)\n            } : {\n                customIds: Arr.ensure(keys)\n            }, DeleteFileResponse).pipe(Effect.withLogSpan(\"deleteFiles\")));\n        };\n        /**\n   * Request file URLs from UploadThing storage.\n   * @param {string | string[]} fileKeys\n   *\n   * @example\n   * const data = await getFileUrls(\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\");\n   * console.log(data); // [{key: \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", url: \"https://uploadthing.com/f/2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\"}]\n   *\n   * @example\n   * const data = await getFileUrls([\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\",\"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\"])\n   * console.log(data) // [{key: \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", url: \"https://uploadthing.com/f/2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\" },{key: \"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\", url: \"https://uploadthing.com/f/1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\"}]\n   *\n   * @deprecated - See https://docs.uploadthing.com/working-with-files#accessing-files for info how to access files\n   */ this.getFileUrls = async (keys, opts)=>{\n            guardServerOnly();\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            class GetFileUrlResponse extends S.Class(\"GetFileUrlResponse\")({\n                data: S.Array(S.Struct({\n                    key: S.String,\n                    url: S.String\n                }))\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/getFileUrl\", keyType === \"fileKey\" ? {\n                fileKeys: Arr.ensure(keys)\n            } : {\n                customIds: Arr.ensure(keys)\n            }, GetFileUrlResponse).pipe(Effect.withLogSpan(\"getFileUrls\")));\n        };\n        /**\n   * Request file list from UploadThing storage.\n   * @param {object} opts\n   * @param {number} opts.limit The maximum number of files to return\n   * @param {number} opts.offset The number of files to skip\n   *\n   * @example\n   * const data = await listFiles({ limit: 1 });\n   * console.log(data); // { key: \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", id: \"2e0fdb64-9957-4262-8e45-f372ba903ac8\" }\n   */ this.listFiles = async (opts)=>{\n            guardServerOnly();\n            class ListFileResponse extends S.Class(\"ListFileResponse\")({\n                hasMore: S.Boolean,\n                files: S.Array(S.Struct({\n                    id: S.String,\n                    customId: S.NullOr(S.String),\n                    key: S.String,\n                    name: S.String,\n                    size: S.Number,\n                    status: S.Literal(\"Deletion Pending\", \"Failed\", \"Uploaded\", \"Uploading\"),\n                    uploadedAt: S.Number\n                }))\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/listFiles\", {\n                ...opts\n            }, ListFileResponse).pipe(Effect.withLogSpan(\"listFiles\")));\n        };\n        this.renameFiles = async (updates)=>{\n            guardServerOnly();\n            class RenameFileResponse extends S.Class(\"RenameFileResponse\")({\n                success: S.Boolean\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/renameFiles\", {\n                updates: Arr.ensure(updates)\n            }, RenameFileResponse).pipe(Effect.withLogSpan(\"renameFiles\")));\n        };\n        this.getUsageInfo = async ()=>{\n            guardServerOnly();\n            class GetUsageInfoResponse extends S.Class(\"GetUsageInfoResponse\")({\n                totalBytes: S.Number,\n                appTotalBytes: S.Number,\n                filesUploaded: S.Number,\n                limitBytes: S.Number\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/getUsageInfo\", {}, GetUsageInfoResponse).pipe(Effect.withLogSpan(\"getUsageInfo\")));\n        };\n        /**\n   * Generate a presigned url for a private file\n   * Unlike {@link getSignedURL}, this method does not make a fetch request to the UploadThing API\n   * and is the recommended way to generate a presigned url for a private file.\n   **/ this.generateSignedURL = async (key, opts)=>{\n            guardServerOnly();\n            const expiresIn = parseTimeToSeconds(opts?.expiresIn ?? \"5 minutes\");\n            if (opts?.expiresIn && isNaN(expiresIn)) {\n                throw new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"expiresIn must be a valid time string, for example '1d', '2 days', or a number of seconds.\"\n                });\n            }\n            if (expiresIn > 86400 * 7) {\n                throw new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"expiresIn must be less than 7 days (604800 seconds).\"\n                });\n            }\n            const program = Effect.gen(function*() {\n                const { apiKey, appId } = yield* UTToken;\n                const ufsHost = yield* UfsHost;\n                const proto = ufsHost.includes(\"local\") ? \"http\" : \"https\";\n                const ufsUrl = yield* generateSignedURL(`${proto}://${appId}.${ufsHost}/f/${key}`, apiKey, {\n                    ttlInSeconds: expiresIn\n                });\n                return {\n                    ufsUrl\n                };\n            });\n            return await this.executeAsync(program.pipe(Effect.catchTag(\"ConfigError\", (e)=>new UploadThingError({\n                    code: \"INVALID_SERVER_CONFIG\",\n                    message: \"There was an error with the server configuration. More info can be found on this error's `cause` property\",\n                    cause: e\n                })), Effect.withLogSpan(\"generateSignedURL\")));\n        };\n        /**\n   * Request a presigned url for a private file(s)\n   * @remarks This method is no longer recommended as it makes a fetch\n   * request to the UploadThing API which incurs redundant latency. It\n   * will be deprecated in UploadThing v8 and removed in UploadThing v9.\n   *\n   * @see {@link generateSignedURL} for a more efficient way to generate a presigned url\n   **/ this.getSignedURL = async (key, opts)=>{\n            guardServerOnly();\n            const expiresIn = opts?.expiresIn ? parseTimeToSeconds(opts.expiresIn) : undefined;\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            if (opts?.expiresIn && isNaN(expiresIn)) {\n                throw new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"expiresIn must be a valid time string, for example '1d', '2 days', or a number of seconds.\"\n                });\n            }\n            if (expiresIn && expiresIn > 86400 * 7) {\n                throw new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"expiresIn must be less than 7 days (604800 seconds).\"\n                });\n            }\n            class GetSignedUrlResponse extends S.Class(\"GetSignedUrlResponse\")({\n                url: S.String,\n                ufsUrl: S.String\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/requestFileAccess\", keyType === \"fileKey\" ? {\n                fileKey: key,\n                expiresIn\n            } : {\n                customId: key,\n                expiresIn\n            }, GetSignedUrlResponse).pipe(Effect.withLogSpan(\"getSignedURL\")));\n        };\n        /**\n   * Update the ACL of a file or set of files.\n   *\n   * @example\n   * // Make a single file public\n   * await utapi.updateACL(\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", \"public-read\");\n   *\n   * // Make multiple files private\n   * await utapi.updateACL(\n   *   [\n   *     \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\",\n   *     \"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\",\n   *   ],\n   *   \"private\",\n   * );\n   */ this.updateACL = async (keys, acl, opts)=>{\n            guardServerOnly();\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            const updates = Arr.ensure(keys).map((key)=>{\n                return keyType === \"fileKey\" ? {\n                    fileKey: key,\n                    acl\n                } : {\n                    customId: key,\n                    acl\n                };\n            });\n            const responseSchema = S.Struct({\n                success: S.Boolean\n            });\n            return await this.executeAsync(this.requestUploadThing(\"/v6/updateACL\", {\n                updates\n            }, responseSchema).pipe(Effect.withLogSpan(\"updateACL\")));\n        };\n        // Assert some stuff\n        guardServerOnly();\n        this.opts = options ?? {};\n        this.fetch = this.opts.fetch ?? globalThis.fetch;\n        this.defaultKeyType = this.opts.defaultKeyType ?? \"fileKey\";\n        this.runtime = makeRuntime(this.fetch, this.opts);\n    }\n    uploadFiles(files, opts) {\n        guardServerOnly();\n        const concurrency = opts?.concurrency ?? 1;\n        if (concurrency < 1 || concurrency > 25) {\n            throw new UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"concurrency must be a positive integer between 1 and 25\"\n            });\n        }\n        const program = Effect.forEach(Arr.ensure(files), (file)=>uploadFile(file, opts ?? {}).pipe(Effect.match({\n                onSuccess: (data)=>({\n                        data,\n                        error: null\n                    }),\n                onFailure: (error)=>({\n                        data: null,\n                        error\n                    })\n            })), {\n            concurrency\n        }).pipe(Effect.map((ups)=>Array.isArray(files) ? ups : ups[0]), Effect.tap((res)=>Effect.logDebug(\"Finished uploading\").pipe(Effect.annotateLogs(\"uploadResult\", res))), Effect.withLogSpan(\"uploadFiles\"));\n        return this.executeAsync(program, opts?.signal);\n    }\n    uploadFilesFromUrl(urls, opts) {\n        guardServerOnly();\n        const concurrency = opts?.concurrency ?? 1;\n        if (concurrency < 1 || concurrency > 25) {\n            throw new UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"concurrency must be a positive integer between 1 and 25\"\n            });\n        }\n        const program = Effect.forEach(Arr.ensure(urls), (url)=>downloadFile(url).pipe(Effect.flatMap((file)=>uploadFile(file, opts ?? {})), Effect.match({\n                onSuccess: (data)=>({\n                        data,\n                        error: null\n                    }),\n                onFailure: (error)=>({\n                        data: null,\n                        error\n                    })\n            })), {\n            concurrency\n        }).pipe(Effect.map((ups)=>Array.isArray(urls) ? ups : ups[0]), Effect.tap((res)=>Effect.logDebug(\"Finished uploading\").pipe(Effect.annotateLogs(\"uploadResult\", res))), Effect.withLogSpan(\"uploadFiles\")).pipe(Effect.withLogSpan(\"uploadFilesFromUrl\"));\n        return this.executeAsync(program, opts?.signal);\n    }\n}\n\nconst createUploadthing = (opts)=>createBuilder(opts);\nconst createRouteHandler = (opts)=>{\n    return makeAdapterHandler((ev)=>Effect.succeed({\n            req: \"request\" in ev ? ev.request : ev\n        }), (ev)=>Effect.succeed(\"request\" in ev ? ev.request : ev), opts, \"server\");\n};\nconst extractRouterConfig = (router)=>Effect.runSync(extractRouterConfig$1(router));\n\nexport { UTApi, UTFile, createRouteHandler, createUploadthing, extractRouterConfig };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;AAEA;AACA;AAGA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AAEA;;;CAGC,GAAG,MAAM,eAAe;IACrB,YAAY,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC;QAC7B,MAAM,sBAAsB;YACxB,GAAG,OAAO;YACV,MAAM,SAAS,QAAQ,CAAC,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,SAAS,0BAA0B;YAClE,cAAc,SAAS,gBAAgB,KAAK,GAAG;QACnD;QACA,KAAK,CAAC,OAAO;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG,oBAAoB,QAAQ;QAC5C,IAAI,CAAC,YAAY,GAAG,oBAAoB,YAAY;IACxD;AACJ;AAEA,SAAS;IACL,uCAAmC;;IAKnC;AACJ;AACA,MAAM,eAAe,CAAC,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;QAChC,IAAI,MAAM,CAAA,GAAA,kJAAA,CAAA,WAAkB,AAAD,EAAE,QAAQ,KAAK,GAAG,GAAG;QAChD,IAAI,OAAO,QAAQ,UAAU;YACzB,mEAAmE;YACnE,8BAA8B;YAC9B,IAAI,IAAI,UAAU,CAAC,UAAU;gBACzB,OAAO,OAAO,CAAA,GAAA,+IAAA,CAAA,OAAW,AAAD,EAAE;oBACtB,MAAM;oBACN,SAAS;oBACT,MAAM;gBACV;YACJ;QACJ;QACA,MAAM,IAAI,IAAI;QACd,MAAM,EAAE,OAAO,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,kBAAkB,EAAE,WAAW,SAAS,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,WAAkB,AAAD,EAAE,QAAQ,OAAO,CAAC;QAChI,MAAM,aAAa,CAAC,OAAO,uMAAA,CAAA,aAAU,CAAC,UAAU,EAAE,IAAI,CAAC,uMAAA,CAAA,aAAU,CAAC,cAAc;QAChF,MAAM,cAAc,OAAO,qNAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,qNAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC;YAChF,SAAS,CAAC;QACd,IAAI,WAAW,OAAO,EAAE,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,CAAC,IAAI,EAAE,WAAW,GAAG,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,CAAC;YACzE,OAAO;gBACH,MAAM;gBACN,SAAS,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;gBAC9D,MAAM,MAAM,MAAM;YACtB;QACJ,IAAI,+IAAA,CAAA,SAAa;QACjB,OAAO,IAAI,OAAO;YACd;SACH,EAAE,MAAM;YACL;YACA,cAAc,KAAK,GAAG;QAC1B;IACJ,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;AAC/B,MAAM,uBAAuB,CAAC,MAAM,IAAI,MAAM,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;QACjD,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,0JAAA,CAAA,UAAO;QACxC,MAAM,UAAU,OAAO,CAAA,GAAA,0JAAA,CAAA,YAAS,AAAD,EAAE;QACjC,MAAM,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,MAAM;QACrC,MAAM,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,oBAAiB,AAAD,EAAE,GAAG,QAAQ,CAAC,EAAE,KAAK,EAAE,QAAQ;YAC9D,8CAA8C;YAC9C,MAAM;gBACF,mBAAmB;gBACnB,kBAAkB,KAAK,IAAI;gBAC3B,kBAAkB,KAAK,IAAI;gBAC3B,kBAAkB,KAAK,IAAI;gBAC3B,kBAAkB,KAAK,QAAQ;gBAC/B,4BAA4B;gBAC5B,YAAY;YAChB;QACJ;QACA,OAAO;YACH;YACA;QACJ;IACJ,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;AAC/B,MAAM,aAAa,CAAC,MAAM,OAAO,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;QACpC,MAAM,YAAY,OAAO,qBAAqB,MAAM,KAAK,kBAAkB,IAAI,UAAU,KAAK,GAAG,EAAE,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,oBAAoB,CAAC,IAAI,CAAA,GAAA,+IAAA,CAAA,OAAW,AAAD,EAAE,wJAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,eAAe,IAAI,CAAA,GAAA,+IAAA,CAAA,OAAW,AAAD,EAAE;gBACpO,MAAM;gBACN,SAAS;YACb;QACJ,MAAM,WAAW,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,WAAW,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,oBAAoB,CAAC,IAAI,CAAA,GAAA,+IAAA,CAAA,OAAW,AAAD,EAAE,wJAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,iBAAiB,CAAC,IAAI,CAAA,GAAA,+IAAA,CAAA,OAAW,AAAD,EAAE;gBACnM,MAAM;gBACN,SAAS;gBACT,MAAM,EAAE,MAAM;YAClB;QACJ,OAAO;YACH,KAAK,UAAU,GAAG;YAClB,KAAK,SAAS,GAAG;YACjB,QAAQ,SAAS,MAAM;YACvB,QAAQ,SAAS,MAAM;YACvB,cAAc,KAAK,YAAY,IAAI,KAAK,GAAG;YAC3C,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,SAAS,QAAQ;QAC/B;IACJ,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;AAE/B,MAAM;IACF,YAAY,OAAO,CAAC;QAChB,IAAI,CAAC,kBAAkB,GAAG,CAAC,UAAU,MAAM,iBAAiB,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,IAAI,EAAE;gBACrE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,0JAAA,CAAA,UAAO;gBACjC,MAAM,UAAU,OAAO,0JAAA,CAAA,SAAM;gBAC7B,MAAM,aAAa,CAAC,OAAO,uMAAA,CAAA,aAAU,CAAC,UAAU,EAAE,IAAI,CAAC,uMAAA,CAAA,aAAU,CAAC,cAAc;gBAChF,OAAO,OAAO,qNAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,qNAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,UAAU,qNAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,OAAO,qNAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC;oBAC5J,yBAAyB,0JAAA,CAAA,sBAAmB;oBAC5C,4BAA4B;oBAC5B,yBAAyB,CAAA,GAAA,iJAAA,CAAA,QAAc,AAAD,EAAE;gBAC5C,IAAI,WAAW,OAAO,EAAE,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE;oBACnC,WAAW,CAAA,GAAA,0JAAA,CAAA,wBAAqB,AAAD,EAAE;oBACjC,WAAW,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD,EAAE;gBAClC,IAAI,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,uNAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,kBAAkB,+IAAA,CAAA,SAAa;YACxF,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,eAAe,CAAC,IAAI,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBACzD,MAAM;oBACN,SAAS;oBACT,OAAO;gBACX,KAAK,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;QAChC,IAAI,CAAC,YAAY,GAAG,OAAO,SAAS;YAChC,MAAM,OAAO,MAAM,QAAQ,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE,wBAAwB,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,SAAS;oBAChH;gBACJ,IAAI;YACR,IAAI,KAAK,IAAI,KAAK,WAAW;gBACzB,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAY,AAAD,EAAE,KAAK,KAAK;YACjC;YACA,OAAO,KAAK,KAAK;QACrB;QACA;;;;;;;;;;;;GAYL,GAAG,IAAI,CAAC,WAAW,GAAG,OAAO,MAAM;YAC1B;YACA,MAAM,EAAE,UAAU,IAAI,CAAC,cAAc,EAAE,GAAG,QAAQ,CAAC;YACnD,MAAM,2BAA2B,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,sBAAsB;gBAC3D,SAAS,+JAAA,CAAA,UAAS;gBAClB,cAAc,+JAAA,CAAA,SAAQ;YAC1B;YACA;YACA,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,YAAY,YAAY;gBAC9F,UAAU,CAAA,GAAA,8IAAA,CAAA,SAAU,AAAD,EAAE;YACzB,IAAI;gBACA,WAAW,CAAA,GAAA,8IAAA,CAAA,SAAU,AAAD,EAAE;YAC1B,GAAG,oBAAoB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;QACnD;QACA;;;;;;;;;;;;;GAaL,GAAG,IAAI,CAAC,WAAW,GAAG,OAAO,MAAM;YAC1B;YACA,MAAM,EAAE,UAAU,IAAI,CAAC,cAAc,EAAE,GAAG,QAAQ,CAAC;YACnD,MAAM,2BAA2B,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,sBAAsB;gBAC3D,MAAM,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,+JAAA,CAAA,SAAQ,AAAD,EAAE;oBACnB,KAAK,+JAAA,CAAA,SAAQ;oBACb,KAAK,+JAAA,CAAA,SAAQ;gBACjB;YACJ;YACA;YACA,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,YAAY,YAAY;gBAC7F,UAAU,CAAA,GAAA,8IAAA,CAAA,SAAU,AAAD,EAAE;YACzB,IAAI;gBACA,WAAW,CAAA,GAAA,8IAAA,CAAA,SAAU,AAAD,EAAE;YAC1B,GAAG,oBAAoB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;QACnD;QACA;;;;;;;;;GASL,GAAG,IAAI,CAAC,SAAS,GAAG,OAAO;YAClB;YACA,MAAM,yBAAyB,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,oBAAoB;gBACvD,SAAS,+JAAA,CAAA,UAAS;gBAClB,OAAO,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,+JAAA,CAAA,SAAQ,AAAD,EAAE;oBACpB,IAAI,+JAAA,CAAA,SAAQ;oBACZ,UAAU,CAAA,GAAA,+JAAA,CAAA,SAAQ,AAAD,EAAE,+JAAA,CAAA,SAAQ;oBAC3B,KAAK,+JAAA,CAAA,SAAQ;oBACb,MAAM,+JAAA,CAAA,SAAQ;oBACd,MAAM,+JAAA,CAAA,SAAQ;oBACd,QAAQ,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD,EAAE,oBAAoB,UAAU,YAAY;oBAC5D,YAAY,+JAAA,CAAA,SAAQ;gBACxB;YACJ;YACA;YACA,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;gBACpE,GAAG,IAAI;YACX,GAAG,kBAAkB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;QACjD;QACA,IAAI,CAAC,WAAW,GAAG,OAAO;YACtB;YACA,MAAM,2BAA2B,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,sBAAsB;gBAC3D,SAAS,+JAAA,CAAA,UAAS;YACtB;YACA;YACA,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,mBAAmB;gBACtE,SAAS,CAAA,GAAA,8IAAA,CAAA,SAAU,AAAD,EAAE;YACxB,GAAG,oBAAoB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;QACnD;QACA,IAAI,CAAC,YAAY,GAAG;YAChB;YACA,MAAM,6BAA6B,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,wBAAwB;gBAC/D,YAAY,+JAAA,CAAA,SAAQ;gBACpB,eAAe,+JAAA,CAAA,SAAQ;gBACvB,eAAe,+JAAA,CAAA,SAAQ;gBACvB,YAAY,+JAAA,CAAA,SAAQ;YACxB;YACA;YACA,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,GAAG,sBAAsB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;QACjI;QACA;;;;IAIJ,GAAG,IAAI,CAAC,iBAAiB,GAAG,OAAO,KAAK;YAChC;YACA,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,aAAa;YACxD,IAAI,MAAM,aAAa,MAAM,YAAY;gBACrC,MAAM,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBACvB,MAAM;oBACN,SAAS;gBACb;YACJ;YACA,IAAI,YAAY,QAAQ,GAAG;gBACvB,MAAM,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBACvB,MAAM;oBACN,SAAS;gBACb;YACJ;YACA,MAAM,UAAU,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE;gBACvB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,0JAAA,CAAA,UAAO;gBACxC,MAAM,UAAU,OAAO,0JAAA,CAAA,UAAO;gBAC9B,MAAM,QAAQ,QAAQ,QAAQ,CAAC,WAAW,SAAS;gBACnD,MAAM,SAAS,OAAO,CAAA,GAAA,wJAAA,CAAA,oBAAiB,AAAD,EAAE,GAAG,MAAM,GAAG,EAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,EAAE,KAAK,EAAE,QAAQ;oBACvF,cAAc;gBAClB;gBACA,OAAO;oBACH;gBACJ;YACJ;YACA,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,eAAe,CAAC,IAAI,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBAC7F,MAAM;oBACN,SAAS;oBACT,OAAO;gBACX,KAAK,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;QAChC;QACA;;;;;;;IAOJ,GAAG,IAAI,CAAC,YAAY,GAAG,OAAO,KAAK;YAC3B;YACA,MAAM,YAAY,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,SAAS,IAAI;YACzE,MAAM,EAAE,UAAU,IAAI,CAAC,cAAc,EAAE,GAAG,QAAQ,CAAC;YACnD,IAAI,MAAM,aAAa,MAAM,YAAY;gBACrC,MAAM,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBACvB,MAAM;oBACN,SAAS;gBACb;YACJ;YACA,IAAI,aAAa,YAAY,QAAQ,GAAG;gBACpC,MAAM,IAAI,wJAAA,CAAA,mBAAgB,CAAC;oBACvB,MAAM;oBACN,SAAS;gBACb;YACJ;YACA,MAAM,6BAA6B,CAAA,GAAA,+JAAA,CAAA,QAAO,AAAD,EAAE,wBAAwB;gBAC/D,KAAK,+JAAA,CAAA,SAAQ;gBACb,QAAQ,+JAAA,CAAA,SAAQ;YACpB;YACA;YACA,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,YAAY,YAAY;gBACpG,SAAS;gBACT;YACJ,IAAI;gBACA,UAAU;gBACV;YACJ,GAAG,sBAAsB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;QACrD;QACA;;;;;;;;;;;;;;;GAeL,GAAG,IAAI,CAAC,SAAS,GAAG,OAAO,MAAM,KAAK;YAC7B;YACA,MAAM,EAAE,UAAU,IAAI,CAAC,cAAc,EAAE,GAAG,QAAQ,CAAC;YACnD,MAAM,UAAU,CAAA,GAAA,8IAAA,CAAA,SAAU,AAAD,EAAE,MAAM,GAAG,CAAC,CAAC;gBAClC,OAAO,YAAY,YAAY;oBAC3B,SAAS;oBACT;gBACJ,IAAI;oBACA,UAAU;oBACV;gBACJ;YACJ;YACA,MAAM,iBAAiB,CAAA,GAAA,+JAAA,CAAA,SAAQ,AAAD,EAAE;gBAC5B,SAAS,+JAAA,CAAA,UAAS;YACtB;YACA,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;gBACpE;YACJ,GAAG,gBAAgB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;QAC/C;QACA,oBAAoB;QACpB;QACA,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,WAAW,KAAK;QAChD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI;QAClD,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI;IACpD;IACA,YAAY,KAAK,EAAE,IAAI,EAAE;QACrB;QACA,MAAM,cAAc,MAAM,eAAe;QACzC,IAAI,cAAc,KAAK,cAAc,IAAI;YACrC,MAAM,IAAI,wJAAA,CAAA,mBAAgB,CAAC;gBACvB,MAAM;gBACN,SAAS;YACb;QACJ;QACA,MAAM,UAAU,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,SAAU,AAAD,EAAE,QAAQ,CAAC,OAAO,WAAW,MAAM,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,QAAY,AAAD,EAAE;gBACjG,WAAW,CAAC,OAAO,CAAC;wBACZ;wBACA,OAAO;oBACX,CAAC;gBACL,WAAW,CAAC,QAAQ,CAAC;wBACb,MAAM;wBACN;oBACJ,CAAC;YACT,KAAK;YACL;QACJ,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAC,MAAM,MAAM,OAAO,CAAC,SAAS,MAAM,GAAG,CAAC,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAC,MAAM,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,sBAAsB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,gBAAgB,QAAQ,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;QAC5L,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,MAAM;IAC5C;IACA,mBAAmB,IAAI,EAAE,IAAI,EAAE;QAC3B;QACA,MAAM,cAAc,MAAM,eAAe;QACzC,IAAI,cAAc,KAAK,cAAc,IAAI;YACrC,MAAM,IAAI,wJAAA,CAAA,mBAAgB,CAAC;gBACvB,MAAM;gBACN,SAAS;YACb;QACJ;QACA,MAAM,UAAU,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,SAAU,AAAD,EAAE,OAAO,CAAC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,CAAC,OAAO,WAAW,MAAM,QAAQ,CAAC,KAAK,CAAA,GAAA,+IAAA,CAAA,QAAY,AAAD,EAAE;gBAC1I,WAAW,CAAC,OAAO,CAAC;wBACZ;wBACA,OAAO;oBACX,CAAC;gBACL,WAAW,CAAC,QAAQ,CAAC;wBACb,MAAM;wBACN;oBACJ,CAAC;YACT,KAAK;YACL;QACJ,GAAG,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAC,MAAM,MAAM,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,MAAU,AAAD,EAAE,CAAC,MAAM,CAAA,GAAA,+IAAA,CAAA,WAAe,AAAD,EAAE,sBAAsB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,eAAmB,AAAD,EAAE,gBAAgB,QAAQ,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE,gBAAgB,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,cAAkB,AAAD,EAAE;QACnO,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,MAAM;IAC5C;AACJ;AAEA,MAAM,oBAAoB,CAAC,OAAO,CAAA,GAAA,qKAAA,CAAA,gBAAa,AAAD,EAAE;AAChD,MAAM,qBAAqB,CAAC;IACxB,OAAO,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD,EAAE,CAAC,KAAK,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE;YACvC,KAAK,aAAa,KAAK,GAAG,OAAO,GAAG;QACxC,IAAI,CAAC,KAAK,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,aAAa,KAAK,GAAG,OAAO,GAAG,KAAK,MAAM;AAC3E;AACA,MAAM,sBAAsB,CAAC,SAAS,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,CAAA,GAAA,mKAAA,CAAA,sBAAqB,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1577, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/uploadthing/next/index.js"], "sourcesContent": ["import * as Effect from 'effect/Effect';\nimport { makeAdapter<PERSON>and<PERSON> } from '../dist/_internal/handler.js';\nimport { createBuilder } from '../dist/_internal/upload-builder.js';\nexport { UTFiles, UTRegion as experimental_UTRegion } from '../dist/_internal/types.js';\n\nconst createUploadthing = (opts)=>createBuilder(opts);\nconst createRouteHandler = (opts)=>{\n    const handler = makeAdapterHandler((req)=>Effect.succeed({\n            req\n        }), (req)=>Effect.succeed(req), opts, \"nextjs-app\");\n    return {\n        POST: handler,\n        GET: handler\n    };\n};\n\nexport { createRouteHandler, createUploadthing };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAGA,MAAM,oBAAoB,CAAC,OAAO,CAAA,GAAA,qKAAA,CAAA,gBAAa,AAAD,EAAE;AAChD,MAAM,qBAAqB,CAAC;IACxB,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD,EAAE,CAAC,MAAM,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE;YACjD;QACJ,IAAI,CAAC,MAAM,CAAA,GAAA,+IAAA,CAAA,UAAc,AAAD,EAAE,MAAM,MAAM;IAC1C,OAAO;QACH,MAAM;QACN,KAAK;IACT;AACJ", "ignoreList": [0], "debugId": null}}]}