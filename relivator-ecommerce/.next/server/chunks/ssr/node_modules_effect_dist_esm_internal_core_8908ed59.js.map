{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "core.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/core.ts"], "sourcesContent": ["import * as Arr from \"../Array.js\"\nimport type * as Cause from \"../Cause.js\"\nimport * as Chunk from \"../Chunk.js\"\nimport * as Context from \"../Context.js\"\nimport type * as Deferred from \"../Deferred.js\"\nimport type * as Differ from \"../Differ.js\"\nimport * as Duration from \"../Duration.js\"\nimport type * as Effect from \"../Effect.js\"\nimport * as Either from \"../Either.js\"\nimport * as Equal from \"../Equal.js\"\nimport type * as ExecutionStrategy from \"../ExecutionStrategy.js\"\nimport type * as Exit from \"../Exit.js\"\nimport type * as Fiber from \"../Fiber.js\"\nimport * as FiberId from \"../FiberId.js\"\nimport type * as FiberRef from \"../FiberRef.js\"\nimport type * as FiberStatus from \"../FiberStatus.js\"\nimport type { LazyArg } from \"../Function.js\"\nimport { dual, identity, pipe } from \"../Function.js\"\nimport { globalValue } from \"../GlobalValue.js\"\nimport * as Hash from \"../Hash.js\"\nimport * as HashMap from \"../HashMap.js\"\nimport type * as HashSet from \"../HashSet.js\"\nimport { format, NodeInspectSymbol, toJSON } from \"../Inspectable.js\"\nimport * as List from \"../List.js\"\nimport type * as LogLevel from \"../LogLevel.js\"\nimport type * as LogSpan from \"../LogSpan.js\"\nimport type * as MetricLabel from \"../MetricLabel.js\"\nimport * as MutableRef from \"../MutableRef.js\"\nimport * as Option from \"../Option.js\"\nimport { pipeArguments } from \"../Pipeable.js\"\nimport { hasProperty, isObject, isPromiseLike, type Predicate, type Refinement } from \"../Predicate.js\"\nimport type * as Request from \"../Request.js\"\nimport type * as BlockedRequests from \"../RequestBlock.js\"\nimport type * as RequestResolver from \"../RequestResolver.js\"\nimport type * as RuntimeFlags from \"../RuntimeFlags.js\"\nimport * as RuntimeFlagsPatch from \"../RuntimeFlagsPatch.js\"\nimport type * as Scope from \"../Scope.js\"\nimport type * as Tracer from \"../Tracer.js\"\nimport type { NoInfer, NotFunction } from \"../Types.js\"\nimport { internalCall, YieldWrap } from \"../Utils.js\"\nimport * as blockedRequests_ from \"./blockedRequests.js\"\nimport * as internalCause from \"./cause.js\"\nimport * as deferred from \"./deferred.js\"\nimport * as internalDiffer from \"./differ.js\"\nimport { CommitPrototype, effectVariance, StructuralCommitPrototype } from \"./effectable.js\"\nimport { getBugErrorMessage } from \"./errors.js\"\nimport type * as FiberRuntime from \"./fiberRuntime.js\"\nimport type * as fiberScope from \"./fiberScope.js\"\nimport * as DeferredOpCodes from \"./opCodes/deferred.js\"\nimport * as OpCodes from \"./opCodes/effect.js\"\nimport * as runtimeFlags_ from \"./runtimeFlags.js\"\nimport { SingleShotGen } from \"./singleShotGen.js\"\n\n// -----------------------------------------------------------------------------\n// Effect\n// -----------------------------------------------------------------------------\n\n/**\n * @internal\n */\nexport const blocked = <A, E>(\n  blockedRequests: BlockedRequests.RequestBlock,\n  _continue: Effect.Effect<A, E>\n): Effect.Blocked<A, E> => {\n  const effect = new EffectPrimitive(\"Blocked\") as any\n  effect.effect_instruction_i0 = blockedRequests\n  effect.effect_instruction_i1 = _continue\n  return effect\n}\n\n/**\n * @internal\n */\nexport const runRequestBlock = (\n  blockedRequests: BlockedRequests.RequestBlock\n): Effect.Effect<void> => {\n  const effect = new EffectPrimitive(\"RunBlocked\") as any\n  effect.effect_instruction_i0 = blockedRequests\n  return effect\n}\n\n/** @internal */\nexport const EffectTypeId: Effect.EffectTypeId = Symbol.for(\"effect/Effect\") as Effect.EffectTypeId\n\n/** @internal */\nexport type Primitive =\n  | Async\n  | Commit\n  | Failure\n  | OnFailure\n  | OnSuccess\n  | OnStep\n  | OnSuccessAndFailure\n  | Success\n  | Sync\n  | UpdateRuntimeFlags\n  | While\n  | FromIterator\n  | WithRuntime\n  | Yield\n  | OpTag\n  | Blocked\n  | RunBlocked\n  | Either.Either<any, any>\n  | Option.Option<any>\n\n/** @internal */\nexport type Continuation =\n  | OnSuccess\n  | OnStep\n  | OnSuccessAndFailure\n  | OnFailure\n  | While\n  | FromIterator\n  | RevertFlags\n\n/** @internal */\nexport class RevertFlags {\n  readonly _op = OpCodes.OP_REVERT_FLAGS\n  constructor(\n    readonly patch: RuntimeFlagsPatch.RuntimeFlagsPatch,\n    readonly op: Primitive & { _op: OpCodes.OP_UPDATE_RUNTIME_FLAGS }\n  ) {\n  }\n}\n\nclass EffectPrimitive {\n  public effect_instruction_i0 = undefined\n  public effect_instruction_i1 = undefined\n  public effect_instruction_i2 = undefined\n  public trace = undefined;\n  [EffectTypeId] = effectVariance\n  constructor(readonly _op: Primitive[\"_op\"]) {}\n  [Equal.symbol](this: {}, that: unknown) {\n    return this === that\n  }\n  [Hash.symbol](this: {}) {\n    return Hash.cached(this, Hash.random(this))\n  }\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n  toJSON() {\n    return {\n      _id: \"Effect\",\n      _op: this._op,\n      effect_instruction_i0: toJSON(this.effect_instruction_i0),\n      effect_instruction_i1: toJSON(this.effect_instruction_i1),\n      effect_instruction_i2: toJSON(this.effect_instruction_i2)\n    }\n  }\n  toString() {\n    return format(this.toJSON())\n  }\n  [NodeInspectSymbol]() {\n    return this.toJSON()\n  }\n  [Symbol.iterator]() {\n    return new SingleShotGen(new YieldWrap(this))\n  }\n}\n\n/** @internal */\nclass EffectPrimitiveFailure {\n  public effect_instruction_i0 = undefined\n  public effect_instruction_i1 = undefined\n  public effect_instruction_i2 = undefined\n  public trace = undefined;\n  [EffectTypeId] = effectVariance\n  constructor(readonly _op: Primitive[\"_op\"]) {\n    // @ts-expect-error\n    this._tag = _op\n  }\n  [Equal.symbol](this: {}, that: unknown) {\n    return exitIsExit(that) && that._op === \"Failure\" &&\n      // @ts-expect-error\n      Equal.equals(this.effect_instruction_i0, that.effect_instruction_i0)\n  }\n  [Hash.symbol](this: {}) {\n    return pipe(\n      // @ts-expect-error\n      Hash.string(this._tag),\n      // @ts-expect-error\n      Hash.combine(Hash.hash(this.effect_instruction_i0)),\n      Hash.cached(this)\n    )\n  }\n  get cause() {\n    return this.effect_instruction_i0\n  }\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n  toJSON() {\n    return {\n      _id: \"Exit\",\n      _tag: this._op,\n      cause: (this.cause as any).toJSON()\n    }\n  }\n  toString() {\n    return format(this.toJSON())\n  }\n  [NodeInspectSymbol]() {\n    return this.toJSON()\n  }\n  [Symbol.iterator]() {\n    return new SingleShotGen(new YieldWrap(this))\n  }\n}\n\n/** @internal */\nclass EffectPrimitiveSuccess {\n  public effect_instruction_i0 = undefined\n  public effect_instruction_i1 = undefined\n  public effect_instruction_i2 = undefined\n  public trace = undefined;\n  [EffectTypeId] = effectVariance\n  constructor(readonly _op: Primitive[\"_op\"]) {\n    // @ts-expect-error\n    this._tag = _op\n  }\n  [Equal.symbol](this: {}, that: unknown) {\n    return exitIsExit(that) && that._op === \"Success\" &&\n      // @ts-expect-error\n      Equal.equals(this.effect_instruction_i0, that.effect_instruction_i0)\n  }\n  [Hash.symbol](this: {}) {\n    return pipe(\n      // @ts-expect-error\n      Hash.string(this._tag),\n      // @ts-expect-error\n      Hash.combine(Hash.hash(this.effect_instruction_i0)),\n      Hash.cached(this)\n    )\n  }\n  get value() {\n    return this.effect_instruction_i0\n  }\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n  toJSON() {\n    return {\n      _id: \"Exit\",\n      _tag: this._op,\n      value: toJSON(this.value)\n    }\n  }\n  toString() {\n    return format(this.toJSON())\n  }\n  [NodeInspectSymbol]() {\n    return this.toJSON()\n  }\n  [Symbol.iterator]() {\n    return new SingleShotGen(new YieldWrap(this))\n  }\n}\n\n/** @internal */\nexport type Op<Tag extends string, Body = {}> = Effect.Effect<never> & Body & {\n  readonly _op: Tag\n}\n\n/** @internal */\nexport interface Async extends\n  Op<OpCodes.OP_ASYNC, {\n    effect_instruction_i0(resume: (effect: Primitive) => void): void\n    readonly effect_instruction_i1: FiberId.FiberId\n  }>\n{}\n\n/** @internal */\nexport interface Blocked<out E = any, out A = any> extends\n  Op<\"Blocked\", {\n    readonly effect_instruction_i0: BlockedRequests.RequestBlock\n    readonly effect_instruction_i1: Effect.Effect<A, E>\n  }>\n{}\n\n/** @internal */\nexport interface RunBlocked extends\n  Op<\"RunBlocked\", {\n    readonly effect_instruction_i0: BlockedRequests.RequestBlock\n  }>\n{}\n\n/** @internal */\nexport interface Failure extends\n  Op<OpCodes.OP_FAILURE, {\n    readonly effect_instruction_i0: Cause.Cause<unknown>\n  }>\n{}\n\n/** @internal */\nexport interface OpTag extends Op<OpCodes.OP_TAG, {}> {}\n\n/** @internal */\nexport interface Commit extends\n  Op<OpCodes.OP_COMMIT, {\n    commit(): Effect.Effect<unknown, unknown, unknown>\n  }>\n{}\n\n/** @internal */\nexport interface OnFailure extends\n  Op<OpCodes.OP_ON_FAILURE, {\n    readonly effect_instruction_i0: Primitive\n    effect_instruction_i1(a: Cause.Cause<unknown>): Primitive\n  }>\n{}\n\n/** @internal */\nexport interface OnSuccess extends\n  Op<OpCodes.OP_ON_SUCCESS, {\n    readonly effect_instruction_i0: Primitive\n    effect_instruction_i1(a: unknown): Primitive\n  }>\n{}\n\n/** @internal */\nexport interface OnStep extends Op<\"OnStep\", { readonly effect_instruction_i0: Primitive }> {}\n\n/** @internal */\nexport interface OnSuccessAndFailure extends\n  Op<OpCodes.OP_ON_SUCCESS_AND_FAILURE, {\n    readonly effect_instruction_i0: Primitive\n    effect_instruction_i1(a: Cause.Cause<unknown>): Primitive\n    effect_instruction_i2(a: unknown): Primitive\n  }>\n{}\n\n/** @internal */\nexport interface Success extends\n  Op<OpCodes.OP_SUCCESS, {\n    readonly effect_instruction_i0: unknown\n  }>\n{}\n\n/** @internal */\nexport interface Sync extends\n  Op<OpCodes.OP_SYNC, {\n    effect_instruction_i0(): unknown\n  }>\n{}\n\n/** @internal */\nexport interface UpdateRuntimeFlags extends\n  Op<OpCodes.OP_UPDATE_RUNTIME_FLAGS, {\n    readonly effect_instruction_i0: RuntimeFlagsPatch.RuntimeFlagsPatch\n    readonly effect_instruction_i1?: (oldRuntimeFlags: RuntimeFlags.RuntimeFlags) => Primitive\n  }>\n{}\n\n/** @internal */\nexport interface While extends\n  Op<OpCodes.OP_WHILE, {\n    effect_instruction_i0(): boolean\n    effect_instruction_i1(): Primitive\n    effect_instruction_i2(a: unknown): void\n  }>\n{}\n\n/** @internal */\nexport interface FromIterator extends\n  Op<OpCodes.OP_ITERATOR, {\n    effect_instruction_i0: Iterator<YieldWrap<Primitive>, any>\n  }>\n{}\n\n/** @internal */\nexport interface WithRuntime extends\n  Op<OpCodes.OP_WITH_RUNTIME, {\n    effect_instruction_i0(fiber: FiberRuntime.FiberRuntime<unknown, unknown>, status: FiberStatus.Running): Primitive\n  }>\n{}\n\n/** @internal */\nexport interface Yield extends Op<OpCodes.OP_YIELD> {}\n\n/** @internal */\nexport const isEffect = (u: unknown): u is Effect.Effect<unknown, unknown, unknown> => hasProperty(u, EffectTypeId)\n\n/* @internal */\nexport const withFiberRuntime = <A, E = never, R = never>(\n  withRuntime: (fiber: FiberRuntime.FiberRuntime<A, E>, status: FiberStatus.Running) => Effect.Effect<A, E, R>\n): Effect.Effect<A, E, R> => {\n  const effect = new EffectPrimitive(OpCodes.OP_WITH_RUNTIME) as any\n  effect.effect_instruction_i0 = withRuntime\n  return effect\n}\n\n/* @internal */\nexport const acquireUseRelease: {\n  <A2, E2, R2, A, X, R3>(\n    use: (a: A) => Effect.Effect<A2, E2, R2>,\n    release: (a: A, exit: Exit.Exit<A2, E2>) => Effect.Effect<X, never, R3>\n  ): <E, R>(acquire: Effect.Effect<A, E, R>) => Effect.Effect<A2, E2 | E, R2 | R3 | R>\n  <A, E, R, A2, E2, R2, X, R3>(\n    acquire: Effect.Effect<A, E, R>,\n    use: (a: A) => Effect.Effect<A2, E2, R2>,\n    release: (a: A, exit: Exit.Exit<A2, E2>) => Effect.Effect<X, never, R3>\n  ): Effect.Effect<A2, E | E2, R | R2 | R3>\n} = dual(3, <A, E, R, A2, E2, R2, X, R3>(\n  acquire: Effect.Effect<A, E, R>,\n  use: (a: A) => Effect.Effect<A2, E2, R2>,\n  release: (a: A, exit: Exit.Exit<A2, E2>) => Effect.Effect<X, never, R3>\n): Effect.Effect<A2, E | E2, R | R2 | R3> =>\n  uninterruptibleMask((restore) =>\n    flatMap(\n      acquire,\n      (a) =>\n        flatMap(exit(suspend(() => restore(use(a)))), (exit): Effect.Effect<A2, E | E2, R | R2 | R3> => {\n          return suspend(() => release(a, exit)).pipe(\n            matchCauseEffect({\n              onFailure: (cause) => {\n                switch (exit._tag) {\n                  case OpCodes.OP_FAILURE:\n                    return failCause(internalCause.sequential(exit.effect_instruction_i0, cause))\n                  case OpCodes.OP_SUCCESS:\n                    return failCause(cause)\n                }\n              },\n              onSuccess: () => exit\n            })\n          )\n        })\n    )\n  ))\n\n/* @internal */\nexport const as: {\n  <B>(value: B): <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<B, E, R>\n  <A, E, R, B>(self: Effect.Effect<A, E, R>, value: B): Effect.Effect<B, E, R>\n} = dual(\n  2,\n  <A, E, R, B>(self: Effect.Effect<A, E, R>, value: B): Effect.Effect<B, E, R> => flatMap(self, () => succeed(value))\n)\n\n/* @internal */\nexport const asVoid = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<void, E, R> => as(self, void 0)\n\n/* @internal */\nexport const custom: {\n  <X, A, E, R>(i0: X, body: (this: { effect_instruction_i0: X }) => Effect.Effect<A, E, R>): Effect.Effect<A, E, R>\n  <X, Y, A, E, R>(\n    i0: X,\n    i1: Y,\n    body: (this: { effect_instruction_i0: X; effect_instruction_i1: Y }) => Effect.Effect<A, E, R>\n  ): Effect.Effect<A, E, R>\n  <X, Y, Z, A, E, R>(\n    i0: X,\n    i1: Y,\n    i2: Z,\n    body: (\n      this: { effect_instruction_i0: X; effect_instruction_i1: Y; effect_instruction_i2: Z }\n    ) => Effect.Effect<A, E, R>\n  ): Effect.Effect<A, E, R>\n} = function() {\n  const wrapper = new EffectPrimitive(OpCodes.OP_COMMIT) as any\n  switch (arguments.length) {\n    case 2: {\n      wrapper.effect_instruction_i0 = arguments[0]\n      wrapper.commit = arguments[1]\n      break\n    }\n    case 3: {\n      wrapper.effect_instruction_i0 = arguments[0]\n      wrapper.effect_instruction_i1 = arguments[1]\n      wrapper.commit = arguments[2]\n      break\n    }\n    case 4: {\n      wrapper.effect_instruction_i0 = arguments[0]\n      wrapper.effect_instruction_i1 = arguments[1]\n      wrapper.effect_instruction_i2 = arguments[2]\n      wrapper.commit = arguments[3]\n      break\n    }\n    default: {\n      throw new Error(getBugErrorMessage(\"you're not supposed to end up here\"))\n    }\n  }\n  return wrapper\n}\n\n/* @internal */\nexport const unsafeAsync = <A, E = never, R = never>(\n  register: (\n    callback: (_: Effect.Effect<A, E, R>) => void\n  ) => void | Effect.Effect<void, never, R>,\n  blockingOn: FiberId.FiberId = FiberId.none\n): Effect.Effect<A, E, R> => {\n  const effect = new EffectPrimitive(OpCodes.OP_ASYNC) as any\n  let cancelerRef: Effect.Effect<void, never, R> | void = undefined\n  effect.effect_instruction_i0 = (resume: (_: Effect.Effect<A, E, R>) => void) => {\n    cancelerRef = register(resume)\n  }\n  effect.effect_instruction_i1 = blockingOn\n  return onInterrupt(effect, (_) => isEffect(cancelerRef) ? cancelerRef : void_)\n}\n\n/* @internal */\nexport const asyncInterrupt = <A, E = never, R = never>(\n  register: (\n    callback: (_: Effect.Effect<A, E, R>) => void\n  ) => void | Effect.Effect<void, never, R>,\n  blockingOn: FiberId.FiberId = FiberId.none\n): Effect.Effect<A, E, R> => suspend(() => unsafeAsync(register, blockingOn))\n\nconst async_ = <A, E = never, R = never>(\n  resume: (\n    callback: (_: Effect.Effect<A, E, R>) => void,\n    signal: AbortSignal\n  ) => void | Effect.Effect<void, never, R>,\n  blockingOn: FiberId.FiberId = FiberId.none\n): Effect.Effect<A, E, R> => {\n  return custom(resume, function() {\n    let backingResume: ((_: Effect.Effect<A, E, R>) => void) | undefined = undefined\n    let pendingEffect: Effect.Effect<A, E, R> | undefined = undefined\n    function proxyResume(effect: Effect.Effect<A, E, R>) {\n      if (backingResume) {\n        backingResume(effect)\n      } else if (pendingEffect === undefined) {\n        pendingEffect = effect\n      }\n    }\n    const effect = new EffectPrimitive(OpCodes.OP_ASYNC) as any\n    effect.effect_instruction_i0 = (resume: (_: Effect.Effect<A, E, R>) => void) => {\n      backingResume = resume\n      if (pendingEffect) {\n        resume(pendingEffect)\n      }\n    }\n    effect.effect_instruction_i1 = blockingOn\n    let cancelerRef: Effect.Effect<void, never, R> | void = undefined\n    let controllerRef: AbortController | void = undefined\n    if (this.effect_instruction_i0.length !== 1) {\n      controllerRef = new AbortController()\n      cancelerRef = internalCall(() => this.effect_instruction_i0(proxyResume, controllerRef!.signal))\n    } else {\n      cancelerRef = internalCall(() => (this.effect_instruction_i0 as any)(proxyResume))\n    }\n    return (cancelerRef || controllerRef) ?\n      onInterrupt(effect, (_) => {\n        if (controllerRef) {\n          controllerRef.abort()\n        }\n        return cancelerRef ?? void_\n      }) :\n      effect\n  })\n}\nexport {\n  /** @internal */\n  async_ as async\n}\n\n/* @internal */\nexport const catchAllCause = dual<\n  <E, A2, E2, R2>(\n    f: (cause: Cause.Cause<E>) => Effect.Effect<A2, E2, R2>\n  ) => <A, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A, E2, R2 | R>,\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    f: (cause: Cause.Cause<E>) => Effect.Effect<A2, E2, R2>\n  ) => Effect.Effect<A2 | A, E2, R2 | R>\n>(2, (self, f) => {\n  const effect = new EffectPrimitive(OpCodes.OP_ON_FAILURE) as any\n  effect.effect_instruction_i0 = self\n  effect.effect_instruction_i1 = f\n  return effect\n})\n\n/* @internal */\nexport const catchAll: {\n  <E, A2, E2, R2>(\n    f: (e: E) => Effect.Effect<A2, E2, R2>\n  ): <A, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A, E2, R2 | R>\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    f: (e: E) => Effect.Effect<A2, E2, R2>\n  ): Effect.Effect<A2 | A, E2, R2 | R>\n} = dual(\n  2,\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    f: (e: E) => Effect.Effect<A2, E2, R2>\n  ): Effect.Effect<A2 | A, E2, R2 | R> => matchEffect(self, { onFailure: f, onSuccess: succeed })\n)\n\n/* @internal */\nexport const catchIf: {\n  <E, EB extends E, A2, E2, R2>(\n    refinement: Refinement<NoInfer<E>, EB>,\n    f: (e: EB) => Effect.Effect<A2, E2, R2>\n  ): <A, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A, E2 | Exclude<E, EB>, R2 | R>\n  <E, A2, E2, R2>(\n    predicate: Predicate<NoInfer<E>>,\n    f: (e: NoInfer<E>) => Effect.Effect<A2, E2, R2>\n  ): <A, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A, E | E2, R2 | R>\n  <A, E, R, EB extends E, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    refinement: Refinement<E, EB>,\n    f: (e: EB) => Effect.Effect<A2, E2, R2>\n  ): Effect.Effect<A2 | A, E2 | Exclude<E, EB>, R2 | R>\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    predicate: Predicate<E>,\n    f: (e: E) => Effect.Effect<A2, E2, R2>\n  ): Effect.Effect<A | A2, E | E2, R | R2>\n} = dual(3, <A, E, R, A2, E2, R2>(\n  self: Effect.Effect<A, E, R>,\n  predicate: Predicate<E>,\n  f: (e: E) => Effect.Effect<A2, E2, R2>\n): Effect.Effect<A | A2, E | E2, R | R2> =>\n  catchAllCause(self, (cause): Effect.Effect<A | A2, E | E2, R | R2> => {\n    const either = internalCause.failureOrCause(cause)\n    switch (either._tag) {\n      case \"Left\":\n        return predicate(either.left) ? f(either.left) : failCause(cause)\n      case \"Right\":\n        return failCause(either.right)\n    }\n  }))\n\n/* @internal */\nexport const catchSome = dual<\n  <E, A2, E2, R2>(\n    pf: (e: NoInfer<E>) => Option.Option<Effect.Effect<A2, E2, R2>>\n  ) => <A, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A, E | E2, R2 | R>,\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    pf: (e: NoInfer<E>) => Option.Option<Effect.Effect<A2, E2, R2>>\n  ) => Effect.Effect<A2 | A, E | E2, R2 | R>\n>(2, <A, E, R, A2, E2, R2>(\n  self: Effect.Effect<A, E, R>,\n  pf: (e: NoInfer<E>) => Option.Option<Effect.Effect<A2, E2, R2>>\n) =>\n  catchAllCause(self, (cause): Effect.Effect<A2 | A, E | E2, R2 | R> => {\n    const either = internalCause.failureOrCause(cause)\n    switch (either._tag) {\n      case \"Left\":\n        return pipe(pf(either.left), Option.getOrElse(() => failCause(cause)))\n      case \"Right\":\n        return failCause(either.right)\n    }\n  }))\n\n/* @internal */\nexport const checkInterruptible = <A, E, R>(\n  f: (isInterruptible: boolean) => Effect.Effect<A, E, R>\n): Effect.Effect<A, E, R> => withFiberRuntime((_, status) => f(runtimeFlags_.interruption(status.runtimeFlags)))\n\nconst originalSymbol = Symbol.for(\"effect/OriginalAnnotation\")\n\n/* @internal */\nexport const originalInstance = <E>(obj: E): E => {\n  if (hasProperty(obj, originalSymbol)) {\n    // @ts-expect-error\n    return obj[originalSymbol]\n  }\n  return obj\n}\n\n/* @internal */\nexport const capture = <E>(obj: E & object, span: Option.Option<Tracer.Span>): E => {\n  if (Option.isSome(span)) {\n    return new Proxy(obj, {\n      has(target, p) {\n        return p === internalCause.spanSymbol || p === originalSymbol || p in target\n      },\n      get(target, p) {\n        if (p === internalCause.spanSymbol) {\n          return span.value\n        }\n        if (p === originalSymbol) {\n          return obj\n        }\n        // @ts-expect-error\n        return target[p]\n      }\n    })\n  }\n  return obj\n}\n\n/* @internal */\nexport const die = (defect: unknown): Effect.Effect<never> =>\n  isObject(defect) && !(internalCause.spanSymbol in defect) ?\n    withFiberRuntime((fiber) => failCause(internalCause.die(capture(defect, currentSpanFromFiber(fiber)))))\n    : failCause(internalCause.die(defect))\n\n/* @internal */\nexport const dieMessage = (message: string): Effect.Effect<never> =>\n  failCauseSync(() => internalCause.die(new RuntimeException(message)))\n\n/* @internal */\nexport const dieSync = (evaluate: LazyArg<unknown>): Effect.Effect<never> => flatMap(sync(evaluate), die)\n\n/* @internal */\nexport const either = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<Either.Either<A, E>, never, R> =>\n  matchEffect(self, {\n    onFailure: (e) => succeed(Either.left(e)),\n    onSuccess: (a) => succeed(Either.right(a))\n  })\n\n/* @internal */\nexport const exit = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<Exit.Exit<A, E>, never, R> =>\n  matchCause(self, {\n    onFailure: exitFailCause,\n    onSuccess: exitSucceed\n  })\n\n/* @internal */\nexport const fail = <E>(error: E): Effect.Effect<never, E> =>\n  isObject(error) && !(internalCause.spanSymbol in error) ?\n    withFiberRuntime((fiber) => failCause(internalCause.fail(capture(error, currentSpanFromFiber(fiber)))))\n    : failCause(internalCause.fail(error))\n\n/* @internal */\nexport const failSync = <E>(evaluate: LazyArg<E>): Effect.Effect<never, E> => flatMap(sync(evaluate), fail)\n\n/* @internal */\nexport const failCause = <E>(cause: Cause.Cause<E>): Effect.Effect<never, E> => {\n  const effect = new EffectPrimitiveFailure(OpCodes.OP_FAILURE) as any\n  effect.effect_instruction_i0 = cause\n  return effect\n}\n\n/* @internal */\nexport const failCauseSync = <E>(\n  evaluate: LazyArg<Cause.Cause<E>>\n): Effect.Effect<never, E> => flatMap(sync(evaluate), failCause)\n\n/* @internal */\nexport const fiberId: Effect.Effect<FiberId.FiberId> = withFiberRuntime((state) => succeed(state.id()))\n\n/* @internal */\nexport const fiberIdWith = <A, E, R>(\n  f: (descriptor: FiberId.Runtime) => Effect.Effect<A, E, R>\n): Effect.Effect<A, E, R> => withFiberRuntime((state) => f(state.id()))\n\n/* @internal */\nexport const flatMap = dual<\n  <A, B, E1, R1>(\n    f: (a: A) => Effect.Effect<B, E1, R1>\n  ) => <E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<B, E1 | E, R1 | R>,\n  <A, E, R, B, E1, R1>(\n    self: Effect.Effect<A, E, R>,\n    f: (a: A) => Effect.Effect<B, E1, R1>\n  ) => Effect.Effect<B, E | E1, R | R1>\n>(\n  2,\n  (self, f) => {\n    const effect = new EffectPrimitive(OpCodes.OP_ON_SUCCESS) as any\n    effect.effect_instruction_i0 = self\n    effect.effect_instruction_i1 = f\n    return effect\n  }\n)\n\n/* @internal */\nexport const andThen: {\n  <A, X>(\n    f: (a: NoInfer<A>) => X\n  ): <E, R>(\n    self: Effect.Effect<A, E, R>\n  ) => [X] extends [Effect.Effect<infer A1, infer E1, infer R1>] ? Effect.Effect<A1, E | E1, R | R1>\n    : [X] extends [PromiseLike<infer A1>] ? Effect.Effect<A1, E | Cause.UnknownException, R>\n    : Effect.Effect<X, E, R>\n  <X>(\n    f: NotFunction<X>\n  ): <A, E, R>(\n    self: Effect.Effect<A, E, R>\n  ) => [X] extends [Effect.Effect<infer A1, infer E1, infer R1>] ? Effect.Effect<A1, E | E1, R | R1>\n    : [X] extends [PromiseLike<infer A1>] ? Effect.Effect<A1, E | Cause.UnknownException, R>\n    : Effect.Effect<X, E, R>\n  <A, E, R, X>(\n    self: Effect.Effect<A, E, R>,\n    f: (a: NoInfer<A>) => X\n  ): [X] extends [Effect.Effect<infer A1, infer E1, infer R1>] ? Effect.Effect<A1, E | E1, R | R1>\n    : [X] extends [PromiseLike<infer A1>] ? Effect.Effect<A1, E | Cause.UnknownException, R>\n    : Effect.Effect<X, E, R>\n  <A, E, R, X>(\n    self: Effect.Effect<A, E, R>,\n    f: NotFunction<X>\n  ): [X] extends [Effect.Effect<infer A1, infer E1, infer R1>] ? Effect.Effect<A1, E | E1, R | R1>\n    : [X] extends [PromiseLike<infer A1>] ? Effect.Effect<A1, E | Cause.UnknownException, R>\n    : Effect.Effect<X, E, R>\n} = dual(2, (self, f) =>\n  flatMap(self, (a) => {\n    const b = typeof f === \"function\" ? (f as any)(a) : f\n    if (isEffect(b)) {\n      return b\n    } else if (isPromiseLike(b)) {\n      return unsafeAsync<any, Cause.UnknownException>((resume) => {\n        b.then((a) => resume(succeed(a)), (e) =>\n          resume(fail(new UnknownException(e, \"An unknown error occurred in Effect.andThen\"))))\n      })\n    }\n    return succeed(b)\n  }))\n\n/* @internal */\nexport const step = <A, E, R>(\n  self: Effect.Effect<A, E, R>\n): Effect.Effect<Exit.Exit<A, E> | Effect.Blocked<A, E>, never, R> => {\n  const effect = new EffectPrimitive(\"OnStep\") as any\n  effect.effect_instruction_i0 = self\n  return effect\n}\n\n/* @internal */\nexport const flatten = <A, E1, R1, E, R>(\n  self: Effect.Effect<Effect.Effect<A, E1, R1>, E, R>\n): Effect.Effect<A, E | E1, R | R1> => flatMap(self, identity)\n\n/* @internal */\nexport const flip = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<E, A, R> =>\n  matchEffect(self, { onFailure: succeed, onSuccess: fail })\n\n/* @internal */\nexport const matchCause: {\n  <E, A2, A, A3>(\n    options: {\n      readonly onFailure: (cause: Cause.Cause<E>) => A2\n      readonly onSuccess: (a: A) => A3\n    }\n  ): <R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A3, never, R>\n  <A, E, R, A2, A3>(\n    self: Effect.Effect<A, E, R>,\n    options: {\n      readonly onFailure: (cause: Cause.Cause<E>) => A2\n      readonly onSuccess: (a: A) => A3\n    }\n  ): Effect.Effect<A2 | A3, never, R>\n} = dual(2, <A, E, R, A2, A3>(\n  self: Effect.Effect<A, E, R>,\n  options: {\n    readonly onFailure: (cause: Cause.Cause<E>) => A2\n    readonly onSuccess: (a: A) => A3\n  }\n): Effect.Effect<A2 | A3, never, R> =>\n  matchCauseEffect(self, {\n    onFailure: (cause) => succeed(options.onFailure(cause)),\n    onSuccess: (a) => succeed(options.onSuccess(a))\n  }))\n\n/* @internal */\nexport const matchCauseEffect: {\n  <E, A2, E2, R2, A, A3, E3, R3>(\n    options: {\n      readonly onFailure: (cause: Cause.Cause<E>) => Effect.Effect<A2, E2, R2>\n      readonly onSuccess: (a: A) => Effect.Effect<A3, E3, R3>\n    }\n  ): <R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A3, E2 | E3, R2 | R3 | R>\n  <A, E, R, A2, E2, R2, A3, E3, R3>(\n    self: Effect.Effect<A, E, R>,\n    options: {\n      readonly onFailure: (cause: Cause.Cause<E>) => Effect.Effect<A2, E2, R2>\n      readonly onSuccess: (a: A) => Effect.Effect<A3, E3, R3>\n    }\n  ): Effect.Effect<A2 | A3, E2 | E3, R2 | R3 | R>\n} = dual(2, <A, E, R, A2, E2, R2, A3, E3, R3>(\n  self: Effect.Effect<A, E, R>,\n  options: {\n    readonly onFailure: (cause: Cause.Cause<E>) => Effect.Effect<A2, E2, R2>\n    readonly onSuccess: (a: A) => Effect.Effect<A3, E3, R3>\n  }\n): Effect.Effect<A2 | A3, E2 | E3, R2 | R3 | R> => {\n  const effect = new EffectPrimitive(OpCodes.OP_ON_SUCCESS_AND_FAILURE) as any\n  effect.effect_instruction_i0 = self\n  effect.effect_instruction_i1 = options.onFailure\n  effect.effect_instruction_i2 = options.onSuccess\n  return effect\n})\n\n/* @internal */\nexport const matchEffect: {\n  <E, A2, E2, R2, A, A3, E3, R3>(\n    options: {\n      readonly onFailure: (e: E) => Effect.Effect<A2, E2, R2>\n      readonly onSuccess: (a: A) => Effect.Effect<A3, E3, R3>\n    }\n  ): <R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A3, E2 | E3, R2 | R3 | R>\n  <A, E, R, A2, E2, R2, A3, E3, R3>(\n    self: Effect.Effect<A, E, R>,\n    options: {\n      readonly onFailure: (e: E) => Effect.Effect<A2, E2, R2>\n      readonly onSuccess: (a: A) => Effect.Effect<A3, E3, R3>\n    }\n  ): Effect.Effect<A2 | A3, E2 | E3, R2 | R3 | R>\n} = dual(2, <A, E, R, A2, E2, R2, A3, E3, R3>(\n  self: Effect.Effect<A, E, R>,\n  options: {\n    readonly onFailure: (e: E) => Effect.Effect<A2, E2, R2>\n    readonly onSuccess: (a: A) => Effect.Effect<A3, E3, R3>\n  }\n): Effect.Effect<A2 | A3, E2 | E3, R2 | R3 | R> =>\n  matchCauseEffect(self, {\n    onFailure: (cause) => {\n      const defects = internalCause.defects(cause)\n      if (defects.length > 0) {\n        return failCause(internalCause.electFailures(cause))\n      }\n      const failures = internalCause.failures(cause)\n      if (failures.length > 0) {\n        return options.onFailure(Chunk.unsafeHead(failures))\n      }\n      return failCause(cause as Cause.Cause<never>)\n    },\n    onSuccess: options.onSuccess\n  }))\n\n/* @internal */\nexport const forEachSequential: {\n  <A, B, E, R>(f: (a: A, i: number) => Effect.Effect<B, E, R>): (self: Iterable<A>) => Effect.Effect<Array<B>, E, R>\n  <A, B, E, R>(self: Iterable<A>, f: (a: A, i: number) => Effect.Effect<B, E, R>): Effect.Effect<Array<B>, E, R>\n} = dual(\n  2,\n  <A, B, E, R>(self: Iterable<A>, f: (a: A, i: number) => Effect.Effect<B, E, R>): Effect.Effect<Array<B>, E, R> =>\n    suspend(() => {\n      const arr = Arr.fromIterable(self)\n      const ret = Arr.allocate<B>(arr.length)\n      let i = 0\n      return as(\n        whileLoop({\n          while: () => i < arr.length,\n          body: () => f(arr[i], i),\n          step: (b) => {\n            ret[i++] = b\n          }\n        }),\n        ret as Array<B>\n      )\n    })\n)\n\n/* @internal */\nexport const forEachSequentialDiscard: {\n  <A, B, E, R>(f: (a: A, i: number) => Effect.Effect<B, E, R>): (self: Iterable<A>) => Effect.Effect<void, E, R>\n  <A, B, E, R>(self: Iterable<A>, f: (a: A, i: number) => Effect.Effect<B, E, R>): Effect.Effect<void, E, R>\n} = dual(\n  2,\n  <A, B, E, R>(self: Iterable<A>, f: (a: A, i: number) => Effect.Effect<B, E, R>): Effect.Effect<void, E, R> =>\n    suspend(() => {\n      const arr = Arr.fromIterable(self)\n      let i = 0\n      return whileLoop({\n        while: () => i < arr.length,\n        body: () => f(arr[i], i),\n        step: () => {\n          i++\n        }\n      })\n    })\n)\n\n/* @internal */\nexport const if_ = dual<\n  <A1, E1, R1, A2, E2, R2>(\n    options: {\n      readonly onTrue: LazyArg<Effect.Effect<A1, E1, R1>>\n      readonly onFalse: LazyArg<Effect.Effect<A2, E2, R2>>\n    }\n  ) => <E = never, R = never>(\n    self: Effect.Effect<boolean, E, R> | boolean\n  ) => Effect.Effect<A1 | A2, E | E1 | E2, R | R1 | R2>,\n  <A1, E1, R1, A2, E2, R2, E = never, R = never>(\n    self: Effect.Effect<boolean, E, R> | boolean,\n    options: {\n      readonly onTrue: LazyArg<Effect.Effect<A1, E1, R1>>\n      readonly onFalse: LazyArg<Effect.Effect<A2, E2, R2>>\n    }\n  ) => Effect.Effect<A1 | A2, E1 | E2 | E, R1 | R2 | R>\n>(\n  (args) => typeof args[0] === \"boolean\" || isEffect(args[0]),\n  <A1, E1, R1, A2, E2, R2, E = never, R = never>(\n    self: Effect.Effect<boolean, E, R> | boolean,\n    options: {\n      readonly onTrue: LazyArg<Effect.Effect<A1, E1, R1>>\n      readonly onFalse: LazyArg<Effect.Effect<A2, E2, R2>>\n    }\n  ): Effect.Effect<A1 | A2, E1 | E2 | E, R1 | R2 | R> =>\n    isEffect(self)\n      ? flatMap(self, (b): Effect.Effect<A1 | A2, E1 | E2, R1 | R2> => (b ? options.onTrue() : options.onFalse()))\n      : self\n      ? options.onTrue()\n      : options.onFalse()\n)\n\n/* @internal */\nexport const interrupt: Effect.Effect<never> = flatMap(fiberId, (fiberId) => interruptWith(fiberId))\n\n/* @internal */\nexport const interruptWith = (fiberId: FiberId.FiberId): Effect.Effect<never> =>\n  failCause(internalCause.interrupt(fiberId))\n\n/* @internal */\nexport const interruptible = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<A, E, R> => {\n  const effect = new EffectPrimitive(OpCodes.OP_UPDATE_RUNTIME_FLAGS) as any\n  effect.effect_instruction_i0 = RuntimeFlagsPatch.enable(runtimeFlags_.Interruption)\n  effect.effect_instruction_i1 = () => self\n  return effect\n}\n\n/* @internal */\nexport const interruptibleMask = <A, E, R>(\n  f: (restore: <AX, EX, RX>(effect: Effect.Effect<AX, EX, RX>) => Effect.Effect<AX, EX, RX>) => Effect.Effect<A, E, R>\n): Effect.Effect<A, E, R> =>\n  custom(f, function() {\n    const effect = new EffectPrimitive(OpCodes.OP_UPDATE_RUNTIME_FLAGS) as any\n    effect.effect_instruction_i0 = RuntimeFlagsPatch.enable(runtimeFlags_.Interruption)\n    effect.effect_instruction_i1 = (oldFlags: RuntimeFlags.RuntimeFlags) =>\n      runtimeFlags_.interruption(oldFlags)\n        ? internalCall(() => this.effect_instruction_i0(interruptible))\n        : internalCall(() => this.effect_instruction_i0(uninterruptible))\n    return effect\n  })\n\n/* @internal */\nexport const intoDeferred: {\n  <A, E>(deferred: Deferred.Deferred<A, E>): <R>(self: Effect.Effect<A, E, R>) => Effect.Effect<boolean, never, R>\n  <A, E, R>(self: Effect.Effect<A, E, R>, deferred: Deferred.Deferred<A, E>): Effect.Effect<boolean, never, R>\n} = dual(\n  2,\n  <A, E, R>(self: Effect.Effect<A, E, R>, deferred: Deferred.Deferred<A, E>): Effect.Effect<boolean, never, R> =>\n    uninterruptibleMask((restore) =>\n      flatMap(\n        exit(restore(self)),\n        (exit) => deferredDone(deferred, exit)\n      )\n    )\n)\n\n/* @internal */\nexport const map: {\n  <A, B>(f: (a: A) => B): <E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<B, E, R>\n  <A, E, R, B>(self: Effect.Effect<A, E, R>, f: (a: A) => B): Effect.Effect<B, E, R>\n} = dual(\n  2,\n  <A, E, R, B>(self: Effect.Effect<A, E, R>, f: (a: A) => B): Effect.Effect<B, E, R> =>\n    flatMap(self, (a) => sync(() => f(a)))\n)\n\n/* @internal */\nexport const mapBoth: {\n  <E, E2, A, A2>(\n    options: { readonly onFailure: (e: E) => E2; readonly onSuccess: (a: A) => A2 }\n  ): <R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2, E2, R>\n  <A, E, R, E2, A2>(\n    self: Effect.Effect<A, E, R>,\n    options: { readonly onFailure: (e: E) => E2; readonly onSuccess: (a: A) => A2 }\n  ): Effect.Effect<A2, E2, R>\n} = dual(2, <A, E, R, E2, A2>(\n  self: Effect.Effect<A, E, R>,\n  options: { readonly onFailure: (e: E) => E2; readonly onSuccess: (a: A) => A2 }\n): Effect.Effect<A2, E2, R> =>\n  matchEffect(self, {\n    onFailure: (e) => failSync(() => options.onFailure(e)),\n    onSuccess: (a) => sync(() => options.onSuccess(a))\n  }))\n\n/* @internal */\nexport const mapError: {\n  <E, E2>(f: (e: E) => E2): <A, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E2, R>\n  <A, E, R, E2>(self: Effect.Effect<A, E, R>, f: (e: E) => E2): Effect.Effect<A, E2, R>\n} = dual(\n  2,\n  <A, E, R, E2>(self: Effect.Effect<A, E, R>, f: (e: E) => E2): Effect.Effect<A, E2, R> =>\n    matchCauseEffect(self, {\n      onFailure: (cause) => {\n        const either = internalCause.failureOrCause(cause)\n        switch (either._tag) {\n          case \"Left\": {\n            return failSync(() => f(either.left))\n          }\n          case \"Right\": {\n            return failCause(either.right)\n          }\n        }\n      },\n      onSuccess: succeed\n    })\n)\n\n/* @internal */\nexport const onError: {\n  <E, X, R2>(\n    cleanup: (cause: Cause.Cause<E>) => Effect.Effect<X, never, R2>\n  ): <A, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R2 | R>\n  <A, E, R, X, R2>(\n    self: Effect.Effect<A, E, R>,\n    cleanup: (cause: Cause.Cause<E>) => Effect.Effect<X, never, R2>\n  ): Effect.Effect<A, E, R2 | R>\n} = dual(2, <A, E, R, X, R2>(\n  self: Effect.Effect<A, E, R>,\n  cleanup: (cause: Cause.Cause<E>) => Effect.Effect<X, never, R2>\n): Effect.Effect<A, E, R2 | R> =>\n  onExit(self, (exit) => exitIsSuccess(exit) ? void_ : cleanup(exit.effect_instruction_i0)))\n\n/* @internal */\nexport const onExit: {\n  <A, E, X, R2>(\n    cleanup: (exit: Exit.Exit<A, E>) => Effect.Effect<X, never, R2>\n  ): <R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R2 | R>\n  <A, E, R, X, R2>(\n    self: Effect.Effect<A, E, R>,\n    cleanup: (exit: Exit.Exit<A, E>) => Effect.Effect<X, never, R2>\n  ): Effect.Effect<A, E, R2 | R>\n} = dual(2, <A, E, R, X, R2>(\n  self: Effect.Effect<A, E, R>,\n  cleanup: (exit: Exit.Exit<A, E>) => Effect.Effect<X, never, R2>\n): Effect.Effect<A, E, R2 | R> =>\n  uninterruptibleMask((restore) =>\n    matchCauseEffect(restore(self), {\n      onFailure: (cause1) => {\n        const result = exitFailCause(cause1)\n        return matchCauseEffect(cleanup(result), {\n          onFailure: (cause2) => exitFailCause(internalCause.sequential(cause1, cause2)),\n          onSuccess: () => result\n        })\n      },\n      onSuccess: (success) => {\n        const result = exitSucceed(success)\n        return zipRight(cleanup(result), result)\n      }\n    })\n  ))\n\n/* @internal */\nexport const onInterrupt: {\n  <X, R2>(\n    cleanup: (interruptors: HashSet.HashSet<FiberId.FiberId>) => Effect.Effect<X, never, R2>\n  ): <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R2 | R>\n  <A, E, R, X, R2>(\n    self: Effect.Effect<A, E, R>,\n    cleanup: (interruptors: HashSet.HashSet<FiberId.FiberId>) => Effect.Effect<X, never, R2>\n  ): Effect.Effect<A, E, R2 | R>\n} = dual(2, <A, E, R, X, R2>(\n  self: Effect.Effect<A, E, R>,\n  cleanup: (interruptors: HashSet.HashSet<FiberId.FiberId>) => Effect.Effect<X, never, R2>\n): Effect.Effect<A, E, R2 | R> =>\n  onExit(\n    self,\n    exitMatch({\n      onFailure: (cause) =>\n        internalCause.isInterruptedOnly(cause)\n          ? asVoid(cleanup(internalCause.interruptors(cause)))\n          : void_,\n      onSuccess: () => void_\n    })\n  ))\n\n/* @internal */\nexport const orElse: {\n  <A2, E2, R2>(\n    that: LazyArg<Effect.Effect<A2, E2, R2>>\n  ): <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A, E2, R2 | R>\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    that: LazyArg<Effect.Effect<A2, E2, R2>>\n  ): Effect.Effect<A2 | A, E2, R2 | R>\n} = dual(\n  2,\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    that: LazyArg<Effect.Effect<A2, E2, R2>>\n  ): Effect.Effect<A2 | A, E2, R2 | R> => attemptOrElse(self, that, succeed)\n)\n\n/* @internal */\nexport const orDie = <A, E, R>(self: Effect.Effect<A, E, R>): Effect.Effect<A, never, R> => orDieWith(self, identity)\n\n/* @internal */\nexport const orDieWith: {\n  <E>(f: (error: E) => unknown): <A, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, never, R>\n  <A, E, R>(self: Effect.Effect<A, E, R>, f: (error: E) => unknown): Effect.Effect<A, never, R>\n} = dual(\n  2,\n  <A, E, R>(self: Effect.Effect<A, E, R>, f: (error: E) => unknown): Effect.Effect<A, never, R> =>\n    matchEffect(self, {\n      onFailure: (e) => die(f(e)),\n      onSuccess: succeed\n    })\n)\n\n/* @internal */\nexport const partitionMap: <A, A1, A2>(\n  elements: Iterable<A>,\n  f: (a: A) => Either.Either<A2, A1>\n) => [left: Array<A1>, right: Array<A2>] = Arr.partitionMap\n/* @internal */\nexport const runtimeFlags: Effect.Effect<RuntimeFlags.RuntimeFlags> = withFiberRuntime((_, status) =>\n  succeed(status.runtimeFlags)\n)\n\n/* @internal */\nexport const succeed = <A>(value: A): Effect.Effect<A> => {\n  const effect = new EffectPrimitiveSuccess(OpCodes.OP_SUCCESS) as any\n  effect.effect_instruction_i0 = value\n  return effect\n}\n\n/* @internal */\nexport const suspend = <A, E, R>(evaluate: LazyArg<Effect.Effect<A, E, R>>): Effect.Effect<A, E, R> => {\n  const effect = new EffectPrimitive(OpCodes.OP_COMMIT) as any\n  effect.commit = evaluate\n  return effect\n}\n\n/* @internal */\nexport const sync = <A>(thunk: LazyArg<A>): Effect.Effect<A> => {\n  const effect = new EffectPrimitive(OpCodes.OP_SYNC) as any\n  effect.effect_instruction_i0 = thunk\n  return effect\n}\n\n/* @internal */\nexport const tap = dual<\n  {\n    <A, X>(\n      f: (a: NoInfer<A>) => X\n    ): <E, R>(\n      self: Effect.Effect<A, E, R>\n    ) => [X] extends [Effect.Effect<infer _A1, infer E1, infer R1>] ? Effect.Effect<A, E | E1, R | R1>\n      : [X] extends [PromiseLike<infer _A1>] ? Effect.Effect<A, E | Cause.UnknownException, R>\n      : Effect.Effect<A, E, R>\n    <A, X, E1, R1>(\n      f: (a: NoInfer<A>) => Effect.Effect<X, E1, R1>,\n      options: { onlyEffect: true }\n    ): <E, R>(\n      self: Effect.Effect<A, E, R>\n    ) => Effect.Effect<A, E | E1, R | R1>\n    <X>(\n      f: NotFunction<X>\n    ): <A, E, R>(\n      self: Effect.Effect<A, E, R>\n    ) => [X] extends [Effect.Effect<infer _A1, infer E1, infer R1>] ? Effect.Effect<A, E | E1, R | R1>\n      : [X] extends [PromiseLike<infer _A1>] ? Effect.Effect<A, E | Cause.UnknownException, R>\n      : Effect.Effect<A, E, R>\n    <X, E1, R1>(\n      f: Effect.Effect<X, E1, R1>,\n      options: { onlyEffect: true }\n    ): <A, E, R>(\n      self: Effect.Effect<A, E, R>\n    ) => Effect.Effect<A, E | E1, R | R1>\n  },\n  {\n    <A, E, R, X>(\n      self: Effect.Effect<A, E, R>,\n      f: (a: NoInfer<A>) => X\n    ): [X] extends [Effect.Effect<infer _A1, infer E1, infer R1>] ? Effect.Effect<A, E | E1, R | R1>\n      : [X] extends [PromiseLike<infer _A1>] ? Effect.Effect<A, E | Cause.UnknownException, R>\n      : Effect.Effect<A, E, R>\n    <A, E, R, X, E1, R1>(\n      self: Effect.Effect<A, E, R>,\n      f: (a: NoInfer<A>) => Effect.Effect<X, E1, R1>,\n      options: { onlyEffect: true }\n    ): Effect.Effect<A, E | E1, R | R1>\n    <A, E, R, X>(\n      self: Effect.Effect<A, E, R>,\n      f: NotFunction<X>\n    ): [X] extends [Effect.Effect<infer _A1, infer E1, infer R1>] ? Effect.Effect<A, E | E1, R | R1>\n      : [X] extends [PromiseLike<infer _A1>] ? Effect.Effect<A, E | Cause.UnknownException, R>\n      : Effect.Effect<A, E, R>\n    <A, E, R, X, E1, R1>(\n      self: Effect.Effect<A, E, R>,\n      f: Effect.Effect<X, E1, R1>,\n      options: { onlyEffect: true }\n    ): Effect.Effect<A, E | E1, R | R1>\n  }\n>(\n  (args) => args.length === 3 || args.length === 2 && !(isObject(args[1]) && \"onlyEffect\" in args[1]),\n  <A, E, R, X>(self: Effect.Effect<A, E, R>, f: X) =>\n    flatMap(self, (a) => {\n      const b = typeof f === \"function\" ? (f as any)(a) : f\n      if (isEffect(b)) {\n        return as(b, a)\n      } else if (isPromiseLike(b)) {\n        return unsafeAsync<any, Cause.UnknownException>((resume) => {\n          b.then((_) => resume(succeed(a)), (e) =>\n            resume(fail(new UnknownException(e, \"An unknown error occurred in Effect.tap\"))))\n        })\n      }\n      return succeed(a)\n    })\n)\n\n/* @internal */\nexport const transplant = <A, E, R>(\n  f: (grafter: <A2, E2, R2>(effect: Effect.Effect<A2, E2, R2>) => Effect.Effect<A2, E2, R2>) => Effect.Effect<A, E, R>\n): Effect.Effect<A, E, R> =>\n  withFiberRuntime<A, E, R>((state) => {\n    const scopeOverride = state.getFiberRef(currentForkScopeOverride)\n    const scope = pipe(scopeOverride, Option.getOrElse(() => state.scope()))\n    return f(fiberRefLocally(currentForkScopeOverride, Option.some(scope)))\n  })\n\n/* @internal */\nexport const attemptOrElse: {\n  <A2, E2, R2, A, A3, E3, R3>(\n    that: LazyArg<Effect.Effect<A2, E2, R2>>,\n    onSuccess: (a: A) => Effect.Effect<A3, E3, R3>\n  ): <E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2 | A3, E2 | E3, R | R2 | R3>\n  <A, E, R, A2, E2, R2, A3, E3, R3>(\n    self: Effect.Effect<A, E, R>,\n    that: LazyArg<Effect.Effect<A2, E2, R2>>,\n    onSuccess: (a: A) => Effect.Effect<A3, E3, R3>\n  ): Effect.Effect<A2 | A3, E2 | E3, R | R2 | R3>\n} = dual(3, <A, E, R, A2, E2, R2, A3, E3, R3>(\n  self: Effect.Effect<A, E, R>,\n  that: LazyArg<Effect.Effect<A2, E2, R2>>,\n  onSuccess: (a: A) => Effect.Effect<A3, E3, R3>\n): Effect.Effect<A2 | A3, E2 | E3, R | R2 | R3> =>\n  matchCauseEffect(self, {\n    onFailure: (cause) => {\n      const defects = internalCause.defects(cause)\n      if (defects.length > 0) {\n        return failCause(Option.getOrThrow(internalCause.keepDefectsAndElectFailures(cause)))\n      }\n      return that()\n    },\n    onSuccess\n  }))\n\n/* @internal */\nexport const uninterruptible: <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R> = <A, E, R>(\n  self: Effect.Effect<A, E, R>\n): Effect.Effect<A, E, R> => {\n  const effect = new EffectPrimitive(OpCodes.OP_UPDATE_RUNTIME_FLAGS) as any\n  effect.effect_instruction_i0 = RuntimeFlagsPatch.disable(runtimeFlags_.Interruption)\n  effect.effect_instruction_i1 = () => self\n  return effect\n}\n\n/* @internal */\nexport const uninterruptibleMask = <A, E, R>(\n  f: (restore: <AX, EX, RX>(effect: Effect.Effect<AX, EX, RX>) => Effect.Effect<AX, EX, RX>) => Effect.Effect<A, E, R>\n): Effect.Effect<A, E, R> =>\n  custom(f, function() {\n    const effect = new EffectPrimitive(OpCodes.OP_UPDATE_RUNTIME_FLAGS) as any\n    effect.effect_instruction_i0 = RuntimeFlagsPatch.disable(runtimeFlags_.Interruption)\n    effect.effect_instruction_i1 = (oldFlags: RuntimeFlags.RuntimeFlags) =>\n      runtimeFlags_.interruption(oldFlags)\n        ? internalCall(() => this.effect_instruction_i0(interruptible))\n        : internalCall(() => this.effect_instruction_i0(uninterruptible))\n    return effect\n  })\n\nconst void_: Effect.Effect<void> = succeed(void 0)\nexport {\n  /* @internal */\n  void_ as void\n}\n\n/* @internal */\nexport const updateRuntimeFlags = (patch: RuntimeFlagsPatch.RuntimeFlagsPatch): Effect.Effect<void> => {\n  const effect = new EffectPrimitive(OpCodes.OP_UPDATE_RUNTIME_FLAGS) as any\n  effect.effect_instruction_i0 = patch\n  effect.effect_instruction_i1 = void 0\n  return effect\n}\n\n/* @internal */\nexport const whenEffect: {\n  <E, R>(\n    condition: Effect.Effect<boolean, E, R>\n  ): <A, E2, R2>(\n    effect: Effect.Effect<A, E2, R2>\n  ) => Effect.Effect<Option.Option<A>, E | E2, R | R2>\n  <A, E2, R2, E, R>(\n    self: Effect.Effect<A, E2, R2>,\n    condition: Effect.Effect<boolean, E, R>\n  ): Effect.Effect<Option.Option<A>, E | E2, R | R2>\n} = dual(2, <A, E2, R2, E, R>(\n  self: Effect.Effect<A, E2, R2>,\n  condition: Effect.Effect<boolean, E, R>\n): Effect.Effect<Option.Option<A>, E | E2, R | R2> =>\n  flatMap(condition, (b) => {\n    if (b) {\n      return pipe(self, map(Option.some))\n    }\n    return succeed(Option.none())\n  }))\n\n/* @internal */\nexport const whileLoop = <A, E, R>(\n  options: {\n    readonly while: LazyArg<boolean>\n    readonly body: LazyArg<Effect.Effect<A, E, R>>\n    readonly step: (a: A) => void\n  }\n): Effect.Effect<void, E, R> => {\n  const effect = new EffectPrimitive(OpCodes.OP_WHILE) as any\n  effect.effect_instruction_i0 = options.while\n  effect.effect_instruction_i1 = options.body\n  effect.effect_instruction_i2 = options.step\n  return effect\n}\n\n/* @internal */\nexport const fromIterator = <Eff extends YieldWrap<Effect.Effect<any, any, any>>, AEff>(\n  iterator: LazyArg<Iterator<Eff, AEff, never>>\n): Effect.Effect<\n  AEff,\n  [Eff] extends [never] ? never : [Eff] extends [YieldWrap<Effect.Effect<infer _A, infer E, infer _R>>] ? E : never,\n  [Eff] extends [never] ? never : [Eff] extends [YieldWrap<Effect.Effect<infer _A, infer _E, infer R>>] ? R : never\n> =>\n  suspend(() => {\n    const effect = new EffectPrimitive(OpCodes.OP_ITERATOR) as any\n    effect.effect_instruction_i0 = iterator()\n    return effect\n  })\n\n/* @internal */\nexport const gen: typeof Effect.gen = function() {\n  const f = arguments.length === 1 ? arguments[0] : arguments[1].bind(arguments[0])\n  return fromIterator(() => f(pipe))\n}\n\n/** @internal */\nexport const fnUntraced: Effect.fn.Untraced = (body: Function, ...pipeables: Array<any>) =>\n  Object.defineProperty(\n    pipeables.length === 0\n      ? function(this: any, ...args: Array<any>) {\n        return fromIterator(() => body.apply(this, args))\n      }\n      : function(this: any, ...args: Array<any>) {\n        let effect = fromIterator(() => body.apply(this, args))\n        for (const x of pipeables) {\n          effect = x(effect, ...args)\n        }\n        return effect\n      },\n    \"length\",\n    { value: body.length, configurable: true }\n  )\n\n/* @internal */\nexport const withConcurrency = dual<\n  (concurrency: number | \"unbounded\") => <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R>,\n  <A, E, R>(self: Effect.Effect<A, E, R>, concurrency: number | \"unbounded\") => Effect.Effect<A, E, R>\n>(2, (self, concurrency) => fiberRefLocally(self, currentConcurrency, concurrency))\n\n/* @internal */\nexport const withRequestBatching = dual<\n  (requestBatching: boolean) => <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R>,\n  <A, E, R>(self: Effect.Effect<A, E, R>, requestBatching: boolean) => Effect.Effect<A, E, R>\n>(2, (self, requestBatching) => fiberRefLocally(self, currentRequestBatching, requestBatching))\n\n/* @internal */\nexport const withRuntimeFlags = dual<\n  (update: RuntimeFlagsPatch.RuntimeFlagsPatch) => <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R>,\n  <A, E, R>(self: Effect.Effect<A, E, R>, update: RuntimeFlagsPatch.RuntimeFlagsPatch) => Effect.Effect<A, E, R>\n>(2, (self, update) => {\n  const effect = new EffectPrimitive(OpCodes.OP_UPDATE_RUNTIME_FLAGS) as any\n  effect.effect_instruction_i0 = update\n  effect.effect_instruction_i1 = () => self\n  return effect\n})\n\n/** @internal */\nexport const withTracerEnabled = dual<\n  (enabled: boolean) => <A, E, R>(effect: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R>,\n  <A, E, R>(effect: Effect.Effect<A, E, R>, enabled: boolean) => Effect.Effect<A, E, R>\n>(2, (effect, enabled) =>\n  fiberRefLocally(\n    effect,\n    currentTracerEnabled,\n    enabled\n  ))\n\n/** @internal */\nexport const withTracerTiming = dual<\n  (enabled: boolean) => <A, E, R>(effect: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R>,\n  <A, E, R>(effect: Effect.Effect<A, E, R>, enabled: boolean) => Effect.Effect<A, E, R>\n>(2, (effect, enabled) =>\n  fiberRefLocally(\n    effect,\n    currentTracerTimingEnabled,\n    enabled\n  ))\n\n/* @internal */\nexport const yieldNow = (options?: {\n  readonly priority?: number | undefined\n}): Effect.Effect<void> => {\n  const effect = new EffectPrimitive(OpCodes.OP_YIELD) as any\n  return typeof options?.priority !== \"undefined\" ?\n    withSchedulingPriority(effect, options.priority) :\n    effect\n}\n\n/* @internal */\nexport const zip = dual<\n  <A2, E2, R2>(\n    that: Effect.Effect<A2, E2, R2>\n  ) => <A, E, R>(\n    self: Effect.Effect<A, E, R>\n  ) => Effect.Effect<[A, A2], E | E2, R | R2>,\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<A2, E2, R2>\n  ) => Effect.Effect<[A, A2], E | E2, R | R2>\n>(2, <A, E, R, A2, E2, R2>(\n  self: Effect.Effect<A, E, R>,\n  that: Effect.Effect<A2, E2, R2>\n): Effect.Effect<[A, A2], E | E2, R | R2> => flatMap(self, (a) => map(that, (b) => [a, b])))\n\n/* @internal */\nexport const zipFlatten: {\n  <A2, E2, R2>(\n    that: Effect.Effect<A2, E2, R2>\n  ): <A extends ReadonlyArray<any>, E, R>(\n    self: Effect.Effect<A, E, R>\n  ) => Effect.Effect<[...A, A2], E | E2, R | R2>\n  <A extends ReadonlyArray<any>, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<A2, E2, R2>\n  ): Effect.Effect<[...A, A2], E | E2, R | R2>\n} = dual(2, <A extends ReadonlyArray<any>, E, R, A2, E2, R2>(\n  self: Effect.Effect<A, E, R>,\n  that: Effect.Effect<A2, E2, R2>\n): Effect.Effect<[...A, A2], E | E2, R | R2> => flatMap(self, (a) => map(that, (b) => [...a, b])))\n\n/* @internal */\nexport const zipLeft: {\n  <A2, E2, R2>(\n    that: Effect.Effect<A2, E2, R2>\n  ): <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E | E2, R | R2>\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<A2, E2, R2>\n  ): Effect.Effect<A, E | E2, R | R2>\n} = dual(2, <A, E, R, A2, E2, R2>(\n  self: Effect.Effect<A, E, R>,\n  that: Effect.Effect<A2, E2, R2>\n): Effect.Effect<A, E | E2, R | R2> => flatMap(self, (a) => as(that, a)))\n\n/* @internal */\nexport const zipRight: {\n  <A2, E2, R2>(\n    that: Effect.Effect<A2, E2, R2>\n  ): <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A2, E | E2, R | R2>\n  <A, E, R, A2, E2, R2>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<A2, E2, R2>\n  ): Effect.Effect<A2, E | E2, R | R2>\n} = dual(2, <A, E, R, A2, E2, R2>(\n  self: Effect.Effect<A, E, R>,\n  that: Effect.Effect<A2, E2, R2>\n): Effect.Effect<A2, E | E2, R | R2> => flatMap(self, () => that))\n\n/* @internal */\nexport const zipWith: {\n  <A2, E2, R2, A, B>(\n    that: Effect.Effect<A2, E2, R2>,\n    f: (a: A, b: A2) => B\n  ): <E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<B, E | E2, R | R2>\n  <A, E, R, A2, E2, R2, B>(\n    self: Effect.Effect<A, E, R>,\n    that: Effect.Effect<A2, E2, R2>,\n    f: (a: A, b: A2) => B\n  ): Effect.Effect<B, E | E2, R | R2>\n} = dual(3, <A, E, R, A2, E2, R2, B>(\n  self: Effect.Effect<A, E, R>,\n  that: Effect.Effect<A2, E2, R2>,\n  f: (a: A, b: A2) => B\n): Effect.Effect<B, E | E2, R | R2> => flatMap(self, (a) => map(that, (b) => f(a, b))))\n\n/* @internal */\nexport const never: Effect.Effect<never> = asyncInterrupt<never>(() => {\n  const interval = setInterval(() => {\n    //\n  }, 2 ** 31 - 1)\n  return sync(() => clearInterval(interval))\n})\n\n// -----------------------------------------------------------------------------\n// Fiber\n// -----------------------------------------------------------------------------\n\n/* @internal */\nexport const interruptFiber = <A, E>(self: Fiber.Fiber<A, E>): Effect.Effect<Exit.Exit<A, E>> =>\n  flatMap(fiberId, (fiberId) => pipe(self, interruptAsFiber(fiberId)))\n\n/* @internal */\nexport const interruptAsFiber = dual<\n  (fiberId: FiberId.FiberId) => <A, E>(self: Fiber.Fiber<A, E>) => Effect.Effect<Exit.Exit<A, E>>,\n  <A, E>(self: Fiber.Fiber<A, E>, fiberId: FiberId.FiberId) => Effect.Effect<Exit.Exit<A, E>>\n>(2, (self, fiberId) => flatMap(self.interruptAsFork(fiberId), () => self.await))\n\n// -----------------------------------------------------------------------------\n// LogLevel\n// -----------------------------------------------------------------------------\n\n/** @internal */\nexport const logLevelAll: LogLevel.LogLevel = {\n  _tag: \"All\",\n  syslog: 0,\n  label: \"ALL\",\n  ordinal: Number.MIN_SAFE_INTEGER,\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const logLevelFatal: LogLevel.LogLevel = {\n  _tag: \"Fatal\",\n  syslog: 2,\n  label: \"FATAL\",\n  ordinal: 50000,\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const logLevelError: LogLevel.LogLevel = {\n  _tag: \"Error\",\n  syslog: 3,\n  label: \"ERROR\",\n  ordinal: 40000,\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const logLevelWarning: LogLevel.LogLevel = {\n  _tag: \"Warning\",\n  syslog: 4,\n  label: \"WARN\",\n  ordinal: 30000,\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const logLevelInfo: LogLevel.LogLevel = {\n  _tag: \"Info\",\n  syslog: 6,\n  label: \"INFO\",\n  ordinal: 20000,\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const logLevelDebug: LogLevel.LogLevel = {\n  _tag: \"Debug\",\n  syslog: 7,\n  label: \"DEBUG\",\n  ordinal: 10000,\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const logLevelTrace: LogLevel.LogLevel = {\n  _tag: \"Trace\",\n  syslog: 7,\n  label: \"TRACE\",\n  ordinal: 0,\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const logLevelNone: LogLevel.LogLevel = {\n  _tag: \"None\",\n  syslog: 7,\n  label: \"OFF\",\n  ordinal: Number.MAX_SAFE_INTEGER,\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const allLogLevels: ReadonlyArray<LogLevel.LogLevel> = [\n  logLevelAll,\n  logLevelTrace,\n  logLevelDebug,\n  logLevelInfo,\n  logLevelWarning,\n  logLevelError,\n  logLevelFatal,\n  logLevelNone\n]\n\n// -----------------------------------------------------------------------------\n// FiberRef\n// -----------------------------------------------------------------------------\n\n/** @internal */\nconst FiberRefSymbolKey = \"effect/FiberRef\"\n\n/** @internal */\nexport const FiberRefTypeId: FiberRef.FiberRefTypeId = Symbol.for(\n  FiberRefSymbolKey\n) as FiberRef.FiberRefTypeId\n\nconst fiberRefVariance = {\n  /* c8 ignore next */\n  _A: (_: any) => _\n}\n\n/* @internal */\nexport const fiberRefGet = <A>(self: FiberRef.FiberRef<A>): Effect.Effect<A> =>\n  withFiberRuntime((fiber) => exitSucceed(fiber.getFiberRef(self)))\n\n/* @internal */\nexport const fiberRefGetAndSet = dual<\n  <A>(value: A) => (self: FiberRef.FiberRef<A>) => Effect.Effect<A>,\n  <A>(self: FiberRef.FiberRef<A>, value: A) => Effect.Effect<A>\n>(2, (self, value) => fiberRefModify(self, (v) => [v, value] as const))\n\n/* @internal */\nexport const fiberRefGetAndUpdate = dual<\n  <A>(f: (a: A) => A) => (self: FiberRef.FiberRef<A>) => Effect.Effect<A>,\n  <A>(self: FiberRef.FiberRef<A>, f: (a: A) => A) => Effect.Effect<A>\n>(2, (self, f) => fiberRefModify(self, (v) => [v, f(v)] as const))\n\n/* @internal */\nexport const fiberRefGetAndUpdateSome = dual<\n  <A>(\n    pf: (a: A) => Option.Option<A>\n  ) => (self: FiberRef.FiberRef<A>) => Effect.Effect<A>,\n  <A>(\n    self: FiberRef.FiberRef<A>,\n    pf: (a: A) => Option.Option<A>\n  ) => Effect.Effect<A>\n>(2, (self, pf) => fiberRefModify(self, (v) => [v, Option.getOrElse(pf(v), () => v)] as const))\n\n/* @internal */\nexport const fiberRefGetWith = dual<\n  <B, E, R, A>(f: (a: A) => Effect.Effect<B, E, R>) => (self: FiberRef.FiberRef<A>) => Effect.Effect<B, E, R>,\n  <A, B, E, R>(self: FiberRef.FiberRef<A>, f: (a: A) => Effect.Effect<B, E, R>) => Effect.Effect<B, E, R>\n>(2, (self, f) => flatMap(fiberRefGet(self), f))\n\n/* @internal */\nexport const fiberRefSet = dual<\n  <A>(value: A) => (self: FiberRef.FiberRef<A>) => Effect.Effect<void>,\n  <A>(self: FiberRef.FiberRef<A>, value: A) => Effect.Effect<void>\n>(2, (self, value) => fiberRefModify(self, () => [void 0, value] as const))\n\n/* @internal */\nexport const fiberRefDelete = <A>(self: FiberRef.FiberRef<A>): Effect.Effect<void> =>\n  withFiberRuntime((state) => {\n    state.unsafeDeleteFiberRef(self)\n    return void_\n  })\n\n/* @internal */\nexport const fiberRefReset = <A>(self: FiberRef.FiberRef<A>): Effect.Effect<void> => fiberRefSet(self, self.initial)\n\n/* @internal */\nexport const fiberRefModify = dual<\n  <A, B>(f: (a: A) => readonly [B, A]) => (self: FiberRef.FiberRef<A>) => Effect.Effect<B>,\n  <A, B>(self: FiberRef.FiberRef<A>, f: (a: A) => readonly [B, A]) => Effect.Effect<B>\n>(2, <A, B>(\n  self: FiberRef.FiberRef<A>,\n  f: (a: A) => readonly [B, A]\n): Effect.Effect<B> =>\n  withFiberRuntime((state) => {\n    const [b, a] = f(state.getFiberRef(self) as A)\n    state.setFiberRef(self, a)\n    return succeed(b)\n  }))\n\n/* @internal */\nexport const fiberRefModifySome = <A, B>(\n  self: FiberRef.FiberRef<A>,\n  def: B,\n  f: (a: A) => Option.Option<readonly [B, A]>\n): Effect.Effect<B> => fiberRefModify(self, (v) => Option.getOrElse(f(v), () => [def, v] as const))\n\n/* @internal */\nexport const fiberRefUpdate = dual<\n  <A>(f: (a: A) => A) => (self: FiberRef.FiberRef<A>) => Effect.Effect<void>,\n  <A>(self: FiberRef.FiberRef<A>, f: (a: A) => A) => Effect.Effect<void>\n>(2, (self, f) => fiberRefModify(self, (v) => [void 0, f(v)] as const))\n\n/* @internal */\nexport const fiberRefUpdateSome = dual<\n  <A>(pf: (a: A) => Option.Option<A>) => (self: FiberRef.FiberRef<A>) => Effect.Effect<void>,\n  <A>(self: FiberRef.FiberRef<A>, pf: (a: A) => Option.Option<A>) => Effect.Effect<void>\n>(2, (self, pf) => fiberRefModify(self, (v) => [void 0, Option.getOrElse(pf(v), () => v)] as const))\n\n/* @internal */\nexport const fiberRefUpdateAndGet = dual<\n  <A>(f: (a: A) => A) => (self: FiberRef.FiberRef<A>) => Effect.Effect<A>,\n  <A>(self: FiberRef.FiberRef<A>, f: (a: A) => A) => Effect.Effect<A>\n>(2, (self, f) =>\n  fiberRefModify(self, (v) => {\n    const result = f(v)\n    return [result, result] as const\n  }))\n\n/* @internal */\nexport const fiberRefUpdateSomeAndGet = dual<\n  <A>(pf: (a: A) => Option.Option<A>) => (self: FiberRef.FiberRef<A>) => Effect.Effect<A>,\n  <A>(self: FiberRef.FiberRef<A>, pf: (a: A) => Option.Option<A>) => Effect.Effect<A>\n>(2, (self, pf) =>\n  fiberRefModify(self, (v) => {\n    const result = Option.getOrElse(pf(v), () => v)\n    return [result, result] as const\n  }))\n\n// circular\n/** @internal */\nconst RequestResolverSymbolKey = \"effect/RequestResolver\"\n\n/** @internal */\nexport const RequestResolverTypeId: RequestResolver.RequestResolverTypeId = Symbol.for(\n  RequestResolverSymbolKey\n) as RequestResolver.RequestResolverTypeId\n\nconst requestResolverVariance = {\n  /* c8 ignore next */\n  _A: (_: unknown) => _,\n  /* c8 ignore next */\n  _R: (_: never) => _\n}\n\n/** @internal */\nexport class RequestResolverImpl<in A, out R> implements RequestResolver.RequestResolver<A, R> {\n  readonly [RequestResolverTypeId] = requestResolverVariance\n  constructor(\n    readonly runAll: (\n      requests: Array<Array<Request.Entry<A>>>\n    ) => Effect.Effect<void, never, R>,\n    readonly target?: unknown\n  ) {\n  }\n  [Hash.symbol](): number {\n    return Hash.cached(this, this.target ? Hash.hash(this.target) : Hash.random(this))\n  }\n  [Equal.symbol](that: unknown): boolean {\n    return this.target ?\n      isRequestResolver(that) && Equal.equals(this.target, (that as RequestResolverImpl<any, any>).target) :\n      this === that\n  }\n  identified(...ids: Array<unknown>): RequestResolver.RequestResolver<A, R> {\n    return new RequestResolverImpl(this.runAll, Chunk.fromIterable(ids))\n  }\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const isRequestResolver = (u: unknown): u is RequestResolver.RequestResolver<unknown, unknown> =>\n  hasProperty(u, RequestResolverTypeId)\n\n// end\n\n/** @internal */\nexport const resolverLocally = dual<\n  <A>(\n    self: FiberRef.FiberRef<A>,\n    value: A\n  ) => <R, B extends Request.Request<any, any>>(\n    use: RequestResolver.RequestResolver<B, R>\n  ) => RequestResolver.RequestResolver<B, R>,\n  <R, B extends Request.Request<any, any>, A>(\n    use: RequestResolver.RequestResolver<B, R>,\n    self: FiberRef.FiberRef<A>,\n    value: A\n  ) => RequestResolver.RequestResolver<B, R>\n>(3, <R, B extends Request.Request<any, any>, A>(\n  use: RequestResolver.RequestResolver<B, R>,\n  self: FiberRef.FiberRef<A>,\n  value: A\n): RequestResolver.RequestResolver<B, R> =>\n  new RequestResolverImpl<B, R>(\n    (requests) =>\n      fiberRefLocally(\n        use.runAll(requests),\n        self,\n        value\n      ),\n    Chunk.make(\"Locally\", use, self, value)\n  ))\n\n/** @internal */\nexport const requestBlockLocally = <A>(\n  self: BlockedRequests.RequestBlock,\n  ref: FiberRef.FiberRef<A>,\n  value: A\n): BlockedRequests.RequestBlock => blockedRequests_.reduce(self, LocallyReducer(ref, value))\n\nconst LocallyReducer = <A>(\n  ref: FiberRef.FiberRef<A>,\n  value: A\n): BlockedRequests.RequestBlock.Reducer<BlockedRequests.RequestBlock> => ({\n  emptyCase: () => blockedRequests_.empty,\n  parCase: (left, right) => blockedRequests_.par(left, right),\n  seqCase: (left, right) => blockedRequests_.seq(left, right),\n  singleCase: (dataSource, blockedRequest) =>\n    blockedRequests_.single(\n      resolverLocally(dataSource, ref, value),\n      blockedRequest as any\n    )\n})\n\n/* @internal */\nexport const fiberRefLocally: {\n  <A>(self: FiberRef.FiberRef<A>, value: A): <B, E, R>(use: Effect.Effect<B, E, R>) => Effect.Effect<B, E, R>\n  <B, E, R, A>(use: Effect.Effect<B, E, R>, self: FiberRef.FiberRef<A>, value: A): Effect.Effect<B, E, R>\n} = dual(\n  3,\n  <B, E, R, A>(use: Effect.Effect<B, E, R>, self: FiberRef.FiberRef<A>, value: A): Effect.Effect<B, E, R> =>\n    acquireUseRelease(\n      zipLeft(fiberRefGet(self), fiberRefSet(self, value)),\n      () => use,\n      (oldValue) => fiberRefSet(self, oldValue)\n    )\n)\n\n/* @internal */\nexport const fiberRefLocallyWith = dual<\n  <A>(self: FiberRef.FiberRef<A>, f: (a: A) => A) => <B, E, R>(use: Effect.Effect<B, E, R>) => Effect.Effect<B, E, R>,\n  <B, E, R, A>(use: Effect.Effect<B, E, R>, self: FiberRef.FiberRef<A>, f: (a: A) => A) => Effect.Effect<B, E, R>\n>(3, (use, self, f) => fiberRefGetWith(self, (a) => fiberRefLocally(use, self, f(a))))\n\n/** @internal */\nexport const fiberRefUnsafeMake = <Value>(\n  initial: Value,\n  options?: {\n    readonly fork?: ((a: Value) => Value) | undefined\n    readonly join?: ((left: Value, right: Value) => Value) | undefined\n  }\n): FiberRef.FiberRef<Value> =>\n  fiberRefUnsafeMakePatch(initial, {\n    differ: internalDiffer.update(),\n    fork: options?.fork ?? identity,\n    join: options?.join\n  })\n\n/** @internal */\nexport const fiberRefUnsafeMakeHashSet = <A>(\n  initial: HashSet.HashSet<A>\n): FiberRef.FiberRef<HashSet.HashSet<A>> => {\n  const differ = internalDiffer.hashSet<A>()\n  return fiberRefUnsafeMakePatch(initial, {\n    differ,\n    fork: differ.empty\n  })\n}\n\n/** @internal */\nexport const fiberRefUnsafeMakeReadonlyArray = <A>(\n  initial: ReadonlyArray<A>\n): FiberRef.FiberRef<ReadonlyArray<A>> => {\n  const differ = internalDiffer.readonlyArray(internalDiffer.update<A>())\n  return fiberRefUnsafeMakePatch(initial, {\n    differ,\n    fork: differ.empty\n  })\n}\n\n/** @internal */\nexport const fiberRefUnsafeMakeContext = <A>(\n  initial: Context.Context<A>\n): FiberRef.FiberRef<Context.Context<A>> => {\n  const differ = internalDiffer.environment<A>()\n  return fiberRefUnsafeMakePatch(initial, {\n    differ,\n    fork: differ.empty\n  })\n}\n\n/** @internal */\nexport const fiberRefUnsafeMakePatch = <Value, Patch>(\n  initial: Value,\n  options: {\n    readonly differ: Differ.Differ<Value, Patch>\n    readonly fork: Patch\n    readonly join?: ((oldV: Value, newV: Value) => Value) | undefined\n  }\n): FiberRef.FiberRef<Value> => {\n  const _fiberRef = {\n    ...CommitPrototype,\n    [FiberRefTypeId]: fiberRefVariance,\n    initial,\n    commit() {\n      return fiberRefGet(this)\n    },\n    diff: (oldValue: Value, newValue: Value) => options.differ.diff(oldValue, newValue),\n    combine: (first: Patch, second: Patch) => options.differ.combine(first, second),\n    patch: (patch: Patch) => (oldValue: Value) => options.differ.patch(patch, oldValue),\n    fork: options.fork,\n    join: options.join ?? ((_, n) => n)\n  }\n  return _fiberRef\n}\n\n/** @internal */\nexport const fiberRefUnsafeMakeRuntimeFlags = (\n  initial: RuntimeFlags.RuntimeFlags\n): FiberRef.FiberRef<RuntimeFlags.RuntimeFlags> =>\n  fiberRefUnsafeMakePatch(initial, {\n    differ: runtimeFlags_.differ,\n    fork: runtimeFlags_.differ.empty\n  })\n\n/** @internal */\nexport const currentContext: FiberRef.FiberRef<Context.Context<never>> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentContext\"),\n  () => fiberRefUnsafeMakeContext(Context.empty())\n)\n\n/** @internal */\nexport const currentSchedulingPriority: FiberRef.FiberRef<number> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentSchedulingPriority\"),\n  () => fiberRefUnsafeMake(0)\n)\n\n/** @internal */\nexport const currentMaxOpsBeforeYield: FiberRef.FiberRef<number> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentMaxOpsBeforeYield\"),\n  () => fiberRefUnsafeMake(2048)\n)\n\n/** @internal */\nexport const currentLogAnnotations: FiberRef.FiberRef<HashMap.HashMap<string, unknown>> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentLogAnnotation\"),\n  () => fiberRefUnsafeMake(HashMap.empty())\n)\n\n/** @internal */\nexport const currentLogLevel: FiberRef.FiberRef<LogLevel.LogLevel> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentLogLevel\"),\n  () => fiberRefUnsafeMake<LogLevel.LogLevel>(logLevelInfo)\n)\n\n/** @internal */\nexport const currentLogSpan: FiberRef.FiberRef<List.List<LogSpan.LogSpan>> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentLogSpan\"),\n  () => fiberRefUnsafeMake(List.empty<LogSpan.LogSpan>())\n)\n\n/** @internal */\nexport const withSchedulingPriority = dual<\n  (priority: number) => <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R>,\n  <A, E, R>(self: Effect.Effect<A, E, R>, priority: number) => Effect.Effect<A, E, R>\n>(2, (self, scheduler) => fiberRefLocally(self, currentSchedulingPriority, scheduler))\n\n/** @internal */\nexport const withMaxOpsBeforeYield = dual<\n  (priority: number) => <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R>,\n  <A, E, R>(self: Effect.Effect<A, E, R>, priority: number) => Effect.Effect<A, E, R>\n>(2, (self, scheduler) => fiberRefLocally(self, currentMaxOpsBeforeYield, scheduler))\n\n/** @internal */\nexport const currentConcurrency: FiberRef.FiberRef<\"unbounded\" | number> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentConcurrency\"),\n  () => fiberRefUnsafeMake<\"unbounded\" | number>(\"unbounded\")\n)\n\n/**\n * @internal\n */\nexport const currentRequestBatching = globalValue(\n  Symbol.for(\"effect/FiberRef/currentRequestBatching\"),\n  () => fiberRefUnsafeMake(true)\n)\n\n/** @internal */\nexport const currentUnhandledErrorLogLevel: FiberRef.FiberRef<Option.Option<LogLevel.LogLevel>> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentUnhandledErrorLogLevel\"),\n  () => fiberRefUnsafeMake(Option.some<LogLevel.LogLevel>(logLevelDebug))\n)\n\n/** @internal */\nexport const withUnhandledErrorLogLevel = dual<\n  (level: Option.Option<LogLevel.LogLevel>) => <A, E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R>,\n  <A, E, R>(self: Effect.Effect<A, E, R>, level: Option.Option<LogLevel.LogLevel>) => Effect.Effect<A, E, R>\n>(2, (self, level) => fiberRefLocally(self, currentUnhandledErrorLogLevel, level))\n\n/** @internal */\nexport const currentMetricLabels: FiberRef.FiberRef<ReadonlyArray<MetricLabel.MetricLabel>> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentMetricLabels\"),\n  () => fiberRefUnsafeMakeReadonlyArray(Arr.empty())\n)\n\n/* @internal */\nexport const metricLabels: Effect.Effect<ReadonlyArray<MetricLabel.MetricLabel>> = fiberRefGet(\n  currentMetricLabels\n)\n\n/** @internal */\nexport const currentForkScopeOverride: FiberRef.FiberRef<Option.Option<fiberScope.FiberScope>> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentForkScopeOverride\"),\n  () =>\n    fiberRefUnsafeMake(Option.none(), {\n      fork: () => Option.none() as Option.Option<fiberScope.FiberScope>,\n      join: (parent, _) => parent\n    })\n)\n\n/** @internal */\nexport const currentInterruptedCause: FiberRef.FiberRef<Cause.Cause<never>> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentInterruptedCause\"),\n  () =>\n    fiberRefUnsafeMake(internalCause.empty, {\n      fork: () => internalCause.empty,\n      join: (parent, _) => parent\n    })\n)\n\n/** @internal */\nexport const currentTracerEnabled: FiberRef.FiberRef<boolean> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentTracerEnabled\"),\n  () => fiberRefUnsafeMake(true)\n)\n\n/** @internal */\nexport const currentTracerTimingEnabled: FiberRef.FiberRef<boolean> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentTracerTiming\"),\n  () => fiberRefUnsafeMake(true)\n)\n\n/** @internal */\nexport const currentTracerSpanAnnotations: FiberRef.FiberRef<HashMap.HashMap<string, unknown>> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentTracerSpanAnnotations\"),\n  () => fiberRefUnsafeMake(HashMap.empty())\n)\n\n/** @internal */\nexport const currentTracerSpanLinks: FiberRef.FiberRef<Chunk.Chunk<Tracer.SpanLink>> = globalValue(\n  Symbol.for(\"effect/FiberRef/currentTracerSpanLinks\"),\n  () => fiberRefUnsafeMake(Chunk.empty())\n)\n\n// -----------------------------------------------------------------------------\n// Scope\n// -----------------------------------------------------------------------------\n\n/** @internal */\nexport const ScopeTypeId: Scope.ScopeTypeId = Symbol.for(\"effect/Scope\") as Scope.ScopeTypeId\n\n/** @internal */\nexport const CloseableScopeTypeId: Scope.CloseableScopeTypeId = Symbol.for(\n  \"effect/CloseableScope\"\n) as Scope.CloseableScopeTypeId\n\n/* @internal */\nexport const scopeAddFinalizer = (\n  self: Scope.Scope,\n  finalizer: Effect.Effect<unknown>\n): Effect.Effect<void> => self.addFinalizer(() => asVoid(finalizer))\n\n/* @internal */\nexport const scopeAddFinalizerExit = (\n  self: Scope.Scope,\n  finalizer: Scope.Scope.Finalizer\n): Effect.Effect<void> => self.addFinalizer(finalizer)\n\n/* @internal */\nexport const scopeClose = (\n  self: Scope.Scope.Closeable,\n  exit: Exit.Exit<unknown, unknown>\n): Effect.Effect<void> => self.close(exit)\n\n/* @internal */\nexport const scopeFork = (\n  self: Scope.Scope,\n  strategy: ExecutionStrategy.ExecutionStrategy\n): Effect.Effect<Scope.Scope.Closeable> => self.fork(strategy)\n\n// -----------------------------------------------------------------------------\n// Cause\n// -----------------------------------------------------------------------------\n\n/** @internal */\nexport const causeSquash = <E>(self: Cause.Cause<E>): unknown => {\n  return causeSquashWith(identity)(self)\n}\n\n/** @internal */\nexport const causeSquashWith = dual<\n  <E>(f: (error: E) => unknown) => (self: Cause.Cause<E>) => unknown,\n  <E>(self: Cause.Cause<E>, f: (error: E) => unknown) => unknown\n>(2, (self, f) => {\n  const option = pipe(self, internalCause.failureOption, Option.map(f))\n  switch (option._tag) {\n    case \"None\": {\n      return pipe(\n        internalCause.defects(self),\n        Chunk.head,\n        Option.match({\n          onNone: () => {\n            const interrupts = Arr.fromIterable(internalCause.interruptors(self)).flatMap((fiberId) =>\n              Arr.fromIterable(FiberId.ids(fiberId)).map((id) => `#${id}`)\n            )\n            return new InterruptedException(interrupts ? `Interrupted by fibers: ${interrupts.join(\", \")}` : void 0)\n          },\n          onSome: identity\n        })\n      )\n    }\n    case \"Some\": {\n      return option.value\n    }\n  }\n})\n\n// -----------------------------------------------------------------------------\n// Errors\n// -----------------------------------------------------------------------------\n\n/** @internal */\nexport const YieldableError: new(message?: string, options?: ErrorOptions) => Cause.YieldableError = (function() {\n  class YieldableError extends globalThis.Error {\n    commit() {\n      return fail(this)\n    }\n    toJSON() {\n      const obj = { ...this }\n      if (this.message) obj.message = this.message\n      if (this.cause) obj.cause = this.cause\n      return obj\n    }\n    [NodeInspectSymbol]() {\n      if (this.toString !== globalThis.Error.prototype.toString) {\n        return this.stack ? `${this.toString()}\\n${this.stack.split(\"\\n\").slice(1).join(\"\\n\")}` : this.toString()\n      } else if (\"Bun\" in globalThis) {\n        return internalCause.pretty(internalCause.fail(this), { renderErrorCause: true })\n      }\n      return this\n    }\n  }\n  Object.assign(YieldableError.prototype, StructuralCommitPrototype)\n  return YieldableError as any\n})()\n\nconst makeException = <T extends { _tag: string; message?: string }>(\n  proto: Omit<T, keyof Cause.YieldableError | \"_tag\">,\n  tag: T[\"_tag\"]\n): new(message?: string | undefined) => T => {\n  class Base extends YieldableError {\n    readonly _tag = tag\n  }\n  Object.assign(Base.prototype, proto)\n  ;(Base.prototype as any).name = tag\n  return Base as any\n}\n\n/** @internal */\nexport const RuntimeExceptionTypeId: Cause.RuntimeExceptionTypeId = Symbol.for(\n  \"effect/Cause/errors/RuntimeException\"\n) as Cause.RuntimeExceptionTypeId\n\n/** @internal */\nexport const RuntimeException = makeException<Cause.RuntimeException>({\n  [RuntimeExceptionTypeId]: RuntimeExceptionTypeId\n}, \"RuntimeException\")\n\n/** @internal */\nexport const isRuntimeException = (u: unknown): u is Cause.RuntimeException => hasProperty(u, RuntimeExceptionTypeId)\n\n/** @internal */\nexport const InterruptedExceptionTypeId: Cause.InterruptedExceptionTypeId = Symbol.for(\n  \"effect/Cause/errors/InterruptedException\"\n) as Cause.InterruptedExceptionTypeId\n\n/** @internal */\nexport const InterruptedException = makeException<Cause.InterruptedException>({\n  [InterruptedExceptionTypeId]: InterruptedExceptionTypeId\n}, \"InterruptedException\")\n\n/** @internal */\nexport const isInterruptedException = (u: unknown): u is Cause.InterruptedException =>\n  hasProperty(u, InterruptedExceptionTypeId)\n\n/** @internal */\nexport const IllegalArgumentExceptionTypeId: Cause.IllegalArgumentExceptionTypeId = Symbol.for(\n  \"effect/Cause/errors/IllegalArgument\"\n) as Cause.IllegalArgumentExceptionTypeId\n\n/** @internal */\nexport const IllegalArgumentException = makeException<Cause.IllegalArgumentException>({\n  [IllegalArgumentExceptionTypeId]: IllegalArgumentExceptionTypeId\n}, \"IllegalArgumentException\")\n\n/** @internal */\nexport const isIllegalArgumentException = (u: unknown): u is Cause.IllegalArgumentException =>\n  hasProperty(u, IllegalArgumentExceptionTypeId)\n\n/** @internal */\nexport const NoSuchElementExceptionTypeId: Cause.NoSuchElementExceptionTypeId = Symbol.for(\n  \"effect/Cause/errors/NoSuchElement\"\n) as Cause.NoSuchElementExceptionTypeId\n\n/** @internal */\nexport const NoSuchElementException = makeException<Cause.NoSuchElementException>({\n  [NoSuchElementExceptionTypeId]: NoSuchElementExceptionTypeId\n}, \"NoSuchElementException\")\n\n/** @internal */\nexport const isNoSuchElementException = (u: unknown): u is Cause.NoSuchElementException =>\n  hasProperty(u, NoSuchElementExceptionTypeId)\n\n/** @internal */\nexport const InvalidPubSubCapacityExceptionTypeId: Cause.InvalidPubSubCapacityExceptionTypeId = Symbol.for(\n  \"effect/Cause/errors/InvalidPubSubCapacityException\"\n) as Cause.InvalidPubSubCapacityExceptionTypeId\n\n/** @internal */\nexport const InvalidPubSubCapacityException = makeException<Cause.InvalidPubSubCapacityException>({\n  [InvalidPubSubCapacityExceptionTypeId]: InvalidPubSubCapacityExceptionTypeId\n}, \"InvalidPubSubCapacityException\")\n\n/** @internal */\nexport const ExceededCapacityExceptionTypeId: Cause.ExceededCapacityExceptionTypeId = Symbol.for(\n  \"effect/Cause/errors/ExceededCapacityException\"\n) as Cause.ExceededCapacityExceptionTypeId\n\n/** @internal */\nexport const ExceededCapacityException = makeException<Cause.ExceededCapacityException>({\n  [ExceededCapacityExceptionTypeId]: ExceededCapacityExceptionTypeId\n}, \"ExceededCapacityException\")\n\n/** @internal */\nexport const isExceededCapacityException = (u: unknown): u is Cause.ExceededCapacityException =>\n  hasProperty(u, ExceededCapacityExceptionTypeId)\n\n/** @internal */\nexport const isInvalidCapacityError = (u: unknown): u is Cause.InvalidPubSubCapacityException =>\n  hasProperty(u, InvalidPubSubCapacityExceptionTypeId)\n\n/** @internal */\nexport const TimeoutExceptionTypeId: Cause.TimeoutExceptionTypeId = Symbol.for(\n  \"effect/Cause/errors/Timeout\"\n) as Cause.TimeoutExceptionTypeId\n\n/** @internal */\nexport const TimeoutException = makeException<Cause.TimeoutException>({\n  [TimeoutExceptionTypeId]: TimeoutExceptionTypeId\n}, \"TimeoutException\")\n\n/** @internal */\nexport const timeoutExceptionFromDuration = (duration: Duration.DurationInput): Cause.TimeoutException =>\n  new TimeoutException(`Operation timed out after '${Duration.format(duration)}'`)\n\n/** @internal */\nexport const isTimeoutException = (u: unknown): u is Cause.TimeoutException => hasProperty(u, TimeoutExceptionTypeId)\n\n/** @internal */\nexport const UnknownExceptionTypeId: Cause.UnknownExceptionTypeId = Symbol.for(\n  \"effect/Cause/errors/UnknownException\"\n) as Cause.UnknownExceptionTypeId\n\n/** @internal */\nexport const UnknownException: new(cause: unknown, message?: string | undefined) => Cause.UnknownException =\n  (function() {\n    class UnknownException extends YieldableError {\n      readonly _tag = \"UnknownException\"\n      readonly error: unknown\n      constructor(cause: unknown, message?: string) {\n        super(message ?? \"An unknown error occurred\", { cause })\n        this.error = cause\n      }\n    }\n    Object.assign(UnknownException.prototype, {\n      [UnknownExceptionTypeId]: UnknownExceptionTypeId,\n      name: \"UnknownException\"\n    })\n    return UnknownException as any\n  })()\n\n/** @internal */\nexport const isUnknownException = (u: unknown): u is Cause.UnknownException => hasProperty(u, UnknownExceptionTypeId)\n\n// -----------------------------------------------------------------------------\n// Exit\n// -----------------------------------------------------------------------------\n\n/** @internal */\nexport const exitIsExit = (u: unknown): u is Exit.Exit<unknown, unknown> =>\n  isEffect(u) && \"_tag\" in u && (u._tag === \"Success\" || u._tag === \"Failure\")\n\n/** @internal */\nexport const exitIsFailure = <A, E>(self: Exit.Exit<A, E>): self is Exit.Failure<A, E> => self._tag === \"Failure\"\n\n/** @internal */\nexport const exitIsSuccess = <A, E>(self: Exit.Exit<A, E>): self is Exit.Success<A, E> => self._tag === \"Success\"\n\n/** @internal */\nexport const exitIsInterrupted = <A, E>(self: Exit.Exit<A, E>): boolean => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE:\n      return internalCause.isInterrupted(self.effect_instruction_i0)\n    case OpCodes.OP_SUCCESS:\n      return false\n  }\n}\n\n/** @internal */\nexport const exitAs = dual<\n  <A2>(value: A2) => <A, E>(self: Exit.Exit<A, E>) => Exit.Exit<A2, E>,\n  <A, E, A2>(self: Exit.Exit<A, E>, value: A2) => Exit.Exit<A2, E>\n>(2, <A, E, A2>(self: Exit.Exit<A, E>, value: A2): Exit.Exit<A2, E> => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE: {\n      return exitFailCause(self.effect_instruction_i0)\n    }\n    case OpCodes.OP_SUCCESS: {\n      return exitSucceed(value) as Exit.Exit<A2, E>\n    }\n  }\n})\n\n/** @internal */\nexport const exitAsVoid = <A, E>(self: Exit.Exit<A, E>): Exit.Exit<void, E> => exitAs(self, void 0)\n\n/** @internal */\nexport const exitCauseOption = <A, E>(self: Exit.Exit<A, E>): Option.Option<Cause.Cause<E>> => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE:\n      return Option.some(self.effect_instruction_i0)\n    case OpCodes.OP_SUCCESS:\n      return Option.none()\n  }\n}\n\n/** @internal */\nexport const exitCollectAll = <A, E>(\n  exits: Iterable<Exit.Exit<A, E>>,\n  options?: {\n    readonly parallel?: boolean | undefined\n  }\n): Option.Option<Exit.Exit<Array<A>, E>> =>\n  exitCollectAllInternal(exits, options?.parallel ? internalCause.parallel : internalCause.sequential)\n\n/** @internal */\nexport const exitDie = (defect: unknown): Exit.Exit<never> =>\n  exitFailCause(internalCause.die(defect)) as Exit.Exit<never>\n\n/** @internal */\nexport const exitExists: {\n  <A, B extends A>(refinement: Refinement<NoInfer<A>, B>): <E>(self: Exit.Exit<A, E>) => self is Exit.Exit<B>\n  <A>(predicate: Predicate<NoInfer<A>>): <E>(self: Exit.Exit<A, E>) => boolean\n  <A, E, B extends A>(self: Exit.Exit<A, E>, refinement: Refinement<A, B>): self is Exit.Exit<B>\n  <A, E>(self: Exit.Exit<A, E>, predicate: Predicate<A>): boolean\n} = dual(2, <A, E, B extends A>(self: Exit.Exit<A, E>, refinement: Refinement<A, B>): self is Exit.Exit<B> => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE:\n      return false\n    case OpCodes.OP_SUCCESS:\n      return refinement(self.effect_instruction_i0)\n  }\n})\n\n/** @internal */\nexport const exitFail = <E>(error: E): Exit.Exit<never, E> =>\n  exitFailCause(internalCause.fail(error)) as Exit.Exit<never, E>\n\n/** @internal */\nexport const exitFailCause = <E>(cause: Cause.Cause<E>): Exit.Exit<never, E> => {\n  const effect = new EffectPrimitiveFailure(OpCodes.OP_FAILURE) as any\n  effect.effect_instruction_i0 = cause\n  return effect\n}\n\n/** @internal */\nexport const exitFlatMap = dual<\n  <A, A2, E2>(f: (a: A) => Exit.Exit<A2, E2>) => <E>(self: Exit.Exit<A, E>) => Exit.Exit<A2, E | E2>,\n  <A, E, E2, A2>(self: Exit.Exit<A, E>, f: (a: A) => Exit.Exit<A2, E2>) => Exit.Exit<A2, E | E2>\n>(2, <A, E, E2, A2>(self: Exit.Exit<A, E>, f: (a: A) => Exit.Exit<A2, E2>): Exit.Exit<A2, E | E2> => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE: {\n      return exitFailCause(self.effect_instruction_i0)\n    }\n    case OpCodes.OP_SUCCESS: {\n      return f(self.effect_instruction_i0)\n    }\n  }\n})\n\n/** @internal */\nexport const exitFlatMapEffect: {\n  <A, E, A2, E2, R>(\n    f: (a: A) => Effect.Effect<Exit.Exit<A2, E>, E2, R>\n  ): (self: Exit.Exit<A, E>) => Effect.Effect<Exit.Exit<A2, E>, E2, R>\n  <A, E, A2, E2, R>(\n    self: Exit.Exit<A, E>,\n    f: (a: A) => Effect.Effect<Exit.Exit<A2, E>, E2, R>\n  ): Effect.Effect<Exit.Exit<A2, E>, E2, R>\n} = dual(2, <A, E, A2, E2, R>(\n  self: Exit.Exit<A, E>,\n  f: (a: A) => Effect.Effect<Exit.Exit<A2, E>, E2, R>\n): Effect.Effect<Exit.Exit<A2, E>, E2, R> => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE: {\n      return succeed(exitFailCause(self.effect_instruction_i0))\n    }\n    case OpCodes.OP_SUCCESS: {\n      return f(self.effect_instruction_i0)\n    }\n  }\n})\n\n/** @internal */\nexport const exitFlatten = <A, E, E2>(\n  self: Exit.Exit<Exit.Exit<A, E>, E2>\n): Exit.Exit<A, E | E2> => pipe(self, exitFlatMap(identity))\n\n/** @internal */\nexport const exitForEachEffect: {\n  <A, B, E2, R>(\n    f: (a: A) => Effect.Effect<B, E2, R>\n  ): <E>(self: Exit.Exit<A, E>) => Effect.Effect<Exit.Exit<B, E | E2>, never, R>\n  <A, E, B, E2, R>(\n    self: Exit.Exit<A, E>,\n    f: (a: A) => Effect.Effect<B, E2, R>\n  ): Effect.Effect<Exit.Exit<B, E | E2>, never, R>\n} = dual(2, <A, E, B, E2, R>(\n  self: Exit.Exit<A, E>,\n  f: (a: A) => Effect.Effect<B, E2, R>\n): Effect.Effect<Exit.Exit<B, E | E2>, never, R> => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE: {\n      return succeed(exitFailCause(self.effect_instruction_i0))\n    }\n    case OpCodes.OP_SUCCESS: {\n      return exit(f(self.effect_instruction_i0))\n    }\n  }\n})\n\n/** @internal */\nexport const exitFromEither = <R, L>(either: Either.Either<R, L>): Exit.Exit<R, L> => {\n  switch (either._tag) {\n    case \"Left\":\n      return exitFail(either.left)\n    case \"Right\":\n      return exitSucceed(either.right)\n  }\n}\n\n/** @internal */\nexport const exitFromOption = <A>(option: Option.Option<A>): Exit.Exit<A, void> => {\n  switch (option._tag) {\n    case \"None\":\n      return exitFail(void 0)\n    case \"Some\":\n      return exitSucceed(option.value)\n  }\n}\n\n/** @internal */\nexport const exitGetOrElse = dual<\n  <E, A2>(orElse: (cause: Cause.Cause<E>) => A2) => <A>(self: Exit.Exit<A, E>) => A | A2,\n  <A, E, A2>(self: Exit.Exit<A, E>, orElse: (cause: Cause.Cause<E>) => A2) => A | A2\n>(2, (self, orElse) => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE:\n      return orElse(self.effect_instruction_i0)\n    case OpCodes.OP_SUCCESS:\n      return self.effect_instruction_i0\n  }\n})\n\n/** @internal */\nexport const exitInterrupt = (fiberId: FiberId.FiberId): Exit.Exit<never> =>\n  exitFailCause(internalCause.interrupt(fiberId))\n\n/** @internal */\nexport const exitMap = dual<\n  <A, B>(f: (a: A) => B) => <E>(self: Exit.Exit<A, E>) => Exit.Exit<B, E>,\n  <A, E, B>(self: Exit.Exit<A, E>, f: (a: A) => B) => Exit.Exit<B, E>\n>(2, (self, f) => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE:\n      return exitFailCause(self.effect_instruction_i0)\n    case OpCodes.OP_SUCCESS:\n      return exitSucceed(f(self.effect_instruction_i0))\n  }\n})\n\n/** @internal */\nexport const exitMapBoth = dual<\n  <E, A, E2, A2>(\n    options: {\n      readonly onFailure: (e: E) => E2\n      readonly onSuccess: (a: A) => A2\n    }\n  ) => (self: Exit.Exit<A, E>) => Exit.Exit<A2, E2>,\n  <A, E, E2, A2>(\n    self: Exit.Exit<A, E>,\n    options: {\n      readonly onFailure: (e: E) => E2\n      readonly onSuccess: (a: A) => A2\n    }\n  ) => Exit.Exit<A2, E2>\n>(2, (self, { onFailure, onSuccess }) => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE:\n      return exitFailCause(pipe(self.effect_instruction_i0, internalCause.map(onFailure)))\n    case OpCodes.OP_SUCCESS:\n      return exitSucceed(onSuccess(self.effect_instruction_i0))\n  }\n})\n\n/** @internal */\nexport const exitMapError = dual<\n  <E, E2>(f: (e: E) => E2) => <A>(self: Exit.Exit<A, E>) => Exit.Exit<A, E2>,\n  <A, E, E2>(self: Exit.Exit<A, E>, f: (e: E) => E2) => Exit.Exit<A, E2>\n>(2, (self, f) => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE:\n      return exitFailCause(pipe(self.effect_instruction_i0, internalCause.map(f)))\n    case OpCodes.OP_SUCCESS:\n      return exitSucceed(self.effect_instruction_i0)\n  }\n})\n\n/** @internal */\nexport const exitMapErrorCause = dual<\n  <E, E2>(f: (cause: Cause.Cause<E>) => Cause.Cause<E2>) => <A>(self: Exit.Exit<A, E>) => Exit.Exit<A, E2>,\n  <E, A, E2>(self: Exit.Exit<A, E>, f: (cause: Cause.Cause<E>) => Cause.Cause<E2>) => Exit.Exit<A, E2>\n>(2, (self, f) => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE:\n      return exitFailCause(f(self.effect_instruction_i0))\n    case OpCodes.OP_SUCCESS:\n      return exitSucceed(self.effect_instruction_i0)\n  }\n})\n\n/** @internal */\nexport const exitMatch = dual<\n  <E, A, Z1, Z2>(options: {\n    readonly onFailure: (cause: Cause.Cause<E>) => Z1\n    readonly onSuccess: (a: A) => Z2\n  }) => (self: Exit.Exit<A, E>) => Z1 | Z2,\n  <A, E, Z1, Z2>(self: Exit.Exit<A, E>, options: {\n    readonly onFailure: (cause: Cause.Cause<E>) => Z1\n    readonly onSuccess: (a: A) => Z2\n  }) => Z1 | Z2\n>(2, (self, { onFailure, onSuccess }) => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE:\n      return onFailure(self.effect_instruction_i0)\n    case OpCodes.OP_SUCCESS:\n      return onSuccess(self.effect_instruction_i0)\n  }\n})\n\n/** @internal */\nexport const exitMatchEffect = dual<\n  <E, A2, E2, R, A, A3, E3, R2>(\n    options: {\n      readonly onFailure: (cause: Cause.Cause<E>) => Effect.Effect<A2, E2, R>\n      readonly onSuccess: (a: A) => Effect.Effect<A3, E3, R2>\n    }\n  ) => (self: Exit.Exit<A, E>) => Effect.Effect<A2 | A3, E2 | E3, R | R2>,\n  <A, E, A2, E2, R, A3, E3, R2>(\n    self: Exit.Exit<A, E>,\n    options: {\n      readonly onFailure: (cause: Cause.Cause<E>) => Effect.Effect<A2, E2, R>\n      readonly onSuccess: (a: A) => Effect.Effect<A3, E3, R2>\n    }\n  ) => Effect.Effect<A2 | A3, E2 | E3, R | R2>\n>(2, (self, { onFailure, onSuccess }) => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE:\n      return onFailure(self.effect_instruction_i0)\n    case OpCodes.OP_SUCCESS:\n      return onSuccess(self.effect_instruction_i0)\n  }\n})\n\n/** @internal */\nexport const exitSucceed = <A>(value: A): Exit.Exit<A> => {\n  const effect = new EffectPrimitiveSuccess(OpCodes.OP_SUCCESS) as any\n  effect.effect_instruction_i0 = value\n  return effect\n}\n\n/** @internal */\nexport const exitVoid: Exit.Exit<void> = exitSucceed(void 0)\n\n/** @internal */\nexport const exitZip = dual<\n  <A2, E2>(that: Exit.Exit<A2, E2>) => <A, E>(self: Exit.Exit<A, E>) => Exit.Exit<[A, A2], E | E2>,\n  <A, E, A2, E2>(self: Exit.Exit<A, E>, that: Exit.Exit<A2, E2>) => Exit.Exit<[A, A2], E | E2>\n>(2, (self, that) =>\n  exitZipWith(self, that, {\n    onSuccess: (a, a2) => [a, a2],\n    onFailure: internalCause.sequential\n  }))\n\n/** @internal */\nexport const exitZipLeft = dual<\n  <A2, E2>(that: Exit.Exit<A2, E2>) => <A, E>(self: Exit.Exit<A, E>) => Exit.Exit<A, E | E2>,\n  <A, E, A2, E2>(self: Exit.Exit<A, E>, that: Exit.Exit<A2, E2>) => Exit.Exit<A, E | E2>\n>(2, (self, that) =>\n  exitZipWith(self, that, {\n    onSuccess: (a, _) => a,\n    onFailure: internalCause.sequential\n  }))\n\n/** @internal */\nexport const exitZipRight = dual<\n  <A2, E2>(that: Exit.Exit<A2, E2>) => <A, E>(self: Exit.Exit<A, E>) => Exit.Exit<A2, E | E2>,\n  <A, E, A2, E2>(self: Exit.Exit<A, E>, that: Exit.Exit<A2, E2>) => Exit.Exit<A2, E | E2>\n>(2, (self, that) =>\n  exitZipWith(self, that, {\n    onSuccess: (_, a2) => a2,\n    onFailure: internalCause.sequential\n  }))\n\n/** @internal */\nexport const exitZipPar = dual<\n  <A2, E2>(that: Exit.Exit<A2, E2>) => <A, E>(self: Exit.Exit<A, E>) => Exit.Exit<[A, A2], E | E2>,\n  <A, E, A2, E2>(self: Exit.Exit<A, E>, that: Exit.Exit<A2, E2>) => Exit.Exit<[A, A2], E | E2>\n>(2, (self, that) =>\n  exitZipWith(self, that, {\n    onSuccess: (a, a2) => [a, a2],\n    onFailure: internalCause.parallel\n  }))\n\n/** @internal */\nexport const exitZipParLeft = dual<\n  <A2, E2>(that: Exit.Exit<A2, E2>) => <A, E>(self: Exit.Exit<A, E>) => Exit.Exit<A, E | E2>,\n  <A, E, A2, E2>(self: Exit.Exit<A, E>, that: Exit.Exit<A2, E2>) => Exit.Exit<A, E | E2>\n>(2, (self, that) =>\n  exitZipWith(self, that, {\n    onSuccess: (a, _) => a,\n    onFailure: internalCause.parallel\n  }))\n\n/** @internal */\nexport const exitZipParRight = dual<\n  <A2, E2>(that: Exit.Exit<A2, E2>) => <A, E>(self: Exit.Exit<A, E>) => Exit.Exit<A2, E | E2>,\n  <A, E, A2, E2>(self: Exit.Exit<A, E>, that: Exit.Exit<A2, E2>) => Exit.Exit<A2, E | E2>\n>(2, (self, that) =>\n  exitZipWith(self, that, {\n    onSuccess: (_, a2) => a2,\n    onFailure: internalCause.parallel\n  }))\n\n/** @internal */\nexport const exitZipWith = dual<\n  <B, E2, A, C, E>(\n    that: Exit.Exit<B, E2>,\n    options: {\n      readonly onSuccess: (a: A, b: B) => C\n      readonly onFailure: (cause: Cause.Cause<E>, cause2: Cause.Cause<E2>) => Cause.Cause<E | E2>\n    }\n  ) => (self: Exit.Exit<A, E>) => Exit.Exit<C, E | E2>,\n  <A, E, B, E2, C>(\n    self: Exit.Exit<A, E>,\n    that: Exit.Exit<B, E2>,\n    options: {\n      readonly onSuccess: (a: A, b: B) => C\n      readonly onFailure: (cause: Cause.Cause<E>, cause2: Cause.Cause<E2>) => Cause.Cause<E | E2>\n    }\n  ) => Exit.Exit<C, E | E2>\n>(3, (\n  self,\n  that,\n  { onFailure, onSuccess }\n) => {\n  switch (self._tag) {\n    case OpCodes.OP_FAILURE: {\n      switch (that._tag) {\n        case OpCodes.OP_SUCCESS:\n          return exitFailCause(self.effect_instruction_i0)\n        case OpCodes.OP_FAILURE: {\n          return exitFailCause(onFailure(self.effect_instruction_i0, that.effect_instruction_i0))\n        }\n      }\n    }\n    case OpCodes.OP_SUCCESS: {\n      switch (that._tag) {\n        case OpCodes.OP_SUCCESS:\n          return exitSucceed(onSuccess(self.effect_instruction_i0, that.effect_instruction_i0))\n        case OpCodes.OP_FAILURE:\n          return exitFailCause(that.effect_instruction_i0)\n      }\n    }\n  }\n})\n\nconst exitCollectAllInternal = <A, E>(\n  exits: Iterable<Exit.Exit<A, E>>,\n  combineCauses: (causeA: Cause.Cause<E>, causeB: Cause.Cause<E>) => Cause.Cause<E>\n): Option.Option<Exit.Exit<Array<A>, E>> => {\n  const list = Chunk.fromIterable(exits)\n  if (!Chunk.isNonEmpty(list)) {\n    return Option.none()\n  }\n  return pipe(\n    Chunk.tailNonEmpty(list),\n    Arr.reduce(\n      pipe(Chunk.headNonEmpty(list), exitMap<A, Chunk.Chunk<A>>(Chunk.of)),\n      (accumulator, current) =>\n        pipe(\n          accumulator,\n          exitZipWith(current, {\n            onSuccess: (list, value) => pipe(list, Chunk.prepend(value)),\n            onFailure: combineCauses\n          })\n        )\n    ),\n    exitMap(Chunk.reverse),\n    exitMap((chunk) => Chunk.toReadonlyArray(chunk) as Array<A>),\n    Option.some\n  )\n}\n\n// -----------------------------------------------------------------------------\n// Deferred\n// -----------------------------------------------------------------------------\n\n/** @internal */\nexport const deferredUnsafeMake = <A, E = never>(fiberId: FiberId.FiberId): Deferred.Deferred<A, E> => {\n  const _deferred = {\n    ...CommitPrototype,\n    [deferred.DeferredTypeId]: deferred.deferredVariance,\n    state: MutableRef.make(deferred.pending<A, E>([])),\n    commit() {\n      return deferredAwait(this)\n    },\n    blockingOn: fiberId\n  }\n  return _deferred\n}\n\n/* @internal */\nexport const deferredMake = <A, E = never>(): Effect.Effect<Deferred.Deferred<A, E>> =>\n  flatMap(fiberId, (id) => deferredMakeAs<A, E>(id))\n\n/* @internal */\nexport const deferredMakeAs = <A, E = never>(fiberId: FiberId.FiberId): Effect.Effect<Deferred.Deferred<A, E>> =>\n  sync(() => deferredUnsafeMake<A, E>(fiberId))\n\n/* @internal */\nexport const deferredAwait = <A, E>(self: Deferred.Deferred<A, E>): Effect.Effect<A, E> =>\n  asyncInterrupt<A, E>((resume) => {\n    const state = MutableRef.get(self.state)\n    switch (state._tag) {\n      case DeferredOpCodes.OP_STATE_DONE: {\n        return resume(state.effect)\n      }\n      case DeferredOpCodes.OP_STATE_PENDING: {\n        // we can push here as the internal state is mutable\n        state.joiners.push(resume)\n        return deferredInterruptJoiner(self, resume)\n      }\n    }\n  }, self.blockingOn)\n\n/* @internal */\nexport const deferredComplete: {\n  <A, E>(effect: Effect.Effect<A, E>): (self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>\n  <A, E>(self: Deferred.Deferred<A, E>, effect: Effect.Effect<A, E>): Effect.Effect<boolean>\n} = dual(\n  2,\n  <A, E>(self: Deferred.Deferred<A, E>, effect: Effect.Effect<A, E>): Effect.Effect<boolean> =>\n    intoDeferred(effect, self)\n)\n\n/* @internal */\nexport const deferredCompleteWith = dual<\n  <A, E>(effect: Effect.Effect<A, E>) => (self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>,\n  <A, E>(self: Deferred.Deferred<A, E>, effect: Effect.Effect<A, E>) => Effect.Effect<boolean>\n>(2, (self, effect) =>\n  sync(() => {\n    const state = MutableRef.get(self.state)\n    switch (state._tag) {\n      case DeferredOpCodes.OP_STATE_DONE: {\n        return false\n      }\n      case DeferredOpCodes.OP_STATE_PENDING: {\n        MutableRef.set(self.state, deferred.done(effect))\n        for (let i = 0, len = state.joiners.length; i < len; i++) {\n          state.joiners[i](effect)\n        }\n        return true\n      }\n    }\n  }))\n\n/* @internal */\nexport const deferredDone = dual<\n  <A, E>(exit: Exit.Exit<A, E>) => (self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>,\n  <A, E>(self: Deferred.Deferred<A, E>, exit: Exit.Exit<A, E>) => Effect.Effect<boolean>\n>(2, (self, exit) => deferredCompleteWith(self, exit))\n\n/* @internal */\nexport const deferredFail = dual<\n  <E>(error: E) => <A>(self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>,\n  <A, E>(self: Deferred.Deferred<A, E>, error: E) => Effect.Effect<boolean>\n>(2, (self, error) => deferredCompleteWith(self, fail(error)))\n\n/* @internal */\nexport const deferredFailSync = dual<\n  <E>(evaluate: LazyArg<E>) => <A>(self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>,\n  <A, E>(self: Deferred.Deferred<A, E>, evaluate: LazyArg<E>) => Effect.Effect<boolean>\n>(2, (self, evaluate) => deferredCompleteWith(self, failSync(evaluate)))\n\n/* @internal */\nexport const deferredFailCause = dual<\n  <E>(cause: Cause.Cause<E>) => <A>(self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>,\n  <A, E>(self: Deferred.Deferred<A, E>, cause: Cause.Cause<E>) => Effect.Effect<boolean>\n>(2, (self, cause) => deferredCompleteWith(self, failCause(cause)))\n\n/* @internal */\nexport const deferredFailCauseSync = dual<\n  <E>(evaluate: LazyArg<Cause.Cause<E>>) => <A>(self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>,\n  <A, E>(self: Deferred.Deferred<A, E>, evaluate: LazyArg<Cause.Cause<E>>) => Effect.Effect<boolean>\n>(2, (self, evaluate) => deferredCompleteWith(self, failCauseSync(evaluate)))\n\n/* @internal */\nexport const deferredDie = dual<\n  (defect: unknown) => <A, E>(self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>,\n  <A, E>(self: Deferred.Deferred<A, E>, defect: unknown) => Effect.Effect<boolean>\n>(2, (self, defect) => deferredCompleteWith(self, die(defect)))\n\n/* @internal */\nexport const deferredDieSync = dual<\n  (evaluate: LazyArg<unknown>) => <A, E>(self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>,\n  <A, E>(self: Deferred.Deferred<A, E>, evaluate: LazyArg<unknown>) => Effect.Effect<boolean>\n>(2, (self, evaluate) => deferredCompleteWith(self, dieSync(evaluate)))\n\n/* @internal */\nexport const deferredInterrupt = <A, E>(self: Deferred.Deferred<A, E>): Effect.Effect<boolean> =>\n  flatMap(fiberId, (fiberId) => deferredCompleteWith(self, interruptWith(fiberId)))\n\n/* @internal */\nexport const deferredInterruptWith = dual<\n  (fiberId: FiberId.FiberId) => <A, E>(self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>,\n  <A, E>(self: Deferred.Deferred<A, E>, fiberId: FiberId.FiberId) => Effect.Effect<boolean>\n>(2, (self, fiberId) => deferredCompleteWith(self, interruptWith(fiberId)))\n\n/* @internal */\nexport const deferredIsDone = <A, E>(self: Deferred.Deferred<A, E>): Effect.Effect<boolean> =>\n  sync(() => MutableRef.get(self.state)._tag === DeferredOpCodes.OP_STATE_DONE)\n\n/* @internal */\nexport const deferredPoll = <A, E>(\n  self: Deferred.Deferred<A, E>\n): Effect.Effect<Option.Option<Effect.Effect<A, E>>> =>\n  sync(() => {\n    const state = MutableRef.get(self.state)\n    switch (state._tag) {\n      case DeferredOpCodes.OP_STATE_DONE: {\n        return Option.some(state.effect)\n      }\n      case DeferredOpCodes.OP_STATE_PENDING: {\n        return Option.none()\n      }\n    }\n  })\n\n/* @internal */\nexport const deferredSucceed = dual<\n  <A>(value: A) => <E>(self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>,\n  <A, E>(self: Deferred.Deferred<A, E>, value: A) => Effect.Effect<boolean>\n>(2, (self, value) => deferredCompleteWith(self, succeed(value)))\n\n/* @internal */\nexport const deferredSync = dual<\n  <A>(evaluate: LazyArg<A>) => <E>(self: Deferred.Deferred<A, E>) => Effect.Effect<boolean>,\n  <A, E>(self: Deferred.Deferred<A, E>, evaluate: LazyArg<A>) => Effect.Effect<boolean>\n>(2, (self, evaluate) => deferredCompleteWith(self, sync(evaluate)))\n\n/** @internal */\nexport const deferredUnsafeDone = <A, E>(self: Deferred.Deferred<A, E>, effect: Effect.Effect<A, E>): void => {\n  const state = MutableRef.get(self.state)\n  if (state._tag === DeferredOpCodes.OP_STATE_PENDING) {\n    MutableRef.set(self.state, deferred.done(effect))\n    for (let i = 0, len = state.joiners.length; i < len; i++) {\n      state.joiners[i](effect)\n    }\n  }\n}\n\nconst deferredInterruptJoiner = <A, E>(\n  self: Deferred.Deferred<A, E>,\n  joiner: (effect: Effect.Effect<A, E>) => void\n): Effect.Effect<void> =>\n  sync(() => {\n    const state = MutableRef.get(self.state)\n    if (state._tag === DeferredOpCodes.OP_STATE_PENDING) {\n      const index = state.joiners.indexOf(joiner)\n      if (index >= 0) {\n        // we can splice here as the internal state is mutable\n        state.joiners.splice(index, 1)\n      }\n    }\n  })\n\n// -----------------------------------------------------------------------------\n// Context\n// -----------------------------------------------------------------------------\n\nconst constContext = withFiberRuntime((fiber) => exitSucceed(fiber.currentContext))\n\n/* @internal */\nexport const context = <R>(): Effect.Effect<Context.Context<R>, never, R> => constContext as any\n\n/* @internal */\nexport const contextWith = <R0, A>(\n  f: (context: Context.Context<R0>) => A\n): Effect.Effect<A, never, R0> => map(context<R0>(), f)\n\n/* @internal */\nexport const contextWithEffect = <R2, A, E, R>(\n  f: (context: Context.Context<R2>) => Effect.Effect<A, E, R>\n): Effect.Effect<A, E, R | R2> => flatMap(context<R2>(), f)\n\n/* @internal */\nexport const provideContext = dual<\n  <R>(context: Context.Context<R>) => <A, E>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E>,\n  <A, E, R>(self: Effect.Effect<A, E, R>, context: Context.Context<R>) => Effect.Effect<A, E>\n>(2, <A, E, R>(self: Effect.Effect<A, E, R>, context: Context.Context<R>) =>\n  fiberRefLocally(\n    currentContext,\n    context\n  )(self as Effect.Effect<A, E>))\n\n/* @internal */\nexport const provideSomeContext = dual<\n  <R>(context: Context.Context<R>) => <A, E, R1>(self: Effect.Effect<A, E, R1>) => Effect.Effect<A, E, Exclude<R1, R>>,\n  <A, E, R1, R>(self: Effect.Effect<A, E, R1>, context: Context.Context<R>) => Effect.Effect<A, E, Exclude<R1, R>>\n>(2, <A, E, R1, R>(self: Effect.Effect<A, E, R1>, context: Context.Context<R>) =>\n  fiberRefLocallyWith(\n    currentContext,\n    (parent) => Context.merge(parent, context)\n  )(self as Effect.Effect<A, E>))\n\n/* @internal */\nexport const mapInputContext = dual<\n  <R2, R>(\n    f: (context: Context.Context<R2>) => Context.Context<R>\n  ) => <A, E>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E, R2>,\n  <A, E, R, R2>(\n    self: Effect.Effect<A, E, R>,\n    f: (context: Context.Context<R2>) => Context.Context<R>\n  ) => Effect.Effect<A, E, R2>\n>(2, <A, E, R, R2>(\n  self: Effect.Effect<A, E, R>,\n  f: (context: Context.Context<R2>) => Context.Context<R>\n) => contextWithEffect((context: Context.Context<R2>) => provideContext(self, f(context))))\n\n// -----------------------------------------------------------------------------\n// Filtering\n// -----------------------------------------------------------------------------\n\n/** @internal */\nexport const filterEffectOrElse: {\n  <A, E2, R2, A2, E3, R3>(\n    options: {\n      readonly predicate: (a: NoInfer<A>) => Effect.Effect<boolean, E2, R2>\n      readonly orElse: (a: NoInfer<A>) => Effect.Effect<A2, E3, R3>\n    }\n  ): <E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A | A2, E | E2 | E3, R | R2 | R3>\n  <A, E, R, E2, R2, A2, E3, R3>(\n    self: Effect.Effect<A, E, R>,\n    options: {\n      readonly predicate: (a: A) => Effect.Effect<boolean, E2, R2>\n      readonly orElse: (a: A) => Effect.Effect<A2, E3, R3>\n    }\n  ): Effect.Effect<A | A2, E | E2 | E3, R | R2 | R3>\n} = dual(2, <A, E, R, E2, R2, A2, E3, R3>(\n  self: Effect.Effect<A, E, R>,\n  options: {\n    readonly predicate: (a: A) => Effect.Effect<boolean, E2, R2>\n    readonly orElse: (a: A) => Effect.Effect<A2, E3, R3>\n  }\n): Effect.Effect<A | A2, E | E2 | E3, R | R2 | R3> =>\n  flatMap(\n    self,\n    (a) =>\n      flatMap(\n        options.predicate(a),\n        (pass): Effect.Effect<A | A2, E3, R3> => pass ? succeed(a) : options.orElse(a)\n      )\n  ))\n\n/** @internal */\nexport const filterEffectOrFail: {\n  <A, E2, R2, E3>(\n    options: {\n      readonly predicate: (a: NoInfer<A>) => Effect.Effect<boolean, E2, R2>\n      readonly orFailWith: (a: NoInfer<A>) => E3\n    }\n  ): <E, R>(self: Effect.Effect<A, E, R>) => Effect.Effect<A, E | E2 | E3, R | R2>\n  <A, E, R, E2, R2, E3>(\n    self: Effect.Effect<A, E, R>,\n    options: {\n      readonly predicate: (a: A) => Effect.Effect<boolean, E2, R2>\n      readonly orFailWith: (a: A) => E3\n    }\n  ): Effect.Effect<A, E | E2 | E3, R | R2>\n} = dual(2, <A, E, R, E2, R2, E3>(\n  self: Effect.Effect<A, E, R>,\n  options: {\n    readonly predicate: (a: A) => Effect.Effect<boolean, E2, R2>\n    readonly orFailWith: (a: A) => E3\n  }\n): Effect.Effect<A, E | E2 | E3, R | R2> =>\n  filterEffectOrElse(self, {\n    predicate: options.predicate,\n    orElse: (a) => fail(options.orFailWith(a))\n  }))\n\n// -----------------------------------------------------------------------------\n// Tracing\n// -----------------------------------------------------------------------------\n\n/** @internal */\nexport const currentSpanFromFiber = <A, E>(fiber: Fiber.RuntimeFiber<A, E>): Option.Option<Tracer.Span> => {\n  const span = fiber.currentSpan\n  return span !== undefined && span._tag === \"Span\" ? Option.some(span) : Option.none()\n}\n\nconst NoopSpanProto: Omit<Tracer.Span, \"parent\" | \"name\" | \"context\"> = {\n  _tag: \"Span\",\n  spanId: \"noop\",\n  traceId: \"noop\",\n  sampled: false,\n  status: {\n    _tag: \"Ended\",\n    startTime: BigInt(0),\n    endTime: BigInt(0),\n    exit: exitVoid\n  },\n  attributes: new Map(),\n  links: [],\n  kind: \"internal\",\n  attribute() {},\n  event() {},\n  end() {},\n  addLinks() {}\n}\n\n/** @internal */\nexport const noopSpan = (options: {\n  readonly name: string\n  readonly parent: Option.Option<Tracer.AnySpan>\n  readonly context: Context.Context<never>\n}): Tracer.Span => Object.assign(Object.create(NoopSpanProto), options)\n"], "names": ["Arr", "Chunk", "Context", "Duration", "Either", "Equal", "FiberId", "dual", "identity", "pipe", "globalValue", "Hash", "HashMap", "format", "NodeInspectSymbol", "toJSON", "List", "MutableRef", "Option", "pipeArguments", "hasProperty", "isObject", "isPromiseLike", "RuntimeFlagsPatch", "internalCall", "YieldWrap", "blockedRequests_", "internalCause", "deferred", "<PERSON><PERSON><PERSON><PERSON>", "CommitPrototype", "effectVariance", "StructuralCommitPrototype", "getBugErrorMessage", "DeferredOpCodes", "OpCodes", "runtimeFlags_", "SingleShotGen", "blocked", "blockedRequests", "_continue", "effect", "EffectPrimitive", "effect_instruction_i0", "effect_instruction_i1", "runRequestBlock", "EffectTypeId", "Symbol", "for", "RevertFlags", "patch", "op", "_op", "OP_REVERT_FLAGS", "constructor", "undefined", "effect_instruction_i2", "trace", "symbol", "that", "cached", "random", "arguments", "_id", "toString", "iterator", "EffectPrimitiveFailure", "_tag", "exitIsExit", "equals", "string", "combine", "hash", "cause", "EffectPrimitiveSuccess", "value", "isEffect", "u", "withFiberRuntime", "with<PERSON>unt<PERSON>", "OP_WITH_RUNTIME", "acquireUseRelease", "acquire", "use", "release", "uninterruptibleMask", "restore", "flatMap", "a", "exit", "suspend", "matchCauseEffect", "onFailure", "OP_FAILURE", "failCause", "sequential", "OP_SUCCESS", "onSuccess", "as", "self", "succeed", "asVoid", "custom", "wrapper", "OP_COMMIT", "length", "commit", "Error", "unsafeAsync", "register", "blockingOn", "none", "OP_ASYNC", "cancelerRef", "resume", "onInterrupt", "_", "void_", "asyncInterrupt", "async_", "backing<PERSON><PERSON>ume", "pendingEffect", "proxyResume", "controllerRef", "AbortController", "signal", "abort", "async", "catchAllCause", "f", "OP_ON_FAILURE", "catchAll", "matchEffect", "catchIf", "predicate", "either", "failureOrCause", "left", "right", "catchSome", "pf", "getOr<PERSON><PERSON>e", "checkInterruptible", "status", "interruption", "runtimeFlags", "originalSymbol", "originalInstance", "obj", "capture", "span", "isSome", "Proxy", "has", "target", "p", "spanSymbol", "get", "die", "defect", "fiber", "currentSpanFromFiber", "dieMessage", "message", "failCauseSync", "RuntimeException", "dieSync", "evaluate", "sync", "e", "matchCause", "exitFailCause", "exitSucceed", "fail", "error", "failSync", "fiberId", "state", "id", "fiberIdWith", "OP_ON_SUCCESS", "and<PERSON><PERSON>", "b", "then", "UnknownException", "step", "flatten", "flip", "options", "OP_ON_SUCCESS_AND_FAILURE", "defects", "electFailures", "failures", "unsafeHead", "forEachSequential", "arr", "fromIterable", "ret", "allocate", "i", "<PERSON><PERSON><PERSON>", "while", "body", "forEachSequentialDiscard", "if_", "args", "onTrue", "onFalse", "interrupt", "interruptWith", "interruptible", "OP_UPDATE_RUNTIME_FLAGS", "enable", "Interruption", "interruptibleMask", "oldFlags", "uninterruptible", "into<PERSON><PERSON><PERSON><PERSON>", "deferredDone", "map", "mapBoth", "mapError", "onError", "cleanup", "onExit", "exitIsSuccess", "cause1", "result", "cause2", "success", "zipRight", "exitMatch", "isInterruptedOnly", "interruptors", "orElse", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "orDieWith", "partitionMap", "thunk", "OP_SYNC", "tap", "transplant", "scopeOverride", "getFiberRef", "currentForkScopeOverride", "scope", "fiberRefLocally", "some", "getOrThrow", "keepDefectsAndElectFailures", "disable", "void", "updateRuntimeFlags", "whenEffect", "condition", "OP_WHILE", "fromIterator", "OP_ITERATOR", "gen", "bind", "fnUntraced", "pipeables", "Object", "defineProperty", "apply", "x", "configurable", "withConcurrency", "concurrency", "currentConcurrency", "withRequestBatching", "requestBatching", "currentRequestBatching", "withRuntimeFlags", "update", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled", "currentTracerEnabled", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentTracer<PERSON><PERSON>ing<PERSON>nabled", "yieldNow", "OP_YIELD", "priority", "withSchedulingPriority", "zip", "zipFlatten", "zipLeft", "zipWith", "never", "interval", "setInterval", "clearInterval", "interruptFiber", "interruptAsFiber", "interruptAsFork", "await", "logLevelAll", "syslog", "label", "ordinal", "Number", "MIN_SAFE_INTEGER", "logLevelFatal", "logLevelError", "logLevelWarning", "logLevelInfo", "logLevelDebug", "logLevelTrace", "logLevelNone", "MAX_SAFE_INTEGER", "allLogLevels", "FiberRefSymbolKey", "FiberRefTypeId", "fiberRefVariance", "_A", "fiberRefGet", "fiberRefGetAndSet", "fiberRefModify", "v", "fiberRefGetAndUpdate", "fiberRefGetAndUpdateSome", "fiberRefGetWith", "fiberRefSet", "fiberRefDelete", "unsafeDeleteFiberRef", "fiberRefReset", "initial", "setFiberRef", "fiberRefModifySome", "def", "fiberRefUpdate", "fiberRefUpdateSome", "fiberRefUpdateAndGet", "fiberRefUpdateSomeAndGet", "RequestResolverSymbolKey", "RequestResolverTypeId", "requestResolverVariance", "_R", "RequestResolverImpl", "runAll", "isRequestResolver", "identified", "ids", "resolverLocally", "requests", "make", "requestBlockLocally", "ref", "reduce", "LocallyReducer", "emptyCase", "empty", "parCase", "par", "seqCase", "seq", "singleCase", "dataSource", "blockedRequest", "single", "oldValue", "fiberRefLocallyWith", "fiberRefUnsafeMake", "fiberRefUnsafeMakePatch", "differ", "fork", "join", "fiberRefUnsafeMakeHashSet", "hashSet", "fiberRefUnsafeMakeReadonlyArray", "readon<PERSON><PERSON><PERSON><PERSON>", "fiberRefUnsafeMakeContext", "environment", "_fiberRef", "diff", "newValue", "first", "second", "n", "fiberRefUnsafeMakeRuntimeFlags", "currentContext", "currentSchedulingPriority", "currentMaxOpsBeforeYield", "currentLogAnnotations", "currentLogLevel", "currentLogSpan", "scheduler", "withMaxOpsBeforeYield", "currentUnhandledErrorLogLevel", "withUnhandledErrorLogLevel", "level", "currentMetricLabels", "metricLabels", "parent", "currentInterruptedCause", "currentTracerSpanAnnotations", "currentTracerSpanLinks", "ScopeTypeId", "CloseableScopeTypeId", "scopeAddFinalizer", "finalizer", "addFinalizer", "scopeAddFinalizerExit", "scopeClose", "close", "scopeFork", "strategy", "causeSquash", "causeSquashWith", "option", "failureOption", "head", "match", "onNone", "interrupts", "InterruptedException", "onSome", "YieldableError", "globalThis", "prototype", "stack", "split", "slice", "pretty", "renderErrorCause", "assign", "makeException", "proto", "tag", "Base", "name", "RuntimeExceptionTypeId", "isRuntimeException", "InterruptedExceptionTypeId", "isInterruptedException", "IllegalArgumentExceptionTypeId", "IllegalArgumentException", "isIllegalArgumentException", "NoSuchElementExceptionTypeId", "NoSuchElementException", "isNoSuchElementException", "InvalidPubSubCapacityExceptionTypeId", "InvalidPubSubCapacityException", "ExceededCapacityExceptionTypeId", "ExceededCapacityException", "isExceededCapacityException", "isInvalidCapacityError", "TimeoutExceptionTypeId", "TimeoutException", "timeoutExceptionFromDuration", "duration", "isTimeoutException", "UnknownExceptionTypeId", "isUnknownException", "exitIsFailure", "exitIsInterrupted", "isInterrupted", "exitAs", "exitAsVoid", "exitCauseOption", "exitCollectAll", "exits", "exitCollectAllInternal", "parallel", "exitDie", "exitExists", "refinement", "exitFail", "exitFlatMap", "exitFlatMapEffect", "exitFlatten", "exitForEachEffect", "exitFromEither", "exitFromOption", "exitGetOrElse", "exitInterrupt", "exitMap", "exitMapBoth", "exitMapError", "exitMapErrorCause", "exitMatchEffect", "exitVoid", "exitZip", "exitZipWith", "a2", "exitZipLeft", "exitZipRight", "exitZipPar", "exitZipParLeft", "exitZipParRight", "combineCauses", "list", "isNonEmpty", "tailNonEmpty", "headNonEmpty", "of", "accumulator", "current", "prepend", "reverse", "chunk", "toReadonlyArray", "deferredUnsafeMake", "_deferred", "DeferredTypeId", "deferred<PERSON><PERSON>ce", "pending", "deferred<PERSON><PERSON><PERSON>", "deferred<PERSON><PERSON>", "deferred<PERSON>akeAs", "OP_STATE_DONE", "OP_STATE_PENDING", "joiners", "push", "deferredInterruptJoiner", "deferredComplete", "deferredCompleteWith", "set", "done", "len", "deferredFail", "deferredFailSync", "deferred<PERSON>ail<PERSON><PERSON><PERSON>", "deferredFailCauseSync", "deferred<PERSON>ie", "deferred<PERSON><PERSON><PERSON><PERSON>", "deferredInterrupt", "deferredInterruptWith", "deferredIsDone", "deferred<PERSON><PERSON>", "deferred<PERSON>ucceed", "deferredSync", "deferredUnsafeDone", "joiner", "index", "indexOf", "splice", "constContext", "context", "contextWith", "contextWithEffect", "provideContext", "provideSomeContext", "merge", "mapInputContext", "filterEffectOrElse", "pass", "filterEffectOrFail", "orFailWith", "currentSpan", "NoopSpanProto", "spanId", "traceId", "sampled", "startTime", "BigInt", "endTime", "attributes", "Map", "links", "kind", "attribute", "event", "end", "addLinks", "noopSpan", "create"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,GAAG,MAAM,aAAa;AAElC,OAAO,KAAKC,KAAK,MAAM,aAAa;AACpC,OAAO,KAAKC,OAAO,MAAM,eAAe;AAGxC,OAAO,KAAKC,QAAQ,MAAM,gBAAgB;AAE1C,OAAO,KAAKC,MAAM,MAAM,cAAc;AACtC,OAAO,KAAKC,KAAK,MAAM,aAAa;AAIpC,OAAO,KAAKC,OAAO,MAAM,eAAe;AAIxC,SAASC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,gBAAgB;AACrD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,OAAO,KAAKC,IAAI,MAAM,YAAY;AAClC,OAAO,KAAKC,OAAO,MAAM,eAAe;AAExC,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,MAAM,QAAQ,mBAAmB;AACrE,OAAO,KAAKC,IAAI,MAAM,YAAY;AAIlC,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAC9C,OAAO,KAAKC,MAAM,MAAM,cAAc;AACtC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,WAAW,EAAEC,QAAQ,EAAEC,aAAa,QAAyC,iBAAiB;AAKvG,OAAO,KAAKC,iBAAiB,MAAM,yBAAyB;AAI5D,SAASC,YAAY,EAAEC,SAAS,QAAQ,aAAa;AACrD,OAAO,KAAKC,gBAAgB,MAAM,sBAAsB;AACxD,OAAO,KAAKC,aAAa,MAAM,YAAY;AAC3C,OAAO,KAAKC,QAAQ,MAAM,eAAe;AACzC,OAAO,KAAKC,cAAc,MAAM,aAAa;AAC7C,SAASC,eAAe,EAAEC,cAAc,EAAEC,yBAAyB,QAAQ,iBAAiB;AAC5F,SAASC,kBAAkB,QAAQ,aAAa;AAGhD,OAAO,KAAKC,eAAe,MAAM,uBAAuB;AACxD,OAAO,KAAKC,OAAO,MAAM,qBAAqB;AAC9C,OAAO,KAAKC,aAAa,MAAM,mBAAmB;AAClD,SAASC,aAAa,QAAQ,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C,MAAMC,OAAO,GAAGA,CACrBC,eAA6C,EAC7CC,SAA8B,KACN;IACxB,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,SAAS,CAAQ;IACpDD,MAAM,CAACE,qBAAqB,GAAGJ,eAAe;IAC9CE,MAAM,CAACG,qBAAqB,GAAGJ,SAAS;IACxC,OAAOC,MAAM;AACf,CAAC;AAKM,MAAMI,eAAe,IAC1BN,eAA6C,IACtB;IACvB,MAAME,MAAM,GAAG,IAAIC,eAAe,CAAC,YAAY,CAAQ;IACvDD,MAAM,CAACE,qBAAqB,GAAGJ,eAAe;IAC9C,OAAOE,MAAM;AACf,CAAC;AAGM,MAAMK,YAAY,GAAA,WAAA,GAAwBC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAwB;AAmC7F,MAAOC,WAAW;IAGXC,KAAA,CAAA;IACAC,EAAA,CAAA;IAHFC,GAAG,0KAAGjB,OAAO,CAACkB,UAAe,CAAA;IACtCC,YACWJ,KAA0C,EAC1CC,EAAwD,CAAA;QADxD,IAAA,CAAAD,KAAK,GAALA,KAAK;QACL,IAAA,CAAAC,EAAE,GAAFA,EAAE;IAEb;;AAGF,MAAMT,eAAe;IAMEU,GAAA,CAAA;IALdT,qBAAqB,GAAGY,SAAS,CAAA;IACjCX,qBAAqB,GAAGW,SAAS,CAAA;IACjCC,qBAAqB,GAAGD,SAAS,CAAA;IACjCE,KAAK,GAAGF,SAAS,CAAA;IACxB,CAACT,YAAY,CAAA,mKAAIf,iBAAc,CAAA;IAC/BuB,YAAqBF,GAAqB,CAAA;QAArB,IAAA,CAAAA,GAAG,GAAHA,GAAG;IAAqB;IAC7C,gJAAC/C,KAAK,CAACqD,GAAM,CAAA,CAAYC,IAAa,EAAA;QACpC,OAAO,IAAI,KAAKA,IAAI;IACtB;IACA,+IAAChD,IAAI,CAAC+C,IAAM,CAAA,GAAC;QACX,yJAAO/C,IAAI,CAACiD,IAAAA,AAAM,EAAC,IAAI,EAAEjD,IAAI,CAACkD,sJAAM,AAANA,EAAO,IAAI,CAAC,CAAC;IAC7C;IACApD,IAAIA,CAAA,EAAA;QACF,6JAAOU,gBAAAA,AAAa,EAAC,IAAI,EAAE2C,SAAS,CAAC;IACvC;IACA/C,MAAMA,CAAA,EAAA;QACJ,OAAO;YACLgD,GAAG,EAAE,QAAQ;YACbX,GAAG,EAAE,IAAI,CAACA,GAAG;YACbT,qBAAqB,0JAAE5B,UAAAA,AAAM,EAAC,IAAI,CAAC4B,qBAAqB,CAAC;YACzDC,qBAAqB,2JAAE7B,SAAAA,AAAM,EAAC,IAAI,CAAC6B,qBAAqB,CAAC;YACzDY,qBAAqB,MAAEzC,8JAAAA,AAAM,EAAC,IAAI,CAACyC,qBAAqB;SACzD;IACH;IACAQ,QAAQA,CAAA,EAAA;QACN,gKAAOnD,SAAAA,AAAM,EAAC,IAAI,CAACE,MAAM,EAAE,CAAC;IAC9B;IACA,CAACD,yKAAiB,CAAA,GAAC;QACjB,OAAO,IAAI,CAACC,MAAM,EAAE;IACtB;IACA,CAACgC,MAAM,CAACkB,QAAQ,CAAA,GAAC;QACf,OAAO,IAAI5B,mLAAa,CAAC,IAAIZ,2JAAS,CAAC,IAAI,CAAC,CAAC;IAC/C;;AAGF,cAAA,GACA,MAAMyC,sBAAsB;IAMLd,GAAA,CAAA;IALdT,qBAAqB,GAAGY,SAAS,CAAA;IACjCX,qBAAqB,GAAGW,SAAS,CAAA;IACjCC,qBAAqB,GAAGD,SAAS,CAAA;IACjCE,KAAK,GAAGF,SAAS,CAAA;IACxB,CAACT,YAAY,CAAA,mKAAIf,iBAAc,CAAA;IAC/BuB,YAAqBF,GAAqB,CAAA;QAArB,IAAA,CAAAA,GAAG,GAAHA,GAAG;QACtB,mBAAA;QACA,IAAI,CAACe,IAAI,GAAGf,GAAG;IACjB;IACA,gJAAC/C,KAAK,CAACqD,GAAM,CAAA,CAAYC,IAAa,EAAA;QACpC,OAAOS,UAAU,CAACT,IAAI,CAAC,IAAIA,IAAI,CAACP,GAAG,KAAK,SAAS,IAC/C,mBAAA;2JACA/C,KAAK,CAACgE,GAAAA,AAAM,EAAC,IAAI,CAAC1B,qBAAqB,EAAEgB,IAAI,CAAChB,qBAAqB,CAAC;IACxE;IACA,+IAAChC,IAAI,CAAC+C,IAAM,CAAA,GAAC;QACX,WAAOjD,yJAAAA,AAAI,EACT,mBAAA;0JACAE,IAAI,CAAC2D,IAAM,AAANA,EAAO,IAAI,CAACH,IAAI,CAAC,EACtB,mBAAA;QACAxD,IAAI,CAAC4D,uJAAAA,AAAO,oJAAC5D,IAAI,CAAC6D,EAAAA,AAAI,EAAC,IAAI,CAAC7B,qBAAqB,CAAC,CAAC,oJACnDhC,IAAI,CAACiD,IAAAA,AAAM,EAAC,IAAI,CAAC,CAClB;IACH;IACA,IAAIa,KAAKA,CAAA,EAAA;QACP,OAAO,IAAI,CAAC9B,qBAAqB;IACnC;IACAlC,IAAIA,CAAA,EAAA;QACF,6JAAOU,gBAAa,AAAbA,EAAc,IAAI,EAAE2C,SAAS,CAAC;IACvC;IACA/C,MAAMA,CAAA,EAAA;QACJ,OAAO;YACLgD,GAAG,EAAE,MAAM;YACXI,IAAI,EAAE,IAAI,CAACf,GAAG;YACdqB,KAAK,EAAG,IAAI,CAACA,KAAa,CAAC1D,MAAM;SAClC;IACH;IACAiD,QAAQA,CAAA,EAAA;QACN,QAAOnD,iKAAAA,AAAM,EAAC,IAAI,CAACE,MAAM,EAAE,CAAC;IAC9B;IACA,CAACD,yKAAiB,CAAA,GAAC;QACjB,OAAO,IAAI,CAACC,MAAM,EAAE;IACtB;IACA,CAACgC,MAAM,CAACkB,QAAQ,CAAA,GAAC;QACf,OAAO,uKAAI5B,gBAAa,CAAC,mJAAIZ,YAAS,CAAC,IAAI,CAAC,CAAC;IAC/C;;AAGF,cAAA,GACA,MAAMiD,sBAAsB;IAMLtB,GAAA,CAAA;IALdT,qBAAqB,GAAGY,SAAS,CAAA;IACjCX,qBAAqB,GAAGW,SAAS,CAAA;IACjCC,qBAAqB,GAAGD,SAAS,CAAA;IACjCE,KAAK,GAAGF,SAAS,CAAA;IACxB,CAACT,YAAY,CAAA,mKAAIf,iBAAc,CAAA;IAC/BuB,YAAqBF,GAAqB,CAAA;QAArB,IAAA,CAAAA,GAAG,GAAHA,GAAG;QACtB,mBAAA;QACA,IAAI,CAACe,IAAI,GAAGf,GAAG;IACjB;IACA,gJAAC/C,KAAK,CAACqD,GAAM,CAAA,CAAYC,IAAa,EAAA;QACpC,OAAOS,UAAU,CAACT,IAAI,CAAC,IAAIA,IAAI,CAACP,GAAG,KAAK,SAAS,IAC/C,mBAAA;2JACA/C,KAAK,CAACgE,GAAAA,AAAM,EAAC,IAAI,CAAC1B,qBAAqB,EAAEgB,IAAI,CAAChB,qBAAqB,CAAC;IACxE;IACA,+IAAChC,IAAI,CAAC+C,IAAM,CAAA,GAAC;QACX,WAAOjD,yJAAAA,AAAI,EACT,mBAAA;0JACAE,IAAI,CAAC2D,IAAAA,AAAM,EAAC,IAAI,CAACH,IAAI,CAAC,EACtB,mBAAA;0JACAxD,IAAI,CAAC4D,KAAO,AAAPA,oJAAQ5D,IAAI,CAAC6D,EAAAA,AAAI,EAAC,IAAI,CAAC7B,qBAAqB,CAAC,CAAC,oJACnDhC,IAAI,CAACiD,IAAAA,AAAM,EAAC,IAAI,CAAC,CAClB;IACH;IACA,IAAIe,KAAKA,CAAA,EAAA;QACP,OAAO,IAAI,CAAChC,qBAAqB;IACnC;IACAlC,IAAIA,CAAA,EAAA;QACF,6JAAOU,gBAAAA,AAAa,EAAC,IAAI,EAAE2C,SAAS,CAAC;IACvC;IACA/C,MAAMA,CAAA,EAAA;QACJ,OAAO;YACLgD,GAAG,EAAE,MAAM;YACXI,IAAI,EAAE,IAAI,CAACf,GAAG;YACduB,KAAK,0JAAE5D,UAAAA,AAAM,EAAC,IAAI,CAAC4D,KAAK;SACzB;IACH;IACAX,QAAQA,CAAA,EAAA;QACN,gKAAOnD,SAAAA,AAAM,EAAC,IAAI,CAACE,MAAM,EAAE,CAAC;IAC9B;IACA,sJAACD,oBAAiB,CAAA,GAAC;QACjB,OAAO,IAAI,CAACC,MAAM,EAAE;IACtB;IACA,CAACgC,MAAM,CAACkB,QAAQ,CAAA,GAAC;QACf,OAAO,uKAAI5B,gBAAa,CAAC,mJAAIZ,YAAS,CAAC,IAAI,CAAC,CAAC;IAC/C;;AA6HK,MAAMmD,QAAQ,IAAIC,CAAU,0JAAoDzD,cAAAA,AAAW,EAACyD,CAAC,EAAE/B,YAAY,CAAC;AAG5G,MAAMgC,gBAAgB,IAC3BC,WAA4G,IAClF;IAC1B,MAAMtC,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAAC6C,UAAe,CAAQ;IAClEvC,MAAM,CAACE,qBAAqB,GAAGoC,WAAW;IAC1C,OAAOtC,MAAM;AACf,CAAC;AAGM,MAAMwC,iBAAiB,GAAA,WAAA,yJAU1B1E,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV2E,OAA+B,EAC/BC,GAAwC,EACxCC,OAAuE,GAEvEC,mBAAmB,EAAEC,OAAO,GAC1BC,OAAO,CACLL,OAAO,GACNM,CAAC,GACAD,OAAO,CAACE,IAAI,CAACC,OAAO,CAAC,IAAMJ,OAAO,CAACH,GAAG,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,IAAI,IAA4C;gBAC7F,OAAOC,OAAO,CAAC,IAAMN,OAAO,CAACI,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAChF,IAAI,CACzCkF,gBAAgB,CAAC;oBACfC,SAAS,EAAGnB,KAAK,IAAI;wBACnB,OAAQgB,IAAI,CAACtB,IAAI;4BACf,4KAAKhC,OAAO,CAAC0D,KAAU;gCACrB,OAAOC,SAAS,gKAACnE,aAAcoE,AAAU,AAAX,CAACA,CAAWN,IAAI,CAAC9C,qBAAqB,EAAE8B,KAAK,CAAC,CAAC;4BAC/E,4KAAKtC,OAAO,CAAC6D,KAAU;gCACrB,OAAOF,SAAS,CAACrB,KAAK,CAAC;wBAC3B;oBACF,CAAC;oBACDwB,SAAS,EAAEA,CAAA,GAAMR;iBAClB,CAAC,CACH;YACH,CAAC,CAAC,CACL,CACF,CAAC;AAGG,MAAMS,EAAE,GAAA,WAAA,GAGX3F,6JAAAA,AAAI,EACN,CAAC,EACD,CAAa4F,IAA4B,EAAExB,KAAQ,GAA6BY,OAAO,CAACY,IAAI,EAAE,IAAMC,OAAO,CAACzB,KAAK,CAAC,CAAC,CACpH;AAGM,MAAM0B,MAAM,IAAaF,IAA4B,GAAgCD,EAAE,CAACC,IAAI,EAAE,KAAK,CAAC,CAAC;AAGrG,MAAMG,MAAM,GAef,SAAAA,CAAA;IACF,MAAMC,OAAO,GAAG,IAAI7D,eAAe,uKAACP,OAAO,CAACqE,KAAS,CAAQ;IAC7D,OAAQ1C,SAAS,CAAC2C,MAAM;QACtB,KAAK,CAAC;YAAE;gBACNF,OAAO,CAAC5D,qBAAqB,GAAGmB,SAAS,CAAC,CAAC,CAAC;gBAC5CyC,OAAO,CAACG,MAAM,GAAG5C,SAAS,CAAC,CAAC,CAAC;gBAC7B;YACF;QACA,KAAK,CAAC;YAAE;gBACNyC,OAAO,CAAC5D,qBAAqB,GAAGmB,SAAS,CAAC,CAAC,CAAC;gBAC5CyC,OAAO,CAAC3D,qBAAqB,GAAGkB,SAAS,CAAC,CAAC,CAAC;gBAC5CyC,OAAO,CAACG,MAAM,GAAG5C,SAAS,CAAC,CAAC,CAAC;gBAC7B;YACF;QACA,KAAK,CAAC;YAAE;gBACNyC,OAAO,CAAC5D,qBAAqB,GAAGmB,SAAS,CAAC,CAAC,CAAC;gBAC5CyC,OAAO,CAAC3D,qBAAqB,GAAGkB,SAAS,CAAC,CAAC,CAAC;gBAC5CyC,OAAO,CAAC/C,qBAAqB,GAAGM,SAAS,CAAC,CAAC,CAAC;gBAC5CyC,OAAO,CAACG,MAAM,GAAG5C,SAAS,CAAC,CAAC,CAAC;gBAC7B;YACF;QACA;YAAS;gBACP,MAAM,IAAI6C,KAAK,iKAAC1E,qBAAkB,AAAlBA,EAAmB,oCAAoC,CAAC,CAAC;YAC3E;IACF;IACA,OAAOsE,OAAO;AAChB,CAAC;AAGM,MAAMK,WAAW,GAAGA,CACzBC,QAEyC,EACzCC,UAAA,oJAA8BxG,OAAY,AAAL,CAACyG,IACZ;IAC1B,MAAMtE,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAAC6E,GAAQ,CAAQ;IAC3D,IAAIC,WAAW,GAAyC1D,SAAS;IACjEd,MAAM,CAACE,qBAAqB,IAAIuE,MAA2C,IAAI;QAC7ED,WAAW,GAAGJ,QAAQ,CAACK,MAAM,CAAC;IAChC,CAAC;IACDzE,MAAM,CAACG,qBAAqB,GAAGkE,UAAU;IACzC,OAAOK,WAAW,CAAC1E,MAAM,GAAG2E,CAAC,GAAKxC,QAAQ,CAACqC,WAAW,CAAC,GAAGA,WAAW,GAAGI,KAAK,CAAC;AAChF,CAAC;AAGM,MAAMC,cAAc,GAAGA,CAC5BT,QAEyC,EACzCC,UAAA,oJAA8BxG,OAAO,AAAK,CAAJyG,EACXrB,OAAO,CAAC,IAAMkB,WAAW,CAACC,QAAQ,EAAEC,UAAU,CAAC,CAAC;AAE7E,MAAMS,MAAM,GAAGA,CACbL,MAGyC,EACzCJ,UAAA,oJAA8BxG,OAAY,AAAL,CAACyG,IACZ;IAC1B,OAAOT,MAAM,CAACY,MAAM,EAAE;QACpB,IAAIM,aAAa,GAAsDjE,SAAS;QAChF,IAAIkE,aAAa,GAAuClE,SAAS;QACjE,SAASmE,WAAWA,CAACjF,MAA8B;YACjD,IAAI+E,aAAa,EAAE;gBACjBA,aAAa,CAAC/E,MAAM,CAAC;YACvB,CAAC,MAAM,IAAIgF,aAAa,KAAKlE,SAAS,EAAE;gBACtCkE,aAAa,GAAGhF,MAAM;YACxB;QACF;QACA,MAAMA,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAAC6E,GAAQ,CAAQ;QAC3DvE,MAAM,CAACE,qBAAqB,IAAIuE,MAA2C,IAAI;YAC7EM,aAAa,GAAGN,MAAM;YACtB,IAAIO,aAAa,EAAE;gBACjBP,MAAM,CAACO,aAAa,CAAC;YACvB;QACF,CAAC;QACDhF,MAAM,CAACG,qBAAqB,GAAGkE,UAAU;QACzC,IAAIG,WAAW,GAAyC1D,SAAS;QACjE,IAAIoE,aAAa,GAA2BpE,SAAS;QACrD,IAAI,IAAI,CAACZ,qBAAqB,CAAC8D,MAAM,KAAK,CAAC,EAAE;YAC3CkB,aAAa,GAAG,IAAIC,eAAe,EAAE;YACrCX,WAAW,sJAAGzF,eAAAA,AAAY,EAAC,IAAM,IAAI,CAACmB,qBAAqB,CAAC+E,WAAW,EAAEC,aAAc,CAACE,MAAM,CAAC,CAAC;QAClG,CAAC,MAAM;YACLZ,WAAW,sJAAGzF,eAAY,AAAZA,EAAa,IAAO,IAAI,CAACmB,qBAA6B,CAAC+E,WAAW,CAAC,CAAC;QACpF;QACA,OAAQT,WAAW,IAAIU,aAAa,GAClCR,WAAW,CAAC1E,MAAM,GAAG2E,CAAC,IAAI;YACxB,IAAIO,aAAa,EAAE;gBACjBA,aAAa,CAACG,KAAK,EAAE;YACvB;YACA,OAAOb,WAAW,IAAII,KAAK;QAC7B,CAAC,CAAC,GACF5E,MAAM;IACV,CAAC,CAAC;AACJ,CAAC;;AAOM,MAAMuF,aAAa,GAAA,WAAA,yJAAGzH,OAAAA,AAAI,EAQ/B,CAAC,EAAE,CAAC4F,IAAI,EAAE8B,CAAC,KAAI;IACf,MAAMxF,MAAM,GAAG,IAAIC,eAAe,CAACP,OAAO,CAAC+F,+KAAa,CAAQ;IAChEzF,MAAM,CAACE,qBAAqB,GAAGwD,IAAI;IACnC1D,MAAM,CAACG,qBAAqB,GAAGqF,CAAC;IAChC,OAAOxF,MAAM;AACf,CAAC,CAAC;AAGK,MAAM0F,QAAQ,GAAA,WAAA,yJAQjB5H,OAAAA,AAAI,EACN,CAAC,EACD,CACE4F,IAA4B,EAC5B8B,CAAsC,GACAG,WAAW,CAACjC,IAAI,EAAE;QAAEP,SAAS,EAAEqC,CAAC;QAAEhC,SAAS,EAAEG;IAAO,CAAE,CAAC,CAChG;AAGM,MAAMiC,OAAO,GAAA,WAAA,yJAmBhB9H,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5BmC,SAAuB,EACvBL,CAAsC,GAEtCD,aAAa,CAAC7B,IAAI,GAAG1B,KAAK,IAA2C;QACnE,MAAM8D,MAAM,GAAG5G,aAAa,CAAC6G,kKAAAA,AAAc,EAAC/D,KAAK,CAAC;QAClD,OAAQ8D,MAAM,CAACpE,IAAI;YACjB,KAAK,MAAM;gBACT,OAAOmE,SAAS,CAACC,MAAM,CAACE,IAAI,CAAC,GAAGR,CAAC,CAACM,MAAM,CAACE,IAAI,CAAC,GAAG3C,SAAS,CAACrB,KAAK,CAAC;YACnE,KAAK,OAAO;gBACV,OAAOqB,SAAS,CAACyC,MAAM,CAACG,KAAK,CAAC;QAClC;IACF,CAAC,CAAC,CAAC;AAGE,MAAMC,SAAS,GAAA,WAAA,wJAAGpI,QAAAA,AAAI,EAQ3B,CAAC,EAAE,CACH4F,IAA4B,EAC5ByC,EAA+D,GAE/DZ,aAAa,CAAC7B,IAAI,GAAG1B,KAAK,IAA2C;QACnE,MAAM8D,MAAM,kKAAG5G,aAAa,CAAC6G,GAAAA,AAAc,EAAC/D,KAAK,CAAC;QAClD,OAAQ8D,MAAM,CAACpE,IAAI;YACjB,KAAK,MAAM;gBACT,6JAAO1D,OAAAA,AAAI,EAACmI,EAAE,CAACL,MAAM,CAACE,IAAI,CAAC,sJAAEvH,MAAM,CAAC2H,KAAAA,AAAS,EAAC,IAAM/C,SAAS,CAACrB,KAAK,CAAC,CAAC,CAAC;YACxE,KAAK,OAAO;gBACV,OAAOqB,SAAS,CAACyC,MAAM,CAACG,KAAK,CAAC;QAClC;IACF,CAAC,CAAC,CAAC;AAGE,MAAMI,kBAAkB,IAC7Bb,CAAuD,GAC5BnD,gBAAgB,CAAC,CAACsC,CAAC,EAAE2B,MAAM,GAAKd,CAAC,uKAAC7F,aAAa,CAAC4G,CAAAA,AAAY,EAACD,MAAM,CAACE,YAAY,CAAC,CAAC,CAAC;AAEhH,MAAMC,cAAc,GAAA,WAAA,GAAGnG,MAAM,CAACC,GAAG,CAAC,2BAA2B,CAAC;AAGvD,MAAMmG,gBAAgB,GAAOC,GAAM,IAAO;IAC/C,2JAAIhI,cAAAA,AAAW,EAACgI,GAAG,EAAEF,cAAc,CAAC,EAAE;QACpC,mBAAA;QACA,OAAOE,GAAG,CAACF,cAAc,CAAC;IAC5B;IACA,OAAOE,GAAG;AACZ,CAAC;AAGM,MAAMC,OAAO,GAAGA,CAAID,GAAe,EAAEE,IAAgC,KAAO;IACjF,wJAAIpI,MAAM,CAACqI,EAAAA,AAAM,EAACD,IAAI,CAAC,EAAE;QACvB,OAAO,IAAIE,KAAK,CAACJ,GAAG,EAAE;YACpBK,GAAGA,EAACC,MAAM,EAAEC,CAAC;gBACX,OAAOA,CAAC,gKAAKhI,aAAwB,AAAX,CAACiI,GAAcD,CAAC,KAAKT,cAAc,IAAIS,CAAC,IAAID,MAAM;YAC9E,CAAC;YACDG,GAAGA,EAACH,MAAM,EAAEC,CAAC;gBACX,IAAIA,CAAC,KAAKhI,aAAa,CAACiI,0JAAU,EAAE;oBAClC,OAAON,IAAI,CAAC3E,KAAK;gBACnB;gBACA,IAAIgF,CAAC,KAAKT,cAAc,EAAE;oBACxB,OAAOE,GAAG;gBACZ;gBACA,mBAAA;gBACA,OAAOM,MAAM,CAACC,CAAC,CAAC;YAClB;SACD,CAAC;IACJ;IACA,OAAOP,GAAG;AACZ,CAAC;AAGM,MAAMU,GAAG,GAAIC,MAAe,2JACjC1I,WAAAA,AAAQ,EAAC0I,MAAM,CAAC,IAAI,CAAA,4JAAEpI,aAAwB,AAAX,CAACiI,GAAcG,MAAM,CAAC,GACvDjF,gBAAgB,EAAEkF,KAAK,GAAKlE,SAAS,gKAACnE,MAAcmI,AAAG,EAACT,KAAL,CAACS,CAAW,CAACC,MAAM,EAAEE,oBAAoB,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GACrGlE,SAAS,gKAACnE,MAAcmI,AAAG,EAACC,KAAL,CAACD,AAAU,CAAC,CAAC;AAGnC,MAAMI,UAAU,IAAIC,OAAe,GACxCC,aAAa,CAAC,mKAAMzI,MAAcmI,AAAG,EAAC,IAAIO,CAAT,CAACP,cAAwB,CAACK,OAAO,CAAC,CAAC,CAAC;AAGhE,MAAMG,OAAO,IAAIC,QAA0B,GAA2BhF,OAAO,CAACiF,IAAI,CAACD,QAAQ,CAAC,EAAET,GAAG,CAAC;AAGlG,MAAMvB,MAAM,IAAapC,IAA4B,GAC1DiC,WAAW,CAACjC,IAAI,EAAE;QAChBP,SAAS,GAAG6E,CAAC,GAAKrE,OAAO,qJAAChG,MAAM,CAACqI,AAAI,AAAJA,EAAKgC,CAAC,CAAC,CAAC;QACzCxE,SAAS,EAAGT,CAAC,IAAKY,OAAO,oJAAChG,MAAM,CAACsI,EAAAA,AAAK,EAAClD,CAAC,CAAC;KAC1C,CAAC;AAGG,MAAMC,IAAI,IAAaU,IAA4B,GACxDuE,UAAU,CAACvE,IAAI,EAAE;QACfP,SAAS,EAAE+E,aAAa;QACxB1E,SAAS,EAAE2E;KACZ,CAAC;AAGG,MAAMC,IAAI,IAAOC,KAAQ,IAC9BzJ,iKAAAA,AAAQ,EAACyJ,KAAK,CAAC,IAAI,CAAA,2JAAEnJ,aAAa,CAACiI,AAAU,IAAIkB,KAAK,CAAC,GACrDhG,gBAAgB,EAAEkF,KAAK,GAAKlE,SAAS,gKAACnE,OAAckJ,AAAI,EAACxB,IAAN,CAACwB,EAAY,CAACC,KAAK,EAAEb,oBAAoB,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GACrGlE,SAAS,EAACnE,aAAa,CAACkJ,uJAAAA,AAAI,EAACC,KAAK,CAAC,CAAC;AAGnC,MAAMC,QAAQ,GAAOR,QAAoB,IAA8BhF,OAAO,CAACiF,IAAI,CAACD,QAAQ,CAAC,EAAEM,IAAI,CAAC;AAGpG,MAAM/E,SAAS,IAAOrB,KAAqB,IAA6B;IAC7E,MAAMhC,MAAM,GAAG,IAAIyB,sBAAsB,wKAAC/B,OAAO,CAAC0D,KAAU,CAAQ;IACpEpD,MAAM,CAACE,qBAAqB,GAAG8B,KAAK;IACpC,OAAOhC,MAAM;AACf,CAAC;AAGM,MAAM2H,aAAa,IACxBG,QAAiC,GACLhF,OAAO,CAACiF,IAAI,CAACD,QAAQ,CAAC,EAAEzE,SAAS,CAAC;AAGzD,MAAMkF,OAAO,GAAA,WAAA,GAAmClG,gBAAgB,CAAEmG,KAAK,IAAK7E,OAAO,CAAC6E,KAAK,CAACC,EAAE,EAAE,CAAC,CAAC;AAGhG,MAAMC,WAAW,IACtBlD,CAA0D,GAC/BnD,gBAAgB,EAAEmG,KAAK,GAAKhD,CAAC,CAACgD,KAAK,CAACC,EAAE,EAAE,CAAC,CAAC;AAGhE,MAAM3F,OAAO,GAAA,WAAA,yJAAGhF,OAAAA,AAAI,EASzB,CAAC,EACD,CAAC4F,IAAI,EAAE8B,CAAC,KAAI;IACV,MAAMxF,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAACiJ,QAAa,CAAQ;IAChE3I,MAAM,CAACE,qBAAqB,GAAGwD,IAAI;IACnC1D,MAAM,CAACG,qBAAqB,GAAGqF,CAAC;IAChC,OAAOxF,MAAM;AACf,CAAC,CACF;AAGM,MAAM4I,OAAO,GAAA,WAAA,GA2BhB9K,6JAAAA,AAAI,EAAC,CAAC,EAAE,CAAC4F,IAAI,EAAE8B,CAAC,GAClB1C,OAAO,CAACY,IAAI,GAAGX,CAAC,IAAI;QAClB,MAAM8F,CAAC,GAAG,OAAOrD,CAAC,KAAK,UAAU,GAAIA,CAAS,CAACzC,CAAC,CAAC,GAAGyC,CAAC;QACrD,IAAIrD,QAAQ,CAAC0G,CAAC,CAAC,EAAE;YACf,OAAOA,CAAC;QACV,CAAC,MAAM,0JAAIhK,iBAAAA,AAAa,EAACgK,CAAC,CAAC,EAAE;YAC3B,OAAO1E,WAAW,CAA+BM,MAAM,IAAI;gBACzDoE,CAAC,CAACC,IAAI,EAAE/F,CAAC,GAAK0B,MAAM,CAACd,OAAO,CAACZ,CAAC,CAAC,CAAC,GAAGiF,CAAC,GAClCvD,MAAM,CAAC2D,IAAI,CAAC,IAAIW,gBAAgB,CAACf,CAAC,EAAE,6CAA6C,CAAC,CAAC,CAAC,CAAC;YACzF,CAAC,CAAC;QACJ;QACA,OAAOrE,OAAO,CAACkF,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC;AAGE,MAAMG,IAAI,IACftF,IAA4B,IACuC;IACnE,MAAM1D,MAAM,GAAG,IAAIC,eAAe,CAAC,QAAQ,CAAQ;IACnDD,MAAM,CAACE,qBAAqB,GAAGwD,IAAI;IACnC,OAAO1D,MAAM;AACf,CAAC;AAGM,MAAMiJ,OAAO,IAClBvF,IAAmD,GACdZ,OAAO,CAACY,IAAI,oJAAE3F,WAAQ,CAAC;AAGvD,MAAMmL,IAAI,IAAaxF,IAA4B,GACxDiC,WAAW,CAACjC,IAAI,EAAE;QAAEP,SAAS,EAAEQ,OAAO;QAAEH,SAAS,EAAE4E;IAAI,CAAE,CAAC;AAGrD,MAAMH,UAAU,GAAA,WAAA,yJAcnBnK,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5ByF,OAGC,GAEDjG,gBAAgB,CAACQ,IAAI,EAAE;QACrBP,SAAS,GAAGnB,KAAK,GAAK2B,OAAO,CAACwF,OAAO,CAAChG,SAAS,CAACnB,KAAK,CAAC,CAAC;QACvDwB,SAAS,GAAGT,CAAC,GAAKY,OAAO,CAACwF,OAAO,CAAC3F,SAAS,CAACT,CAAC,CAAC;KAC/C,CAAC,CAAC;AAGE,MAAMG,gBAAgB,GAAA,WAAA,yJAczBpF,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5ByF,OAGC,KAC+C;IAChD,MAAMnJ,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAAC0J,oBAAyB,CAAQ;IAC5EpJ,MAAM,CAACE,qBAAqB,GAAGwD,IAAI;IACnC1D,MAAM,CAACG,qBAAqB,GAAGgJ,OAAO,CAAChG,SAAS;IAChDnD,MAAM,CAACe,qBAAqB,GAAGoI,OAAO,CAAC3F,SAAS;IAChD,OAAOxD,MAAM;AACf,CAAC,CAAC;AAGK,MAAM2F,WAAW,GAAA,WAAA,GAcpB7H,6JAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5ByF,OAGC,GAEDjG,gBAAgB,CAACQ,IAAI,EAAE;QACrBP,SAAS,GAAGnB,KAAK,IAAI;YACnB,MAAMqH,OAAO,kKAAGnK,UAAcmK,AAAO,EAACrH,CAAT,CAACqH,GAAa,CAAC;YAC5C,IAAIA,OAAO,CAACrF,MAAM,GAAG,CAAC,EAAE;gBACtB,OAAOX,SAAS,gKAACnE,aAAa,CAACoK,EAAAA,AAAa,EAACtH,KAAK,CAAC,CAAC;YACtD;YACA,MAAMuH,QAAQ,kKAAGrK,WAAcqK,AAAQ,EAAT,AAAUvH,CAATuH,IAAc,CAAC;YAC9C,IAAIA,QAAQ,CAACvF,MAAM,GAAG,CAAC,EAAE;gBACvB,OAAOmF,OAAO,CAAChG,SAAS,oJAAC3F,KAAK,CAACgM,OAAAA,AAAU,EAACD,QAAQ,CAAC,CAAC;YACtD;YACA,OAAOlG,SAAS,CAACrB,KAA2B,CAAC;QAC/C,CAAC;QACDwB,SAAS,EAAE2F,OAAO,CAAC3F,SAAAA;KACpB,CAAC,CAAC;AAGE,MAAMiG,iBAAiB,GAAA,WAAA,yJAG1B3L,OAAAA,AAAI,EACN,CAAC,EACD,CAAa4F,IAAiB,EAAE8B,CAA8C,GAC5EvC,OAAO,CAAC,MAAK;QACX,MAAMyG,GAAG,sJAAGnM,GAAG,CAACoM,WAAY,AAAZA,EAAajG,IAAI,CAAC;QAClC,MAAMkG,GAAG,GAAGrM,GAAG,CAACsM,0JAAAA,AAAQ,EAAIH,GAAG,CAAC1F,MAAM,CAAC;QACvC,IAAI8F,CAAC,GAAG,CAAC;QACT,OAAOrG,EAAE,CACPsG,SAAS,CAAC;YACRC,KAAK,EAAEA,CAAA,GAAMF,CAAC,GAAGJ,GAAG,CAAC1F,MAAM;YAC3BiG,IAAI,EAAEA,CAAA,GAAMzE,CAAC,CAACkE,GAAG,CAACI,CAAC,CAAC,EAAEA,CAAC,CAAC;YACxBd,IAAI,EAAGH,CAAC,IAAI;gBACVe,GAAG,CAACE,CAAC,EAAE,CAAC,GAAGjB,CAAC;YACd;SACD,CAAC,EACFe,GAAe,CAChB;IACH,CAAC,CAAC,CACL;AAGM,MAAMM,wBAAwB,GAAA,WAAA,yJAGjCpM,OAAAA,AAAI,EACN,CAAC,EACD,CAAa4F,IAAiB,EAAE8B,CAA8C,GAC5EvC,OAAO,CAAC,MAAK;QACX,MAAMyG,GAAG,sJAAGnM,GAAG,CAACoM,WAAAA,AAAY,EAACjG,IAAI,CAAC;QAClC,IAAIoG,CAAC,GAAG,CAAC;QACT,OAAOC,SAAS,CAAC;YACfC,KAAK,EAAEA,CAAA,GAAMF,CAAC,GAAGJ,GAAG,CAAC1F,MAAM;YAC3BiG,IAAI,EAAEA,CAAA,GAAMzE,CAAC,CAACkE,GAAG,CAACI,CAAC,CAAC,EAAEA,CAAC,CAAC;YACxBd,IAAI,EAAEA,CAAA,KAAK;gBACTc,CAAC,EAAE;YACL;SACD,CAAC;IACJ,CAAC,CAAC,CACL;AAGM,MAAMK,GAAG,GAAA,WAAA,yJAAGrM,OAAAA,AAAI,GAiBpBsM,IAAI,GAAK,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,IAAIjI,QAAQ,CAACiI,IAAI,CAAC,CAAC,CAAC,CAAC,EAC3D,CACE1G,IAA4C,EAC5CyF,OAGC,GAEDhH,QAAQ,CAACuB,IAAI,CAAC,GACVZ,OAAO,CAACY,IAAI,GAAGmF,CAAC,GAAgDA,CAAC,GAAGM,OAAO,CAACkB,MAAM,EAAE,GAAGlB,OAAO,CAACmB,OAAO,EAAG,CAAC,GAC1G5G,IAAI,GACJyF,OAAO,CAACkB,MAAM,EAAE,GAChBlB,OAAO,CAACmB,OAAO,EAAE,CACxB;AAGM,MAAMC,SAAS,GAAA,WAAA,GAAyBzH,OAAO,CAACyF,OAAO,GAAGA,OAAO,GAAKiC,aAAa,CAACjC,OAAO,CAAC,CAAC;AAG7F,MAAMiC,aAAa,IAAIjC,OAAwB,GACpDlF,SAAS,KAACnE,aAAa,CAACqL,yJAAAA,AAAS,EAAChC,OAAO,CAAC,CAAC;AAGtC,MAAMkC,aAAa,IAAa/G,IAA4B,IAA4B;IAC7F,MAAM1D,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAACgL,kBAAuB,CAAQ;IAC1E1K,MAAM,CAACE,qBAAqB,kKAAGpB,SAAkB6L,AAAM,QAAP,CAACA,2JAAOhL,aAAa,CAACiL,CAAY,CAAC;IACnF5K,MAAM,CAACG,qBAAqB,GAAG,IAAMuD,IAAI;IACzC,OAAO1D,MAAM;AACf,CAAC;AAGM,MAAM6K,iBAAiB,IAC5BrF,CAAoH,GAEpH3B,MAAM,CAAC2B,CAAC,EAAE;QACR,MAAMxF,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAACgL,kBAAuB,CAAQ;QAC1E1K,MAAM,CAACE,qBAAqB,kKAAGpB,SAAwB,AAAN6L,QAAD,CAACA,2JAAOhL,aAAa,CAACiL,CAAY,CAAC;QACnF5K,MAAM,CAACG,qBAAqB,IAAI2K,QAAmC,wKACjEnL,aAAa,CAAC4G,EAAAA,AAAY,EAACuE,QAAQ,CAAC,sJAChC/L,eAAAA,AAAY,EAAC,IAAM,IAAI,CAACmB,qBAAqB,CAACuK,aAAa,CAAC,CAAC,sJAC7D1L,eAAAA,AAAY,EAAC,IAAM,IAAI,CAACmB,qBAAqB,CAAC6K,eAAe,CAAC,CAAC;QACrE,OAAO/K,MAAM;IACf,CAAC,CAAC;AAGG,MAAMgL,YAAY,GAAA,WAAA,yJAGrBlN,OAAI,AAAJA,EACF,CAAC,EACD,CAAU4F,IAA4B,EAAEvE,QAAiC,GACvEyD,mBAAmB,EAAEC,OAAO,GAC1BC,OAAO,CACLE,IAAI,CAACH,OAAO,CAACa,IAAI,CAAC,CAAC,GAClBV,IAAI,GAAKiI,YAAY,CAAC9L,QAAQ,EAAE6D,IAAI,CAAC,CACvC,CACF,CACJ;AAGM,MAAMkI,GAAG,GAAA,WAAA,GAGZpN,6JAAAA,AAAI,EACN,CAAC,EACD,CAAa4F,IAA4B,EAAE8B,CAAc,GACvD1C,OAAO,CAACY,IAAI,GAAGX,CAAC,GAAKgF,IAAI,CAAC,IAAMvC,CAAC,CAACzC,CAAC,CAAC,CAAC,CAAC,CACzC;AAGM,MAAMoI,OAAO,GAAA,WAAA,wJAQhBrN,QAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5ByF,OAA+E,GAE/ExD,WAAW,CAACjC,IAAI,EAAE;QAChBP,SAAS,GAAG6E,CAAC,GAAKM,QAAQ,CAAC,IAAMa,OAAO,CAAChG,SAAS,CAAC6E,CAAC,CAAC,CAAC;QACtDxE,SAAS,GAAGT,CAAC,GAAKgF,IAAI,CAAC,IAAMoB,OAAO,CAAC3F,SAAS,CAACT,CAAC,CAAC;KAClD,CAAC,CAAC;AAGE,MAAMqI,QAAQ,GAAA,WAAA,yJAGjBtN,OAAI,AAAJA,EACF,CAAC,EACD,CAAc4F,IAA4B,EAAE8B,CAAe,GACzDtC,gBAAgB,CAACQ,IAAI,EAAE;QACrBP,SAAS,GAAGnB,KAAK,IAAI;YACnB,MAAM8D,MAAM,kKAAG5G,aAAa,CAAC6G,GAAAA,AAAc,EAAC/D,KAAK,CAAC;YAClD,OAAQ8D,MAAM,CAACpE,IAAI;gBACjB,KAAK,MAAM;oBAAE;wBACX,OAAO4G,QAAQ,CAAC,IAAM9C,CAAC,CAACM,MAAM,CAACE,IAAI,CAAC,CAAC;oBACvC;gBACA,KAAK,OAAO;oBAAE;wBACZ,OAAO3C,SAAS,CAACyC,MAAM,CAACG,KAAK,CAAC;oBAChC;YACF;QACF,CAAC;QACDzC,SAAS,EAAEG;KACZ,CAAC,CACL;AAGM,MAAM0H,OAAO,GAAA,WAAA,GAQhBvN,6JAAI,AAAJA,EAAK,CAAC,EAAE,CACV4F,IAA4B,EAC5B4H,OAA+D,GAE/DC,MAAM,CAAC7H,IAAI,GAAGV,IAAI,GAAKwI,aAAa,CAACxI,IAAI,CAAC,GAAG4B,KAAK,GAAG0G,OAAO,CAACtI,IAAI,CAAC9C,qBAAqB,CAAC,CAAC,CAAC;AAGrF,MAAMqL,MAAM,GAAA,WAAA,wJAQfzN,QAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5B4H,OAA+D,GAE/D1I,mBAAmB,EAAEC,OAAO,GAC1BK,gBAAgB,CAACL,OAAO,CAACa,IAAI,CAAC,EAAE;YAC9BP,SAAS,GAAGsI,MAAM,IAAI;gBACpB,MAAMC,MAAM,GAAGxD,aAAa,CAACuD,MAAM,CAAC;gBACpC,OAAOvI,gBAAgB,CAACoI,OAAO,CAACI,MAAM,CAAC,EAAE;oBACvCvI,SAAS,EAAGwI,MAAM,IAAKzD,aAAa,gKAAChJ,aAAa,AAACoE,AAAU,CAAVA,CAAWmI,MAAM,EAAEE,MAAM,CAAC,CAAC;oBAC9EnI,SAAS,EAAEA,CAAA,GAAMkI;iBAClB,CAAC;YACJ,CAAC;YACDlI,SAAS,GAAGoI,OAAO,IAAI;gBACrB,MAAMF,MAAM,GAAGvD,WAAW,CAACyD,OAAO,CAAC;gBACnC,OAAOC,QAAQ,CAACP,OAAO,CAACI,MAAM,CAAC,EAAEA,MAAM,CAAC;YAC1C;SACD,CAAC,CACH,CAAC;AAGG,MAAMhH,WAAW,GAAA,WAAA,yJAQpB5G,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5B4H,OAAwF,GAExFC,MAAM,CACJ7H,IAAI,EACJoI,SAAS,CAAC;QACR3I,SAAS,EAAGnB,KAAK,mKACf9C,aAAa,CAAC6M,MAAiB,AAAjBA,EAAkB/J,KAAK,CAAC,GAClC4B,MAAM,CAAC0H,OAAO,gKAACpM,aAAa,CAAC8M,CAAAA,AAAY,EAAChK,KAAK,CAAC,CAAC,CAAC,GAClD4C,KAAK;QACXpB,SAAS,EAAEA,CAAA,GAAMoB;KAClB,CAAC,CACH,CAAC;AAGG,MAAMqH,MAAM,GAAA,WAAA,yJAQfnO,OAAAA,AAAI,EACN,CAAC,EACD,CACE4F,IAA4B,EAC5BxC,IAAwC,GACFgL,aAAa,CAACxI,IAAI,EAAExC,IAAI,EAAEyC,OAAO,CAAC,CAC3E;AAGM,MAAMwI,KAAK,GAAazI,IAA4B,IAAiC0I,SAAS,CAAC1I,IAAI,oJAAE3F,WAAQ,CAAC;AAG9G,MAAMqO,SAAS,GAAA,WAAA,OAGlBtO,yJAAAA,AAAI,EACN,CAAC,EACD,CAAU4F,IAA4B,EAAE8B,CAAwB,GAC9DG,WAAW,CAACjC,IAAI,EAAE;QAChBP,SAAS,GAAG6E,CAAC,GAAKX,GAAG,CAAC7B,CAAC,CAACwC,CAAC,CAAC,CAAC;QAC3BxE,SAAS,EAAEG;KACZ,CAAC,CACL;AAGM,MAAM0I,YAAY,GAGkB9O,GAAG,CAAC8O,0JAAY;AAEpD,MAAM7F,YAAY,GAAA,WAAA,GAA6CnE,gBAAgB,CAAC,CAACsC,CAAC,EAAE2B,MAAM,GAC/F3C,OAAO,CAAC2C,MAAM,CAACE,YAAY,CAAC,CAC7B;AAGM,MAAM7C,OAAO,IAAOzB,KAAQ,IAAsB;IACvD,MAAMlC,MAAM,GAAG,IAAIiC,sBAAsB,wKAACvC,OAAO,CAAC6D,KAAU,CAAQ;IACpEvD,MAAM,CAACE,qBAAqB,GAAGgC,KAAK;IACpC,OAAOlC,MAAM;AACf,CAAC;AAGM,MAAMiD,OAAO,IAAa6E,QAAyC,IAA4B;IACpG,MAAM9H,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAACqE,IAAS,CAAQ;IAC5D/D,MAAM,CAACiE,MAAM,GAAG6D,QAAQ;IACxB,OAAO9H,MAAM;AACf,CAAC;AAGM,MAAM+H,IAAI,IAAOuE,KAAiB,IAAsB;IAC7D,MAAMtM,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAAC6M,EAAO,CAAQ;IAC1DvM,MAAM,CAACE,qBAAqB,GAAGoM,KAAK;IACpC,OAAOtM,MAAM;AACf,CAAC;AAGM,MAAMwM,GAAG,GAAA,WAAA,GAAG1O,6JAAAA,AAAI,GAsDpBsM,IAAI,GAAKA,IAAI,CAACpG,MAAM,KAAK,CAAC,IAAIoG,IAAI,CAACpG,MAAM,KAAK,CAAC,IAAI,CAAA,wJAAEpF,WAAAA,AAAQ,EAACwL,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,EACnG,CAAa1G,IAA4B,EAAE8B,CAAI,GAC7C1C,OAAO,CAACY,IAAI,GAAGX,CAAC,IAAI;QAClB,MAAM8F,CAAC,GAAG,OAAOrD,CAAC,KAAK,UAAU,GAAIA,CAAS,CAACzC,CAAC,CAAC,GAAGyC,CAAC;QACrD,IAAIrD,QAAQ,CAAC0G,CAAC,CAAC,EAAE;YACf,OAAOpF,EAAE,CAACoF,CAAC,EAAE9F,CAAC,CAAC;QACjB,CAAC,MAAM,2JAAIlE,gBAAa,AAAbA,EAAcgK,CAAC,CAAC,EAAE;YAC3B,OAAO1E,WAAW,EAA+BM,MAAM,IAAI;gBACzDoE,CAAC,CAACC,IAAI,EAAEnE,CAAC,GAAKF,MAAM,CAACd,OAAO,CAACZ,CAAC,CAAC,CAAC,EAAGiF,CAAC,IAClCvD,MAAM,CAAC2D,IAAI,CAAC,IAAIW,gBAAgB,CAACf,CAAC,EAAE,yCAAyC,CAAC,CAAC,CAAC,CAAC;YACrF,CAAC,CAAC;QACJ;QACA,OAAOrE,OAAO,CAACZ,CAAC,CAAC;IACnB,CAAC,CAAC,CACL;AAGM,MAAM0J,UAAU,IACrBjH,CAAoH,GAEpHnD,gBAAgB,EAAWmG,KAAK,IAAI;QAClC,MAAMkE,aAAa,GAAGlE,KAAK,CAACmE,WAAW,CAACC,wBAAwB,CAAC;QACjE,MAAMC,KAAK,yJAAG7O,OAAI,AAAJA,EAAK0O,aAAa,sJAAEjO,MAAM,CAAC2H,KAAAA,AAAS,EAAC,IAAMoC,KAAK,CAACqE,KAAK,EAAE,CAAC,CAAC;QACxE,OAAOrH,CAAC,CAACsH,eAAe,CAACF,wBAAwB,qJAAEnO,MAAM,CAACsO,CAAAA,AAAI,EAACF,KAAK,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC;AAGG,MAAMX,aAAa,GAAA,WAAA,yJAUtBpO,OAAI,AAAJA,EAAK,CAAC,EAAE,CACV4F,IAA4B,EAC5BxC,IAAwC,EACxCsC,SAA8C,GAE9CN,gBAAgB,CAACQ,IAAI,EAAE;QACrBP,SAAS,GAAGnB,KAAK,IAAI;YACnB,MAAMqH,OAAO,GAAGnK,aAAa,CAACmK,2JAAAA,AAAO,EAACrH,KAAK,CAAC;YAC5C,IAAIqH,OAAO,CAACrF,MAAM,GAAG,CAAC,EAAE;gBACtB,OAAOX,SAAS,qJAAC5E,MAAM,CAACuO,MAAAA,AAAU,iKAAC9N,aAAa,CAAC+N,gBAAAA,AAA2B,EAACjL,KAAK,CAAC,CAAC,CAAC;YACvF;YACA,OAAOd,IAAI,EAAE;QACf,CAAC;QACDsC;KACD,CAAC,CAAC;AAGE,MAAMuH,eAAe,IAC1BrH,IAA4B,IACF;IAC1B,MAAM1D,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAACgL,kBAAuB,CAAQ;IAC1E1K,MAAM,CAACE,qBAAqB,kKAAGpB,UAAkBoO,AAAO,OAAR,CAACA,4JAAQvN,aAAa,CAACiL,CAAY,CAAC;IACpF5K,MAAM,CAACG,qBAAqB,GAAG,IAAMuD,IAAI;IACzC,OAAO1D,MAAM;AACf,CAAC;AAGM,MAAM4C,mBAAmB,IAC9B4C,CAAoH,GAEpH3B,MAAM,CAAC2B,CAAC,EAAE;QACR,MAAMxF,MAAM,GAAG,IAAIC,eAAe,CAACP,OAAO,CAACgL,yLAAuB,CAAQ;QAC1E1K,MAAM,CAACE,qBAAqB,kKAAGpB,UAAkBoO,AAAO,OAAR,CAACA,4JAAQvN,aAAa,CAACiL,CAAY,CAAC;QACpF5K,MAAM,CAACG,qBAAqB,IAAI2K,QAAmC,yKACjEnL,aAAa,CAAC4G,CAAAA,AAAY,EAACuE,QAAQ,CAAC,sJAChC/L,eAAAA,AAAY,EAAC,IAAM,IAAI,CAACmB,qBAAqB,CAACuK,aAAa,CAAC,CAAC,sJAC7D1L,eAAAA,AAAY,EAAC,IAAM,IAAI,CAACmB,qBAAqB,CAAC6K,eAAe,CAAC,CAAC;QACrE,OAAO/K,MAAM;IACf,CAAC,CAAC;AAEJ,MAAM4E,KAAK,GAAA,WAAA,GAAwBjB,OAAO,CAAC,KAAK,CAAC,CAAC;;AAO3C,MAAMyJ,kBAAkB,IAAI3M,KAA0C,IAAyB;IACpG,MAAMT,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAACgL,kBAAuB,CAAQ;IAC1E1K,MAAM,CAACE,qBAAqB,GAAGO,KAAK;IACpCT,MAAM,CAACG,qBAAqB,GAAG,KAAK,CAAC;IACrC,OAAOH,MAAM;AACf,CAAC;AAGM,MAAMqN,UAAU,GAAA,WAAA,yJAUnBvP,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA8B,EAC9B4J,SAAuC,GAEvCxK,OAAO,CAACwK,SAAS,GAAGzE,CAAC,IAAI;QACvB,IAAIA,CAAC,EAAE;YACL,QAAO7K,4JAAAA,AAAI,EAAC0F,IAAI,EAAEwH,GAAG,iJAACzM,MAAM,CAACsO,AAAI,CAAC,CAAC;QACrC;QACA,OAAOpJ,OAAO,qJAAClF,MAAM,CAAC6F,AAAI,EAAE,CAAC;IAC/B,CAAC,CAAC,CAAC;AAGE,MAAMyF,SAAS,IACpBZ,OAIC,IAC4B;IAC7B,MAAMnJ,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAAC6N,GAAQ,CAAQ;IAC3DvN,MAAM,CAACE,qBAAqB,GAAGiJ,OAAO,CAACa,KAAK;IAC5ChK,MAAM,CAACG,qBAAqB,GAAGgJ,OAAO,CAACc,IAAI;IAC3CjK,MAAM,CAACe,qBAAqB,GAAGoI,OAAO,CAACH,IAAI;IAC3C,OAAOhJ,MAAM;AACf,CAAC;AAGM,MAAMwN,YAAY,IACvBhM,QAA6C,GAM7CyB,OAAO,CAAC,MAAK;QACX,MAAMjD,MAAM,GAAG,IAAIC,eAAe,uKAACP,OAAO,CAAC+N,OAAW,CAAQ;QAC9DzN,MAAM,CAACE,qBAAqB,GAAGsB,QAAQ,EAAE;QACzC,OAAOxB,MAAM;IACf,CAAC,CAAC;AAGG,MAAM0N,GAAG,GAAsB,SAAAA,CAAA;IACpC,MAAMlI,CAAC,GAAGnE,SAAS,CAAC2C,MAAM,KAAK,CAAC,GAAG3C,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAACsM,IAAI,CAACtM,SAAS,CAAC,CAAC,CAAC,CAAC;IACjF,OAAOmM,YAAY,CAAC,IAAMhI,CAAC,kJAACxH,QAAI,CAAC,CAAC;AACpC,CAAC;AAGM,MAAM4P,UAAU,GAAuBA,CAAC3D,IAAc,EAAE,GAAG4D,SAAqB,GACrFC,MAAM,CAACC,cAAc,CACnBF,SAAS,CAAC7J,MAAM,KAAK,CAAC,GAClB,SAAoB,GAAGoG,IAAgB;QACvC,OAAOoD,YAAY,CAAC,IAAMvD,IAAI,CAAC+D,KAAK,CAAC,IAAI,EAAE5D,IAAI,CAAC,CAAC;IACnD,CAAC,GACC,SAAoB,GAAGA,IAAgB;QACvC,IAAIpK,MAAM,GAAGwN,YAAY,CAAC,IAAMvD,IAAI,CAAC+D,KAAK,CAAC,IAAI,EAAE5D,IAAI,CAAC,CAAC;QACvD,KAAK,MAAM6D,CAAC,IAAIJ,SAAS,CAAE;YACzB7N,MAAM,GAAGiO,CAAC,CAACjO,MAAM,EAAE,GAAGoK,IAAI,CAAC;QAC7B;QACA,OAAOpK,MAAM;IACf,CAAC,EACH,QAAQ,EACR;QAAEkC,KAAK,EAAE+H,IAAI,CAACjG,MAAM;QAAEkK,YAAY,EAAE;IAAI,CAAE,CAC3C;AAGI,MAAMC,eAAe,GAAA,WAAA,yJAAGrQ,OAAAA,AAAI,EAGjC,CAAC,EAAE,CAAC4F,IAAI,EAAE0K,WAAW,GAAKtB,eAAe,CAACpJ,IAAI,EAAE2K,kBAAkB,EAAED,WAAW,CAAC,CAAC;AAG5E,MAAME,mBAAmB,GAAA,WAAA,yJAAGxQ,OAAAA,AAAI,EAGrC,CAAC,EAAE,CAAC4F,IAAI,EAAE6K,eAAe,GAAKzB,eAAe,CAACpJ,IAAI,EAAE8K,sBAAsB,EAAED,eAAe,CAAC,CAAC;AAGxF,MAAME,gBAAgB,GAAA,WAAA,yJAAG3Q,OAAAA,AAAI,EAGlC,CAAC,EAAE,CAAC4F,IAAI,EAAEgL,MAAM,KAAI;IACpB,MAAM1O,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAACgL,kBAAuB,CAAQ;IAC1E1K,MAAM,CAACE,qBAAqB,GAAGwO,MAAM;IACrC1O,MAAM,CAACG,qBAAqB,GAAG,IAAMuD,IAAI;IACzC,OAAO1D,MAAM;AACf,CAAC,CAAC;AAGK,MAAM2O,iBAAiB,GAAA,WAAA,yJAAG7Q,OAAAA,AAAI,EAGnC,CAAC,EAAE,CAACkC,MAAM,EAAE4O,OAAO,GACnB9B,eAAe,CACb9M,MAAM,EACN6O,oBAAoB,EACpBD,OAAO,CACR,CAAC;AAGG,MAAME,gBAAgB,GAAA,WAAA,yJAAGhR,OAAAA,AAAI,EAGlC,CAAC,EAAE,CAACkC,MAAM,EAAE4O,OAAO,GACnB9B,eAAe,CACb9M,MAAM,EACN+O,0BAA0B,EAC1BH,OAAO,CACR,CAAC;AAGG,MAAMI,QAAQ,IAAI7F,OAExB,IAAyB;IACxB,MAAMnJ,MAAM,GAAG,IAAIC,eAAe,wKAACP,OAAO,CAACuP,GAAQ,CAAQ;IAC3D,OAAO,OAAO9F,OAAO,EAAE+F,QAAQ,KAAK,WAAW,GAC7CC,sBAAsB,CAACnP,MAAM,EAAEmJ,OAAO,CAAC+F,QAAQ,CAAC,GAChDlP,MAAM;AACV,CAAC;AAGM,MAAMoP,GAAG,GAAA,WAAA,IAAGtR,4JAAAA,AAAI,EAUrB,CAAC,EAAE,CACH4F,IAA4B,EAC5BxC,IAA+B,GACY4B,OAAO,CAACY,IAAI,GAAGX,CAAC,GAAKmI,GAAG,CAAChK,IAAI,GAAG2H,CAAC,GAAK;gBAAC9F,CAAC;gBAAE8F,CAAC;aAAC,CAAC,CAAC,CAAC;AAGrF,MAAMwG,UAAU,GAAA,WAAA,GAUnBvR,6JAAI,AAAJA,EAAK,CAAC,EAAE,CACV4F,IAA4B,EAC5BxC,IAA+B,GACe4B,OAAO,CAACY,IAAI,GAAGX,CAAC,GAAKmI,GAAG,CAAChK,IAAI,GAAG2H,CAAC,GAAK,CAAC;mBAAG9F,CAAC;gBAAE8F,CAAC;aAAC,CAAC,CAAC,CAAC;AAG3F,MAAMyG,OAAO,GAAA,WAAA,yJAQhBxR,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5BxC,IAA+B,GACM4B,OAAO,CAACY,IAAI,GAAGX,CAAC,GAAKU,EAAE,CAACvC,IAAI,EAAE6B,CAAC,CAAC,CAAC,CAAC;AAGlE,MAAM8I,QAAQ,GAAA,WAAA,yJAQjB/N,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5BxC,IAA+B,GACO4B,OAAO,CAACY,IAAI,EAAE,IAAMxC,IAAI,CAAC,CAAC;AAG3D,MAAMqO,OAAO,GAAA,WAAA,yJAUhBzR,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5BxC,IAA+B,EAC/BsE,CAAqB,GACgB1C,OAAO,CAACY,IAAI,GAAGX,CAAC,GAAKmI,GAAG,CAAChK,IAAI,GAAG2H,CAAC,GAAKrD,CAAC,CAACzC,CAAC,EAAE8F,CAAC,CAAC,CAAC,CAAC,CAAC;AAGhF,MAAM2G,KAAK,GAAA,WAAA,GAAyB3K,cAAc,CAAQ,MAAK;IACpE,MAAM4K,QAAQ,GAAGC,WAAW,CAAC,MAAK;IAChC,EAAA;IAAA,CACD,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACf,OAAO3H,IAAI,CAAC,IAAM4H,aAAa,CAACF,QAAQ,CAAC,CAAC;AAC5C,CAAC,CAAC;AAOK,MAAMG,cAAc,IAAUlM,IAAuB,GAC1DZ,OAAO,CAACyF,OAAO,GAAGA,OAAO,yJAAKvK,OAAAA,AAAI,EAAC0F,IAAI,EAAEmM,gBAAgB,CAACtH,OAAO,CAAC,CAAC,CAAC;AAG/D,MAAMsH,gBAAgB,GAAA,WAAA,yJAAG/R,OAAI,AAAJA,EAG9B,CAAC,EAAE,CAAC4F,IAAI,EAAE6E,OAAO,GAAKzF,OAAO,CAACY,IAAI,CAACoM,eAAe,CAACvH,OAAO,CAAC,EAAE,IAAM7E,IAAI,CAACqM,KAAK,CAAC,CAAC;AAO1E,MAAMC,WAAW,GAAsB;IAC5CtO,IAAI,EAAE,KAAK;IACXuO,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAEC,MAAM,CAACC,gBAAgB;IAChCrS,IAAIA,CAAA;QACF,6JAAOU,gBAAAA,AAAa,EAAC,IAAI,EAAE2C,SAAS,CAAC;IACvC;CACD;AAGM,MAAMiP,aAAa,GAAsB;IAC9C5O,IAAI,EAAE,OAAO;IACbuO,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,KAAK;IACdnS,IAAIA,CAAA;QACF,6JAAOU,gBAAa,AAAbA,EAAc,IAAI,EAAE2C,SAAS,CAAC;IACvC;CACD;AAGM,MAAMkP,aAAa,GAAsB;IAC9C7O,IAAI,EAAE,OAAO;IACbuO,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,KAAK;IACdnS,IAAIA,CAAA;QACF,6JAAOU,gBAAAA,AAAa,EAAC,IAAI,EAAE2C,SAAS,CAAC;IACvC;CACD;AAGM,MAAMmP,eAAe,GAAsB;IAChD9O,IAAI,EAAE,SAAS;IACfuO,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,KAAK;IACdnS,IAAIA,CAAA;QACF,6JAAOU,gBAAa,AAAbA,EAAc,IAAI,EAAE2C,SAAS,CAAC;IACvC;CACD;AAGM,MAAMoP,YAAY,GAAsB;IAC7C/O,IAAI,EAAE,MAAM;IACZuO,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,KAAK;IACdnS,IAAIA,CAAA;QACF,6JAAOU,gBAAAA,AAAa,EAAC,IAAI,EAAE2C,SAAS,CAAC;IACvC;CACD;AAGM,MAAMqP,aAAa,GAAsB;IAC9ChP,IAAI,EAAE,OAAO;IACbuO,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,KAAK;IACdnS,IAAIA,CAAA;QACF,6JAAOU,gBAAAA,AAAa,EAAC,IAAI,EAAE2C,SAAS,CAAC;IACvC;CACD;AAGM,MAAMsP,aAAa,GAAsB;IAC9CjP,IAAI,EAAE,OAAO;IACbuO,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,CAAC;IACVnS,IAAIA,CAAA;QACF,6JAAOU,gBAAAA,AAAa,EAAC,IAAI,EAAE2C,SAAS,CAAC;IACvC;CACD;AAGM,MAAMuP,YAAY,GAAsB;IAC7ClP,IAAI,EAAE,MAAM;IACZuO,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAEC,MAAM,CAACS,gBAAgB;IAChC7S,IAAIA,CAAA;QACF,6JAAOU,gBAAAA,AAAa,EAAC,IAAI,EAAE2C,SAAS,CAAC;IACvC;CACD;AAGM,MAAMyP,YAAY,GAAqC;IAC5Dd,WAAW;IACXW,aAAa;IACbD,aAAa;IACbD,YAAY;IACZD,eAAe;IACfD,aAAa;IACbD,aAAa;IACbM,YAAY;CACb;AAED,gFAAA;AACA,WAAA;AACA,gFAAA;AAEA,cAAA,GACA,MAAMG,iBAAiB,GAAG,iBAAiB;AAGpC,MAAMC,cAAc,GAAA,WAAA,GAA4B1Q,MAAM,CAACC,GAAG,CAC/DwQ,iBAAiB,CACS;AAE5B,MAAME,gBAAgB,GAAG;IACvB,kBAAA,GACAC,EAAE,GAAGvM,CAAM,GAAKA;CACjB;AAGM,MAAMwM,WAAW,IAAOzN,IAA0B,GACvDrB,gBAAgB,EAAEkF,KAAK,GAAKY,WAAW,CAACZ,KAAK,CAACoF,WAAW,CAACjJ,IAAI,CAAC,CAAC,CAAC;AAG5D,MAAM0N,iBAAiB,GAAA,WAAA,yJAAGtT,OAAAA,AAAI,EAGnC,CAAC,EAAE,CAAC4F,IAAI,EAAExB,KAAK,GAAKmP,cAAc,CAAC3N,IAAI,GAAG4N,CAAC,GAAK;YAACA,CAAC;YAAEpP,KAAK;SAAU,CAAC,CAAC;AAGhE,MAAMqP,oBAAoB,GAAA,WAAA,yJAAGzT,OAAAA,AAAI,EAGtC,CAAC,EAAE,CAAC4F,IAAI,EAAE8B,CAAC,GAAK6L,cAAc,CAAC3N,IAAI,GAAG4N,CAAC,GAAK;YAACA,CAAC;YAAE9L,CAAC,CAAC8L,CAAC,CAAC;SAAU,CAAC,CAAC;AAG3D,MAAME,wBAAwB,GAAA,WAAA,yJAAG1T,OAAI,AAAJA,EAQtC,CAAC,EAAE,CAAC4F,IAAI,EAAEyC,EAAE,GAAKkL,cAAc,CAAC3N,IAAI,GAAG4N,CAAC,GAAK;YAACA,CAAC;gKAAE7S,MAAM,CAAC2H,KAAAA,AAAS,EAACD,EAAE,CAACmL,CAAC,CAAC,EAAE,IAAMA,CAAC,CAAC;SAAU,CAAC,CAAC;AAGxF,MAAMG,eAAe,GAAA,WAAA,yJAAG3T,OAAAA,AAAI,EAGjC,CAAC,EAAE,CAAC4F,IAAI,EAAE8B,CAAC,GAAK1C,OAAO,CAACqO,WAAW,CAACzN,IAAI,CAAC,EAAE8B,CAAC,CAAC,CAAC;AAGzC,MAAMkM,WAAW,GAAA,WAAA,GAAG5T,6JAAAA,AAAI,EAG7B,CAAC,EAAE,CAAC4F,IAAI,EAAExB,KAAK,GAAKmP,cAAc,CAAC3N,IAAI,EAAE,IAAM;YAAC,KAAK,CAAC;YAAExB,KAAK;SAAU,CAAC,CAAC;AAGpE,MAAMyP,cAAc,IAAOjO,IAA0B,GAC1DrB,gBAAgB,EAAEmG,KAAK,IAAI;QACzBA,KAAK,CAACoJ,oBAAoB,CAAClO,IAAI,CAAC;QAChC,OAAOkB,KAAK;IACd,CAAC,CAAC;AAGG,MAAMiN,aAAa,GAAOnO,IAA0B,IAA0BgO,WAAW,CAAChO,IAAI,EAAEA,IAAI,CAACoO,OAAO,CAAC;AAG7G,MAAMT,cAAc,GAAA,WAAA,yJAAGvT,OAAAA,AAAI,EAGhC,CAAC,EAAE,CACH4F,IAA0B,EAC1B8B,CAA4B,GAE5BnD,gBAAgB,EAAEmG,KAAK,IAAI;QACzB,MAAM,CAACK,CAAC,EAAE9F,CAAC,CAAC,GAAGyC,CAAC,CAACgD,KAAK,CAACmE,WAAW,CAACjJ,IAAI,CAAM,CAAC;QAC9C8E,KAAK,CAACuJ,WAAW,CAACrO,IAAI,EAAEX,CAAC,CAAC;QAC1B,OAAOY,OAAO,CAACkF,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC;AAGE,MAAMmJ,kBAAkB,GAAGA,CAChCtO,IAA0B,EAC1BuO,GAAM,EACNzM,CAA2C,GACtB6L,cAAc,CAAC3N,IAAI,GAAG4N,CAAC,uJAAK7S,MAAM,CAAC2H,KAAAA,AAAS,EAACZ,CAAC,CAAC8L,CAAC,CAAC,EAAE,IAAM;gBAACW,GAAG;gBAAEX,CAAC;aAAU,CAAC,CAAC;AAG5F,MAAMY,cAAc,GAAA,WAAA,yJAAGpU,OAAAA,AAAI,EAGhC,CAAC,EAAE,CAAC4F,IAAI,EAAE8B,CAAC,GAAK6L,cAAc,CAAC3N,IAAI,GAAG4N,CAAC,GAAK;YAAC,KAAK,CAAC;YAAE9L,CAAC,CAAC8L,CAAC,CAAC;SAAU,CAAC,CAAC;AAGhE,MAAMa,kBAAkB,GAAA,WAAA,yJAAGrU,OAAAA,AAAI,EAGpC,CAAC,EAAE,CAAC4F,IAAI,EAAEyC,EAAE,GAAKkL,cAAc,CAAC3N,IAAI,GAAG4N,CAAC,GAAK;YAAC,KAAK,CAAC;+JAAE7S,MAAM,CAAC2H,MAAS,AAATA,EAAUD,EAAE,CAACmL,CAAC,CAAC,EAAE,IAAMA,CAAC,CAAC;SAAU,CAAC,CAAC;AAG7F,MAAMc,oBAAoB,GAAA,WAAA,yJAAGtU,OAAAA,AAAI,EAGtC,CAAC,EAAE,CAAC4F,IAAI,EAAE8B,CAAC,GACX6L,cAAc,CAAC3N,IAAI,EAAG4N,CAAC,IAAI;QACzB,MAAM5F,MAAM,GAAGlG,CAAC,CAAC8L,CAAC,CAAC;QACnB,OAAO;YAAC5F,MAAM;YAAEA,MAAM;SAAU;IAClC,CAAC,CAAC,CAAC;AAGE,MAAM2G,wBAAwB,GAAA,WAAA,yJAAGvU,OAAAA,AAAI,EAG1C,CAAC,EAAE,CAAC4F,IAAI,EAAEyC,EAAE,GACZkL,cAAc,CAAC3N,IAAI,EAAG4N,CAAC,IAAI;QACzB,MAAM5F,MAAM,uJAAGjN,MAAM,CAAC2H,KAAS,AAATA,EAAUD,EAAE,CAACmL,CAAC,CAAC,EAAE,IAAMA,CAAC,CAAC;QAC/C,OAAO;YAAC5F,MAAM;YAAEA,MAAM;SAAU;IAClC,CAAC,CAAC,CAAC;AAEL,WAAA;AACA,cAAA,GACA,MAAM4G,wBAAwB,GAAG,wBAAwB;AAGlD,MAAMC,qBAAqB,GAAA,WAAA,GAA0CjS,MAAM,CAACC,GAAG,CACpF+R,wBAAwB,CACgB;AAE1C,MAAME,uBAAuB,GAAG;IAC9B,kBAAA,GACAtB,EAAE,EAAGvM,CAAU,IAAKA,CAAC;IACrB,kBAAA,GACA8N,EAAE,GAAG9N,CAAQ,GAAKA;CACnB;AAGK,MAAO+N,mBAAmB;IAGnBC,MAAA,CAAA;IAGA1L,MAAA,CAAA;IALF,CAACsL,qBAAqB,CAAA,GAAIC,uBAAuB,CAAA;IAC1D3R,YACW8R,MAEyB,EACzB1L,MAAgB,CAAA;QAHhB,IAAA,CAAA0L,MAAM,GAANA,MAAM;QAGN,IAAA,CAAA1L,MAAM,GAANA,MAAM;IAEjB;IACA,+IAAC/I,IAAI,CAAC+C,IAAM,CAAA,GAAC;QACX,yJAAO/C,IAAI,CAACiD,IAAAA,AAAM,EAAC,IAAI,EAAE,IAAI,CAAC8F,MAAM,IAAG/I,IAAI,CAAC6D,mJAAAA,AAAI,EAAC,IAAI,CAACkF,MAAM,CAAC,qJAAG/I,IAAI,CAACkD,IAAAA,AAAM,EAAC,IAAI,CAAC,CAAC;IACpF;IACA,gJAACxD,KAAK,CAACqD,GAAM,CAAA,CAAEC,IAAa,EAAA;QAC1B,OAAO,IAAI,CAAC+F,MAAM,GAChB2L,iBAAiB,CAAC1R,IAAI,CAAC,uJAAItD,KAAK,CAACgE,GAAAA,AAAM,EAAC,IAAI,CAACqF,MAAM,EAAG/F,IAAsC,CAAC+F,MAAM,CAAC,GACpG,IAAI,KAAK/F,IAAI;IACjB;IACA2R,UAAUA,CAAC,GAAGC,GAAmB,EAAA;QAC/B,OAAO,IAAIJ,mBAAmB,CAAC,IAAI,CAACC,MAAM,qJAAEnV,KAAK,CAACmM,SAAAA,AAAY,EAACmJ,GAAG,CAAC,CAAC;IACtE;IACA9U,IAAIA,CAAA,EAAA;QACF,WAAOU,kKAAAA,AAAa,EAAC,IAAI,EAAE2C,SAAS,CAAC;IACvC;;AAIK,MAAMuR,iBAAiB,IAAIxQ,CAAU,0JAC1CzD,cAAAA,AAAW,EAACyD,CAAC,EAAEmQ,qBAAqB,CAAC;AAKhC,MAAMQ,eAAe,GAAA,WAAA,OAAGjV,yJAAAA,AAAI,EAYjC,CAAC,EAAE,CACH4E,GAA0C,EAC1CgB,IAA0B,EAC1BxB,KAAQ,GAER,IAAIwQ,mBAAmB,EACpBM,QAAQ,GACPlG,eAAe,CACbpK,GAAG,CAACiQ,MAAM,CAACK,QAAQ,CAAC,EACpBtP,IAAI,EACJxB,KAAK,CACN,GACH1E,KAAK,CAACyV,mJAAAA,AAAI,EAAC,SAAS,EAAEvQ,GAAG,EAAEgB,IAAI,EAAExB,KAAK,CAAC,CACxC,CAAC;AAGG,MAAMgR,mBAAmB,GAAGA,CACjCxP,IAAkC,EAClCyP,GAAyB,EACzBjR,KAAQ,OACyBjD,gBAAgB,CAACmU,6JAAAA,AAAM,EAAC1P,IAAI,EAAE2P,cAAc,CAACF,GAAG,EAAEjR,KAAK,CAAC,CAAC;AAE5F,MAAMmR,cAAc,GAAGA,CACrBF,GAAyB,EACzBjR,KAAQ,GAAA,CACgE;QACxEoR,SAAS,EAAEA,CAAA,uKAAMrU,SAAsB,OAAN,CAACsU;QAClCC,OAAO,EAAEA,CAACxN,IAAI,EAAEC,KAAK,4KAAKhH,MAAiBwU,AAAG,EAACzN,IAAI,EAAEC,EAAX,CAACwN,EAAe,CAAC;QAC3DC,OAAO,EAAEA,CAAC1N,IAAI,EAAEC,KAAK,4KAAKhH,MAAiB0U,AAAG,EAAC3N,IAAI,EAAEC,EAAX,CAAC0N,EAAe,CAAC;QAC3DC,UAAU,EAAEA,CAACC,UAAU,EAAEC,cAAc,4KACrC7U,SAAiB8U,AAAM,EACrBhB,KADc,CAACgB,SACA,CAACF,UAAU,EAAEV,GAAG,EAAEjR,KAAK,CAAC,EACvC4R,cAAqB;KAE1B,CAAC;AAGK,MAAMhH,eAAe,GAAA,WAAA,IAGxBhP,4JAAAA,AAAI,EACN,CAAC,EACD,CAAa4E,GAA2B,EAAEgB,IAA0B,EAAExB,KAAQ,GAC5EM,iBAAiB,CACf8M,OAAO,CAAC6B,WAAW,CAACzN,IAAI,CAAC,EAAEgO,WAAW,CAAChO,IAAI,EAAExB,KAAK,CAAC,CAAC,EACpD,IAAMQ,GAAG,GACRsR,QAAQ,GAAKtC,WAAW,CAAChO,IAAI,EAAEsQ,QAAQ,CAAC,CAC1C,CACJ;AAGM,MAAMC,mBAAmB,GAAA,WAAA,yJAAGnW,OAAAA,AAAI,EAGrC,CAAC,EAAE,CAAC4E,GAAG,EAAEgB,IAAI,EAAE8B,CAAC,GAAKiM,eAAe,CAAC/N,IAAI,GAAGX,CAAC,GAAK+J,eAAe,CAACpK,GAAG,EAAEgB,IAAI,EAAE8B,CAAC,CAACzC,CAAC,CAAC,CAAC,CAAC,CAAC;AAG/E,MAAMmR,kBAAkB,GAAGA,CAChCpC,OAAc,EACd3I,OAGC,GAEDgL,uBAAuB,CAACrC,OAAO,EAAE;QAC/BsC,MAAM,MAAEhV,cAAc,CAACsP,sJAAAA,AAAM,EAAE;QAC/B2F,IAAI,EAAElL,OAAO,EAAEkL,IAAI,sJAAItW,WAAQ;QAC/BuW,IAAI,EAAEnL,OAAO,EAAEmL;KAChB,CAAC;AAGG,MAAMC,yBAAyB,IACpCzC,OAA2B,IACc;IACzC,MAAMsC,MAAM,mKAAGhV,UAAeoV,AAAO,EAAK,EAAb,CAACA;IAC9B,OAAOL,uBAAuB,CAACrC,OAAO,EAAE;QACtCsC,MAAM;QACNC,IAAI,EAAED,MAAM,CAACb,KAAAA;KACd,CAAC;AACJ,CAAC;AAGM,MAAMkB,+BAA+B,IAC1C3C,OAAyB,IACc;IACvC,MAAMsC,MAAM,mKAAGhV,cAAc,CAACsV,CAAAA,AAAa,GAACtV,cAAc,CAACsP,yJAAAA,AAAM,EAAK,CAAC;IACvE,OAAOyF,uBAAuB,CAACrC,OAAO,EAAE;QACtCsC,MAAM;QACNC,IAAI,EAAED,MAAM,CAACb,KAAAA;KACd,CAAC;AACJ,CAAC;AAGM,MAAMoB,yBAAyB,GACpC7C,OAA2B,IACc;IACzC,MAAMsC,MAAM,mKAAGhV,cAAc,AAACwV,AAAW,CAAXA,CAAgB;IAC9C,OAAOT,uBAAuB,CAACrC,OAAO,EAAE;QACtCsC,MAAM;QACNC,IAAI,EAAED,MAAM,CAACb,KAAAA;KACd,CAAC;AACJ,CAAC;AAGM,MAAMY,uBAAuB,GAAGA,CACrCrC,OAAc,EACd3I,OAIC,KAC2B;IAC5B,MAAM0L,SAAS,GAAG;QAChB,GAAGxV,kLAAe;QAClB,CAAC2R,cAAc,CAAA,EAAGC,gBAAgB;QAClCa,OAAO;QACP7N,MAAMA,CAAA;YACJ,OAAOkN,WAAW,CAAC,IAAI,CAAC;QAC1B,CAAC;QACD2D,IAAI,EAAEA,CAACd,QAAe,EAAEe,QAAe,GAAK5L,OAAO,CAACiL,MAAM,CAACU,IAAI,CAACd,QAAQ,EAAEe,QAAQ,CAAC;QACnFjT,OAAO,EAAEA,CAACkT,KAAY,EAAEC,MAAa,GAAK9L,OAAO,CAACiL,MAAM,CAACtS,OAAO,CAACkT,KAAK,EAAEC,MAAM,CAAC;QAC/ExU,KAAK,GAAGA,KAAY,IAAMuT,QAAe,GAAK7K,OAAO,CAACiL,MAAM,CAAC3T,KAAK,CAACA,KAAK,EAAEuT,QAAQ,CAAC;QACnFK,IAAI,EAAElL,OAAO,CAACkL,IAAI;QAClBC,IAAI,EAAEnL,OAAO,CAACmL,IAAI,IAAA,CAAK,CAAC3P,CAAC,EAAEuQ,CAAC,GAAKA,CAAC;KACnC;IACD,OAAOL,SAAS;AAClB,CAAC;AAGM,MAAMM,8BAA8B,IACzCrD,OAAkC,GAElCqC,uBAAuB,CAACrC,OAAO,EAAE;QAC/BsC,MAAM,oKAAEzU,SAAoB,IAAP,CAACyU;QACtBC,IAAI,oKAAE1U,SAAoB,CAAC4T,GAAR,CAACa,CAAOb;KAC5B,CAAC;AAGG,MAAM6B,cAAc,GAAA,WAAA,4JAA8CnX,cAAAA,AAAW,EAAA,WAAA,GAClFqC,MAAM,CAACC,GAAG,CAAC,gCAAgC,CAAC,EAC5C,IAAMoU,yBAAyB,sJAAClX,OAAO,CAAC8V,AAAK,EAAE,CAAC,CACjD;AAGM,MAAM8B,yBAAyB,GAAA,WAAA,4JAA8BpX,cAAAA,AAAW,EAAA,WAAA,GAC7EqC,MAAM,CAACC,GAAG,CAAC,2CAA2C,CAAC,EACvD,IAAM2T,kBAAkB,CAAC,CAAC,CAAC,CAC5B;AAGM,MAAMoB,wBAAwB,GAAA,WAAA,4JAA8BrX,cAAAA,AAAW,EAAA,WAAA,GAC5EqC,MAAM,CAACC,GAAG,CAAC,0CAA0C,CAAC,EACtD,IAAM2T,kBAAkB,CAAC,IAAI,CAAC,CAC/B;AAGM,MAAMqB,qBAAqB,GAAA,WAAA,GAAwDtX,uKAAAA,AAAW,EAAA,WAAA,GACnGqC,MAAM,CAACC,GAAG,CAAC,sCAAsC,CAAC,EAClD,IAAM2T,kBAAkB,sJAAC/V,OAAO,CAACoV,AAAK,EAAE,CAAC,CAC1C;AAGM,MAAMiC,eAAe,GAAA,WAAA,4JAAyCvX,cAAAA,AAAW,EAAA,WAAA,GAC9EqC,MAAM,CAACC,GAAG,CAAC,iCAAiC,CAAC,EAC7C,IAAM2T,kBAAkB,CAAoBzD,YAAY,CAAC,CAC1D;AAGM,MAAMgF,cAAc,GAAA,WAAA,GAAkDxX,uKAAAA,AAAW,EAAA,WAAA,GACtFqC,MAAM,CAACC,GAAG,CAAC,gCAAgC,CAAC,EAC5C,IAAM2T,kBAAkB,mJAAC3V,IAAI,CAACgV,GAAK,AAALA,EAAwB,CAAC,CACxD;AAGM,MAAMpE,sBAAsB,GAAA,WAAA,IAAGrR,4JAAAA,AAAI,EAGxC,CAAC,EAAE,CAAC4F,IAAI,EAAEgS,SAAS,GAAK5I,eAAe,CAACpJ,IAAI,EAAE2R,yBAAyB,EAAEK,SAAS,CAAC,CAAC;AAG/E,MAAMC,qBAAqB,GAAA,WAAA,wJAAG7X,QAAAA,AAAI,EAGvC,CAAC,EAAE,CAAC4F,IAAI,EAAEgS,SAAS,GAAK5I,eAAe,CAACpJ,IAAI,EAAE4R,wBAAwB,EAAEI,SAAS,CAAC,CAAC;AAG9E,MAAMrH,kBAAkB,GAAA,WAAA,4JAA4CpQ,cAAAA,AAAW,EAAA,WAAA,GACpFqC,MAAM,CAACC,GAAG,CAAC,oCAAoC,CAAC,EAChD,IAAM2T,kBAAkB,CAAuB,WAAW,CAAC,CAC5D;AAKM,MAAM1F,sBAAsB,GAAA,WAAA,4JAAGvQ,cAAAA,AAAW,EAAA,WAAA,GAC/CqC,MAAM,CAACC,GAAG,CAAC,wCAAwC,CAAC,EACpD,IAAM2T,kBAAkB,CAAC,IAAI,CAAC,CAC/B;AAGM,MAAM0B,6BAA6B,GAAA,WAAA,2JAAwD3X,eAAAA,AAAW,EAAA,WAAA,GAC3GqC,MAAM,CAACC,GAAG,CAAC,+CAA+C,CAAC,EAC3D,IAAM2T,kBAAkB,qJAACzV,MAAM,CAACsO,AAAI,EAAoB2D,aAAa,CAAC,CAAC,CACxE;AAGM,MAAMmF,0BAA0B,GAAA,WAAA,GAAG/X,6JAAAA,AAAI,EAG5C,CAAC,EAAE,CAAC4F,IAAI,EAAEoS,KAAK,GAAKhJ,eAAe,CAACpJ,IAAI,EAAEkS,6BAA6B,EAAEE,KAAK,CAAC,CAAC;AAG3E,MAAMC,mBAAmB,GAAA,WAAA,4JAA8D9X,cAAAA,AAAW,EAAA,WAAA,GACvGqC,MAAM,CAACC,GAAG,CAAC,qCAAqC,CAAC,EACjD,IAAMkU,+BAA+B,oJAAClX,GAAG,CAACgW,IAAAA,AAAK,EAAE,CAAC,CACnD;AAGM,MAAMyC,YAAY,GAAA,WAAA,GAA0D7E,WAAW,CAC5F4E,mBAAmB,CACpB;AAGM,MAAMnJ,wBAAwB,GAAA,WAAA,2JAA4D3O,eAAAA,AAAW,EAAA,WAAA,GAC1GqC,MAAM,CAACC,GAAG,CAAC,0CAA0C,CAAC,EACtD,IACE2T,kBAAkB,CAACzV,MAAM,CAAC6F,oJAAAA,AAAI,EAAE,GAAE;QAChC+P,IAAI,EAAEA,CAAA,uJAAM5V,MAAM,CAAC6F,AAAI,EAA0C;QACjEgQ,IAAI,EAAEA,CAAC2B,MAAM,EAAEtR,CAAC,GAAKsR;KACtB,CAAC,CACL;AAGM,MAAMC,uBAAuB,GAAA,WAAA,GAA0CjY,uKAAAA,AAAW,EAAA,WAAA,GACvFqC,MAAM,CAACC,GAAG,CAAC,yCAAyC,CAAC,EACrD,IACE2T,kBAAkB,4JAAChV,QAAmB,EAAE,GAAR,CAACqU;QAC/Bc,IAAI,EAAEA,CAAA,8JAAMnV,QAAmB,KAAN,CAACqU;QAC1Be,IAAI,EAAEA,CAAC2B,MAAM,EAAEtR,CAAC,GAAKsR;KACtB,CAAC,CACL;AAGM,MAAMpH,oBAAoB,GAAA,WAAA,2JAA+B5Q,eAAAA,AAAW,EAAA,WAAA,GACzEqC,MAAM,CAACC,GAAG,CAAC,sCAAsC,CAAC,EAClD,IAAM2T,kBAAkB,CAAC,IAAI,CAAC,CAC/B;AAGM,MAAMnF,0BAA0B,GAAA,WAAA,4JAA+B9Q,cAAW,AAAXA,EAAW,WAAA,GAC/EqC,MAAM,CAACC,GAAG,CAAC,qCAAqC,CAAC,EACjD,IAAM2T,kBAAkB,CAAC,IAAI,CAAC,CAC/B;AAGM,MAAMiC,4BAA4B,GAAA,WAAA,GAAwDlY,uKAAAA,AAAW,EAAA,WAAA,GAC1GqC,MAAM,CAACC,GAAG,CAAC,8CAA8C,CAAC,EAC1D,IAAM2T,kBAAkB,sJAAC/V,OAAO,CAACoV,AAAK,EAAE,CAAC,CAC1C;AAGM,MAAM6C,sBAAsB,GAAA,WAAA,2JAAoDnY,eAAAA,AAAW,EAAA,WAAA,GAChGqC,MAAM,CAACC,GAAG,CAAC,wCAAwC,CAAC,EACpD,IAAM2T,kBAAkB,oJAAC1W,KAAK,CAAC+V,EAAAA,AAAK,EAAE,CAAC,CACxC;AAOM,MAAM8C,WAAW,GAAA,WAAA,GAAsB/V,MAAM,CAACC,GAAG,CAAC,cAAc,CAAsB;AAGtF,MAAM+V,oBAAoB,GAAA,WAAA,GAA+BhW,MAAM,CAACC,GAAG,CACxE,uBAAuB,CACM;AAGxB,MAAMgW,iBAAiB,GAAGA,CAC/B7S,IAAiB,EACjB8S,SAAiC,GACT9S,IAAI,CAAC+S,YAAY,CAAC,IAAM7S,MAAM,CAAC4S,SAAS,CAAC,CAAC;AAG7D,MAAME,qBAAqB,GAAGA,CACnChT,IAAiB,EACjB8S,SAAgC,GACR9S,IAAI,CAAC+S,YAAY,CAACD,SAAS,CAAC;AAG/C,MAAMG,UAAU,GAAGA,CACxBjT,IAA2B,EAC3BV,IAAiC,GACTU,IAAI,CAACkT,KAAK,CAAC5T,IAAI,CAAC;AAGnC,MAAM6T,SAAS,GAAGA,CACvBnT,IAAiB,EACjBoT,QAA6C,GACJpT,IAAI,CAAC2Q,IAAI,CAACyC,QAAQ,CAAC;AAOvD,MAAMC,WAAW,IAAOrT,IAAoB,IAAa;IAC9D,OAAOsT,eAAe,mJAACjZ,WAAQ,CAAC,CAAC2F,IAAI,CAAC;AACxC,CAAC;AAGM,MAAMsT,eAAe,GAAA,WAAA,GAAGlZ,6JAAAA,AAAI,EAGjC,CAAC,EAAE,CAAC4F,IAAI,EAAE8B,CAAC,KAAI;IACf,MAAMyR,MAAM,yJAAGjZ,OAAAA,AAAI,EAAC0F,IAAI,6JAAExE,aAAa,CAACgY,EAAa,sJAAEzY,MAAOyM,AAAD,AAAI,CAAHA,CAAI1F,CAAC,CAAC,CAAC;IACrE,OAAQyR,MAAM,CAACvV,IAAI;QACjB,KAAK,MAAM;YAAE;gBACX,6JAAO1D,OAAAA,AAAI,gKACTkB,WAAqB,AAAPmK,EAAQ3F,AAAT,CAAC2F,GAAY,CAAC,iJAC3B7L,KAAK,CAAC2Z,CAAI,EACV1Y,MAAM,CAAC2Y,qJAAAA,AAAK,EAAC;oBACXC,MAAM,EAAEA,CAAA,KAAK;wBACX,MAAMC,UAAU,GAAG/Z,GAAG,CAACoM,8JAAAA,AAAY,iKAACzK,aAAa,CAAC8M,CAAAA,AAAY,EAACtI,IAAI,CAAC,CAAC,CAACZ,OAAO,EAAEyF,OAAO,OACpFhL,GAAG,CAACoM,0JAAAA,AAAY,uJAAC9L,MAAW,AAAHiV,CAAD,CAAKvK,AAAJuK,OAAW,CAAC,CAAC,CAAC5H,GAAG,EAAEzC,EAAE,GAAK,CAAA,CAAA,EAAIA,EAAE,EAAE,CAAC,CAC7D;wBACD,OAAO,IAAI8O,oBAAoB,CAACD,UAAU,GAAG,CAAA,uBAAA,EAA0BA,UAAU,CAAChD,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC;oBAC1G,CAAC;oBACDkD,MAAM,oJAAEzZ,WAAAA;iBACT,CAAC,CACH;YACH;QACA,KAAK,MAAM;YAAE;gBACX,OAAOkZ,MAAM,CAAC/U,KAAK;YACrB;IACF;AACF,CAAC,CAAC;AAOK,MAAMuV,cAAc,GAAA,WAAA,GAA2E;IACpG,MAAMA,cAAe,SAAQC,UAAU,CAACxT,KAAK;QAC3CD,MAAMA,CAAA,EAAA;YACJ,OAAOmE,IAAI,CAAC,IAAI,CAAC;QACnB;QACA9J,MAAMA,CAAA,EAAA;YACJ,MAAMqI,GAAG,GAAG;gBAAE,GAAG,IAAA;YAAI,CAAE;YACvB,IAAI,IAAI,CAACe,OAAO,EAAEf,GAAG,CAACe,OAAO,GAAG,IAAI,CAACA,OAAO;YAC5C,IAAI,IAAI,CAAC1F,KAAK,EAAE2E,GAAG,CAAC3E,KAAK,GAAG,IAAI,CAACA,KAAK;YACtC,OAAO2E,GAAG;QACZ;QACA,sJAACtI,oBAAiB,CAAA,GAAC;YACjB,IAAI,IAAI,CAACkD,QAAQ,KAAKmW,UAAU,CAACxT,KAAK,CAACyT,SAAS,CAACpW,QAAQ,EAAE;gBACzD,OAAO,IAAI,CAACqW,KAAK,GAAG,GAAG,IAAI,CAACrW,QAAQ,EAAE,CAAA,EAAA,EAAK,IAAI,CAACqW,KAAK,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAACxD,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC/S,QAAQ,EAAE;YAC3G,CAAC,MAAM,IAAI,KAAK,IAAImW,UAAU,EAAE;gBAC9B,sKAAOxY,SAAc6Y,AAAM,EAAC7Y,EAAR,CAAC6Y,UAAoB,CAAC3P,wJAAAA,AAAI,EAAC,IAAI,CAAC,EAAE;oBAAE4P,gBAAgB,EAAE;gBAAI,CAAE,CAAC;YACnF;YACA,OAAO,IAAI;QACb;;IAEFlK,MAAM,CAACmK,MAAM,CAACR,cAAc,CAACE,SAAS,EAAEpY,4LAAyB,CAAC;IAClE,OAAOkY,cAAqB;AAC9B,CAAC,CAAC,CAAE;AAEJ,MAAMS,aAAa,GAAGA,CACpBC,KAAmD,EACnDC,GAAc,KAC4B;IAC1C,MAAMC,IAAK,SAAQZ,cAAc;QACtB/V,IAAI,GAAG0W,GAAG,CAAA;;IAErBtK,MAAM,CAACmK,MAAM,CAACI,IAAI,CAACV,SAAS,EAAEQ,KAAK,CAAC;IAClCE,IAAI,CAACV,SAAiB,CAACW,IAAI,GAAGF,GAAG;IACnC,OAAOC,IAAW;AACpB,CAAC;AAGM,MAAME,sBAAsB,GAAA,WAAA,GAAiCjY,MAAM,CAACC,GAAG,CAC5E,sCAAsC,CACP;AAG1B,MAAMqH,gBAAgB,GAAA,WAAA,GAAGsQ,aAAa,CAAyB;IACpE,CAACK,sBAAsB,CAAA,EAAGA;CAC3B,EAAE,kBAAkB,CAAC;AAGf,MAAMC,kBAAkB,IAAIpW,CAAU,0JAAkCzD,cAAAA,AAAW,EAACyD,CAAC,EAAEmW,sBAAsB,CAAC;AAG9G,MAAME,0BAA0B,GAAA,WAAA,GAAqCnY,MAAM,CAACC,GAAG,CACpF,0CAA0C,CACP;AAG9B,MAAMgX,oBAAoB,GAAA,WAAA,GAAGW,aAAa,CAA6B;IAC5E,CAACO,0BAA0B,CAAA,EAAGA;CAC/B,EAAE,sBAAsB,CAAC;AAGnB,MAAMC,sBAAsB,IAAItW,CAAU,yJAC/CzD,eAAAA,AAAW,EAACyD,CAAC,EAAEqW,0BAA0B,CAAC;AAGrC,MAAME,8BAA8B,GAAA,WAAA,GAAyCrY,MAAM,CAACC,GAAG,CAC5F,qCAAqC,CACE;AAGlC,MAAMqY,wBAAwB,GAAA,WAAA,GAAGV,aAAa,CAAiC;IACpF,CAACS,8BAA8B,CAAA,EAAGA;CACnC,EAAE,0BAA0B,CAAC;AAGvB,MAAME,0BAA0B,IAAIzW,CAAU,0JACnDzD,cAAAA,AAAW,EAACyD,CAAC,EAAEuW,8BAA8B,CAAC;AAGzC,MAAMG,4BAA4B,GAAA,WAAA,GAAuCxY,MAAM,CAACC,GAAG,CACxF,mCAAmC,CACE;AAGhC,MAAMwY,sBAAsB,GAAA,WAAA,GAAGb,aAAa,CAA+B;IAChF,CAACY,4BAA4B,CAAA,EAAGA;CACjC,EAAE,wBAAwB,CAAC;AAGrB,MAAME,wBAAwB,IAAI5W,CAAU,IACjDzD,oKAAAA,AAAW,EAACyD,CAAC,EAAE0W,4BAA4B,CAAC;AAGvC,MAAMG,oCAAoC,GAAA,WAAA,GAA+C3Y,MAAM,CAACC,GAAG,CACxG,oDAAoD,CACP;AAGxC,MAAM2Y,8BAA8B,GAAA,WAAA,GAAGhB,aAAa,CAAuC;IAChG,CAACe,oCAAoC,CAAA,EAAGA;CACzC,EAAE,gCAAgC,CAAC;AAG7B,MAAME,+BAA+B,GAAA,WAAA,GAA0C7Y,MAAM,CAACC,GAAG,CAC9F,+CAA+C,CACP;AAGnC,MAAM6Y,yBAAyB,GAAA,WAAA,GAAGlB,aAAa,CAAkC;IACtF,CAACiB,+BAA+B,CAAA,EAAGA;CACpC,EAAE,2BAA2B,CAAC;AAGxB,MAAME,2BAA2B,IAAIjX,CAAU,OACpDzD,iKAAAA,AAAW,EAACyD,CAAC,EAAE+W,+BAA+B,CAAC;AAG1C,MAAMG,sBAAsB,IAAIlX,CAAU,0JAC/CzD,cAAW,AAAXA,EAAYyD,CAAC,EAAE6W,oCAAoC,CAAC;AAG/C,MAAMM,sBAAsB,GAAA,WAAA,GAAiCjZ,MAAM,CAACC,GAAG,CAC5E,6BAA6B,CACE;AAG1B,MAAMiZ,gBAAgB,GAAA,WAAA,GAAGtB,aAAa,CAAyB;IACpE,CAACqB,sBAAsB,CAAA,EAAGA;CAC3B,EAAE,kBAAkB,CAAC;AAGf,MAAME,4BAA4B,GAAIC,QAAgC,IAC3E,IAAIF,gBAAgB,CAAC,CAAA,2BAAA,wJAA8B9b,QAAQ,CAACU,AAAM,EAACsb,QAAQ,CAAC,CAAA,CAAA,CAAG,CAAC;AAG3E,MAAMC,kBAAkB,IAAIvX,CAAU,0JAAkCzD,cAAAA,AAAW,EAACyD,CAAC,EAAEmX,sBAAsB,CAAC;AAG9G,MAAMK,sBAAsB,GAAA,WAAA,GAAiCtZ,MAAM,CAACC,GAAG,CAC5E,sCAAsC,CACP;AAG1B,MAAMwI,gBAAgB,GAAA,WAAA,GAC1B;IACC,MAAMA,gBAAiB,SAAQ0O,cAAc;QAClC/V,IAAI,GAAG,kBAAkB,CAAA;QACzB2G,KAAK,CAAA;QACdxH,YAAYmB,KAAc,EAAE0F,OAAgB,CAAA;YAC1C,KAAK,CAACA,OAAO,IAAI,2BAA2B,EAAE;gBAAE1F;YAAK,CAAE,CAAC;YACxD,IAAI,CAACqG,KAAK,GAAGrG,KAAK;QACpB;;IAEF8L,MAAM,CAACmK,MAAM,CAAClP,gBAAgB,CAAC4O,SAAS,EAAE;QACxC,CAACiC,sBAAsB,CAAA,EAAGA,sBAAsB;QAChDtB,IAAI,EAAE;KACP,CAAC;IACF,OAAOvP,gBAAuB;AAChC,CAAC,CAAC,CAAE;AAGC,MAAM8Q,kBAAkB,IAAIzX,CAAU,0JAAkCzD,cAAAA,AAAW,EAACyD,CAAC,EAAEwX,sBAAsB,CAAC;AAO9G,MAAMjY,UAAU,IAAIS,CAAU,GACnCD,QAAQ,CAACC,CAAC,CAAC,IAAI,MAAM,IAAIA,CAAC,IAAA,CAAKA,CAAC,CAACV,IAAI,KAAK,SAAS,IAAIU,CAAC,CAACV,IAAI,KAAK,SAAS,CAAC;AAGvE,MAAMoY,aAAa,GAAUpW,IAAqB,IAAiCA,IAAI,CAAChC,IAAI,KAAK,SAAS;AAG1G,MAAM8J,aAAa,IAAU9H,IAAqB,GAAiCA,IAAI,CAAChC,IAAI,KAAK,SAAS;AAG1G,MAAMqY,iBAAiB,IAAUrW,IAAqB,IAAa;IACxE,OAAQA,IAAI,CAAChC,IAAI;QACf,KAAKhC,OAAO,CAAC0D,4KAAU;YACrB,sKAAOlE,aAAa,CAAC8a,EAAAA,AAAa,EAACtW,IAAI,CAACxD,qBAAqB,CAAC;QAChE,4KAAKR,OAAO,CAAC6D,KAAU;YACrB,OAAO,KAAK;IAChB;AACF,CAAC;AAGM,MAAM0W,MAAM,GAAA,WAAA,OAAGnc,yJAAAA,AAAI,EAGxB,CAAC,EAAE,CAAW4F,IAAqB,EAAExB,KAAS,KAAsB;IACpE,OAAQwB,IAAI,CAAChC,IAAI;QACf,4KAAKhC,OAAO,CAAC0D,KAAU;YAAE;gBACvB,OAAO8E,aAAa,CAACxE,IAAI,CAACxD,qBAAqB,CAAC;YAClD;QACA,4KAAKR,OAAO,CAAC6D,KAAU;YAAE;gBACvB,OAAO4E,WAAW,CAACjG,KAAK,CAAqB;YAC/C;IACF;AACF,CAAC,CAAC;AAGK,MAAMgY,UAAU,IAAUxW,IAAqB,GAAyBuW,MAAM,CAACvW,IAAI,EAAE,KAAK,CAAC,CAAC;AAG5F,MAAMyW,eAAe,IAAUzW,IAAqB,IAAmC;IAC5F,OAAQA,IAAI,CAAChC,IAAI;QACf,4KAAKhC,OAAO,CAAC0D,KAAU;YACrB,OAAO3E,MAAM,CAACsO,oJAAAA,AAAI,EAACrJ,IAAI,CAACxD,qBAAqB,CAAC;QAChD,4KAAKR,OAAO,CAAC6D,KAAU;YACrB,2JAAO9E,MAAM,CAAC6F,AAAI,EAAE;IACxB;AACF,CAAC;AAGM,MAAM8V,cAAc,GAAGA,CAC5BC,KAAgC,EAChClR,OAEC,GAEDmR,sBAAsB,CAACD,KAAK,EAAElR,OAAO,EAAEoR,QAAQ,6JAAGrb,YAAsB,CAAT,CAACqb,4JAAWrb,aAAa,AAAW,CAAVoE,AAAW;AAG/F,MAAMkX,OAAO,IAAIlT,MAAe,GACrCY,aAAa,gKAAChJ,MAAcmI,AAAG,EAACC,KAAL,CAACD,AAAU,CAAC,CAAqB;AAGvD,MAAMoT,UAAU,GAAA,WAAA,IAKnB3c,4JAAAA,AAAI,EAAC,CAAC,EAAE,CAAoB4F,IAAqB,EAAEgX,UAA4B,KAA0B;IAC3G,OAAQhX,IAAI,CAAChC,IAAI;QACf,4KAAKhC,OAAO,CAAC0D,KAAU;YACrB,OAAO,KAAK;QACd,4KAAK1D,OAAO,CAAC6D,KAAU;YACrB,OAAOmX,UAAU,CAAChX,IAAI,CAACxD,qBAAqB,CAAC;IACjD;AACF,CAAC,CAAC;AAGK,MAAMya,QAAQ,IAAOtS,KAAQ,GAClCH,aAAa,gKAAChJ,OAAckJ,AAAI,EAACC,IAAN,CAACD,AAAU,CAAC,CAAwB;AAG1D,MAAMF,aAAa,GAAOlG,KAAqB,IAAyB;IAC7E,MAAMhC,MAAM,GAAG,IAAIyB,sBAAsB,wKAAC/B,OAAO,CAAC0D,KAAU,CAAQ;IACpEpD,MAAM,CAACE,qBAAqB,GAAG8B,KAAK;IACpC,OAAOhC,MAAM;AACf,CAAC;AAGM,MAAM4a,WAAW,GAAA,WAAA,GAAG9c,6JAAAA,AAAI,EAG7B,CAAC,EAAE,CAAe4F,IAAqB,EAAE8B,CAA8B,KAA2B;IAClG,OAAQ9B,IAAI,CAAChC,IAAI;QACf,4KAAKhC,OAAO,CAAC0D,KAAU;YAAE;gBACvB,OAAO8E,aAAa,CAACxE,IAAI,CAACxD,qBAAqB,CAAC;YAClD;QACA,4KAAKR,OAAO,CAAC6D,KAAU;YAAE;gBACvB,OAAOiC,CAAC,CAAC9B,IAAI,CAACxD,qBAAqB,CAAC;YACtC;IACF;AACF,CAAC,CAAC;AAGK,MAAM2a,iBAAiB,GAAA,WAAA,yJAQ1B/c,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAAqB,EACrB8B,CAAmD,KACT;IAC1C,OAAQ9B,IAAI,CAAChC,IAAI;QACf,4KAAKhC,OAAO,CAAC0D,KAAU;YAAE;gBACvB,OAAOO,OAAO,CAACuE,aAAa,CAACxE,IAAI,CAACxD,qBAAqB,CAAC,CAAC;YAC3D;QACA,4KAAKR,OAAO,CAAC6D,KAAU;YAAE;gBACvB,OAAOiC,CAAC,CAAC9B,IAAI,CAACxD,qBAAqB,CAAC;YACtC;IACF;AACF,CAAC,CAAC;AAGK,MAAM4a,WAAW,GACtBpX,IAAoC,0JACX1F,OAAAA,AAAI,EAAC0F,IAAI,EAAEkX,WAAW,mJAAC7c,WAAQ,CAAC,CAAC;AAGrD,MAAMgd,iBAAiB,GAAA,WAAA,yJAQ1Bjd,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAAqB,EACrB8B,CAAoC,KACa;IACjD,OAAQ9B,IAAI,CAAChC,IAAI;QACf,4KAAKhC,OAAO,CAAC0D,KAAU;YAAE;gBACvB,OAAOO,OAAO,CAACuE,aAAa,CAACxE,IAAI,CAACxD,qBAAqB,CAAC,CAAC;YAC3D;QACA,4KAAKR,OAAO,CAAC6D,KAAU;YAAE;gBACvB,OAAOP,IAAI,CAACwC,CAAC,CAAC9B,IAAI,CAACxD,qBAAqB,CAAC,CAAC;YAC5C;IACF;AACF,CAAC,CAAC;AAGK,MAAM8a,cAAc,IAAUlV,MAA2B,IAAqB;IACnF,OAAQA,MAAM,CAACpE,IAAI;QACjB,KAAK,MAAM;YACT,OAAOiZ,QAAQ,CAAC7U,MAAM,CAACE,IAAI,CAAC;QAC9B,KAAK,OAAO;YACV,OAAOmC,WAAW,CAACrC,MAAM,CAACG,KAAK,CAAC;IACpC;AACF,CAAC;AAGM,MAAMgV,cAAc,IAAOhE,MAAwB,IAAwB;IAChF,OAAQA,MAAM,CAACvV,IAAI;QACjB,KAAK,MAAM;YACT,OAAOiZ,QAAQ,CAAC,KAAK,CAAC,CAAC;QACzB,KAAK,MAAM;YACT,OAAOxS,WAAW,CAAC8O,MAAM,CAAC/U,KAAK,CAAC;IACpC;AACF,CAAC;AAGM,MAAMgZ,aAAa,GAAA,WAAA,yJAAGpd,OAAI,AAAJA,EAG3B,CAAC,EAAE,CAAC4F,IAAI,EAAEuI,MAAM,KAAI;IACpB,OAAQvI,IAAI,CAAChC,IAAI;QACf,4KAAKhC,OAAO,CAAC0D,KAAU;YACrB,OAAO6I,MAAM,CAACvI,IAAI,CAACxD,qBAAqB,CAAC;QAC3C,2KAAKR,OAAO,CAAC6D,MAAU;YACrB,OAAOG,IAAI,CAACxD,qBAAqB;IACrC;AACF,CAAC,CAAC;AAGK,MAAMib,aAAa,IAAI5S,OAAwB,GACpDL,aAAa,gKAAChJ,YAAcqL,AAAS,CAAV,CAACA,AAAUhC,OAAO,CAAC,CAAC;AAG1C,MAAM6S,OAAO,GAAA,WAAA,wJAAGtd,QAAAA,AAAI,EAGzB,CAAC,EAAE,CAAC4F,IAAI,EAAE8B,CAAC,KAAI;IACf,OAAQ9B,IAAI,CAAChC,IAAI;QACf,4KAAKhC,OAAO,CAAC0D,KAAU;YACrB,OAAO8E,aAAa,CAACxE,IAAI,CAACxD,qBAAqB,CAAC;QAClD,KAAKR,OAAO,CAAC6D,4KAAU;YACrB,OAAO4E,WAAW,CAAC3C,CAAC,CAAC9B,IAAI,CAACxD,qBAAqB,CAAC,CAAC;IACrD;AACF,CAAC,CAAC;AAGK,MAAMmb,WAAW,GAAA,WAAA,OAAGvd,yJAAAA,AAAI,EAc7B,CAAC,EAAE,CAAC4F,IAAI,EAAE,EAAEP,SAAS,EAAEK,SAAAA,EAAW,KAAI;IACtC,OAAQE,IAAI,CAAChC,IAAI;QACf,2KAAKhC,OAAO,CAAC0D,MAAU;YACrB,OAAO8E,aAAa,uJAAClK,OAAAA,AAAI,EAAC0F,IAAI,CAACxD,qBAAqB,iKAAEhB,MAAcgM,AAAG,EAAC/H,KAAL,CAAC+H,GAAa,CAAC,CAAC,CAAC;QACtF,KAAKxL,OAAO,CAAC6D,4KAAU;YACrB,OAAO4E,WAAW,CAAC3E,SAAS,CAACE,IAAI,CAACxD,qBAAqB,CAAC,CAAC;IAC7D;AACF,CAAC,CAAC;AAGK,MAAMob,YAAY,GAAA,WAAA,IAAGxd,4JAAAA,AAAI,EAG9B,CAAC,EAAE,CAAC4F,IAAI,EAAE8B,CAAC,KAAI;IACf,OAAQ9B,IAAI,CAAChC,IAAI;QACf,4KAAKhC,OAAO,CAAC0D,KAAU;YACrB,OAAO8E,aAAa,uJAAClK,OAAAA,AAAI,EAAC0F,IAAI,CAACxD,qBAAqB,GAAEhB,aAAa,CAACgM,sJAAG,AAAHA,EAAI1F,CAAC,CAAC,CAAC,CAAC;QAC9E,4KAAK9F,OAAO,CAAC6D,KAAU;YACrB,OAAO4E,WAAW,CAACzE,IAAI,CAACxD,qBAAqB,CAAC;IAClD;AACF,CAAC,CAAC;AAGK,MAAMqb,iBAAiB,GAAA,WAAA,yJAAGzd,OAAAA,AAAI,EAGnC,CAAC,EAAE,CAAC4F,IAAI,EAAE8B,CAAC,KAAI;IACf,OAAQ9B,IAAI,CAAChC,IAAI;QACf,4KAAKhC,OAAO,CAAC0D,KAAU;YACrB,OAAO8E,aAAa,CAAC1C,CAAC,CAAC9B,IAAI,CAACxD,qBAAqB,CAAC,CAAC;QACrD,4KAAKR,OAAO,CAAC6D,KAAU;YACrB,OAAO4E,WAAW,CAACzE,IAAI,CAACxD,qBAAqB,CAAC;IAClD;AACF,CAAC,CAAC;AAGK,MAAM4L,SAAS,GAAA,WAAA,yJAAGhO,OAAAA,AAAI,EAS3B,CAAC,EAAE,CAAC4F,IAAI,EAAE,EAAEP,SAAS,EAAEK,SAAAA,EAAW,KAAI;IACtC,OAAQE,IAAI,CAAChC,IAAI;QACf,2KAAKhC,OAAO,CAAC0D,MAAU;YACrB,OAAOD,SAAS,CAACO,IAAI,CAACxD,qBAAqB,CAAC;QAC9C,KAAKR,OAAO,CAAC6D,4KAAU;YACrB,OAAOC,SAAS,CAACE,IAAI,CAACxD,qBAAqB,CAAC;IAChD;AACF,CAAC,CAAC;AAGK,MAAMsb,eAAe,GAAA,WAAA,yJAAG1d,OAAAA,AAAI,EAcjC,CAAC,EAAE,CAAC4F,IAAI,EAAE,EAAEP,SAAS,EAAEK,SAAAA,EAAW,KAAI;IACtC,OAAQE,IAAI,CAAChC,IAAI;QACf,4KAAKhC,OAAO,CAAC0D,KAAU;YACrB,OAAOD,SAAS,CAACO,IAAI,CAACxD,qBAAqB,CAAC;QAC9C,4KAAKR,OAAO,CAAC6D,KAAU;YACrB,OAAOC,SAAS,CAACE,IAAI,CAACxD,qBAAqB,CAAC;IAChD;AACF,CAAC,CAAC;AAGK,MAAMiI,WAAW,GAAOjG,KAAQ,IAAkB;IACvD,MAAMlC,MAAM,GAAG,IAAIiC,sBAAsB,wKAACvC,OAAO,CAAC6D,KAAU,CAAQ;IACpEvD,MAAM,CAACE,qBAAqB,GAAGgC,KAAK;IACpC,OAAOlC,MAAM;AACf,CAAC;AAGM,MAAMyb,QAAQ,GAAA,WAAA,GAAoBtT,WAAW,CAAC,KAAK,CAAC,CAAC;AAGrD,MAAMuT,OAAO,GAAA,WAAA,yJAAG5d,OAAAA,AAAI,EAGzB,CAAC,EAAE,CAAC4F,IAAI,EAAExC,IAAI,GACdya,WAAW,CAACjY,IAAI,EAAExC,IAAI,EAAE;QACtBsC,SAAS,EAAEA,CAACT,CAAC,EAAE6Y,EAAE,GAAK;gBAAC7Y,CAAC;gBAAE6Y,EAAE;aAAC;QAC7BzY,SAAS,6JAAEjE,aAAa,AAACoE,CAAAA;KAC1B,CAAC,CAAC;AAGE,MAAMuY,WAAW,GAAA,WAAA,OAAG/d,yJAAAA,AAAI,EAG7B,CAAC,EAAE,CAAC4F,IAAI,EAAExC,IAAI,GACdya,WAAW,CAACjY,IAAI,EAAExC,IAAI,EAAE;QACtBsC,SAAS,EAAEA,CAACT,CAAC,EAAE4B,CAAC,GAAK5B,CAAC;QACtBI,SAAS,6JAAEjE,aAAa,AAACoE,CAAAA;KAC1B,CAAC,CAAC;AAGE,MAAMwY,YAAY,GAAA,WAAA,GAAGhe,6JAAAA,AAAI,EAG9B,CAAC,EAAE,CAAC4F,IAAI,EAAExC,IAAI,GACdya,WAAW,CAACjY,IAAI,EAAExC,IAAI,EAAE;QACtBsC,SAAS,EAAEA,CAACmB,CAAC,EAAEiX,EAAE,GAAKA,EAAE;QACxBzY,SAAS,6JAAEjE,aAAa,AAACoE,CAAAA;KAC1B,CAAC,CAAC;AAGE,MAAMyY,UAAU,GAAA,WAAA,yJAAGje,OAAI,AAAJA,EAGxB,CAAC,EAAE,CAAC4F,IAAI,EAAExC,IAAI,GACdya,WAAW,CAACjY,IAAI,EAAExC,IAAI,EAAE;QACtBsC,SAAS,EAAEA,CAACT,CAAC,EAAE6Y,EAAE,GAAK;gBAAC7Y,CAAC;gBAAE6Y,EAAE;aAAC;QAC7BzY,SAAS,6JAAEjE,WAAcqb,EAAD,CAACA;KAC1B,CAAC,CAAC;AAGE,MAAMyB,cAAc,GAAA,WAAA,yJAAGle,OAAAA,AAAI,EAGhC,CAAC,EAAE,CAAC4F,IAAI,EAAExC,IAAI,GACdya,WAAW,CAACjY,IAAI,EAAExC,IAAI,EAAE;QACtBsC,SAAS,EAAEA,CAACT,CAAC,EAAE4B,CAAC,GAAK5B,CAAC;QACtBI,SAAS,6JAAEjE,WAAcqb,EAAD,CAACA;KAC1B,CAAC,CAAC;AAGE,MAAM0B,eAAe,GAAA,WAAA,yJAAGne,OAAI,AAAJA,EAG7B,CAAC,EAAE,CAAC4F,IAAI,EAAExC,IAAI,GACdya,WAAW,CAACjY,IAAI,EAAExC,IAAI,EAAE;QACtBsC,SAAS,EAAEA,CAACmB,CAAC,EAAEiX,EAAE,GAAKA,EAAE;QACxBzY,SAAS,EAAEjE,aAAa,CAACqb,wJAAAA;KAC1B,CAAC,CAAC;AAGE,MAAMoB,WAAW,GAAA,WAAA,yJAAG7d,OAAAA,AAAI,EAgB7B,CAAC,EAAE,CACH4F,IAAI,EACJxC,IAAI,EACJ,EAAEiC,SAAS,EAAEK,SAAAA,EAAW,KACtB;IACF,OAAQE,IAAI,CAAChC,IAAI;QACf,KAAKhC,OAAO,CAAC0D,4KAAU;YAAE;gBACvB,OAAQlC,IAAI,CAACQ,IAAI;oBACf,4KAAKhC,OAAO,CAAC6D,KAAU;wBACrB,OAAO2E,aAAa,CAACxE,IAAI,CAACxD,qBAAqB,CAAC;oBAClD,4KAAKR,OAAO,CAAC0D,KAAU;wBAAE;4BACvB,OAAO8E,aAAa,CAAC/E,SAAS,CAACO,IAAI,CAACxD,qBAAqB,EAAEgB,IAAI,CAAChB,qBAAqB,CAAC,CAAC;wBACzF;gBACF;YACF;QACA,4KAAKR,OAAO,CAAC6D,KAAU;YAAE;gBACvB,OAAQrC,IAAI,CAACQ,IAAI;oBACf,4KAAKhC,OAAO,CAAC6D,KAAU;wBACrB,OAAO4E,WAAW,CAAC3E,SAAS,CAACE,IAAI,CAACxD,qBAAqB,EAAEgB,IAAI,CAAChB,qBAAqB,CAAC,CAAC;oBACvF,4KAAKR,OAAO,CAAC0D,KAAU;wBACrB,OAAO8E,aAAa,CAAChH,IAAI,CAAChB,qBAAqB,CAAC;gBACpD;YACF;IACF;AACF,CAAC,CAAC;AAEF,MAAMoa,sBAAsB,GAAGA,CAC7BD,KAAgC,EAChC6B,aAAiF,KACxC;IACzC,MAAMC,IAAI,sJAAG3e,KAAK,CAACmM,SAAAA,AAAY,EAAC0Q,KAAK,CAAC;IACtC,IAAI,CAAC7c,KAAK,CAAC4e,0JAAAA,AAAU,EAACD,IAAI,CAAC,EAAE;QAC3B,2JAAO1d,MAAM,CAAK,AAAJ6F,EAAM;IACtB;IACA,6JAAOtG,OAAAA,AAAI,qJACTR,KAAK,CAAC6e,SAAAA,AAAY,EAACF,IAAI,CAAC,MACxB5e,GAAG,CAAC6V,oJAAAA,AAAM,wJACRpV,OAAAA,AAAI,qJAACR,KAAK,CAAC8e,SAAAA,AAAY,EAACH,IAAI,CAAC,EAAEf,OAAO,gJAAoB5d,KAAK,AAAG,CAAF+e,AAAG,CAAC,EACpE,CAACC,WAAW,EAAEC,OAAO,yJACnBze,OAAAA,AAAI,EACFwe,WAAW,EACXb,WAAW,CAACc,OAAO,EAAE;YACnBjZ,SAAS,EAAEA,CAAC2Y,IAAI,EAAEja,KAAK,yJAAKlE,OAAI,AAAJA,EAAKme,IAAI,EAAE3e,KAAK,CAACkf,uJAAAA,AAAO,EAACxa,KAAK,CAAC,CAAC;YAC5DiB,SAAS,EAAE+Y;SACZ,CAAC,CACH,CACJ,EACDd,OAAO,gJAAC5d,KAAK,CAACmf,IAAO,CAAC,EACtBvB,OAAO,EAAEwB,KAAK,OAAKpf,KAAK,CAACqf,2JAAAA,AAAe,EAACD,KAAK,CAAa,CAAC,kJAC5Dne,MAAM,CAACsO,AAAI,CACZ;AACH,CAAC;AAOM,MAAM+P,kBAAkB,IAAkBvU,OAAwB,IAA6B;IACpG,MAAMwU,SAAS,GAAG;QAChB,mKAAG1d,kBAAe;QAClB,CAACF,QAAQ,CAAC6d,sKAAc,CAAA,gKAAG7d,QAAQ,CAAC8d,UAAgB;QACpDzU,KAAK,0JAAEhK,OAAWyU,AAAI,GAAL,CAACA,gKAAK9T,QAAQ,CAAC+d,CAAAA,AAAO,EAAO,EAAE,CAAC,CAAC;QAClDjZ,MAAMA,CAAA;YACJ,OAAOkZ,aAAa,CAAC,IAAI,CAAC;QAC5B,CAAC;QACD9Y,UAAU,EAAEkE;KACb;IACD,OAAOwU,SAAS;AAClB,CAAC;AAGM,MAAMK,YAAY,GAAGA,CAAA,GAC1Bta,OAAO,CAACyF,OAAO,EAAGE,EAAE,IAAK4U,cAAc,CAAO5U,EAAE,CAAC,CAAC;AAG7C,MAAM4U,cAAc,IAAkB9U,OAAwB,GACnER,IAAI,CAAC,IAAM+U,kBAAkB,CAAOvU,OAAO,CAAC,CAAC;AAGxC,MAAM4U,aAAa,IAAUzZ,IAA6B,GAC/DmB,cAAc,EAAQJ,MAAM,IAAI;QAC9B,MAAM+D,KAAK,IAAGhK,UAAU,CAAC4I,kJAAAA,AAAG,EAAC1D,IAAI,CAAC8E,KAAK,CAAC;QACxC,OAAQA,KAAK,CAAC9G,IAAI;YAChB,8KAAKjC,eAAe,CAAC6d,AAAa;gBAAE;oBAClC,OAAO7Y,MAAM,CAAC+D,KAAK,CAACxI,MAAM,CAAC;gBAC7B;YACA,8KAAKP,eAAe,CAAC8d,GAAgB;gBAAE;oBACrC,oDAAA;oBACA/U,KAAK,CAACgV,OAAO,CAACC,IAAI,CAAChZ,MAAM,CAAC;oBAC1B,OAAOiZ,uBAAuB,CAACha,IAAI,EAAEe,MAAM,CAAC;gBAC9C;QACF;IACF,CAAC,EAAEf,IAAI,CAACW,UAAU,CAAC;AAGd,MAAMsZ,gBAAgB,GAAA,WAAA,GAGzB7f,6JAAAA,AAAI,EACN,CAAC,EACD,CAAO4F,IAA6B,EAAE1D,MAA2B,GAC/DgL,YAAY,CAAChL,MAAM,EAAE0D,IAAI,CAAC,CAC7B;AAGM,MAAMka,oBAAoB,GAAA,WAAA,GAAG9f,6JAAAA,AAAI,EAGtC,CAAC,EAAE,CAAC4F,IAAI,EAAE1D,MAAM,GAChB+H,IAAI,CAAC,MAAK;QACR,MAAMS,KAAK,2JAAGhK,MAAc,AAAH4I,EAAI1D,EAAL,CAAC0D,CAAQ,CAACoB,KAAK,CAAC;QACxC,OAAQA,KAAK,CAAC9G,IAAI;YAChB,8KAAKjC,eAAe,CAAC6d,AAAa;gBAAE;oBAClC,OAAO,KAAK;gBACd;YACA,8KAAK7d,eAAe,CAAC8d,GAAgB;gBAAE;wBACrC/e,UAAU,CAACqf,+IAAAA,AAAG,EAACna,IAAI,CAAC8E,KAAK,oKAAErJ,OAAS2e,AAAI,CAAL,CAACA,AAAK9d,MAAM,CAAC,CAAC;oBACjD,IAAK,IAAI8J,CAAC,GAAG,CAAC,EAAEiU,GAAG,GAAGvV,KAAK,CAACgV,OAAO,CAACxZ,MAAM,EAAE8F,CAAC,GAAGiU,GAAG,EAAEjU,CAAC,EAAE,CAAE;wBACxDtB,KAAK,CAACgV,OAAO,CAAC1T,CAAC,CAAC,CAAC9J,MAAM,CAAC;oBAC1B;oBACA,OAAO,IAAI;gBACb;QACF;IACF,CAAC,CAAC,CAAC;AAGE,MAAMiL,YAAY,GAAA,WAAA,yJAAGnN,OAAAA,AAAI,EAG9B,CAAC,EAAE,CAAC4F,IAAI,EAAEV,IAAI,GAAK4a,oBAAoB,CAACla,IAAI,EAAEV,IAAI,CAAC,CAAC;AAG/C,MAAMgb,YAAY,GAAA,WAAA,yJAAGlgB,OAAAA,AAAI,EAG9B,CAAC,EAAE,CAAC4F,IAAI,EAAE2E,KAAK,GAAKuV,oBAAoB,CAACla,IAAI,EAAE0E,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC;AAGvD,MAAM4V,gBAAgB,GAAA,WAAA,yJAAGngB,OAAAA,AAAI,EAGlC,CAAC,EAAE,CAAC4F,IAAI,EAAEoE,QAAQ,GAAK8V,oBAAoB,CAACla,IAAI,EAAE4E,QAAQ,CAACR,QAAQ,CAAC,CAAC,CAAC;AAGjE,MAAMoW,iBAAiB,GAAA,WAAA,yJAAGpgB,OAAAA,AAAI,EAGnC,CAAC,EAAE,CAAC4F,IAAI,EAAE1B,KAAK,GAAK4b,oBAAoB,CAACla,IAAI,EAAEL,SAAS,CAACrB,KAAK,CAAC,CAAC,CAAC;AAG5D,MAAMmc,qBAAqB,GAAA,WAAA,yJAAGrgB,OAAAA,AAAI,EAGvC,CAAC,EAAE,CAAC4F,IAAI,EAAEoE,QAAQ,GAAK8V,oBAAoB,CAACla,IAAI,EAAEiE,aAAa,CAACG,QAAQ,CAAC,CAAC,CAAC;AAGtE,MAAMsW,WAAW,GAAA,WAAA,wJAAGtgB,QAAAA,AAAI,EAG7B,CAAC,EAAE,CAAC4F,IAAI,EAAE4D,MAAM,GAAKsW,oBAAoB,CAACla,IAAI,EAAE2D,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC;AAGxD,MAAM+W,eAAe,GAAA,WAAA,yJAAGvgB,OAAAA,AAAI,EAGjC,CAAC,EAAE,CAAC4F,IAAI,EAAEoE,QAAQ,GAAK8V,oBAAoB,CAACla,IAAI,EAAEmE,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;AAGhE,MAAMwW,iBAAiB,IAAU5a,IAA6B,GACnEZ,OAAO,CAACyF,OAAO,GAAGA,OAAO,GAAKqV,oBAAoB,CAACla,IAAI,EAAE8G,aAAa,CAACjC,OAAO,CAAC,CAAC,CAAC;AAG5E,MAAMgW,qBAAqB,GAAA,WAAA,yJAAGzgB,OAAAA,AAAI,EAGvC,CAAC,EAAE,CAAC4F,IAAI,EAAE6E,OAAO,GAAKqV,oBAAoB,CAACla,IAAI,EAAE8G,aAAa,CAACjC,OAAO,CAAC,CAAC,CAAC;AAGpE,MAAMiW,cAAc,IAAU9a,IAA6B,GAChEqE,IAAI,CAAC,4JAAMvJ,MAAW4I,AAAG,EAAC1D,EAAL,CAAC0D,CAAQ,CAACoB,KAAK,CAAC,CAAC9G,IAAI,8KAAKjC,eAAe,CAAC6d,AAAa,CAAC;AAGxE,MAAMmB,YAAY,IACvB/a,IAA6B,GAE7BqE,IAAI,CAAC,MAAK;QACR,MAAMS,KAAK,GAAGhK,UAAU,CAAC4I,mJAAAA,AAAG,EAAC1D,IAAI,CAAC8E,KAAK,CAAC;QACxC,OAAQA,KAAK,CAAC9G,IAAI;YAChB,8KAAKjC,eAAe,CAAC6d,AAAa;gBAAE;oBAClC,QAAO7e,MAAM,CAACsO,mJAAAA,AAAI,EAACvE,KAAK,CAACxI,MAAM,CAAC;gBAClC;YACA,8KAAKP,eAAe,CAAC8d,GAAgB;gBAAE;oBACrC,2JAAO9e,MAAM,CAAC6F,AAAI,EAAE;gBACtB;QACF;IACF,CAAC,CAAC;AAGG,MAAMoa,eAAe,GAAA,WAAA,yJAAG5gB,OAAAA,AAAI,EAGjC,CAAC,EAAE,CAAC4F,IAAI,EAAExB,KAAK,GAAK0b,oBAAoB,CAACla,IAAI,EAAEC,OAAO,CAACzB,KAAK,CAAC,CAAC,CAAC;AAG1D,MAAMyc,YAAY,GAAA,WAAA,OAAG7gB,yJAAAA,AAAI,EAG9B,CAAC,EAAE,CAAC4F,IAAI,EAAEoE,QAAQ,GAAK8V,oBAAoB,CAACla,IAAI,EAAEqE,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC;AAG7D,MAAM8W,kBAAkB,GAAGA,CAAOlb,IAA6B,EAAE1D,MAA2B,KAAU;IAC3G,MAAMwI,KAAK,2JAAGhK,MAAW4I,AAAG,EAAC1D,EAAL,CAAC0D,CAAQ,CAACoB,KAAK,CAAC;IACxC,IAAIA,KAAK,CAAC9G,IAAI,8KAAKjC,eAAe,CAAC8d,GAAgB,EAAE;QACnD/e,UAAU,CAACqf,mJAAAA,AAAG,EAACna,IAAI,CAAC8E,KAAK,oKAAErJ,OAAS2e,AAAI,CAAL,CAACA,AAAK9d,MAAM,CAAC,CAAC;QACjD,IAAK,IAAI8J,CAAC,GAAG,CAAC,EAAEiU,GAAG,GAAGvV,KAAK,CAACgV,OAAO,CAACxZ,MAAM,EAAE8F,CAAC,GAAGiU,GAAG,EAAEjU,CAAC,EAAE,CAAE;YACxDtB,KAAK,CAACgV,OAAO,CAAC1T,CAAC,CAAC,CAAC9J,MAAM,CAAC;QAC1B;IACF;AACF,CAAC;AAED,MAAM0d,uBAAuB,GAAGA,CAC9Bha,IAA6B,EAC7Bmb,MAA6C,GAE7C9W,IAAI,CAAC,MAAK;QACR,MAAMS,KAAK,2JAAGhK,MAAW4I,AAAG,EAAC1D,EAAL,CAAC0D,CAAQ,CAACoB,KAAK,CAAC;QACxC,IAAIA,KAAK,CAAC9G,IAAI,8KAAKjC,eAAe,CAAC8d,GAAgB,EAAE;YACnD,MAAMuB,KAAK,GAAGtW,KAAK,CAACgV,OAAO,CAACuB,OAAO,CAACF,MAAM,CAAC;YAC3C,IAAIC,KAAK,IAAI,CAAC,EAAE;gBACd,sDAAA;gBACAtW,KAAK,CAACgV,OAAO,CAACwB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;YAChC;QACF;IACF,CAAC,CAAC;AAEJ,gFAAA;AACA,UAAA;AACA,gFAAA;AAEA,MAAMG,YAAY,GAAA,WAAA,GAAG5c,gBAAgB,EAAEkF,KAAK,GAAKY,WAAW,CAACZ,KAAK,CAAC6N,cAAc,CAAC,CAAC;AAG5E,MAAM8J,OAAO,GAAGA,CAAA,GAAsDD,YAAmB;AAGzF,MAAME,WAAW,IACtB3Z,CAAsC,GACN0F,GAAG,CAACgU,OAAO,EAAM,EAAE1Z,CAAC,CAAC;AAGhD,MAAM4Z,iBAAiB,GAC5B5Z,CAA2D,IAC3B1C,OAAO,CAACoc,OAAO,EAAM,EAAE1Z,CAAC,CAAC;AAGpD,MAAM6Z,cAAc,GAAA,WAAA,yJAAGvhB,OAAAA,AAAI,EAGhC,CAAC,EAAE,CAAU4F,IAA4B,EAAEwb,OAA2B,GACtEpS,eAAe,CACbsI,cAAc,EACd8J,OAAO,CACR,CAACxb,IAA2B,CAAC,CAAC;AAG1B,MAAM4b,kBAAkB,GAAA,WAAA,yJAAGxhB,OAAAA,AAAI,EAGpC,CAAC,EAAE,CAAc4F,IAA6B,EAAEwb,OAA2B,GAC3EjL,mBAAmB,CACjBmB,cAAc,GACba,MAAM,wJAAKxY,OAAO,CAAC8hB,AAAK,AAALA,EAAMtJ,MAAM,EAAEiJ,OAAO,CAAC,CAC3C,CAACxb,IAA2B,CAAC,CAAC;AAG1B,MAAM8b,eAAe,GAAA,WAAA,GAAG1hB,6JAAAA,AAAI,EAQjC,CAAC,EAAE,CACH4F,IAA4B,EAC5B8B,CAAuD,GACpD4Z,iBAAiB,EAAEF,OAA4B,GAAKG,cAAc,CAAC3b,IAAI,EAAE8B,CAAC,CAAC0Z,OAAO,CAAC,CAAC,CAAC,CAAC;AAOpF,MAAMO,kBAAkB,GAAA,WAAA,yJAc3B3hB,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5ByF,OAGC,GAEDrG,OAAO,CACLY,IAAI,GACHX,CAAC,GACAD,OAAO,CACLqG,OAAO,CAACtD,SAAS,CAAC9C,CAAC,CAAC,GACnB2c,IAAI,GAAoCA,IAAI,GAAG/b,OAAO,CAACZ,CAAC,CAAC,GAAGoG,OAAO,CAAC8C,MAAM,CAAClJ,CAAC,CAAC,CAC/E,CACJ,CAAC;AAGG,MAAM4c,kBAAkB,GAAA,WAAA,yJAc3B7hB,OAAAA,AAAI,EAAC,CAAC,EAAE,CACV4F,IAA4B,EAC5ByF,OAGC,GAEDsW,kBAAkB,CAAC/b,IAAI,EAAE;QACvBmC,SAAS,EAAEsD,OAAO,CAACtD,SAAS;QAC5BoG,MAAM,GAAGlJ,CAAC,GAAKqF,IAAI,CAACe,OAAO,CAACyW,UAAU,CAAC7c,CAAC,CAAC;KAC1C,CAAC,CAAC;AAOE,MAAMyE,oBAAoB,IAAUD,KAA+B,IAAgC;IACxG,MAAMV,IAAI,GAAGU,KAAK,CAACsY,WAAW;IAC9B,OAAOhZ,IAAI,KAAK/F,SAAS,IAAI+F,IAAI,CAACnF,IAAI,KAAK,MAAM,uJAAGjD,MAAM,CAAK,AAAJsO,EAAKlG,IAAI,CAAC,uJAAGpI,MAAM,CAAC6F,AAAI,EAAE;AACvF,CAAC;AAED,MAAMwb,aAAa,GAAqD;IACtEpe,IAAI,EAAE,MAAM;IACZqe,MAAM,EAAE,MAAM;IACdC,OAAO,EAAE,MAAM;IACfC,OAAO,EAAE,KAAK;IACd3Z,MAAM,EAAE;QACN5E,IAAI,EAAE,OAAO;QACbwe,SAAS,EAAA,WAAA,GAAEC,MAAM,CAAC,CAAC,CAAC;QACpBC,OAAO,EAAA,WAAA,GAAED,MAAM,CAAC,CAAC,CAAC;QAClBnd,IAAI,EAAEyY;KACP;IACD4E,UAAU,EAAA,WAAA,GAAE,IAAIC,GAAG,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,UAAU;IAChBC,SAASA,CAAA,IAAI,CAAC;IACdC,KAAKA,CAAA,IAAI,CAAC;IACVC,GAAGA,CAAA,IAAI,CAAC;IACRC,QAAQA,CAAA,IAAI;CACb;AAGM,MAAMC,QAAQ,IAAI1X,OAIxB,GAAkB2E,MAAM,CAACmK,MAAM,CAACnK,MAAM,CAACgT,MAAM,CAAChB,aAAa,CAAC,EAAE3W,OAAO,CAAC", "ignoreList": [0], "debugId": null}}]}