{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/node-fetch-native/dist/chunks/multipart-parser.mjs"], "sourcesContent": ["var U=Object.defineProperty;var E=(_,o)=>U(_,\"name\",{value:o,configurable:!0});import\"node:fs\";import\"node:path\";import{FormData as w,File as B}from\"../node.mjs\";import\"node:http\";import\"node:https\";import\"node:zlib\";import\"node:stream\";import\"node:buffer\";import\"node:util\";import\"../shared/node-fetch-native.DfbY2q-x.mjs\";import\"node:url\";import\"node:net\";let D=0;const t={START_BOUNDARY:D++,HEADER_FIELD_START:D++,HEADER_FIELD:D++,HEADER_VALUE_START:D++,HEADER_VALUE:D++,HEADER_VALUE_ALMOST_DONE:D++,HEADERS_ALMOST_DONE:D++,PART_DATA_START:D++,PART_DATA:D++,END:D++};let F=1;const u={PART_BOUNDARY:F,LAST_BOUNDARY:F*=2},g=10,N=13,V=32,S=45,Y=58,x=97,C=122,I=E(_=>_|32,\"lower\"),p=E(()=>{},\"noop\");class M{static{E(this,\"MultipartParser\")}constructor(o){this.index=0,this.flags=0,this.onHeaderEnd=p,this.onHeaderField=p,this.onHeadersEnd=p,this.onHeaderValue=p,this.onPartBegin=p,this.onPartData=p,this.onPartEnd=p,this.boundaryChars={},o=`\\r\n--`+o;const n=new Uint8Array(o.length);for(let r=0;r<o.length;r++)n[r]=o.charCodeAt(r),this.boundaryChars[n[r]]=!0;this.boundary=n,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=t.START_BOUNDARY}write(o){let n=0;const r=o.length;let d=this.index,{lookbehind:l,boundary:c,boundaryChars:m,index:e,state:i,flags:A}=this;const H=this.boundary.length,O=H-1,y=o.length;let a,L;const f=E(h=>{this[h+\"Mark\"]=n},\"mark\"),s=E(h=>{delete this[h+\"Mark\"]},\"clear\"),T=E((h,P,R,k)=>{(P===void 0||P!==R)&&this[h](k&&k.subarray(P,R))},\"callback\"),b=E((h,P)=>{const R=h+\"Mark\";R in this&&(P?(T(h,this[R],n,o),delete this[R]):(T(h,this[R],o.length,o),this[R]=0))},\"dataCallback\");for(n=0;n<r;n++)switch(a=o[n],i){case t.START_BOUNDARY:if(e===c.length-2){if(a===S)A|=u.LAST_BOUNDARY;else if(a!==N)return;e++;break}else if(e-1===c.length-2){if(A&u.LAST_BOUNDARY&&a===S)i=t.END,A=0;else if(!(A&u.LAST_BOUNDARY)&&a===g)e=0,T(\"onPartBegin\"),i=t.HEADER_FIELD_START;else return;break}a!==c[e+2]&&(e=-2),a===c[e+2]&&e++;break;case t.HEADER_FIELD_START:i=t.HEADER_FIELD,f(\"onHeaderField\"),e=0;case t.HEADER_FIELD:if(a===N){s(\"onHeaderField\"),i=t.HEADERS_ALMOST_DONE;break}if(e++,a===S)break;if(a===Y){if(e===1)return;b(\"onHeaderField\",!0),i=t.HEADER_VALUE_START;break}if(L=I(a),L<x||L>C)return;break;case t.HEADER_VALUE_START:if(a===V)break;f(\"onHeaderValue\"),i=t.HEADER_VALUE;case t.HEADER_VALUE:a===N&&(b(\"onHeaderValue\",!0),T(\"onHeaderEnd\"),i=t.HEADER_VALUE_ALMOST_DONE);break;case t.HEADER_VALUE_ALMOST_DONE:if(a!==g)return;i=t.HEADER_FIELD_START;break;case t.HEADERS_ALMOST_DONE:if(a!==g)return;T(\"onHeadersEnd\"),i=t.PART_DATA_START;break;case t.PART_DATA_START:i=t.PART_DATA,f(\"onPartData\");case t.PART_DATA:if(d=e,e===0){for(n+=O;n<y&&!(o[n]in m);)n+=H;n-=O,a=o[n]}if(e<c.length)c[e]===a?(e===0&&b(\"onPartData\",!0),e++):e=0;else if(e===c.length)e++,a===N?A|=u.PART_BOUNDARY:a===S?A|=u.LAST_BOUNDARY:e=0;else if(e-1===c.length)if(A&u.PART_BOUNDARY){if(e=0,a===g){A&=~u.PART_BOUNDARY,T(\"onPartEnd\"),T(\"onPartBegin\"),i=t.HEADER_FIELD_START;break}}else A&u.LAST_BOUNDARY&&a===S?(T(\"onPartEnd\"),i=t.END,A=0):e=0;if(e>0)l[e-1]=a;else if(d>0){const h=new Uint8Array(l.buffer,l.byteOffset,l.byteLength);T(\"onPartData\",0,d,h),d=0,f(\"onPartData\"),n--}break;case t.END:break;default:throw new Error(`Unexpected state entered: ${i}`)}b(\"onHeaderField\"),b(\"onHeaderValue\"),b(\"onPartData\"),this.index=e,this.state=i,this.flags=A}end(){if(this.state===t.HEADER_FIELD_START&&this.index===0||this.state===t.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==t.END)throw new Error(\"MultipartParser.end(): stream ended unexpectedly\")}}function $(_){const o=_.match(/\\bfilename=(\"(.*?)\"|([^()<>@,;:\\\\\"/[\\]?={}\\s\\t]+))($|;\\s)/i);if(!o)return;const n=o[2]||o[3]||\"\";let r=n.slice(n.lastIndexOf(\"\\\\\")+1);return r=r.replace(/%22/g,'\"'),r=r.replace(/&#(\\d{4});/g,(d,l)=>String.fromCharCode(l)),r}E($,\"_fileName\");async function v(_,o){if(!/multipart/i.test(o))throw new TypeError(\"Failed to fetch\");const n=o.match(/boundary=(?:\"([^\"]+)\"|([^;]+))/i);if(!n)throw new TypeError(\"no or bad content-type header, no multipart boundary\");const r=new M(n[1]||n[2]);let d,l,c,m,e,i;const A=[],H=new w,O=E(s=>{c+=f.decode(s,{stream:!0})},\"onPartData\"),y=E(s=>{A.push(s)},\"appendToFile\"),a=E(()=>{const s=new B(A,i,{type:e});H.append(m,s)},\"appendFileToFormData\"),L=E(()=>{H.append(m,c)},\"appendEntryToFormData\"),f=new TextDecoder(\"utf-8\");f.decode(),r.onPartBegin=function(){r.onPartData=O,r.onPartEnd=L,d=\"\",l=\"\",c=\"\",m=\"\",e=\"\",i=null,A.length=0},r.onHeaderField=function(s){d+=f.decode(s,{stream:!0})},r.onHeaderValue=function(s){l+=f.decode(s,{stream:!0})},r.onHeaderEnd=function(){if(l+=f.decode(),d=d.toLowerCase(),d===\"content-disposition\"){const s=l.match(/\\bname=(\"([^\"]*)\"|([^()<>@,;:\\\\\"/[\\]?={}\\s\\t]+))/i);s&&(m=s[2]||s[3]||\"\"),i=$(l),i&&(r.onPartData=y,r.onPartEnd=a)}else d===\"content-type\"&&(e=l);l=\"\",d=\"\"};for await(const s of _)r.write(s);return r.end(),H}E(v,\"toFormData\");export{v as toFormData};\n"], "names": [], "mappings": ";;;AAA+E;AAAgB;AAAkB;AAAiD;AAAkB;AAAmB;AAAkB;AAAoB;AAAoB;AAAkB;AAAiD;AAAiB;AAArV,IAAI,IAAE,OAAO,cAAc;AAAC,IAAI,IAAE,CAAC,GAAE,IAAI,EAAE,GAAE,QAAO;QAAC,OAAM;QAAE,cAAa,CAAC;IAAC;;;;;;;;;;;;;AAA0R,IAAI,IAAE;AAAE,MAAM,IAAE;IAAC,gBAAe;IAAI,oBAAmB;IAAI,cAAa;IAAI,oBAAmB;IAAI,cAAa;IAAI,0BAAyB;IAAI,qBAAoB;IAAI,iBAAgB;IAAI,WAAU;IAAI,KAAI;AAAG;AAAE,IAAI,IAAE;AAAE,MAAM,IAAE;IAAC,eAAc;IAAE,eAAc,KAAG;AAAC,GAAE,IAAE,IAAG,IAAE,IAAG,IAAE,IAAG,IAAE,IAAG,IAAE,IAAG,IAAE,IAAG,IAAE,KAAI,IAAE,EAAE,CAAA,IAAG,IAAE,IAAG,UAAS,IAAE,EAAE,KAAK,GAAE;AAAQ,MAAM;IAAE,MAAM;QAAC,EAAE,IAAI,EAAC;IAAkB,CAAC;IAAA,YAAY,CAAC,CAAC;QAAC,IAAI,CAAC,KAAK,GAAC,GAAE,IAAI,CAAC,KAAK,GAAC,GAAE,IAAI,CAAC,WAAW,GAAC,GAAE,IAAI,CAAC,aAAa,GAAC,GAAE,IAAI,CAAC,YAAY,GAAC,GAAE,IAAI,CAAC,aAAa,GAAC,GAAE,IAAI,CAAC,WAAW,GAAC,GAAE,IAAI,CAAC,UAAU,GAAC,GAAE,IAAI,CAAC,SAAS,GAAC,GAAE,IAAI,CAAC,aAAa,GAAC,CAAC,GAAE,IAAE,CAAC;EAC36B,CAAC,GAAC;QAAE,MAAM,IAAE,IAAI,WAAW,EAAE,MAAM;QAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,CAAC,CAAC,EAAE,GAAC,EAAE,UAAU,CAAC,IAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,GAAC,CAAC;QAAE,IAAI,CAAC,QAAQ,GAAC,GAAE,IAAI,CAAC,UAAU,GAAC,IAAI,WAAW,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC,IAAG,IAAI,CAAC,KAAK,GAAC,EAAE,cAAc;IAAA;IAAC,MAAM,CAAC,EAAC;QAAC,IAAI,IAAE;QAAE,MAAM,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,IAAI,CAAC,KAAK,EAAC,EAAC,YAAW,CAAC,EAAC,UAAS,CAAC,EAAC,eAAc,CAAC,EAAC,OAAM,CAAC,EAAC,OAAM,CAAC,EAAC,OAAM,CAAC,EAAC,GAAC,IAAI;QAAC,MAAM,IAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAC,IAAE,IAAE,GAAE,IAAE,EAAE,MAAM;QAAC,IAAI,GAAE;QAAE,MAAM,IAAE,EAAE,CAAA;YAAI,IAAI,CAAC,IAAE,OAAO,GAAC;QAAC,GAAE,SAAQ,IAAE,EAAE,CAAA;YAAI,OAAO,IAAI,CAAC,IAAE,OAAO;QAAA,GAAE,UAAS,IAAE,EAAE,CAAC,GAAE,GAAE,GAAE;YAAK,CAAC,MAAI,KAAK,KAAG,MAAI,CAAC,KAAG,IAAI,CAAC,EAAE,CAAC,KAAG,EAAE,QAAQ,CAAC,GAAE;QAAG,GAAE,aAAY,IAAE,EAAE,CAAC,GAAE;YAAK,MAAM,IAAE,IAAE;YAAO,KAAK,IAAI,IAAE,CAAC,IAAE,CAAC,EAAE,GAAE,IAAI,CAAC,EAAE,EAAC,GAAE,IAAG,OAAO,IAAI,CAAC,EAAE,IAAE,CAAC,EAAE,GAAE,IAAI,CAAC,EAAE,EAAC,EAAE,MAAM,EAAC,IAAG,IAAI,CAAC,EAAE,GAAC,CAAC,CAAC;QAAC,GAAE;QAAgB,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,OAAO,IAAE,CAAC,CAAC,EAAE,EAAC;YAAG,KAAK,EAAE,cAAc;gBAAC,IAAG,MAAI,EAAE,MAAM,GAAC,GAAE;oBAAC,IAAG,MAAI,GAAE,KAAG,EAAE,aAAa;yBAAM,IAAG,MAAI,GAAE;oBAAO;oBAAI;gBAAK,OAAM,IAAG,IAAE,MAAI,EAAE,MAAM,GAAC,GAAE;oBAAC,IAAG,IAAE,EAAE,aAAa,IAAE,MAAI,GAAE,IAAE,EAAE,GAAG,EAAC,IAAE;yBAAO,IAAG,CAAC,CAAC,IAAE,EAAE,aAAa,KAAG,MAAI,GAAE,IAAE,GAAE,EAAE,gBAAe,IAAE,EAAE,kBAAkB;yBAAM;oBAAO;gBAAK;gBAAC,MAAI,CAAC,CAAC,IAAE,EAAE,IAAE,CAAC,IAAE,CAAC,CAAC,GAAE,MAAI,CAAC,CAAC,IAAE,EAAE,IAAE;gBAAI;YAAM,KAAK,EAAE,kBAAkB;gBAAC,IAAE,EAAE,YAAY,EAAC,EAAE,kBAAiB,IAAE;YAAE,KAAK,EAAE,YAAY;gBAAC,IAAG,MAAI,GAAE;oBAAC,EAAE,kBAAiB,IAAE,EAAE,mBAAmB;oBAAC;gBAAK;gBAAC,IAAG,KAAI,MAAI,GAAE;gBAAM,IAAG,MAAI,GAAE;oBAAC,IAAG,MAAI,GAAE;oBAAO,EAAE,iBAAgB,CAAC,IAAG,IAAE,EAAE,kBAAkB;oBAAC;gBAAK;gBAAC,IAAG,IAAE,EAAE,IAAG,IAAE,KAAG,IAAE,GAAE;gBAAO;YAAM,KAAK,EAAE,kBAAkB;gBAAC,IAAG,MAAI,GAAE;gBAAM,EAAE,kBAAiB,IAAE,EAAE,YAAY;YAAC,KAAK,EAAE,YAAY;gBAAC,MAAI,KAAG,CAAC,EAAE,iBAAgB,CAAC,IAAG,EAAE,gBAAe,IAAE,EAAE,wBAAwB;gBAAE;YAAM,KAAK,EAAE,wBAAwB;gBAAC,IAAG,MAAI,GAAE;gBAAO,IAAE,EAAE,kBAAkB;gBAAC;YAAM,KAAK,EAAE,mBAAmB;gBAAC,IAAG,MAAI,GAAE;gBAAO,EAAE,iBAAgB,IAAE,EAAE,eAAe;gBAAC;YAAM,KAAK,EAAE,eAAe;gBAAC,IAAE,EAAE,SAAS,EAAC,EAAE;YAAc,KAAK,EAAE,SAAS;gBAAC,IAAG,IAAE,GAAE,MAAI,GAAE;oBAAC,IAAI,KAAG,GAAE,IAAE,KAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAG,CAAC,GAAG,KAAG;oBAAE,KAAG,GAAE,IAAE,CAAC,CAAC,EAAE;gBAAA;gBAAC,IAAG,IAAE,EAAE,MAAM,EAAC,CAAC,CAAC,EAAE,KAAG,IAAE,CAAC,MAAI,KAAG,EAAE,cAAa,CAAC,IAAG,GAAG,IAAE,IAAE;qBAAO,IAAG,MAAI,EAAE,MAAM,EAAC,KAAI,MAAI,IAAE,KAAG,EAAE,aAAa,GAAC,MAAI,IAAE,KAAG,EAAE,aAAa,GAAC,IAAE;qBAAO,IAAG,IAAE,MAAI,EAAE,MAAM,EAAC,IAAG,IAAE,EAAE,aAAa,EAAC;oBAAC,IAAG,IAAE,GAAE,MAAI,GAAE;wBAAC,KAAG,CAAC,EAAE,aAAa,EAAC,EAAE,cAAa,EAAE,gBAAe,IAAE,EAAE,kBAAkB;wBAAC;oBAAK;gBAAC,OAAM,IAAE,EAAE,aAAa,IAAE,MAAI,IAAE,CAAC,EAAE,cAAa,IAAE,EAAE,GAAG,EAAC,IAAE,CAAC,IAAE,IAAE;gBAAE,IAAG,IAAE,GAAE,CAAC,CAAC,IAAE,EAAE,GAAC;qBAAO,IAAG,IAAE,GAAE;oBAAC,MAAM,IAAE,IAAI,WAAW,EAAE,MAAM,EAAC,EAAE,UAAU,EAAC,EAAE,UAAU;oBAAE,EAAE,cAAa,GAAE,GAAE,IAAG,IAAE,GAAE,EAAE,eAAc;gBAAG;gBAAC;YAAM,KAAK,EAAE,GAAG;gBAAC;YAAM;gBAAQ,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,GAAG;QAAC;QAAC,EAAE,kBAAiB,EAAE,kBAAiB,EAAE,eAAc,IAAI,CAAC,KAAK,GAAC,GAAE,IAAI,CAAC,KAAK,GAAC,GAAE,IAAI,CAAC,KAAK,GAAC;IAAC;IAAC,MAAK;QAAC,IAAG,IAAI,CAAC,KAAK,KAAG,EAAE,kBAAkB,IAAE,IAAI,CAAC,KAAK,KAAG,KAAG,IAAI,CAAC,KAAK,KAAG,EAAE,SAAS,IAAE,IAAI,CAAC,KAAK,KAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAC,IAAI,CAAC,SAAS;aAAQ,IAAG,IAAI,CAAC,KAAK,KAAG,EAAE,GAAG,EAAC,MAAM,IAAI,MAAM;IAAmD;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,MAAM,IAAE,EAAE,KAAK,CAAC;IAA8D,IAAG,CAAC,GAAE;IAAO,MAAM,IAAE,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE,IAAE;IAAG,IAAI,IAAE,EAAE,KAAK,CAAC,EAAE,WAAW,CAAC,QAAM;IAAG,OAAO,IAAE,EAAE,OAAO,CAAC,QAAO,MAAK,IAAE,EAAE,OAAO,CAAC,eAAc,CAAC,GAAE,IAAI,OAAO,YAAY,CAAC,KAAI;AAAC;AAAC,EAAE,GAAE;AAAa,eAAe,EAAE,CAAC,EAAC,CAAC;IAAE,IAAG,CAAC,aAAa,IAAI,CAAC,IAAG,MAAM,IAAI,UAAU;IAAmB,MAAM,IAAE,EAAE,KAAK,CAAC;IAAmC,IAAG,CAAC,GAAE,MAAM,IAAI,UAAU;IAAwD,MAAM,IAAE,IAAI,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE;IAAE,MAAM,IAAE,EAAE,EAAC,IAAE,IAAI,wJAAA,CAAA,WAAC,EAAC,IAAE,EAAE,CAAA;QAAI,KAAG,EAAE,MAAM,CAAC,GAAE;YAAC,QAAO,CAAC;QAAC;IAAE,GAAE,eAAc,IAAE,EAAE,CAAA;QAAI,EAAE,IAAI,CAAC;IAAE,GAAE,iBAAgB,IAAE,EAAE;QAAK,MAAM,IAAE,IAAI,wJAAA,CAAA,OAAC,CAAC,GAAE,GAAE;YAAC,MAAK;QAAC;QAAG,EAAE,MAAM,CAAC,GAAE;IAAE,GAAE,yBAAwB,IAAE,EAAE;QAAK,EAAE,MAAM,CAAC,GAAE;IAAE,GAAE,0BAAyB,IAAE,IAAI,YAAY;IAAS,EAAE,MAAM,IAAG,EAAE,WAAW,GAAC;QAAW,EAAE,UAAU,GAAC,GAAE,EAAE,SAAS,GAAC,GAAE,IAAE,IAAG,IAAE,IAAG,IAAE,IAAG,IAAE,IAAG,IAAE,IAAG,IAAE,MAAK,EAAE,MAAM,GAAC;IAAC,GAAE,EAAE,aAAa,GAAC,SAAS,CAAC;QAAE,KAAG,EAAE,MAAM,CAAC,GAAE;YAAC,QAAO,CAAC;QAAC;IAAE,GAAE,EAAE,aAAa,GAAC,SAAS,CAAC;QAAE,KAAG,EAAE,MAAM,CAAC,GAAE;YAAC,QAAO,CAAC;QAAC;IAAE,GAAE,EAAE,WAAW,GAAC;QAAW,IAAG,KAAG,EAAE,MAAM,IAAG,IAAE,EAAE,WAAW,IAAG,MAAI,uBAAsB;YAAC,MAAM,IAAE,EAAE,KAAK,CAAC;YAAqD,KAAG,CAAC,IAAE,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE,IAAE,EAAE,GAAE,IAAE,EAAE,IAAG,KAAG,CAAC,EAAE,UAAU,GAAC,GAAE,EAAE,SAAS,GAAC,CAAC;QAAC,OAAM,MAAI,kBAAgB,CAAC,IAAE,CAAC;QAAE,IAAE,IAAG,IAAE;IAAE;IAAE,WAAU,MAAM,KAAK,EAAE,EAAE,KAAK,CAAC;IAAG,OAAO,EAAE,GAAG,IAAG;AAAC;AAAC,EAAE,GAAE", "ignoreList": [0], "debugId": null}}]}