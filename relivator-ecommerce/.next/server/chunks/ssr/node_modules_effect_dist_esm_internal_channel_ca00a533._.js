module.exports = {

"[project]/node_modules/effect/dist/esm/internal/channel/childExecutorDecision.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChildExecutorDecisionTypeId": (()=>ChildExecutorDecisionTypeId),
    "Close": (()=>Close),
    "Continue": (()=>Continue),
    "Yield": (()=>Yield),
    "isChildExecutorDecision": (()=>isChildExecutorDecision),
    "isClose": (()=>isClose),
    "isContinue": (()=>isContinue),
    "isYield": (()=>isYield),
    "match": (()=>match)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelChildExecutorDecision.js [app-rsc] (ecmascript)");
;
;
;
/** @internal */ const ChildExecutorDecisionSymbolKey = "effect/ChannelChildExecutorDecision";
const ChildExecutorDecisionTypeId = /*#__PURE__*/ Symbol.for(ChildExecutorDecisionSymbolKey);
/** @internal */ const proto = {
    [ChildExecutorDecisionTypeId]: ChildExecutorDecisionTypeId
};
const Continue = (_)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CONTINUE"];
    return op;
};
const Close = (value)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CLOSE"];
    op.value = value;
    return op;
};
const Yield = (_)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_YIELD"];
    return op;
};
const isChildExecutorDecision = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, ChildExecutorDecisionTypeId);
const isContinue = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CONTINUE"];
const isClose = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CLOSE"];
const isYield = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_YIELD"];
const match = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onClose, onContinue, onYield })=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CONTINUE"]:
            {
                return onContinue();
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CLOSE"]:
            {
                return onClose(self.value);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_YIELD"]:
            {
                return onYield();
            }
    }
}); //# sourceMappingURL=childExecutorDecision.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/channel/continuation.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ContinuationFinalizerImpl": (()=>ContinuationFinalizerImpl),
    "ContinuationKImpl": (()=>ContinuationKImpl),
    "ContinuationTypeId": (()=>ContinuationTypeId)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Exit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$continuation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/continuation.js [app-rsc] (ecmascript)");
;
;
const ContinuationTypeId = /*#__PURE__*/ Symbol.for("effect/ChannelContinuation");
const continuationVariance = {
    /* c8 ignore next */ _Env: (_)=>_,
    /* c8 ignore next */ _InErr: (_)=>_,
    /* c8 ignore next */ _InElem: (_)=>_,
    /* c8 ignore next */ _InDone: (_)=>_,
    /* c8 ignore next */ _OutErr: (_)=>_,
    /* c8 ignore next */ _OutDone: (_)=>_,
    /* c8 ignore next */ _OutErr2: (_)=>_,
    /* c8 ignore next */ _OutElem: (_)=>_,
    /* c8 ignore next */ _OutDone2: (_)=>_
};
class ContinuationKImpl {
    onSuccess;
    onHalt;
    _tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$continuation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CONTINUATION_K"];
    [ContinuationTypeId] = continuationVariance;
    constructor(onSuccess, onHalt){
        this.onSuccess = onSuccess;
        this.onHalt = onHalt;
    }
    onExit(exit) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isFailure"])(exit) ? this.onHalt(exit.cause) : this.onSuccess(exit.value);
    }
}
class ContinuationFinalizerImpl {
    finalizer;
    _tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$continuation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CONTINUATION_FINALIZER"];
    [ContinuationTypeId] = continuationVariance;
    constructor(finalizer){
        this.finalizer = finalizer;
    }
} //# sourceMappingURL=continuation.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/channel/upstreamPullStrategy.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PullAfterAllEnqueued": (()=>PullAfterAllEnqueued),
    "PullAfterNext": (()=>PullAfterNext),
    "UpstreamPullStrategyTypeId": (()=>UpstreamPullStrategyTypeId),
    "isPullAfterAllEnqueued": (()=>isPullAfterAllEnqueued),
    "isPullAfterNext": (()=>isPullAfterNext),
    "isUpstreamPullStrategy": (()=>isUpstreamPullStrategy),
    "match": (()=>match)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelUpstreamPullStrategy.js [app-rsc] (ecmascript)");
;
;
;
/** @internal */ const UpstreamPullStrategySymbolKey = "effect/ChannelUpstreamPullStrategy";
const UpstreamPullStrategyTypeId = /*#__PURE__*/ Symbol.for(UpstreamPullStrategySymbolKey);
const upstreamPullStrategyVariance = {
    /* c8 ignore next */ _A: (_)=>_
};
/** @internal */ const proto = {
    [UpstreamPullStrategyTypeId]: upstreamPullStrategyVariance
};
const PullAfterNext = (emitSeparator)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULL_AFTER_NEXT"];
    op.emitSeparator = emitSeparator;
    return op;
};
const PullAfterAllEnqueued = (emitSeparator)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULL_AFTER_ALL_ENQUEUED"];
    op.emitSeparator = emitSeparator;
    return op;
};
const isUpstreamPullStrategy = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, UpstreamPullStrategyTypeId);
const isPullAfterNext = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULL_AFTER_NEXT"];
const isPullAfterAllEnqueued = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULL_AFTER_ALL_ENQUEUED"];
const match = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onAllEnqueued, onNext })=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULL_AFTER_NEXT"]:
            {
                return onNext(self.emitSeparator);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULL_AFTER_ALL_ENQUEUED"]:
            {
                return onAllEnqueued(self.emitSeparator);
            }
    }
}); //# sourceMappingURL=upstreamPullStrategy.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/channel/channelState.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChannelStateTypeId": (()=>ChannelStateTypeId),
    "Done": (()=>Done),
    "Emit": (()=>Emit),
    "Read": (()=>Read),
    "effect": (()=>effect),
    "effectOrUndefinedIgnored": (()=>effectOrUndefinedIgnored),
    "fromEffect": (()=>fromEffect),
    "isChannelState": (()=>isChannelState),
    "isDone": (()=>isDone),
    "isEmit": (()=>isEmit),
    "isFromEffect": (()=>isFromEffect),
    "isRead": (()=>isRead)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelState.js [app-rsc] (ecmascript)");
;
;
;
const ChannelStateTypeId = /*#__PURE__*/ Symbol.for("effect/ChannelState");
const channelStateVariance = {
    /* c8 ignore next */ _E: (_)=>_,
    /* c8 ignore next */ _R: (_)=>_
};
/** @internal */ const proto = {
    [ChannelStateTypeId]: channelStateVariance
};
const Done = ()=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"];
    return op;
};
const Emit = ()=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_EMIT"];
    return op;
};
const fromEffect = (effect)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FROM_EFFECT"];
    op.effect = effect;
    return op;
};
const Read = (upstream, onEffect, onEmit, onDone)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_READ"];
    op.upstream = upstream;
    op.onEffect = onEffect;
    op.onEmit = onEmit;
    op.onDone = onDone;
    return op;
};
const isChannelState = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, ChannelStateTypeId);
const isDone = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"];
const isEmit = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_EMIT"];
const isFromEffect = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FROM_EFFECT"];
const isRead = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_READ"];
const effect = (self)=>isFromEffect(self) ? self.effect : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
const effectOrUndefinedIgnored = (self)=>isFromEffect(self) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ignore"])(self.effect) : undefined; //# sourceMappingURL=channelState.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/channel/subexecutor.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DrainChildExecutors": (()=>DrainChildExecutors),
    "Emit": (()=>Emit),
    "OP_DRAIN_CHILD_EXECUTORS": (()=>OP_DRAIN_CHILD_EXECUTORS),
    "OP_EMIT": (()=>OP_EMIT),
    "OP_PULL_FROM_CHILD": (()=>OP_PULL_FROM_CHILD),
    "OP_PULL_FROM_UPSTREAM": (()=>OP_PULL_FROM_UPSTREAM),
    "PullFromChild": (()=>PullFromChild),
    "PullFromUpstream": (()=>PullFromUpstream)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Exit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
;
;
;
const OP_PULL_FROM_CHILD = "PullFromChild";
const OP_PULL_FROM_UPSTREAM = "PullFromUpstream";
const OP_DRAIN_CHILD_EXECUTORS = "DrainChildExecutors";
const OP_EMIT = "Emit";
class PullFromChild {
    childExecutor;
    parentSubexecutor;
    onEmit;
    _tag = OP_PULL_FROM_CHILD;
    constructor(childExecutor, parentSubexecutor, onEmit){
        this.childExecutor = childExecutor;
        this.parentSubexecutor = parentSubexecutor;
        this.onEmit = onEmit;
    }
    close(exit) {
        const fin1 = this.childExecutor.close(exit);
        const fin2 = this.parentSubexecutor.close(exit);
        if (fin1 !== undefined && fin2 !== undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipWith"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(fin1), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(fin2), (exit1, exit2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(exit1, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(exit2)));
        } else if (fin1 !== undefined) {
            return fin1;
        } else if (fin2 !== undefined) {
            return fin2;
        } else {
            return undefined;
        }
    }
    enqueuePullFromChild(_child) {
        return this;
    }
}
class PullFromUpstream {
    upstreamExecutor;
    createChild;
    lastDone;
    activeChildExecutors;
    combineChildResults;
    combineWithChildResult;
    onPull;
    onEmit;
    _tag = OP_PULL_FROM_UPSTREAM;
    constructor(upstreamExecutor, createChild, lastDone, activeChildExecutors, combineChildResults, combineWithChildResult, onPull, onEmit){
        this.upstreamExecutor = upstreamExecutor;
        this.createChild = createChild;
        this.lastDone = lastDone;
        this.activeChildExecutors = activeChildExecutors;
        this.combineChildResults = combineChildResults;
        this.combineWithChildResult = combineWithChildResult;
        this.onPull = onPull;
        this.onEmit = onEmit;
    }
    close(exit) {
        const fin1 = this.upstreamExecutor.close(exit);
        const fins = [
            ...this.activeChildExecutors.map((child)=>child !== undefined ? child.childExecutor.close(exit) : undefined),
            fin1
        ];
        const result = fins.reduce((acc, next)=>{
            if (acc !== undefined && next !== undefined) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipWith"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(next), (exit1, exit2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(exit1, exit2));
            } else if (acc !== undefined) {
                return acc;
            } else if (next !== undefined) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(next);
            } else {
                return undefined;
            }
        }, undefined);
        return result === undefined ? result : result;
    }
    enqueuePullFromChild(child) {
        return new PullFromUpstream(this.upstreamExecutor, this.createChild, this.lastDone, [
            ...this.activeChildExecutors,
            child
        ], this.combineChildResults, this.combineWithChildResult, this.onPull, this.onEmit);
    }
}
class DrainChildExecutors {
    upstreamExecutor;
    lastDone;
    activeChildExecutors;
    upstreamDone;
    combineChildResults;
    combineWithChildResult;
    onPull;
    _tag = OP_DRAIN_CHILD_EXECUTORS;
    constructor(upstreamExecutor, lastDone, activeChildExecutors, upstreamDone, combineChildResults, combineWithChildResult, onPull){
        this.upstreamExecutor = upstreamExecutor;
        this.lastDone = lastDone;
        this.activeChildExecutors = activeChildExecutors;
        this.upstreamDone = upstreamDone;
        this.combineChildResults = combineChildResults;
        this.combineWithChildResult = combineWithChildResult;
        this.onPull = onPull;
    }
    close(exit) {
        const fin1 = this.upstreamExecutor.close(exit);
        const fins = [
            ...this.activeChildExecutors.map((child)=>child !== undefined ? child.childExecutor.close(exit) : undefined),
            fin1
        ];
        const result = fins.reduce((acc, next)=>{
            if (acc !== undefined && next !== undefined) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipWith"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(next), (exit1, exit2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(exit1, exit2));
            } else if (acc !== undefined) {
                return acc;
            } else if (next !== undefined) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(next);
            } else {
                return undefined;
            }
        }, undefined);
        return result === undefined ? result : result;
    }
    enqueuePullFromChild(child) {
        return new DrainChildExecutors(this.upstreamExecutor, this.lastDone, [
            ...this.activeChildExecutors,
            child
        ], this.upstreamDone, this.combineChildResults, this.combineWithChildResult, this.onPull);
    }
}
class Emit {
    value;
    next;
    _tag = OP_EMIT;
    constructor(value, next){
        this.value = value;
        this.next = next;
    }
    close(exit) {
        const result = this.next.close(exit);
        return result === undefined ? result : result;
    }
    enqueuePullFromChild(_child) {
        return this;
    }
} //# sourceMappingURL=subexecutor.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/channel/upstreamPullRequest.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NoUpstream": (()=>NoUpstream),
    "Pulled": (()=>Pulled),
    "UpstreamPullRequestTypeId": (()=>UpstreamPullRequestTypeId),
    "isNoUpstream": (()=>isNoUpstream),
    "isPulled": (()=>isPulled),
    "isUpstreamPullRequest": (()=>isUpstreamPullRequest),
    "match": (()=>match)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullRequest$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelUpstreamPullRequest.js [app-rsc] (ecmascript)");
;
;
;
/** @internal */ const UpstreamPullRequestSymbolKey = "effect/ChannelUpstreamPullRequest";
const UpstreamPullRequestTypeId = /*#__PURE__*/ Symbol.for(UpstreamPullRequestSymbolKey);
const upstreamPullRequestVariance = {
    /* c8 ignore next */ _A: (_)=>_
};
/** @internal */ const proto = {
    [UpstreamPullRequestTypeId]: upstreamPullRequestVariance
};
const Pulled = (value)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullRequest$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULLED"];
    op.value = value;
    return op;
};
const NoUpstream = (activeDownstreamCount)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullRequest$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_NO_UPSTREAM"];
    op.activeDownstreamCount = activeDownstreamCount;
    return op;
};
const isUpstreamPullRequest = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, UpstreamPullRequestTypeId);
const isPulled = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullRequest$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULLED"];
const isNoUpstream = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullRequest$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_NO_UPSTREAM"];
const match = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onNoUpstream, onPulled })=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullRequest$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULLED"]:
            {
                return onPulled(self.value);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullRequest$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_NO_UPSTREAM"]:
            {
                return onNoUpstream(self.activeDownstreamCount);
            }
    }
}); //# sourceMappingURL=upstreamPullRequest.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/channel/channelExecutor.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChannelExecutor": (()=>ChannelExecutor),
    "readUpstream": (()=>readUpstream),
    "runIn": (()=>runIn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Cause.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Deferred.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/ExecutionStrategy.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Exit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Fiber.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/FiberId.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/HashSet.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Option.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Scope.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/core-stream.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channel.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelChildExecutorDecision.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelState.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelUpstreamPullStrategy.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$continuation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/continuation.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/channel/channelState.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$continuation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/channel/continuation.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/channel/subexecutor.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$upstreamPullRequest$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/channel/upstreamPullRequest.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
class ChannelExecutor {
    _activeSubexecutor = undefined;
    _cancelled = undefined;
    _closeLastSubstream = undefined;
    _currentChannel;
    _done = undefined;
    _doneStack = [];
    _emitted = undefined;
    _executeCloseLastSubstream;
    _input = undefined;
    _inProgressFinalizer = undefined;
    _providedEnv;
    constructor(initialChannel, providedEnv, executeCloseLastSubstream){
        this._currentChannel = initialChannel;
        this._executeCloseLastSubstream = executeCloseLastSubstream;
        this._providedEnv = providedEnv;
    }
    run() {
        let result = undefined;
        while(result === undefined){
            if (this._cancelled !== undefined) {
                result = this.processCancellation();
            } else if (this._activeSubexecutor !== undefined) {
                result = this.runSubexecutor();
            } else {
                try {
                    if (this._currentChannel === undefined) {
                        result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])();
                    } else {
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(this._currentChannel)) {
                            this._currentChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(this._currentChannel);
                        }
                        switch(this._currentChannel._tag){
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BRACKET_OUT"]:
                                {
                                    result = this.runBracketOut(this._currentChannel);
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BRIDGE"]:
                                {
                                    const bridgeInput = this._currentChannel.input;
                                    // PipeTo(left, Bridge(queue, channel))
                                    // In a fiber: repeatedly run left and push its outputs to the queue
                                    // Add a finalizer to interrupt the fiber and close the executor
                                    this._currentChannel = this._currentChannel.channel;
                                    if (this._input !== undefined) {
                                        const inputExecutor = this._input;
                                        this._input = undefined;
                                        const drainer = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(bridgeInput.awaitRead(), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
                                                    const state = inputExecutor.run();
                                                    switch(state._tag){
                                                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"]:
                                                            {
                                                                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(inputExecutor.getDone(), {
                                                                    onFailure: (cause)=>bridgeInput.error(cause),
                                                                    onSuccess: (value)=>bridgeInput.done(value)
                                                                });
                                                            }
                                                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_EMIT"]:
                                                            {
                                                                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(bridgeInput.emit(inputExecutor.getEmit()), ()=>drainer());
                                                            }
                                                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FROM_EFFECT"]:
                                                            {
                                                                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])(state.effect, {
                                                                    onFailure: (cause)=>bridgeInput.error(cause),
                                                                    onSuccess: ()=>drainer()
                                                                });
                                                            }
                                                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_READ"]:
                                                            {
                                                                return readUpstream(state, ()=>drainer(), (cause)=>bridgeInput.error(cause));
                                                            }
                                                    }
                                                }));
                                        result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forkDaemon"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(drainer())), (fiber)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>this.addFinalizer((exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(fiber), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
                                                            const effect = this.restorePipe(exit, inputExecutor);
                                                            return effect !== undefined ? effect : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
                                                        }))))));
                                    }
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CONCAT_ALL"]:
                                {
                                    const executor = new ChannelExecutor(this._currentChannel.value(), this._providedEnv, (effect)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>{
                                            const prevLastClose = this._closeLastSubstream === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"] : this._closeLastSubstream;
                                            this._closeLastSubstream = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(prevLastClose, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(effect));
                                        }));
                                    executor._input = this._input;
                                    const channel = this._currentChannel;
                                    this._activeSubexecutor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PullFromUpstream"](executor, (value)=>channel.k(value), undefined, [], (x, y)=>channel.combineInners(x, y), (x, y)=>channel.combineAll(x, y), (request)=>channel.onPull(request), (value)=>channel.onEmit(value));
                                    this._closeLastSubstream = undefined;
                                    this._currentChannel = undefined;
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_EMIT"]:
                                {
                                    this._emitted = this._currentChannel.out;
                                    this._currentChannel = this._activeSubexecutor !== undefined ? undefined : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
                                    result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Emit"])();
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ENSURING"]:
                                {
                                    this.runEnsuring(this._currentChannel);
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAIL"]:
                                {
                                    result = this.doneHalt(this._currentChannel.error());
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FOLD"]:
                                {
                                    this._doneStack.push(this._currentChannel.k);
                                    this._currentChannel = this._currentChannel.channel;
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FROM_EFFECT"]:
                                {
                                    const effect = this._providedEnv === undefined ? this._currentChannel.effect() : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(this._currentChannel.effect(), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provide"])(this._providedEnv));
                                    result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])(effect, {
                                        onFailure: (cause)=>{
                                            const state = this.doneHalt(cause);
                                            return state !== undefined && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isFromEffect"])(state) ? state.effect : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
                                        },
                                        onSuccess: (value)=>{
                                            const state = this.doneSucceed(value);
                                            return state !== undefined && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isFromEffect"])(state) ? state.effect : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
                                        }
                                    }));
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PIPE_TO"]:
                                {
                                    const previousInput = this._input;
                                    const leftExec = new ChannelExecutor(this._currentChannel.left(), this._providedEnv, (effect)=>this._executeCloseLastSubstream(effect));
                                    leftExec._input = previousInput;
                                    this._input = leftExec;
                                    this.addFinalizer((exit)=>{
                                        const effect = this.restorePipe(exit, previousInput);
                                        return effect !== undefined ? effect : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
                                    });
                                    this._currentChannel = this._currentChannel.right();
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PROVIDE"]:
                                {
                                    const previousEnv = this._providedEnv;
                                    this._providedEnv = this._currentChannel.context();
                                    this._currentChannel = this._currentChannel.inner;
                                    this.addFinalizer(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>{
                                            this._providedEnv = previousEnv;
                                        }));
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_READ"]:
                                {
                                    const read = this._currentChannel;
                                    result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Read"])(this._input, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"], (emitted)=>{
                                        try {
                                            this._currentChannel = read.more(emitted);
                                        } catch (error) {
                                            this._currentChannel = read.done.onExit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(error));
                                        }
                                        return undefined;
                                    }, (exit)=>{
                                        const onExit = (exit)=>{
                                            return read.done.onExit(exit);
                                        };
                                        this._currentChannel = onExit(exit);
                                        return undefined;
                                    });
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCEED"]:
                                {
                                    result = this.doneSucceed(this._currentChannel.evaluate());
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCEED_NOW"]:
                                {
                                    result = this.doneSucceed(this._currentChannel.terminal);
                                    break;
                                }
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUSPEND"]:
                                {
                                    this._currentChannel = this._currentChannel.channel();
                                    break;
                                }
                            default:
                                {
                                    // @ts-expect-error
                                    this._currentChannel._tag;
                                }
                        }
                    }
                } catch (error) {
                    this._currentChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(error));
                }
            }
        }
        return result;
    }
    getDone() {
        return this._done;
    }
    getEmit() {
        return this._emitted;
    }
    cancelWith(exit) {
        this._cancelled = exit;
    }
    clearInProgressFinalizer() {
        this._inProgressFinalizer = undefined;
    }
    storeInProgressFinalizer(finalizer) {
        this._inProgressFinalizer = finalizer;
    }
    popAllFinalizers(exit) {
        const finalizers = [];
        let next = this._doneStack.pop();
        while(next){
            if (next._tag === "ContinuationFinalizer") {
                finalizers.push(next.finalizer);
            }
            next = this._doneStack.pop();
        }
        const effect = finalizers.length === 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"] : runFinalizers(finalizers, exit);
        this.storeInProgressFinalizer(effect);
        return effect;
    }
    popNextFinalizers() {
        const builder = [];
        while(this._doneStack.length !== 0){
            const cont = this._doneStack[this._doneStack.length - 1];
            if (cont._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$continuation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CONTINUATION_K"]) {
                return builder;
            }
            builder.push(cont);
            this._doneStack.pop();
        }
        return builder;
    }
    restorePipe(exit, prev) {
        const currInput = this._input;
        this._input = prev;
        if (currInput !== undefined) {
            const effect = currInput.close(exit);
            return effect;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
    }
    close(exit) {
        let runInProgressFinalizers = undefined;
        const finalizer = this._inProgressFinalizer;
        if (finalizer !== undefined) {
            runInProgressFinalizers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(finalizer, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensuring"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>this.clearInProgressFinalizer())));
        }
        let closeSelf = undefined;
        const selfFinalizers = this.popAllFinalizers(exit);
        if (selfFinalizers !== undefined) {
            closeSelf = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(selfFinalizers, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensuring"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>this.clearInProgressFinalizer())));
        }
        const closeSubexecutors = this._activeSubexecutor === undefined ? undefined : this._activeSubexecutor.close(exit);
        if (closeSubexecutors === undefined && runInProgressFinalizers === undefined && closeSelf === undefined) {
            return undefined;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(ifNotNull(closeSubexecutors)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zip"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(ifNotNull(runInProgressFinalizers))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zip"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(ifNotNull(closeSelf))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(([[exit1, exit2], exit3])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(exit1, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(exit2), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(exit3))), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptible"], // TODO: remove
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>exit)));
    }
    doneSucceed(value) {
        if (this._doneStack.length === 0) {
            this._done = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(value);
            this._currentChannel = undefined;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])();
        }
        const head = this._doneStack[this._doneStack.length - 1];
        if (head._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$continuation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CONTINUATION_K"]) {
            this._doneStack.pop();
            this._currentChannel = head.onSuccess(value);
            return undefined;
        }
        const finalizers = this.popNextFinalizers();
        if (this._doneStack.length === 0) {
            this._doneStack = finalizers.reverse();
            this._done = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(value);
            this._currentChannel = undefined;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])();
        }
        const finalizerEffect = runFinalizers(finalizers.map((f)=>f.finalizer), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(value));
        this.storeInProgressFinalizer(finalizerEffect);
        const effect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(finalizerEffect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensuring"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>this.clearInProgressFinalizer())), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptible"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>this.doneSucceed(value))));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(effect);
    }
    doneHalt(cause) {
        if (this._doneStack.length === 0) {
            this._done = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause);
            this._currentChannel = undefined;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])();
        }
        const head = this._doneStack[this._doneStack.length - 1];
        if (head._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$continuation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CONTINUATION_K"]) {
            this._doneStack.pop();
            try {
                this._currentChannel = head.onHalt(cause);
            } catch (error) {
                this._currentChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(error));
            }
            return undefined;
        }
        const finalizers = this.popNextFinalizers();
        if (this._doneStack.length === 0) {
            this._doneStack = finalizers.reverse();
            this._done = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause);
            this._currentChannel = undefined;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])();
        }
        const finalizerEffect = runFinalizers(finalizers.map((f)=>f.finalizer), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause));
        this.storeInProgressFinalizer(finalizerEffect);
        const effect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(finalizerEffect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensuring"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>this.clearInProgressFinalizer())), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptible"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>this.doneHalt(cause))));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(effect);
    }
    processCancellation() {
        this._currentChannel = undefined;
        this._done = this._cancelled;
        this._cancelled = undefined;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])();
    }
    runBracketOut(bracketOut) {
        const effect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptible"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])(this.provide(bracketOut.acquire()), {
            onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>{
                    this._currentChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause);
                }),
            onSuccess: (out)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>{
                    this.addFinalizer((exit)=>this.provide(bracketOut.finalizer(out, exit)));
                    this._currentChannel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(out);
                })
        }));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(effect);
    }
    provide(effect) {
        if (this._providedEnv === undefined) {
            return effect;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(effect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provide"])(this._providedEnv));
    }
    runEnsuring(ensuring) {
        this.addFinalizer(ensuring.finalizer);
        this._currentChannel = ensuring.channel;
    }
    addFinalizer(f) {
        this._doneStack.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$continuation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ContinuationFinalizerImpl"](f));
    }
    runSubexecutor() {
        const subexecutor = this._activeSubexecutor;
        switch(subexecutor._tag){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULL_FROM_CHILD"]:
                {
                    return this.pullFromChild(subexecutor.childExecutor, subexecutor.parentSubexecutor, subexecutor.onEmit, subexecutor);
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULL_FROM_UPSTREAM"]:
                {
                    return this.pullFromUpstream(subexecutor);
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DRAIN_CHILD_EXECUTORS"]:
                {
                    return this.drainChildExecutors(subexecutor);
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_EMIT"]:
                {
                    this._emitted = subexecutor.value;
                    this._activeSubexecutor = subexecutor.next;
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Emit"])();
                }
        }
    }
    replaceSubexecutor(nextSubExec) {
        this._currentChannel = undefined;
        this._activeSubexecutor = nextSubExec;
    }
    finishWithExit(exit) {
        const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(exit, {
            onFailure: (cause)=>this.doneHalt(cause),
            onSuccess: (value)=>this.doneSucceed(value)
        });
        this._activeSubexecutor = undefined;
        return state === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effect"])(state);
    }
    finishSubexecutorWithCloseEffect(subexecutorDone, ...closeFuncs) {
        this.addFinalizer(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(closeFuncs, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEach"])((closeFunc)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>closeFunc(subexecutorDone)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((closeEffect)=>closeEffect !== undefined ? closeEffect : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"])), {
                discard: true
            })));
        const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(subexecutorDone, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
            onFailure: (cause)=>this.doneHalt(cause),
            onSuccess: (value)=>this.doneSucceed(value)
        }));
        this._activeSubexecutor = undefined;
        return state;
    }
    applyUpstreamPullStrategy(upstreamFinished, queue, strategy) {
        switch(strategy._tag){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULL_AFTER_NEXT"]:
                {
                    const shouldPrepend = !upstreamFinished || queue.some((subexecutor)=>subexecutor !== undefined);
                    return [
                        strategy.emitSeparator,
                        shouldPrepend ? [
                            undefined,
                            ...queue
                        ] : queue
                    ];
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelUpstreamPullStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULL_AFTER_ALL_ENQUEUED"]:
                {
                    const shouldEnqueue = !upstreamFinished || queue.some((subexecutor)=>subexecutor !== undefined);
                    return [
                        strategy.emitSeparator,
                        shouldEnqueue ? [
                            ...queue,
                            undefined
                        ] : queue
                    ];
                }
        }
    }
    pullFromChild(childExecutor, parentSubexecutor, onEmitted, subexecutor) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Read"])(childExecutor, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"], (emitted)=>{
            const childExecutorDecision = onEmitted(emitted);
            switch(childExecutorDecision._tag){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CONTINUE"]:
                    {
                        break;
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_CLOSE"]:
                    {
                        this.finishWithDoneValue(childExecutor, parentSubexecutor, childExecutorDecision.value);
                        break;
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelChildExecutorDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_YIELD"]:
                    {
                        const modifiedParent = parentSubexecutor.enqueuePullFromChild(subexecutor);
                        this.replaceSubexecutor(modifiedParent);
                        break;
                    }
            }
            this._activeSubexecutor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Emit"](emitted, this._activeSubexecutor);
            return undefined;
        }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
            onFailure: (cause)=>{
                const state = this.handleSubexecutorFailure(childExecutor, parentSubexecutor, cause);
                return state === undefined ? undefined : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effectOrUndefinedIgnored"])(state);
            },
            onSuccess: (doneValue)=>{
                this.finishWithDoneValue(childExecutor, parentSubexecutor, doneValue);
                return undefined;
            }
        }));
    }
    finishWithDoneValue(childExecutor, parentSubexecutor, doneValue) {
        const subexecutor = parentSubexecutor;
        switch(subexecutor._tag){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PULL_FROM_UPSTREAM"]:
                {
                    const modifiedParent = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PullFromUpstream"](subexecutor.upstreamExecutor, subexecutor.createChild, subexecutor.lastDone !== undefined ? subexecutor.combineChildResults(subexecutor.lastDone, doneValue) : doneValue, subexecutor.activeChildExecutors, subexecutor.combineChildResults, subexecutor.combineWithChildResult, subexecutor.onPull, subexecutor.onEmit);
                    this._closeLastSubstream = childExecutor.close((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(doneValue));
                    this.replaceSubexecutor(modifiedParent);
                    break;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DRAIN_CHILD_EXECUTORS"]:
                {
                    const modifiedParent = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DrainChildExecutors"](subexecutor.upstreamExecutor, subexecutor.lastDone !== undefined ? subexecutor.combineChildResults(subexecutor.lastDone, doneValue) : doneValue, subexecutor.activeChildExecutors, subexecutor.upstreamDone, subexecutor.combineChildResults, subexecutor.combineWithChildResult, subexecutor.onPull);
                    this._closeLastSubstream = childExecutor.close((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(doneValue));
                    this.replaceSubexecutor(modifiedParent);
                    break;
                }
            default:
                {
                    break;
                }
        }
    }
    handleSubexecutorFailure(childExecutor, parentSubexecutor, cause) {
        return this.finishSubexecutorWithCloseEffect((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause), (exit)=>parentSubexecutor.close(exit), (exit)=>childExecutor.close(exit));
    }
    pullFromUpstream(subexecutor) {
        if (subexecutor.activeChildExecutors.length === 0) {
            return this.performPullFromUpstream(subexecutor);
        }
        const activeChild = subexecutor.activeChildExecutors[0];
        const parentSubexecutor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PullFromUpstream"](subexecutor.upstreamExecutor, subexecutor.createChild, subexecutor.lastDone, subexecutor.activeChildExecutors.slice(1), subexecutor.combineChildResults, subexecutor.combineWithChildResult, subexecutor.onPull, subexecutor.onEmit);
        if (activeChild === undefined) {
            return this.performPullFromUpstream(parentSubexecutor);
        }
        this.replaceSubexecutor(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PullFromChild"](activeChild.childExecutor, parentSubexecutor, activeChild.onEmit));
        return undefined;
    }
    performPullFromUpstream(subexecutor) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Read"])(subexecutor.upstreamExecutor, (effect)=>{
            const closeLastSubstream = this._closeLastSubstream === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"] : this._closeLastSubstream;
            this._closeLastSubstream = undefined;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(this._executeCloseLastSubstream(closeLastSubstream), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(effect));
        }, (emitted)=>{
            if (this._closeLastSubstream !== undefined) {
                const closeLastSubstream = this._closeLastSubstream;
                this._closeLastSubstream = undefined;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(this._executeCloseLastSubstream(closeLastSubstream), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(()=>{
                    const childExecutor = new ChannelExecutor(subexecutor.createChild(emitted), this._providedEnv, this._executeCloseLastSubstream);
                    childExecutor._input = this._input;
                    const [emitSeparator, updatedChildExecutors] = this.applyUpstreamPullStrategy(false, subexecutor.activeChildExecutors, subexecutor.onPull((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$upstreamPullRequest$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Pulled"])(emitted)));
                    this._activeSubexecutor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PullFromChild"](childExecutor, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PullFromUpstream"](subexecutor.upstreamExecutor, subexecutor.createChild, subexecutor.lastDone, updatedChildExecutors, subexecutor.combineChildResults, subexecutor.combineWithChildResult, subexecutor.onPull, subexecutor.onEmit), subexecutor.onEmit);
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSome"])(emitSeparator)) {
                        this._activeSubexecutor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Emit"](emitSeparator.value, this._activeSubexecutor);
                    }
                    return undefined;
                }));
            }
            const childExecutor = new ChannelExecutor(subexecutor.createChild(emitted), this._providedEnv, this._executeCloseLastSubstream);
            childExecutor._input = this._input;
            const [emitSeparator, updatedChildExecutors] = this.applyUpstreamPullStrategy(false, subexecutor.activeChildExecutors, subexecutor.onPull((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$upstreamPullRequest$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Pulled"])(emitted)));
            this._activeSubexecutor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PullFromChild"](childExecutor, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PullFromUpstream"](subexecutor.upstreamExecutor, subexecutor.createChild, subexecutor.lastDone, updatedChildExecutors, subexecutor.combineChildResults, subexecutor.combineWithChildResult, subexecutor.onPull, subexecutor.onEmit), subexecutor.onEmit);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSome"])(emitSeparator)) {
                this._activeSubexecutor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Emit"](emitSeparator.value, this._activeSubexecutor);
            }
            return undefined;
        }, (exit)=>{
            if (subexecutor.activeChildExecutors.some((subexecutor)=>subexecutor !== undefined)) {
                const drain = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DrainChildExecutors"](subexecutor.upstreamExecutor, subexecutor.lastDone, [
                    undefined,
                    ...subexecutor.activeChildExecutors
                ], subexecutor.upstreamExecutor.getDone(), subexecutor.combineChildResults, subexecutor.combineWithChildResult, subexecutor.onPull);
                if (this._closeLastSubstream !== undefined) {
                    const closeLastSubstream = this._closeLastSubstream;
                    this._closeLastSubstream = undefined;
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(this._executeCloseLastSubstream(closeLastSubstream), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(()=>this.replaceSubexecutor(drain)));
                }
                this.replaceSubexecutor(drain);
                return undefined;
            }
            const closeLastSubstream = this._closeLastSubstream;
            const state = this.finishSubexecutorWithCloseEffect((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(exit, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((a)=>subexecutor.combineWithChildResult(subexecutor.lastDone, a))), ()=>closeLastSubstream, (exit)=>subexecutor.upstreamExecutor.close(exit));
            return state === undefined ? undefined : // NOTE: assuming finalizers cannot fail
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effectOrUndefinedIgnored"])(state);
        });
    }
    drainChildExecutors(subexecutor) {
        if (subexecutor.activeChildExecutors.length === 0) {
            const lastClose = this._closeLastSubstream;
            if (lastClose !== undefined) {
                this.addFinalizer(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(lastClose));
            }
            return this.finishSubexecutorWithCloseEffect(subexecutor.upstreamDone, ()=>lastClose, (exit)=>subexecutor.upstreamExecutor.close(exit));
        }
        const activeChild = subexecutor.activeChildExecutors[0];
        const rest = subexecutor.activeChildExecutors.slice(1);
        if (activeChild === undefined) {
            const [emitSeparator, remainingExecutors] = this.applyUpstreamPullStrategy(true, rest, subexecutor.onPull((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$upstreamPullRequest$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NoUpstream"])(rest.reduce((n, curr)=>curr !== undefined ? n + 1 : n, 0))));
            this.replaceSubexecutor(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DrainChildExecutors"](subexecutor.upstreamExecutor, subexecutor.lastDone, remainingExecutors, subexecutor.upstreamDone, subexecutor.combineChildResults, subexecutor.combineWithChildResult, subexecutor.onPull));
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSome"])(emitSeparator)) {
                this._emitted = emitSeparator.value;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Emit"])();
            }
            return undefined;
        }
        const parentSubexecutor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DrainChildExecutors"](subexecutor.upstreamExecutor, subexecutor.lastDone, rest, subexecutor.upstreamDone, subexecutor.combineChildResults, subexecutor.combineWithChildResult, subexecutor.onPull);
        this.replaceSubexecutor(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$subexecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PullFromChild"](activeChild.childExecutor, parentSubexecutor, activeChild.onEmit));
        return undefined;
    }
}
const ifNotNull = (effect)=>effect !== undefined ? effect : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
const runFinalizers = (finalizers, exit)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEach"])(finalizers, (fin)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(fin(exit))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((exits)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["all"])(exits), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"]))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>exit)));
};
const readUpstream = (r, onSuccess, onFailure)=>{
    const readStack = [
        r
    ];
    const read = ()=>{
        const current = readStack.pop();
        if (current === undefined || current.upstream === undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dieMessage"])("Unexpected end of input for channel execution");
        }
        const state = current.upstream.run();
        switch(state._tag){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_EMIT"]:
                {
                    const emitEffect = current.onEmit(current.upstream.getEmit());
                    if (readStack.length === 0) {
                        if (emitEffect === undefined) {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(onSuccess);
                        }
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(emitEffect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])({
                            onFailure,
                            onSuccess
                        }));
                    }
                    if (emitEffect === undefined) {
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>read());
                    }
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(emitEffect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])({
                        onFailure,
                        onSuccess: ()=>read()
                    }));
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"]:
                {
                    const doneEffect = current.onDone(current.upstream.getDone());
                    if (readStack.length === 0) {
                        if (doneEffect === undefined) {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(onSuccess);
                        }
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(doneEffect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])({
                            onFailure,
                            onSuccess
                        }));
                    }
                    if (doneEffect === undefined) {
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>read());
                    }
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(doneEffect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])({
                        onFailure,
                        onSuccess: ()=>read()
                    }));
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FROM_EFFECT"]:
                {
                    readStack.push(current);
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(current.onEffect(state.effect), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAllCause"])((cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
                            const doneEffect = current.onDone((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause));
                            return doneEffect === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"] : doneEffect;
                        })), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])({
                        onFailure,
                        onSuccess: ()=>read()
                    }));
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_READ"]:
                {
                    readStack.push(current);
                    readStack.push(state);
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>read());
                }
        }
    };
    return read();
};
const runIn = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, scope)=>{
    const run = (channelDeferred, scopeDeferred, scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["acquireUseRelease"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>new ChannelExecutor(self, void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"])), (exec)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>runScopedInterpret(exec.run(), exec).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["intoDeferred"])(channelDeferred), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(channelDeferred)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipLeft"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(scopeDeferred)))), (exec, exit)=>{
            const finalize = exec.close(exit);
            if (finalize === undefined) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tapErrorCause"])(finalize, (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addFinalizer"])(scope, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause)));
        });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptibleMask"])((restore)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["all"])([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fork"])(scope, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"]),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])()
        ]).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(([child, channelDeferred, scopeDeferred])=>restore(run(channelDeferred, scopeDeferred, child)).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forkIn"])(scope), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((fiber)=>scope.addFinalizer((exit)=>{
                    const interruptors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isFailure"])(exit) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptors"])(exit.cause) : undefined;
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isDone"])(channelDeferred).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((isDone)=>isDone ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(scopeDeferred, void 0).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(fiber)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["inheritAll"])(fiber))) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(scopeDeferred, void 0).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(interruptors && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["size"])(interruptors) > 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(fiber, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combineAll"])(interruptors)) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(fiber)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["inheritAll"])(fiber)))));
                }).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(restore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(channelDeferred)))))))));
});
/** @internal */ const runScopedInterpret = (channelState, exec)=>{
    const op = channelState;
    switch(op._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FROM_EFFECT"]:
            {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(op.effect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>runScopedInterpret(exec.run(), exec)));
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_EMIT"]:
            {
                // Can't really happen because Out <:< Nothing. So just skip ahead.
                return runScopedInterpret(exec.run(), exec);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"]:
            {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>exec.getDone());
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_READ"]:
            {
                return readUpstream(op, ()=>runScopedInterpret(exec.run(), exec), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"]);
            }
    }
}; //# sourceMappingURL=channelExecutor.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/channel/mergeDecision.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Await": (()=>Await),
    "AwaitConst": (()=>AwaitConst),
    "Done": (()=>Done),
    "MergeDecisionTypeId": (()=>MergeDecisionTypeId),
    "isMergeDecision": (()=>isMergeDecision),
    "match": (()=>match)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelMergeDecision.js [app-rsc] (ecmascript)");
;
;
;
/** @internal */ const MergeDecisionSymbolKey = "effect/ChannelMergeDecision";
const MergeDecisionTypeId = /*#__PURE__*/ Symbol.for(MergeDecisionSymbolKey);
/** @internal */ const proto = {
    [MergeDecisionTypeId]: {
        _R: (_)=>_,
        _E0: (_)=>_,
        _Z0: (_)=>_,
        _E: (_)=>_,
        _Z: (_)=>_
    }
};
const Done = (effect)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"];
    op.effect = effect;
    return op;
};
const Await = (f)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_AWAIT"];
    op.f = f;
    return op;
};
const AwaitConst = (effect)=>Await(()=>effect);
const isMergeDecision = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, MergeDecisionTypeId);
const match = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onAwait, onDone })=>{
    const op = self;
    switch(op._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"]:
            return onDone(op.effect);
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_AWAIT"]:
            return onAwait(op.f);
    }
}); //# sourceMappingURL=mergeDecision.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/channel/mergeState.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BothRunning": (()=>BothRunning),
    "LeftDone": (()=>LeftDone),
    "MergeStateTypeId": (()=>MergeStateTypeId),
    "RightDone": (()=>RightDone),
    "isBothRunning": (()=>isBothRunning),
    "isLeftDone": (()=>isLeftDone),
    "isMergeState": (()=>isMergeState),
    "isRightDone": (()=>isRightDone),
    "match": (()=>match)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelMergeState.js [app-rsc] (ecmascript)");
;
;
;
/** @internal */ const MergeStateSymbolKey = "effect/ChannelMergeState";
const MergeStateTypeId = /*#__PURE__*/ Symbol.for(MergeStateSymbolKey);
/** @internal */ const proto = {
    [MergeStateTypeId]: MergeStateTypeId
};
const BothRunning = (left, right)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BOTH_RUNNING"];
    op.left = left;
    op.right = right;
    return op;
};
const LeftDone = (f)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_LEFT_DONE"];
    op.f = f;
    return op;
};
const RightDone = (f)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RIGHT_DONE"];
    op.f = f;
    return op;
};
const isMergeState = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, MergeStateTypeId);
const isBothRunning = (self)=>{
    return self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BOTH_RUNNING"];
};
const isLeftDone = (self)=>{
    return self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_LEFT_DONE"];
};
const isRightDone = (self)=>{
    return self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RIGHT_DONE"];
};
const match = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onBothRunning, onLeftDone, onRightDone })=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BOTH_RUNNING"]:
            {
                return onBothRunning(self.left, self.right);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_LEFT_DONE"]:
            {
                return onLeftDone(self.f);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RIGHT_DONE"]:
            {
                return onRightDone(self.f);
            }
    }
}); //# sourceMappingURL=mergeState.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/channel/mergeStrategy.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BackPressure": (()=>BackPressure),
    "BufferSliding": (()=>BufferSliding),
    "MergeStrategyTypeId": (()=>MergeStrategyTypeId),
    "isBackPressure": (()=>isBackPressure),
    "isBufferSliding": (()=>isBufferSliding),
    "isMergeStrategy": (()=>isMergeStrategy),
    "match": (()=>match)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelMergeStrategy.js [app-rsc] (ecmascript)");
;
;
;
/** @internal */ const MergeStrategySymbolKey = "effect/ChannelMergeStrategy";
const MergeStrategyTypeId = /*#__PURE__*/ Symbol.for(MergeStrategySymbolKey);
/** @internal */ const proto = {
    [MergeStrategyTypeId]: MergeStrategyTypeId
};
const BackPressure = (_)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BACK_PRESSURE"];
    return op;
};
const BufferSliding = (_)=>{
    const op = Object.create(proto);
    op._tag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BUFFER_SLIDING"];
    return op;
};
const isMergeStrategy = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, MergeStrategyTypeId);
const isBackPressure = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BACK_PRESSURE"];
const isBufferSliding = (self)=>self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BUFFER_SLIDING"];
const match = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onBackPressure, onBufferSliding })=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BACK_PRESSURE"]:
            {
                return onBackPressure();
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BUFFER_SLIDING"]:
            {
                return onBufferSliding();
            }
    }
}); //# sourceMappingURL=mergeStrategy.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/channel/singleProducerAsyncInput.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "make": (()=>make)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Cause.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Deferred.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Either.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Exit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Ref.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
/** @internal */ const OP_STATE_EMPTY = "Empty";
/** @internal */ const OP_STATE_EMIT = "Emit";
/** @internal */ const OP_STATE_ERROR = "Error";
/** @internal */ const OP_STATE_DONE = "Done";
/** @internal */ const stateEmpty = (notifyProducer)=>({
        _tag: OP_STATE_EMPTY,
        notifyProducer
    });
/** @internal */ const stateEmit = (notifyConsumers)=>({
        _tag: OP_STATE_EMIT,
        notifyConsumers
    });
/** @internal */ const stateError = (cause)=>({
        _tag: OP_STATE_ERROR,
        cause
    });
/** @internal */ const stateDone = (done)=>({
        _tag: OP_STATE_DONE,
        done
    });
/** @internal */ class SingleProducerAsyncInputImpl {
    ref;
    constructor(ref){
        this.ref = ref;
    }
    awaitRead() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modify"])(this.ref, (state)=>state._tag === OP_STATE_EMPTY ? [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(state.notifyProducer),
                state
            ] : [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"],
                state
            ]));
    }
    get close() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberIdWith"])((fiberId)=>this.error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(fiberId)));
    }
    done(value) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modify"])(this.ref, (state)=>{
            switch(state._tag){
                case OP_STATE_EMPTY:
                    {
                        return [
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(state.notifyProducer),
                            state
                        ];
                    }
                case OP_STATE_EMIT:
                    {
                        return [
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEach"])(state.notifyConsumers, (deferred)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(deferred, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])(value)), {
                                discard: true
                            }),
                            stateDone(value)
                        ];
                    }
                case OP_STATE_ERROR:
                    {
                        return [
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"],
                            state
                        ];
                    }
                case OP_STATE_DONE:
                    {
                        return [
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"],
                            state
                        ];
                    }
            }
        }));
    }
    emit(element) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(), (deferred)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modify"])(this.ref, (state)=>{
                switch(state._tag){
                    case OP_STATE_EMPTY:
                        {
                            return [
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(state.notifyProducer),
                                state
                            ];
                        }
                    case OP_STATE_EMIT:
                        {
                            const notifyConsumer = state.notifyConsumers[0];
                            const notifyConsumers = state.notifyConsumers.slice(1);
                            if (notifyConsumer !== undefined) {
                                return [
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(notifyConsumer, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"])(element)),
                                    notifyConsumers.length === 0 ? stateEmpty(deferred) : stateEmit(notifyConsumers)
                                ];
                            }
                            throw new Error("Bug: Channel.SingleProducerAsyncInput.emit - Queue was empty! please report an issue at https://github.com/Effect-TS/effect/issues");
                        }
                    case OP_STATE_ERROR:
                        {
                            return [
                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"],
                                state
                            ];
                        }
                    case OP_STATE_DONE:
                        {
                            return [
                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"],
                                state
                            ];
                        }
                }
            })));
    }
    error(cause) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modify"])(this.ref, (state)=>{
            switch(state._tag){
                case OP_STATE_EMPTY:
                    {
                        return [
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(state.notifyProducer),
                            state
                        ];
                    }
                case OP_STATE_EMIT:
                    {
                        return [
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEach"])(state.notifyConsumers, (deferred)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(deferred, cause), {
                                discard: true
                            }),
                            stateError(cause)
                        ];
                    }
                case OP_STATE_ERROR:
                    {
                        return [
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"],
                            state
                        ];
                    }
                case OP_STATE_DONE:
                    {
                        return [
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"],
                            state
                        ];
                    }
            }
        }));
    }
    get take() {
        return this.takeWith((cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(cause, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])), (elem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(elem), (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"])(done)));
    }
    takeWith(onError, onElement, onDone) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(), (deferred)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modify"])(this.ref, (state)=>{
                switch(state._tag){
                    case OP_STATE_EMPTY:
                        {
                            return [
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(state.notifyProducer, void 0), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(deferred), {
                                    onFailure: onError,
                                    onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                                        onLeft: onDone,
                                        onRight: onElement
                                    })
                                })),
                                stateEmit([
                                    deferred
                                ])
                            ];
                        }
                    case OP_STATE_EMIT:
                        {
                            return [
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(deferred), {
                                    onFailure: onError,
                                    onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                                        onLeft: onDone,
                                        onRight: onElement
                                    })
                                }),
                                stateEmit([
                                    ...state.notifyConsumers,
                                    deferred
                                ])
                            ];
                        }
                    case OP_STATE_ERROR:
                        {
                            return [
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(onError(state.cause)),
                                state
                            ];
                        }
                    case OP_STATE_DONE:
                        {
                            return [
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(onDone(state.done)),
                                state
                            ];
                        }
                }
            })));
    }
}
const make = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((deferred)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(stateEmpty(deferred))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((ref)=>new SingleProducerAsyncInputImpl(ref))); //# sourceMappingURL=singleProducerAsyncInput.js.map
}}),

};

//# sourceMappingURL=node_modules_effect_dist_esm_internal_channel_ca00a533._.js.map