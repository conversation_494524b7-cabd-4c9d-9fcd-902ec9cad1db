module.exports = {

"[project]/node_modules/effect/dist/esm/Schema.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @since 3.10.0
 */ __turbopack_context__.s({
    "Any": (()=>Any),
    "Array": (()=>Array$),
    "ArrayEnsure": (()=>ArrayEnsure),
    "ArrayFormatterIssue": (()=>ArrayFormatterIssue),
    "BetweenBigDecimalSchemaId": (()=>BetweenBigDecimalSchemaId),
    "BetweenBigIntSchemaId": (()=>BetweenBigIntSchemaId),
    "BetweenDateSchemaId": (()=>BetweenDateSchemaId),
    "BetweenDurationSchemaId": (()=>BetweenDurationSchemaId),
    "BetweenSchemaId": (()=>BetweenSchemaId),
    "BigDecimal": (()=>BigDecimal),
    "BigDecimalFromNumber": (()=>BigDecimalFromNumber),
    "BigDecimalFromSelf": (()=>BigDecimalFromSelf),
    "BigInt": (()=>BigInt$),
    "BigIntFromNumber": (()=>BigIntFromNumber),
    "BigIntFromSelf": (()=>BigIntFromSelf),
    "Boolean": (()=>Boolean$),
    "BooleanFromString": (()=>BooleanFromString),
    "BooleanFromUnknown": (()=>BooleanFromUnknown),
    "BrandSchemaId": (()=>BrandSchemaId),
    "Capitalize": (()=>Capitalize),
    "Capitalized": (()=>Capitalized),
    "CapitalizedSchemaId": (()=>CapitalizedSchemaId),
    "Cause": (()=>Cause),
    "CauseFromSelf": (()=>CauseFromSelf),
    "Char": (()=>Char),
    "Chunk": (()=>Chunk),
    "ChunkFromSelf": (()=>ChunkFromSelf),
    "Class": (()=>Class),
    "Config": (()=>Config),
    "Data": (()=>Data),
    "DataFromSelf": (()=>DataFromSelf),
    "Date": (()=>Date$),
    "DateFromNumber": (()=>DateFromNumber),
    "DateFromSelf": (()=>DateFromSelf),
    "DateFromSelfSchemaId": (()=>DateFromSelfSchemaId),
    "DateFromString": (()=>DateFromString),
    "DateTimeUtc": (()=>DateTimeUtc),
    "DateTimeUtcFromDate": (()=>DateTimeUtcFromDate),
    "DateTimeUtcFromNumber": (()=>DateTimeUtcFromNumber),
    "DateTimeUtcFromSelf": (()=>DateTimeUtcFromSelf),
    "DateTimeZoned": (()=>DateTimeZoned),
    "DateTimeZonedFromSelf": (()=>DateTimeZonedFromSelf),
    "Defect": (()=>Defect),
    "Duration": (()=>Duration),
    "DurationFromMillis": (()=>DurationFromMillis),
    "DurationFromNanos": (()=>DurationFromNanos),
    "DurationFromSelf": (()=>DurationFromSelf),
    "Either": (()=>Either),
    "EitherFromSelf": (()=>EitherFromSelf),
    "EitherFromUnion": (()=>EitherFromUnion),
    "EndsWithSchemaId": (()=>EndsWithSchemaId),
    "Enums": (()=>Enums),
    "Exit": (()=>Exit),
    "ExitFromSelf": (()=>ExitFromSelf),
    "FiberId": (()=>FiberId),
    "FiberIdFromSelf": (()=>FiberIdFromSelf),
    "Finite": (()=>Finite),
    "FiniteSchemaId": (()=>FiniteSchemaId),
    "FromPropertySignature": (()=>FromPropertySignature),
    "GreaterThanBigDecimalSchemaId": (()=>GreaterThanBigDecimalSchemaId),
    "GreaterThanBigIntSchemaId": (()=>GreaterThanBigIntSchemaId),
    "GreaterThanDateSchemaId": (()=>GreaterThanDateSchemaId),
    "GreaterThanDurationSchemaId": (()=>GreaterThanDurationSchemaId),
    "GreaterThanOrEqualToBigDecimalSchemaId": (()=>GreaterThanOrEqualToBigDecimalSchemaId),
    "GreaterThanOrEqualToBigIntSchemaId": (()=>GreaterThanOrEqualToBigIntSchemaId),
    "GreaterThanOrEqualToDateSchemaId": (()=>GreaterThanOrEqualToDateSchemaId),
    "GreaterThanOrEqualToDurationSchemaId": (()=>GreaterThanOrEqualToDurationSchemaId),
    "GreaterThanOrEqualToSchemaId": (()=>GreaterThanOrEqualToSchemaId),
    "GreaterThanSchemaId": (()=>GreaterThanSchemaId),
    "HashMap": (()=>HashMap),
    "HashMapFromSelf": (()=>HashMapFromSelf),
    "HashSet": (()=>HashSet),
    "HashSetFromSelf": (()=>HashSetFromSelf),
    "IncludesSchemaId": (()=>IncludesSchemaId),
    "InstanceOfSchemaId": (()=>InstanceOfSchemaId),
    "Int": (()=>Int),
    "IntSchemaId": (()=>IntSchemaId),
    "ItemsCountSchemaId": (()=>ItemsCountSchemaId),
    "JsonNumber": (()=>JsonNumber),
    "JsonNumberSchemaId": (()=>JsonNumberSchemaId),
    "LengthSchemaId": (()=>LengthSchemaId),
    "LessThanBigDecimalSchemaId": (()=>LessThanBigDecimalSchemaId),
    "LessThanBigIntSchemaId": (()=>LessThanBigIntSchemaId),
    "LessThanDateSchemaId": (()=>LessThanDateSchemaId),
    "LessThanDurationSchemaId": (()=>LessThanDurationSchemaId),
    "LessThanOrEqualToBigDecimalSchemaId": (()=>LessThanOrEqualToBigDecimalSchemaId),
    "LessThanOrEqualToBigIntSchemaId": (()=>LessThanOrEqualToBigIntSchemaId),
    "LessThanOrEqualToDateSchemaId": (()=>LessThanOrEqualToDateSchemaId),
    "LessThanOrEqualToDurationSchemaId": (()=>LessThanOrEqualToDurationSchemaId),
    "LessThanOrEqualToSchemaId": (()=>LessThanOrEqualToSchemaId),
    "LessThanSchemaId": (()=>LessThanSchemaId),
    "List": (()=>List),
    "ListFromSelf": (()=>ListFromSelf),
    "Literal": (()=>Literal),
    "Lowercase": (()=>Lowercase),
    "Lowercased": (()=>Lowercased),
    "LowercasedSchemaId": (()=>LowercasedSchemaId),
    "Map": (()=>map),
    "MapFromRecord": (()=>MapFromRecord),
    "MapFromSelf": (()=>MapFromSelf),
    "MaxItemsSchemaId": (()=>MaxItemsSchemaId),
    "MaxLengthSchemaId": (()=>MaxLengthSchemaId),
    "MinItemsSchemaId": (()=>MinItemsSchemaId),
    "MinLengthSchemaId": (()=>MinLengthSchemaId),
    "MultipleOfSchemaId": (()=>MultipleOfSchemaId),
    "Negative": (()=>Negative),
    "NegativeBigDecimalFromSelf": (()=>NegativeBigDecimalFromSelf),
    "NegativeBigDecimalSchemaId": (()=>NegativeBigDecimalSchemaId),
    "NegativeBigInt": (()=>NegativeBigInt),
    "NegativeBigIntFromSelf": (()=>NegativeBigIntFromSelf),
    "Never": (()=>Never),
    "NonEmptyArray": (()=>NonEmptyArray),
    "NonEmptyArrayEnsure": (()=>NonEmptyArrayEnsure),
    "NonEmptyChunk": (()=>NonEmptyChunk),
    "NonEmptyChunkFromSelf": (()=>NonEmptyChunkFromSelf),
    "NonEmptyString": (()=>NonEmptyString),
    "NonEmptyTrimmedString": (()=>NonEmptyTrimmedString),
    "NonNaN": (()=>NonNaN),
    "NonNaNSchemaId": (()=>NonNaNSchemaId),
    "NonNegative": (()=>NonNegative),
    "NonNegativeBigDecimalFromSelf": (()=>NonNegativeBigDecimalFromSelf),
    "NonNegativeBigDecimalSchemaId": (()=>NonNegativeBigDecimalSchemaId),
    "NonNegativeBigInt": (()=>NonNegativeBigInt),
    "NonNegativeBigIntFromSelf": (()=>NonNegativeBigIntFromSelf),
    "NonNegativeInt": (()=>NonNegativeInt),
    "NonPositive": (()=>NonPositive),
    "NonPositiveBigDecimalFromSelf": (()=>NonPositiveBigDecimalFromSelf),
    "NonPositiveBigDecimalSchemaId": (()=>NonPositiveBigDecimalSchemaId),
    "NonPositiveBigInt": (()=>NonPositiveBigInt),
    "NonPositiveBigIntFromSelf": (()=>NonPositiveBigIntFromSelf),
    "Not": (()=>Not),
    "Null": (()=>Null),
    "NullOr": (()=>NullOr),
    "NullishOr": (()=>NullishOr),
    "Number": (()=>Number$),
    "NumberFromString": (()=>NumberFromString),
    "Object": (()=>Object$),
    "Option": (()=>Option),
    "OptionFromNonEmptyTrimmedString": (()=>OptionFromNonEmptyTrimmedString),
    "OptionFromNullOr": (()=>OptionFromNullOr),
    "OptionFromNullishOr": (()=>OptionFromNullishOr),
    "OptionFromSelf": (()=>OptionFromSelf),
    "OptionFromUndefinedOr": (()=>OptionFromUndefinedOr),
    "PatternSchemaId": (()=>PatternSchemaId),
    "Positive": (()=>Positive),
    "PositiveBigDecimalFromSelf": (()=>PositiveBigDecimalFromSelf),
    "PositiveBigDecimalSchemaId": (()=>PositiveBigDecimalSchemaId),
    "PositiveBigInt": (()=>PositiveBigInt),
    "PositiveBigIntFromSelf": (()=>PositiveBigIntFromSelf),
    "PropertyKey": (()=>PropertyKey$),
    "PropertySignatureDeclaration": (()=>PropertySignatureDeclaration),
    "PropertySignatureTransformation": (()=>PropertySignatureTransformation),
    "PropertySignatureTypeId": (()=>PropertySignatureTypeId),
    "ReadonlyMap": (()=>ReadonlyMap),
    "ReadonlyMapFromRecord": (()=>ReadonlyMapFromRecord),
    "ReadonlyMapFromSelf": (()=>ReadonlyMapFromSelf),
    "ReadonlySet": (()=>ReadonlySet),
    "ReadonlySetFromSelf": (()=>ReadonlySetFromSelf),
    "Record": (()=>Record),
    "Redacted": (()=>Redacted),
    "RedactedFromSelf": (()=>RedactedFromSelf),
    "RefineSchemaId": (()=>RefineSchemaId),
    "Set": (()=>set),
    "SetFromSelf": (()=>SetFromSelf),
    "SortedSet": (()=>SortedSet),
    "SortedSetFromSelf": (()=>SortedSetFromSelf),
    "StartsWithSchemaId": (()=>StartsWithSchemaId),
    "String": (()=>String$),
    "StringFromBase64": (()=>StringFromBase64),
    "StringFromBase64Url": (()=>StringFromBase64Url),
    "StringFromHex": (()=>StringFromHex),
    "StringFromUriComponent": (()=>StringFromUriComponent),
    "Struct": (()=>Struct),
    "Symbol": (()=>Symbol$),
    "SymbolFromSelf": (()=>SymbolFromSelf),
    "TaggedClass": (()=>TaggedClass),
    "TaggedError": (()=>TaggedError),
    "TaggedRequest": (()=>TaggedRequest),
    "TaggedStruct": (()=>TaggedStruct),
    "TemplateLiteral": (()=>TemplateLiteral),
    "TemplateLiteralParser": (()=>TemplateLiteralParser),
    "TimeZone": (()=>TimeZone),
    "TimeZoneFromSelf": (()=>TimeZoneFromSelf),
    "TimeZoneNamed": (()=>TimeZoneNamed),
    "TimeZoneNamedFromSelf": (()=>TimeZoneNamedFromSelf),
    "TimeZoneOffset": (()=>TimeZoneOffset),
    "TimeZoneOffsetFromSelf": (()=>TimeZoneOffsetFromSelf),
    "ToPropertySignature": (()=>ToPropertySignature),
    "Trim": (()=>Trim),
    "Trimmed": (()=>Trimmed),
    "TrimmedSchemaId": (()=>TrimmedSchemaId),
    "Tuple": (()=>Tuple),
    "TypeId": (()=>TypeId),
    "ULID": (()=>ULID),
    "ULIDSchemaId": (()=>ULIDSchemaId),
    "URL": (()=>URL$),
    "URLFromSelf": (()=>URLFromSelf),
    "UUID": (()=>UUID),
    "UUIDSchemaId": (()=>UUIDSchemaId),
    "Uint8": (()=>Uint8),
    "Uint8Array": (()=>Uint8Array$),
    "Uint8ArrayFromBase64": (()=>Uint8ArrayFromBase64),
    "Uint8ArrayFromBase64Url": (()=>Uint8ArrayFromBase64Url),
    "Uint8ArrayFromHex": (()=>Uint8ArrayFromHex),
    "Uint8ArrayFromSelf": (()=>Uint8ArrayFromSelf),
    "Uncapitalize": (()=>Uncapitalize),
    "Uncapitalized": (()=>Uncapitalized),
    "UncapitalizedSchemaId": (()=>UncapitalizedSchemaId),
    "Undefined": (()=>Undefined),
    "UndefinedOr": (()=>UndefinedOr),
    "Union": (()=>Union),
    "UniqueSymbolFromSelf": (()=>UniqueSymbolFromSelf),
    "Unknown": (()=>Unknown),
    "Uppercase": (()=>Uppercase),
    "Uppercased": (()=>Uppercased),
    "UppercasedSchemaId": (()=>UppercasedSchemaId),
    "ValidDateFromSelf": (()=>ValidDateFromSelf),
    "ValidDateSchemaId": (()=>ValidDateSchemaId),
    "Void": (()=>Void),
    "annotations": (()=>annotations),
    "asSchema": (()=>asSchema),
    "asSerializable": (()=>asSerializable),
    "asSerializableWithResult": (()=>asSerializableWithResult),
    "asWithResult": (()=>asWithResult),
    "attachPropertySignature": (()=>attachPropertySignature),
    "between": (()=>between),
    "betweenBigDecimal": (()=>betweenBigDecimal),
    "betweenBigInt": (()=>betweenBigInt),
    "betweenDate": (()=>betweenDate),
    "betweenDuration": (()=>betweenDuration),
    "brand": (()=>brand),
    "capitalized": (()=>capitalized),
    "clamp": (()=>clamp),
    "clampBigDecimal": (()=>clampBigDecimal),
    "clampBigInt": (()=>clampBigInt),
    "clampDuration": (()=>clampDuration),
    "compose": (()=>compose),
    "declare": (()=>declare),
    "decode": (()=>decode),
    "decodeEither": (()=>decodeEither),
    "decodePromise": (()=>decodePromise),
    "decodeUnknown": (()=>decodeUnknown),
    "decodeUnknownEither": (()=>decodeUnknownEither),
    "decodeUnknownPromise": (()=>decodeUnknownPromise),
    "deserialize": (()=>deserialize),
    "deserializeExit": (()=>deserializeExit),
    "deserializeFailure": (()=>deserializeFailure),
    "deserializeSuccess": (()=>deserializeSuccess),
    "element": (()=>element),
    "encode": (()=>encode),
    "encodeEither": (()=>encodeEither),
    "encodePromise": (()=>encodePromise),
    "encodeUnknown": (()=>encodeUnknown),
    "encodeUnknownEither": (()=>encodeUnknownEither),
    "encodeUnknownPromise": (()=>encodeUnknownPromise),
    "encodedBoundSchema": (()=>encodedBoundSchema),
    "encodedSchema": (()=>encodedSchema),
    "endsWith": (()=>endsWith),
    "equivalence": (()=>equivalence),
    "exitSchema": (()=>exitSchema),
    "extend": (()=>extend),
    "failureSchema": (()=>failureSchema),
    "filter": (()=>filter),
    "filterEffect": (()=>filterEffect),
    "finite": (()=>finite),
    "format": (()=>format),
    "fromBrand": (()=>fromBrand),
    "fromKey": (()=>fromKey),
    "getClassTag": (()=>getClassTag),
    "getNumberIndexedAccess": (()=>getNumberIndexedAccess),
    "greaterThan": (()=>greaterThan),
    "greaterThanBigDecimal": (()=>greaterThanBigDecimal),
    "greaterThanBigInt": (()=>greaterThanBigInt),
    "greaterThanDate": (()=>greaterThanDate),
    "greaterThanDuration": (()=>greaterThanDuration),
    "greaterThanOrEqualTo": (()=>greaterThanOrEqualTo),
    "greaterThanOrEqualToBigDecimal": (()=>greaterThanOrEqualToBigDecimal),
    "greaterThanOrEqualToBigInt": (()=>greaterThanOrEqualToBigInt),
    "greaterThanOrEqualToDate": (()=>greaterThanOrEqualToDate),
    "greaterThanOrEqualToDuration": (()=>greaterThanOrEqualToDuration),
    "head": (()=>head),
    "headNonEmpty": (()=>headNonEmpty),
    "headOrElse": (()=>headOrElse),
    "includes": (()=>includes),
    "instanceOf": (()=>instanceOf),
    "int": (()=>int),
    "isPropertySignature": (()=>isPropertySignature),
    "isSchema": (()=>isSchema),
    "itemsCount": (()=>itemsCount),
    "keyof": (()=>keyof),
    "length": (()=>length),
    "lessThan": (()=>lessThan),
    "lessThanBigDecimal": (()=>lessThanBigDecimal),
    "lessThanBigInt": (()=>lessThanBigInt),
    "lessThanDate": (()=>lessThanDate),
    "lessThanDuration": (()=>lessThanDuration),
    "lessThanOrEqualTo": (()=>lessThanOrEqualTo),
    "lessThanOrEqualToBigDecimal": (()=>lessThanOrEqualToBigDecimal),
    "lessThanOrEqualToBigInt": (()=>lessThanOrEqualToBigInt),
    "lessThanOrEqualToDate": (()=>lessThanOrEqualToDate),
    "lessThanOrEqualToDuration": (()=>lessThanOrEqualToDuration),
    "lowercased": (()=>lowercased),
    "make": (()=>make),
    "makePropertySignature": (()=>makePropertySignature),
    "maxItems": (()=>maxItems),
    "maxLength": (()=>maxLength),
    "minItems": (()=>minItems),
    "minLength": (()=>minLength),
    "multipleOf": (()=>multipleOf),
    "mutable": (()=>mutable),
    "negative": (()=>negative),
    "negativeBigDecimal": (()=>negativeBigDecimal),
    "negativeBigInt": (()=>negativeBigInt),
    "nonEmptyString": (()=>nonEmptyString),
    "nonNaN": (()=>nonNaN),
    "nonNegative": (()=>nonNegative),
    "nonNegativeBigDecimal": (()=>nonNegativeBigDecimal),
    "nonNegativeBigInt": (()=>nonNegativeBigInt),
    "nonPositive": (()=>nonPositive),
    "nonPositiveBigDecimal": (()=>nonPositiveBigDecimal),
    "nonPositiveBigInt": (()=>nonPositiveBigInt),
    "omit": (()=>omit),
    "optional": (()=>optional),
    "optionalElement": (()=>optionalElement),
    "optionalToOptional": (()=>optionalToOptional),
    "optionalToRequired": (()=>optionalToRequired),
    "optionalWith": (()=>optionalWith),
    "parseJson": (()=>parseJson),
    "parseNumber": (()=>parseNumber),
    "partial": (()=>partial),
    "partialWith": (()=>partialWith),
    "pattern": (()=>pattern),
    "pick": (()=>pick),
    "pickLiteral": (()=>pickLiteral),
    "pluck": (()=>pluck),
    "positive": (()=>positive),
    "positiveBigDecimal": (()=>positiveBigDecimal),
    "positiveBigInt": (()=>positiveBigInt),
    "propertySignature": (()=>propertySignature),
    "rename": (()=>rename),
    "required": (()=>required),
    "requiredToOptional": (()=>requiredToOptional),
    "serializableSchema": (()=>serializableSchema),
    "serialize": (()=>serialize),
    "serializeExit": (()=>serializeExit),
    "serializeFailure": (()=>serializeFailure),
    "serializeSuccess": (()=>serializeSuccess),
    "split": (()=>split),
    "standardSchemaV1": (()=>standardSchemaV1),
    "startsWith": (()=>startsWith),
    "successSchema": (()=>successSchema),
    "suspend": (()=>suspend),
    "symbolSerializable": (()=>symbolSerializable),
    "symbolWithResult": (()=>symbolWithResult),
    "tag": (()=>tag),
    "transform": (()=>transform),
    "transformLiteral": (()=>transformLiteral),
    "transformLiterals": (()=>transformLiterals),
    "transformOrFail": (()=>transformOrFail),
    "trimmed": (()=>trimmed),
    "typeSchema": (()=>typeSchema),
    "uncapitalized": (()=>uncapitalized),
    "uppercased": (()=>uppercased),
    "validDate": (()=>validDate),
    "validate": (()=>validate),
    "validateEither": (()=>validateEither),
    "validatePromise": (()=>validatePromise),
    "withConstructorDefault": (()=>withConstructorDefault),
    "withDecodingDefault": (()=>withDecodingDefault),
    "withDefaults": (()=>withDefaults)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Array.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/BigDecimal.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigInt$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/BigInt.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Boolean$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Boolean.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Cause.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Chunk.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Config$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Config.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ConfigError$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/ConfigError.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Data$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Data.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/DateTime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Duration.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Either.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Encoding.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Equal.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Equivalence.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Exit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fast$2d$check$2f$lib$2f$esm$2f$arbitrary$2f$array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fast-check/lib/esm/arbitrary/array.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/FiberId.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/GlobalValue.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/HashMap.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/HashSet.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/cause.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$errors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/schema/errors.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/schema/schemaId.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/schema/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$List$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/List.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Number$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Number.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Option.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/ParseResult.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Pipeable.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Redacted$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Redacted.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Request$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Request.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scheduler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Scheduler.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/SchemaAST.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SortedSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/SortedSet.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$String$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/String.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Struct$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Struct.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const TypeId = /*#__PURE__*/ Symbol.for("effect/Schema");
function make(ast) {
    return class SchemaClass {
        [TypeId] = variance;
        static ast = ast;
        static annotations(annotations) {
            return make(mergeSchemaAnnotations(this.ast, annotations));
        }
        static pipe() {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
        }
        static toString() {
            return String(ast);
        }
        static Type;
        static Encoded;
        static Context;
        static [TypeId] = variance;
    };
}
const variance = {
    /* c8 ignore next */ _A: (_)=>_,
    /* c8 ignore next */ _I: (_)=>_,
    /* c8 ignore next */ _R: (_)=>_
};
const makeStandardResult = (exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSuccess"])(exit) ? exit.value : makeStandardFailureResult((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pretty"])(exit.cause));
const makeStandardFailureResult = (message)=>({
        issues: [
            {
                message
            }
        ]
    });
const makeStandardFailureFromParseIssue = (issue)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ArrayFormatter"].formatIssue(issue), (issues)=>({
            issues: issues.map((issue)=>({
                    path: issue.path,
                    message: issue.message
                }))
        }));
const standardSchemaV1 = (schema, overrideOptions)=>{
    const decodeUnknown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(schema, {
        errors: "all"
    });
    return class StandardSchemaV1Class extends make(schema.ast) {
        static "~standard" = {
            version: 1,
            vendor: "effect",
            validate (value) {
                const scheduler = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scheduler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SyncScheduler"]();
                const fiber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runFork"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchEffect"])(decodeUnknown(value, overrideOptions), {
                    onFailure: makeStandardFailureFromParseIssue,
                    onSuccess: (value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])({
                            value
                        })
                }), {
                    scheduler
                });
                scheduler.flush();
                const exit = fiber.unsafePoll();
                if (exit) {
                    return makeStandardResult(exit);
                }
                return new Promise((resolve)=>{
                    fiber.addObserver((exit)=>{
                        resolve(makeStandardResult(exit));
                    });
                });
            }
        };
    };
};
const builtInAnnotations = {
    schemaId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SchemaIdAnnotationId"],
    message: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MessageAnnotationId"],
    missingMessage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MissingMessageAnnotationId"],
    identifier: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["IdentifierAnnotationId"],
    title: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TitleAnnotationId"],
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DescriptionAnnotationId"],
    examples: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExamplesAnnotationId"],
    default: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DefaultAnnotationId"],
    documentation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DocumentationAnnotationId"],
    jsonSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JSONSchemaAnnotationId"],
    arbitrary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ArbitraryAnnotationId"],
    pretty: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PrettyAnnotationId"],
    equivalence: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EquivalenceAnnotationId"],
    concurrency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ConcurrencyAnnotationId"],
    batching: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BatchingAnnotationId"],
    parseIssueTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ParseIssueTitleAnnotationId"],
    parseOptions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ParseOptionsAnnotationId"],
    decodingFallback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DecodingFallbackAnnotationId"]
};
const toASTAnnotations = (annotations)=>{
    if (!annotations) {
        return {};
    }
    const out = {
        ...annotations
    };
    for(const key in builtInAnnotations){
        if (key in annotations) {
            const id = builtInAnnotations[key];
            out[id] = annotations[key];
            delete out[key];
        }
    }
    return out;
};
const mergeSchemaAnnotations = (ast, annotations)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["annotations"])(ast, toASTAnnotations(annotations));
function asSchema(schema) {
    return schema;
}
const format = (schema)=>String(schema.ast);
const encodedSchema = (schema)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodedAST"])(schema.ast));
const encodedBoundSchema = (schema)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodedBoundAST"])(schema.ast));
const typeSchema = (schema)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["typeAST"])(schema.ast));
;
const encodeUnknown = (schema, options)=>{
    const encodeUnknown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(schema, options);
    return (u, overrideOptions)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapError"])(encodeUnknown(u, overrideOptions), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseError"]);
};
const encodeUnknownEither = (schema, options)=>{
    const encodeUnknownEither = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknownEither"])(schema, options);
    return (u, overrideOptions)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapLeft"])(encodeUnknownEither(u, overrideOptions), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseError"]);
};
const encodeUnknownPromise = (schema, options)=>{
    const parser = encodeUnknown(schema, options);
    return (u, overrideOptions)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runPromise"])(parser(u, overrideOptions));
};
const encode = encodeUnknown;
const encodeEither = encodeUnknownEither;
const encodePromise = encodeUnknownPromise;
const decodeUnknown = (schema, options)=>{
    const decodeUnknown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(schema, options);
    return (u, overrideOptions)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapError"])(decodeUnknown(u, overrideOptions), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseError"]);
};
const decodeUnknownEither = (schema, options)=>{
    const decodeUnknownEither = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknownEither"])(schema, options);
    return (u, overrideOptions)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapLeft"])(decodeUnknownEither(u, overrideOptions), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseError"]);
};
const decodeUnknownPromise = (schema, options)=>{
    const parser = decodeUnknown(schema, options);
    return (u, overrideOptions)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runPromise"])(parser(u, overrideOptions));
};
const decode = decodeUnknown;
const decodeEither = decodeUnknownEither;
const decodePromise = decodeUnknownPromise;
const validate = (schema, options)=>{
    const validate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validate"])(schema, options);
    return (u, overrideOptions)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapError"])(validate(u, overrideOptions), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseError"]);
};
const validateEither = (schema, options)=>{
    const validateEither = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateEither"])(schema, options);
    return (u, overrideOptions)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapLeft"])(validateEither(u, overrideOptions), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseError"]);
};
const validatePromise = (schema, options)=>{
    const parser = validate(schema, options);
    return (u, overrideOptions)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runPromise"])(parser(u, overrideOptions));
};
const isSchema = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, TypeId) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isObject"])(u[TypeId]);
function getDefaultLiteralAST(literals) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isMembers"])(literals) ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Union"].make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapMembers"])(literals, (literal)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Literal"](literal))) : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Literal"](literals[0]);
}
function makeLiteralClass(literals, ast = getDefaultLiteralAST(literals)) {
    return class LiteralClass extends make(ast) {
        static annotations(annotations) {
            return makeLiteralClass(this.literals, mergeSchemaAnnotations(this.ast, annotations));
        }
        static literals = [
            ...literals
        ];
    };
}
function Literal(...literals) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmptyReadonlyArray"])(literals) ? makeLiteralClass(literals) : Never;
}
const pickLiteral = (...literals)=>(_schema)=>Literal(...literals);
const UniqueSymbolFromSelf = (symbol)=>make(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["UniqueSymbol"](symbol));
const getDefaultEnumsAST = (enums)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Enums"](Object.keys(enums).filter((key)=>typeof enums[enums[key]] !== "number").map((key)=>[
            key,
            enums[key]
        ]));
const makeEnumsClass = (enums, ast = getDefaultEnumsAST(enums))=>class EnumsClass extends make(ast) {
        static annotations(annotations) {
            return makeEnumsClass(this.enums, mergeSchemaAnnotations(this.ast, annotations));
        }
        static enums = {
            ...enums
        };
    };
const Enums = (enums)=>makeEnumsClass(enums);
const TemplateLiteral = (...[head, ...tail])=>{
    const spans = [];
    let h = "";
    let ts = tail;
    if (isSchema(head)) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isLiteral"])(head.ast)) {
            h = String(head.ast.literal);
        } else {
            ts = [
                head,
                ...ts
            ];
        }
    } else {
        h = String(head);
    }
    for(let i = 0; i < ts.length; i++){
        const item = ts[i];
        if (isSchema(item)) {
            if (i < ts.length - 1) {
                const next = ts[i + 1];
                if (isSchema(next)) {
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isLiteral"])(next.ast)) {
                        spans.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TemplateLiteralSpan"](item.ast, String(next.ast.literal)));
                        i++;
                        continue;
                    }
                } else {
                    spans.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TemplateLiteralSpan"](item.ast, String(next)));
                    i++;
                    continue;
                }
            }
            spans.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TemplateLiteralSpan"](item.ast, ""));
        } else {
            spans.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TemplateLiteralSpan"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Literal"](item), ""));
        }
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmptyArray"])(spans)) {
        return make(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TemplateLiteral"](h, spans));
    } else {
        return make(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TemplateLiteral"]("", [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TemplateLiteralSpan"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Literal"](h), "")
        ]));
    }
};
function getTemplateLiteralParserCoercedElement(encoded, schema) {
    const ast = encoded.ast;
    switch(ast._tag){
        case "Literal":
            {
                const literal = ast.literal;
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isString"])(literal)) {
                    const s = String(literal);
                    return transform(Literal(s), schema, {
                        strict: true,
                        decode: ()=>literal,
                        encode: ()=>s
                    });
                }
                break;
            }
        case "NumberKeyword":
            return compose(NumberFromString, schema);
        case "Union":
            {
                const members = [];
                let hasCoercions = false;
                for (const member of ast.types){
                    const schema = make(member);
                    const encoded = encodedSchema(schema);
                    const coerced = getTemplateLiteralParserCoercedElement(encoded, schema);
                    if (coerced) {
                        hasCoercions = true;
                    }
                    members.push(coerced ?? schema);
                }
                return hasCoercions ? compose(Union(...members), schema) : schema;
            }
    }
}
const TemplateLiteralParser = (...params)=>{
    const encodedSchemas = [];
    const elements = [];
    const schemas = [];
    let coerced = false;
    for(let i = 0; i < params.length; i++){
        const param = params[i];
        const schema = isSchema(param) ? param : Literal(param);
        schemas.push(schema);
        const encoded = encodedSchema(schema);
        encodedSchemas.push(encoded);
        const element = getTemplateLiteralParserCoercedElement(encoded, schema);
        if (element) {
            elements.push(element);
            coerced = true;
        } else {
            elements.push(schema);
        }
    }
    const from = TemplateLiteral(...encodedSchemas);
    const re = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getTemplateLiteralCapturingRegExp"])(from.ast);
    let to = Tuple(...elements);
    if (coerced) {
        to = to.annotations({
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AutoTitleAnnotationId"]]: format(Tuple(...schemas))
        });
    }
    return class TemplateLiteralParserClass extends transformOrFail(from, to, {
        strict: false,
        decode: (i, _, ast)=>{
            const match = re.exec(i);
            return match ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(match.slice(1, params.length + 1)) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, `${re.source}: no match for ${JSON.stringify(i)}`));
        },
        encode: (tuple)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(tuple.join(""))
    }) {
        static params = params.slice();
    };
};
const declareConstructor = (typeParameters, options, annotations)=>makeDeclareClass(typeParameters, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Declaration"](typeParameters.map((tp)=>tp.ast), (...typeParameters)=>options.decode(...typeParameters.map(make)), (...typeParameters)=>options.encode(...typeParameters.map(make)), toASTAnnotations(annotations)));
const declarePrimitive = (is, annotations)=>{
    const decodeUnknown = ()=>(input, _, ast)=>is(input) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(input) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, input));
    const encodeUnknown = decodeUnknown;
    return makeDeclareClass([], new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Declaration"]([], decodeUnknown, encodeUnknown, toASTAnnotations(annotations)));
};
function makeDeclareClass(typeParameters, ast) {
    return class DeclareClass extends make(ast) {
        static annotations(annotations) {
            return makeDeclareClass(this.typeParameters, mergeSchemaAnnotations(this.ast, annotations));
        }
        static typeParameters = [
            ...typeParameters
        ];
    };
}
const declare = function() {
    if (Array.isArray(arguments[0])) {
        const typeParameters = arguments[0];
        const options = arguments[1];
        const annotations = arguments[2];
        return declareConstructor(typeParameters, options, annotations);
    }
    const is = arguments[0];
    const annotations = arguments[1];
    return declarePrimitive(is, annotations);
};
const BrandSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/Brand");
const fromBrand = (constructor, annotations)=>(self)=>{
        const out = makeBrandClass(self, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Refinement"](self.ast, function predicate(a, _, ast) {
            const either = constructor.either(a);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isLeft"])(either) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, a, either.left.map((v)=>v.message).join(", "))) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
        }, toASTAnnotations({
            schemaId: BrandSchemaId,
            [BrandSchemaId]: {
                constructor
            },
            ...annotations
        })));
        return out;
    };
const InstanceOfSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/InstanceOf");
const instanceOf = (constructor, annotations)=>declare((u)=>u instanceof constructor, {
        title: constructor.name,
        description: `an instance of ${constructor.name}`,
        pretty: ()=>String,
        schemaId: InstanceOfSchemaId,
        [InstanceOfSchemaId]: {
            constructor
        },
        ...annotations
    });
class Undefined extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["undefinedKeyword"]) {
}
class Void extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["voidKeyword"]) {
}
class Null extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["null"]) {
}
class Never extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["neverKeyword"]) {
}
class Unknown extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unknownKeyword"]) {
}
class Any extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["anyKeyword"]) {
}
class BigIntFromSelf extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["bigIntKeyword"]) {
}
class SymbolFromSelf extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbolKeyword"]) {
}
/** @ignore */ class String$ extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["stringKeyword"]) {
}
/** @ignore */ class Number$ extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["numberKeyword"]) {
}
/** @ignore */ class Boolean$ extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["booleanKeyword"]) {
}
/** @ignore */ class Object$ extends /*#__PURE__*/ make(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["objectKeyword"]) {
}
;
const getDefaultUnionAST = (members)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Union"].make(members.map((m)=>m.ast));
function makeUnionClass(members, ast = getDefaultUnionAST(members)) {
    return class UnionClass extends make(ast) {
        static annotations(annotations) {
            return makeUnionClass(this.members, mergeSchemaAnnotations(this.ast, annotations));
        }
        static members = [
            ...members
        ];
    };
}
function Union(...members) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isMembers"])(members) ? makeUnionClass(members) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmptyReadonlyArray"])(members) ? members[0] : Never;
}
const NullOr = (self)=>Union(self, Null);
const UndefinedOr = (self)=>Union(self, Undefined);
const NullishOr = (self)=>Union(self, Null, Undefined);
const keyof = (self)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keyof"])(self.ast));
const element = (self)=>new ElementImpl(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OptionalType"](self.ast, false), self);
const optionalElement = (self)=>new ElementImpl(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OptionalType"](self.ast, true), self);
class ElementImpl {
    ast;
    from;
    [TypeId];
    _Token;
    constructor(ast, from){
        this.ast = ast;
        this.from = from;
    }
    annotations(annotations) {
        return new ElementImpl(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OptionalType"](this.ast.type, this.ast.isOptional, {
            ...this.ast.annotations,
            ...toASTAnnotations(annotations)
        }), this.from);
    }
    toString() {
        return `${this.ast.type}${this.ast.isOptional ? "?" : ""}`;
    }
}
const getDefaultTupleTypeAST = (elements, rest)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TupleType"](elements.map((el)=>isSchema(el) ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OptionalType"](el.ast, false) : el.ast), rest.map((el)=>isSchema(el) ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](el.ast) : el.ast), true);
function makeTupleTypeClass(elements, rest, ast = getDefaultTupleTypeAST(elements, rest)) {
    return class TupleTypeClass extends make(ast) {
        static annotations(annotations) {
            return makeTupleTypeClass(this.elements, this.rest, mergeSchemaAnnotations(this.ast, annotations));
        }
        static elements = [
            ...elements
        ];
        static rest = [
            ...rest
        ];
    };
}
function Tuple(...args) {
    return Array.isArray(args[0]) ? makeTupleTypeClass(args[0], args.slice(1)) : makeTupleTypeClass(args, []);
}
function makeArrayClass(value, ast) {
    return class ArrayClass extends makeTupleTypeClass([], [
        value
    ], ast) {
        static annotations(annotations) {
            return makeArrayClass(this.value, mergeSchemaAnnotations(this.ast, annotations));
        }
        static value = value;
    };
}
const Array$ = (value)=>makeArrayClass(value);
;
function makeNonEmptyArrayClass(value, ast) {
    return class NonEmptyArrayClass extends makeTupleTypeClass([
        value
    ], [
        value
    ], ast) {
        static annotations(annotations) {
            return makeNonEmptyArrayClass(this.value, mergeSchemaAnnotations(this.ast, annotations));
        }
        static value = value;
    };
}
const NonEmptyArray = (value)=>makeNonEmptyArrayClass(value);
function ArrayEnsure(value) {
    return transform(Union(value, Array$(value)), Array$(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensure"])(i),
        encode: (a)=>a.length === 1 ? a[0] : a
    });
}
function NonEmptyArrayEnsure(value) {
    return transform(Union(value, NonEmptyArray(value)), NonEmptyArray(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmptyReadonlyArray"])(i) ? i : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["of"])(i),
        encode: (a)=>a.length === 1 ? a[0] : a
    });
}
const formatPropertySignatureToken = (isOptional)=>isOptional ? "\"?:\"" : "\":\"";
class PropertySignatureDeclaration extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OptionalType"] {
    isReadonly;
    defaultValue;
    /**
   * @since 3.10.0
   */ _tag = "PropertySignatureDeclaration";
    constructor(type, isOptional, isReadonly, annotations, defaultValue){
        super(type, isOptional, annotations);
        this.isReadonly = isReadonly;
        this.defaultValue = defaultValue;
    }
    /**
   * @since 3.10.0
   */ toString() {
        const token = formatPropertySignatureToken(this.isOptional);
        const type = String(this.type);
        return `PropertySignature<${token}, ${type}, never, ${token}, ${type}>`;
    }
}
class FromPropertySignature extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OptionalType"] {
    isReadonly;
    fromKey;
    constructor(type, isOptional, isReadonly, annotations, fromKey){
        super(type, isOptional, annotations);
        this.isReadonly = isReadonly;
        this.fromKey = fromKey;
    }
}
class ToPropertySignature extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OptionalType"] {
    isReadonly;
    defaultValue;
    constructor(type, isOptional, isReadonly, annotations, defaultValue){
        super(type, isOptional, annotations);
        this.isReadonly = isReadonly;
        this.defaultValue = defaultValue;
    }
}
const formatPropertyKey = (p)=>{
    if (p === undefined) {
        return "never";
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isString"])(p)) {
        return JSON.stringify(p);
    }
    return String(p);
};
class PropertySignatureTransformation {
    from;
    to;
    decode;
    encode;
    /**
   * @since 3.10.0
   */ _tag = "PropertySignatureTransformation";
    constructor(from, to, decode, encode){
        this.from = from;
        this.to = to;
        this.decode = decode;
        this.encode = encode;
    }
    /**
   * @since 3.10.0
   */ toString() {
        return `PropertySignature<${formatPropertySignatureToken(this.to.isOptional)}, ${this.to.type}, ${formatPropertyKey(this.from.fromKey)}, ${formatPropertySignatureToken(this.from.isOptional)}, ${this.from.type}>`;
    }
}
const mergeSignatureAnnotations = (ast, annotations)=>{
    switch(ast._tag){
        case "PropertySignatureDeclaration":
            {
                return new PropertySignatureDeclaration(ast.type, ast.isOptional, ast.isReadonly, {
                    ...ast.annotations,
                    ...annotations
                }, ast.defaultValue);
            }
        case "PropertySignatureTransformation":
            {
                return new PropertySignatureTransformation(ast.from, new ToPropertySignature(ast.to.type, ast.to.isOptional, ast.to.isReadonly, {
                    ...ast.to.annotations,
                    ...annotations
                }, ast.to.defaultValue), ast.decode, ast.encode);
            }
    }
};
const PropertySignatureTypeId = /*#__PURE__*/ Symbol.for("effect/PropertySignature");
const isPropertySignature = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, PropertySignatureTypeId);
class PropertySignatureImpl {
    ast;
    [TypeId];
    [PropertySignatureTypeId] = null;
    _TypeToken;
    _Key;
    _EncodedToken;
    _HasDefault;
    constructor(ast){
        this.ast = ast;
    }
    pipe() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
    annotations(annotations) {
        return new PropertySignatureImpl(mergeSignatureAnnotations(this.ast, toASTAnnotations(annotations)));
    }
    toString() {
        return String(this.ast);
    }
}
const makePropertySignature = (ast)=>new PropertySignatureImpl(ast);
class PropertySignatureWithFromImpl extends PropertySignatureImpl {
    from;
    constructor(ast, from){
        super(ast);
        this.from = from;
    }
    annotations(annotations) {
        return new PropertySignatureWithFromImpl(mergeSignatureAnnotations(this.ast, toASTAnnotations(annotations)), this.from);
    }
}
const propertySignature = (self)=>new PropertySignatureWithFromImpl(new PropertySignatureDeclaration(self.ast, false, true, {}, undefined), self);
const withConstructorDefault = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, defaultValue)=>{
    const ast = self.ast;
    switch(ast._tag){
        case "PropertySignatureDeclaration":
            return makePropertySignature(new PropertySignatureDeclaration(ast.type, ast.isOptional, ast.isReadonly, ast.annotations, defaultValue));
        case "PropertySignatureTransformation":
            return makePropertySignature(new PropertySignatureTransformation(ast.from, new ToPropertySignature(ast.to.type, ast.to.isOptional, ast.to.isReadonly, ast.to.annotations, defaultValue), ast.decode, ast.encode));
    }
});
const applyDefaultValue = (o, defaultValue)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(o, {
        onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(defaultValue()),
        onSome: (value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(value === undefined ? defaultValue() : value)
    });
const pruneUndefined = (ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pruneUndefined"])(ast, pruneUndefined, (ast)=>{
        const pruned = pruneUndefined(ast.to);
        if (pruned) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Transformation"](ast.from, pruned, ast.transformation);
        }
    });
const withDecodingDefault = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, defaultValue)=>{
    const ast = self.ast;
    switch(ast._tag){
        case "PropertySignatureDeclaration":
            {
                const to = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["typeAST"])(ast.type);
                return makePropertySignature(new PropertySignatureTransformation(new FromPropertySignature(ast.type, ast.isOptional, ast.isReadonly, ast.annotations), new ToPropertySignature(pruneUndefined(to) ?? to, false, true, {}, ast.defaultValue), (o)=>applyDefaultValue(o, defaultValue), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]));
            }
        case "PropertySignatureTransformation":
            {
                const to = ast.to.type;
                return makePropertySignature(new PropertySignatureTransformation(ast.from, new ToPropertySignature(pruneUndefined(to) ?? to, false, ast.to.isReadonly, ast.to.annotations, ast.to.defaultValue), (o)=>applyDefaultValue(ast.decode(o), defaultValue), ast.encode));
            }
    }
});
const withDefaults = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, defaults)=>self.pipe(withDecodingDefault(defaults.decoding), withConstructorDefault(defaults.constructor)));
const fromKey = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, key)=>{
    const ast = self.ast;
    switch(ast._tag){
        case "PropertySignatureDeclaration":
            {
                return makePropertySignature(new PropertySignatureTransformation(new FromPropertySignature(ast.type, ast.isOptional, ast.isReadonly, ast.annotations, key), new ToPropertySignature((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["typeAST"])(ast.type), ast.isOptional, ast.isReadonly, {}, ast.defaultValue), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]));
            }
        case "PropertySignatureTransformation":
            return makePropertySignature(new PropertySignatureTransformation(new FromPropertySignature(ast.from.type, ast.from.isOptional, ast.from.isReadonly, ast.from.annotations, key), ast.to, ast.decode, ast.encode));
    }
});
const optionalToRequired = (from, to, options)=>makePropertySignature(new PropertySignatureTransformation(new FromPropertySignature(from.ast, true, true, {}, undefined), new ToPropertySignature(to.ast, false, true, {}, undefined), (o)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(options.decode(o)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(options.encode)));
const requiredToOptional = (from, to, options)=>makePropertySignature(new PropertySignatureTransformation(new FromPropertySignature(from.ast, false, true, {}, undefined), new ToPropertySignature(to.ast, true, true, {}, undefined), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(options.decode), (o)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(options.encode(o))));
const optionalToOptional = (from, to, options)=>makePropertySignature(new PropertySignatureTransformation(new FromPropertySignature(from.ast, true, true, {}, undefined), new ToPropertySignature(to.ast, true, true, {}, undefined), options.decode, options.encode));
const optionalPropertySignatureAST = (self, options)=>{
    const isExact = options?.exact;
    const defaultValue = options?.default;
    const isNullable = options?.nullable;
    const asOption = options?.as == "Option";
    const asOptionEncode = options?.onNoneEncoding ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["orElse"])(options.onNoneEncoding) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"];
    if (isExact) {
        if (defaultValue) {
            if (isNullable) {
                return withConstructorDefault(optionalToRequired(NullOr(self), typeSchema(self), {
                    decode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                        onNone: defaultValue,
                        onSome: (a)=>a === null ? defaultValue() : a
                    }),
                    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"]
                }), defaultValue).ast;
            } else {
                return withConstructorDefault(optionalToRequired(self, typeSchema(self), {
                    decode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                        onNone: defaultValue,
                        onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
                    }),
                    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"]
                }), defaultValue).ast;
            }
        } else if (asOption) {
            if (isNullable) {
                return optionalToRequired(NullOr(self), OptionFromSelf(typeSchema(self)), {
                    decode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filter"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNotNull"]),
                    encode: asOptionEncode
                }).ast;
            } else {
                return optionalToRequired(self, OptionFromSelf(typeSchema(self)), {
                    decode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"],
                    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
                }).ast;
            }
        } else {
            if (isNullable) {
                return optionalToOptional(NullOr(self), typeSchema(self), {
                    decode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filter"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNotNull"]),
                    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
                }).ast;
            } else {
                return new PropertySignatureDeclaration(self.ast, true, true, {}, undefined);
            }
        }
    } else {
        if (defaultValue) {
            if (isNullable) {
                return withConstructorDefault(optionalToRequired(NullishOr(self), typeSchema(self), {
                    decode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                        onNone: defaultValue,
                        onSome: (a)=>a == null ? defaultValue() : a
                    }),
                    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"]
                }), defaultValue).ast;
            } else {
                return withConstructorDefault(optionalToRequired(UndefinedOr(self), typeSchema(self), {
                    decode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                        onNone: defaultValue,
                        onSome: (a)=>a === undefined ? defaultValue() : a
                    }),
                    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"]
                }), defaultValue).ast;
            }
        } else if (asOption) {
            if (isNullable) {
                return optionalToRequired(NullishOr(self), OptionFromSelf(typeSchema(self)), {
                    decode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filter"])((a)=>a != null),
                    encode: asOptionEncode
                }).ast;
            } else {
                return optionalToRequired(UndefinedOr(self), OptionFromSelf(typeSchema(self)), {
                    decode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filter"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNotUndefined"]),
                    encode: asOptionEncode
                }).ast;
            }
        } else {
            if (isNullable) {
                return optionalToOptional(NullishOr(self), UndefinedOr(typeSchema(self)), {
                    decode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filter"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNotNull"]),
                    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
                }).ast;
            } else {
                return new PropertySignatureDeclaration(UndefinedOr(self).ast, true, true, {}, undefined);
            }
        }
    }
};
const optional = (self)=>{
    const ast = self.ast === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["undefinedKeyword"] || self.ast === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["neverKeyword"] ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["undefinedKeyword"] : UndefinedOr(self).ast;
    return new PropertySignatureWithFromImpl(new PropertySignatureDeclaration(ast, true, true, {}, undefined), self);
};
const optionalWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSchema(args[0]), (self, options)=>{
    return new PropertySignatureWithFromImpl(optionalPropertySignatureAST(self, options), self);
});
const preserveMissingMessageAnnotation = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pickAnnotations"])([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MissingMessageAnnotationId"]
]);
const getDefaultTypeLiteralAST = (fields, records)=>{
    const ownKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ownKeys"])(fields);
    const pss = [];
    if (ownKeys.length > 0) {
        const from = [];
        const to = [];
        const transformations = [];
        for(let i = 0; i < ownKeys.length; i++){
            const key = ownKeys[i];
            const field = fields[key];
            if (isPropertySignature(field)) {
                const ast = field.ast;
                switch(ast._tag){
                    case "PropertySignatureDeclaration":
                        {
                            const type = ast.type;
                            const isOptional = ast.isOptional;
                            const toAnnotations = ast.annotations;
                            from.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignature"](key, type, isOptional, true, preserveMissingMessageAnnotation(ast)));
                            to.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignature"](key, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["typeAST"])(type), isOptional, true, toAnnotations));
                            pss.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignature"](key, type, isOptional, true, toAnnotations));
                            break;
                        }
                    case "PropertySignatureTransformation":
                        {
                            const fromKey = ast.from.fromKey ?? key;
                            from.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignature"](fromKey, ast.from.type, ast.from.isOptional, true, ast.from.annotations));
                            to.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignature"](key, ast.to.type, ast.to.isOptional, true, ast.to.annotations));
                            transformations.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignatureTransformation"](fromKey, key, ast.decode, ast.encode));
                            break;
                        }
                }
            } else {
                from.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignature"](key, field.ast, false, true));
                to.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignature"](key, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["typeAST"])(field.ast), false, true));
                pss.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignature"](key, field.ast, false, true));
            }
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmptyReadonlyArray"])(transformations)) {
            const issFrom = [];
            const issTo = [];
            for (const r of records){
                const { indexSignatures, propertySignatures } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["record"])(r.key.ast, r.value.ast);
                propertySignatures.forEach((ps)=>{
                    from.push(ps);
                    to.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignature"](ps.name, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["typeAST"])(ps.type), ps.isOptional, ps.isReadonly, ps.annotations));
                });
                indexSignatures.forEach((is)=>{
                    issFrom.push(is);
                    issTo.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["IndexSignature"](is.parameter, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["typeAST"])(is.type), is.isReadonly));
                });
            }
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Transformation"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TypeLiteral"](from, issFrom, {
                [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AutoTitleAnnotationId"]]: "Struct (Encoded side)"
            }), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TypeLiteral"](to, issTo, {
                [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AutoTitleAnnotationId"]]: "Struct (Type side)"
            }), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TypeLiteralTransformation"](transformations));
        }
    }
    const iss = [];
    for (const r of records){
        const { indexSignatures, propertySignatures } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["record"])(r.key.ast, r.value.ast);
        propertySignatures.forEach((ps)=>pss.push(ps));
        indexSignatures.forEach((is)=>iss.push(is));
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TypeLiteral"](pss, iss);
};
const lazilyMergeDefaults = (fields, out)=>{
    const ownKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ownKeys"])(fields);
    for (const key of ownKeys){
        const field = fields[key];
        if (out[key] === undefined && isPropertySignature(field)) {
            const ast = field.ast;
            const defaultValue = ast._tag === "PropertySignatureDeclaration" ? ast.defaultValue : ast.to.defaultValue;
            if (defaultValue !== undefined) {
                out[key] = defaultValue();
            }
        }
    }
    return out;
};
function makeTypeLiteralClass(fields, records, ast = getDefaultTypeLiteralAST(fields, records)) {
    return class TypeLiteralClass extends make(ast) {
        static annotations(annotations) {
            return makeTypeLiteralClass(this.fields, this.records, mergeSchemaAnnotations(this.ast, annotations));
        }
        static fields = {
            ...fields
        };
        static records = [
            ...records
        ];
        static make = (props, options)=>{
            const propsWithDefaults = lazilyMergeDefaults(fields, {
                ...props
            });
            return getDisableValidationMakeOption(options) ? propsWithDefaults : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateSync"])(this)(propsWithDefaults);
        };
        static pick(...keys) {
            return Struct((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Struct$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pick"])(fields, ...keys));
        }
        static omit(...keys) {
            return Struct((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Struct$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["omit"])(fields, ...keys));
        }
    };
}
function Struct(fields, ...records) {
    return makeTypeLiteralClass(fields, records);
}
const tag = (tag)=>Literal(tag).pipe(propertySignature, withConstructorDefault(()=>tag));
const TaggedStruct = (value, fields)=>Struct({
        _tag: tag(value),
        ...fields
    });
function makeRecordClass(key, value, ast) {
    return class RecordClass extends makeTypeLiteralClass({}, [
        {
            key,
            value
        }
    ], ast) {
        static annotations(annotations) {
            return makeRecordClass(key, value, mergeSchemaAnnotations(this.ast, annotations));
        }
        static key = key;
        static value = value;
    };
}
const Record = (options)=>makeRecordClass(options.key, options.value);
const pick = (...keys)=>(self)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pick"])(self.ast, keys));
const omit = (...keys)=>(self)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["omit"])(self.ast, keys));
const pluck = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (schema, key)=>{
    const ps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPropertyKeyIndexedAccess"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["typeAST"])(schema.ast), key);
    const value = make(ps.isOptional ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["orUndefined"])(ps.type) : ps.type);
    const out = transform(schema.pipe(pick(key)), value, {
        strict: true,
        decode: (i)=>i[key],
        encode: (a)=>ps.isOptional && a === undefined ? {} : {
                [key]: a
            }
    });
    return out;
});
function makeBrandClass(from, ast) {
    return class BrandClass extends make(ast) {
        static annotations(annotations) {
            return makeBrandClass(this.from, mergeSchemaAnnotations(this.ast, annotations));
        }
        static make = (a, options)=>{
            return getDisableValidationMakeOption(options) ? a : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateSync"])(this)(a);
        };
        static from = from;
    };
}
const brand = (brand, annotations)=>(self)=>{
        const annotation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBrandAnnotation"])(self.ast), {
            onNone: ()=>[
                    brand
                ],
            onSome: (brands)=>[
                    ...brands,
                    brand
                ]
        });
        const ast = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["annotations"])(self.ast, toASTAnnotations({
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BrandAnnotationId"]]: annotation,
            ...annotations
        }));
        return makeBrandClass(self, ast);
    };
const partial = (self)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["partial"])(self.ast));
const partialWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSchema(args[0]), (self, options)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["partial"])(self.ast, options)));
const required = (self)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["required"])(self.ast));
const mutable = (schema)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mutable"])(schema.ast));
const intersectTypeLiterals = (x, y, path)=>{
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTypeLiteral"])(x) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTypeLiteral"])(y)) {
        const propertySignatures = [
            ...x.propertySignatures
        ];
        for (const ps of y.propertySignatures){
            const name = ps.name;
            const i = propertySignatures.findIndex((ps)=>ps.name === name);
            if (i === -1) {
                propertySignatures.push(ps);
            } else {
                const { isOptional, type } = propertySignatures[i];
                propertySignatures[i] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignature"](name, extendAST(type, ps.type, path.concat(name)), isOptional, true);
            }
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TypeLiteral"](propertySignatures, x.indexSignatures.concat(y.indexSignatures));
    }
    throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$errors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSchemaExtendErrorMessage"])(x, y, path));
};
const preserveRefinementAnnotations = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["omitAnnotations"])([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["IdentifierAnnotationId"]
]);
const addRefinementToMembers = (refinement, asts)=>asts.map((ast)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Refinement"](ast, refinement.filter, preserveRefinementAnnotations(refinement)));
const extendAST = (x, y, path)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Union"].make(intersectUnionMembers([
        x
    ], [
        y
    ], path));
const getTypes = (ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isUnion"])(ast) ? ast.types : [
        ast
    ];
const intersectUnionMembers = (xs, ys, path)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(xs, (x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(ys, (y)=>{
            switch(y._tag){
                case "Literal":
                    {
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isString"])(y.literal) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isStringKeyword"])(x) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNumber"])(y.literal) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNumberKeyword"])(x) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isBoolean"])(y.literal) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isBooleanKeyword"])(x)) {
                            return [
                                y
                            ];
                        }
                        break;
                    }
                case "StringKeyword":
                    {
                        if (y === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["stringKeyword"]) {
                            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isStringKeyword"])(x) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isLiteral"])(x) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isString"])(x.literal)) {
                                return [
                                    x
                                ];
                            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isRefinement"])(x)) {
                                return addRefinementToMembers(x, intersectUnionMembers(getTypes(x.from), [
                                    y
                                ], path));
                            }
                        } else if (x === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["stringKeyword"]) {
                            return [
                                y
                            ];
                        }
                        break;
                    }
                case "NumberKeyword":
                    {
                        if (y === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["numberKeyword"]) {
                            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNumberKeyword"])(x) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isLiteral"])(x) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNumber"])(x.literal)) {
                                return [
                                    x
                                ];
                            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isRefinement"])(x)) {
                                return addRefinementToMembers(x, intersectUnionMembers(getTypes(x.from), [
                                    y
                                ], path));
                            }
                        } else if (x === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["numberKeyword"]) {
                            return [
                                y
                            ];
                        }
                        break;
                    }
                case "BooleanKeyword":
                    {
                        if (y === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["booleanKeyword"]) {
                            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isBooleanKeyword"])(x) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isLiteral"])(x) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isBoolean"])(x.literal)) {
                                return [
                                    x
                                ];
                            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isRefinement"])(x)) {
                                return addRefinementToMembers(x, intersectUnionMembers(getTypes(x.from), [
                                    y
                                ], path));
                            }
                        } else if (x === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["booleanKeyword"]) {
                            return [
                                y
                            ];
                        }
                        break;
                    }
                case "Union":
                    return intersectUnionMembers(getTypes(x), y.types, path);
                case "Suspend":
                    return [
                        new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Suspend"](()=>extendAST(x, y.f(), path))
                    ];
                case "Refinement":
                    return addRefinementToMembers(y, intersectUnionMembers(getTypes(x), getTypes(y.from), path));
                case "TypeLiteral":
                    {
                        switch(x._tag){
                            case "Union":
                                return intersectUnionMembers(x.types, [
                                    y
                                ], path);
                            case "Suspend":
                                return [
                                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Suspend"](()=>extendAST(x.f(), y, path))
                                ];
                            case "Refinement":
                                return addRefinementToMembers(x, intersectUnionMembers(getTypes(x.from), [
                                    y
                                ], path));
                            case "TypeLiteral":
                                return [
                                    intersectTypeLiterals(x, y, path)
                                ];
                            case "Transformation":
                                {
                                    const transformation = x.transformation;
                                    const from = intersectTypeLiterals(x.from, y, path);
                                    const to = intersectTypeLiterals(x.to, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["typeAST"])(y), path);
                                    switch(transformation._tag){
                                        case "TypeLiteralTransformation":
                                            return [
                                                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Transformation"](from, to, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TypeLiteralTransformation"](transformation.propertySignatureTransformations))
                                            ];
                                        case "ComposeTransformation":
                                            return [
                                                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Transformation"](from, to, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["composeTransformation"])
                                            ];
                                        case "FinalTransformation":
                                            return [
                                                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Transformation"](from, to, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FinalTransformation"]((fromA, options, ast, fromI)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(transformation.decode(fromA, options, ast, fromI), (partial)=>({
                                                            ...fromA,
                                                            ...partial
                                                        })), (toI, options, ast, toA)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(transformation.encode(toI, options, ast, toA), (partial)=>({
                                                            ...toI,
                                                            ...partial
                                                        }))))
                                            ];
                                    }
                                }
                        }
                        break;
                    }
                case "Transformation":
                    {
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTransformation"])(x)) {
                            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTypeLiteralTransformation"])(y.transformation) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTypeLiteralTransformation"])(x.transformation)) {
                                return [
                                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Transformation"](intersectTypeLiterals(x.from, y.from, path), intersectTypeLiterals(x.to, y.to, path), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TypeLiteralTransformation"](y.transformation.propertySignatureTransformations.concat(x.transformation.propertySignatureTransformations)))
                                ];
                            }
                        } else {
                            return intersectUnionMembers([
                                y
                            ], [
                                x
                            ], path);
                        }
                        break;
                    }
            }
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$errors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSchemaExtendErrorMessage"])(x, y, path));
        }));
const extend = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>make(extendAST(self.ast, that.ast, [])));
const compose = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSchema(args[1]), (from, to)=>makeTransformationClass(from, to, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["compose"])(from.ast, to.ast)));
const suspend = (f)=>make(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Suspend"](()=>f().ast));
const RefineSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/Refine");
function makeRefineClass(from, filter, ast) {
    return class RefineClass extends make(ast) {
        static annotations(annotations) {
            return makeRefineClass(this.from, this.filter, mergeSchemaAnnotations(this.ast, annotations));
        }
        static [RefineSchemaId] = from;
        static from = from;
        static filter = filter;
        static make = (a, options)=>{
            return getDisableValidationMakeOption(options) ? a : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateSync"])(this)(a);
        };
    };
}
const fromFilterPredicateReturnTypeItem = (item, ast, input)=>{
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isBoolean"])(item)) {
        return item ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])() : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, input));
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isString"])(item)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, input, item));
    }
    if (item !== undefined) {
        if ("_tag" in item) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(item);
        }
        const issue = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, input, item.message);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmptyReadonlyArray"])(item.path) ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Pointer"](item.path, input, issue) : issue);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
};
const toFilterParseIssue = (out, ast, input)=>{
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSingle"])(out)) {
        return fromFilterPredicateReturnTypeItem(out, ast, input);
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmptyReadonlyArray"])(out)) {
        const issues = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filterMap"])(out, (issue)=>fromFilterPredicateReturnTypeItem(issue, ast, input));
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmptyReadonlyArray"])(issues)) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(issues.length === 1 ? issues[0] : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Composite"](ast, input, issues));
        }
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
};
function filter(predicate, annotations) {
    return (self)=>{
        function filter(input, options, ast) {
            return toFilterParseIssue(predicate(input, options, ast), ast, input);
        }
        const ast = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Refinement"](self.ast, filter, toASTAnnotations(annotations));
        return makeRefineClass(self, filter, ast);
    };
}
const filterEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>transformOrFail(self, typeSchema(self), {
        strict: true,
        decode: (i, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(f(i, options, ast), (filterReturnType)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(toFilterParseIssue(filterReturnType, ast, i), {
                    onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(i),
                    onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"]
                })),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(a)
    }));
function makeTransformationClass(from, to, ast) {
    return class TransformationClass extends make(ast) {
        static annotations(annotations) {
            return makeTransformationClass(this.from, this.to, mergeSchemaAnnotations(this.ast, annotations));
        }
        static from = from;
        static to = to;
    };
}
const transformOrFail = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSchema(args[0]) && isSchema(args[1]), (from, to, options)=>makeTransformationClass(from, to, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Transformation"](from.ast, to.ast, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FinalTransformation"](options.decode, options.encode))));
const transform = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSchema(args[0]) && isSchema(args[1]), (from, to, options)=>transformOrFail(from, to, {
        strict: true,
        decode: (fromA, _options, _ast, toA)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(options.decode(fromA, toA)),
        encode: (toI, _options, _ast, toA)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(options.encode(toI, toA))
    }));
function transformLiteral(from, to) {
    return transform(Literal(from), Literal(to), {
        strict: true,
        decode: ()=>to,
        encode: ()=>from
    });
}
function transformLiterals(...pairs) {
    return Union(...pairs.map(([from, to])=>transformLiteral(from, to)));
}
const attachPropertySignature = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSchema(args[0]), (schema, key, value, annotations)=>{
    const ast = extend(typeSchema(schema), Struct({
        [key]: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSymbol"])(value) ? UniqueSymbolFromSelf(value) : Literal(value)
    })).ast;
    return make(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Transformation"](schema.ast, annotations ? mergeSchemaAnnotations(ast, annotations) : ast, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TypeLiteralTransformation"]([
        new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PropertySignatureTransformation"](key, key, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(value), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])())
    ])));
});
const annotations = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, annotations)=>self.annotations(annotations));
const rename = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, mapping)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rename"])(self.ast, mapping)));
const TrimmedSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/Trimmed");
const trimmed = (annotations)=>(self)=>self.pipe(filter((a)=>a === a.trim(), {
            schemaId: TrimmedSchemaId,
            title: "trimmed",
            description: "a string with no leading or trailing whitespace",
            jsonSchema: {
                pattern: "^\\S[\\s\\S]*\\S$|^\\S$|^$"
            },
            ...annotations
        }));
const MaxLengthSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MaxLengthSchemaId"];
const maxLength = (maxLength, annotations)=>(self)=>self.pipe(filter((a)=>a.length <= maxLength, {
            schemaId: MaxLengthSchemaId,
            title: `maxLength(${maxLength})`,
            description: `a string at most ${maxLength} character(s) long`,
            jsonSchema: {
                maxLength
            },
            ...annotations
        }));
const MinLengthSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MinLengthSchemaId"];
const minLength = (minLength, annotations)=>(self)=>self.pipe(filter((a)=>a.length >= minLength, {
            schemaId: MinLengthSchemaId,
            title: `minLength(${minLength})`,
            description: `a string at least ${minLength} character(s) long`,
            jsonSchema: {
                minLength
            },
            ...annotations
        }));
const LengthSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LengthSchemaId"];
const length = (length, annotations)=>(self)=>{
        const minLength = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isObject"])(length) ? Math.max(0, Math.floor(length.min)) : Math.max(0, Math.floor(length));
        const maxLength = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isObject"])(length) ? Math.max(minLength, Math.floor(length.max)) : minLength;
        if (minLength !== maxLength) {
            return self.pipe(filter((a)=>a.length >= minLength && a.length <= maxLength, {
                schemaId: LengthSchemaId,
                title: `length({ min: ${minLength}, max: ${maxLength})`,
                description: `a string at least ${minLength} character(s) and at most ${maxLength} character(s) long`,
                jsonSchema: {
                    minLength,
                    maxLength
                },
                ...annotations
            }));
        }
        return self.pipe(filter((a)=>a.length === minLength, {
            schemaId: LengthSchemaId,
            title: `length(${minLength})`,
            description: minLength === 1 ? `a single character` : `a string ${minLength} character(s) long`,
            jsonSchema: {
                minLength,
                maxLength: minLength
            },
            ...annotations
        }));
    };
const PatternSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/Pattern");
const pattern = (regex, annotations)=>(self)=>{
        const source = regex.source;
        return self.pipe(filter((a)=>{
            // The following line ensures that `lastIndex` is reset to `0` in case the user has specified the `g` flag
            regex.lastIndex = 0;
            return regex.test(a);
        }, {
            schemaId: PatternSchemaId,
            [PatternSchemaId]: {
                regex
            },
            // title: `pattern(/${source}/)`, // avoiding this because it can be very long
            description: `a string matching the pattern ${source}`,
            jsonSchema: {
                pattern: source
            },
            ...annotations
        }));
    };
const StartsWithSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/StartsWith");
const startsWith = (startsWith, annotations)=>(self)=>{
        const formatted = JSON.stringify(startsWith);
        return self.pipe(filter((a)=>a.startsWith(startsWith), {
            schemaId: StartsWithSchemaId,
            [StartsWithSchemaId]: {
                startsWith
            },
            title: `startsWith(${formatted})`,
            description: `a string starting with ${formatted}`,
            jsonSchema: {
                pattern: `^${startsWith}`
            },
            ...annotations
        }));
    };
const EndsWithSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/EndsWith");
const endsWith = (endsWith, annotations)=>(self)=>{
        const formatted = JSON.stringify(endsWith);
        return self.pipe(filter((a)=>a.endsWith(endsWith), {
            schemaId: EndsWithSchemaId,
            [EndsWithSchemaId]: {
                endsWith
            },
            title: `endsWith(${formatted})`,
            description: `a string ending with ${formatted}`,
            jsonSchema: {
                pattern: `^.*${endsWith}$`
            },
            ...annotations
        }));
    };
const IncludesSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/Includes");
const includes = (searchString, annotations)=>(self)=>{
        const formatted = JSON.stringify(searchString);
        return self.pipe(filter((a)=>a.includes(searchString), {
            schemaId: IncludesSchemaId,
            [IncludesSchemaId]: {
                includes: searchString
            },
            title: `includes(${formatted})`,
            description: `a string including ${formatted}`,
            jsonSchema: {
                pattern: `.*${searchString}.*`
            },
            ...annotations
        }));
    };
const LowercasedSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/Lowercased");
const lowercased = (annotations)=>(self)=>self.pipe(filter((a)=>a === a.toLowerCase(), {
            schemaId: LowercasedSchemaId,
            title: "lowercased",
            description: "a lowercase string",
            jsonSchema: {
                pattern: "^[^A-Z]*$"
            },
            ...annotations
        }));
class Lowercased extends /*#__PURE__*/ String$.pipe(/*#__PURE__*/ lowercased({
    identifier: "Lowercased"
})) {
}
const UppercasedSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/Uppercased");
const uppercased = (annotations)=>(self)=>self.pipe(filter((a)=>a === a.toUpperCase(), {
            schemaId: UppercasedSchemaId,
            title: "uppercased",
            description: "an uppercase string",
            jsonSchema: {
                pattern: "^[^a-z]*$"
            },
            ...annotations
        }));
class Uppercased extends /*#__PURE__*/ String$.pipe(/*#__PURE__*/ uppercased({
    identifier: "Uppercased"
})) {
}
const CapitalizedSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/Capitalized");
const capitalized = (annotations)=>(self)=>self.pipe(filter((a)=>a[0]?.toUpperCase() === a[0], {
            schemaId: CapitalizedSchemaId,
            title: "capitalized",
            description: "a capitalized string",
            jsonSchema: {
                pattern: "^[^a-z]?.*$"
            },
            ...annotations
        }));
class Capitalized extends /*#__PURE__*/ String$.pipe(/*#__PURE__*/ capitalized({
    identifier: "Capitalized"
})) {
}
const UncapitalizedSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/Uncapitalized");
const uncapitalized = (annotations)=>(self)=>self.pipe(filter((a)=>a[0]?.toLowerCase() === a[0], {
            schemaId: UncapitalizedSchemaId,
            title: "uncapitalized",
            description: "a uncapitalized string",
            jsonSchema: {
                pattern: "^[^A-Z]?.*$"
            },
            ...annotations
        }));
class Uncapitalized extends /*#__PURE__*/ String$.pipe(/*#__PURE__*/ uncapitalized({
    identifier: "Uncapitalized"
})) {
}
class Char extends /*#__PURE__*/ String$.pipe(/*#__PURE__*/ length(1, {
    identifier: "Char"
})) {
}
const nonEmptyString = (annotations)=>minLength(1, {
        title: "nonEmptyString",
        description: "a non empty string",
        ...annotations
    });
class Lowercase extends /*#__PURE__*/ transform(String$.annotations({
    description: "a string that will be converted to lowercase"
}), Lowercased, {
    strict: true,
    decode: (i)=>i.toLowerCase(),
    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
}).annotations({
    identifier: "Lowercase"
}) {
}
class Uppercase extends /*#__PURE__*/ transform(String$.annotations({
    description: "a string that will be converted to uppercase"
}), Uppercased, {
    strict: true,
    decode: (i)=>i.toUpperCase(),
    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
}).annotations({
    identifier: "Uppercase"
}) {
}
class Capitalize extends /*#__PURE__*/ transform(String$.annotations({
    description: "a string that will be converted to a capitalized format"
}), Capitalized, {
    strict: true,
    decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$String$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["capitalize"])(i),
    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
}).annotations({
    identifier: "Capitalize"
}) {
}
class Uncapitalize extends /*#__PURE__*/ transform(String$.annotations({
    description: "a string that will be converted to an uncapitalized format"
}), Uncapitalized, {
    strict: true,
    decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$String$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uncapitalize"])(i),
    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
}).annotations({
    identifier: "Uncapitalize"
}) {
}
class Trimmed extends /*#__PURE__*/ String$.pipe(/*#__PURE__*/ trimmed({
    identifier: "Trimmed"
})) {
}
class NonEmptyTrimmedString extends /*#__PURE__*/ Trimmed.pipe(/*#__PURE__*/ nonEmptyString({
    identifier: "NonEmptyTrimmedString"
})) {
}
class Trim extends /*#__PURE__*/ transform(String$.annotations({
    description: "a string that will be trimmed"
}), Trimmed, {
    strict: true,
    decode: (i)=>i.trim(),
    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
}).annotations({
    identifier: "Trim"
}) {
}
const split = (separator)=>transform(String$.annotations({
        description: "a string that will be split"
    }), Array$(String$), {
        strict: true,
        decode: (i)=>i.split(separator),
        encode: (a)=>a.join(separator)
    });
const getErrorMessage = (e)=>e instanceof Error ? e.message : String(e);
const getParseJsonTransformation = (options)=>transformOrFail(String$.annotations({
        description: "a string to be decoded into JSON"
    }), Unknown, {
        strict: true,
        decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["try"])({
                try: ()=>JSON.parse(i, options?.reviver),
                catch: (e)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, getErrorMessage(e))
            }),
        encode: (a, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["try"])({
                try: ()=>JSON.stringify(a, options?.replacer, options?.space),
                catch: (e)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, a, getErrorMessage(e))
            })
    }).annotations({
        title: "parseJson",
        schemaId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ParseJsonSchemaId"]
    });
const parseJson = (schemaOrOptions, o)=>isSchema(schemaOrOptions) ? compose(parseJson(o), schemaOrOptions) : getParseJsonTransformation(schemaOrOptions);
class NonEmptyString extends /*#__PURE__*/ String$.pipe(/*#__PURE__*/ nonEmptyString({
    identifier: "NonEmptyString"
})) {
}
const UUIDSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/UUID");
const uuidRegexp = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i;
class UUID extends /*#__PURE__*/ String$.pipe(/*#__PURE__*/ pattern(uuidRegexp, {
    schemaId: UUIDSchemaId,
    identifier: "UUID",
    jsonSchema: {
        format: "uuid",
        pattern: uuidRegexp.source
    },
    description: "a Universally Unique Identifier",
    arbitrary: ()=>(fc)=>fc.uuid()
})) {
}
const ULIDSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/ULID");
const ulidRegexp = /^[0-7][0-9A-HJKMNP-TV-Z]{25}$/i;
class ULID extends /*#__PURE__*/ String$.pipe(/*#__PURE__*/ pattern(ulidRegexp, {
    schemaId: ULIDSchemaId,
    identifier: "ULID",
    description: "a Universally Unique Lexicographically Sortable Identifier",
    arbitrary: ()=>(fc)=>fc.ulid()
})) {
}
class URLFromSelf extends /*#__PURE__*/ instanceOf(URL, {
    identifier: "URLFromSelf",
    arbitrary: ()=>(fc)=>fc.webUrl().map((s)=>new URL(s)),
    pretty: ()=>(url)=>url.toString()
}) {
}
/** @ignore */ class URL$ extends /*#__PURE__*/ transformOrFail(String$.annotations({
    description: "a string to be decoded into a URL"
}), URLFromSelf, {
    strict: true,
    decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["try"])({
            try: ()=>new URL(i),
            catch: (e)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, `Unable to decode ${JSON.stringify(i)} into a URL. ${getErrorMessage(e)}`)
        }),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(a.toString())
}).annotations({
    identifier: "URL",
    pretty: ()=>(url)=>url.toString()
}) {
}
;
const FiniteSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FiniteSchemaId"];
const finite = (annotations)=>(self)=>self.pipe(filter(Number.isFinite, {
            schemaId: FiniteSchemaId,
            title: "finite",
            description: "a finite number",
            jsonSchema: {
                "type": "number"
            },
            ...annotations
        }));
const GreaterThanSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GreaterThanSchemaId"];
const greaterThan = (exclusiveMinimum, annotations)=>(self)=>self.pipe(filter((a)=>a > exclusiveMinimum, {
            schemaId: GreaterThanSchemaId,
            title: `greaterThan(${exclusiveMinimum})`,
            description: exclusiveMinimum === 0 ? "a positive number" : `a number greater than ${exclusiveMinimum}`,
            jsonSchema: {
                exclusiveMinimum
            },
            ...annotations
        }));
const GreaterThanOrEqualToSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GreaterThanOrEqualToSchemaId"];
const greaterThanOrEqualTo = (minimum, annotations)=>(self)=>self.pipe(filter((a)=>a >= minimum, {
            schemaId: GreaterThanOrEqualToSchemaId,
            title: `greaterThanOrEqualTo(${minimum})`,
            description: minimum === 0 ? "a non-negative number" : `a number greater than or equal to ${minimum}`,
            jsonSchema: {
                minimum
            },
            ...annotations
        }));
const MultipleOfSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/MultipleOf");
const multipleOf = (divisor, annotations)=>(self)=>{
        const positiveDivisor = Math.abs(divisor); // spec requires positive divisor
        return self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Number$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["remainder"])(a, divisor) === 0, {
            schemaId: MultipleOfSchemaId,
            title: `multipleOf(${positiveDivisor})`,
            description: `a number divisible by ${positiveDivisor}`,
            jsonSchema: {
                multipleOf: positiveDivisor
            },
            ...annotations
        }));
    };
const IntSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["IntSchemaId"];
const int = (annotations)=>(self)=>self.pipe(filter((a)=>Number.isSafeInteger(a), {
            schemaId: IntSchemaId,
            title: "int",
            description: "an integer",
            jsonSchema: {
                type: "integer"
            },
            ...annotations
        }));
const LessThanSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LessThanSchemaId"];
const lessThan = (exclusiveMaximum, annotations)=>(self)=>self.pipe(filter((a)=>a < exclusiveMaximum, {
            schemaId: LessThanSchemaId,
            title: `lessThan(${exclusiveMaximum})`,
            description: exclusiveMaximum === 0 ? "a negative number" : `a number less than ${exclusiveMaximum}`,
            jsonSchema: {
                exclusiveMaximum
            },
            ...annotations
        }));
const LessThanOrEqualToSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LessThanOrEqualToSchemaId"];
const lessThanOrEqualTo = (maximum, annotations)=>(self)=>self.pipe(filter((a)=>a <= maximum, {
            schemaId: LessThanOrEqualToSchemaId,
            title: `lessThanOrEqualTo(${maximum})`,
            description: maximum === 0 ? "a non-positive number" : `a number less than or equal to ${maximum}`,
            jsonSchema: {
                maximum
            },
            ...annotations
        }));
const BetweenSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BetweenSchemaId"];
const between = (minimum, maximum, annotations)=>(self)=>self.pipe(filter((a)=>a >= minimum && a <= maximum, {
            schemaId: BetweenSchemaId,
            title: `between(${minimum}, ${maximum})`,
            description: `a number between ${minimum} and ${maximum}`,
            jsonSchema: {
                minimum,
                maximum
            },
            ...annotations
        }));
const NonNaNSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NonNaNSchemaId"];
const nonNaN = (annotations)=>(self)=>self.pipe(filter((a)=>!Number.isNaN(a), {
            schemaId: NonNaNSchemaId,
            title: "nonNaN",
            description: "a number excluding NaN",
            ...annotations
        }));
const positive = (annotations)=>greaterThan(0, {
        title: "positive",
        ...annotations
    });
const negative = (annotations)=>lessThan(0, {
        title: "negative",
        ...annotations
    });
const nonPositive = (annotations)=>lessThanOrEqualTo(0, {
        title: "nonPositive",
        ...annotations
    });
const nonNegative = (annotations)=>greaterThanOrEqualTo(0, {
        title: "nonNegative",
        ...annotations
    });
const clamp = (minimum, maximum)=>(self)=>{
        return transform(self, typeSchema(self).pipe(between(minimum, maximum)), {
            strict: false,
            decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Number$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clamp"])(i, {
                    minimum,
                    maximum
                }),
            encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
        });
    };
function parseNumber(self) {
    return transformOrFail(self, Number$, {
        strict: false,
        decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromOption"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Number$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parse"])(i), ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, `Unable to decode ${JSON.stringify(i)} into a number`)),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(String(a))
    });
}
class NumberFromString extends /*#__PURE__*/ parseNumber(String$.annotations({
    description: "a string to be decoded into a number"
})).annotations({
    identifier: "NumberFromString"
}) {
}
class Finite extends /*#__PURE__*/ Number$.pipe(/*#__PURE__*/ finite({
    identifier: "Finite"
})) {
}
class Int extends /*#__PURE__*/ Number$.pipe(/*#__PURE__*/ int({
    identifier: "Int"
})) {
}
class NonNaN extends /*#__PURE__*/ Number$.pipe(/*#__PURE__*/ nonNaN({
    identifier: "NonNaN"
})) {
}
class Positive extends /*#__PURE__*/ Number$.pipe(/*#__PURE__*/ positive({
    identifier: "Positive"
})) {
}
class Negative extends /*#__PURE__*/ Number$.pipe(/*#__PURE__*/ negative({
    identifier: "Negative"
})) {
}
class NonPositive extends /*#__PURE__*/ Number$.pipe(/*#__PURE__*/ nonPositive({
    identifier: "NonPositive"
})) {
}
class NonNegative extends /*#__PURE__*/ Number$.pipe(/*#__PURE__*/ nonNegative({
    identifier: "NonNegative"
})) {
}
const JsonNumberSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JsonNumberSchemaId"];
class JsonNumber extends /*#__PURE__*/ Number$.pipe(/*#__PURE__*/ finite({
    schemaId: JsonNumberSchemaId,
    identifier: "JsonNumber"
})) {
}
class Not extends /*#__PURE__*/ transform(/*#__PURE__*/ Boolean$.annotations({
    description: "a boolean that will be negated"
}), Boolean$, {
    strict: true,
    decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Boolean$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["not"])(i),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Boolean$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["not"])(a)
}) {
}
const encodeSymbol = (sym, ast)=>{
    const key = Symbol.keyFor(sym);
    return key === undefined ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, sym, `Unable to encode a unique symbol ${String(sym)} into a string`)) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(key);
};
const decodeSymbol = (s)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(Symbol.for(s));
/** @ignore */ class Symbol$ extends /*#__PURE__*/ transformOrFail(String$.annotations({
    description: "a string to be decoded into a globally shared symbol"
}), SymbolFromSelf, {
    strict: false,
    decode: (i)=>decodeSymbol(i),
    encode: (a, _, ast)=>encodeSymbol(a, ast)
}).annotations({
    identifier: "Symbol"
}) {
}
;
const GreaterThanBigIntSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GreaterThanBigintSchemaId"];
const greaterThanBigInt = (min, annotations)=>(self)=>self.pipe(filter((a)=>a > min, {
            schemaId: GreaterThanBigIntSchemaId,
            [GreaterThanBigIntSchemaId]: {
                min
            },
            title: `greaterThanBigInt(${min})`,
            description: min === 0n ? "a positive bigint" : `a bigint greater than ${min}n`,
            ...annotations
        }));
const GreaterThanOrEqualToBigIntSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GreaterThanOrEqualToBigIntSchemaId"];
const greaterThanOrEqualToBigInt = (min, annotations)=>(self)=>self.pipe(filter((a)=>a >= min, {
            schemaId: GreaterThanOrEqualToBigIntSchemaId,
            [GreaterThanOrEqualToBigIntSchemaId]: {
                min
            },
            title: `greaterThanOrEqualToBigInt(${min})`,
            description: min === 0n ? "a non-negative bigint" : `a bigint greater than or equal to ${min}n`,
            ...annotations
        }));
const LessThanBigIntSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LessThanBigIntSchemaId"];
const lessThanBigInt = (max, annotations)=>(self)=>self.pipe(filter((a)=>a < max, {
            schemaId: LessThanBigIntSchemaId,
            [LessThanBigIntSchemaId]: {
                max
            },
            title: `lessThanBigInt(${max})`,
            description: max === 0n ? "a negative bigint" : `a bigint less than ${max}n`,
            ...annotations
        }));
const LessThanOrEqualToBigIntSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LessThanOrEqualToBigIntSchemaId"];
const lessThanOrEqualToBigInt = (max, annotations)=>(self)=>self.pipe(filter((a)=>a <= max, {
            schemaId: LessThanOrEqualToBigIntSchemaId,
            [LessThanOrEqualToBigIntSchemaId]: {
                max
            },
            title: `lessThanOrEqualToBigInt(${max})`,
            description: max === 0n ? "a non-positive bigint" : `a bigint less than or equal to ${max}n`,
            ...annotations
        }));
const BetweenBigIntSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BetweenBigintSchemaId"];
const betweenBigInt = (min, max, annotations)=>(self)=>self.pipe(filter((a)=>a >= min && a <= max, {
            schemaId: BetweenBigIntSchemaId,
            [BetweenBigIntSchemaId]: {
                min,
                max
            },
            title: `betweenBigInt(${min}, ${max})`,
            description: `a bigint between ${min}n and ${max}n`,
            ...annotations
        }));
const positiveBigInt = (annotations)=>greaterThanBigInt(0n, {
        title: "positiveBigInt",
        ...annotations
    });
const negativeBigInt = (annotations)=>lessThanBigInt(0n, {
        title: "negativeBigInt",
        ...annotations
    });
const nonNegativeBigInt = (annotations)=>greaterThanOrEqualToBigInt(0n, {
        title: "nonNegativeBigInt",
        ...annotations
    });
const nonPositiveBigInt = (annotations)=>lessThanOrEqualToBigInt(0n, {
        title: "nonPositiveBigInt",
        ...annotations
    });
const clampBigInt = (minimum, maximum)=>(self)=>transform(self, self.pipe(typeSchema, betweenBigInt(minimum, maximum)), {
            strict: false,
            decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigInt$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clamp"])(i, {
                    minimum,
                    maximum
                }),
            encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
        });
/** @ignore */ class BigInt$ extends /*#__PURE__*/ transformOrFail(String$.annotations({
    description: "a string to be decoded into a bigint"
}), BigIntFromSelf, {
    strict: true,
    decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromOption"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigInt$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromString"])(i), ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, `Unable to decode ${JSON.stringify(i)} into a bigint`)),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(String(a))
}).annotations({
    identifier: "BigInt"
}) {
}
;
const PositiveBigIntFromSelf = /*#__PURE__*/ BigIntFromSelf.pipe(/*#__PURE__*/ positiveBigInt({
    identifier: "PositiveBigintFromSelf"
}));
const PositiveBigInt = /*#__PURE__*/ BigInt$.pipe(/*#__PURE__*/ positiveBigInt({
    identifier: "PositiveBigint"
}));
const NegativeBigIntFromSelf = /*#__PURE__*/ BigIntFromSelf.pipe(/*#__PURE__*/ negativeBigInt({
    identifier: "NegativeBigintFromSelf"
}));
const NegativeBigInt = /*#__PURE__*/ BigInt$.pipe(/*#__PURE__*/ negativeBigInt({
    identifier: "NegativeBigint"
}));
const NonPositiveBigIntFromSelf = /*#__PURE__*/ BigIntFromSelf.pipe(/*#__PURE__*/ nonPositiveBigInt({
    identifier: "NonPositiveBigintFromSelf"
}));
const NonPositiveBigInt = /*#__PURE__*/ BigInt$.pipe(/*#__PURE__*/ nonPositiveBigInt({
    identifier: "NonPositiveBigint"
}));
const NonNegativeBigIntFromSelf = /*#__PURE__*/ BigIntFromSelf.pipe(/*#__PURE__*/ nonNegativeBigInt({
    identifier: "NonNegativeBigintFromSelf"
}));
const NonNegativeBigInt = /*#__PURE__*/ BigInt$.pipe(/*#__PURE__*/ nonNegativeBigInt({
    identifier: "NonNegativeBigint"
}));
class BigIntFromNumber extends /*#__PURE__*/ transformOrFail(Number$.annotations({
    description: "a number to be decoded into a bigint"
}), BigIntFromSelf.pipe(betweenBigInt(BigInt(Number.MIN_SAFE_INTEGER), BigInt(Number.MAX_SAFE_INTEGER))), {
    strict: true,
    decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromOption"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigInt$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromNumber"])(i), ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, `Unable to decode ${i} into a bigint`)),
    encode: (a, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromOption"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigInt$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toNumber"])(a), ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, a, `Unable to encode ${a}n into a number`))
}).annotations({
    identifier: "BigIntFromNumber"
}) {
}
const redactedArbitrary = (value)=>(fc)=>value(fc).map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Redacted$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"]);
const toComposite = (eff, onSuccess, ast, actual)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapBoth"])(eff, {
        onFailure: (e)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Composite"](ast, actual, e),
        onSuccess
    });
const redactedParse = (decodeUnknown)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Redacted$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isRedacted"])(u) ? toComposite(decodeUnknown((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Redacted$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["value"])(u), options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Redacted$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"], ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const RedactedFromSelf = (value)=>declare([
        value
    ], {
        decode: (value)=>redactedParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(value)),
        encode: (value)=>redactedParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(value))
    }, {
        description: "Redacted(<redacted>)",
        pretty: ()=>()=>"Redacted(<redacted>)",
        arbitrary: redactedArbitrary,
        equivalence: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Redacted$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"]
    });
function Redacted(value) {
    return transform(value, RedactedFromSelf(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Redacted$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(i),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Redacted$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["value"])(a)
    });
}
class DurationFromSelf extends /*#__PURE__*/ declare(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isDuration"], {
    identifier: "DurationFromSelf",
    pretty: ()=>String,
    arbitrary: ()=>(fc)=>fc.oneof(fc.constant(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["infinity"]), fc.bigInt({
                min: 0n
            }).map((_)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nanos"])(_)), fc.maxSafeNat().map((_)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["millis"])(_))),
    equivalence: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Equivalence"]
}) {
}
class DurationFromNanos extends /*#__PURE__*/ transformOrFail(NonNegativeBigIntFromSelf.annotations({
    description: "a bigint to be decoded into a Duration"
}), DurationFromSelf.pipe(filter((duration)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isFinite"])(duration), {
    description: "a finite duration"
})), {
    strict: true,
    decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nanos"])(i)),
    encode: (a, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toNanos"])(a), {
            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, a, `Unable to encode ${a} into a bigint`)),
            onSome: (nanos)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(nanos)
        })
}).annotations({
    identifier: "DurationFromNanos"
}) {
}
const NonNegativeInt = /*#__PURE__*/ NonNegative.pipe(int()).annotations({
    identifier: "NonNegativeInt"
});
class DurationFromMillis extends /*#__PURE__*/ transform(NonNegative.annotations({
    description: "a non-negative number to be decoded into a Duration"
}), DurationFromSelf, {
    strict: true,
    decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["millis"])(i),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toMillis"])(a)
}).annotations({
    identifier: "DurationFromMillis"
}) {
}
const DurationValueMillis = /*#__PURE__*/ TaggedStruct("Millis", {
    millis: NonNegativeInt
});
const DurationValueNanos = /*#__PURE__*/ TaggedStruct("Nanos", {
    nanos: BigInt$
});
const DurationValueInfinity = /*#__PURE__*/ TaggedStruct("Infinity", {});
const durationValueInfinity = /*#__PURE__*/ DurationValueInfinity.make({});
const DurationValue = /*#__PURE__*/ Union(DurationValueMillis, DurationValueNanos, DurationValueInfinity).annotations({
    identifier: "DurationValue",
    description: "an JSON-compatible tagged union to be decoded into a Duration"
});
const FiniteHRTime = /*#__PURE__*/ Tuple(element(NonNegativeInt).annotations({
    title: "seconds"
}), element(NonNegativeInt).annotations({
    title: "nanos"
})).annotations({
    identifier: "FiniteHRTime"
});
const InfiniteHRTime = /*#__PURE__*/ Tuple(Literal(-1), Literal(0)).annotations({
    identifier: "InfiniteHRTime"
});
const HRTime = /*#__PURE__*/ Union(FiniteHRTime, InfiniteHRTime).annotations({
    identifier: "HRTime",
    description: "a tuple of seconds and nanos to be decoded into a Duration"
});
const isDurationValue = (u)=>typeof u === "object";
class Duration extends /*#__PURE__*/ transform(Union(DurationValue, HRTime), DurationFromSelf, {
    strict: true,
    decode: (i)=>{
        if (isDurationValue(i)) {
            switch(i._tag){
                case "Millis":
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["millis"])(i.millis);
                case "Nanos":
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nanos"])(i.nanos);
                case "Infinity":
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["infinity"];
            }
        }
        const [seconds, nanos] = i;
        return seconds === -1 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["infinity"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nanos"])(BigInt(seconds) * BigInt(1e9) + BigInt(nanos));
    },
    encode: (a)=>{
        switch(a.value._tag){
            case "Millis":
                return DurationValueMillis.make({
                    millis: a.value.millis
                });
            case "Nanos":
                return DurationValueNanos.make({
                    nanos: a.value.nanos
                });
            case "Infinity":
                return durationValueInfinity;
        }
    }
}).annotations({
    identifier: "Duration"
}) {
}
const clampDuration = (minimum, maximum)=>(self)=>transform(self, self.pipe(typeSchema, betweenDuration(minimum, maximum)), {
            strict: false,
            decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clamp"])(i, {
                    minimum,
                    maximum
                }),
            encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
        });
const LessThanDurationSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/LessThanDuration");
const lessThanDuration = (max, annotations)=>(self)=>self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["lessThan"])(a, max), {
            schemaId: LessThanDurationSchemaId,
            [LessThanDurationSchemaId]: {
                max
            },
            title: `lessThanDuration(${max})`,
            description: `a Duration less than ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decode"])(max)}`,
            ...annotations
        }));
const LessThanOrEqualToDurationSchemaId = /*#__PURE__*/ Symbol.for("effect/schema/LessThanOrEqualToDuration");
const lessThanOrEqualToDuration = (max, annotations)=>(self)=>self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["lessThanOrEqualTo"])(a, max), {
            schemaId: LessThanDurationSchemaId,
            [LessThanDurationSchemaId]: {
                max
            },
            title: `lessThanOrEqualToDuration(${max})`,
            description: `a Duration less than or equal to ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decode"])(max)}`,
            ...annotations
        }));
const GreaterThanDurationSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/GreaterThanDuration");
const greaterThanDuration = (min, annotations)=>(self)=>self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["greaterThan"])(a, min), {
            schemaId: GreaterThanDurationSchemaId,
            [GreaterThanDurationSchemaId]: {
                min
            },
            title: `greaterThanDuration(${min})`,
            description: `a Duration greater than ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decode"])(min)}`,
            ...annotations
        }));
const GreaterThanOrEqualToDurationSchemaId = /*#__PURE__*/ Symbol.for("effect/schema/GreaterThanOrEqualToDuration");
const greaterThanOrEqualToDuration = (min, annotations)=>(self)=>self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["greaterThanOrEqualTo"])(a, min), {
            schemaId: GreaterThanOrEqualToDurationSchemaId,
            [GreaterThanOrEqualToDurationSchemaId]: {
                min
            },
            title: `greaterThanOrEqualToDuration(${min})`,
            description: `a Duration greater than or equal to ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decode"])(min)}`,
            ...annotations
        }));
const BetweenDurationSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/BetweenDuration");
const betweenDuration = (minimum, maximum, annotations)=>(self)=>self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["between"])(a, {
                minimum,
                maximum
            }), {
            schemaId: BetweenDurationSchemaId,
            [BetweenDurationSchemaId]: {
                maximum,
                minimum
            },
            title: `betweenDuration(${minimum}, ${maximum})`,
            description: `a Duration between ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decode"])(minimum)} and ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decode"])(maximum)}`,
            ...annotations
        }));
class Uint8ArrayFromSelf extends /*#__PURE__*/ declare(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isUint8Array"], {
    identifier: "Uint8ArrayFromSelf",
    pretty: ()=>(u8arr)=>`new Uint8Array(${JSON.stringify(Array.from(u8arr))})`,
    arbitrary: ()=>(fc)=>fc.uint8Array(),
    equivalence: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"])
}) {
}
class Uint8 extends /*#__PURE__*/ Number$.pipe(/*#__PURE__*/ between(0, 255, {
    identifier: "Uint8",
    description: "a 8-bit unsigned integer"
})) {
}
/** @ignore */ class Uint8Array$ extends /*#__PURE__*/ transform(Array$(Uint8).annotations({
    description: "an array of 8-bit unsigned integers to be decoded into a Uint8Array"
}), Uint8ArrayFromSelf, {
    strict: true,
    decode: (i)=>Uint8Array.from(i),
    encode: (a)=>Array.from(a)
}).annotations({
    identifier: "Uint8Array"
}) {
}
;
const makeUint8ArrayTransformation = (id, decode, encode)=>transformOrFail(String$.annotations({
        description: "a string to be decoded into a Uint8Array"
    }), Uint8ArrayFromSelf, {
        strict: true,
        decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapLeft"])(decode(i), (decodeException)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, decodeException.message)),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(encode(a))
    }).annotations({
        identifier: id
    });
const Uint8ArrayFromBase64 = /*#__PURE__*/ makeUint8ArrayTransformation("Uint8ArrayFromBase64", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeBase64"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeBase64"]);
const Uint8ArrayFromBase64Url = /*#__PURE__*/ makeUint8ArrayTransformation("Uint8ArrayFromBase64Url", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeBase64Url"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeBase64Url"]);
const Uint8ArrayFromHex = /*#__PURE__*/ makeUint8ArrayTransformation("Uint8ArrayFromHex", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeHex"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeHex"]);
const makeEncodingTransformation = (id, decode, encode)=>transformOrFail(String$.annotations({
        description: `A string that is interpreted as being ${id}-encoded and will be decoded into a UTF-8 string`
    }), String$, {
        strict: true,
        decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapLeft"])(decode(i), (decodeException)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, decodeException.message)),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(encode(a))
    }).annotations({
        identifier: `StringFrom${id}`
    });
const StringFromBase64 = /*#__PURE__*/ makeEncodingTransformation("Base64", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeBase64String"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeBase64"]);
const StringFromBase64Url = /*#__PURE__*/ makeEncodingTransformation("Base64Url", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeBase64UrlString"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeBase64Url"]);
const StringFromHex = /*#__PURE__*/ makeEncodingTransformation("Hex", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeHexString"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeHex"]);
const StringFromUriComponent = /*#__PURE__*/ transformOrFail(String$.annotations({
    description: `A string that is interpreted as being UriComponent-encoded and will be decoded into a UTF-8 string`
}), String$, {
    strict: true,
    decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapLeft"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUriComponent"])(i), (decodeException)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, decodeException.message)),
    encode: (a, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapLeft"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Encoding$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUriComponent"])(a), (encodeException)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, a, encodeException.message))
}).annotations({
    identifier: `StringFromUriComponent`
});
const MinItemsSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MinItemsSchemaId"];
const minItems = (n, annotations)=>(self)=>{
        const minItems = Math.floor(n);
        if (minItems < 1) {
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$errors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getInvalidArgumentErrorMessage"])(`Expected an integer greater than or equal to 1, actual ${n}`));
        }
        return self.pipe(filter((a)=>a.length >= minItems, {
            schemaId: MinItemsSchemaId,
            title: `minItems(${minItems})`,
            description: `an array of at least ${minItems} item(s)`,
            jsonSchema: {
                minItems
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["StableFilterAnnotationId"]]: true,
            ...annotations
        }));
    };
const MaxItemsSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MaxItemsSchemaId"];
const maxItems = (n, annotations)=>(self)=>{
        const maxItems = Math.floor(n);
        if (maxItems < 1) {
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$errors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getInvalidArgumentErrorMessage"])(`Expected an integer greater than or equal to 1, actual ${n}`));
        }
        return self.pipe(filter((a)=>a.length <= maxItems, {
            schemaId: MaxItemsSchemaId,
            title: `maxItems(${maxItems})`,
            description: `an array of at most ${maxItems} item(s)`,
            jsonSchema: {
                maxItems
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["StableFilterAnnotationId"]]: true,
            ...annotations
        }));
    };
const ItemsCountSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ItemsCountSchemaId"];
const itemsCount = (n, annotations)=>(self)=>{
        const itemsCount = Math.floor(n);
        if (itemsCount < 0) {
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$errors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getInvalidArgumentErrorMessage"])(`Expected an integer greater than or equal to 0, actual ${n}`));
        }
        return self.pipe(filter((a)=>a.length === itemsCount, {
            schemaId: ItemsCountSchemaId,
            title: `itemsCount(${itemsCount})`,
            description: `an array of exactly ${itemsCount} item(s)`,
            jsonSchema: {
                minItems: itemsCount,
                maxItems: itemsCount
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["StableFilterAnnotationId"]]: true,
            ...annotations
        }));
    };
const getNumberIndexedAccess = (self)=>make((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getNumberIndexedAccess"])(self.ast));
function head(self) {
    return transform(self, OptionFromSelf(getNumberIndexedAccess(typeSchema(self))), {
        strict: false,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["head"])(i),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(a, {
                onNone: ()=>[],
                onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["of"]
            })
    });
}
function headNonEmpty(self) {
    return transform(self, getNumberIndexedAccess(typeSchema(self)), {
        strict: false,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["headNonEmpty"])(i),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["of"])(a)
    });
}
const headOrElse = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>isSchema(args[0]), (self, fallback)=>transformOrFail(self, getNumberIndexedAccess(typeSchema(self)), {
        strict: true,
        decode: (i, _, ast)=>i.length > 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(i[0]) : fallback ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(fallback()) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, "Unable to retrieve the first element of an empty array")),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["of"])(a))
    }));
const ValidDateSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/ValidDate");
const validDate = (annotations)=>(self)=>self.pipe(filter((a)=>!Number.isNaN(a.getTime()), {
            schemaId: ValidDateSchemaId,
            [ValidDateSchemaId]: {
                noInvalidDate: true
            },
            title: "validDate",
            description: "a valid Date",
            ...annotations
        }));
const LessThanDateSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/LessThanDate");
const lessThanDate = (max, annotations)=>(self)=>self.pipe(filter((a)=>a < max, {
            schemaId: LessThanDateSchemaId,
            [LessThanDateSchemaId]: {
                max
            },
            title: `lessThanDate(${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(max)})`,
            description: `a date before ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(max)}`,
            ...annotations
        }));
const LessThanOrEqualToDateSchemaId = /*#__PURE__*/ Symbol.for("effect/schema/LessThanOrEqualToDate");
const lessThanOrEqualToDate = (max, annotations)=>(self)=>self.pipe(filter((a)=>a <= max, {
            schemaId: LessThanDateSchemaId,
            [LessThanDateSchemaId]: {
                max
            },
            title: `lessThanOrEqualToDate(${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(max)})`,
            description: `a date before or equal to ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(max)}`,
            ...annotations
        }));
const GreaterThanDateSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/GreaterThanDate");
const greaterThanDate = (min, annotations)=>(self)=>self.pipe(filter((a)=>a > min, {
            schemaId: GreaterThanDateSchemaId,
            [GreaterThanDateSchemaId]: {
                min
            },
            title: `greaterThanDate(${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(min)})`,
            description: `a date after ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(min)}`,
            ...annotations
        }));
const GreaterThanOrEqualToDateSchemaId = /*#__PURE__*/ Symbol.for("effect/schema/GreaterThanOrEqualToDate");
const greaterThanOrEqualToDate = (min, annotations)=>(self)=>self.pipe(filter((a)=>a >= min, {
            schemaId: GreaterThanOrEqualToDateSchemaId,
            [GreaterThanOrEqualToDateSchemaId]: {
                min
            },
            title: `greaterThanOrEqualToDate(${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(min)})`,
            description: `a date after or equal to ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(min)}`,
            ...annotations
        }));
const BetweenDateSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/BetweenDate");
const betweenDate = (min, max, annotations)=>(self)=>self.pipe(filter((a)=>a <= max && a >= min, {
            schemaId: BetweenDateSchemaId,
            [BetweenDateSchemaId]: {
                max,
                min
            },
            title: `betweenDate(${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(min)}, ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(max)})`,
            description: `a date between ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(min)} and ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(max)}`,
            ...annotations
        }));
const DateFromSelfSchemaId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$schemaId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DateFromSelfSchemaId"];
class DateFromSelf extends /*#__PURE__*/ declare(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isDate"], {
    identifier: "DateFromSelf",
    schemaId: DateFromSelfSchemaId,
    [DateFromSelfSchemaId]: {
        noInvalidDate: false
    },
    description: "a potentially invalid Date instance",
    pretty: ()=>(date)=>`new Date(${JSON.stringify(date)})`,
    arbitrary: ()=>(fc)=>fc.date({
                noInvalidDate: false
            }),
    equivalence: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Date"]
}) {
}
class ValidDateFromSelf extends /*#__PURE__*/ DateFromSelf.pipe(/*#__PURE__*/ validDate({
    identifier: "ValidDateFromSelf",
    description: "a valid Date instance"
})) {
}
class DateFromString extends /*#__PURE__*/ transform(String$.annotations({
    description: "a string to be decoded into a Date"
}), DateFromSelf, {
    strict: true,
    decode: (i)=>new Date(i),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(a)
}).annotations({
    identifier: "DateFromString"
}) {
}
/** @ignore */ class Date$ extends /*#__PURE__*/ DateFromString.pipe(/*#__PURE__*/ validDate({
    identifier: "Date"
})) {
}
;
class DateFromNumber extends /*#__PURE__*/ transform(Number$.annotations({
    description: "a number to be decoded into a Date"
}), DateFromSelf, {
    strict: true,
    decode: (i)=>new Date(i),
    encode: (a)=>a.getTime()
}).annotations({
    identifier: "DateFromNumber"
}) {
}
class DateTimeUtcFromSelf extends /*#__PURE__*/ declare((u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isDateTime"])(u) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isUtc"])(u), {
    identifier: "DateTimeUtcFromSelf",
    description: "a DateTime.Utc instance",
    pretty: ()=>(dateTime)=>dateTime.toString(),
    arbitrary: ()=>(fc)=>fc.date({
                noInvalidDate: true
            }).map((date)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromDate"])(date)),
    equivalence: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Equivalence"]
}) {
}
const decodeDateTimeUtc = (input, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["try"])({
        try: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeMake"])(input),
        catch: ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, input, `Unable to decode ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatUnknown"])(input)} into a DateTime.Utc`)
    });
class DateTimeUtcFromNumber extends /*#__PURE__*/ transformOrFail(Number$.annotations({
    description: "a number to be decoded into a DateTime.Utc"
}), DateTimeUtcFromSelf, {
    strict: true,
    decode: (i, _, ast)=>decodeDateTimeUtc(i, ast),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toEpochMillis"])(a))
}).annotations({
    identifier: "DateTimeUtcFromNumber"
}) {
}
class DateTimeUtcFromDate extends /*#__PURE__*/ transformOrFail(DateFromSelf.annotations({
    description: "a Date to be decoded into a DateTime.Utc"
}), DateTimeUtcFromSelf, {
    strict: true,
    decode: (i, _, ast)=>decodeDateTimeUtc(i, ast),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toDateUtc"])(a))
}).annotations({
    identifier: "DateTimeUtcFromDate"
}) {
}
class DateTimeUtc extends /*#__PURE__*/ transformOrFail(String$.annotations({
    description: "a string to be decoded into a DateTime.Utc"
}), DateTimeUtcFromSelf, {
    strict: true,
    decode: (i, _, ast)=>decodeDateTimeUtc(i, ast),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatIso"])(a))
}).annotations({
    identifier: "DateTimeUtc"
}) {
}
const timeZoneOffsetArbitrary = ()=>(fc)=>fc.integer({
            min: -12 * 60 * 60 * 1000,
            max: 14 * 60 * 60 * 1000
        }).map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zoneMakeOffset"]);
class TimeZoneOffsetFromSelf extends /*#__PURE__*/ declare(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTimeZoneOffset"], {
    identifier: "TimeZoneOffsetFromSelf",
    description: "a TimeZone.Offset instance",
    pretty: ()=>(zone)=>zone.toString(),
    arbitrary: timeZoneOffsetArbitrary
}) {
}
class TimeZoneOffset extends /*#__PURE__*/ transform(Number$.annotations({
    description: "a number to be decoded into a TimeZone.Offset"
}), TimeZoneOffsetFromSelf, {
    strict: true,
    decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zoneMakeOffset"])(i),
    encode: (a)=>a.offset
}).annotations({
    identifier: "TimeZoneOffset"
}) {
}
const timeZoneNamedArbitrary = ()=>(fc)=>fc.constantFrom(...Intl.supportedValuesOf("timeZone")).map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zoneUnsafeMakeNamed"]);
class TimeZoneNamedFromSelf extends /*#__PURE__*/ declare(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTimeZoneNamed"], {
    identifier: "TimeZoneNamedFromSelf",
    description: "a TimeZone.Named instance",
    pretty: ()=>(zone)=>zone.toString(),
    arbitrary: timeZoneNamedArbitrary
}) {
}
class TimeZoneNamed extends /*#__PURE__*/ transformOrFail(String$.annotations({
    description: "a string to be decoded into a TimeZone.Named"
}), TimeZoneNamedFromSelf, {
    strict: true,
    decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["try"])({
            try: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zoneUnsafeMakeNamed"])(i),
            catch: ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, `Unable to decode ${JSON.stringify(i)} into a TimeZone.Named`)
        }),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(a.id)
}).annotations({
    identifier: "TimeZoneNamed"
}) {
}
class TimeZoneFromSelf extends /*#__PURE__*/ Union(TimeZoneOffsetFromSelf, TimeZoneNamedFromSelf) {
}
class TimeZone extends /*#__PURE__*/ transformOrFail(String$.annotations({
    description: "a string to be decoded into a TimeZone"
}), TimeZoneFromSelf, {
    strict: true,
    decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zoneFromString"])(i), {
            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, `Unable to decode ${JSON.stringify(i)} into a TimeZone`)),
            onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
        }),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zoneToString"])(a))
}).annotations({
    identifier: "TimeZone"
}) {
}
const timeZoneArbitrary = (fc)=>fc.oneof(timeZoneOffsetArbitrary()(fc), timeZoneNamedArbitrary()(fc));
class DateTimeZonedFromSelf extends /*#__PURE__*/ declare((u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isDateTime"])(u) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isZoned"])(u), {
    identifier: "DateTimeZonedFromSelf",
    description: "a DateTime.Zoned instance",
    pretty: ()=>(dateTime)=>dateTime.toString(),
    arbitrary: ()=>(fc)=>fc.tuple(fc.integer({
                // time zone db supports +/- 1000 years or so
                min: -31536000000000,
                max: 31536000000000
            }), timeZoneArbitrary(fc)).map(([millis, timeZone])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeMakeZoned"])(millis, {
                    timeZone
                })),
    equivalence: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Equivalence"]
}) {
}
class DateTimeZoned extends /*#__PURE__*/ transformOrFail(String$.annotations({
    description: "a string to be decoded into a DateTime.Zoned"
}), DateTimeZonedFromSelf, {
    strict: true,
    decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["makeZonedFromString"])(i), {
            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, `Unable to decode ${JSON.stringify(i)} into a DateTime.Zoned`)),
            onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
        }),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$DateTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatIsoZoned"])(a))
}).annotations({
    identifier: "DateTimeZoned"
}) {
}
const OptionNoneEncoded = /*#__PURE__*/ Struct({
    _tag: Literal("None")
}).annotations({
    description: "NoneEncoded"
});
const optionSomeEncoded = (value)=>Struct({
        _tag: Literal("Some"),
        value
    }).annotations({
        description: `SomeEncoded<${format(value)}>`
    });
const optionEncoded = (value)=>Union(OptionNoneEncoded, optionSomeEncoded(value)).annotations({
        description: `OptionEncoded<${format(value)}>`
    });
const optionDecode = (input)=>input._tag === "None" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])() : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(input.value);
const optionArbitrary = (value, ctx)=>(fc)=>fc.oneof(ctx, fc.record({
            _tag: fc.constant("None")
        }), fc.record({
            _tag: fc.constant("Some"),
            value: value(fc)
        })).map(optionDecode);
const optionPretty = (value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
        onNone: ()=>"none()",
        onSome: (a)=>`some(${value(a)})`
    });
const optionParse = (decodeUnknown)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isOption"])(u) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNone"])(u) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])()) : toComposite(decodeUnknown(u.value, options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"], ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const OptionFromSelf = (value)=>{
    return declare([
        value
    ], {
        decode: (value)=>optionParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(value)),
        encode: (value)=>optionParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(value))
    }, {
        description: `Option<${format(value)}>`,
        pretty: optionPretty,
        arbitrary: optionArbitrary,
        equivalence: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"]
    });
};
const makeNoneEncoded = {
    _tag: "None"
};
const makeSomeEncoded = (value)=>({
        _tag: "Some",
        value
    });
function Option(value) {
    const value_ = asSchema(value);
    const out = transform(optionEncoded(value_), OptionFromSelf(typeSchema(value_)), {
        strict: true,
        decode: (i)=>optionDecode(i),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(a, {
                onNone: ()=>makeNoneEncoded,
                onSome: makeSomeEncoded
            })
    });
    return out;
}
function OptionFromNullOr(value) {
    return transform(NullOr(value), OptionFromSelf(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromNullable"])(i),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrNull"])(a)
    });
}
function OptionFromNullishOr(value, onNoneEncoding) {
    return transform(NullishOr(value), OptionFromSelf(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromNullable"])(i),
        encode: onNoneEncoding === null ? (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrNull"])(a) : (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrUndefined"])(a)
    });
}
function OptionFromUndefinedOr(value) {
    return transform(UndefinedOr(value), OptionFromSelf(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromNullable"])(i),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrUndefined"])(a)
    });
}
class OptionFromNonEmptyTrimmedString extends /*#__PURE__*/ transform(String$, /*#__PURE__*/ OptionFromSelf(NonEmptyTrimmedString), {
    strict: true,
    decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filter"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(i.trim()), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$String$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmpty"]),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(a, ()=>"")
}) {
}
const rightEncoded = (right)=>Struct({
        _tag: Literal("Right"),
        right
    }).annotations({
        description: `RightEncoded<${format(right)}>`
    });
const leftEncoded = (left)=>Struct({
        _tag: Literal("Left"),
        left
    }).annotations({
        description: `LeftEncoded<${format(left)}>`
    });
const eitherEncoded = (right, left)=>Union(rightEncoded(right), leftEncoded(left)).annotations({
        description: `EitherEncoded<${format(left)}, ${format(right)}>`
    });
const eitherDecode = (input)=>input._tag === "Left" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])(input.left) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"])(input.right);
const eitherArbitrary = (right, left)=>(fc)=>fc.oneof(fc.record({
            _tag: fc.constant("Left"),
            left: left(fc)
        }), fc.record({
            _tag: fc.constant("Right"),
            right: right(fc)
        })).map(eitherDecode);
const eitherPretty = (right, left)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
        onLeft: (e)=>`left(${left(e)})`,
        onRight: (a)=>`right(${right(a)})`
    });
const eitherParse = (parseRight, decodeUnknownLeft)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEither"])(u) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(u, {
            onLeft: (left)=>toComposite(decodeUnknownLeft(left, options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"], ast, u),
            onRight: (right)=>toComposite(parseRight(right, options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"], ast, u)
        }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const EitherFromSelf = ({ left, right })=>{
    return declare([
        right,
        left
    ], {
        decode: (right, left)=>eitherParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(right), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(left)),
        encode: (right, left)=>eitherParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(right), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(left))
    }, {
        description: `Either<${format(right)}, ${format(left)}>`,
        pretty: eitherPretty,
        arbitrary: eitherArbitrary,
        equivalence: (right, left)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"])({
                left,
                right
            })
    });
};
const makeLeftEncoded = (left)=>({
        _tag: "Left",
        left
    });
const makeRightEncoded = (right)=>({
        _tag: "Right",
        right
    });
const Either = ({ left, right })=>{
    const right_ = asSchema(right);
    const left_ = asSchema(left);
    const out = transform(eitherEncoded(right_, left_), EitherFromSelf({
        left: typeSchema(left_),
        right: typeSchema(right_)
    }), {
        strict: true,
        decode: (i)=>eitherDecode(i),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(a, {
                onLeft: makeLeftEncoded,
                onRight: makeRightEncoded
            })
    });
    return out;
};
const EitherFromUnion = ({ left, right })=>{
    const right_ = asSchema(right);
    const left_ = asSchema(left);
    const toright = typeSchema(right_);
    const toleft = typeSchema(left_);
    const fromRight = transform(right_, rightEncoded(toright), {
        strict: true,
        decode: (i)=>makeRightEncoded(i),
        encode: (a)=>a.right
    });
    const fromLeft = transform(left_, leftEncoded(toleft), {
        strict: true,
        decode: (i)=>makeLeftEncoded(i),
        encode: (a)=>a.left
    });
    const out = transform(Union(fromRight, fromLeft), EitherFromSelf({
        left: toleft,
        right: toright
    }), {
        strict: true,
        decode: (i)=>i._tag === "Left" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])(i.left) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"])(i.right),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(a, {
                onLeft: makeLeftEncoded,
                onRight: makeRightEncoded
            })
    });
    return out;
};
const mapArbitrary = (key, value, ctx)=>{
    return (fc)=>{
        const items = fc.array(fc.tuple(key(fc), value(fc)));
        return (ctx.depthIdentifier !== undefined ? fc.oneof(ctx, fc.constant([]), items) : items).map((as)=>new Map(as));
    };
};
const readonlyMapPretty = (key, value)=>(map)=>`new Map([${Array.from(map.entries()).map(([k, v])=>`[${key(k)}, ${value(v)}]`).join(", ")}])`;
const readonlyMapEquivalence = (key, value)=>{
    const arrayEquivalence = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(([ka, va], [kb, vb])=>key(ka, kb) && value(va, vb)));
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((a, b)=>arrayEquivalence(Array.from(a.entries()), Array.from(b.entries())));
};
const readonlyMapParse = (decodeUnknown)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isMap"])(u) ? toComposite(decodeUnknown(Array.from(u.entries()), options), (as)=>new Map(as), ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const mapFromSelf_ = (key, value, description)=>declare([
        key,
        value
    ], {
        decode: (Key, Value)=>readonlyMapParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(Array$(Tuple(Key, Value)))),
        encode: (Key, Value)=>readonlyMapParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(Array$(Tuple(Key, Value))))
    }, {
        description,
        pretty: readonlyMapPretty,
        arbitrary: mapArbitrary,
        equivalence: readonlyMapEquivalence
    });
const ReadonlyMapFromSelf = ({ key, value })=>mapFromSelf_(key, value, `ReadonlyMap<${format(key)}, ${format(value)}>`);
const MapFromSelf = ({ key, value })=>mapFromSelf_(key, value, `Map<${format(key)}, ${format(value)}>`);
function ReadonlyMap({ key, value }) {
    return transform(Array$(Tuple(key, value)), ReadonlyMapFromSelf({
        key: typeSchema(asSchema(key)),
        value: typeSchema(asSchema(value))
    }), {
        strict: true,
        decode: (i)=>new Map(i),
        encode: (a)=>Array.from(a.entries())
    });
}
/** @ignore */ function map({ key, value }) {
    return transform(Array$(Tuple(key, value)), MapFromSelf({
        key: typeSchema(asSchema(key)),
        value: typeSchema(asSchema(value))
    }), {
        strict: true,
        decode: (i)=>new Map(i),
        encode: (a)=>Array.from(a.entries())
    });
}
;
const ReadonlyMapFromRecord = ({ key, value })=>transform(Record({
        key: encodedBoundSchema(key),
        value
    }).annotations({
        description: "a record to be decoded into a ReadonlyMap"
    }), ReadonlyMapFromSelf({
        key,
        value: typeSchema(value)
    }), {
        strict: true,
        decode: (i)=>new Map(Object.entries(i)),
        encode: (a)=>Object.fromEntries(a)
    });
const MapFromRecord = ({ key, value })=>transform(Record({
        key: encodedBoundSchema(key),
        value
    }).annotations({
        description: "a record to be decoded into a Map"
    }), MapFromSelf({
        key,
        value: typeSchema(value)
    }), {
        strict: true,
        decode: (i)=>new Map(Object.entries(i)),
        encode: (a)=>Object.fromEntries(a)
    });
const setArbitrary = (item, ctx)=>(fc)=>{
        const items = fc.array(item(fc));
        return (ctx.depthIdentifier !== undefined ? fc.oneof(ctx, fc.constant([]), items) : items).map((as)=>new Set(as));
    };
const readonlySetPretty = (item)=>(set)=>`new Set([${Array.from(set.values()).map((a)=>item(a)).join(", ")}])`;
const readonlySetEquivalence = (item)=>{
    const arrayEquivalence = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"])(item);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((a, b)=>arrayEquivalence(Array.from(a.values()), Array.from(b.values())));
};
const readonlySetParse = (decodeUnknown)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSet"])(u) ? toComposite(decodeUnknown(Array.from(u.values()), options), (as)=>new Set(as), ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const setFromSelf_ = (value, description)=>declare([
        value
    ], {
        decode: (item)=>readonlySetParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(Array$(item))),
        encode: (item)=>readonlySetParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(Array$(item)))
    }, {
        description,
        pretty: readonlySetPretty,
        arbitrary: setArbitrary,
        equivalence: readonlySetEquivalence
    });
const ReadonlySetFromSelf = (value)=>setFromSelf_(value, `ReadonlySet<${format(value)}>`);
const SetFromSelf = (value)=>setFromSelf_(value, `Set<${format(value)}>`);
function ReadonlySet(value) {
    return transform(Array$(value), ReadonlySetFromSelf(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>new Set(i),
        encode: (a)=>Array.from(a)
    });
}
/** @ignore */ function set(value) {
    return transform(Array$(value), SetFromSelf(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>new Set(i),
        encode: (a)=>Array.from(a)
    });
}
;
const bigDecimalPretty = ()=>(val)=>`BigDecimal(${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["normalize"])(val))})`;
const bigDecimalArbitrary = ()=>(fc)=>fc.tuple(fc.bigInt(), fc.integer({
            min: 0,
            max: 18
        })).map(([value, scale])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(value, scale));
class BigDecimalFromSelf extends /*#__PURE__*/ declare(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isBigDecimal"], {
    identifier: "BigDecimalFromSelf",
    pretty: bigDecimalPretty,
    arbitrary: bigDecimalArbitrary,
    equivalence: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Equivalence"]
}) {
}
class BigDecimal extends /*#__PURE__*/ transformOrFail(String$.annotations({
    description: "a string to be decoded into a BigDecimal"
}), BigDecimalFromSelf, {
    strict: true,
    decode: (i, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromString"])(i).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, i, `Unable to decode ${JSON.stringify(i)} into a BigDecimal`)),
            onSome: (val)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["normalize"])(val))
        })),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["normalize"])(a)))
}).annotations({
    identifier: "BigDecimal"
}) {
}
class BigDecimalFromNumber extends /*#__PURE__*/ transform(Number$.annotations({
    description: "a number to be decoded into a BigDecimal"
}), BigDecimalFromSelf, {
    strict: true,
    decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromNumber"])(i),
    encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeToNumber"])(a)
}).annotations({
    identifier: "BigDecimalFromNumber"
}) {
}
const GreaterThanBigDecimalSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/GreaterThanBigDecimal");
const greaterThanBigDecimal = (min, annotations)=>(self)=>{
        const formatted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])(min);
        return self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["greaterThan"])(a, min), {
            schemaId: GreaterThanBigDecimalSchemaId,
            [GreaterThanBigDecimalSchemaId]: {
                min
            },
            title: `greaterThanBigDecimal(${formatted})`,
            description: `a BigDecimal greater than ${formatted}`,
            ...annotations
        }));
    };
const GreaterThanOrEqualToBigDecimalSchemaId = /*#__PURE__*/ Symbol.for("effect/schema/GreaterThanOrEqualToBigDecimal");
const greaterThanOrEqualToBigDecimal = (min, annotations)=>(self)=>{
        const formatted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])(min);
        return self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["greaterThanOrEqualTo"])(a, min), {
            schemaId: GreaterThanOrEqualToBigDecimalSchemaId,
            [GreaterThanOrEqualToBigDecimalSchemaId]: {
                min
            },
            title: `greaterThanOrEqualToBigDecimal(${formatted})`,
            description: `a BigDecimal greater than or equal to ${formatted}`,
            ...annotations
        }));
    };
const LessThanBigDecimalSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/LessThanBigDecimal");
const lessThanBigDecimal = (max, annotations)=>(self)=>{
        const formatted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])(max);
        return self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["lessThan"])(a, max), {
            schemaId: LessThanBigDecimalSchemaId,
            [LessThanBigDecimalSchemaId]: {
                max
            },
            title: `lessThanBigDecimal(${formatted})`,
            description: `a BigDecimal less than ${formatted}`,
            ...annotations
        }));
    };
const LessThanOrEqualToBigDecimalSchemaId = /*#__PURE__*/ Symbol.for("effect/schema/LessThanOrEqualToBigDecimal");
const lessThanOrEqualToBigDecimal = (max, annotations)=>(self)=>{
        const formatted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])(max);
        return self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["lessThanOrEqualTo"])(a, max), {
            schemaId: LessThanOrEqualToBigDecimalSchemaId,
            [LessThanOrEqualToBigDecimalSchemaId]: {
                max
            },
            title: `lessThanOrEqualToBigDecimal(${formatted})`,
            description: `a BigDecimal less than or equal to ${formatted}`,
            ...annotations
        }));
    };
const PositiveBigDecimalSchemaId = /*#__PURE__*/ Symbol.for("effect/schema/PositiveBigDecimal");
const positiveBigDecimal = (annotations)=>(self)=>self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPositive"])(a), {
            schemaId: PositiveBigDecimalSchemaId,
            title: "positiveBigDecimal",
            description: `a positive BigDecimal`,
            ...annotations
        }));
const PositiveBigDecimalFromSelf = /*#__PURE__*/ BigDecimalFromSelf.pipe(/*#__PURE__*/ positiveBigDecimal({
    identifier: "PositiveBigDecimalFromSelf"
}));
const NonNegativeBigDecimalSchemaId = /*#__PURE__*/ Symbol.for("effect/schema/NonNegativeBigDecimal");
const nonNegativeBigDecimal = (annotations)=>(self)=>self.pipe(filter((a)=>a.value >= 0n, {
            schemaId: NonNegativeBigDecimalSchemaId,
            title: "nonNegativeBigDecimal",
            description: `a non-negative BigDecimal`,
            ...annotations
        }));
const NonNegativeBigDecimalFromSelf = /*#__PURE__*/ BigDecimalFromSelf.pipe(/*#__PURE__*/ nonNegativeBigDecimal({
    identifier: "NonNegativeBigDecimalFromSelf"
}));
const NegativeBigDecimalSchemaId = /*#__PURE__*/ Symbol.for("effect/schema/NegativeBigDecimal");
const negativeBigDecimal = (annotations)=>(self)=>self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNegative"])(a), {
            schemaId: NegativeBigDecimalSchemaId,
            title: "negativeBigDecimal",
            description: `a negative BigDecimal`,
            ...annotations
        }));
const NegativeBigDecimalFromSelf = /*#__PURE__*/ BigDecimalFromSelf.pipe(/*#__PURE__*/ negativeBigDecimal({
    identifier: "NegativeBigDecimalFromSelf"
}));
const NonPositiveBigDecimalSchemaId = /*#__PURE__*/ Symbol.for("effect/schema/NonPositiveBigDecimal");
const nonPositiveBigDecimal = (annotations)=>(self)=>self.pipe(filter((a)=>a.value <= 0n, {
            schemaId: NonPositiveBigDecimalSchemaId,
            title: "nonPositiveBigDecimal",
            description: `a non-positive BigDecimal`,
            ...annotations
        }));
const NonPositiveBigDecimalFromSelf = /*#__PURE__*/ BigDecimalFromSelf.pipe(/*#__PURE__*/ nonPositiveBigDecimal({
    identifier: "NonPositiveBigDecimalFromSelf"
}));
const BetweenBigDecimalSchemaId = /*#__PURE__*/ Symbol.for("effect/SchemaId/BetweenBigDecimal");
const betweenBigDecimal = (minimum, maximum, annotations)=>(self)=>{
        const formattedMinimum = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])(minimum);
        const formattedMaximum = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])(maximum);
        return self.pipe(filter((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["between"])(a, {
                minimum,
                maximum
            }), {
            schemaId: BetweenBigDecimalSchemaId,
            [BetweenBigDecimalSchemaId]: {
                maximum,
                minimum
            },
            title: `betweenBigDecimal(${formattedMinimum}, ${formattedMaximum})`,
            description: `a BigDecimal between ${formattedMinimum} and ${formattedMaximum}`,
            ...annotations
        }));
    };
const clampBigDecimal = (minimum, maximum)=>(self)=>transform(self, self.pipe(typeSchema, betweenBigDecimal(minimum, maximum)), {
            strict: false,
            decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$BigDecimal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clamp"])(i, {
                    minimum,
                    maximum
                }),
            encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
        });
const chunkArbitrary = (item, ctx)=>(fc)=>{
        const items = fc.array(item(fc));
        return (ctx.depthIdentifier !== undefined ? fc.oneof(ctx, fc.constant([]), items) : items).map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"]);
    };
const chunkPretty = (item)=>(c)=>`Chunk(${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toReadonlyArray"])(c).map(item).join(", ")})`;
const chunkParse = (decodeUnknown)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isChunk"])(u) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmpty"])(u) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()) : toComposite(decodeUnknown((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toReadonlyArray"])(u), options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"], ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const ChunkFromSelf = (value)=>{
    return declare([
        value
    ], {
        decode: (item)=>chunkParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(Array$(item))),
        encode: (item)=>chunkParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(Array$(item)))
    }, {
        description: `Chunk<${format(value)}>`,
        pretty: chunkPretty,
        arbitrary: chunkArbitrary,
        equivalence: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"]
    });
};
function Chunk(value) {
    return transform(Array$(value), ChunkFromSelf(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>i.length === 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])() : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(i),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toReadonlyArray"])(a)
    });
}
const nonEmptyChunkArbitrary = (item)=>(fc)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fast$2d$check$2f$lib$2f$esm$2f$arbitrary$2f$array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["array"])(item(fc), {
            minLength: 1
        }).map((as)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromNonEmptyArray"])(as));
const nonEmptyChunkPretty = (item)=>(c)=>`NonEmptyChunk(${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toReadonlyArray"])(c).map(item).join(", ")})`;
const nonEmptyChunkParse = (decodeUnknown)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isChunk"])(u) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmpty"])(u) ? toComposite(decodeUnknown((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toReadonlyArray"])(u), options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromNonEmptyArray"], ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const NonEmptyChunkFromSelf = (value)=>{
    return declare([
        value
    ], {
        decode: (item)=>nonEmptyChunkParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(NonEmptyArray(item))),
        encode: (item)=>nonEmptyChunkParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(NonEmptyArray(item)))
    }, {
        description: `NonEmptyChunk<${format(value)}>`,
        pretty: nonEmptyChunkPretty,
        arbitrary: nonEmptyChunkArbitrary,
        equivalence: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"]
    });
};
function NonEmptyChunk(value) {
    return transform(NonEmptyArray(value), NonEmptyChunkFromSelf(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromNonEmptyArray"])(i),
        encode: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toReadonlyArray"])(a)
    });
}
const decodeData = (a)=>Array.isArray(a) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Data$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["array"])(a) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Data$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["struct"])(a);
const dataArbitrary = (item)=>(fc)=>item(fc).map(decodeData);
const dataPretty = (item)=>(d)=>`Data(${item(d)})`;
const dataParse = (decodeUnknown)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEqual"])(u) ? toComposite(decodeUnknown(u, options), decodeData, ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const DataFromSelf = (value)=>{
    return declare([
        value
    ], {
        decode: (item)=>dataParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(item)),
        encode: (item)=>dataParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(item))
    }, {
        description: `Data<${format(value)}>`,
        pretty: dataPretty,
        arbitrary: dataArbitrary
    });
};
const Data = (value)=>{
    return transform(value, DataFromSelf(typeSchema(value)), {
        strict: false,
        decode: (i)=>decodeData(i),
        encode: (a)=>Array.isArray(a) ? Array.from(a) : Object.assign({}, a)
    });
};
const isField = (u)=>isSchema(u) || isPropertySignature(u);
const isFields = (fields)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ownKeys"])(fields).every((key)=>isField(fields[key]));
const getFields = (hasFields)=>"fields" in hasFields ? hasFields.fields : getFields(hasFields[RefineSchemaId]);
const getSchemaFromFieldsOr = (fieldsOr)=>isFields(fieldsOr) ? Struct(fieldsOr) : isSchema(fieldsOr) ? fieldsOr : Struct(getFields(fieldsOr));
const getFieldsFromFieldsOr = (fieldsOr)=>isFields(fieldsOr) ? fieldsOr : getFields(fieldsOr);
const Class = (identifier)=>(fieldsOr, annotations)=>makeClass({
            kind: "Class",
            identifier,
            schema: getSchemaFromFieldsOr(fieldsOr),
            fields: getFieldsFromFieldsOr(fieldsOr),
            Base: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Data$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Class"],
            annotations
        });
const getClassTag = (tag)=>withConstructorDefault(propertySignature(Literal(tag)), ()=>tag);
const TaggedClass = (identifier)=>(tag, fieldsOr, annotations)=>{
        const fields = getFieldsFromFieldsOr(fieldsOr);
        const schema = getSchemaFromFieldsOr(fieldsOr);
        const newFields = {
            _tag: getClassTag(tag)
        };
        const taggedFields = extendFields(newFields, fields);
        return class TaggedClass extends makeClass({
            kind: "TaggedClass",
            identifier: identifier ?? tag,
            schema: extend(schema, Struct(newFields)),
            fields: taggedFields,
            Base: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Data$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Class"],
            annotations
        }) {
            static _tag = tag;
        };
    };
const TaggedError = (identifier)=>(tag, fieldsOr, annotations)=>{
        class Base extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Data$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Error"] {
        }
        ;
        Base.prototype.name = tag;
        const fields = getFieldsFromFieldsOr(fieldsOr);
        const schema = getSchemaFromFieldsOr(fieldsOr);
        const newFields = {
            _tag: getClassTag(tag)
        };
        const taggedFields = extendFields(newFields, fields);
        return class TaggedErrorClass extends makeClass({
            kind: "TaggedError",
            identifier: identifier ?? tag,
            schema: extend(schema, Struct(newFields)),
            fields: taggedFields,
            Base,
            annotations,
            disableToString: true
        }) {
            static _tag = tag;
            get message() {
                return `{ ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ownKeys"])(fields).map((p)=>`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatPropertyKey"])(p)}: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatUnknown"])(this[p])}`).join(", ")} }`;
            }
        };
    };
const extendFields = (a, b)=>{
    const out = {
        ...a
    };
    for (const key of (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ownKeys"])(b)){
        if (key in a) {
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$errors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getASTDuplicatePropertySignatureErrorMessage"])(key));
        }
        out[key] = b[key];
    }
    return out;
};
function getDisableValidationMakeOption(options) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isBoolean"])(options) ? options : options?.disableValidation ?? false;
}
const astCache = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])("effect/Schema/astCache", ()=>new WeakMap());
const getClassAnnotations = (annotations)=>{
    if (annotations === undefined) {
        return [];
    } else if (Array.isArray(annotations)) {
        return annotations;
    } else {
        return [
            annotations
        ];
    }
};
const makeClass = ({ Base, annotations, disableToString, fields, identifier, kind, schema })=>{
    const classSymbol = Symbol.for(`effect/Schema/${kind}/${identifier}`);
    const [typeAnnotations, transformationAnnotations, encodedAnnotations] = getClassAnnotations(annotations);
    const typeSchema_ = typeSchema(schema);
    const declarationSurrogate = typeSchema_.annotations({
        identifier,
        ...typeAnnotations
    });
    const typeSide = typeSchema_.annotations({
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AutoTitleAnnotationId"]]: `${identifier} (Type side)`,
        ...typeAnnotations
    });
    const constructorSchema = schema.annotations({
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AutoTitleAnnotationId"]]: `${identifier} (Constructor)`,
        ...typeAnnotations
    });
    const encodedSide = schema.annotations({
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AutoTitleAnnotationId"]]: `${identifier} (Encoded side)`,
        ...encodedAnnotations
    });
    const transformationSurrogate = schema.annotations({
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JSONIdentifierAnnotationId"]]: identifier,
        ...encodedAnnotations,
        ...typeAnnotations,
        ...transformationAnnotations
    });
    const fallbackInstanceOf = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, classSymbol) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(typeSide)(u);
    const klass = class extends Base {
        constructor(props = {}, options = false){
            props = {
                ...props
            };
            if (kind !== "Class") {
                delete props["_tag"];
            }
            props = lazilyMergeDefaults(fields, props);
            if (!getDisableValidationMakeOption(options)) {
                props = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateSync"])(constructorSchema)(props);
            }
            super(props, true);
        }
        // ----------------
        // Schema interface
        // ----------------
        static [TypeId] = variance;
        static get ast() {
            let out = astCache.get(this);
            if (out) {
                return out;
            }
            const declaration = declare([
                schema
            ], {
                decode: ()=>(input, _, ast)=>input instanceof this || fallbackInstanceOf(input) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(input) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, input)),
                encode: ()=>(input, options)=>input instanceof this ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(input) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(typeSide)(input, options), (props)=>new this(props, true))
            }, {
                identifier,
                pretty: (pretty)=>(self)=>`${identifier}(${pretty(self)})`,
                // @ts-expect-error
                arbitrary: (arb)=>(fc)=>arb(fc).map((props)=>new this(props)),
                equivalence: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"],
                [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SurrogateAnnotationId"]]: declarationSurrogate.ast,
                ...typeAnnotations
            });
            out = transform(encodedSide, declaration, {
                strict: true,
                decode: (i)=>new this(i, true),
                encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
            }).annotations({
                [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SurrogateAnnotationId"]]: transformationSurrogate.ast,
                ...transformationAnnotations
            }).ast;
            astCache.set(this, out);
            return out;
        }
        static pipe() {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
        }
        static annotations(annotations) {
            return make(this.ast).annotations(annotations);
        }
        static toString() {
            return `(${String(encodedSide)} <-> ${identifier})`;
        }
        // ----------------
        // Class interface
        // ----------------
        static make(...args) {
            return new this(...args);
        }
        static fields = {
            ...fields
        };
        static identifier = identifier;
        static extend(identifier) {
            return (newFieldsOr, annotations)=>{
                const newFields = getFieldsFromFieldsOr(newFieldsOr);
                const newSchema = getSchemaFromFieldsOr(newFieldsOr);
                const extendedFields = extendFields(fields, newFields);
                return makeClass({
                    kind,
                    identifier,
                    schema: extend(schema, newSchema),
                    fields: extendedFields,
                    Base: this,
                    annotations
                });
            };
        }
        static transformOrFail(identifier) {
            return (newFieldsOr, options, annotations)=>{
                const transformedFields = extendFields(fields, newFieldsOr);
                return makeClass({
                    kind,
                    identifier,
                    schema: transformOrFail(schema, typeSchema(Struct(transformedFields)), options),
                    fields: transformedFields,
                    Base: this,
                    annotations
                });
            };
        }
        static transformOrFailFrom(identifier) {
            return (newFields, options, annotations)=>{
                const transformedFields = extendFields(fields, newFields);
                return makeClass({
                    kind,
                    identifier,
                    schema: transformOrFail(encodedSchema(schema), Struct(transformedFields), options),
                    fields: transformedFields,
                    Base: this,
                    annotations
                });
            };
        }
        // ----------------
        // other
        // ----------------
        get [classSymbol]() {
            return classSymbol;
        }
    };
    if (disableToString !== true) {
        Object.defineProperty(klass.prototype, "toString", {
            value () {
                return `${identifier}({ ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ownKeys"])(fields).map((p)=>`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatPropertyKey"])(p)}: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatUnknown"])(this[p])}`).join(", ")} })`;
            },
            configurable: true,
            writable: true
        });
    }
    return klass;
};
const FiberIdNoneEncoded = /*#__PURE__*/ Struct({
    _tag: Literal("None")
}).annotations({
    identifier: "FiberIdNoneEncoded"
});
const FiberIdRuntimeEncoded = /*#__PURE__*/ Struct({
    _tag: Literal("Runtime"),
    id: Int,
    startTimeMillis: Int
}).annotations({
    identifier: "FiberIdRuntimeEncoded"
});
const FiberIdCompositeEncoded = /*#__PURE__*/ Struct({
    _tag: Literal("Composite"),
    left: suspend(()=>FiberIdEncoded),
    right: suspend(()=>FiberIdEncoded)
}).annotations({
    identifier: "FiberIdCompositeEncoded"
});
const FiberIdEncoded = /*#__PURE__*/ Union(FiberIdNoneEncoded, FiberIdRuntimeEncoded, FiberIdCompositeEncoded).annotations({
    identifier: "FiberIdEncoded"
});
const fiberIdArbitrary = (fc)=>fc.letrec((tie)=>({
            None: fc.record({
                _tag: fc.constant("None")
            }),
            Runtime: fc.record({
                _tag: fc.constant("Runtime"),
                id: fc.integer(),
                startTimeMillis: fc.integer()
            }),
            Composite: fc.record({
                _tag: fc.constant("Composite"),
                left: tie("FiberId"),
                right: tie("FiberId")
            }),
            FiberId: fc.oneof(tie("None"), tie("Runtime"), tie("Composite"))
        })).FiberId.map(fiberIdDecode);
const fiberIdPretty = (fiberId)=>{
    switch(fiberId._tag){
        case "None":
            return "FiberId.none";
        case "Runtime":
            return `FiberId.runtime(${fiberId.id}, ${fiberId.startTimeMillis})`;
        case "Composite":
            return `FiberId.composite(${fiberIdPretty(fiberId.right)}, ${fiberIdPretty(fiberId.left)})`;
    }
};
class FiberIdFromSelf extends /*#__PURE__*/ declare(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isFiberId"], {
    identifier: "FiberIdFromSelf",
    pretty: ()=>fiberIdPretty,
    arbitrary: ()=>fiberIdArbitrary
}) {
}
const fiberIdDecode = (input)=>{
    switch(input._tag){
        case "None":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"];
        case "Runtime":
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runtime"])(input.id, input.startTimeMillis);
        case "Composite":
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["composite"])(fiberIdDecode(input.left), fiberIdDecode(input.right));
    }
};
const fiberIdEncode = (input)=>{
    switch(input._tag){
        case "None":
            return {
                _tag: "None"
            };
        case "Runtime":
            return {
                _tag: "Runtime",
                id: input.id,
                startTimeMillis: input.startTimeMillis
            };
        case "Composite":
            return {
                _tag: "Composite",
                left: fiberIdEncode(input.left),
                right: fiberIdEncode(input.right)
            };
    }
};
class FiberId extends /*#__PURE__*/ transform(FiberIdEncoded, FiberIdFromSelf, {
    strict: true,
    decode: (i)=>fiberIdDecode(i),
    encode: (a)=>fiberIdEncode(a)
}).annotations({
    identifier: "FiberId"
}) {
}
const causeDieEncoded = (defect)=>Struct({
        _tag: Literal("Die"),
        defect
    });
const CauseEmptyEncoded = /*#__PURE__*/ Struct({
    _tag: /*#__PURE__*/ Literal("Empty")
});
const causeFailEncoded = (error)=>Struct({
        _tag: Literal("Fail"),
        error
    });
const CauseInterruptEncoded = /*#__PURE__*/ Struct({
    _tag: /*#__PURE__*/ Literal("Interrupt"),
    fiberId: FiberIdEncoded
});
let causeEncodedId = 0;
const causeEncoded = (error, defect)=>{
    const error_ = asSchema(error);
    const defect_ = asSchema(defect);
    const suspended = suspend(()=>out);
    const out = Union(CauseEmptyEncoded, causeFailEncoded(error_), causeDieEncoded(defect_), CauseInterruptEncoded, Struct({
        _tag: Literal("Sequential"),
        left: suspended,
        right: suspended
    }), Struct({
        _tag: Literal("Parallel"),
        left: suspended,
        right: suspended
    })).annotations({
        title: `CauseEncoded<${format(error)}>`,
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JSONIdentifierAnnotationId"]]: `CauseEncoded${causeEncodedId++}`
    });
    return out;
};
const causeArbitrary = (error, defect)=>(fc)=>fc.letrec((tie)=>({
                Empty: fc.record({
                    _tag: fc.constant("Empty")
                }),
                Fail: fc.record({
                    _tag: fc.constant("Fail"),
                    error: error(fc)
                }),
                Die: fc.record({
                    _tag: fc.constant("Die"),
                    defect: defect(fc)
                }),
                Interrupt: fc.record({
                    _tag: fc.constant("Interrupt"),
                    fiberId: fiberIdArbitrary(fc)
                }),
                Sequential: fc.record({
                    _tag: fc.constant("Sequential"),
                    left: tie("Cause"),
                    right: tie("Cause")
                }),
                Parallel: fc.record({
                    _tag: fc.constant("Parallel"),
                    left: tie("Cause"),
                    right: tie("Cause")
                }),
                Cause: fc.oneof(tie("Empty"), tie("Fail"), tie("Die"), tie("Interrupt"), tie("Sequential"), tie("Parallel"))
            })).Cause.map(causeDecode);
const causePretty = (error)=>(cause)=>{
        const f = (cause)=>{
            switch(cause._tag){
                case "Empty":
                    return "Cause.empty";
                case "Fail":
                    return `Cause.fail(${error(cause.error)})`;
                case "Die":
                    return `Cause.die(${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pretty"])(cause)})`;
                case "Interrupt":
                    return `Cause.interrupt(${fiberIdPretty(cause.fiberId)})`;
                case "Sequential":
                    return `Cause.sequential(${f(cause.left)}, ${f(cause.right)})`;
                case "Parallel":
                    return `Cause.parallel(${f(cause.left)}, ${f(cause.right)})`;
            }
        };
        return f(cause);
    };
const causeParse = (decodeUnknown)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isCause"])(u) ? toComposite(decodeUnknown(causeEncode(u), options), causeDecode, ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const CauseFromSelf = ({ defect, error })=>{
    return declare([
        error,
        defect
    ], {
        decode: (error, defect)=>causeParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(causeEncoded(error, defect))),
        encode: (error, defect)=>causeParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(causeEncoded(error, defect)))
    }, {
        title: `Cause<${error.ast}>`,
        pretty: causePretty,
        arbitrary: causeArbitrary
    });
};
function causeDecode(cause) {
    switch(cause._tag){
        case "Empty":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"];
        case "Fail":
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(cause.error);
        case "Die":
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(cause.defect);
        case "Interrupt":
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(fiberIdDecode(cause.fiberId));
        case "Sequential":
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"])(causeDecode(cause.left), causeDecode(cause.right));
        case "Parallel":
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"])(causeDecode(cause.left), causeDecode(cause.right));
    }
}
function causeEncode(cause) {
    switch(cause._tag){
        case "Empty":
            return {
                _tag: "Empty"
            };
        case "Fail":
            return {
                _tag: "Fail",
                error: cause.error
            };
        case "Die":
            return {
                _tag: "Die",
                defect: cause.defect
            };
        case "Interrupt":
            return {
                _tag: "Interrupt",
                fiberId: cause.fiberId
            };
        case "Sequential":
            return {
                _tag: "Sequential",
                left: causeEncode(cause.left),
                right: causeEncode(cause.right)
            };
        case "Parallel":
            return {
                _tag: "Parallel",
                left: causeEncode(cause.left),
                right: causeEncode(cause.right)
            };
    }
}
const Cause = ({ defect, error })=>{
    const error_ = asSchema(error);
    const defect_ = asSchema(defect);
    const out = transform(causeEncoded(error_, defect_), CauseFromSelf({
        error: typeSchema(error_),
        defect: typeSchema(defect_)
    }), {
        strict: false,
        decode: (i)=>causeDecode(i),
        encode: (a)=>causeEncode(a)
    });
    return out;
};
class Defect extends /*#__PURE__*/ transform(Unknown, Unknown, {
    strict: true,
    decode: (i)=>{
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isObject"])(i) && "message" in i && typeof i.message === "string") {
            const err = new Error(i.message, {
                cause: i
            });
            if ("name" in i && typeof i.name === "string") {
                err.name = i.name;
            }
            err.stack = "stack" in i && typeof i.stack === "string" ? i.stack : "";
            return err;
        }
        return String(i);
    },
    encode: (a)=>{
        if (a instanceof Error) {
            return {
                name: a.name,
                message: a.message
            };
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["prettyErrorMessage"])(a);
    }
}).annotations({
    identifier: "Defect"
}) {
}
const exitFailureEncoded = (error, defect)=>Struct({
        _tag: Literal("Failure"),
        cause: causeEncoded(error, defect)
    });
const exitSuccessEncoded = (value)=>Struct({
        _tag: Literal("Success"),
        value
    });
const exitEncoded = (value, error, defect)=>{
    return Union(exitFailureEncoded(error, defect), exitSuccessEncoded(value)).annotations({
        title: `ExitEncoded<${format(value)}, ${format(error)}, ${format(defect)}>`
    });
};
const exitDecode = (input)=>{
    switch(input._tag){
        case "Failure":
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(causeDecode(input.cause));
        case "Success":
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(input.value);
    }
};
const exitArbitrary = (value, error, defect)=>(fc)=>fc.oneof(fc.record({
            _tag: fc.constant("Failure"),
            cause: causeArbitrary(error, defect)(fc)
        }), fc.record({
            _tag: fc.constant("Success"),
            value: value(fc)
        })).map(exitDecode);
const exitPretty = (value, error)=>(exit)=>exit._tag === "Failure" ? `Exit.failCause(${causePretty(error)(exit.cause)})` : `Exit.succeed(${value(exit.value)})`;
const exitParse = (decodeUnknownValue, decodeUnknownCause)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isExit"])(u) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(u, {
            onFailure: (cause)=>toComposite(decodeUnknownCause(cause, options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"], ast, u),
            onSuccess: (value)=>toComposite(decodeUnknownValue(value, options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"], ast, u)
        }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const ExitFromSelf = ({ defect, failure, success })=>declare([
        success,
        failure,
        defect
    ], {
        decode: (success, failure, defect)=>exitParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(success), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(CauseFromSelf({
                error: failure,
                defect
            }))),
        encode: (success, failure, defect)=>exitParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(success), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(CauseFromSelf({
                error: failure,
                defect
            })))
    }, {
        title: `Exit<${success.ast}, ${failure.ast}>`,
        pretty: exitPretty,
        arbitrary: exitArbitrary
    });
const Exit = ({ defect, failure, success })=>{
    const success_ = asSchema(success);
    const failure_ = asSchema(failure);
    const defect_ = asSchema(defect);
    const out = transform(exitEncoded(success_, failure_, defect_), ExitFromSelf({
        failure: typeSchema(failure_),
        success: typeSchema(success_),
        defect: typeSchema(defect_)
    }), {
        strict: false,
        decode: (i)=>exitDecode(i),
        encode: (a)=>a._tag === "Failure" ? {
                _tag: "Failure",
                cause: a.cause
            } : {
                _tag: "Success",
                value: a.value
            }
    });
    return out;
};
const hashSetArbitrary = (item, ctx)=>(fc)=>{
        const items = fc.array(item(fc));
        return (ctx.depthIdentifier !== undefined ? fc.oneof(ctx, fc.constant([]), items) : items).map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"]);
    };
const hashSetPretty = (item)=>(set)=>`HashSet(${Array.from(set).map((a)=>item(a)).join(", ")})`;
const hashSetEquivalence = (item)=>{
    const arrayEquivalence = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"])(item);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((a, b)=>arrayEquivalence(Array.from(a), Array.from(b)));
};
const hashSetParse = (decodeUnknown)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isHashSet"])(u) ? toComposite(decodeUnknown(Array.from(u), options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"], ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const HashSetFromSelf = (value)=>{
    return declare([
        value
    ], {
        decode: (item)=>hashSetParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(Array$(item))),
        encode: (item)=>hashSetParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(Array$(item)))
    }, {
        description: `HashSet<${format(value)}>`,
        pretty: hashSetPretty,
        arbitrary: hashSetArbitrary,
        equivalence: hashSetEquivalence
    });
};
function HashSet(value) {
    return transform(Array$(value), HashSetFromSelf(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(i),
        encode: (a)=>Array.from(a)
    });
}
const hashMapArbitrary = (key, value, ctx)=>(fc)=>{
        const items = fc.array(fc.tuple(key(fc), value(fc)));
        return (ctx.depthIdentifier !== undefined ? fc.oneof(ctx, fc.constant([]), items) : items).map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"]);
    };
const hashMapPretty = (key, value)=>(map)=>`HashMap([${Array.from(map).map(([k, v])=>`[${key(k)}, ${value(v)}]`).join(", ")}])`;
const hashMapEquivalence = (key, value)=>{
    const arrayEquivalence = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(([ka, va], [kb, vb])=>key(ka, kb) && value(va, vb)));
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((a, b)=>arrayEquivalence(Array.from(a), Array.from(b)));
};
const hashMapParse = (decodeUnknown)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isHashMap"])(u) ? toComposite(decodeUnknown(Array.from(u), options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"], ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const HashMapFromSelf = ({ key, value })=>{
    return declare([
        key,
        value
    ], {
        decode: (key, value)=>hashMapParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(Array$(Tuple(key, value)))),
        encode: (key, value)=>hashMapParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(Array$(Tuple(key, value))))
    }, {
        description: `HashMap<${format(key)}, ${format(value)}>`,
        pretty: hashMapPretty,
        arbitrary: hashMapArbitrary,
        equivalence: hashMapEquivalence
    });
};
const HashMap = ({ key, value })=>{
    return transform(Array$(Tuple(key, value)), HashMapFromSelf({
        key: typeSchema(asSchema(key)),
        value: typeSchema(asSchema(value))
    }), {
        strict: true,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(i),
        encode: (a)=>Array.from(a)
    });
};
const listArbitrary = (item, ctx)=>(fc)=>{
        const items = fc.array(item(fc));
        return (ctx.depthIdentifier !== undefined ? fc.oneof(ctx, fc.constant([]), items) : items).map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$List$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"]);
    };
const listPretty = (item)=>(set)=>`List(${Array.from(set).map((a)=>item(a)).join(", ")})`;
const listEquivalence = (item)=>{
    const arrayEquivalence = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"])(item);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((a, b)=>arrayEquivalence(Array.from(a), Array.from(b)));
};
const listParse = (decodeUnknown)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$List$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isList"])(u) ? toComposite(decodeUnknown(Array.from(u), options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$List$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"], ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const ListFromSelf = (value)=>{
    return declare([
        value
    ], {
        decode: (item)=>listParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(Array$(item))),
        encode: (item)=>listParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(Array$(item)))
    }, {
        description: `List<${format(value)}>`,
        pretty: listPretty,
        arbitrary: listArbitrary,
        equivalence: listEquivalence
    });
};
function List(value) {
    return transform(Array$(value), ListFromSelf(typeSchema(asSchema(value))), {
        strict: true,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$List$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(i),
        encode: (a)=>Array.from(a)
    });
}
const sortedSetArbitrary = (item, ord, ctx)=>(fc)=>{
        const items = fc.array(item(fc));
        return (ctx.depthIdentifier !== undefined ? fc.oneof(ctx, fc.constant([]), items) : items).map((as)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SortedSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(as, ord));
    };
const sortedSetPretty = (item)=>(set)=>`new SortedSet([${Array.from((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SortedSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["values"])(set)).map((a)=>item(a)).join(", ")}])`;
const sortedSetParse = (decodeUnknown, ord)=>(u, options, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SortedSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSortedSet"])(u) ? toComposite(decodeUnknown(Array.from((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SortedSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["values"])(u)), options), (as)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SortedSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(as, ord), ast, u) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Type"](ast, u));
const SortedSetFromSelf = (value, ordA, ordI)=>{
    return declare([
        value
    ], {
        decode: (item)=>sortedSetParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknown"])(Array$(item)), ordA),
        encode: (item)=>sortedSetParse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeUnknown"])(Array$(item)), ordI)
    }, {
        description: `SortedSet<${format(value)}>`,
        pretty: sortedSetPretty,
        arbitrary: (arb, ctx)=>sortedSetArbitrary(arb, ordA, ctx),
        equivalence: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SortedSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalence"])()
    });
};
function SortedSet(value, ordA) {
    const to = typeSchema(asSchema(value));
    return transform(Array$(value), SortedSetFromSelf(to, ordA, ordA), {
        strict: true,
        decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SortedSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(i, ordA),
        encode: (a)=>Array.from((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SortedSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["values"])(a))
    });
}
class BooleanFromUnknown extends /*#__PURE__*/ transform(Unknown, Boolean$, {
    strict: true,
    decode: (i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTruthy"])(i),
    encode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
}).annotations({
    identifier: "BooleanFromUnknown"
}) {
}
class BooleanFromString extends /*#__PURE__*/ transform(Literal("true", "false").annotations({
    description: "a string to be decoded into a boolean"
}), Boolean$, {
    strict: true,
    decode: (i)=>i === "true",
    encode: (a)=>a ? "true" : "false"
}).annotations({
    identifier: "BooleanFromString"
}) {
}
const Config = (name, schema)=>{
    const decodeUnknownEither = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["decodeUnknownEither"])(schema);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Config$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])(name).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Config$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapOrFail"])((s)=>decodeUnknownEither(s).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapLeft"])((error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ConfigError$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["InvalidData"])([], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TreeFormatter"].formatIssueSync(error))))));
};
const symbolSerializable = /*#__PURE__*/ Symbol.for("effect/Schema/Serializable/symbol");
const asSerializable = (serializable)=>serializable;
const serializableSchema = (self)=>self[symbolSerializable];
const serialize = (self)=>encodeUnknown(self[symbolSerializable])(self);
const deserialize = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>decodeUnknown(self[symbolSerializable])(value));
const symbolWithResult = /*#__PURE__*/ Symbol.for("effect/Schema/Serializable/symbolResult");
const asWithResult = (withExit)=>withExit;
const failureSchema = (self)=>self[symbolWithResult].failure;
const successSchema = (self)=>self[symbolWithResult].success;
const exitSchemaCache = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])("effect/Schema/Serializable/exitSchemaCache", ()=>new WeakMap());
const exitSchema = (self)=>{
    const proto = Object.getPrototypeOf(self);
    if (!(symbolWithResult in proto)) {
        return Exit({
            failure: failureSchema(self),
            success: successSchema(self),
            defect: Defect
        });
    }
    let schema = exitSchemaCache.get(proto);
    if (schema === undefined) {
        schema = Exit({
            failure: failureSchema(self),
            success: successSchema(self),
            defect: Defect
        });
        exitSchemaCache.set(proto, schema);
    }
    return schema;
};
const serializeFailure = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>encode(self[symbolWithResult].failure)(value));
const deserializeFailure = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>decodeUnknown(self[symbolWithResult].failure)(value));
const serializeSuccess = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>encode(self[symbolWithResult].success)(value));
const deserializeSuccess = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>decodeUnknown(self[symbolWithResult].success)(value));
const serializeExit = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>encode(exitSchema(self))(value));
const deserializeExit = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>decodeUnknown(exitSchema(self))(value));
const asSerializableWithResult = (procedure)=>procedure;
const TaggedRequest = (identifier)=>(tag, options, annotations)=>{
        const taggedFields = extendFields({
            _tag: getClassTag(tag)
        }, options.payload);
        return class TaggedRequestClass extends makeClass({
            kind: "TaggedRequest",
            identifier: identifier ?? tag,
            schema: Struct(taggedFields),
            fields: taggedFields,
            Base: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Request$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Class"],
            annotations
        }) {
            static _tag = tag;
            static success = options.success;
            static failure = options.failure;
            get [symbolSerializable]() {
                return this.constructor;
            }
            get [symbolWithResult]() {
                return {
                    failure: options.failure,
                    success: options.success
                };
            }
        };
    };
const equivalence = (schema)=>go(schema.ast, []);
const getEquivalenceAnnotation = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAnnotation"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EquivalenceAnnotationId"]);
const go = (ast, path)=>{
    const hook = getEquivalenceAnnotation(ast);
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSome"])(hook)) {
        switch(ast._tag){
            case "Declaration":
                return hook.value(...ast.typeParameters.map((tp)=>go(tp, path)));
            case "Refinement":
                return hook.value(go(ast.from, path));
            default:
                return hook.value();
        }
    }
    switch(ast._tag){
        case "NeverKeyword":
            throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$errors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEquivalenceUnsupportedErrorMessage"])(ast, path));
        case "Transformation":
            return go(ast.to, path);
        case "Declaration":
        case "Literal":
        case "StringKeyword":
        case "TemplateLiteral":
        case "UniqueSymbol":
        case "SymbolKeyword":
        case "UnknownKeyword":
        case "AnyKeyword":
        case "NumberKeyword":
        case "BooleanKeyword":
        case "BigIntKeyword":
        case "UndefinedKeyword":
        case "VoidKeyword":
        case "Enums":
        case "ObjectKeyword":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"];
        case "Refinement":
            return go(ast.from, path);
        case "Suspend":
            {
                const get = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["memoizeThunk"])(()=>go(ast.f(), path));
                return (a, b)=>get()(a, b);
            }
        case "TupleType":
            {
                const elements = ast.elements.map((element, i)=>go(element.type, path.concat(i)));
                const rest = ast.rest.map((annotatedAST)=>go(annotatedAST.type, path));
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((a, b)=>{
                    const len = a.length;
                    if (len !== b.length) {
                        return false;
                    }
                    // ---------------------------------------------
                    // handle elements
                    // ---------------------------------------------
                    let i = 0;
                    for(; i < Math.min(len, ast.elements.length); i++){
                        if (!elements[i](a[i], b[i])) {
                            return false;
                        }
                    }
                    // ---------------------------------------------
                    // handle rest element
                    // ---------------------------------------------
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmptyReadonlyArray"])(rest)) {
                        const [head, ...tail] = rest;
                        for(; i < len - tail.length; i++){
                            if (!head(a[i], b[i])) {
                                return false;
                            }
                        }
                        // ---------------------------------------------
                        // handle post rest elements
                        // ---------------------------------------------
                        for(let j = 0; j < tail.length; j++){
                            i += j;
                            if (!tail[j](a[i], b[i])) {
                                return false;
                            }
                        }
                    }
                    return true;
                });
            }
        case "TypeLiteral":
            {
                if (ast.propertySignatures.length === 0 && ast.indexSignatures.length === 0) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"];
                }
                const propertySignatures = ast.propertySignatures.map((ps)=>go(ps.type, path.concat(ps.name)));
                const indexSignatures = ast.indexSignatures.map((is)=>go(is.type, path));
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((a, b)=>{
                    const aStringKeys = Object.keys(a);
                    const aSymbolKeys = Object.getOwnPropertySymbols(a);
                    // ---------------------------------------------
                    // handle property signatures
                    // ---------------------------------------------
                    for(let i = 0; i < propertySignatures.length; i++){
                        const ps = ast.propertySignatures[i];
                        const name = ps.name;
                        const aHas = Object.prototype.hasOwnProperty.call(a, name);
                        const bHas = Object.prototype.hasOwnProperty.call(b, name);
                        if (ps.isOptional) {
                            if (aHas !== bHas) {
                                return false;
                            }
                        }
                        if (aHas && bHas && !propertySignatures[i](a[name], b[name])) {
                            return false;
                        }
                    }
                    // ---------------------------------------------
                    // handle index signatures
                    // ---------------------------------------------
                    let bSymbolKeys;
                    let bStringKeys;
                    for(let i = 0; i < indexSignatures.length; i++){
                        const is = ast.indexSignatures[i];
                        const encodedParameter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEncodedParameter"])(is.parameter);
                        const isSymbol = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$SchemaAST$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSymbolKeyword"])(encodedParameter);
                        if (isSymbol) {
                            bSymbolKeys = bSymbolKeys || Object.getOwnPropertySymbols(b);
                            if (aSymbolKeys.length !== bSymbolKeys.length) {
                                return false;
                            }
                        } else {
                            bStringKeys = bStringKeys || Object.keys(b);
                            if (aStringKeys.length !== bStringKeys.length) {
                                return false;
                            }
                        }
                        const aKeys = isSymbol ? aSymbolKeys : aStringKeys;
                        for(let j = 0; j < aKeys.length; j++){
                            const key = aKeys[j];
                            if (!Object.prototype.hasOwnProperty.call(b, key) || !indexSignatures[i](a[key], b[key])) {
                                return false;
                            }
                        }
                    }
                    return true;
                });
            }
        case "Union":
            {
                const searchTree = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSearchTree"])(ast.types, true);
                const ownKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$schema$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ownKeys"])(searchTree.keys);
                const len = ownKeys.length;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equivalence$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((a, b)=>{
                    let candidates = [];
                    if (len > 0 && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isRecordOrArray"])(a)) {
                        for(let i = 0; i < len; i++){
                            const name = ownKeys[i];
                            const buckets = searchTree.keys[name].buckets;
                            if (Object.prototype.hasOwnProperty.call(a, name)) {
                                const literal = String(a[name]);
                                if (Object.prototype.hasOwnProperty.call(buckets, literal)) {
                                    candidates = candidates.concat(buckets[literal]);
                                }
                            }
                        }
                    }
                    if (searchTree.otherwise.length > 0) {
                        candidates = candidates.concat(searchTree.otherwise);
                    }
                    const tuples = candidates.map((ast)=>[
                            go(ast, path),
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])({
                                ast
                            })
                        ]);
                    for(let i = 0; i < tuples.length; i++){
                        const [equivalence, is] = tuples[i];
                        if (is(a) && is(b)) {
                            if (equivalence(a, b)) {
                                return true;
                            }
                        }
                    }
                    return false;
                });
            }
    }
};
const SymbolStruct = /*#__PURE__*/ TaggedStruct("symbol", {
    key: String$
}).annotations({
    description: "an object to be decoded into a globally shared symbol"
});
const SymbolFromStruct = /*#__PURE__*/ transformOrFail(SymbolStruct, SymbolFromSelf, {
    strict: true,
    decode: (i)=>decodeSymbol(i.key),
    encode: (a, _, ast)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ParseResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(encodeSymbol(a, ast), (key)=>SymbolStruct.make({
                key
            }))
});
/** @ignore */ class PropertyKey$ extends /*#__PURE__*/ Union(String$, Number$, SymbolFromStruct).annotations({
    identifier: "PropertyKey"
}) {
}
;
class ArrayFormatterIssue extends /*#__PURE__*/ Struct({
    _tag: propertySignature(Literal("Pointer", "Unexpected", "Missing", "Composite", "Refinement", "Transformation", "Type", "Forbidden")).annotations({
        description: "The tag identifying the type of parse issue"
    }),
    path: propertySignature(Array$(PropertyKey$)).annotations({
        description: "The path to the property where the issue occurred"
    }),
    message: propertySignature(String$).annotations({
        description: "A descriptive message explaining the issue"
    })
}).annotations({
    identifier: "ArrayFormatterIssue",
    description: "Represents an issue returned by the ArrayFormatter formatter"
}) {
} //# sourceMappingURL=Schema.js.map
}}),

};

//# sourceMappingURL=node_modules_effect_dist_esm_Schema_a7e1fe85.js.map