module.exports = {

"[project]/node_modules/effect/dist/esm/internal/core.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CloseableScopeTypeId": (()=>CloseableScopeTypeId),
    "EffectTypeId": (()=>EffectTypeId),
    "ExceededCapacityException": (()=>ExceededCapacityException),
    "ExceededCapacityExceptionTypeId": (()=>ExceededCapacityExceptionTypeId),
    "FiberRefTypeId": (()=>FiberRefTypeId),
    "IllegalArgumentException": (()=>IllegalArgumentException),
    "IllegalArgumentExceptionTypeId": (()=>IllegalArgumentExceptionTypeId),
    "InterruptedException": (()=>InterruptedException),
    "InterruptedExceptionTypeId": (()=>InterruptedExceptionTypeId),
    "InvalidPubSubCapacityException": (()=>InvalidPubSubCapacityException),
    "InvalidPubSubCapacityExceptionTypeId": (()=>InvalidPubSubCapacityExceptionTypeId),
    "NoSuchElementException": (()=>NoSuchElementException),
    "NoSuchElementExceptionTypeId": (()=>NoSuchElementExceptionTypeId),
    "RequestResolverImpl": (()=>RequestResolverImpl),
    "RequestResolverTypeId": (()=>RequestResolverTypeId),
    "RevertFlags": (()=>RevertFlags),
    "RuntimeException": (()=>RuntimeException),
    "RuntimeExceptionTypeId": (()=>RuntimeExceptionTypeId),
    "ScopeTypeId": (()=>ScopeTypeId),
    "TimeoutException": (()=>TimeoutException),
    "TimeoutExceptionTypeId": (()=>TimeoutExceptionTypeId),
    "UnknownException": (()=>UnknownException),
    "UnknownExceptionTypeId": (()=>UnknownExceptionTypeId),
    "YieldableError": (()=>YieldableError),
    "acquireUseRelease": (()=>acquireUseRelease),
    "allLogLevels": (()=>allLogLevels),
    "andThen": (()=>andThen),
    "as": (()=>as),
    "asVoid": (()=>asVoid),
    "async": (()=>async_),
    "asyncInterrupt": (()=>asyncInterrupt),
    "attemptOrElse": (()=>attemptOrElse),
    "blocked": (()=>blocked),
    "capture": (()=>capture),
    "catchAll": (()=>catchAll),
    "catchAllCause": (()=>catchAllCause),
    "catchIf": (()=>catchIf),
    "catchSome": (()=>catchSome),
    "causeSquash": (()=>causeSquash),
    "causeSquashWith": (()=>causeSquashWith),
    "checkInterruptible": (()=>checkInterruptible),
    "context": (()=>context),
    "contextWith": (()=>contextWith),
    "contextWithEffect": (()=>contextWithEffect),
    "currentConcurrency": (()=>currentConcurrency),
    "currentContext": (()=>currentContext),
    "currentForkScopeOverride": (()=>currentForkScopeOverride),
    "currentInterruptedCause": (()=>currentInterruptedCause),
    "currentLogAnnotations": (()=>currentLogAnnotations),
    "currentLogLevel": (()=>currentLogLevel),
    "currentLogSpan": (()=>currentLogSpan),
    "currentMaxOpsBeforeYield": (()=>currentMaxOpsBeforeYield),
    "currentMetricLabels": (()=>currentMetricLabels),
    "currentRequestBatching": (()=>currentRequestBatching),
    "currentSchedulingPriority": (()=>currentSchedulingPriority),
    "currentSpanFromFiber": (()=>currentSpanFromFiber),
    "currentTracerEnabled": (()=>currentTracerEnabled),
    "currentTracerSpanAnnotations": (()=>currentTracerSpanAnnotations),
    "currentTracerSpanLinks": (()=>currentTracerSpanLinks),
    "currentTracerTimingEnabled": (()=>currentTracerTimingEnabled),
    "currentUnhandledErrorLogLevel": (()=>currentUnhandledErrorLogLevel),
    "custom": (()=>custom),
    "deferredAwait": (()=>deferredAwait),
    "deferredComplete": (()=>deferredComplete),
    "deferredCompleteWith": (()=>deferredCompleteWith),
    "deferredDie": (()=>deferredDie),
    "deferredDieSync": (()=>deferredDieSync),
    "deferredDone": (()=>deferredDone),
    "deferredFail": (()=>deferredFail),
    "deferredFailCause": (()=>deferredFailCause),
    "deferredFailCauseSync": (()=>deferredFailCauseSync),
    "deferredFailSync": (()=>deferredFailSync),
    "deferredInterrupt": (()=>deferredInterrupt),
    "deferredInterruptWith": (()=>deferredInterruptWith),
    "deferredIsDone": (()=>deferredIsDone),
    "deferredMake": (()=>deferredMake),
    "deferredMakeAs": (()=>deferredMakeAs),
    "deferredPoll": (()=>deferredPoll),
    "deferredSucceed": (()=>deferredSucceed),
    "deferredSync": (()=>deferredSync),
    "deferredUnsafeDone": (()=>deferredUnsafeDone),
    "deferredUnsafeMake": (()=>deferredUnsafeMake),
    "die": (()=>die),
    "dieMessage": (()=>dieMessage),
    "dieSync": (()=>dieSync),
    "either": (()=>either),
    "exit": (()=>exit),
    "exitAs": (()=>exitAs),
    "exitAsVoid": (()=>exitAsVoid),
    "exitCauseOption": (()=>exitCauseOption),
    "exitCollectAll": (()=>exitCollectAll),
    "exitDie": (()=>exitDie),
    "exitExists": (()=>exitExists),
    "exitFail": (()=>exitFail),
    "exitFailCause": (()=>exitFailCause),
    "exitFlatMap": (()=>exitFlatMap),
    "exitFlatMapEffect": (()=>exitFlatMapEffect),
    "exitFlatten": (()=>exitFlatten),
    "exitForEachEffect": (()=>exitForEachEffect),
    "exitFromEither": (()=>exitFromEither),
    "exitFromOption": (()=>exitFromOption),
    "exitGetOrElse": (()=>exitGetOrElse),
    "exitInterrupt": (()=>exitInterrupt),
    "exitIsExit": (()=>exitIsExit),
    "exitIsFailure": (()=>exitIsFailure),
    "exitIsInterrupted": (()=>exitIsInterrupted),
    "exitIsSuccess": (()=>exitIsSuccess),
    "exitMap": (()=>exitMap),
    "exitMapBoth": (()=>exitMapBoth),
    "exitMapError": (()=>exitMapError),
    "exitMapErrorCause": (()=>exitMapErrorCause),
    "exitMatch": (()=>exitMatch),
    "exitMatchEffect": (()=>exitMatchEffect),
    "exitSucceed": (()=>exitSucceed),
    "exitVoid": (()=>exitVoid),
    "exitZip": (()=>exitZip),
    "exitZipLeft": (()=>exitZipLeft),
    "exitZipPar": (()=>exitZipPar),
    "exitZipParLeft": (()=>exitZipParLeft),
    "exitZipParRight": (()=>exitZipParRight),
    "exitZipRight": (()=>exitZipRight),
    "exitZipWith": (()=>exitZipWith),
    "fail": (()=>fail),
    "failCause": (()=>failCause),
    "failCauseSync": (()=>failCauseSync),
    "failSync": (()=>failSync),
    "fiberId": (()=>fiberId),
    "fiberIdWith": (()=>fiberIdWith),
    "fiberRefDelete": (()=>fiberRefDelete),
    "fiberRefGet": (()=>fiberRefGet),
    "fiberRefGetAndSet": (()=>fiberRefGetAndSet),
    "fiberRefGetAndUpdate": (()=>fiberRefGetAndUpdate),
    "fiberRefGetAndUpdateSome": (()=>fiberRefGetAndUpdateSome),
    "fiberRefGetWith": (()=>fiberRefGetWith),
    "fiberRefLocally": (()=>fiberRefLocally),
    "fiberRefLocallyWith": (()=>fiberRefLocallyWith),
    "fiberRefModify": (()=>fiberRefModify),
    "fiberRefModifySome": (()=>fiberRefModifySome),
    "fiberRefReset": (()=>fiberRefReset),
    "fiberRefSet": (()=>fiberRefSet),
    "fiberRefUnsafeMake": (()=>fiberRefUnsafeMake),
    "fiberRefUnsafeMakeContext": (()=>fiberRefUnsafeMakeContext),
    "fiberRefUnsafeMakeHashSet": (()=>fiberRefUnsafeMakeHashSet),
    "fiberRefUnsafeMakePatch": (()=>fiberRefUnsafeMakePatch),
    "fiberRefUnsafeMakeReadonlyArray": (()=>fiberRefUnsafeMakeReadonlyArray),
    "fiberRefUnsafeMakeRuntimeFlags": (()=>fiberRefUnsafeMakeRuntimeFlags),
    "fiberRefUpdate": (()=>fiberRefUpdate),
    "fiberRefUpdateAndGet": (()=>fiberRefUpdateAndGet),
    "fiberRefUpdateSome": (()=>fiberRefUpdateSome),
    "fiberRefUpdateSomeAndGet": (()=>fiberRefUpdateSomeAndGet),
    "filterEffectOrElse": (()=>filterEffectOrElse),
    "filterEffectOrFail": (()=>filterEffectOrFail),
    "flatMap": (()=>flatMap),
    "flatten": (()=>flatten),
    "flip": (()=>flip),
    "fnUntraced": (()=>fnUntraced),
    "forEachSequential": (()=>forEachSequential),
    "forEachSequentialDiscard": (()=>forEachSequentialDiscard),
    "fromIterator": (()=>fromIterator),
    "gen": (()=>gen),
    "if_": (()=>if_),
    "interrupt": (()=>interrupt),
    "interruptAsFiber": (()=>interruptAsFiber),
    "interruptFiber": (()=>interruptFiber),
    "interruptWith": (()=>interruptWith),
    "interruptible": (()=>interruptible),
    "interruptibleMask": (()=>interruptibleMask),
    "intoDeferred": (()=>intoDeferred),
    "isEffect": (()=>isEffect),
    "isExceededCapacityException": (()=>isExceededCapacityException),
    "isIllegalArgumentException": (()=>isIllegalArgumentException),
    "isInterruptedException": (()=>isInterruptedException),
    "isInvalidCapacityError": (()=>isInvalidCapacityError),
    "isNoSuchElementException": (()=>isNoSuchElementException),
    "isRequestResolver": (()=>isRequestResolver),
    "isRuntimeException": (()=>isRuntimeException),
    "isTimeoutException": (()=>isTimeoutException),
    "isUnknownException": (()=>isUnknownException),
    "logLevelAll": (()=>logLevelAll),
    "logLevelDebug": (()=>logLevelDebug),
    "logLevelError": (()=>logLevelError),
    "logLevelFatal": (()=>logLevelFatal),
    "logLevelInfo": (()=>logLevelInfo),
    "logLevelNone": (()=>logLevelNone),
    "logLevelTrace": (()=>logLevelTrace),
    "logLevelWarning": (()=>logLevelWarning),
    "map": (()=>map),
    "mapBoth": (()=>mapBoth),
    "mapError": (()=>mapError),
    "mapInputContext": (()=>mapInputContext),
    "matchCause": (()=>matchCause),
    "matchCauseEffect": (()=>matchCauseEffect),
    "matchEffect": (()=>matchEffect),
    "metricLabels": (()=>metricLabels),
    "never": (()=>never),
    "noopSpan": (()=>noopSpan),
    "onError": (()=>onError),
    "onExit": (()=>onExit),
    "onInterrupt": (()=>onInterrupt),
    "orDie": (()=>orDie),
    "orDieWith": (()=>orDieWith),
    "orElse": (()=>orElse),
    "originalInstance": (()=>originalInstance),
    "partitionMap": (()=>partitionMap),
    "provideContext": (()=>provideContext),
    "provideSomeContext": (()=>provideSomeContext),
    "requestBlockLocally": (()=>requestBlockLocally),
    "resolverLocally": (()=>resolverLocally),
    "runRequestBlock": (()=>runRequestBlock),
    "runtimeFlags": (()=>runtimeFlags),
    "scopeAddFinalizer": (()=>scopeAddFinalizer),
    "scopeAddFinalizerExit": (()=>scopeAddFinalizerExit),
    "scopeClose": (()=>scopeClose),
    "scopeFork": (()=>scopeFork),
    "step": (()=>step),
    "succeed": (()=>succeed),
    "suspend": (()=>suspend),
    "sync": (()=>sync),
    "tap": (()=>tap),
    "timeoutExceptionFromDuration": (()=>timeoutExceptionFromDuration),
    "transplant": (()=>transplant),
    "uninterruptible": (()=>uninterruptible),
    "uninterruptibleMask": (()=>uninterruptibleMask),
    "unsafeAsync": (()=>unsafeAsync),
    "updateRuntimeFlags": (()=>updateRuntimeFlags),
    "void": (()=>void_),
    "whenEffect": (()=>whenEffect),
    "whileLoop": (()=>whileLoop),
    "withConcurrency": (()=>withConcurrency),
    "withFiberRuntime": (()=>withFiberRuntime),
    "withMaxOpsBeforeYield": (()=>withMaxOpsBeforeYield),
    "withRequestBatching": (()=>withRequestBatching),
    "withRuntimeFlags": (()=>withRuntimeFlags),
    "withSchedulingPriority": (()=>withSchedulingPriority),
    "withTracerEnabled": (()=>withTracerEnabled),
    "withTracerTiming": (()=>withTracerTiming),
    "withUnhandledErrorLogLevel": (()=>withUnhandledErrorLogLevel),
    "yieldNow": (()=>yieldNow),
    "zip": (()=>zip),
    "zipFlatten": (()=>zipFlatten),
    "zipLeft": (()=>zipLeft),
    "zipRight": (()=>zipRight),
    "zipWith": (()=>zipWith)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Array.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Chunk.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Context.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Duration.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Either.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Equal.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/FiberId.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/GlobalValue.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Hash.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/HashMap.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Inspectable.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$List$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/List.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/MutableRef.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Option.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Pipeable.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$RuntimeFlagsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/RuntimeFlagsPatch.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$blockedRequests$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/blockedRequests.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/cause.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/deferred.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$differ$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/differ.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/effectable.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$errors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/errors.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/deferred.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/runtimeFlags.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$singleShotGen$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/singleShotGen.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const blocked = (blockedRequests, _continue)=>{
    const effect = new EffectPrimitive("Blocked");
    effect.effect_instruction_i0 = blockedRequests;
    effect.effect_instruction_i1 = _continue;
    return effect;
};
const runRequestBlock = (blockedRequests)=>{
    const effect = new EffectPrimitive("RunBlocked");
    effect.effect_instruction_i0 = blockedRequests;
    return effect;
};
const EffectTypeId = /*#__PURE__*/ Symbol.for("effect/Effect");
class RevertFlags {
    patch;
    op;
    _op = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_REVERT_FLAGS"];
    constructor(patch, op){
        this.patch = patch;
        this.op = op;
    }
}
class EffectPrimitive {
    _op;
    effect_instruction_i0 = undefined;
    effect_instruction_i1 = undefined;
    effect_instruction_i2 = undefined;
    trace = undefined;
    [EffectTypeId] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effectVariance"];
    constructor(_op){
        this._op = _op;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]](that) {
        return this === that;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]]() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cached"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["random"])(this));
    }
    pipe() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
    toJSON() {
        return {
            _id: "Effect",
            _op: this._op,
            effect_instruction_i0: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toJSON"])(this.effect_instruction_i0),
            effect_instruction_i1: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toJSON"])(this.effect_instruction_i1),
            effect_instruction_i2: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toJSON"])(this.effect_instruction_i2)
        };
    }
    toString() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])(this.toJSON());
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NodeInspectSymbol"]]() {
        return this.toJSON();
    }
    [Symbol.iterator]() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$singleShotGen$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SingleShotGen"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["YieldWrap"](this));
    }
}
/** @internal */ class EffectPrimitiveFailure {
    _op;
    effect_instruction_i0 = undefined;
    effect_instruction_i1 = undefined;
    effect_instruction_i2 = undefined;
    trace = undefined;
    [EffectTypeId] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effectVariance"];
    constructor(_op){
        this._op = _op;
        // @ts-expect-error
        this._tag = _op;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]](that) {
        return exitIsExit(that) && that._op === "Failure" && // @ts-expect-error
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"])(this.effect_instruction_i0, that.effect_instruction_i0);
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]]() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(// @ts-expect-error
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])(this._tag), // @ts-expect-error
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(this.effect_instruction_i0)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cached"])(this));
    }
    get cause() {
        return this.effect_instruction_i0;
    }
    pipe() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
    toJSON() {
        return {
            _id: "Exit",
            _tag: this._op,
            cause: this.cause.toJSON()
        };
    }
    toString() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])(this.toJSON());
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NodeInspectSymbol"]]() {
        return this.toJSON();
    }
    [Symbol.iterator]() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$singleShotGen$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SingleShotGen"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["YieldWrap"](this));
    }
}
/** @internal */ class EffectPrimitiveSuccess {
    _op;
    effect_instruction_i0 = undefined;
    effect_instruction_i1 = undefined;
    effect_instruction_i2 = undefined;
    trace = undefined;
    [EffectTypeId] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effectVariance"];
    constructor(_op){
        this._op = _op;
        // @ts-expect-error
        this._tag = _op;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]](that) {
        return exitIsExit(that) && that._op === "Success" && // @ts-expect-error
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"])(this.effect_instruction_i0, that.effect_instruction_i0);
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]]() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(// @ts-expect-error
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])(this._tag), // @ts-expect-error
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(this.effect_instruction_i0)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cached"])(this));
    }
    get value() {
        return this.effect_instruction_i0;
    }
    pipe() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
    toJSON() {
        return {
            _id: "Exit",
            _tag: this._op,
            value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toJSON"])(this.value)
        };
    }
    toString() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])(this.toJSON());
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NodeInspectSymbol"]]() {
        return this.toJSON();
    }
    [Symbol.iterator]() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$singleShotGen$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SingleShotGen"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["YieldWrap"](this));
    }
}
const isEffect = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, EffectTypeId);
const withFiberRuntime = (withRuntime)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_WITH_RUNTIME"]);
    effect.effect_instruction_i0 = withRuntime;
    return effect;
};
const acquireUseRelease = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (acquire, use, release)=>uninterruptibleMask((restore)=>flatMap(acquire, (a)=>flatMap(exit(suspend(()=>restore(use(a)))), (exit)=>{
                return suspend(()=>release(a, exit)).pipe(matchCauseEffect({
                    onFailure: (cause)=>{
                        switch(exit._tag){
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
                                return failCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"])(exit.effect_instruction_i0, cause));
                            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
                                return failCause(cause);
                        }
                    },
                    onSuccess: ()=>exit
                }));
            }))));
const as = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>flatMap(self, ()=>succeed(value)));
const asVoid = (self)=>as(self, void 0);
const custom = function() {
    const wrapper = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_COMMIT"]);
    switch(arguments.length){
        case 2:
            {
                wrapper.effect_instruction_i0 = arguments[0];
                wrapper.commit = arguments[1];
                break;
            }
        case 3:
            {
                wrapper.effect_instruction_i0 = arguments[0];
                wrapper.effect_instruction_i1 = arguments[1];
                wrapper.commit = arguments[2];
                break;
            }
        case 4:
            {
                wrapper.effect_instruction_i0 = arguments[0];
                wrapper.effect_instruction_i1 = arguments[1];
                wrapper.effect_instruction_i2 = arguments[2];
                wrapper.commit = arguments[3];
                break;
            }
        default:
            {
                throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$errors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBugErrorMessage"])("you're not supposed to end up here"));
            }
    }
    return wrapper;
};
const unsafeAsync = (register, blockingOn = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ASYNC"]);
    let cancelerRef = undefined;
    effect.effect_instruction_i0 = (resume)=>{
        cancelerRef = register(resume);
    };
    effect.effect_instruction_i1 = blockingOn;
    return onInterrupt(effect, (_)=>isEffect(cancelerRef) ? cancelerRef : void_);
};
const asyncInterrupt = (register, blockingOn = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])=>suspend(()=>unsafeAsync(register, blockingOn));
const async_ = (resume, blockingOn = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])=>{
    return custom(resume, function() {
        let backingResume = undefined;
        let pendingEffect = undefined;
        function proxyResume(effect) {
            if (backingResume) {
                backingResume(effect);
            } else if (pendingEffect === undefined) {
                pendingEffect = effect;
            }
        }
        const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ASYNC"]);
        effect.effect_instruction_i0 = (resume)=>{
            backingResume = resume;
            if (pendingEffect) {
                resume(pendingEffect);
            }
        };
        effect.effect_instruction_i1 = blockingOn;
        let cancelerRef = undefined;
        let controllerRef = undefined;
        if (this.effect_instruction_i0.length !== 1) {
            controllerRef = new AbortController();
            cancelerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>this.effect_instruction_i0(proxyResume, controllerRef.signal));
        } else {
            cancelerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>this.effect_instruction_i0(proxyResume));
        }
        return cancelerRef || controllerRef ? onInterrupt(effect, (_)=>{
            if (controllerRef) {
                controllerRef.abort();
            }
            return cancelerRef ?? void_;
        }) : effect;
    });
};
;
const catchAllCause = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_FAILURE"]);
    effect.effect_instruction_i0 = self;
    effect.effect_instruction_i1 = f;
    return effect;
});
const catchAll = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>matchEffect(self, {
        onFailure: f,
        onSuccess: succeed
    }));
const catchIf = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, predicate, f)=>catchAllCause(self, (cause)=>{
        const either = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failureOrCause"])(cause);
        switch(either._tag){
            case "Left":
                return predicate(either.left) ? f(either.left) : failCause(cause);
            case "Right":
                return failCause(either.right);
        }
    }));
const catchSome = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>catchAllCause(self, (cause)=>{
        const either = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failureOrCause"])(cause);
        switch(either._tag){
            case "Left":
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(pf(either.left), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(()=>failCause(cause)));
            case "Right":
                return failCause(either.right);
        }
    }));
const checkInterruptible = (f)=>withFiberRuntime((_, status)=>f((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruption"])(status.runtimeFlags)));
const originalSymbol = /*#__PURE__*/ Symbol.for("effect/OriginalAnnotation");
const originalInstance = (obj)=>{
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(obj, originalSymbol)) {
        // @ts-expect-error
        return obj[originalSymbol];
    }
    return obj;
};
const capture = (obj, span)=>{
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSome"])(span)) {
        return new Proxy(obj, {
            has (target, p) {
                return p === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["spanSymbol"] || p === originalSymbol || p in target;
            },
            get (target, p) {
                if (p === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["spanSymbol"]) {
                    return span.value;
                }
                if (p === originalSymbol) {
                    return obj;
                }
                // @ts-expect-error
                return target[p];
            }
        });
    }
    return obj;
};
const die = (defect)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isObject"])(defect) && !(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["spanSymbol"] in defect) ? withFiberRuntime((fiber)=>failCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(capture(defect, currentSpanFromFiber(fiber))))) : failCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(defect));
const dieMessage = (message)=>failCauseSync(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(new RuntimeException(message)));
const dieSync = (evaluate)=>flatMap(sync(evaluate), die);
const either = (self)=>matchEffect(self, {
        onFailure: (e)=>succeed((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])(e)),
        onSuccess: (a)=>succeed((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"])(a))
    });
const exit = (self)=>matchCause(self, {
        onFailure: exitFailCause,
        onSuccess: exitSucceed
    });
const fail = (error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isObject"])(error) && !(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["spanSymbol"] in error) ? withFiberRuntime((fiber)=>failCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(capture(error, currentSpanFromFiber(fiber))))) : failCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(error));
const failSync = (evaluate)=>flatMap(sync(evaluate), fail);
const failCause = (cause)=>{
    const effect = new EffectPrimitiveFailure(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]);
    effect.effect_instruction_i0 = cause;
    return effect;
};
const failCauseSync = (evaluate)=>flatMap(sync(evaluate), failCause);
const fiberId = /*#__PURE__*/ withFiberRuntime((state)=>succeed(state.id()));
const fiberIdWith = (f)=>withFiberRuntime((state)=>f(state.id()));
const flatMap = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_SUCCESS"]);
    effect.effect_instruction_i0 = self;
    effect.effect_instruction_i1 = f;
    return effect;
});
const andThen = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>flatMap(self, (a)=>{
        const b = typeof f === "function" ? f(a) : f;
        if (isEffect(b)) {
            return b;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPromiseLike"])(b)) {
            return unsafeAsync((resume)=>{
                b.then((a)=>resume(succeed(a)), (e)=>resume(fail(new UnknownException(e, "An unknown error occurred in Effect.andThen"))));
            });
        }
        return succeed(b);
    }));
const step = (self)=>{
    const effect = new EffectPrimitive("OnStep");
    effect.effect_instruction_i0 = self;
    return effect;
};
const flatten = (self)=>flatMap(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]);
const flip = (self)=>matchEffect(self, {
        onFailure: succeed,
        onSuccess: fail
    });
const matchCause = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>matchCauseEffect(self, {
        onFailure: (cause)=>succeed(options.onFailure(cause)),
        onSuccess: (a)=>succeed(options.onSuccess(a))
    }));
const matchCauseEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_SUCCESS_AND_FAILURE"]);
    effect.effect_instruction_i0 = self;
    effect.effect_instruction_i1 = options.onFailure;
    effect.effect_instruction_i2 = options.onSuccess;
    return effect;
});
const matchEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>matchCauseEffect(self, {
        onFailure: (cause)=>{
            const defects = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["defects"])(cause);
            if (defects.length > 0) {
                return failCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["electFailures"])(cause));
            }
            const failures = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failures"])(cause);
            if (failures.length > 0) {
                return options.onFailure((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeHead"])(failures));
            }
            return failCause(cause);
        },
        onSuccess: options.onSuccess
    }));
const forEachSequential = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>suspend(()=>{
        const arr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(self);
        const ret = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["allocate"])(arr.length);
        let i = 0;
        return as(whileLoop({
            while: ()=>i < arr.length,
            body: ()=>f(arr[i], i),
            step: (b)=>{
                ret[i++] = b;
            }
        }), ret);
    }));
const forEachSequentialDiscard = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>suspend(()=>{
        const arr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(self);
        let i = 0;
        return whileLoop({
            while: ()=>i < arr.length,
            body: ()=>f(arr[i], i),
            step: ()=>{
                i++;
            }
        });
    }));
const if_ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>typeof args[0] === "boolean" || isEffect(args[0]), (self, options)=>isEffect(self) ? flatMap(self, (b)=>b ? options.onTrue() : options.onFalse()) : self ? options.onTrue() : options.onFalse());
const interrupt = /*#__PURE__*/ flatMap(fiberId, (fiberId)=>interruptWith(fiberId));
const interruptWith = (fiberId)=>failCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(fiberId));
const interruptible = (self)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_UPDATE_RUNTIME_FLAGS"]);
    effect.effect_instruction_i0 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$RuntimeFlagsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["enable"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Interruption"]);
    effect.effect_instruction_i1 = ()=>self;
    return effect;
};
const interruptibleMask = (f)=>custom(f, function() {
        const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_UPDATE_RUNTIME_FLAGS"]);
        effect.effect_instruction_i0 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$RuntimeFlagsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["enable"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Interruption"]);
        effect.effect_instruction_i1 = (oldFlags)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruption"])(oldFlags) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>this.effect_instruction_i0(interruptible)) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>this.effect_instruction_i0(uninterruptible));
        return effect;
    });
const intoDeferred = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, deferred)=>uninterruptibleMask((restore)=>flatMap(exit(restore(self)), (exit)=>deferredDone(deferred, exit))));
const map = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>flatMap(self, (a)=>sync(()=>f(a))));
const mapBoth = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>matchEffect(self, {
        onFailure: (e)=>failSync(()=>options.onFailure(e)),
        onSuccess: (a)=>sync(()=>options.onSuccess(a))
    }));
const mapError = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>matchCauseEffect(self, {
        onFailure: (cause)=>{
            const either = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failureOrCause"])(cause);
            switch(either._tag){
                case "Left":
                    {
                        return failSync(()=>f(either.left));
                    }
                case "Right":
                    {
                        return failCause(either.right);
                    }
            }
        },
        onSuccess: succeed
    }));
const onError = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, cleanup)=>onExit(self, (exit)=>exitIsSuccess(exit) ? void_ : cleanup(exit.effect_instruction_i0)));
const onExit = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, cleanup)=>uninterruptibleMask((restore)=>matchCauseEffect(restore(self), {
            onFailure: (cause1)=>{
                const result = exitFailCause(cause1);
                return matchCauseEffect(cleanup(result), {
                    onFailure: (cause2)=>exitFailCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"])(cause1, cause2)),
                    onSuccess: ()=>result
                });
            },
            onSuccess: (success)=>{
                const result = exitSucceed(success);
                return zipRight(cleanup(result), result);
            }
        })));
const onInterrupt = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, cleanup)=>onExit(self, exitMatch({
        onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isInterruptedOnly"])(cause) ? asVoid(cleanup((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptors"])(cause))) : void_,
        onSuccess: ()=>void_
    })));
const orElse = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>attemptOrElse(self, that, succeed));
const orDie = (self)=>orDieWith(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]);
const orDieWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>matchEffect(self, {
        onFailure: (e)=>die(f(e)),
        onSuccess: succeed
    }));
const partitionMap = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["partitionMap"];
const runtimeFlags = /*#__PURE__*/ withFiberRuntime((_, status)=>succeed(status.runtimeFlags));
const succeed = (value)=>{
    const effect = new EffectPrimitiveSuccess(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]);
    effect.effect_instruction_i0 = value;
    return effect;
};
const suspend = (evaluate)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_COMMIT"]);
    effect.commit = evaluate;
    return effect;
};
const sync = (thunk)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SYNC"]);
    effect.effect_instruction_i0 = thunk;
    return effect;
};
const tap = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>args.length === 3 || args.length === 2 && !((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isObject"])(args[1]) && "onlyEffect" in args[1]), (self, f)=>flatMap(self, (a)=>{
        const b = typeof f === "function" ? f(a) : f;
        if (isEffect(b)) {
            return as(b, a);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPromiseLike"])(b)) {
            return unsafeAsync((resume)=>{
                b.then((_)=>resume(succeed(a)), (e)=>resume(fail(new UnknownException(e, "An unknown error occurred in Effect.tap"))));
            });
        }
        return succeed(a);
    }));
const transplant = (f)=>withFiberRuntime((state)=>{
        const scopeOverride = state.getFiberRef(currentForkScopeOverride);
        const scope = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(scopeOverride, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(()=>state.scope()));
        return f(fiberRefLocally(currentForkScopeOverride, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(scope)));
    });
const attemptOrElse = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, that, onSuccess)=>matchCauseEffect(self, {
        onFailure: (cause)=>{
            const defects = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["defects"])(cause);
            if (defects.length > 0) {
                return failCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrThrow"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["keepDefectsAndElectFailures"])(cause)));
            }
            return that();
        },
        onSuccess
    }));
const uninterruptible = (self)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_UPDATE_RUNTIME_FLAGS"]);
    effect.effect_instruction_i0 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$RuntimeFlagsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["disable"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Interruption"]);
    effect.effect_instruction_i1 = ()=>self;
    return effect;
};
const uninterruptibleMask = (f)=>custom(f, function() {
        const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_UPDATE_RUNTIME_FLAGS"]);
        effect.effect_instruction_i0 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$RuntimeFlagsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["disable"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Interruption"]);
        effect.effect_instruction_i1 = (oldFlags)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruption"])(oldFlags) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>this.effect_instruction_i0(interruptible)) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>this.effect_instruction_i0(uninterruptible));
        return effect;
    });
const void_ = /*#__PURE__*/ succeed(void 0);
;
const updateRuntimeFlags = (patch)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_UPDATE_RUNTIME_FLAGS"]);
    effect.effect_instruction_i0 = patch;
    effect.effect_instruction_i1 = void 0;
    return effect;
};
const whenEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, condition)=>flatMap(condition, (b)=>{
        if (b) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"]));
        }
        return succeed((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])());
    }));
const whileLoop = (options)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_WHILE"]);
    effect.effect_instruction_i0 = options.while;
    effect.effect_instruction_i1 = options.body;
    effect.effect_instruction_i2 = options.step;
    return effect;
};
const fromIterator = (iterator)=>suspend(()=>{
        const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ITERATOR"]);
        effect.effect_instruction_i0 = iterator();
        return effect;
    });
const gen = function() {
    const f = arguments.length === 1 ? arguments[0] : arguments[1].bind(arguments[0]);
    return fromIterator(()=>f(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"]));
};
const fnUntraced = (body, ...pipeables)=>Object.defineProperty(pipeables.length === 0 ? function(...args) {
        return fromIterator(()=>body.apply(this, args));
    } : function(...args) {
        let effect = fromIterator(()=>body.apply(this, args));
        for (const x of pipeables){
            effect = x(effect, ...args);
        }
        return effect;
    }, "length", {
        value: body.length,
        configurable: true
    });
const withConcurrency = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, concurrency)=>fiberRefLocally(self, currentConcurrency, concurrency));
const withRequestBatching = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, requestBatching)=>fiberRefLocally(self, currentRequestBatching, requestBatching));
const withRuntimeFlags = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, update)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_UPDATE_RUNTIME_FLAGS"]);
    effect.effect_instruction_i0 = update;
    effect.effect_instruction_i1 = ()=>self;
    return effect;
});
const withTracerEnabled = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (effect, enabled)=>fiberRefLocally(effect, currentTracerEnabled, enabled));
const withTracerTiming = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (effect, enabled)=>fiberRefLocally(effect, currentTracerTimingEnabled, enabled));
const yieldNow = (options)=>{
    const effect = new EffectPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_YIELD"]);
    return typeof options?.priority !== "undefined" ? withSchedulingPriority(effect, options.priority) : effect;
};
const zip = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>flatMap(self, (a)=>map(that, (b)=>[
                a,
                b
            ])));
const zipFlatten = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>flatMap(self, (a)=>map(that, (b)=>[
                ...a,
                b
            ])));
const zipLeft = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>flatMap(self, (a)=>as(that, a)));
const zipRight = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>flatMap(self, ()=>that));
const zipWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, that, f)=>flatMap(self, (a)=>map(that, (b)=>f(a, b))));
const never = /*#__PURE__*/ asyncInterrupt(()=>{
    const interval = setInterval(()=>{
    //
    }, 2 ** 31 - 1);
    return sync(()=>clearInterval(interval));
});
const interruptFiber = (self)=>flatMap(fiberId, (fiberId)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, interruptAsFiber(fiberId)));
const interruptAsFiber = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, fiberId)=>flatMap(self.interruptAsFork(fiberId), ()=>self.await));
const logLevelAll = {
    _tag: "All",
    syslog: 0,
    label: "ALL",
    ordinal: Number.MIN_SAFE_INTEGER,
    pipe () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
};
const logLevelFatal = {
    _tag: "Fatal",
    syslog: 2,
    label: "FATAL",
    ordinal: 50000,
    pipe () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
};
const logLevelError = {
    _tag: "Error",
    syslog: 3,
    label: "ERROR",
    ordinal: 40000,
    pipe () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
};
const logLevelWarning = {
    _tag: "Warning",
    syslog: 4,
    label: "WARN",
    ordinal: 30000,
    pipe () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
};
const logLevelInfo = {
    _tag: "Info",
    syslog: 6,
    label: "INFO",
    ordinal: 20000,
    pipe () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
};
const logLevelDebug = {
    _tag: "Debug",
    syslog: 7,
    label: "DEBUG",
    ordinal: 10000,
    pipe () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
};
const logLevelTrace = {
    _tag: "Trace",
    syslog: 7,
    label: "TRACE",
    ordinal: 0,
    pipe () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
};
const logLevelNone = {
    _tag: "None",
    syslog: 7,
    label: "OFF",
    ordinal: Number.MAX_SAFE_INTEGER,
    pipe () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
};
const allLogLevels = [
    logLevelAll,
    logLevelTrace,
    logLevelDebug,
    logLevelInfo,
    logLevelWarning,
    logLevelError,
    logLevelFatal,
    logLevelNone
];
// -----------------------------------------------------------------------------
// FiberRef
// -----------------------------------------------------------------------------
/** @internal */ const FiberRefSymbolKey = "effect/FiberRef";
const FiberRefTypeId = /*#__PURE__*/ Symbol.for(FiberRefSymbolKey);
const fiberRefVariance = {
    /* c8 ignore next */ _A: (_)=>_
};
const fiberRefGet = (self)=>withFiberRuntime((fiber)=>exitSucceed(fiber.getFiberRef(self)));
const fiberRefGetAndSet = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>fiberRefModify(self, (v)=>[
            v,
            value
        ]));
const fiberRefGetAndUpdate = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>fiberRefModify(self, (v)=>[
            v,
            f(v)
        ]));
const fiberRefGetAndUpdateSome = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>fiberRefModify(self, (v)=>[
            v,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(pf(v), ()=>v)
        ]));
const fiberRefGetWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>flatMap(fiberRefGet(self), f));
const fiberRefSet = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>fiberRefModify(self, ()=>[
            void 0,
            value
        ]));
const fiberRefDelete = (self)=>withFiberRuntime((state)=>{
        state.unsafeDeleteFiberRef(self);
        return void_;
    });
const fiberRefReset = (self)=>fiberRefSet(self, self.initial);
const fiberRefModify = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>withFiberRuntime((state)=>{
        const [b, a] = f(state.getFiberRef(self));
        state.setFiberRef(self, a);
        return succeed(b);
    }));
const fiberRefModifySome = (self, def, f)=>fiberRefModify(self, (v)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(f(v), ()=>[
                def,
                v
            ]));
const fiberRefUpdate = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>fiberRefModify(self, (v)=>[
            void 0,
            f(v)
        ]));
const fiberRefUpdateSome = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>fiberRefModify(self, (v)=>[
            void 0,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(pf(v), ()=>v)
        ]));
const fiberRefUpdateAndGet = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>fiberRefModify(self, (v)=>{
        const result = f(v);
        return [
            result,
            result
        ];
    }));
const fiberRefUpdateSomeAndGet = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>fiberRefModify(self, (v)=>{
        const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(pf(v), ()=>v);
        return [
            result,
            result
        ];
    }));
// circular
/** @internal */ const RequestResolverSymbolKey = "effect/RequestResolver";
const RequestResolverTypeId = /*#__PURE__*/ Symbol.for(RequestResolverSymbolKey);
const requestResolverVariance = {
    /* c8 ignore next */ _A: (_)=>_,
    /* c8 ignore next */ _R: (_)=>_
};
class RequestResolverImpl {
    runAll;
    target;
    [RequestResolverTypeId] = requestResolverVariance;
    constructor(runAll, target){
        this.runAll = runAll;
        this.target = target;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]]() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cached"])(this, this.target ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(this.target) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["random"])(this));
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]](that) {
        return this.target ? isRequestResolver(that) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"])(this.target, that.target) : this === that;
    }
    identified(...ids) {
        return new RequestResolverImpl(this.runAll, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(ids));
    }
    pipe() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
}
const isRequestResolver = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, RequestResolverTypeId);
const resolverLocally = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (use, self, value)=>new RequestResolverImpl((requests)=>fiberRefLocally(use.runAll(requests), self, value), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])("Locally", use, self, value)));
const requestBlockLocally = (self, ref, value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$blockedRequests$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["reduce"])(self, LocallyReducer(ref, value));
const LocallyReducer = (ref, value)=>({
        emptyCase: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$blockedRequests$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"],
        parCase: (left, right)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$blockedRequests$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["par"])(left, right),
        seqCase: (left, right)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$blockedRequests$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["seq"])(left, right),
        singleCase: (dataSource, blockedRequest)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$blockedRequests$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["single"])(resolverLocally(dataSource, ref, value), blockedRequest)
    });
const fiberRefLocally = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (use, self, value)=>acquireUseRelease(zipLeft(fiberRefGet(self), fiberRefSet(self, value)), ()=>use, (oldValue)=>fiberRefSet(self, oldValue)));
const fiberRefLocallyWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (use, self, f)=>fiberRefGetWith(self, (a)=>fiberRefLocally(use, self, f(a))));
const fiberRefUnsafeMake = (initial, options)=>fiberRefUnsafeMakePatch(initial, {
        differ: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$differ$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["update"])(),
        fork: options?.fork ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"],
        join: options?.join
    });
const fiberRefUnsafeMakeHashSet = (initial)=>{
    const differ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$differ$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hashSet"])();
    return fiberRefUnsafeMakePatch(initial, {
        differ,
        fork: differ.empty
    });
};
const fiberRefUnsafeMakeReadonlyArray = (initial)=>{
    const differ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$differ$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readonlyArray"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$differ$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["update"])());
    return fiberRefUnsafeMakePatch(initial, {
        differ,
        fork: differ.empty
    });
};
const fiberRefUnsafeMakeContext = (initial)=>{
    const differ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$differ$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["environment"])();
    return fiberRefUnsafeMakePatch(initial, {
        differ,
        fork: differ.empty
    });
};
const fiberRefUnsafeMakePatch = (initial, options)=>{
    const _fiberRef = {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CommitPrototype"],
        [FiberRefTypeId]: fiberRefVariance,
        initial,
        commit () {
            return fiberRefGet(this);
        },
        diff: (oldValue, newValue)=>options.differ.diff(oldValue, newValue),
        combine: (first, second)=>options.differ.combine(first, second),
        patch: (patch)=>(oldValue)=>options.differ.patch(patch, oldValue),
        fork: options.fork,
        join: options.join ?? ((_, n)=>n)
    };
    return _fiberRef;
};
const fiberRefUnsafeMakeRuntimeFlags = (initial)=>fiberRefUnsafeMakePatch(initial, {
        differ: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["differ"],
        fork: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["differ"].empty
    });
const currentContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentContext"), ()=>fiberRefUnsafeMakeContext((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()));
const currentSchedulingPriority = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentSchedulingPriority"), ()=>fiberRefUnsafeMake(0));
const currentMaxOpsBeforeYield = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentMaxOpsBeforeYield"), ()=>fiberRefUnsafeMake(2048));
const currentLogAnnotations = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentLogAnnotation"), ()=>fiberRefUnsafeMake((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()));
const currentLogLevel = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentLogLevel"), ()=>fiberRefUnsafeMake(logLevelInfo));
const currentLogSpan = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentLogSpan"), ()=>fiberRefUnsafeMake((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$List$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()));
const withSchedulingPriority = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, scheduler)=>fiberRefLocally(self, currentSchedulingPriority, scheduler));
const withMaxOpsBeforeYield = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, scheduler)=>fiberRefLocally(self, currentMaxOpsBeforeYield, scheduler));
const currentConcurrency = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentConcurrency"), ()=>fiberRefUnsafeMake("unbounded"));
const currentRequestBatching = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentRequestBatching"), ()=>fiberRefUnsafeMake(true));
const currentUnhandledErrorLogLevel = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentUnhandledErrorLogLevel"), ()=>fiberRefUnsafeMake((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(logLevelDebug)));
const withUnhandledErrorLogLevel = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, level)=>fiberRefLocally(self, currentUnhandledErrorLogLevel, level));
const currentMetricLabels = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentMetricLabels"), ()=>fiberRefUnsafeMakeReadonlyArray((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()));
const metricLabels = /*#__PURE__*/ fiberRefGet(currentMetricLabels);
const currentForkScopeOverride = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentForkScopeOverride"), ()=>fiberRefUnsafeMake((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])(), {
        fork: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])(),
        join: (parent, _)=>parent
    }));
const currentInterruptedCause = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentInterruptedCause"), ()=>fiberRefUnsafeMake(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"], {
        fork: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"],
        join: (parent, _)=>parent
    }));
const currentTracerEnabled = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentTracerEnabled"), ()=>fiberRefUnsafeMake(true));
const currentTracerTimingEnabled = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentTracerTiming"), ()=>fiberRefUnsafeMake(true));
const currentTracerSpanAnnotations = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentTracerSpanAnnotations"), ()=>fiberRefUnsafeMake((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()));
const currentTracerSpanLinks = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentTracerSpanLinks"), ()=>fiberRefUnsafeMake((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()));
const ScopeTypeId = /*#__PURE__*/ Symbol.for("effect/Scope");
const CloseableScopeTypeId = /*#__PURE__*/ Symbol.for("effect/CloseableScope");
const scopeAddFinalizer = (self, finalizer)=>self.addFinalizer(()=>asVoid(finalizer));
const scopeAddFinalizerExit = (self, finalizer)=>self.addFinalizer(finalizer);
const scopeClose = (self, exit)=>self.close(exit);
const scopeFork = (self, strategy)=>self.fork(strategy);
const causeSquash = (self)=>{
    return causeSquashWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"])(self);
};
const causeSquashWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const option = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failureOption"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(f));
    switch(option._tag){
        case "None":
            {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["defects"])(self), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["head"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                    onNone: ()=>{
                        const interrupts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptors"])(self)).flatMap((fiberId)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ids"])(fiberId)).map((id)=>`#${id}`));
                        return new InterruptedException(interrupts ? `Interrupted by fibers: ${interrupts.join(", ")}` : void 0);
                    },
                    onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]
                }));
            }
        case "Some":
            {
                return option.value;
            }
    }
});
const YieldableError = /*#__PURE__*/ function() {
    class YieldableError extends globalThis.Error {
        commit() {
            return fail(this);
        }
        toJSON() {
            const obj = {
                ...this
            };
            if (this.message) obj.message = this.message;
            if (this.cause) obj.cause = this.cause;
            return obj;
        }
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NodeInspectSymbol"]]() {
            if (this.toString !== globalThis.Error.prototype.toString) {
                return this.stack ? `${this.toString()}\n${this.stack.split("\n").slice(1).join("\n")}` : this.toString();
            } else if ("Bun" in globalThis) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pretty"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(this), {
                    renderErrorCause: true
                });
            }
            return this;
        }
    }
    Object.assign(YieldableError.prototype, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["StructuralCommitPrototype"]);
    return YieldableError;
}();
const makeException = (proto, tag)=>{
    class Base extends YieldableError {
        _tag = tag;
    }
    Object.assign(Base.prototype, proto);
    Base.prototype.name = tag;
    return Base;
};
const RuntimeExceptionTypeId = /*#__PURE__*/ Symbol.for("effect/Cause/errors/RuntimeException");
const RuntimeException = /*#__PURE__*/ makeException({
    [RuntimeExceptionTypeId]: RuntimeExceptionTypeId
}, "RuntimeException");
const isRuntimeException = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, RuntimeExceptionTypeId);
const InterruptedExceptionTypeId = /*#__PURE__*/ Symbol.for("effect/Cause/errors/InterruptedException");
const InterruptedException = /*#__PURE__*/ makeException({
    [InterruptedExceptionTypeId]: InterruptedExceptionTypeId
}, "InterruptedException");
const isInterruptedException = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, InterruptedExceptionTypeId);
const IllegalArgumentExceptionTypeId = /*#__PURE__*/ Symbol.for("effect/Cause/errors/IllegalArgument");
const IllegalArgumentException = /*#__PURE__*/ makeException({
    [IllegalArgumentExceptionTypeId]: IllegalArgumentExceptionTypeId
}, "IllegalArgumentException");
const isIllegalArgumentException = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, IllegalArgumentExceptionTypeId);
const NoSuchElementExceptionTypeId = /*#__PURE__*/ Symbol.for("effect/Cause/errors/NoSuchElement");
const NoSuchElementException = /*#__PURE__*/ makeException({
    [NoSuchElementExceptionTypeId]: NoSuchElementExceptionTypeId
}, "NoSuchElementException");
const isNoSuchElementException = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, NoSuchElementExceptionTypeId);
const InvalidPubSubCapacityExceptionTypeId = /*#__PURE__*/ Symbol.for("effect/Cause/errors/InvalidPubSubCapacityException");
const InvalidPubSubCapacityException = /*#__PURE__*/ makeException({
    [InvalidPubSubCapacityExceptionTypeId]: InvalidPubSubCapacityExceptionTypeId
}, "InvalidPubSubCapacityException");
const ExceededCapacityExceptionTypeId = /*#__PURE__*/ Symbol.for("effect/Cause/errors/ExceededCapacityException");
const ExceededCapacityException = /*#__PURE__*/ makeException({
    [ExceededCapacityExceptionTypeId]: ExceededCapacityExceptionTypeId
}, "ExceededCapacityException");
const isExceededCapacityException = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, ExceededCapacityExceptionTypeId);
const isInvalidCapacityError = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, InvalidPubSubCapacityExceptionTypeId);
const TimeoutExceptionTypeId = /*#__PURE__*/ Symbol.for("effect/Cause/errors/Timeout");
const TimeoutException = /*#__PURE__*/ makeException({
    [TimeoutExceptionTypeId]: TimeoutExceptionTypeId
}, "TimeoutException");
const timeoutExceptionFromDuration = (duration)=>new TimeoutException(`Operation timed out after '${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Duration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["format"])(duration)}'`);
const isTimeoutException = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, TimeoutExceptionTypeId);
const UnknownExceptionTypeId = /*#__PURE__*/ Symbol.for("effect/Cause/errors/UnknownException");
const UnknownException = /*#__PURE__*/ function() {
    class UnknownException extends YieldableError {
        _tag = "UnknownException";
        error;
        constructor(cause, message){
            super(message ?? "An unknown error occurred", {
                cause
            });
            this.error = cause;
        }
    }
    Object.assign(UnknownException.prototype, {
        [UnknownExceptionTypeId]: UnknownExceptionTypeId,
        name: "UnknownException"
    });
    return UnknownException;
}();
const isUnknownException = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, UnknownExceptionTypeId);
const exitIsExit = (u)=>isEffect(u) && "_tag" in u && (u._tag === "Success" || u._tag === "Failure");
const exitIsFailure = (self)=>self._tag === "Failure";
const exitIsSuccess = (self)=>self._tag === "Success";
const exitIsInterrupted = (self)=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isInterrupted"])(self.effect_instruction_i0);
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            return false;
    }
};
const exitAs = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            {
                return exitFailCause(self.effect_instruction_i0);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            {
                return exitSucceed(value);
            }
    }
});
const exitAsVoid = (self)=>exitAs(self, void 0);
const exitCauseOption = (self)=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(self.effect_instruction_i0);
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
    }
};
const exitCollectAll = (exits, options)=>exitCollectAllInternal(exits, options?.parallel ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"]);
const exitDie = (defect)=>exitFailCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(defect));
const exitExists = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, refinement)=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            return false;
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            return refinement(self.effect_instruction_i0);
    }
});
const exitFail = (error)=>exitFailCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(error));
const exitFailCause = (cause)=>{
    const effect = new EffectPrimitiveFailure(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]);
    effect.effect_instruction_i0 = cause;
    return effect;
};
const exitFlatMap = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            {
                return exitFailCause(self.effect_instruction_i0);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            {
                return f(self.effect_instruction_i0);
            }
    }
});
const exitFlatMapEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            {
                return succeed(exitFailCause(self.effect_instruction_i0));
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            {
                return f(self.effect_instruction_i0);
            }
    }
});
const exitFlatten = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, exitFlatMap(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]));
const exitForEachEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            {
                return succeed(exitFailCause(self.effect_instruction_i0));
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            {
                return exit(f(self.effect_instruction_i0));
            }
    }
});
const exitFromEither = (either)=>{
    switch(either._tag){
        case "Left":
            return exitFail(either.left);
        case "Right":
            return exitSucceed(either.right);
    }
};
const exitFromOption = (option)=>{
    switch(option._tag){
        case "None":
            return exitFail(void 0);
        case "Some":
            return exitSucceed(option.value);
    }
};
const exitGetOrElse = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, orElse)=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            return orElse(self.effect_instruction_i0);
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            return self.effect_instruction_i0;
    }
});
const exitInterrupt = (fiberId)=>exitFailCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(fiberId));
const exitMap = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            return exitFailCause(self.effect_instruction_i0);
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            return exitSucceed(f(self.effect_instruction_i0));
    }
});
const exitMapBoth = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onFailure, onSuccess })=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            return exitFailCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self.effect_instruction_i0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(onFailure)));
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            return exitSucceed(onSuccess(self.effect_instruction_i0));
    }
});
const exitMapError = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            return exitFailCause((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self.effect_instruction_i0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(f)));
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            return exitSucceed(self.effect_instruction_i0);
    }
});
const exitMapErrorCause = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            return exitFailCause(f(self.effect_instruction_i0));
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            return exitSucceed(self.effect_instruction_i0);
    }
});
const exitMatch = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onFailure, onSuccess })=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            return onFailure(self.effect_instruction_i0);
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            return onSuccess(self.effect_instruction_i0);
    }
});
const exitMatchEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onFailure, onSuccess })=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            return onFailure(self.effect_instruction_i0);
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            return onSuccess(self.effect_instruction_i0);
    }
});
const exitSucceed = (value)=>{
    const effect = new EffectPrimitiveSuccess(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]);
    effect.effect_instruction_i0 = value;
    return effect;
};
const exitVoid = /*#__PURE__*/ exitSucceed(void 0);
const exitZip = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>exitZipWith(self, that, {
        onSuccess: (a, a2)=>[
                a,
                a2
            ],
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"]
    }));
const exitZipLeft = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>exitZipWith(self, that, {
        onSuccess: (a, _)=>a,
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"]
    }));
const exitZipRight = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>exitZipWith(self, that, {
        onSuccess: (_, a2)=>a2,
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"]
    }));
const exitZipPar = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>exitZipWith(self, that, {
        onSuccess: (a, a2)=>[
                a,
                a2
            ],
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"]
    }));
const exitZipParLeft = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>exitZipWith(self, that, {
        onSuccess: (a, _)=>a,
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"]
    }));
const exitZipParRight = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>exitZipWith(self, that, {
        onSuccess: (_, a2)=>a2,
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"]
    }));
const exitZipWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, that, { onFailure, onSuccess })=>{
    switch(self._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
            {
                switch(that._tag){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
                        return exitFailCause(self.effect_instruction_i0);
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
                        {
                            return exitFailCause(onFailure(self.effect_instruction_i0, that.effect_instruction_i0));
                        }
                }
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
            {
                switch(that._tag){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
                        return exitSucceed(onSuccess(self.effect_instruction_i0, that.effect_instruction_i0));
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
                        return exitFailCause(that.effect_instruction_i0);
                }
            }
    }
});
const exitCollectAllInternal = (exits, combineCauses)=>{
    const list = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(exits);
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmpty"])(list)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tailNonEmpty"])(list), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["reduce"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["headNonEmpty"])(list), exitMap(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["of"])), (accumulator, current)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(accumulator, exitZipWith(current, {
            onSuccess: (list, value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(list, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["prepend"])(value)),
            onFailure: combineCauses
        }))), exitMap(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["reverse"]), exitMap((chunk)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toReadonlyArray"])(chunk)), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"]);
};
const deferredUnsafeMake = (fiberId)=>{
    const _deferred = {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CommitPrototype"],
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DeferredTypeId"]]: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deferredVariance"],
        state: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pending"])([])),
        commit () {
            return deferredAwait(this);
        },
        blockingOn: fiberId
    };
    return _deferred;
};
const deferredMake = ()=>flatMap(fiberId, (id)=>deferredMakeAs(id));
const deferredMakeAs = (fiberId)=>sync(()=>deferredUnsafeMake(fiberId));
const deferredAwait = (self)=>asyncInterrupt((resume)=>{
        const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(self.state);
        switch(state._tag){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_STATE_DONE"]:
                {
                    return resume(state.effect);
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_STATE_PENDING"]:
                {
                    // we can push here as the internal state is mutable
                    state.joiners.push(resume);
                    return deferredInterruptJoiner(self, resume);
                }
        }
    }, self.blockingOn);
const deferredComplete = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, effect)=>intoDeferred(effect, self));
const deferredCompleteWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, effect)=>sync(()=>{
        const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(self.state);
        switch(state._tag){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_STATE_DONE"]:
                {
                    return false;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_STATE_PENDING"]:
                {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(self.state, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["done"])(effect));
                    for(let i = 0, len = state.joiners.length; i < len; i++){
                        state.joiners[i](effect);
                    }
                    return true;
                }
        }
    }));
const deferredDone = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, exit)=>deferredCompleteWith(self, exit));
const deferredFail = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, error)=>deferredCompleteWith(self, fail(error)));
const deferredFailSync = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, evaluate)=>deferredCompleteWith(self, failSync(evaluate)));
const deferredFailCause = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, cause)=>deferredCompleteWith(self, failCause(cause)));
const deferredFailCauseSync = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, evaluate)=>deferredCompleteWith(self, failCauseSync(evaluate)));
const deferredDie = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, defect)=>deferredCompleteWith(self, die(defect)));
const deferredDieSync = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, evaluate)=>deferredCompleteWith(self, dieSync(evaluate)));
const deferredInterrupt = (self)=>flatMap(fiberId, (fiberId)=>deferredCompleteWith(self, interruptWith(fiberId)));
const deferredInterruptWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, fiberId)=>deferredCompleteWith(self, interruptWith(fiberId)));
const deferredIsDone = (self)=>sync(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(self.state)._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_STATE_DONE"]);
const deferredPoll = (self)=>sync(()=>{
        const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(self.state);
        switch(state._tag){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_STATE_DONE"]:
                {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(state.effect);
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_STATE_PENDING"]:
                {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
                }
        }
    });
const deferredSucceed = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>deferredCompleteWith(self, succeed(value)));
const deferredSync = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, evaluate)=>deferredCompleteWith(self, sync(evaluate)));
const deferredUnsafeDone = (self, effect)=>{
    const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(self.state);
    if (state._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_STATE_PENDING"]) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(self.state, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["done"])(effect));
        for(let i = 0, len = state.joiners.length; i < len; i++){
            state.joiners[i](effect);
        }
    }
};
const deferredInterruptJoiner = (self, joiner)=>sync(()=>{
        const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(self.state);
        if (state._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_STATE_PENDING"]) {
            const index = state.joiners.indexOf(joiner);
            if (index >= 0) {
                // we can splice here as the internal state is mutable
                state.joiners.splice(index, 1);
            }
        }
    });
// -----------------------------------------------------------------------------
// Context
// -----------------------------------------------------------------------------
const constContext = /*#__PURE__*/ withFiberRuntime((fiber)=>exitSucceed(fiber.currentContext));
const context = ()=>constContext;
const contextWith = (f)=>map(context(), f);
const contextWithEffect = (f)=>flatMap(context(), f);
const provideContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, context)=>fiberRefLocally(currentContext, context)(self));
const provideSomeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, context)=>fiberRefLocallyWith(currentContext, (parent)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["merge"])(parent, context))(self));
const mapInputContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>contextWithEffect((context)=>provideContext(self, f(context))));
const filterEffectOrElse = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>flatMap(self, (a)=>flatMap(options.predicate(a), (pass)=>pass ? succeed(a) : options.orElse(a))));
const filterEffectOrFail = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>filterEffectOrElse(self, {
        predicate: options.predicate,
        orElse: (a)=>fail(options.orFailWith(a))
    }));
const currentSpanFromFiber = (fiber)=>{
    const span = fiber.currentSpan;
    return span !== undefined && span._tag === "Span" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(span) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
};
const NoopSpanProto = {
    _tag: "Span",
    spanId: "noop",
    traceId: "noop",
    sampled: false,
    status: {
        _tag: "Ended",
        startTime: /*#__PURE__*/ BigInt(0),
        endTime: /*#__PURE__*/ BigInt(0),
        exit: exitVoid
    },
    attributes: /*#__PURE__*/ new Map(),
    links: [],
    kind: "internal",
    attribute () {},
    event () {},
    end () {},
    addLinks () {}
};
const noopSpan = (options)=>Object.assign(Object.create(NoopSpanProto), options); //# sourceMappingURL=core.js.map
}}),

};

//# sourceMappingURL=node_modules_effect_dist_esm_internal_core_8908ed59.js.map