module.exports = {

"[project]/node_modules/node-fetch-native/dist/chunks/multipart-parser.mjs [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_node-fetch-native_dist_chunks_multipart-parser_mjs_914ac0f5._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/node-fetch-native/dist/chunks/multipart-parser.mjs [app-rsc] (ecmascript)");
    });
});
}}),

};