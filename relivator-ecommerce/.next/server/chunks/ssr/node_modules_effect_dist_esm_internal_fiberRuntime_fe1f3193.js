module.exports = {

"[project]/node_modules/effect/dist/esm/internal/fiberRuntime.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FiberRuntime": (()=>FiberRuntime),
    "acquireRelease": (()=>acquireRelease),
    "acquireReleaseInterruptible": (()=>acquireReleaseInterruptible),
    "addFinalizer": (()=>addFinalizer),
    "all": (()=>all),
    "allSuccesses": (()=>allSuccesses),
    "allWith": (()=>allWith),
    "annotateLogsScoped": (()=>annotateLogsScoped),
    "batchedLogger": (()=>batchedLogger),
    "currentLoggers": (()=>currentLoggers),
    "currentMinimumLogLevel": (()=>currentMinimumLogLevel),
    "currentRuntimeFlags": (()=>currentRuntimeFlags),
    "currentSupervisor": (()=>currentSupervisor),
    "daemonChildren": (()=>daemonChildren),
    "defaultLogger": (()=>defaultLogger),
    "disconnect": (()=>disconnect),
    "ensuring": (()=>ensuring),
    "exists": (()=>exists),
    "fiberActive": (()=>fiberActive),
    "fiberAll": (()=>fiberAll),
    "fiberAwaitAll": (()=>fiberAwaitAll),
    "fiberFailures": (()=>fiberFailures),
    "fiberInterruptFork": (()=>fiberInterruptFork),
    "fiberJoinAll": (()=>fiberJoinAll),
    "fiberLifetimes": (()=>fiberLifetimes),
    "fiberRefLocallyScoped": (()=>fiberRefLocallyScoped),
    "fiberRefLocallyScopedWith": (()=>fiberRefLocallyScopedWith),
    "fiberRefMake": (()=>fiberRefMake),
    "fiberRefMakeContext": (()=>fiberRefMakeContext),
    "fiberRefMakeRuntimeFlags": (()=>fiberRefMakeRuntimeFlags),
    "fiberRefMakeWith": (()=>fiberRefMakeWith),
    "fiberRefUnsafeMakeSupervisor": (()=>fiberRefUnsafeMakeSupervisor),
    "fiberScoped": (()=>fiberScoped),
    "fiberStarted": (()=>fiberStarted),
    "fiberSuccesses": (()=>fiberSuccesses),
    "filter": (()=>filter),
    "finalizersMask": (()=>finalizersMask),
    "finalizersMaskInternal": (()=>finalizersMaskInternal),
    "forEach": (()=>forEach),
    "forEachConcurrentDiscard": (()=>forEachConcurrentDiscard),
    "forEachParN": (()=>forEachParN),
    "forEachParUnbounded": (()=>forEachParUnbounded),
    "fork": (()=>fork),
    "forkDaemon": (()=>forkDaemon),
    "forkWithErrorHandler": (()=>forkWithErrorHandler),
    "interruptWhenPossible": (()=>interruptWhenPossible),
    "invokeWithInterrupt": (()=>invokeWithInterrupt),
    "jsonLogger": (()=>jsonLogger),
    "labelMetricsScoped": (()=>labelMetricsScoped),
    "logFmtLogger": (()=>logFmtLogger),
    "loggerWithConsoleError": (()=>loggerWithConsoleError),
    "loggerWithConsoleLog": (()=>loggerWithConsoleLog),
    "loggerWithLeveledLog": (()=>loggerWithLeveledLog),
    "loggerWithSpanAnnotations": (()=>loggerWithSpanAnnotations),
    "makeSpanScoped": (()=>makeSpanScoped),
    "mergeAll": (()=>mergeAll),
    "parallelFinalizers": (()=>parallelFinalizers),
    "parallelNFinalizers": (()=>parallelNFinalizers),
    "partition": (()=>partition),
    "prettyLogger": (()=>prettyLogger),
    "race": (()=>race),
    "raceAll": (()=>raceAll),
    "raceFibersWith": (()=>raceFibersWith),
    "raceWith": (()=>raceWith),
    "reduceEffect": (()=>reduceEffect),
    "replicate": (()=>replicate),
    "replicateEffect": (()=>replicateEffect),
    "scope": (()=>scope),
    "scopeExtend": (()=>scopeExtend),
    "scopeMake": (()=>scopeMake),
    "scopeTag": (()=>scopeTag),
    "scopeUse": (()=>scopeUse),
    "scopeWith": (()=>scopeWith),
    "scopedEffect": (()=>scopedEffect),
    "scopedWith": (()=>scopedWith),
    "sequentialFinalizers": (()=>sequentialFinalizers),
    "structuredLogger": (()=>structuredLogger),
    "tagMetricsScoped": (()=>tagMetricsScoped),
    "tracerLogger": (()=>tracerLogger),
    "unsafeFork": (()=>unsafeFork),
    "unsafeForkUnstarted": (()=>unsafeForkUnstarted),
    "unsafeMakeChildFiber": (()=>unsafeMakeChildFiber),
    "using": (()=>using),
    "validate": (()=>validate),
    "validateAll": (()=>validateAll),
    "validateAllPar": (()=>validateAllPar),
    "validateAllParDiscard": (()=>validateAllParDiscard),
    "validateFirst": (()=>validateFirst),
    "validateWith": (()=>validateWith),
    "whenLogLevel": (()=>whenLogLevel),
    "withClockScoped": (()=>withClockScoped),
    "withConfigProviderScoped": (()=>withConfigProviderScoped),
    "withEarlyRelease": (()=>withEarlyRelease),
    "withRandomScoped": (()=>withRandomScoped),
    "withRuntimeFlagsScoped": (()=>withRuntimeFlagsScoped),
    "withSpanScoped": (()=>withSpanScoped),
    "withTracerScoped": (()=>withTracerScoped),
    "zipLeftOptions": (()=>zipLeftOptions),
    "zipOptions": (()=>zipOptions),
    "zipRightOptions": (()=>zipRightOptions),
    "zipWithOptions": (()=>zipWithOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Array.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Boolean$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Boolean.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Chunk.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Context.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Deferred.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Effectable.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/ExecutionStrategy.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/FiberId.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/FiberRefs.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRefsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/FiberRefsPatch.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberStatus$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/FiberStatus.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/GlobalValue.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/HashMap.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/HashSet.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Inspectable.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$LogLevel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/LogLevel.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Micro$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Micro.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/MutableRef.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Option.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Pipeable.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Ref.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$RuntimeFlagsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/RuntimeFlagsPatch.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scheduler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Scheduler.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$blockedRequests$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/blockedRequests.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/cause.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$clock$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/clock.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$completedRequestMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/completedRequestMap.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$concurrency$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/concurrency.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$configProvider$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/configProvider.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/core-effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/core.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/defaultServices.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2f$console$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/defaultServices/console.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$executionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/executionStrategy.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/fiber.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/fiberMessage.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/fiberRefs.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberScope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/fiberScope.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/logger.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$metric$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/metric.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$metric$2f$boundaries$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/metric/boundaries.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$metric$2f$label$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/metric/label.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$random$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/random.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$request$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/request.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/runtimeFlags.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$supervisor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/supervisor.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$supervisor$2f$patch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/supervisor/patch.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/tracer.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/version.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const fiberStarted = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$metric$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["counter"])("effect_fiber_started", {
    incremental: true
});
const fiberActive = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$metric$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["counter"])("effect_fiber_active");
const fiberSuccesses = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$metric$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["counter"])("effect_fiber_successes", {
    incremental: true
});
const fiberFailures = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$metric$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["counter"])("effect_fiber_failures", {
    incremental: true
});
const fiberLifetimes = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$metric$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tagged"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$metric$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["histogram"])("effect_fiber_lifetimes", /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$metric$2f$boundaries$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exponential"])({
    start: 0.5,
    factor: 2,
    count: 35
})), "time_unit", "milliseconds");
/** @internal */ const EvaluationSignalContinue = "Continue";
/** @internal */ const EvaluationSignalDone = "Done";
/** @internal */ const EvaluationSignalYieldNow = "Yield";
const runtimeFiberVariance = {
    /* c8 ignore next */ _E: (_)=>_,
    /* c8 ignore next */ _A: (_)=>_
};
const absurd = (_)=>{
    throw new Error(`BUG: FiberRuntime - ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toStringUnknown"])(_)} - please report an issue at https://github.com/Effect-TS/effect/issues`);
};
const YieldedOp = /*#__PURE__*/ Symbol.for("effect/internal/fiberRuntime/YieldedOp");
const yieldedOpChannel = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])("effect/internal/fiberRuntime/yieldedOpChannel", ()=>({
        currentOp: null
    }));
const contOpSuccess = {
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_SUCCESS"]]: (_, cont, value)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>cont.effect_instruction_i1(value));
    },
    ["OnStep"]: (_, _cont, value)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])(value));
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_SUCCESS_AND_FAILURE"]]: (_, cont, value)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>cont.effect_instruction_i2(value));
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_REVERT_FLAGS"]]: (self, cont, value)=>{
        self.patchRuntimeFlags(self.currentRuntimeFlags, cont.patch);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(self.currentRuntimeFlags) && self.isInterrupted()) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])(self.getInterruptedCause());
        } else {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])(value);
        }
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_WHILE"]]: (self, cont, value)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>cont.effect_instruction_i2(value));
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>cont.effect_instruction_i0())) {
            self.pushStack(cont);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>cont.effect_instruction_i1());
        } else {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
        }
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ITERATOR"]]: (self, cont, value)=>{
        const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>cont.effect_instruction_i0.next(value));
        if (state.done) return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])(state.value);
        self.pushStack(cont);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["yieldWrapGet"])(state.value);
    }
};
const drainQueueWhileRunningTable = {
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT_SIGNAL"]]: (self, runtimeFlags, cur, message)=>{
        self.processNewInterruptSignal(message.cause);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(runtimeFlags) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])(message.cause) : cur;
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RESUME"]]: (_self, _runtimeFlags, _cur, _message)=>{
        throw new Error("It is illegal to have multiple concurrent run loops in a single fiber");
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_STATEFUL"]]: (self, runtimeFlags, cur, message)=>{
        message.onFiber(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberStatus$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["running"])(runtimeFlags));
        return cur;
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_YIELD_NOW"]]: (_self, _runtimeFlags, cur, _message)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["yieldNow"])(), ()=>cur);
    }
};
/**
 * Executes all requests, submitting requests to each data source in parallel.
 */ const runBlockedRequests = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEachSequentialDiscard"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$blockedRequests$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(self), (requestsByRequestResolver)=>forEachConcurrentDiscard((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$blockedRequests$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequentialCollectionToChunk"])(requestsByRequestResolver), ([dataSource, sequential])=>{
            const map = new Map();
            const arr = [];
            for (const block of sequential){
                arr.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toReadonlyArray"])(block));
                for (const entry of block){
                    map.set(entry.request, entry);
                }
            }
            const flat = arr.flat();
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefLocally"])(invokeWithInterrupt(dataSource.runAll(arr), flat, ()=>flat.forEach((entry)=>{
                    entry.listeners.interrupted = true;
                })), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$completedRequestMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentRequestMap"], map);
        }, false, false));
const _version = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCurrentVersion"])();
class FiberRuntime extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Class"] {
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FiberTypeId"]] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberVariance"];
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuntimeFiberTypeId"]] = runtimeFiberVariance;
    _fiberRefs;
    _fiberId;
    _queue = /*#__PURE__*/ new Array();
    _children = null;
    _observers = /*#__PURE__*/ new Array();
    _running = false;
    _stack = [];
    _asyncInterruptor = null;
    _asyncBlockingOn = null;
    _exitValue = null;
    _steps = [];
    _isYielding = false;
    currentRuntimeFlags;
    currentOpCount = 0;
    currentSupervisor;
    currentScheduler;
    currentTracer;
    currentSpan;
    currentContext;
    currentDefaultServices;
    constructor(fiberId, fiberRefs0, runtimeFlags0){
        super();
        this.currentRuntimeFlags = runtimeFlags0;
        this._fiberId = fiberId;
        this._fiberRefs = fiberRefs0;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runtimeMetrics"])(runtimeFlags0)) {
            const tags = this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentMetricLabels"]);
            fiberStarted.unsafeUpdate(1, tags);
            fiberActive.unsafeUpdate(1, tags);
        }
        this.refreshRefCache();
    }
    commit() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["join"])(this);
    }
    /**
   * The identity of the fiber.
   */ id() {
        return this._fiberId;
    }
    /**
   * Begins execution of the effect associated with this fiber on in the
   * background. This can be called to "kick off" execution of a fiber after
   * it has been created.
   */ resume(effect) {
        this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["resume"])(effect));
    }
    /**
   * The status of the fiber.
   */ get status() {
        return this.ask((_, status)=>status);
    }
    /**
   * Gets the fiber runtime flags.
   */ get runtimeFlags() {
        return this.ask((state, status)=>{
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberStatus$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isDone"])(status)) {
                return state.currentRuntimeFlags;
            }
            return status.runtimeFlags;
        });
    }
    /**
   * Returns the current `FiberScope` for the fiber.
   */ scope() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberScope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeMake"])(this);
    }
    /**
   * Retrieves the immediate children of the fiber.
   */ get children() {
        return this.ask((fiber)=>Array.from(fiber.getChildren()));
    }
    /**
   * Gets the fiber's set of children.
   */ getChildren() {
        if (this._children === null) {
            this._children = new Set();
        }
        return this._children;
    }
    /**
   * Retrieves the interrupted cause of the fiber, which will be `Cause.empty`
   * if the fiber has not been interrupted.
   *
   * **NOTE**: This method is safe to invoke on any fiber, but if not invoked
   * on this fiber, then values derived from the fiber's state (including the
   * log annotations and log level) may not be up-to-date.
   */ getInterruptedCause() {
        return this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentInterruptedCause"]);
    }
    /**
   * Retrieves the whole set of fiber refs.
   */ fiberRefs() {
        return this.ask((fiber)=>fiber.getFiberRefs());
    }
    /**
   * Returns an effect that will contain information computed from the fiber
   * state and status while running on the fiber.
   *
   * This allows the outside world to interact safely with mutable fiber state
   * without locks or immutable data.
   */ ask(f) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
            const deferred = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deferredUnsafeMake"])(this._fiberId);
            this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["stateful"])((fiber, status)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deferredUnsafeDone"])(deferred, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>f(fiber, status)));
            }));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deferredAwait"])(deferred);
        });
    }
    /**
   * Adds a message to be processed by the fiber on the fiber.
   */ tell(message) {
        this._queue.push(message);
        if (!this._running) {
            this._running = true;
            this.drainQueueLaterOnExecutor();
        }
    }
    get await() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["async"])((resume)=>{
            const cb = (exit)=>resume((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(exit));
            this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["stateful"])((fiber, _)=>{
                if (fiber._exitValue !== null) {
                    cb(this._exitValue);
                } else {
                    fiber.addObserver(cb);
                }
            }));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["stateful"])((fiber, _)=>{
                    fiber.removeObserver(cb);
                })));
        }, this.id());
    }
    get inheritAll() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((parentFiber, parentStatus)=>{
            const parentFiberId = parentFiber.id();
            const parentFiberRefs = parentFiber.getFiberRefs();
            const parentRuntimeFlags = parentStatus.runtimeFlags;
            const childFiberRefs = this.getFiberRefs();
            const updatedFiberRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["joinAs"])(parentFiberRefs, parentFiberId, childFiberRefs);
            parentFiber.setFiberRefs(updatedFiberRefs);
            const updatedRuntimeFlags = parentFiber.getFiberRef(currentRuntimeFlags);
            const patch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["diff"])(parentRuntimeFlags, updatedRuntimeFlags), // Do not inherit WindDown or Interruption!
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$RuntimeFlagsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exclude"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Interruption"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$RuntimeFlagsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exclude"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WindDown"]));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateRuntimeFlags"])(patch);
        });
    }
    /**
   * Tentatively observes the fiber, but returns immediately if it is not
   * already done.
   */ get poll() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromNullable"])(this._exitValue));
    }
    /**
   * Unsafely observes the fiber, but returns immediately if it is not
   * already done.
   */ unsafePoll() {
        return this._exitValue;
    }
    /**
   * In the background, interrupts the fiber as if interrupted from the specified fiber.
   */ interruptAsFork(fiberId) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptSignal"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(fiberId))));
    }
    /**
   * In the background, interrupts the fiber as if interrupted from the specified fiber.
   */ unsafeInterruptAsFork(fiberId) {
        this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptSignal"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(fiberId)));
    }
    /**
   * Adds an observer to the list of observers.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ addObserver(observer) {
        if (this._exitValue !== null) {
            observer(this._exitValue);
        } else {
            this._observers.push(observer);
        }
    }
    /**
   * Removes the specified observer from the list of observers that will be
   * notified when the fiber exits.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ removeObserver(observer) {
        this._observers = this._observers.filter((o)=>o !== observer);
    }
    /**
   * Retrieves all fiber refs of the fiber.
   *
   * **NOTE**: This method is safe to invoke on any fiber, but if not invoked
   * on this fiber, then values derived from the fiber's state (including the
   * log annotations and log level) may not be up-to-date.
   */ getFiberRefs() {
        this.setFiberRef(currentRuntimeFlags, this.currentRuntimeFlags);
        return this._fiberRefs;
    }
    /**
   * Deletes the specified fiber ref.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ unsafeDeleteFiberRef(fiberRef) {
        this._fiberRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["delete_"])(this._fiberRefs, fiberRef);
    }
    /**
   * Retrieves the state of the fiber ref, or else its initial value.
   *
   * **NOTE**: This method is safe to invoke on any fiber, but if not invoked
   * on this fiber, then values derived from the fiber's state (including the
   * log annotations and log level) may not be up-to-date.
   */ getFiberRef(fiberRef) {
        if (this._fiberRefs.locals.has(fiberRef)) {
            return this._fiberRefs.locals.get(fiberRef)[0][1];
        }
        return fiberRef.initial;
    }
    /**
   * Sets the fiber ref to the specified value.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ setFiberRef(fiberRef, value) {
        this._fiberRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateAs"])(this._fiberRefs, {
            fiberId: this._fiberId,
            fiberRef,
            value
        });
        this.refreshRefCache();
    }
    refreshRefCache() {
        this.currentDefaultServices = this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentServices"]);
        this.currentTracer = this.currentDefaultServices.unsafeMap.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tracerTag"].key);
        this.currentSupervisor = this.getFiberRef(currentSupervisor);
        this.currentScheduler = this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scheduler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentScheduler"]);
        this.currentContext = this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentContext"]);
        this.currentSpan = this.currentContext.unsafeMap.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["spanTag"].key);
    }
    /**
   * Wholesale replaces all fiber refs of this fiber.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ setFiberRefs(fiberRefs) {
        this._fiberRefs = fiberRefs;
        this.refreshRefCache();
    }
    /**
   * Adds a reference to the specified fiber inside the children set.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ addChild(child) {
        this.getChildren().add(child);
    }
    /**
   * Removes a reference to the specified fiber inside the children set.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ removeChild(child) {
        this.getChildren().delete(child);
    }
    /**
   * Transfers all children of this fiber that are currently running to the
   * specified fiber scope.
   *
   * **NOTE**: This method must be invoked by the fiber itself after it has
   * evaluated the effects but prior to exiting.
   */ transferChildren(scope) {
        const children = this._children;
        // Clear the children of the current fiber
        this._children = null;
        if (children !== null && children.size > 0) {
            for (const child of children){
                // If the child is still running, add it to the scope
                if (child._exitValue === null) {
                    scope.add(this.currentRuntimeFlags, child);
                }
            }
        }
    }
    /**
   * On the current thread, executes all messages in the fiber's inbox. This
   * method may return before all work is done, in the event the fiber executes
   * an asynchronous operation.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ drainQueueOnCurrentThread() {
        let recurse = true;
        while(recurse){
            let evaluationSignal = EvaluationSignalContinue;
            const prev = globalThis[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentFiberURI"]];
            globalThis[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentFiberURI"]] = this;
            try {
                while(evaluationSignal === EvaluationSignalContinue){
                    evaluationSignal = this._queue.length === 0 ? EvaluationSignalDone : this.evaluateMessageWhileSuspended(this._queue.splice(0, 1)[0]);
                }
            } finally{
                this._running = false;
                globalThis[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentFiberURI"]] = prev;
            }
            // Maybe someone added something to the queue between us checking, and us
            // giving up the drain. If so, we need to restart the draining, but only
            // if we beat everyone else to the restart:
            if (this._queue.length > 0 && !this._running) {
                this._running = true;
                if (evaluationSignal === EvaluationSignalYieldNow) {
                    this.drainQueueLaterOnExecutor();
                    recurse = false;
                } else {
                    recurse = true;
                }
            } else {
                recurse = false;
            }
        }
    }
    /**
   * Schedules the execution of all messages in the fiber's inbox.
   *
   * This method will return immediately after the scheduling
   * operation is completed, but potentially before such messages have been
   * executed.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ drainQueueLaterOnExecutor() {
        this.currentScheduler.scheduleTask(this.run, this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentSchedulingPriority"]));
    }
    /**
   * Drains the fiber's message queue while the fiber is actively running,
   * returning the next effect to execute, which may be the input effect if no
   * additional effect needs to be executed.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ drainQueueWhileRunning(runtimeFlags, cur0) {
        let cur = cur0;
        while(this._queue.length > 0){
            const message = this._queue.splice(0, 1)[0];
            // @ts-expect-error
            cur = drainQueueWhileRunningTable[message._tag](this, runtimeFlags, cur, message);
        }
        return cur;
    }
    /**
   * Determines if the fiber is interrupted.
   *
   * **NOTE**: This method is safe to invoke on any fiber, but if not invoked
   * on this fiber, then values derived from the fiber's state (including the
   * log annotations and log level) may not be up-to-date.
   */ isInterrupted() {
        return !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmpty"])(this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentInterruptedCause"]));
    }
    /**
   * Adds an interruptor to the set of interruptors that are interrupting this
   * fiber.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ addInterruptedCause(cause) {
        const oldSC = this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentInterruptedCause"]);
        this.setFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentInterruptedCause"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"])(oldSC, cause));
    }
    /**
   * Processes a new incoming interrupt signal.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ processNewInterruptSignal(cause) {
        this.addInterruptedCause(cause);
        this.sendInterruptSignalToAllChildren();
    }
    /**
   * Interrupts all children of the current fiber, returning an effect that will
   * await the exit of the children. This method will return null if the fiber
   * has no children.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ sendInterruptSignalToAllChildren() {
        if (this._children === null || this._children.size === 0) {
            return false;
        }
        let told = false;
        for (const child of this._children){
            child.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptSignal"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(this.id())));
            told = true;
        }
        return told;
    }
    /**
   * Interrupts all children of the current fiber, returning an effect that will
   * await the exit of the children. This method will return null if the fiber
   * has no children.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ interruptAllChildren() {
        if (this.sendInterruptSignalToAllChildren()) {
            const it = this._children.values();
            this._children = null;
            let isDone = false;
            const body = ()=>{
                const next = it.next();
                if (!next.done) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["asVoid"])(next.value.await);
                } else {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>{
                        isDone = true;
                    });
                }
            };
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["whileLoop"])({
                while: ()=>!isDone,
                body,
                step: ()=>{
                //
                }
            });
        }
        return null;
    }
    reportExitValue(exit) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runtimeMetrics"])(this.currentRuntimeFlags)) {
            const tags = this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentMetricLabels"]);
            const startTimeMillis = this.id().startTimeMillis;
            const endTimeMillis = Date.now();
            fiberLifetimes.unsafeUpdate(endTimeMillis - startTimeMillis, tags);
            fiberActive.unsafeUpdate(-1, tags);
            switch(exit._tag){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
                    {
                        fiberSuccesses.unsafeUpdate(1, tags);
                        break;
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
                    {
                        fiberFailures.unsafeUpdate(1, tags);
                        break;
                    }
            }
        }
        if (exit._tag === "Failure") {
            const level = this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentUnhandledErrorLogLevel"]);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isInterruptedOnly"])(exit.cause) && level._tag === "Some") {
                this.log("Fiber terminated with an unhandled error", exit.cause, level);
            }
        }
    }
    setExitValue(exit) {
        this._exitValue = exit;
        this.reportExitValue(exit);
        for(let i = this._observers.length - 1; i >= 0; i--){
            this._observers[i](exit);
        }
        this._observers = [];
    }
    getLoggers() {
        return this.getFiberRef(currentLoggers);
    }
    log(message, cause, overrideLogLevel) {
        const logLevel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSome"])(overrideLogLevel) ? overrideLogLevel.value : this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentLogLevel"]);
        const minimumLogLevel = this.getFiberRef(currentMinimumLogLevel);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$LogLevel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["greaterThan"])(minimumLogLevel, logLevel)) {
            return;
        }
        const spans = this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentLogSpan"]);
        const annotations = this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentLogAnnotations"]);
        const loggers = this.getLoggers();
        const contextMap = this.getFiberRefs();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["size"])(loggers) > 0) {
            const clockService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(this.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentServices"]), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$clock$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clockTag"]);
            const date = new Date(clockService.unsafeCurrentTimeMillis());
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withRedactableContext"])(contextMap, ()=>{
                for (const logger of loggers){
                    logger.log({
                        fiberId: this.id(),
                        logLevel,
                        message,
                        cause,
                        context: contextMap,
                        spans,
                        annotations,
                        date
                    });
                }
            });
        }
    }
    /**
   * Evaluates a single message on the current thread, while the fiber is
   * suspended. This method should only be called while evaluation of the
   * fiber's effect is suspended due to an asynchronous operation.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ evaluateMessageWhileSuspended(message) {
        switch(message._tag){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_YIELD_NOW"]:
                {
                    return EvaluationSignalYieldNow;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT_SIGNAL"]:
                {
                    this.processNewInterruptSignal(message.cause);
                    if (this._asyncInterruptor !== null) {
                        this._asyncInterruptor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])(message.cause));
                        this._asyncInterruptor = null;
                    }
                    return EvaluationSignalContinue;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RESUME"]:
                {
                    this._asyncInterruptor = null;
                    this._asyncBlockingOn = null;
                    this.evaluateEffect(message.effect);
                    return EvaluationSignalContinue;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_STATEFUL"]:
                {
                    message.onFiber(this, this._exitValue !== null ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberStatus$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["done"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberStatus$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspended"])(this.currentRuntimeFlags, this._asyncBlockingOn));
                    return EvaluationSignalContinue;
                }
            default:
                {
                    return absurd(message);
                }
        }
    }
    /**
   * Evaluates an effect until completion, potentially asynchronously.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ evaluateEffect(effect0) {
        this.currentSupervisor.onResume(this);
        try {
            let effect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(this.currentRuntimeFlags) && this.isInterrupted() ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])(this.getInterruptedCause()) : effect0;
            while(effect !== null){
                const eff = effect;
                const exit = this.runLoop(eff);
                if (exit === YieldedOp) {
                    const op = yieldedOpChannel.currentOp;
                    yieldedOpChannel.currentOp = null;
                    if (op._op === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_YIELD"]) {
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cooperativeYielding"])(this.currentRuntimeFlags)) {
                            this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["yieldNow"])());
                            this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["resume"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitVoid"]));
                            effect = null;
                        } else {
                            effect = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitVoid"];
                        }
                    } else if (op._op === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ASYNC"]) {
                        // Terminate this evaluation, async resumption will continue evaluation:
                        effect = null;
                    }
                } else {
                    this.currentRuntimeFlags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(this.currentRuntimeFlags, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["enable"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WindDown"]));
                    const interruption = this.interruptAllChildren();
                    if (interruption !== null) {
                        effect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(interruption, ()=>exit);
                    } else {
                        if (this._queue.length === 0) {
                            // No more messages to process, so we will allow the fiber to end life:
                            this.setExitValue(exit);
                        } else {
                            // There are messages, possibly added by the final op executed by
                            // the fiber. To be safe, we should execute those now before we
                            // allow the fiber to end life:
                            this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["resume"])(exit));
                        }
                        effect = null;
                    }
                }
            }
        } finally{
            this.currentSupervisor.onSuspend(this);
        }
    }
    /**
   * Begins execution of the effect associated with this fiber on the current
   * thread. This can be called to "kick off" execution of a fiber after it has
   * been created, in hopes that the effect can be executed synchronously.
   *
   * This is not the normal way of starting a fiber, but it is useful when the
   * express goal of executing the fiber is to synchronously produce its exit.
   */ start(effect) {
        if (!this._running) {
            this._running = true;
            const prev = globalThis[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentFiberURI"]];
            globalThis[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentFiberURI"]] = this;
            try {
                this.evaluateEffect(effect);
            } finally{
                this._running = false;
                globalThis[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentFiberURI"]] = prev;
                // Because we're special casing `start`, we have to be responsible
                // for spinning up the fiber if there were new messages added to
                // the queue between the completion of the effect and the transition
                // to the not running state.
                if (this._queue.length > 0) {
                    this.drainQueueLaterOnExecutor();
                }
            }
        } else {
            this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["resume"])(effect));
        }
    }
    /**
   * Begins execution of the effect associated with this fiber on in the
   * background, and on the correct thread pool. This can be called to "kick
   * off" execution of a fiber after it has been created, in hopes that the
   * effect can be executed synchronously.
   */ startFork(effect) {
        this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["resume"])(effect));
    }
    /**
   * Takes the current runtime flags, patches them to return the new runtime
   * flags, and then makes any changes necessary to fiber state based on the
   * specified patch.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ patchRuntimeFlags(oldRuntimeFlags, patch) {
        const newRuntimeFlags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["patch"])(oldRuntimeFlags, patch);
        globalThis[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentFiberURI"]] = this;
        this.currentRuntimeFlags = newRuntimeFlags;
        return newRuntimeFlags;
    }
    /**
   * Initiates an asynchronous operation, by building a callback that will
   * resume execution, and then feeding that callback to the registration
   * function, handling error cases and repeated resumptions appropriately.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ initiateAsync(runtimeFlags, asyncRegister) {
        let alreadyCalled = false;
        const callback = (effect)=>{
            if (!alreadyCalled) {
                alreadyCalled = true;
                this.tell((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberMessage$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["resume"])(effect));
            }
        };
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(runtimeFlags)) {
            this._asyncInterruptor = callback;
        }
        try {
            asyncRegister(callback);
        } catch (e) {
            callback((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(e)));
        }
    }
    pushStack(cont) {
        this._stack.push(cont);
        if (cont._op === "OnStep") {
            this._steps.push({
                refs: this.getFiberRefs(),
                flags: this.currentRuntimeFlags
            });
        }
    }
    popStack() {
        const item = this._stack.pop();
        if (item) {
            if (item._op === "OnStep") {
                this._steps.pop();
            }
            return item;
        }
        return;
    }
    getNextSuccessCont() {
        let frame = this.popStack();
        while(frame){
            if (frame._op !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_FAILURE"]) {
                return frame;
            }
            frame = this.popStack();
        }
    }
    getNextFailCont() {
        let frame = this.popStack();
        while(frame){
            if (frame._op !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_SUCCESS"] && frame._op !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_WHILE"] && frame._op !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ITERATOR"]) {
                return frame;
            }
            frame = this.popStack();
        }
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_TAG"]](op) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.currentContext, op));
    }
    ["Left"](op) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(op.left);
    }
    ["None"](_) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NoSuchElementException"]());
    }
    ["Right"](op) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])(op.right);
    }
    ["Some"](op) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])(op.value);
    }
    ["Micro"](op) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeAsync"])((microResume)=>{
            let resume = microResume;
            const fiber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Micro$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runFork"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Micro$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provideContext"])(op, this.currentContext));
            fiber.addObserver((exit)=>{
                if (exit._tag === "Success") {
                    return resume((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])(exit.value));
                }
                switch(exit.cause._tag){
                    case "Interrupt":
                        {
                            return resume((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])));
                        }
                    case "Fail":
                        {
                            return resume((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(exit.cause.error));
                        }
                    case "Die":
                        {
                            return resume((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(exit.cause.defect));
                        }
                }
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeAsync"])((abortResume)=>{
                resume = (_)=>{
                    abortResume(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"]);
                };
                fiber.unsafeInterrupt();
            });
        });
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SYNC"]](op) {
        const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>op.effect_instruction_i0());
        const cont = this.getNextSuccessCont();
        if (cont !== undefined) {
            if (!(cont._op in contOpSuccess)) {
                // @ts-expect-error
                absurd(cont);
            }
            // @ts-expect-error
            return contOpSuccess[cont._op](this, cont, value);
        } else {
            yieldedOpChannel.currentOp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])(value);
            return YieldedOp;
        }
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]](op) {
        const oldCur = op;
        const cont = this.getNextSuccessCont();
        if (cont !== undefined) {
            if (!(cont._op in contOpSuccess)) {
                // @ts-expect-error
                absurd(cont);
            }
            // @ts-expect-error
            return contOpSuccess[cont._op](this, cont, oldCur.effect_instruction_i0);
        } else {
            yieldedOpChannel.currentOp = oldCur;
            return YieldedOp;
        }
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]](op) {
        const cause = op.effect_instruction_i0;
        const cont = this.getNextFailCont();
        if (cont !== undefined) {
            switch(cont._op){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_FAILURE"]:
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_SUCCESS_AND_FAILURE"]:
                    {
                        if (!((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(this.currentRuntimeFlags) && this.isInterrupted())) {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>cont.effect_instruction_i1(cause));
                        } else {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["stripFailures"])(cause));
                        }
                    }
                case "OnStep":
                    {
                        if (!((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(this.currentRuntimeFlags) && this.isInterrupted())) {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])(cause));
                        } else {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["stripFailures"])(cause));
                        }
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_REVERT_FLAGS"]:
                    {
                        this.patchRuntimeFlags(this.currentRuntimeFlags, cont.patch);
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(this.currentRuntimeFlags) && this.isInterrupted()) {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"])(cause, this.getInterruptedCause()));
                        } else {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])(cause);
                        }
                    }
                default:
                    {
                        absurd(cont);
                    }
            }
        } else {
            yieldedOpChannel.currentOp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])(cause);
            return YieldedOp;
        }
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_WITH_RUNTIME"]](op) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>op.effect_instruction_i0(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberStatus$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["running"])(this.currentRuntimeFlags)));
    }
    ["Blocked"](op) {
        const refs = this.getFiberRefs();
        const flags = this.currentRuntimeFlags;
        if (this._steps.length > 0) {
            const frames = [];
            const snap = this._steps[this._steps.length - 1];
            let frame = this.popStack();
            while(frame && frame._op !== "OnStep"){
                frames.push(frame);
                frame = this.popStack();
            }
            this.setFiberRefs(snap.refs);
            this.currentRuntimeFlags = snap.flags;
            const patchRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRefsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["diff"])(snap.refs, refs);
            const patchFlags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["diff"])(snap.flags, flags);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["blocked"])(op.effect_instruction_i0, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((newFiber)=>{
                while(frames.length > 0){
                    newFiber.pushStack(frames.pop());
                }
                newFiber.setFiberRefs((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRefsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["patch"])(newFiber.id(), newFiber.getFiberRefs())(patchRefs));
                newFiber.currentRuntimeFlags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["patch"])(patchFlags)(newFiber.currentRuntimeFlags);
                return op.effect_instruction_i1;
            })));
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptibleMask"])((restore)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(forkDaemon((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runRequestBlock"])(op.effect_instruction_i0)), ()=>restore(op.effect_instruction_i1)));
    }
    ["RunBlocked"](op) {
        return runBlockedRequests(op.effect_instruction_i0);
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_UPDATE_RUNTIME_FLAGS"]](op) {
        const updateFlags = op.effect_instruction_i0;
        const oldRuntimeFlags = this.currentRuntimeFlags;
        const newRuntimeFlags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["patch"])(oldRuntimeFlags, updateFlags);
        // One more chance to short circuit: if we're immediately going
        // to interrupt. Interruption will cause immediate reversion of
        // the flag, so as long as we "peek ahead", there's no need to
        // set them to begin with.
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(newRuntimeFlags) && this.isInterrupted()) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])(this.getInterruptedCause());
        } else {
            // Impossible to short circuit, so record the changes
            this.patchRuntimeFlags(this.currentRuntimeFlags, updateFlags);
            if (op.effect_instruction_i1) {
                // Since we updated the flags, we need to revert them
                const revertFlags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["diff"])(newRuntimeFlags, oldRuntimeFlags);
                this.pushStack(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RevertFlags"](revertFlags, op));
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>op.effect_instruction_i1(oldRuntimeFlags));
            } else {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitVoid"];
            }
        }
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_SUCCESS"]](op) {
        this.pushStack(op);
        return op.effect_instruction_i0;
    }
    ["OnStep"](op) {
        this.pushStack(op);
        return op.effect_instruction_i0;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_FAILURE"]](op) {
        this.pushStack(op);
        return op.effect_instruction_i0;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_SUCCESS_AND_FAILURE"]](op) {
        this.pushStack(op);
        return op.effect_instruction_i0;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ASYNC"]](op) {
        this._asyncBlockingOn = op.effect_instruction_i1;
        this.initiateAsync(this.currentRuntimeFlags, op.effect_instruction_i0);
        yieldedOpChannel.currentOp = op;
        return YieldedOp;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_YIELD"]](op) {
        this._isYielding = false;
        yieldedOpChannel.currentOp = op;
        return YieldedOp;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_WHILE"]](op) {
        const check = op.effect_instruction_i0;
        const body = op.effect_instruction_i1;
        if (check()) {
            this.pushStack(op);
            return body();
        } else {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitVoid"];
        }
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ITERATOR"]](op) {
        return contOpSuccess[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ITERATOR"]](this, op, undefined);
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_COMMIT"]](op) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>op.commit());
    }
    /**
   * The main run-loop for evaluating effects.
   *
   * **NOTE**: This method must be invoked by the fiber itself.
   */ runLoop(effect0) {
        let cur = effect0;
        this.currentOpCount = 0;
        while(true){
            if ((this.currentRuntimeFlags & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OpSupervision"]) !== 0) {
                this.currentSupervisor.onEffect(this, cur);
            }
            if (this._queue.length > 0) {
                cur = this.drainQueueWhileRunning(this.currentRuntimeFlags, cur);
            }
            if (!this._isYielding) {
                this.currentOpCount += 1;
                const shouldYield = this.currentScheduler.shouldYield(this);
                if (shouldYield !== false) {
                    this._isYielding = true;
                    this.currentOpCount = 0;
                    const oldCur = cur;
                    cur = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["yieldNow"])({
                        priority: shouldYield
                    }), ()=>oldCur);
                }
            }
            try {
                // @ts-expect-error
                cur = this.currentTracer.context(()=>{
                    if (_version !== cur[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EffectTypeId"]]._V) {
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dieMessage"])(`Cannot execute an Effect versioned ${cur[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EffectTypeId"]]._V} with a Runtime of version ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCurrentVersion"])()}`);
                    }
                    // @ts-expect-error
                    return this[cur._op](cur);
                }, this);
                if (cur === YieldedOp) {
                    const op = yieldedOpChannel.currentOp;
                    if (op._op === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_YIELD"] || op._op === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ASYNC"]) {
                        return YieldedOp;
                    }
                    yieldedOpChannel.currentOp = null;
                    return op._op === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"] || op._op === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"] ? op : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(op));
                }
            } catch (e) {
                if (cur !== YieldedOp && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(cur, "_op") || !(cur._op in this)) {
                    cur = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dieMessage"])(`Not a valid effect: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toStringUnknown"])(cur)}`);
                } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isInterruptedException"])(e)) {
                    cur = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(e), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])));
                } else {
                    cur = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(e);
                }
            }
        }
    }
    run = ()=>{
        this.drainQueueOnCurrentThread();
    };
}
const currentMinimumLogLevel = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])("effect/FiberRef/currentMinimumLogLevel", ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefUnsafeMake"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$LogLevel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromLiteral"])("Info")));
const loggerWithConsoleLog = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["makeLogger"])((opts)=>{
        const services = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrDefault"])(opts.context, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentServices"]);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(services, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2f$console$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["consoleTag"]).unsafe.log(self.log(opts));
    });
const loggerWithLeveledLog = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["makeLogger"])((opts)=>{
        const services = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrDefault"])(opts.context, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentServices"]);
        const unsafeLogger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(services, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2f$console$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["consoleTag"]).unsafe;
        switch(opts.logLevel._tag){
            case "Debug":
                return unsafeLogger.debug(self.log(opts));
            case "Info":
                return unsafeLogger.info(self.log(opts));
            case "Trace":
                return unsafeLogger.trace(self.log(opts));
            case "Warning":
                return unsafeLogger.warn(self.log(opts));
            case "Error":
            case "Fatal":
                return unsafeLogger.error(self.log(opts));
            default:
                return unsafeLogger.log(self.log(opts));
        }
    });
const loggerWithConsoleError = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["makeLogger"])((opts)=>{
        const services = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrDefault"])(opts.context, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentServices"]);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(services, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2f$console$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["consoleTag"]).unsafe.error(self.log(opts));
    });
const defaultLogger = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/Logger/defaultLogger"), ()=>loggerWithConsoleLog(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["stringLogger"]));
const jsonLogger = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/Logger/jsonLogger"), ()=>loggerWithConsoleLog(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsonLogger"]));
const logFmtLogger = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/Logger/logFmtLogger"), ()=>loggerWithConsoleLog(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logfmtLogger"]));
const prettyLogger = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/Logger/prettyLogger"), ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["prettyLoggerDefault"]);
const structuredLogger = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/Logger/structuredLogger"), ()=>loggerWithConsoleLog(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["structuredLogger"]));
const tracerLogger = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/Logger/tracerLogger"), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["makeLogger"])(({ annotations, cause, context, fiberId, logLevel, message })=>{
        const span = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOption"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrDefault"])(context, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentContext"]), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["spanTag"]);
        if (span._tag === "None" || span.value._tag === "ExternalSpan") {
            return;
        }
        const clockService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrDefault"])(context, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentServices"]), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$clock$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clockTag"]);
        const attributes = {};
        for (const [key, value] of annotations){
            attributes[key] = value;
        }
        attributes["effect.fiberId"] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["threadName"])(fiberId);
        attributes["effect.logLevel"] = logLevel.label;
        if (cause !== null && cause._tag !== "Empty") {
            attributes["effect.cause"] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pretty"])(cause, {
                renderErrorCause: true
            });
        }
        span.value.event((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Inspectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toStringUnknown"])(Array.isArray(message) ? message[0] : message), clockService.unsafeCurrentTimeNanos(), attributes);
    }));
const loggerWithSpanAnnotations = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapInputOptions"])(self, (options)=>{
        const span = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(options.context, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentContext"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOption"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["spanTag"]));
        if (span._tag === "None") {
            return options;
        }
        return {
            ...options,
            annotations: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(options.annotations, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])("effect.traceId", span.value.traceId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])("effect.spanId", span.value.spanId), span.value._tag === "Span" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])("effect.spanName", span.value.name) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"])
        };
    });
const currentLoggers = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$GlobalValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalValue"])(/*#__PURE__*/ Symbol.for("effect/FiberRef/currentLoggers"), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefUnsafeMakeHashSet"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(defaultLogger, tracerLogger)));
const batchedLogger = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, window, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(scope, (scope)=>{
        let buffer = [];
        const flush = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
            if (buffer.length === 0) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
            }
            const arr = buffer;
            buffer = [];
            return f(arr);
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptibleMask"])((restore)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sleep"])(window), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(flush), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forever"], restore, forkDaemon, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((fiber)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopeAddFinalizer"])(scope, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptFiber"])(fiber))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(addFinalizer(()=>flush)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["makeLogger"])((options)=>{
                buffer.push(self.log(options));
            }))));
    }));
const annotateLogsScoped = function() {
    if (typeof arguments[0] === "string") {
        return fiberRefLocallyScopedWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentLogAnnotations"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(arguments[0], arguments[1]));
    }
    const entries = Object.entries(arguments[0]);
    return fiberRefLocallyScopedWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentLogAnnotations"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mutate"])((annotations)=>{
        for(let i = 0; i < entries.length; i++){
            const [key, value] = entries[i];
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(annotations, key, value);
        }
        return annotations;
    }));
};
const whenLogLevel = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (effect, level)=>{
    const requiredLogLevel = typeof level === "string" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$LogLevel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromLiteral"])(level) : level;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((fiberState)=>{
        const minimumLogLevel = fiberState.getFiberRef(currentMinimumLogLevel);
        // Imitate the behaviour of `FiberRuntime.log`
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$LogLevel$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["greaterThan"])(minimumLogLevel, requiredLogLevel)) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])());
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(effect, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"]);
    });
});
const acquireRelease = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[0]), (acquire, release)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptible"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tap"])(acquire, (a)=>addFinalizer((exit)=>release(a, exit)))));
const acquireReleaseInterruptible = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[0]), (acquire, release)=>ensuring(acquire, addFinalizer((exit)=>release(exit))));
const addFinalizer = (finalizer)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((runtime)=>{
        const acquireRefs = runtime.getFiberRefs();
        const acquireFlags = runtime.currentRuntimeFlags;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(scope, (scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopeAddFinalizerExit"])(scope, (exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((runtimeFinalizer)=>{
                    const preRefs = runtimeFinalizer.getFiberRefs();
                    const preFlags = runtimeFinalizer.currentRuntimeFlags;
                    const patchRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRefsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["diff"])(preRefs, acquireRefs);
                    const patchFlags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["diff"])(preFlags, acquireFlags);
                    const inverseRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRefsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["diff"])(acquireRefs, preRefs);
                    runtimeFinalizer.setFiberRefs((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRefsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["patch"])(patchRefs, runtimeFinalizer.id(), acquireRefs));
                    return ensuring((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withRuntimeFlags"])(finalizer(exit), patchFlags), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>{
                        runtimeFinalizer.setFiberRefs((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRefsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["patch"])(inverseRefs, runtimeFinalizer.id(), runtimeFinalizer.getFiberRefs()));
                    }));
                })));
    });
const daemonChildren = (self)=>{
    const forkScope = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefLocally"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentForkScopeOverride"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberScope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalScope"]));
    return forkScope(self);
};
/** @internal */ const _existsParFound = /*#__PURE__*/ Symbol.for("effect/Effect/existsPar/found");
const exists = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isIterable"])(args[0]) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[0]), (elements, predicate, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$concurrency$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSimple"])(options?.concurrency, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>existsLoop(elements[Symbol.iterator](), 0, predicate)), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchEffect"])(forEach(elements, (a, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["if_"])(predicate(a, i), {
                onTrue: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(_existsParFound),
                onFalse: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"]
            }), options), {
            onFailure: (e)=>e === _existsParFound ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(true) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(e),
            onSuccess: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(false)
        })));
const existsLoop = (iterator, index, f)=>{
    const next = iterator.next();
    if (next.done) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(false);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(f(next.value, index), (b)=>b ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(b) : existsLoop(iterator, index + 1, f)));
};
const filter = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isIterable"])(args[0]) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[0]), (elements, predicate, options)=>{
    const predicate_ = options?.negate ? (a, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(predicate(a, i), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Boolean$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["not"]) : predicate;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$concurrency$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSimple"])(options?.concurrency, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(elements).reduceRight((effect, a, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipWith"])(effect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>predicate_(a, i)), (list, b)=>b ? [
                        a,
                        ...list
                    ] : list), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>new Array()))), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(forEach(elements, (a, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(predicate_(a, i), (b)=>b ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(a) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])()), options), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSomes"]));
});
// === all
const allResolveInput = (input)=>{
    if (Array.isArray(input) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isIterable"])(input)) {
        return [
            input,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])()
        ];
    }
    const keys = Object.keys(input);
    const size = keys.length;
    return [
        keys.map((k)=>input[k]),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])((values)=>{
            const res = {};
            for(let i = 0; i < size; i++){
                ;
                res[keys[i]] = values[i];
            }
            return res;
        })
    ];
};
const allValidate = (effects, reconcile, options)=>{
    const eitherEffects = [];
    for (const effect of effects){
        eitherEffects.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["either"])(effect));
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(forEach(eitherEffects, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"], {
        concurrency: options?.concurrency,
        batching: options?.batching,
        concurrentFinalizers: options?.concurrentFinalizers
    }), (eithers)=>{
        const none = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
        const size = eithers.length;
        const errors = new Array(size);
        const successes = new Array(size);
        let errored = false;
        for(let i = 0; i < size; i++){
            const either = eithers[i];
            if (either._tag === "Left") {
                errors[i] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(either.left);
                errored = true;
            } else {
                successes[i] = either.right;
                errors[i] = none;
            }
        }
        if (errored) {
            return reconcile._tag === "Some" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(reconcile.value(errors)) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(errors);
        } else if (options?.discard) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
        }
        return reconcile._tag === "Some" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(reconcile.value(successes)) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(successes);
    });
};
const allEither = (effects, reconcile, options)=>{
    const eitherEffects = [];
    for (const effect of effects){
        eitherEffects.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["either"])(effect));
    }
    if (options?.discard) {
        return forEach(eitherEffects, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"], {
            concurrency: options?.concurrency,
            batching: options?.batching,
            discard: true,
            concurrentFinalizers: options?.concurrentFinalizers
        });
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(forEach(eitherEffects, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"], {
        concurrency: options?.concurrency,
        batching: options?.batching,
        concurrentFinalizers: options?.concurrentFinalizers
    }), (eithers)=>reconcile._tag === "Some" ? reconcile.value(eithers) : eithers);
};
const all = (arg, options)=>{
    const [effects, reconcile] = allResolveInput(arg);
    if (options?.mode === "validate") {
        return allValidate(effects, reconcile, options);
    } else if (options?.mode === "either") {
        return allEither(effects, reconcile, options);
    }
    return options?.discard !== true && reconcile._tag === "Some" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(forEach(effects, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"], options), reconcile.value) : forEach(effects, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"], options);
};
const allWith = (options)=>(arg)=>all(arg, options);
const allSuccesses = (elements, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(all((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(elements).map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"]), options), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filterMap"])((exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitIsSuccess"])(exit) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(exit.effect_instruction_i0) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])()));
const replicate = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, n)=>Array.from({
        length: n
    }, ()=>self));
const replicateEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[0]), (self, n, options)=>all(replicate(self, n), options));
const forEach = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isIterable"])(args[0]), (self, f, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((r)=>{
        const isRequestBatchingEnabled = options?.batching === true || options?.batching === "inherit" && r.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentRequestBatching"]);
        if (options?.discard) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$concurrency$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(options.concurrency, ()=>finalizersMaskInternal(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"], options?.concurrentFinalizers)((restore)=>isRequestBatchingEnabled ? forEachConcurrentDiscard(self, (a, i)=>restore(f(a, i)), true, false, 1) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEachSequentialDiscard"])(self, (a, i)=>restore(f(a, i)))), ()=>finalizersMaskInternal(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"], options?.concurrentFinalizers)((restore)=>forEachConcurrentDiscard(self, (a, i)=>restore(f(a, i)), isRequestBatchingEnabled, false)), (n)=>finalizersMaskInternal((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallelN"])(n), options?.concurrentFinalizers)((restore)=>forEachConcurrentDiscard(self, (a, i)=>restore(f(a, i)), isRequestBatchingEnabled, false, n)));
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$concurrency$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(options?.concurrency, ()=>finalizersMaskInternal(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"], options?.concurrentFinalizers)((restore)=>isRequestBatchingEnabled ? forEachParN(self, 1, (a, i)=>restore(f(a, i)), true) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEachSequential"])(self, (a, i)=>restore(f(a, i)))), ()=>finalizersMaskInternal(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"], options?.concurrentFinalizers)((restore)=>forEachParUnbounded(self, (a, i)=>restore(f(a, i)), isRequestBatchingEnabled)), (n)=>finalizersMaskInternal((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallelN"])(n), options?.concurrentFinalizers)((restore)=>forEachParN(self, n, (a, i)=>restore(f(a, i)), isRequestBatchingEnabled)));
    }));
const forEachParUnbounded = (self, f, batching)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
        const as = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(self);
        const array = new Array(as.length);
        const fn = (a, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(f(a, i), (b)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>array[i] = b));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(forEachConcurrentDiscard(as, fn, batching, false), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(array));
    });
const forEachConcurrentDiscard = (self, f, batching, processAll, n)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptibleMask"])((restore)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["transplant"])((graft)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((parent)=>{
                let todos = Array.from(self).reverse();
                let target = todos.length;
                if (target === 0) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
                }
                let counter = 0;
                let interrupted = false;
                const fibersCount = n ? Math.min(todos.length, n) : todos.length;
                const fibers = new Set();
                const results = new Array();
                const interruptAll = ()=>fibers.forEach((fiber)=>{
                        fiber.currentScheduler.scheduleTask(()=>{
                            fiber.unsafeInterruptAsFork(parent.id());
                        }, 0);
                    });
                const startOrder = new Array();
                const joinOrder = new Array();
                const residual = new Array();
                const collectExits = ()=>{
                    const exits = results.filter(({ exit })=>exit._tag === "Failure").sort((a, b)=>a.index < b.index ? -1 : a.index === b.index ? 0 : 1).map(({ exit })=>exit);
                    if (exits.length === 0) {
                        exits.push(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitVoid"]);
                    }
                    return exits;
                };
                const runFiber = (eff, interruptImmediately = false)=>{
                    const runnable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptible"])(graft(eff));
                    const fiber = unsafeForkUnstarted(runnable, parent, parent.currentRuntimeFlags, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberScope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalScope"]);
                    parent.currentScheduler.scheduleTask(()=>{
                        if (interruptImmediately) {
                            fiber.unsafeInterruptAsFork(parent.id());
                        }
                        fiber.resume(runnable);
                    }, 0);
                    return fiber;
                };
                const onInterruptSignal = ()=>{
                    if (!processAll) {
                        target -= todos.length;
                        todos = [];
                    }
                    interrupted = true;
                    interruptAll();
                };
                const stepOrExit = batching ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["step"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"];
                const processingFiber = runFiber((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["async"])((resume)=>{
                    const pushResult = (res, index)=>{
                        if (res._op === "Blocked") {
                            residual.push(res);
                        } else {
                            results.push({
                                index,
                                exit: res
                            });
                            if (res._op === "Failure" && !interrupted) {
                                onInterruptSignal();
                            }
                        }
                    };
                    const next = ()=>{
                        if (todos.length > 0) {
                            const a = todos.pop();
                            let index = counter++;
                            const returnNextElement = ()=>{
                                const a = todos.pop();
                                index = counter++;
                                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["yieldNow"])(), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(stepOrExit(restore(f(a, index))), onRes));
                            };
                            const onRes = (res)=>{
                                if (todos.length > 0) {
                                    pushResult(res, index);
                                    if (todos.length > 0) {
                                        return returnNextElement();
                                    }
                                }
                                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(res);
                            };
                            const todo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(stepOrExit(restore(f(a, index))), onRes);
                            const fiber = runFiber(todo);
                            startOrder.push(fiber);
                            fibers.add(fiber);
                            if (interrupted) {
                                fiber.currentScheduler.scheduleTask(()=>{
                                    fiber.unsafeInterruptAsFork(parent.id());
                                }, 0);
                            }
                            fiber.addObserver((wrapped)=>{
                                let exit;
                                if (wrapped._op === "Failure") {
                                    exit = wrapped;
                                } else {
                                    exit = wrapped.effect_instruction_i0;
                                }
                                joinOrder.push(fiber);
                                fibers.delete(fiber);
                                pushResult(exit, index);
                                if (results.length === target) {
                                    resume((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitCollectAll"])(collectExits(), {
                                        parallel: true
                                    }), ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitVoid"])));
                                } else if (residual.length + results.length === target) {
                                    const exits = collectExits();
                                    const requests = residual.map((blocked)=>blocked.effect_instruction_i0).reduce(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$blockedRequests$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["par"]);
                                    resume((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["blocked"])(requests, forEachConcurrentDiscard([
                                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitCollectAll"])(exits, {
                                            parallel: true
                                        }), ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitVoid"]),
                                        ...residual.map((blocked)=>blocked.effect_instruction_i1)
                                    ], (i)=>i, batching, true, n))));
                                } else {
                                    next();
                                }
                            });
                        }
                    };
                    for(let i = 0; i < fibersCount; i++){
                        next();
                    }
                }));
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["asVoid"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["onExit"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(restore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["join"])(processingFiber))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitMatch"])({
                    onFailure: (cause)=>{
                        onInterruptSignal();
                        const target = residual.length + 1;
                        const concurrency = Math.min(typeof n === "number" ? n : residual.length, residual.length);
                        const toPop = Array.from(residual);
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["async"])((cb)=>{
                            const exits = [];
                            let count = 0;
                            let index = 0;
                            const check = (index, hitNext)=>(exit)=>{
                                    exits[index] = exit;
                                    count++;
                                    if (count === target) {
                                        cb((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitFailCause"])(cause)));
                                    }
                                    if (toPop.length > 0 && hitNext) {
                                        next();
                                    }
                                };
                            const next = ()=>{
                                runFiber(toPop.pop(), true).addObserver(check(index, true));
                                index++;
                            };
                            processingFiber.addObserver(check(index, false));
                            index++;
                            for(let i = 0; i < concurrency; i++){
                                next();
                            }
                        });
                    },
                    onSuccess: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEachSequential"])(joinOrder, (f)=>f.inheritAll)
                })));
            })));
const forEachParN = (self, n, f, batching)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
        const as = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(self);
        const array = new Array(as.length);
        const fn = (a, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(f(a, i), (b)=>array[i] = b);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(forEachConcurrentDiscard(as, fn, batching, false, n), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(array));
    });
const fork = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((state, status)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(unsafeFork(self, state, status.runtimeFlags)));
const forkDaemon = (self)=>forkWithScopeOverride(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberScope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalScope"]);
const forkWithErrorHandler = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, handler)=>fork((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["onError"])(self, (cause)=>{
        const either = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failureOrCause"])(cause);
        switch(either._tag){
            case "Left":
                return handler(either.left);
            case "Right":
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(either.right);
        }
    })));
const unsafeFork = (effect, parentFiber, parentRuntimeFlags, overrideScope = null)=>{
    const childFiber = unsafeMakeChildFiber(effect, parentFiber, parentRuntimeFlags, overrideScope);
    childFiber.resume(effect);
    return childFiber;
};
const unsafeForkUnstarted = (effect, parentFiber, parentRuntimeFlags, overrideScope = null)=>{
    const childFiber = unsafeMakeChildFiber(effect, parentFiber, parentRuntimeFlags, overrideScope);
    return childFiber;
};
const unsafeMakeChildFiber = (effect, parentFiber, parentRuntimeFlags, overrideScope = null)=>{
    const childId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeMake"])();
    const parentFiberRefs = parentFiber.getFiberRefs();
    const childFiberRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forkAs"])(parentFiberRefs, childId);
    const childFiber = new FiberRuntime(childId, childFiberRefs, parentRuntimeFlags);
    const childContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiberRefs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrDefault"])(childFiberRefs, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentContext"]);
    const supervisor = childFiber.currentSupervisor;
    supervisor.onStart(childContext, effect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(parentFiber), childFiber);
    childFiber.addObserver((exit)=>supervisor.onEnd(exit, childFiber));
    const parentScope = overrideScope !== null ? overrideScope : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(parentFiber.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentForkScopeOverride"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(()=>parentFiber.scope()));
    parentScope.add(parentRuntimeFlags, childFiber);
    return childFiber;
};
/* @internal */ const forkWithScopeOverride = (self, scopeOverride)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((parentFiber, parentStatus)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(unsafeFork(self, parentFiber, parentStatus.runtimeFlags, scopeOverride)));
const mergeAll = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isFunction"])(args[2]), (elements, zero, f, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$concurrency$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSimple"])(options?.concurrency, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(elements).reduce((acc, a, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipWith"])(acc, a, (acc, a)=>f(acc, a, i)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(zero)), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(zero), (acc)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(forEach(elements, (effect, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(effect, (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["update"])(acc, (b)=>f(b, a, i))), options), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(acc)))));
const partition = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isIterable"])(args[0]), (elements, f, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(forEach(elements, (a, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["either"])(f(a, i)), options), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((chunk)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["partitionMap"])(chunk, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]))));
const validateAll = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isIterable"])(args[0]), (elements, f, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(partition(elements, f, {
        concurrency: options?.concurrency,
        batching: options?.batching,
        concurrentFinalizers: options?.concurrentFinalizers
    }), ([es, bs])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmptyArray"])(es) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(es) : options?.discard ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(bs)));
const raceAll = (all)=>{
    const list = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(all);
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmpty"])(list)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dieSync"])(()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["IllegalArgumentException"](`Received an empty collection of effects`));
    }
    const self = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["headNonEmpty"])(list);
    const effects = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tailNonEmpty"])(list);
    const inheritAll = (res)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["inheritAll"])(res[1]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(res[0]));
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deferredMake"])(), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(effects.length), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((fails)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptibleMask"])((restore)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(fork((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(self)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((head)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(effects, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEachSequential"])((effect)=>fork((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(effect))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((fibers)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(fibers)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((tail)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(tail, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["prepend"])(head))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tap"])((fibers)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(fibers, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["reduce"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"], (effect, fiber)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(effect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["_await"])(fiber), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(raceAllArbiter(fibers, fiber, done, fails)), fork, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["asVoid"])))))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((fibers)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(restore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(done), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(inheritAll))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["onInterrupt"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(fibers, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["reduce"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"], (effect, fiber)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(effect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipLeft"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptFiber"])(fiber))))))))))))))));
};
const raceAllArbiter = (fibers, winner, deferred, fails)=>(exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitMatchEffect"])(exit, {
            onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modify"])(fails, (fails)=>[
                        fails === 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deferredFailCause"])(deferred, cause), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["asVoid"]) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"],
                        fails - 1
                    ]), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"]),
            onSuccess: (value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deferredSucceed"])(deferred, [
                    value,
                    winner
                ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((set)=>set ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(fibers), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["reduce"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"], (effect, fiber)=>fiber === winner ? effect : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(effect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipLeft"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptFiber"])(fiber))))) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"]))
        });
const reduceEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isIterable"])(args[0]) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[0]), (elements, zero, f, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$concurrency$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSimple"])(options?.concurrency, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(elements).reduce((acc, a, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipWith"])(acc, a, (acc, a)=>f(acc, a, i)), zero), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(mergeAll([
                zero,
                ...elements
            ], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])(), (acc, elem, i)=>{
                switch(acc._tag){
                    case "None":
                        {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(elem);
                        }
                    case "Some":
                        {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(f(acc.value, elem, i));
                        }
                }
            }, options), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((option)=>{
                switch(option._tag){
                    case "None":
                        {
                            throw new Error("BUG: Effect.reduceEffect - please report an issue at https://github.com/Effect-TS/effect/issues");
                        }
                    case "Some":
                        {
                            return option.value;
                        }
                }
            })))));
const parallelFinalizers = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["contextWithEffect"])((context)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOption"])(context, scopeTag), {
            onNone: ()=>self,
            onSome: (scope)=>{
                switch(scope.strategy._tag){
                    case "Parallel":
                        return self;
                    case "Sequential":
                    case "ParallelN":
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopeFork"])(scope, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"]), (inner)=>scopeExtend(self, inner));
                }
            }
        }));
const parallelNFinalizers = (parallelism)=>(self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["contextWithEffect"])((context)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOption"])(context, scopeTag), {
                onNone: ()=>self,
                onSome: (scope)=>{
                    if (scope.strategy._tag === "ParallelN" && scope.strategy.parallelism === parallelism) {
                        return self;
                    }
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopeFork"])(scope, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallelN"])(parallelism)), (inner)=>scopeExtend(self, inner));
                }
            }));
const finalizersMask = (strategy)=>(self)=>finalizersMaskInternal(strategy, true)(self);
const finalizersMaskInternal = (strategy, concurrentFinalizers)=>(self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["contextWithEffect"])((context)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOption"])(context, scopeTag), {
                onNone: ()=>self(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]),
                onSome: (scope)=>{
                    if (concurrentFinalizers === true) {
                        const patch = strategy._tag === "Parallel" ? parallelFinalizers : strategy._tag === "Sequential" ? sequentialFinalizers : parallelNFinalizers(strategy.parallelism);
                        switch(scope.strategy._tag){
                            case "Parallel":
                                return patch(self(parallelFinalizers));
                            case "Sequential":
                                return patch(self(sequentialFinalizers));
                            case "ParallelN":
                                return patch(self(parallelNFinalizers(scope.strategy.parallelism)));
                        }
                    } else {
                        return self(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]);
                    }
                }
            }));
const scopeWith = (f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(scopeTag, f);
const scopedWith = (f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(scopeMake(), (scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["onExit"])(f(scope), (exit)=>scope.close(exit)));
const scopedEffect = (effect)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(scopeMake(), (scope)=>scopeUse(effect, scope));
const sequentialFinalizers = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["contextWithEffect"])((context)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOption"])(context, scopeTag), {
            onNone: ()=>self,
            onSome: (scope)=>{
                switch(scope.strategy._tag){
                    case "Sequential":
                        return self;
                    case "Parallel":
                    case "ParallelN":
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopeFork"])(scope, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$ExecutionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"]), (inner)=>scopeExtend(self, inner));
                }
            }
        }));
const tagMetricsScoped = (key, value)=>labelMetricsScoped([
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$metric$2f$label$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(key, value)
    ]);
const labelMetricsScoped = (labels)=>fiberRefLocallyScopedWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentMetricLabels"], (old)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["union"])(old, labels));
const using = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, use)=>scopedWith((scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(scopeExtend(self, scope), use)));
const validate = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[1]), (self, that, options)=>validateWith(self, that, (a, b)=>[
            a,
            b
        ], options));
const validateWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[1]), (self, that, f, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(zipWithOptions((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(self), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(that), (ea, eb)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitZipWith"])(ea, eb, {
            onSuccess: f,
            onFailure: (ca, cb)=>options?.concurrent ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"])(ca, cb) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"])(ca, cb)
        }), options)));
const validateAllPar = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (elements, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(partition(elements, f), ([es, bs])=>es.length === 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(bs) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(es)));
const validateAllParDiscard = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (elements, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(partition(elements, f), ([es, _])=>es.length === 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(es)));
const validateFirst = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isIterable"])(args[0]), (elements, f, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flip"])(forEach(elements, (a, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flip"])(f(a, i)), options)));
const withClockScoped = (c)=>fiberRefLocallyScopedWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentServices"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$clock$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clockTag"], c));
const withRandomScoped = (value)=>fiberRefLocallyScopedWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentServices"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$random$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["randomTag"], value));
const withConfigProviderScoped = (provider)=>fiberRefLocallyScopedWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentServices"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$configProvider$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["configProviderTag"], provider));
const withEarlyRelease = (self)=>scopeWith((parent)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopeFork"])(parent, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$executionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"]), (child)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, scopeExtend(child), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((value)=>[
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberIdWith"])((fiberId)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopeClose"])(child, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitInterrupt"])(fiberId))),
                    value
                ]))));
const zipOptions = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[1]), (self, that, options)=>zipWithOptions(self, that, (a, b)=>[
            a,
            b
        ], options));
const zipLeftOptions = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[1]), (self, that, options)=>{
    if (options?.concurrent !== true && (options?.batching === undefined || options.batching === false)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipLeft"])(self, that);
    }
    return zipWithOptions(self, that, (a, _)=>a, options);
});
const zipRightOptions = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[1]), (self, that, options)=>{
    if (options?.concurrent !== true && (options?.batching === undefined || options.batching === false)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(self, that);
    }
    return zipWithOptions(self, that, (_, b)=>b, options);
});
const zipWithOptions = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEffect"])(args[1]), (self, that, f, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(all([
        self,
        that
    ], {
        concurrency: options?.concurrent ? 2 : 1,
        batching: options?.batching,
        concurrentFinalizers: options?.concurrentFinalizers
    }), ([a, a2])=>f(a, a2)));
const withRuntimeFlagsScoped = (update)=>{
    if (update === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$RuntimeFlagsPatch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"]) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runtimeFlags"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((runtimeFlags)=>{
        const updatedRuntimeFlags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["patch"])(runtimeFlags, update);
        const revertRuntimeFlags = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["diff"])(updatedRuntimeFlags, runtimeFlags);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateRuntimeFlags"])(update), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(addFinalizer(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateRuntimeFlags"])(revertRuntimeFlags))), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["asVoid"]);
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptible"]);
};
const scopeTag = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GenericTag"])("effect/Scope");
const scope = scopeTag;
const scopeUnsafeAddFinalizer = (scope, fin)=>{
    if (scope.state._tag === "Open") {
        scope.state.finalizers.set({}, fin);
    }
};
const ScopeImplProto = {
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ScopeTypeId"]]: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ScopeTypeId"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CloseableScopeTypeId"]]: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CloseableScopeTypeId"],
    pipe () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    },
    fork (strategy) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>{
            const newScope = scopeUnsafeMake(strategy);
            if (this.state._tag === "Closed") {
                newScope.state = this.state;
                return newScope;
            }
            const key = {};
            const fin = (exit)=>newScope.close(exit);
            this.state.finalizers.set(key, fin);
            scopeUnsafeAddFinalizer(newScope, (_)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>{
                    if (this.state._tag === "Open") {
                        this.state.finalizers.delete(key);
                    }
                }));
            return newScope;
        });
    },
    close (exit) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
            if (this.state._tag === "Closed") {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
            }
            const finalizers = Array.from(this.state.finalizers.values()).reverse();
            this.state = {
                _tag: "Closed",
                exit
            };
            if (finalizers.length === 0) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$executionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSequential"])(this.strategy) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEachSequential"])(finalizers, (fin)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(fin(exit))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((results)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitCollectAll"])(results), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitAsVoid"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitVoid"])))) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$executionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isParallel"])(this.strategy) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(forEachParUnbounded(finalizers, (fin)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(fin(exit)), false), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((results)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitCollectAll"])(results, {
                    parallel: true
                }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitAsVoid"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitVoid"])))) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(forEachParN(finalizers, this.strategy.parallelism, (fin)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(fin(exit)), false), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((results)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitCollectAll"])(results, {
                    parallel: true
                }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitAsVoid"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitVoid"]))));
        });
    },
    addFinalizer (fin) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
            if (this.state._tag === "Closed") {
                return fin(this.state.exit);
            }
            this.state.finalizers.set({}, fin);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
        });
    }
};
const scopeUnsafeMake = (strategy = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$executionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"])=>{
    const scope = Object.create(ScopeImplProto);
    scope.strategy = strategy;
    scope.state = {
        _tag: "Open",
        finalizers: new Map()
    };
    return scope;
};
const scopeMake = (strategy = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$executionStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>scopeUnsafeMake(strategy));
const scopeExtend = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (effect, scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapInputContext"])(effect, // @ts-expect-error
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["merge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(scopeTag, scope))));
const scopeUse = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (effect, scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(effect, scopeExtend(scope), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["onExit"])((exit)=>scope.close(exit))));
const fiberRefUnsafeMakeSupervisor = (initial)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefUnsafeMakePatch"])(initial, {
        differ: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$supervisor$2f$patch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["differ"],
        fork: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$supervisor$2f$patch$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"]
    });
const fiberRefLocallyScoped = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["asVoid"])(acquireRelease((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefGet"])(self), (oldValue)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefSet"])(self, value), oldValue)), (oldValue)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefSet"])(self, oldValue))));
const fiberRefLocallyScopedWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefGetWith"])(self, (a)=>fiberRefLocallyScoped(self, f(a))));
const fiberRefMake = (initial, options)=>fiberRefMakeWith(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefUnsafeMake"])(initial, options));
const fiberRefMakeWith = (ref)=>acquireRelease((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(ref), (ref)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefUpdate"])(ref, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"])), (fiberRef)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefDelete"])(fiberRef));
const fiberRefMakeContext = (initial)=>fiberRefMakeWith(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefUnsafeMakeContext"])(initial));
const fiberRefMakeRuntimeFlags = (initial)=>fiberRefMakeWith(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefUnsafeMakeRuntimeFlags"])(initial));
const currentRuntimeFlags = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefUnsafeMakeRuntimeFlags"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$runtimeFlags$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"]);
const currentSupervisor = /*#__PURE__*/ fiberRefUnsafeMakeSupervisor(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$supervisor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"]);
const fiberAwaitAll = (fibers)=>forEach(fibers, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["_await"]);
const fiberAll = (fibers)=>{
    const _fiberAll = {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CommitPrototype"],
        commit () {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["join"])(this);
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FiberTypeId"]]: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberVariance"],
        id: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(fibers).reduce((id, fiber)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])(id, fiber.id()), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"]),
        await: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(forEachParUnbounded(fibers, (fiber)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])(fiber.await), false)),
        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(forEachParUnbounded(fibers, (fiber)=>fiber.children, false), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"]),
        inheritAll: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEachSequentialDiscard"])(fibers, (fiber)=>fiber.inheritAll),
        poll: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEachSequential"])(fibers, (fiber)=>fiber.poll), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["reduceRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitSucceed"])(new Array())), (optionB, optionA)=>{
            switch(optionA._tag){
                case "None":
                    {
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
                    }
                case "Some":
                    {
                        switch(optionB._tag){
                            case "None":
                                {
                                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
                                }
                            case "Some":
                                {
                                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitZipWith"])(optionA.value, optionB.value, {
                                        onSuccess: (a, chunk)=>[
                                                a,
                                                ...chunk
                                            ],
                                        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"]
                                    }));
                                }
                        }
                    }
            }
        })),
        interruptAsFork: (fiberId)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEachSequentialDiscard"])(fibers, (fiber)=>fiber.interruptAsFork(fiberId))
    };
    return _fiberAll;
};
const fiberInterruptFork = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["asVoid"])(forkDaemon((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptFiber"])(self)));
const fiberJoinAll = (fibers)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["join"])(fiberAll(fibers));
const fiberScoped = (self)=>acquireRelease((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(self), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptFiber"]);
const raceWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, other, options)=>raceFibersWith(self, other, {
        onSelfWin: (winner, loser)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(winner.await, (exit)=>{
                switch(exit._tag){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
                        {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(winner.inheritAll, ()=>options.onSelfDone(exit, loser));
                        }
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
                        {
                            return options.onSelfDone(exit, loser);
                        }
                }
            }),
        onOtherWin: (winner, loser)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(winner.await, (exit)=>{
                switch(exit._tag){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCESS"]:
                        {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(winner.inheritAll, ()=>options.onOtherDone(exit, loser));
                        }
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAILURE"]:
                        {
                            return options.onOtherDone(exit, loser);
                        }
                }
            })
    }));
const disconnect = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptibleMask"])((restore)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberIdWith"])((fiberId)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(forkDaemon(restore(self)), (fiber)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(restore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["join"])(fiber)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["onInterrupt"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(fiber, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAsFork"])(fiberId)))))));
const race = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberIdWith"])((parentFiberId)=>raceWith(self, that, {
            onSelfDone: (exit, right)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitMatchEffect"])(exit, {
                    onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["join"])(right), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapErrorCause"])((cause2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"])(cause, cause2))),
                    onSuccess: (value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(right, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAsFiber"])(parentFiberId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(value))
                }),
            onOtherDone: (exit, left)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitMatchEffect"])(exit, {
                    onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["join"])(left), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapErrorCause"])((cause2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"])(cause2, cause))),
                    onSuccess: (value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(left, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAsFiber"])(parentFiberId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(value))
                })
        })));
const raceFibersWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, other, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((parentFiber, parentStatus)=>{
        const parentRuntimeFlags = parentStatus.runtimeFlags;
        const raceIndicator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(true);
        const leftFiber = unsafeMakeChildFiber(self, parentFiber, parentRuntimeFlags, options.selfScope);
        const rightFiber = unsafeMakeChildFiber(other, parentFiber, parentRuntimeFlags, options.otherScope);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["async"])((cb)=>{
            leftFiber.addObserver(()=>completeRace(leftFiber, rightFiber, options.onSelfWin, raceIndicator, cb));
            rightFiber.addObserver(()=>completeRace(rightFiber, leftFiber, options.onOtherWin, raceIndicator, cb));
            leftFiber.startFork(self);
            rightFiber.startFork(other);
        }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])(leftFiber.id(), rightFiber.id()));
    }));
const completeRace = (winner, loser, cont, ab, cb)=>{
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$MutableRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["compareAndSet"])(true, false)(ab)) {
        cb(cont(winner, loser));
    }
};
const ensuring = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, finalizer)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptibleMask"])((restore)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])(restore(self), {
            onFailure: (cause1)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])(finalizer, {
                    onFailure: (cause2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sequential"])(cause1, cause2)),
                    onSuccess: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause1)
                }),
            onSuccess: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(finalizer, a)
        })));
const invokeWithInterrupt = (self, entries, onInterrupt)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberIdWith"])((id)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(forkDaemon((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(self)), (processing)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["async"])((cb)=>{
                const counts = entries.map((_)=>_.listeners.count);
                const checkDone = ()=>{
                    if (counts.every((count)=>count === 0)) {
                        if (entries.every((_)=>{
                            if (_.result.state.current._tag === "Pending") {
                                return true;
                            } else if (_.result.state.current._tag === "Done" && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitIsExit"])(_.result.state.current.effect) && _.result.state.current.effect._tag === "Failure" && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isInterrupted"])(_.result.state.current.effect.cause)) {
                                return true;
                            } else {
                                return false;
                            }
                        })) {
                            cleanup.forEach((f)=>f());
                            onInterrupt?.();
                            cb((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptFiber"])(processing));
                        }
                    }
                };
                processing.addObserver((exit)=>{
                    cleanup.forEach((f)=>f());
                    cb(exit);
                });
                const cleanup = entries.map((r, i)=>{
                    const observer = (count)=>{
                        counts[i] = count;
                        checkDone();
                    };
                    r.listeners.addObserver(observer);
                    return ()=>r.listeners.removeObserver(observer);
                });
                checkDone();
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>{
                    cleanup.forEach((f)=>f());
                });
            })), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
                const residual = entries.flatMap((entry)=>{
                    if (!entry.state.completed) {
                        return [
                            entry
                        ];
                    }
                    return [];
                });
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEachSequentialDiscard"])(residual, (entry)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$request$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["complete"])(entry.request, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exitInterrupt"])(id)));
            })));
const interruptWhenPossible = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, all)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fiberRefGetWith"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$completedRequestMap$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentRequestMap"], (map)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
            const entries = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(all).flatMap((_)=>map.has(_) ? [
                    map.get(_)
                ] : []);
            return invokeWithInterrupt(self, entries);
        })));
const makeSpanScoped = (name, options)=>{
    options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addSpanStackTrace"])(options);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptible"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((fiber)=>{
        const scope = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(fiber.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentContext"]), scopeTag);
        const span = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeMakeSpan"])(fiber, name, options);
        const timingEnabled = fiber.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentTracerTimingEnabled"]);
        const clock_ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(fiber.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentServices"]), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$clock$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clockTag"]);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopeAddFinalizerExit"])(scope, (exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["endSpan"])(span, exit, clock_, timingEnabled)), span);
    }));
};
const withTracerScoped = (value)=>fiberRefLocallyScopedWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$defaultServices$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentServices"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tracerTag"], value));
const withSpanScoped = function() {
    const dataFirst = typeof arguments[0] !== "string";
    const name = dataFirst ? arguments[1] : arguments[0];
    const options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addSpanStackTrace"])(dataFirst ? arguments[2] : arguments[1]);
    if (dataFirst) {
        const self = arguments[0];
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(makeSpanScoped(name, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addSpanStackTrace"])(options)), (span)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provideService"])(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["spanTag"], span));
    }
    return (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(makeSpanScoped(name, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addSpanStackTrace"])(options)), (span)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provideService"])(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["spanTag"], span));
}; //# sourceMappingURL=fiberRuntime.js.map
}}),

};

//# sourceMappingURL=node_modules_effect_dist_esm_internal_fiberRuntime_fe1f3193.js.map