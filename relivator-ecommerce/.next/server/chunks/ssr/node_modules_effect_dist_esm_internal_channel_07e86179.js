module.exports = {

"[project]/node_modules/effect/dist/esm/internal/channel.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChannelException": (()=>ChannelException),
    "ChannelExceptionTypeId": (()=>ChannelExceptionTypeId),
    "acquireUseRelease": (()=>acquireUseRelease),
    "as": (()=>as),
    "asVoid": (()=>asVoid),
    "buffer": (()=>buffer),
    "bufferChunk": (()=>bufferChunk),
    "catchAll": (()=>catchAll),
    "collect": (()=>collect),
    "concatMap": (()=>concatMap),
    "concatOut": (()=>concatOut),
    "context": (()=>context),
    "contextWith": (()=>contextWith),
    "contextWithChannel": (()=>contextWithChannel),
    "contextWithEffect": (()=>contextWithEffect),
    "doneCollect": (()=>doneCollect),
    "drain": (()=>drain),
    "emitCollect": (()=>emitCollect),
    "ensuring": (()=>ensuring),
    "flatten": (()=>flatten),
    "foldChannel": (()=>foldChannel),
    "fromEither": (()=>fromEither),
    "fromInput": (()=>fromInput),
    "fromOption": (()=>fromOption),
    "fromPubSub": (()=>fromPubSub),
    "fromPubSubScoped": (()=>fromPubSubScoped),
    "fromQueue": (()=>fromQueue),
    "identityChannel": (()=>identityChannel),
    "interruptWhen": (()=>interruptWhen),
    "interruptWhenDeferred": (()=>interruptWhenDeferred),
    "isChannelException": (()=>isChannelException),
    "map": (()=>map),
    "mapEffect": (()=>mapEffect),
    "mapError": (()=>mapError),
    "mapErrorCause": (()=>mapErrorCause),
    "mapInput": (()=>mapInput),
    "mapInputContext": (()=>mapInputContext),
    "mapInputEffect": (()=>mapInputEffect),
    "mapInputError": (()=>mapInputError),
    "mapInputErrorEffect": (()=>mapInputErrorEffect),
    "mapInputIn": (()=>mapInputIn),
    "mapInputInEffect": (()=>mapInputInEffect),
    "mapOut": (()=>mapOut),
    "mapOutEffect": (()=>mapOutEffect),
    "mapOutEffectPar": (()=>mapOutEffectPar),
    "mergeAll": (()=>mergeAll),
    "mergeAllUnbounded": (()=>mergeAllUnbounded),
    "mergeAllUnboundedWith": (()=>mergeAllUnboundedWith),
    "mergeAllWith": (()=>mergeAllWith),
    "mergeMap": (()=>mergeMap),
    "mergeOut": (()=>mergeOut),
    "mergeOutWith": (()=>mergeOutWith),
    "mergeWith": (()=>mergeWith),
    "never": (()=>never),
    "orDie": (()=>orDie),
    "orDieWith": (()=>orDieWith),
    "orElse": (()=>orElse),
    "pipeToOrFail": (()=>pipeToOrFail),
    "provideLayer": (()=>provideLayer),
    "provideService": (()=>provideService),
    "provideSomeLayer": (()=>provideSomeLayer),
    "read": (()=>read),
    "repeated": (()=>repeated),
    "run": (()=>run),
    "runCollect": (()=>runCollect),
    "runDrain": (()=>runDrain),
    "runScoped": (()=>runScoped),
    "scoped": (()=>scoped),
    "scopedWith": (()=>scopedWith),
    "service": (()=>service),
    "serviceWith": (()=>serviceWith),
    "serviceWithChannel": (()=>serviceWithChannel),
    "serviceWithEffect": (()=>serviceWithEffect),
    "splitLines": (()=>splitLines),
    "toPubSub": (()=>toPubSub),
    "toPull": (()=>toPull),
    "toPullIn": (()=>toPullIn),
    "toQueue": (()=>toQueue),
    "unwrap": (()=>unwrap),
    "unwrapScoped": (()=>unwrapScoped),
    "unwrapScopedWith": (()=>unwrapScopedWith),
    "updateService": (()=>updateService),
    "withSpan": (()=>withSpan),
    "writeAll": (()=>writeAll),
    "writeChunk": (()=>writeChunk),
    "zip": (()=>zip),
    "zipLeft": (()=>zipLeft),
    "zipRight": (()=>zipRight)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Cause.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Chunk.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Context.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Deferred.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Either.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Equal.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Exit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Fiber.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/FiberRef.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Layer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Layer.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Option.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$PubSub$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/PubSub.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Queue.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Ref.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Scope.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelExecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/channel/channelExecutor.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/channel/mergeDecision.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/channel/mergeState.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/channel/mergeStrategy.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$singleProducerAsyncInput$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/channel/singleProducerAsyncInput.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/core-effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/core-stream.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelMergeDecision.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelMergeState.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/channelState.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/tracer.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const acquireUseRelease = (acquire, use, release)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"])), (ref)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptible"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tap"])(acquire, (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(ref, (exit)=>release(a, exit))))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(use), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensuringWith"])((exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(ref), (f)=>f(exit)))));
const as = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>map(self, ()=>value));
const asVoid = (self)=>map(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constVoid"]);
const buffer = (options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
        const doBuffer = (empty, isEmpty, ref)=>unwrap((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["modify"])(ref, (inElem)=>isEmpty(inElem) ? [
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
                        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(input), ()=>doBuffer(empty, isEmpty, ref)),
                        onFailure: (error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(error),
                        onDone: (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(done)
                    }),
                    inElem
                ] : [
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(inElem), ()=>doBuffer(empty, isEmpty, ref)),
                    empty
                ]));
        return doBuffer(options.empty, options.isEmpty, options.ref);
    });
const bufferChunk = (ref)=>buffer({
        empty: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])(),
        isEmpty: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmpty"],
        ref
    });
const catchAll = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAllCause"])(self, (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failureOrCause"])(cause), {
            onLeft: f,
            onRight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"]
        })));
const concatMap = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["concatMapWith"])(self, f, ()=>void 0, ()=>void 0));
const collect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>{
    const collector = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (out)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(pf(out), {
                onNone: ()=>collector,
                onSome: (out2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(out2), ()=>collector)
            }),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(self, collector);
});
const concatOut = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["concatAll"])(self);
const mapInput = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const reader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (inElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(inElem), ()=>reader),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"])(f(done))
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(reader, self);
});
const mapInputEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const reader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (inElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(inElem), ()=>reader),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f(done))
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(reader, self);
});
const mapInputError = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const reader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (inElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(inElem), ()=>reader),
        onFailure: (error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(f(error)),
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(reader, self);
});
const mapInputErrorEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const reader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (inElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(inElem), ()=>reader),
        onFailure: (error)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f(error)),
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(reader, self);
});
const mapInputIn = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const reader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (inElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(f(inElem)), ()=>reader),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(reader, self);
});
const mapInputInEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const reader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (inElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f(inElem)), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"]), ()=>reader),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(reader, self);
});
const doneCollect = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
        const builder = [];
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(self, doneCollectReader(builder)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((outDone)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])([
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(builder),
                outDone
            ])));
    });
/** @internal */ const doneCollectReader = (builder)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (outElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>{
                builder.push(outElem);
            }), ()=>doneCollectReader(builder)),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
    });
};
const drain = (self)=>{
    const drainer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWithCause"])({
        onInput: ()=>drainer,
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(self, drainer);
};
const emitCollect = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(doneCollect(self), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"]);
const ensuring = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, finalizer)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensuringWith"])(self, ()=>finalizer));
const context = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["context"])());
const contextWith = (f)=>map(context(), f);
const contextWithChannel = (f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(context(), f);
const contextWithEffect = (f)=>mapEffect(context(), f);
const flatten = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]);
const foldChannel = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["foldCauseChannel"])(self, {
        onFailure: (cause)=>{
            const either = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failureOrCause"])(cause);
            switch(either._tag){
                case "Left":
                    {
                        return options.onFailure(either.left);
                    }
                case "Right":
                    {
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(either.right);
                    }
            }
        },
        onSuccess: options.onSuccess
    }));
const fromEither = (either)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(either, {
            onLeft: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
            onRight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
        }));
const fromInput = (input)=>unwrap(input.takeWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"], (elem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(elem), ()=>fromInput(input)), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]));
const fromPubSub = (pubsub)=>unwrapScoped((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$PubSub$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["subscribe"])(pubsub), fromQueue));
const fromPubSubScoped = (pubsub)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$PubSub$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["subscribe"])(pubsub), fromQueue);
const fromOption = (option)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(option, {
            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])()),
            onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
        }));
const fromQueue = (queue)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>fromQueueInternal(queue));
/** @internal */ const fromQueueInternal = (queue)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["take"])(queue)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
        onLeft: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
            onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
            onSuccess: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
        }),
        onRight: (elem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(elem), ()=>fromQueueInternal(queue))
    })));
const identityChannel = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (input)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(input), ()=>identityChannel()),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
    });
const interruptWhen = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, effect)=>mergeWith(self, {
        other: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(effect),
        onSelfDone: (selfDone)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>selfDone)),
        onOtherDone: (effectDone)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Done"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>effectDone))
    }));
const interruptWhenDeferred = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, deferred)=>interruptWhen(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(deferred)));
const map = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>f(a))));
const mapEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (z)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f(z))));
const mapError = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>mapErrorCause(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(f)));
const mapErrorCause = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAllCause"])(self, (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(f(cause))));
const mapOut = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const reader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
        onInput: (outElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(f(outElem)), ()=>reader),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(self, reader);
});
const mapOutEffect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const reader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWithCause"])({
        onInput: (outElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f(outElem)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>reader)),
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
        onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(self, reader);
});
const mapOutEffectPar = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, f, n)=>unwrapScopedWith((scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["gen"])(function*() {
            const input = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$singleProducerAsyncInput$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])();
            const queueReader = fromInput(input);
            const queue = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["bounded"])(n);
            yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addFinalizer"])(scope, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["shutdown"])(queue));
            const errorSignal = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])();
            const withPermits = n === Number.POSITIVE_INFINITY ? (_)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"] : (yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["makeSemaphore"])(n)).withPermits;
            const pull = yield* queueReader.pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(self), toPullIn(scope));
            yield* pull.pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])({
                onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(queue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause)),
                onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                    onLeft: (outDone)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(withPermits(n)(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"])), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["asVoid"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(queue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])(outDone))))),
                    onRight: (outElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["gen"])(function*() {
                            const deferred = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])();
                            const latch = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])();
                            yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(queue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(deferred), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"]));
                            yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(latch, void 0).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptibleMask"])((restore)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(restore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(errorSignal))).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["raceFirst"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(restore(f(outElem)))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]))).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tapErrorCause"])((cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(errorSignal, cause)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["intoDeferred"])(deferred))), withPermits(1), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forkIn"])(scope));
                            yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(latch);
                        })
                })
            }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forever"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forkIn"])(scope));
            const consumer = unwrap((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["take"])(queue)), {
                onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
                onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                    onLeft: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"],
                    onRight: (outElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(outElem), ()=>consumer)
                })
            }));
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["embedInput"])(consumer, input);
        })));
const mergeAll = (options)=>{
    return (channels)=>mergeAllWith(options)(channels, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constVoid"]);
};
const mergeAllUnbounded = (channels)=>mergeAllWith({
        concurrency: "unbounded"
    })(channels, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constVoid"]);
const mergeAllUnboundedWith = (channels, f)=>mergeAllWith({
        concurrency: "unbounded"
    })(channels, f);
const mergeAllWith = ({ bufferSize = 16, concurrency, mergeStrategy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BackPressure"])() })=>(channels, f)=>unwrapScopedWith((scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["gen"])(function*() {
                const concurrencyN = concurrency === "unbounded" ? Number.MAX_SAFE_INTEGER : concurrency;
                const input = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$singleProducerAsyncInput$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])();
                const queueReader = fromInput(input);
                const queue = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["bounded"])(bufferSize);
                yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addFinalizer"])(scope, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["shutdown"])(queue));
                const cancelers = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unbounded"])();
                yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addFinalizer"])(scope, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["shutdown"])(cancelers));
                const lastDone = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])());
                const errorSignal = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])();
                const withPermits = (yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["makeSemaphore"])(concurrencyN)).withPermits;
                const pull = yield* toPullIn((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(queueReader, channels), scope);
                function evaluatePull(pull) {
                    return pull.pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                        onLeft: (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(done)),
                        onRight: (outElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(queue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"])(outElem))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])())
                    })), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["repeat"])({
                        until: (_)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSome"])(_)
                    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((outDone)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["update"])(lastDone, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(outDone.value),
                            onSome: (lastDone)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(f(lastDone, outDone.value))
                        }))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAllCause"])((cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isInterrupted"])(cause) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(queue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause)).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(errorSignal, void 0)), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["asVoid"])));
                }
                yield* pull.pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])({
                    onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(queue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause)).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(false))),
                    onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                        onLeft: (outDone)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["raceWith"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(errorSignal)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(withPermits(concurrencyN)(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"])), {
                                onSelfDone: (_, permitAcquisition)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(permitAcquisition), false),
                                onOtherDone: (_, failureAwait)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(failureAwait), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Ref$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(lastDone).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                                        onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(queue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])(outDone))),
                                        onSome: (lastDone)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(queue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])(f(lastDone, outDone))))
                                    })), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(false)))
                            }),
                        onRight: (channel)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeStrategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(mergeStrategy, {
                                onBackPressure: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["gen"])(function*() {
                                        const latch = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])();
                                        const raceEffects = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopedWith"])((scope)=>toPullIn((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(queueReader, channel), scope).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((pull)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["race"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(evaluatePull(pull)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(errorSignal))))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"])));
                                        yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(latch, void 0).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(raceEffects), withPermits(1), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forkIn"])(scope));
                                        yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(latch);
                                        const errored = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isDone"])(errorSignal);
                                        return !errored;
                                    }),
                                onBufferSliding: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["gen"])(function*() {
                                        const canceler = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])();
                                        const latch = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])();
                                        const size = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["size"])(cancelers);
                                        yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["take"])(cancelers).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((canceler)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(canceler, void 0)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["when"])(()=>size >= concurrencyN));
                                        yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(cancelers, canceler);
                                        const raceEffects = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopedWith"])((scope)=>toPullIn((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(queueReader, channel), scope).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((pull)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(evaluatePull(pull)).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["race"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(errorSignal)))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["race"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(canceler)))))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"])));
                                        yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(latch, void 0).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(raceEffects), withPermits(1), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forkIn"])(scope));
                                        yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(latch);
                                        const errored = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Deferred$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isDone"])(errorSignal);
                                        return !errored;
                                    })
                            })
                    })
                }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["repeat"])({
                    while: (_)=>_
                }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forkIn"])(scope));
                const consumer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["take"])(queue), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCause"])({
                    onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
                    onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                        onLeft: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"],
                        onRight: (outElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(outElem), ()=>consumer)
                    })
                }), unwrap);
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["embedInput"])(consumer, input);
            }));
const mergeMap = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, f, options)=>mergeAll(options)(mapOut(self, f)));
const mergeOut = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, n)=>mergeAll({
        concurrency: n
    })(mapOut(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"])));
const mergeOutWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, n, f)=>mergeAllWith({
        concurrency: n
    })(mapOut(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]), f));
const mergeWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, options)=>{
    function merge(scope) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["gen"])(function*() {
            const input = yield* (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$singleProducerAsyncInput$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])();
            const queueReader = fromInput(input);
            const pullL = yield* toPullIn((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(queueReader, self), scope);
            const pullR = yield* toPullIn((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(queueReader, options.other), scope);
            function handleSide(exit, fiber, pull) {
                return (done, both, single)=>{
                    function onDecision(decision) {
                        const op = decision;
                        if (op._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"]) {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(fiber), op.effect)));
                        }
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["await"])(fiber), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                            onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(op.f((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause))),
                            onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                                onLeft: (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(op.f((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(done))),
                                onRight: (elem)=>zipRight((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(elem), go(single(op.f)))
                            })
                        }));
                    }
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(exit, {
                        onFailure: (cause)=>onDecision(done((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause))),
                        onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                            onLeft: (z)=>onDecision(done((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(z))),
                            onRight: (elem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(elem), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forkIn"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(pull), scope)), (leftFiber)=>go(both(leftFiber, fiber)))))
                        })
                    });
                };
            }
            function go(state) {
                switch(state._tag){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BOTH_RUNNING"]:
                        {
                            const leftJoin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["join"])(state.left));
                            const rightJoin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["join"])(state.right));
                            return unwrap((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["raceWith"])(leftJoin, rightJoin, {
                                onSelfDone: (leftExit, rf)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(rf), handleSide(leftExit, state.right, pullL)(options.onSelfDone, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BothRunning"], (f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LeftDone"])(f))),
                                onOtherDone: (rightExit, lf)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Fiber$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(lf), handleSide(rightExit, state.left, pullR)(options.onOtherDone, (left, right)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BothRunning"])(right, left), (f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RightDone"])(f)))
                            }));
                        }
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_LEFT_DONE"]:
                        {
                            return unwrap((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(pullR), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                                onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(state.f((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause))),
                                onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                                    onLeft: (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(state.f((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(done))),
                                    onRight: (elem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(elem), ()=>go((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LeftDone"])(state.f)))
                                })
                            })));
                        }
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelMergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RIGHT_DONE"]:
                        {
                            return unwrap((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["exit"])(pullL), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                                onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(state.f((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause))),
                                onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
                                    onLeft: (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(state.f((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(done))),
                                    onRight: (elem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(elem), ()=>go((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RightDone"])(state.f)))
                                })
                            })));
                        }
                }
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((parent)=>{
                const inherit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((state)=>{
                    ;
                    state.transferChildren(parent.scope());
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
                });
                const leftFiber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(pullL).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensuring"])(inherit), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forkIn"])(scope));
                const rightFiber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptible"])(pullR).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensuring"])(inherit), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forkIn"])(scope));
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipWith"])(leftFiber, rightFiber, (left, right)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BothRunning"])(left, right));
            })).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(go), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["embedInput"])(input));
        });
    }
    return unwrapScopedWith(merge);
});
const never = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["never"]);
const orDie = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, error)=>orDieWith(self, error));
const orDieWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>catchAll(self, (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCauseSync"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(f(e)))));
const orElse = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>catchAll(self, that));
const pipeToOrFail = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
        let channelException = undefined;
        const reader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWith"])({
            onInput: (outElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(outElem), ()=>reader),
            onFailure: (outErr)=>{
                channelException = ChannelException(outErr);
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(channelException));
            },
            onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
        });
        const writer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWithCause"])({
            onInput: (outElem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(outElem), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>writer)),
            onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isDieType"])(cause) && isChannelException(cause.defect) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"])(cause.defect, channelException) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(cause.defect.error) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause),
            onDone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeedNow"]
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeTo"])(self, reader), that), writer);
    }));
const provideService = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, tag, service)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(context(), (context)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provideContext"])(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(context, tag, service)));
});
const provideLayer = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, layer)=>unwrapScopedWith((scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Layer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildWithScope"])(layer, scope), (context)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provideContext"])(self, context))));
const mapInputContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>contextWithChannel((context)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provideContext"])(self, f(context))));
const provideSomeLayer = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, layer)=>// @ts-expect-error
    provideLayer(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Layer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["merge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Layer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["context"])(), layer)));
const read = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readOrFail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])());
const repeated = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, ()=>repeated(self));
const run = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopedWith"])((scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelExecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runIn"])(self, scope));
const runCollect = (self)=>run((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["collectElements"])(self));
const runDrain = (self)=>run(drain(self));
const runScoped = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scopeWith"])((scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelExecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runIn"])(self, scope));
const scoped = (effect)=>unwrap((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptibleMask"])((restore)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(), (scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["acquireReleaseOut"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tapErrorCause"])(restore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extend"])(effect, scope)), (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["close"])(scope, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause))), (_, exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["close"])(scope, exit)))));
const scopedWith = (f)=>unwrapScoped((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scope"], (scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(f(scope)), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])));
const service = (tag)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(tag);
const serviceWith = (tag)=>(f)=>map(service(tag), f);
const serviceWithChannel = (tag)=>(f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(service(tag), f);
const serviceWithEffect = (tag)=>(f)=>mapEffect(service(tag), f);
const splitLines = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
        let stringBuilder = "";
        let midCRLF = false;
        const splitLinesChunk = (chunk)=>{
            const chunkBuilder = [];
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(chunk, (str)=>{
                if (str.length !== 0) {
                    let from = 0;
                    let indexOfCR = str.indexOf("\r");
                    let indexOfLF = str.indexOf("\n");
                    if (midCRLF) {
                        if (indexOfLF === 0) {
                            chunkBuilder.push(stringBuilder);
                            stringBuilder = "";
                            from = 1;
                            indexOfLF = str.indexOf("\n", from);
                        } else {
                            stringBuilder = stringBuilder + "\r";
                        }
                        midCRLF = false;
                    }
                    while(indexOfCR !== -1 || indexOfLF !== -1){
                        if (indexOfCR === -1 || indexOfLF !== -1 && indexOfLF < indexOfCR) {
                            if (stringBuilder.length === 0) {
                                chunkBuilder.push(str.substring(from, indexOfLF));
                            } else {
                                chunkBuilder.push(stringBuilder + str.substring(from, indexOfLF));
                                stringBuilder = "";
                            }
                            from = indexOfLF + 1;
                            indexOfLF = str.indexOf("\n", from);
                        } else {
                            if (str.length === indexOfCR + 1) {
                                midCRLF = true;
                                indexOfCR = -1;
                            } else {
                                if (indexOfLF === indexOfCR + 1) {
                                    if (stringBuilder.length === 0) {
                                        chunkBuilder.push(str.substring(from, indexOfCR));
                                    } else {
                                        stringBuilder = stringBuilder + str.substring(from, indexOfCR);
                                        chunkBuilder.push(stringBuilder);
                                        stringBuilder = "";
                                    }
                                    from = indexOfCR + 2;
                                    indexOfCR = str.indexOf("\r", from);
                                    indexOfLF = str.indexOf("\n", from);
                                } else {
                                    indexOfCR = str.indexOf("\r", indexOfCR + 1);
                                }
                            }
                        }
                    }
                    if (midCRLF) {
                        stringBuilder = stringBuilder + str.substring(from, str.length - 1);
                    } else {
                        stringBuilder = stringBuilder + str.substring(from, str.length);
                    }
                }
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(chunkBuilder);
        };
        const loop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWithCause"])({
            onInput: (input)=>{
                const out = splitLinesChunk(input);
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmpty"])(out) ? loop : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])(out), ()=>loop);
            },
            onFailure: (cause)=>stringBuilder.length === 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["of"])(stringBuilder)), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause)),
            onDone: (done)=>stringBuilder.length === 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(done) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["of"])(stringBuilder)), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(done))
        });
        return loop;
    });
const toPubSub = (pubsub)=>toQueue(pubsub);
const toPull = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["scope"], (scope)=>toPullIn(self, scope));
const toPullIn = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, scope)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zip"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelExecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ChannelExecutor"](self, void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"])), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runtime"])()).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tap"])(([executor, runtime])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Scope$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addFinalizerExit"])(scope, (exit)=>{
            const finalizer = executor.close(exit);
            return finalizer !== undefined ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provide"])(finalizer, runtime) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
        })), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptible"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(([executor])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>interpretToPull(executor.run(), executor)))));
/** @internal */ const interpretToPull = (channelState, exec)=>{
    const state = channelState;
    switch(state._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"]:
            {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(exec.getDone(), {
                    onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"],
                    onSuccess: (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])(done))
                });
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_EMIT"]:
            {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"])(exec.getEmit()));
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FROM_EFFECT"]:
            {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(state.effect, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>interpretToPull(exec.run(), exec)));
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$channelState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_READ"]:
            {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$channelExecutor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readUpstream"])(state, ()=>interpretToPull(exec.run(), exec), (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause));
            }
    }
};
const toQueue = (queue)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>toQueueInternal(queue));
/** @internal */ const toQueueInternal = (queue)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["readWithCause"])({
        onInput: (elem)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(queue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"])(elem))), ()=>toQueueInternal(queue)),
        onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(queue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause))))),
        onDone: (done)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Queue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["offer"])(queue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(done)))))
    });
};
const unwrap = (channel)=>flatten((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromEffect"])(channel));
const unwrapScoped = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["concatAllWith"])(scoped(self), (d, _)=>d, (d, _)=>d);
const unwrapScopedWith = (f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["concatAllWith"])(scopedWith(f), (d, _)=>d, (d, _)=>d);
const updateService = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, tag, f)=>mapInputContext(self, (context)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["merge"])(context, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(tag, f((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(context, tag))))));
const withSpan = function() {
    const dataFirst = typeof arguments[0] !== "string";
    const name = dataFirst ? arguments[1] : arguments[0];
    const options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addSpanStackTrace"])(dataFirst ? arguments[2] : arguments[1]);
    const acquire = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["all"])([
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["makeSpan"])(name, options),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["context"])(),
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clock"],
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentTracerTimingEnabled"])
    ]);
    if (dataFirst) {
        const self = arguments[0];
        return acquireUseRelease(acquire, ([span, context])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provideContext"])(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(context, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["spanTag"], span)), ([span, , clock, timingEnabled], exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["endSpan"])(span, exit, clock, timingEnabled));
    }
    return (self)=>acquireUseRelease(acquire, ([span, context])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["provideContext"])(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(context, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$tracer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["spanTag"], span)), ([span, , clock, timingEnabled], exit)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["endSpan"])(span, exit, clock, timingEnabled));
};
const writeAll = (...outs)=>writeChunk((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(outs));
const writeChunk = (outs)=>writeChunkWriter(0, outs.length, outs);
/** @internal */ const writeChunkWriter = (idx, len, chunk)=>{
    return idx === len ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["write"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(chunk, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(idx))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>writeChunkWriter(idx + 1, len, chunk)));
};
const zip = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isChannel"])(args[1]), (self, that, options)=>options?.concurrent ? mergeWith(self, {
        other: that,
        onSelfDone: (exit1)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Await"])((exit2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zip"])(exit1, exit2))),
        onOtherDone: (exit2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$channel$2f$mergeDecision$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Await"])((exit1)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zip"])(exit1, exit2)))
    }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (a)=>map(that, (b)=>[
                a,
                b
            ])));
const zipLeft = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isChannel"])(args[1]), (self, that, options)=>options?.concurrent ? map(zip(self, that, {
        concurrent: true
    }), (tuple)=>tuple[0]) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (z)=>as(that, z)));
const zipRight = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isChannel"])(args[1]), (self, that, options)=>options?.concurrent ? map(zip(self, that, {
        concurrent: true
    }), (tuple)=>tuple[1]) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, ()=>that));
const ChannelExceptionTypeId = /*#__PURE__*/ Symbol.for("effect/Channel/ChannelException");
const ChannelException = (error)=>({
        _tag: "ChannelException",
        [ChannelExceptionTypeId]: ChannelExceptionTypeId,
        error
    });
const isChannelException = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, ChannelExceptionTypeId); //# sourceMappingURL=channel.js.map
}}),

};

//# sourceMappingURL=node_modules_effect_dist_esm_internal_channel_07e86179.js.map