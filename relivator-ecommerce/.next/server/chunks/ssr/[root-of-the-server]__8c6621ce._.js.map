{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_89759f3d.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_89759f3d-module__7rcAjq__className\",\n  \"variable\": \"geist_89759f3d-module__7rcAjq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_89759f3d.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-geist-sans%22}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_1bed3778.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_1bed3778-module__Mp-kKq__className\",\n  \"variable\": \"geist_mono_1bed3778-module__Mp-kKq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_1bed3778.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-geist-mono%22}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/app.ts"], "sourcesContent": ["export const SEO_CONFIG = {\n  description:\n    \"Relivator is a robust ecommerce template built with next.js and other modern technologies. It's designed for developers who want a fast, modern, and scalable foundation without reinventing the backend.\",\n  fullName: \"Relivator Next.js Template\",\n  name: \"Relivator\",\n  slogan: \"Store which makes you happy.\",\n};\n\nexport const SYSTEM_CONFIG = {\n  redirectAfterSignIn: \"/dashboard/uploads\",\n  redirectAfterSignUp: \"/dashboard/uploads\",\n  repoName: \"relivator\",\n  repoOwner: \"blefnk\",\n  repoStars: true,\n};\n\nexport const ADMIN_CONFIG = {\n  displayEmails: false,\n};\n\nexport const DB_DEV_LOGGER = false;\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,aAAa;IACxB,aACE;IACF,UAAU;IACV,MAAM;IACN,QAAQ;AACV;AAEO,MAAM,gBAAgB;IAC3B,qBAAqB;IACrB,qBAAqB;IACrB,UAAU;IACV,WAAW;IACX,WAAW;AACb;AAEO,MAAM,eAAe;IAC1B,eAAe;AACjB;AAEO,MAAM,gBAAgB", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/users/tables.ts"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED - DO NOT EDIT DIRECTLY\n *\n * To modify the schema, edit src/lib/auth.ts instead,\n * then run 'bun db:auth' to regenerate this file.\n *\n * Any direct changes to this file will be overwritten.\n */\n\nimport {\n  boolean,\n  integer,\n  pgTable,\n  text,\n  timestamp,\n} from \"drizzle-orm/pg-core\";\n\nexport const userTable = pgTable(\"user\", {\n  age: integer(\"age\"),\n  createdAt: timestamp(\"created_at\").notNull(),\n  email: text(\"email\").notNull().unique(),\n  emailVerified: boolean(\"email_verified\").notNull(),\n  firstName: text(\"first_name\"),\n  id: text(\"id\").primaryKey(),\n  image: text(\"image\"),\n  lastName: text(\"last_name\"),\n  name: text(\"name\").notNull(),\n  twoFactorEnabled: boolean(\"two_factor_enabled\"),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n});\n\nexport const sessionTable = pgTable(\"session\", {\n  createdAt: timestamp(\"created_at\").notNull(),\n  expiresAt: timestamp(\"expires_at\").notNull(),\n  id: text(\"id\").primaryKey(),\n  ipAddress: text(\"ip_address\"),\n  token: text(\"token\").notNull().unique(),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n  userAgent: text(\"user_agent\"),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n\nexport const accountTable = pgTable(\"account\", {\n  accessToken: text(\"access_token\"),\n  accessTokenExpiresAt: timestamp(\"access_token_expires_at\"),\n  accountId: text(\"account_id\").notNull(),\n  createdAt: timestamp(\"created_at\").notNull(),\n  id: text(\"id\").primaryKey(),\n  idToken: text(\"id_token\"),\n  password: text(\"password\"),\n  providerId: text(\"provider_id\").notNull(),\n  refreshToken: text(\"refresh_token\"),\n  refreshTokenExpiresAt: timestamp(\"refresh_token_expires_at\"),\n  scope: text(\"scope\"),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n\nexport const verificationTable = pgTable(\"verification\", {\n  createdAt: timestamp(\"created_at\"),\n  expiresAt: timestamp(\"expires_at\").notNull(),\n  id: text(\"id\").primaryKey(),\n  identifier: text(\"identifier\").notNull(),\n  updatedAt: timestamp(\"updated_at\"),\n  value: text(\"value\").notNull(),\n});\n\nexport const twoFactorTable = pgTable(\"two_factor\", {\n  backupCodes: text(\"backup_codes\").notNull(),\n  id: text(\"id\").primaryKey(),\n  secret: text(\"secret\").notNull(),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;AAED;AAAA;AAAA;AAAA;AAAA;;AAQO,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IACvC,KAAK,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;IACb,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,OAAO,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO;IAChD,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IAChB,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,OAAO,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IACZ,UAAU,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IACf,MAAM,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,kBAAkB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;IAC1B,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;AAC5C;AAEO,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IAC7C,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IAChB,OAAO,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IAChB,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,UAAU,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D;AAEO,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IAC7C,aAAa,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IAClB,sBAAsB,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE;IAChC,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACrC,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,SAAS,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IACd,UAAU,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IACf,YAAY,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACvC,cAAc,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IACnB,uBAAuB,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE;IACjC,OAAO,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE;IACZ,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,UAAU,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D;AAEO,MAAM,oBAAoB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;IACvD,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE;IACrB,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,YAAY,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE;IACrB,OAAO,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;AAC9B;AAEO,MAAM,iBAAiB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IAClD,aAAa,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,OAAO;IACzC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,UAAU,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/uploads/tables.ts"], "sourcesContent": ["import { pgEnum, pgTable, text, timestamp } from \"drizzle-orm/pg-core\";\n\nimport { userTable } from \"../users/tables\";\n\nexport const mediaTypeEnum = pgEnum(\"type\", [\"image\", \"video\"]);\n\nexport const uploadsTable = pgTable(\"uploads\", {\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n  id: text(\"id\").primaryKey(),\n  key: text(\"key\").notNull(), // UploadThing file key\n  type: mediaTypeEnum(\"type\").notNull(),\n  updatedAt: timestamp(\"updated_at\").defaultNow().notNull(),\n  url: text(\"url\").notNull(), // UploadThing file URL\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAEA;;;AAEO,MAAM,gBAAgB,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IAAC;IAAS;CAAQ;AAEvD,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IAC7C,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,KAAK,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,OAAO,OAAO;IACxB,MAAM,cAAc,QAAQ,OAAO;IACnC,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,KAAK,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,OAAO,OAAO;IACxB,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,sIAAA,CAAA,YAAS,CAAC,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/uploads/relations.ts"], "sourcesContent": ["import { relations } from \"drizzle-orm\";\n\nimport { userTable } from \"../users/tables\";\nimport { uploadsTable } from \"./tables\";\n\nexport const uploadsRelations = relations(uploadsTable, ({ one }) => ({\n  user: one(userTable, {\n    fields: [uploadsTable.userId],\n    references: [userTable.id],\n  }),\n}));\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;AAEO,MAAM,mBAAmB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,wIAAA,CAAA,eAAY,EAAE,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACpE,MAAM,IAAI,sIAAA,CAAA,YAAS,EAAE;YACnB,QAAQ;gBAAC,wIAAA,CAAA,eAAY,CAAC,MAAM;aAAC;YAC7B,YAAY;gBAAC,sIAAA,CAAA,YAAS,CAAC,EAAE;aAAC;QAC5B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/uploads/types.ts"], "sourcesContent": ["import type { uploadsTable } from \"./tables\";\n\nexport type MediaUpload = typeof uploadsTable.$inferSelect;\n// export type NewMediaUpload = typeof uploadsTable.$inferInsert;\n"], "names": [], "mappings": ";;CAGA,iEAAiE", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/payments/tables.ts"], "sourcesContent": ["import { pgTable, text, timestamp } from \"drizzle-orm/pg-core\";\n\nimport { userTable } from \"../users/tables\";\n\nexport const polarCustomerTable = pgTable(\"polar_customer\", {\n  createdAt: timestamp(\"created_at\").notNull(),\n  customerId: text(\"customer_id\").notNull().unique(),\n  id: text(\"id\").primaryKey(),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n\nexport const polarSubscriptionTable = pgTable(\"polar_subscription\", {\n  createdAt: timestamp(\"created_at\").notNull(),\n  customerId: text(\"customer_id\").notNull(),\n  id: text(\"id\").primaryKey(),\n  productId: text(\"product_id\").notNull(),\n  status: text(\"status\").notNull(),\n  subscriptionId: text(\"subscription_id\").notNull().unique(),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAEA;;;AAEO,MAAM,qBAAqB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;IAC1D,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,YAAY,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO,GAAG,MAAM;IAChD,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,sIAAA,CAAA,YAAS,CAAC,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D;AAEO,MAAM,yBAAyB,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB;IAClE,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,YAAY,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACvC,IAAI,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACrC,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,gBAAgB,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,mBAAmB,OAAO,GAAG,MAAM;IACxD,WAAW,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,QAAQ,CAAA,GAAA,+JAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,sIAAA,CAAA,YAAS,CAAC,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/payments/relations.ts"], "sourcesContent": ["import { relations } from \"drizzle-orm\";\n\nimport { userTable } from \"../users/tables\";\nimport { polarCustomerTable, polarSubscriptionTable } from \"./tables\";\n\nexport const polarCustomerRelations = relations(polarCustomerTable, ({ one }) => ({\n  user: one(userTable, {\n    fields: [polarCustomerTable.userId],\n    references: [userTable.id],\n  }),\n}));\n\nexport const polarSubscriptionRelations = relations(polarSubscriptionTable, ({ one }) => ({\n  user: one(userTable, {\n    fields: [polarSubscriptionTable.userId],\n    references: [userTable.id],\n  }),\n}));\n\nexport const extendUserRelations = relations(userTable, ({ many }) => ({\n  polarCustomers: many(polarCustomerTable),\n  polarSubscriptions: many(polarSubscriptionTable),\n}));\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;;;;AAEO,MAAM,yBAAyB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,yIAAA,CAAA,qBAAkB,EAAE,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAChF,MAAM,IAAI,sIAAA,CAAA,YAAS,EAAE;YACnB,QAAQ;gBAAC,yIAAA,CAAA,qBAAkB,CAAC,MAAM;aAAC;YACnC,YAAY;gBAAC,sIAAA,CAAA,YAAS,CAAC,EAAE;aAAC;QAC5B;IACF,CAAC;AAEM,MAAM,6BAA6B,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,yIAAA,CAAA,yBAAsB,EAAE,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACxF,MAAM,IAAI,sIAAA,CAAA,YAAS,EAAE;YACnB,QAAQ;gBAAC,yIAAA,CAAA,yBAAsB,CAAC,MAAM;aAAC;YACvC,YAAY;gBAAC,sIAAA,CAAA,YAAS,CAAC,EAAE;aAAC;QAC5B;IACF,CAAC;AAEM,MAAM,sBAAsB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,sIAAA,CAAA,YAAS,EAAE,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QACrE,gBAAgB,KAAK,yIAAA,CAAA,qBAAkB;QACvC,oBAAoB,KAAK,yIAAA,CAAA,yBAAsB;IACjD,CAAC", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/users/relations.ts"], "sourcesContent": ["import { relations } from \"drizzle-orm\";\n\nimport { uploadsTable } from \"../uploads/tables\";\nimport { accountTable, sessionTable, userTable } from \"./tables\";\n\nexport const userRelations = relations(userTable, ({ many }) => ({\n  accounts: many(accountTable),\n  sessions: many(sessionTable),\n  uploads: many(uploadsTable),\n}));\n\nexport const sessionRelations = relations(sessionTable, ({ one }) => ({\n  user: one(userTable, {\n    fields: [sessionTable.userId],\n    references: [userTable.id],\n  }),\n}));\n\nexport const accountRelations = relations(accountTable, ({ one }) => ({\n  user: one(userTable, {\n    fields: [accountTable.userId],\n    references: [userTable.id],\n  }),\n}));\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;;;;AAEO,MAAM,gBAAgB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,sIAAA,CAAA,YAAS,EAAE,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QAC/D,UAAU,KAAK,sIAAA,CAAA,eAAY;QAC3B,UAAU,KAAK,sIAAA,CAAA,eAAY;QAC3B,SAAS,KAAK,wIAAA,CAAA,eAAY;IAC5B,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,sIAAA,CAAA,eAAY,EAAE,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACpE,MAAM,IAAI,sIAAA,CAAA,YAAS,EAAE;YACnB,QAAQ;gBAAC,sIAAA,CAAA,eAAY,CAAC,MAAM;aAAC;YAC7B,YAAY;gBAAC,sIAAA,CAAA,YAAS,CAAC,EAAE;aAAC;QAC5B;IACF,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,sIAAA,CAAA,eAAY,EAAE,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACpE,MAAM,IAAI,sIAAA,CAAA,YAAS,EAAE;YACnB,QAAQ;gBAAC,sIAAA,CAAA,eAAY,CAAC,MAAM;aAAC;YAC7B,YAAY;gBAAC,sIAAA,CAAA,YAAS,CAAC,EAAE;aAAC;QAC5B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/index.ts"], "sourcesContent": ["export * from \"./uploads/relations\";\nexport * from \"./uploads/tables\";\nexport * from \"./uploads/types\";\n\nexport * from \"./payments/relations\";\nexport * from \"./payments/tables\";\nexport * from \"./payments/types\";\n\n// relations\nexport * from \"./users/relations\";\n\n// schema\nexport * from \"./users/tables\";\n// types\nexport * from \"./users/types\";\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AAEA;AACA;AACA;AAEA,YAAY;AACZ;AAEA,SAAS;AACT;AACA,QAAQ;AACR", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/index.ts"], "sourcesContent": ["import \"dotenv/config\";\nimport { drizzle } from \"drizzle-orm/postgres-js\";\nimport postgres from \"postgres\";\n\nimport { DB_DEV_LOGGER } from \"~/app\";\n\nimport * as schema from \"./schema\";\n\n// Ensure the database URL is set\nif (!process.env.DATABASE_URL) {\n  throw new Error(\"🔴 DATABASE_URL environment variable is not set\");\n}\n\n/**\n * Caches the database connection in development to\n * prevent creating a new connection on every HMR update.\n */\ntype DbConnection = ReturnType<typeof postgres>;\nconst globalForDb = globalThis as unknown as {\n  conn?: DbConnection;\n};\nexport const conn: DbConnection =\n  globalForDb.conn ?? postgres(process.env.DATABASE_URL);\nif (process.env.NODE_ENV !== \"production\") {\n  globalForDb.conn = conn;\n}\n\n// Database connection instance\nexport const db = drizzle(conn, {\n  logger: DB_DEV_LOGGER && process.env.NODE_ENV !== \"production\",\n  schema,\n});\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AAEA;AAAA;;;;;;AAEA,iCAAiC;AACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,YAAY,EAAE;IAC7B,MAAM,IAAI,MAAM;AAClB;AAOA,MAAM,cAAc;AAGb,MAAM,OACX,YAAY,IAAI,IAAI,CAAA,GAAA,wIAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,GAAG,CAAC,YAAY;AACvD,wCAA2C;IACzC,YAAY,IAAI,GAAG;AACrB;AAGO,MAAM,KAAK,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAE,MAAM;IAC9B,QAAQ,0GAAA,CAAA,gBAAa,IAAI,oDAAyB;IAClD,QAAA;AACF", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/lib/auth.ts"], "sourcesContent": ["// note: run `bun db:auth` to generate the `users.ts`\n// schema after making breaking changes to this file\n\nimport { betterAuth } from \"better-auth\";\nimport { drizzleAdapter } from \"better-auth/adapters/drizzle\";\nimport { twoFactor } from \"better-auth/plugins\";\nimport { polar } from \"@polar-sh/better-auth\";\nimport { Polar } from \"@polar-sh/sdk\";\nimport { headers } from \"next/headers\";\nimport { redirect } from \"next/navigation\";\n\nimport type { UserDbType } from \"~/lib/auth-types\";\n\nimport { SYSTEM_CONFIG } from \"~/app\";\nimport { db } from \"~/db\";\nimport {\n  accountTable,\n  sessionTable,\n  twoFactorTable,\n  userTable,\n  verificationTable,\n} from \"~/db/schema\";\n\ninterface GitHubProfile {\n  [key: string]: unknown;\n  email?: string;\n  name?: string;\n}\n\ninterface GoogleProfile {\n  [key: string]: unknown;\n  email?: string;\n  family_name?: string;\n  given_name?: string;\n}\n\ninterface SocialProviderConfig {\n  [key: string]: unknown;\n  clientId: string;\n  clientSecret: string;\n  mapProfileToUser: (\n    profile: GitHubProfile | GoogleProfile,\n  ) => Record<string, unknown>;\n  redirectURI?: string;\n  scope: string[];\n}\n\nconst hasGithubCredentials =\n  process.env.AUTH_GITHUB_ID &&\n  process.env.AUTH_GITHUB_SECRET &&\n  process.env.AUTH_GITHUB_ID.length > 0 &&\n  process.env.AUTH_GITHUB_SECRET.length > 0;\n\nconst hasGoogleCredentials =\n  process.env.AUTH_GOOGLE_ID &&\n  process.env.AUTH_GOOGLE_SECRET &&\n  process.env.AUTH_GOOGLE_ID.length > 0 &&\n  process.env.AUTH_GOOGLE_SECRET.length > 0;\n\n// Build social providers configuration\nconst socialProviders: Record<string, SocialProviderConfig> = {};\n\nif (hasGithubCredentials) {\n  socialProviders.github = {\n    clientId: process.env.AUTH_GITHUB_ID ?? \"\",\n    clientSecret: process.env.AUTH_GITHUB_SECRET ?? \"\",\n    mapProfileToUser: (profile: GitHubProfile) => {\n      let firstName = \"\";\n      let lastName = \"\";\n      if (profile.name) {\n        const nameParts = profile.name.split(\" \");\n        firstName = nameParts[0];\n        lastName = nameParts.length > 1 ? nameParts.slice(1).join(\" \") : \"\";\n      }\n      return {\n        age: null,\n        firstName,\n        lastName,\n      };\n    },\n    scope: [\"user:email\", \"read:user\"],\n  };\n}\n\nif (hasGoogleCredentials) {\n  socialProviders.google = {\n    clientId: process.env.AUTH_GOOGLE_ID ?? \"\",\n    clientSecret: process.env.AUTH_GOOGLE_SECRET ?? \"\",\n    mapProfileToUser: (profile: GoogleProfile) => {\n      return {\n        age: null,\n        firstName: profile.given_name ?? \"\",\n        lastName: profile.family_name ?? \"\",\n      };\n    },\n    scope: [\"openid\", \"email\", \"profile\"],\n  };\n}\n\nconst polarClient = new Polar({\n  accessToken: process.env.POLAR_ACCESS_TOKEN,\n  server: (process.env.POLAR_ENVIRONMENT as \"production\" | \"sandbox\") || \"production\",\n});\n\nexport const auth = betterAuth({\n  account: {\n    accountLinking: {\n      allowDifferentEmails: false,\n      enabled: true,\n      trustedProviders: Object.keys(socialProviders),\n    },\n  },\n  baseURL: process.env.NEXT_SERVER_APP_URL,\n\n  database: drizzleAdapter(db, {\n    provider: \"pg\",\n    schema: {\n      account: accountTable,\n      session: sessionTable,\n      twoFactor: twoFactorTable,\n      user: userTable,\n      verification: verificationTable,\n    },\n  }),\n\n  emailAndPassword: {\n    enabled: true,\n  },\n\n  // Configure OAuth behavior\n  oauth: {\n    // Default redirect URL after successful login\n    defaultCallbackUrl: SYSTEM_CONFIG.redirectAfterSignIn,\n    // URL to redirect to on error\n    errorCallbackUrl: \"/auth/error\",\n    // Whether to link accounts with the same email\n    linkAccountsByEmail: true,\n  },\n\n  plugins: [\n    twoFactor(),\n    polar({\n      client: polarClient,\n      createCustomerOnSignUp: true,\n      enableCustomerPortal: true,\n      // Configure checkout\n      checkout: {\n        enabled: true,\n        products: [\n          {\n            productId: \"pro-plan\", // Replace with actual product ID from Polar Dashboard\n            slug: \"pro\" // Custom slug for easy reference in Checkout URL\n          },\n          {\n            productId: \"premium-plan\", // Replace with actual product ID from Polar Dashboard\n            slug: \"premium\" // Custom slug for easy reference in Checkout URL\n          }\n        ],\n        successUrl: \"/dashboard/billing?checkout_success=true&checkout_id={CHECKOUT_ID}\",\n      },\n      // Configure webhooks\n      webhooks: {\n        secret: process.env.POLAR_WEBHOOK_SECRET || \"\",\n        onPayload: async (payload) => {\n          console.log(\"Received webhook payload:\", payload.type);\n        },\n      },\n    }),\n  ],\n\n  secret: process.env.AUTH_SECRET,\n\n  // Only include social providers if credentials are available\n  socialProviders,\n\n  user: {\n    additionalFields: {\n      age: {\n        input: true,\n        required: false,\n        type: \"number\",\n      },\n      firstName: {\n        input: true,\n        required: false,\n        type: \"string\",\n      },\n      lastName: {\n        input: true,\n        required: false,\n        type: \"string\",\n      },\n    },\n  },\n});\n\nexport const getCurrentUser = async (): Promise<null | UserDbType> => {\n  const session = await auth.api.getSession({\n    headers: await headers(),\n  });\n  if (!session) {\n    return null;\n  }\n  return session.user as UserDbType;\n};\n\nexport const getCurrentUserOrRedirect = async (\n  forbiddenUrl = \"/auth/sign-in\",\n  okUrl = \"\",\n  ignoreForbidden = false,\n): Promise<null | UserDbType> => {\n  const user = await getCurrentUser();\n\n  // if no user is found\n  if (!user) {\n    // redirect to forbidden url unless explicitly ignored\n    if (!ignoreForbidden) {\n      redirect(forbiddenUrl);\n    }\n    // if ignoring forbidden, return the null user immediately\n    // (don't proceed to okUrl check)\n    return user; // user is null here\n  }\n\n  // if user is found and an okUrl is provided, redirect there\n  if (okUrl) {\n    redirect(okUrl);\n  }\n\n  // if user is found and no okUrl is provided, return the user\n  return user; // user is UserDbType here\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD,oDAAoD;;;;;;AAEpD;AAAA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAIA;AACA;AACA;AAAA;;;;;;;;;;;AAgCA,MAAM,uBACJ,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,kBAAkB,IAC9B,QAAQ,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,KACpC,QAAQ,GAAG,CAAC,kBAAkB,CAAC,MAAM,GAAG;AAE1C,MAAM,uBACJ,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,kBAAkB,IAC9B,QAAQ,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,KACpC,QAAQ,GAAG,CAAC,kBAAkB,CAAC,MAAM,GAAG;AAE1C,uCAAuC;AACvC,MAAM,kBAAwD,CAAC;AAE/D,IAAI,sBAAsB;IACxB,gBAAgB,MAAM,GAAG;QACvB,UAAU,QAAQ,GAAG,CAAC,cAAc,IAAI;QACxC,cAAc,QAAQ,GAAG,CAAC,kBAAkB,IAAI;QAChD,kBAAkB,CAAC;YACjB,IAAI,YAAY;YAChB,IAAI,WAAW;YACf,IAAI,QAAQ,IAAI,EAAE;gBAChB,MAAM,YAAY,QAAQ,IAAI,CAAC,KAAK,CAAC;gBACrC,YAAY,SAAS,CAAC,EAAE;gBACxB,WAAW,UAAU,MAAM,GAAG,IAAI,UAAU,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO;YACnE;YACA,OAAO;gBACL,KAAK;gBACL;gBACA;YACF;QACF;QACA,OAAO;YAAC;YAAc;SAAY;IACpC;AACF;AAEA,IAAI,sBAAsB;IACxB,gBAAgB,MAAM,GAAG;QACvB,UAAU,QAAQ,GAAG,CAAC,cAAc,IAAI;QACxC,cAAc,QAAQ,GAAG,CAAC,kBAAkB,IAAI;QAChD,kBAAkB,CAAC;YACjB,OAAO;gBACL,KAAK;gBACL,WAAW,QAAQ,UAAU,IAAI;gBACjC,UAAU,QAAQ,WAAW,IAAI;YACnC;QACF;QACA,OAAO;YAAC;YAAU;YAAS;SAAU;IACvC;AACF;AAEA,MAAM,cAAc,IAAI,iKAAA,CAAA,QAAK,CAAC;IAC5B,aAAa,QAAQ,GAAG,CAAC,kBAAkB;IAC3C,QAAQ,AAAC,QAAQ,GAAG,CAAC,iBAAiB,IAAiC;AACzE;AAEO,MAAM,OAAO,CAAA,GAAA,gKAAA,CAAA,aAAU,AAAD,EAAE;IAC7B,SAAS;QACP,gBAAgB;YACd,sBAAsB;YACtB,SAAS;YACT,kBAAkB,OAAO,IAAI,CAAC;QAChC;IACF;IACA,SAAS,QAAQ,GAAG,CAAC,mBAAmB;IAExC,UAAU,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE,kHAAA,CAAA,KAAE,EAAE;QAC3B,UAAU;QACV,QAAQ;YACN,SAAS,sIAAA,CAAA,eAAY;YACrB,SAAS,sIAAA,CAAA,eAAY;YACrB,WAAW,sIAAA,CAAA,iBAAc;YACzB,MAAM,sIAAA,CAAA,YAAS;YACf,cAAc,sIAAA,CAAA,oBAAiB;QACjC;IACF;IAEA,kBAAkB;QAChB,SAAS;IACX;IAEA,2BAA2B;IAC3B,OAAO;QACL,8CAA8C;QAC9C,oBAAoB,0GAAA,CAAA,gBAAa,CAAC,mBAAmB;QACrD,8BAA8B;QAC9B,kBAAkB;QAClB,+CAA+C;QAC/C,qBAAqB;IACvB;IAEA,SAAS;QACP,CAAA,GAAA,4LAAA,CAAA,YAAS,AAAD;QACR,CAAA,GAAA,gKAAA,CAAA,QAAK,AAAD,EAAE;YACJ,QAAQ;YACR,wBAAwB;YACxB,sBAAsB;YACtB,qBAAqB;YACrB,UAAU;gBACR,SAAS;gBACT,UAAU;oBACR;wBACE,WAAW;wBACX,MAAM,MAAM,iDAAiD;oBAC/D;oBACA;wBACE,WAAW;wBACX,MAAM,UAAU,iDAAiD;oBACnE;iBACD;gBACD,YAAY;YACd;YACA,qBAAqB;YACrB,UAAU;gBACR,QAAQ,QAAQ,GAAG,CAAC,oBAAoB,IAAI;gBAC5C,WAAW,OAAO;oBAChB,QAAQ,GAAG,CAAC,6BAA6B,QAAQ,IAAI;gBACvD;YACF;QACF;KACD;IAED,QAAQ,QAAQ,GAAG,CAAC,WAAW;IAE/B,6DAA6D;IAC7D;IAEA,MAAM;QACJ,kBAAkB;YAChB,KAAK;gBACH,OAAO;gBACP,UAAU;gBACV,MAAM;YACR;YACA,WAAW;gBACT,OAAO;gBACP,UAAU;gBACV,MAAM;YACR;YACA,UAAU;gBACR,OAAO;gBACP,UAAU;gBACV,MAAM;YACR;QACF;IACF;AACF;AAEO,MAAM,iBAAiB;IAC5B,MAAM,UAAU,MAAM,KAAK,GAAG,CAAC,UAAU,CAAC;QACxC,SAAS,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB;IACA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IACA,OAAO,QAAQ,IAAI;AACrB;AAEO,MAAM,2BAA2B,OACtC,eAAe,eAAe,EAC9B,QAAQ,EAAE,EACV,kBAAkB,KAAK;IAEvB,MAAM,OAAO,MAAM;IAEnB,sBAAsB;IACtB,IAAI,CAAC,MAAM;QACT,sDAAsD;QACtD,IAAI,CAAC,iBAAiB;YACpB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;QACX;QACA,0DAA0D;QAC1D,iCAAiC;QACjC,OAAO,MAAM,oBAAoB;IACnC;IAEA,4DAA4D;IAC5D,IAAI,OAAO;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,6DAA6D;IAC7D,OAAO,MAAM,0BAA0B;AACzC", "debugId": null}}, {"offset": {"line": 837, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/app/api/uploadthing/core.ts"], "sourcesContent": ["import { createId } from \"@paralleldrive/cuid2\";\nimport { createUploadthing, type FileRouter } from \"uploadthing/next\";\nimport { UploadThingError } from \"uploadthing/server\";\n\nimport { db } from \"~/db\";\nimport { uploadsTable } from \"~/db/schema\";\nimport { auth } from \"~/lib/auth\";\n\nconst f = createUploadthing();\n// FileRouter for the app, can contain multiple FileRoutes\nexport const ourFileRouter = {\n  // Define as many FileRoutes as you like, each with a unique routeSlug\n  imageUploader: f({\n    image: {\n      // Allow multiple images for a gallery\n      maxFileCount: 10,\n      /**\n       * For full list of options and defaults, see the File Route API reference\n       * @see https://docs.uploadthing.com/file-routes#route-config\n       */\n      maxFileSize: \"4MB\",\n    },\n  })\n    // Set permissions and file types for this FileRoute\n    .middleware(async ({ req }) => {\n      // This code runs on your server before upload\n      // Get the user session using auth.api.getSession\n      const session = await auth.api.getSession({ headers: req.headers });\n\n      // If you throw, the user will not be able to upload\n      if (!session?.user?.id) throw new UploadThingError(\"Unauthorized\");\n\n      // Whatever is returned here is accessible in onUploadComplete as `metadata`\n      // Ensure userId is correctly passed\n      return { userId: session.user.id };\n    })\n    .onUploadComplete(async ({ file, metadata }) => {\n      // This code RUNS ON THE SERVER after upload\n      console.log(\"Upload complete for userId (image):\", metadata.userId);\n      console.log(\"file url\", file.ufsUrl); // Public CDN URL is useful info\n      console.log(\"file key\", file.key);\n\n      // Save the upload details to the database\n      try {\n        await db.insert(uploadsTable).values({\n          id: createId(),\n          key: file.key,\n          type: \"image\",\n          url: file.ufsUrl, // Store the public CDN URL\n          userId: metadata.userId,\n        });\n        console.log(\n          \"Saved image upload details to database for userId:\",\n          metadata.userId,\n        );\n      } catch (error) {\n        console.error(\n          \"Failed to save image upload details to database:\",\n          error,\n        );\n        // Optionally, you might want to delete the file from UploadThing if DB insert fails\n        // await utapi.deleteFiles(file.key);\n        throw new UploadThingError(\"Failed to process upload metadata.\");\n      }\n\n      // !!! Whatever is returned here is sent to the clientside `onClientUploadComplete` callback\n      // Return necessary info, like the file URL or key, if needed on the client\n      return {\n        fileKey: file.key,\n        fileUrl: file.ufsUrl,\n        uploadedBy: metadata.userId,\n      };\n    }),\n\n  // New route for video uploads\n  videoUploader: f({\n    video: { maxFileCount: 5, maxFileSize: \"64MB\" },\n  })\n    .middleware(async ({ req }) => {\n      // Same middleware logic as imageUploader\n      const session = await auth.api.getSession({ headers: req.headers });\n      if (!session?.user?.id) throw new UploadThingError(\"Unauthorized\");\n      return { userId: session.user.id };\n    })\n    .onUploadComplete(async ({ file, metadata }) => {\n      console.log(\"Upload complete for userId (video):\", metadata.userId);\n      console.log(\"file url\", file.ufsUrl); // Public CDN URL is useful info\n      console.log(\"file key\", file.key);\n\n      // Save the upload details to the database\n      try {\n        await db.insert(uploadsTable).values({\n          id: createId(),\n          key: file.key,\n          type: \"video\", // Explicitly set type to video\n          url: file.ufsUrl,\n          userId: metadata.userId,\n        });\n        console.log(\n          \"Saved video upload details to database for userId:\",\n          metadata.userId,\n        );\n      } catch (error) {\n        console.error(\n          \"Failed to save video upload details to database:\",\n          error,\n        );\n        throw new UploadThingError(\"Failed to process upload metadata.\");\n      }\n\n      return {\n        fileKey: file.key,\n        fileUrl: file.ufsUrl,\n        uploadedBy: metadata.userId,\n      };\n    }),\n} satisfies FileRouter;\n\nexport type OurFileRouter = typeof ourFileRouter;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAEA;AACA;AAAA;AACA;;;;;;;AAEA,MAAM,IAAI,CAAA,GAAA,4JAAA,CAAA,oBAAiB,AAAD;AAEnB,MAAM,gBAAgB;IAC3B,sEAAsE;IACtE,eAAe,EAAE;QACf,OAAO;YACL,sCAAsC;YACtC,cAAc;YACd;;;OAGC,GACD,aAAa;QACf;IACF,EACE,oDAAoD;KACnD,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE;QACxB,8CAA8C;QAC9C,iDAAiD;QACjD,MAAM,UAAU,MAAM,kHAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU,CAAC;YAAE,SAAS,IAAI,OAAO;QAAC;QAEjE,oDAAoD;QACpD,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM,IAAI,wJAAA,CAAA,mBAAgB,CAAC;QAEnD,4EAA4E;QAC5E,oCAAoC;QACpC,OAAO;YAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;QAAC;IACnC,GACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE;QACzC,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,uCAAuC,SAAS,MAAM;QAClE,QAAQ,GAAG,CAAC,YAAY,KAAK,MAAM,GAAG,gCAAgC;QACtE,QAAQ,GAAG,CAAC,YAAY,KAAK,GAAG;QAEhC,0CAA0C;QAC1C,IAAI;YACF,MAAM,kHAAA,CAAA,KAAE,CAAC,MAAM,CAAC,wIAAA,CAAA,eAAY,EAAE,MAAM,CAAC;gBACnC,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD;gBACX,KAAK,KAAK,GAAG;gBACb,MAAM;gBACN,KAAK,KAAK,MAAM;gBAChB,QAAQ,SAAS,MAAM;YACzB;YACA,QAAQ,GAAG,CACT,sDACA,SAAS,MAAM;QAEnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CACX,oDACA;YAEF,oFAAoF;YACpF,qCAAqC;YACrC,MAAM,IAAI,wJAAA,CAAA,mBAAgB,CAAC;QAC7B;QAEA,4FAA4F;QAC5F,2EAA2E;QAC3E,OAAO;YACL,SAAS,KAAK,GAAG;YACjB,SAAS,KAAK,MAAM;YACpB,YAAY,SAAS,MAAM;QAC7B;IACF;IAEF,8BAA8B;IAC9B,eAAe,EAAE;QACf,OAAO;YAAE,cAAc;YAAG,aAAa;QAAO;IAChD,GACG,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE;QACxB,yCAAyC;QACzC,MAAM,UAAU,MAAM,kHAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU,CAAC;YAAE,SAAS,IAAI,OAAO;QAAC;QACjE,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM,IAAI,wJAAA,CAAA,mBAAgB,CAAC;QACnD,OAAO;YAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;QAAC;IACnC,GACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE;QACzC,QAAQ,GAAG,CAAC,uCAAuC,SAAS,MAAM;QAClE,QAAQ,GAAG,CAAC,YAAY,KAAK,MAAM,GAAG,gCAAgC;QACtE,QAAQ,GAAG,CAAC,YAAY,KAAK,GAAG;QAEhC,0CAA0C;QAC1C,IAAI;YACF,MAAM,kHAAA,CAAA,KAAE,CAAC,MAAM,CAAC,wIAAA,CAAA,eAAY,EAAE,MAAM,CAAC;gBACnC,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD;gBACX,KAAK,KAAK,GAAG;gBACb,MAAM;gBACN,KAAK,KAAK,MAAM;gBAChB,QAAQ,SAAS,MAAM;YACzB;YACA,QAAQ,GAAG,CACT,sDACA,SAAS,MAAM;QAEnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CACX,oDACA;YAEF,MAAM,IAAI,wJAAA,CAAA,mBAAgB,CAAC;QAC7B;QAEA,OAAO;YACL,SAAS,KAAK,GAAG;YACjB,SAAS,KAAK,MAAM;YACpB,YAAY,SAAS,MAAM;QAC7B;IACF;AACJ", "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/lib/hooks/use-cart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hooks/use-cart.tsx <module evaluation>\",\n    \"CartProvider\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hooks/use-cart.tsx <module evaluation>\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,4DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,4DACA", "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/lib/hooks/use-cart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hooks/use-cart.tsx\",\n    \"CartProvider\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/hooks/use-cart.tsx\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,wCACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,wCACA", "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1000, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/lib/cn.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/button.tsx"], "sourcesContent": ["import type * as React from \"react\";\n\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"~/lib/cn\";\n\nconst buttonVariants = cva(\n  `\n    inline-flex shrink-0 items-center justify-center gap-2 rounded-md text-sm\n    font-medium whitespace-nowrap shadow-sm transition-all duration-200\n    ease-in-out outline-none\n    hover:shadow-md\n    focus:shadow-lg\n    focus-visible:border-ring focus-visible:ring-2 focus-visible:ring-ring/60\n    active:shadow\n    disabled:pointer-events-none disabled:opacity-50\n    aria-invalid:border-destructive aria-invalid:ring-destructive/20\n    dark:aria-invalid:ring-destructive/40\n    [&_svg]:pointer-events-none [&_svg]:shrink-0\n    [&_svg:not([class*='size-'])]:size-4\n  `,\n  {\n    defaultVariants: {\n      size: \"default\",\n      variant: \"default\",\n    },\n    variants: {\n      size: {\n        default: `\n          h-9 px-4 py-2\n          has-[>svg]:px-3\n        `,\n        icon: \"size-9\",\n        lg: `\n          h-10 rounded-md px-6\n          has-[>svg]:px-4\n        `,\n        sm: `\n          h-8 gap-1.5 rounded-md px-3\n          has-[>svg]:px-2.5\n        `,\n      },\n      variant: {\n        default: `\n          bg-primary text-primary-foreground shadow-xs\n          hover:bg-primary/90 hover:shadow-md\n          focus-visible:ring-2 focus-visible:ring-primary/60\n        `,\n        destructive: `\n          bg-destructive text-white shadow-xs\n          hover:bg-destructive/90 hover:shadow-md\n          focus-visible:ring-2 focus-visible:ring-destructive/40\n          dark:bg-destructive/60 dark:focus-visible:ring-destructive/40\n        `,\n        ghost: `\n          hover:bg-accent hover:text-accent-foreground\n          focus-visible:ring-2 focus-visible:ring-accent/40\n          dark:hover:bg-accent/50\n        `,\n        link: `\n          text-primary underline-offset-4\n          hover:underline\n          focus-visible:ring-2 focus-visible:ring-primary/40\n        `,\n        outline: `\n          border bg-background shadow-xs\n          hover:bg-accent hover:text-accent-foreground hover:shadow-md\n          focus-visible:ring-2 focus-visible:ring-accent/40\n          dark:border-input dark:bg-input/30 dark:hover:bg-input/50\n        `,\n        secondary: `\n          bg-secondary text-secondary-foreground shadow-xs\n          hover:bg-secondary/80 hover:shadow-md\n          focus-visible:ring-2 focus-visible:ring-secondary/40\n        `,\n      },\n    },\n  },\n);\n\nfunction Button({\n  asChild = false,\n  className,\n  size,\n  variant,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      className={cn(buttonVariants({ className, size, variant }))}\n      data-slot=\"button\"\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,CAAC;;;;;;;;;;;;;EAaD,CAAC,EACD;IACE,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,UAAU;QACR,MAAM;YACJ,SAAS,CAAC;;;QAGV,CAAC;YACD,MAAM;YACN,IAAI,CAAC;;;QAGL,CAAC;YACD,IAAI,CAAC;;;QAGL,CAAC;QACH;QACA,SAAS;YACP,SAAS,CAAC;;;;QAIV,CAAC;YACD,aAAa,CAAC;;;;;QAKd,CAAC;YACD,OAAO,CAAC;;;;QAIR,CAAC;YACD,MAAM,CAAC;;;;QAIP,CAAC;YACD,SAAS,CAAC;;;;;QAKV,CAAC;YACD,WAAW,CAAC;;;;QAIZ,CAAC;QACH;IACF;AACF;AAGF,SAAS,OAAO,EACd,UAAU,KAAK,EACf,SAAS,EACT,IAAI,EACJ,OAAO,EACP,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAW;YAAM;QAAQ;QACxD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/footer.tsx"], "sourcesContent": ["import { Facebook, Github, Instagram, Linkedin, Twitter } from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { SEO_CONFIG } from \"~/app\";\nimport { cn } from \"~/lib/cn\";\nimport { But<PERSON> } from \"~/ui/primitives/button\";\n\nexport function Footer({ className }: { className?: string }) {\n  return (\n    <footer className={cn(\"border-t bg-background\", className)}>\n      <div\n        className={`\n          container mx-auto max-w-7xl px-4 py-12\n          sm:px-6\n          lg:px-8\n        `}\n      >\n        <div\n          className={`\n            grid grid-cols-1 gap-8\n            md:grid-cols-4\n          `}\n        >\n          <div className=\"space-y-4\">\n            <Link className=\"flex items-center gap-2\" href=\"/\">\n              <span\n                className={`\n                  bg-gradient-to-r from-primary to-primary/70 bg-clip-text\n                  text-xl font-bold tracking-tight text-transparent\n                `}\n              >\n                {SEO_CONFIG.name}\n              </span>\n            </Link>\n            <p className=\"text-sm text-muted-foreground\">\n              Your one-stop shop for everything tech. Premium products at\n              competitive prices.\n            </p>\n            <div className=\"flex space-x-4\">\n              <Button\n                className=\"h-8 w-8 rounded-full\"\n                size=\"icon\"\n                variant=\"ghost\"\n              >\n                <Facebook className=\"h-4 w-4\" />\n                <span className=\"sr-only\">Facebook</span>\n              </Button>\n              <Button\n                className=\"h-8 w-8 rounded-full\"\n                size=\"icon\"\n                variant=\"ghost\"\n              >\n                <Twitter className=\"h-4 w-4\" />\n                <span className=\"sr-only\">Twitter</span>\n              </Button>\n              <Button\n                className=\"h-8 w-8 rounded-full\"\n                size=\"icon\"\n                variant=\"ghost\"\n              >\n                <Instagram className=\"h-4 w-4\" />\n                <span className=\"sr-only\">Instagram</span>\n              </Button>\n              <Button\n                className=\"h-8 w-8 rounded-full\"\n                size=\"icon\"\n                variant=\"ghost\"\n              >\n                <Github className=\"h-4 w-4\" />\n                <span className=\"sr-only\">GitHub</span>\n              </Button>\n              <Button\n                className=\"h-8 w-8 rounded-full\"\n                size=\"icon\"\n                variant=\"ghost\"\n              >\n                <Linkedin className=\"h-4 w-4\" />\n                <span className=\"sr-only\">LinkedIn</span>\n              </Button>\n            </div>\n          </div>\n          <div>\n            <h3 className=\"mb-4 text-sm font-semibold\">Shop</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/products\"\n                >\n                  All Products\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/products?category=audio\"\n                >\n                  Audio\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/products?category=wearables\"\n                >\n                  Wearables\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/products?category=smartphones\"\n                >\n                  Smartphones\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/products?category=laptops\"\n                >\n                  Laptops\n                </Link>\n              </li>\n            </ul>\n          </div>\n          <div>\n            <h3 className=\"mb-4 text-sm font-semibold\">Company</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/about\"\n                >\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/careers\"\n                >\n                  Careers\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/blog\"\n                >\n                  Blog\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/press\"\n                >\n                  Press\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/contact\"\n                >\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n          <div>\n            <h3 className=\"mb-4 text-sm font-semibold\">Support</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/help\"\n                >\n                  Help Center\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/shipping\"\n                >\n                  Shipping & Returns\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/warranty\"\n                >\n                  Warranty\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/privacy\"\n                >\n                  Privacy Policy\n                </Link>\n              </li>\n              <li>\n                <Link\n                  className={`\n                    text-muted-foreground\n                    hover:text-foreground\n                  `}\n                  href=\"/terms\"\n                >\n                  Terms of Service\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n        <div className=\"mt-12 border-t pt-8\">\n          <div\n            className={`\n              flex flex-col items-center justify-between gap-4\n              md:flex-row\n            `}\n          >\n            <p className=\"text-sm text-muted-foreground\">\n              &copy; {new Date().getFullYear()} {SEO_CONFIG.name}. All rights\n              reserved.\n            </p>\n            <div\n              className={\n                \"flex items-center gap-4 text-sm text-muted-foreground\"\n              }\n            >\n              <Link className=\"hover:text-foreground\" href=\"/privacy\">\n                Privacy\n              </Link>\n              <Link className=\"hover:text-foreground\" href=\"/terms\">\n                Terms\n              </Link>\n              <Link className=\"hover:text-foreground\" href=\"/cookies\">\n                Cookies\n              </Link>\n              <Link className=\"hover:text-foreground\" href=\"/sitemap\">\n                Sitemap\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;;;;;;;AAEO,SAAS,OAAO,EAAE,SAAS,EAA0B;IAC1D,qBACE,8OAAC;QAAO,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;kBAC9C,cAAA,8OAAC;YACC,WAAW,CAAC;;;;QAIZ,CAAC;;8BAED,8OAAC;oBACC,WAAW,CAAC;;;UAGZ,CAAC;;sCAED,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,MAAK;8CAC7C,cAAA,8OAAC;wCACC,WAAW,CAAC;;;gBAGZ,CAAC;kDAEA,0GAAA,CAAA,aAAU,CAAC,IAAI;;;;;;;;;;;8CAGpB,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAI7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,MAAK;4CACL,SAAQ;;8DAER,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC,kIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,MAAK;4CACL,SAAQ;;8DAER,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC,kIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,MAAK;4CACL,SAAQ;;8DAER,8OAAC,4MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC,kIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,MAAK;4CACL,SAAQ;;8DAER,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC,kIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,MAAK;4CACL,SAAQ;;8DAER,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAIhC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;;;;;;;;;;;;;sCAMP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;;;;;;;;;;;;;sCAMP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,WAAW,CAAC;;;kBAGZ,CAAC;gDACD,MAAK;0DACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAW,CAAC;;;YAGZ,CAAC;;0CAED,8OAAC;gCAAE,WAAU;;oCAAgC;oCACnC,IAAI,OAAO,WAAW;oCAAG;oCAAE,0GAAA,CAAA,aAAU,CAAC,IAAI;oCAAC;;;;;;;0CAGrD,8OAAC;gCACC,WACE;;kDAGF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAwB,MAAK;kDAAW;;;;;;kDAGxD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAwB,MAAK;kDAAS;;;;;;kDAGtD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAwB,MAAK;kDAAW;;;;;;kDAGxD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAwB,MAAK;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStE", "debugId": null}}, {"offset": {"line": 1778, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/header/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/components/header/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,qEACA", "debugId": null}}, {"offset": {"line": 1792, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/header/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/components/header/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,iDACA", "debugId": null}}, {"offset": {"line": 1806, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1816, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/components/theme-provider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,sEACA", "debugId": null}}, {"offset": {"line": 1830, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/components/theme-provider.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,kDACA", "debugId": null}}, {"offset": {"line": 1844, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1854, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/primitives/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 1868, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/primitives/sonner.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 1882, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1892, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\n\nimport { NextSSRPlugin } from \"@uploadthing/react/next-ssr-plugin\";\nimport { SpeedInsights } from \"@vercel/speed-insights/next\";\nimport { <PERSON>ei<PERSON>, <PERSON>ei<PERSON>_Mon<PERSON> } from \"next/font/google\";\nimport { extractRouterConfig } from \"uploadthing/server\";\n\nimport { SEO_CONFIG } from \"~/app\";\nimport { ourFileRouter } from \"~/app/api/uploadthing/core\";\nimport { CartProvider } from \"~/lib/hooks/use-cart\";\nimport \"~/css/globals.css\";\nimport { Footer } from \"~/ui/components/footer\";\nimport { Header } from \"~/ui/components/header/header\";\nimport { ThemeProvider } from \"~/ui/components/theme-provider\";\nimport { Toaster } from \"~/ui/primitives/sonner\";\n\nconst geistSans = Geist({\n  subsets: [\"latin\"],\n  variable: \"--font-geist-sans\",\n});\n\nconst geistMono = Geist_Mono({\n  subsets: [\"latin\"],\n  variable: \"--font-geist-mono\",\n});\n\nexport const metadata: Metadata = {\n  description: `${SEO_CONFIG.description}`,\n  title: `${SEO_CONFIG.fullName}`,\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body\n        className={`\n          ${geistSans.variable}\n          ${geistMono.variable}\n          min-h-screen bg-gradient-to-br from-white to-slate-100\n          text-neutral-900 antialiased\n          selection:bg-primary/80\n          dark:from-neutral-950 dark:to-neutral-900 dark:text-neutral-100\n        `}\n      >\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"system\"\n          disableTransitionOnChange\n          enableSystem\n        >\n          <NextSSRPlugin routerConfig={extractRouterConfig(ourFileRouter)} />\n          <CartProvider>\n            <Header showAuth={true} />\n            <main className={`flex min-h-screen flex-col`}>{children}</main>\n            <Footer />\n            <Toaster />\n          </CartProvider>\n        </ThemeProvider>\n        <SpeedInsights />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAYO,MAAM,WAAqB;IAChC,aAAa,GAAG,0GAAA,CAAA,aAAU,CAAC,WAAW,EAAE;IACxC,OAAO,GAAG,0GAAA,CAAA,aAAU,CAAC,QAAQ,EAAE;AACjC;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,8OAAC;YACC,WAAW,CAAC;UACV,EAAE,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC;UACrB,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC;;;;;QAKvB,CAAC;;8BAED,8OAAC,6IAAA,CAAA,gBAAa;oBACZ,WAAU;oBACV,cAAa;oBACb,yBAAyB;oBACzB,YAAY;;sCAEZ,8OAAC,wKAAA,CAAA,gBAAa;4BAAC,cAAc,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE,wIAAA,CAAA,gBAAa;;;;;;sCAC9D,8OAAC,mIAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,SAAM;oCAAC,UAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAW,CAAC,0BAA0B,CAAC;8CAAG;;;;;;8CAChD,8OAAC,kIAAA,CAAA,SAAM;;;;;8CACP,8OAAC,kIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;8BAGZ,8OAAC,uKAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;AAItB", "debugId": null}}]}