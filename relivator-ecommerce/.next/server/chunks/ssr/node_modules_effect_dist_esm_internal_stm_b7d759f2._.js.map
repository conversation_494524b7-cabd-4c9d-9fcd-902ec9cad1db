{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "versioned.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/versioned.ts"], "sourcesContent": ["/** @internal */\nexport class Versioned<out A> {\n  constructor(readonly value: A) {}\n}\n"], "names": ["Versioned", "value", "constructor"], "mappings": "AAAA,cAAA;;;AACM,MAAOA,SAAS;IACCC,KAAA,CAAA;IAArBC,YAAqBD,KAAQ,CAAA;QAAR,IAAA,CAAAA,KAAK,GAALA,KAAK;IAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "file": "entry.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/entry.ts"], "sourcesContent": ["import type * as TRef from \"../../TRef.js\"\nimport * as Versioned from \"./versioned.js\"\n\n/** @internal */\nexport interface Entry {\n  readonly ref: TRef.TRef<any>\n  readonly expected: Versioned.Versioned<any>\n  isChanged: boolean // mutable by design\n  readonly isNew: boolean\n  newValue: any // mutable by design\n}\n\n/** @internal */\nexport const make = <A>(ref: TRef.TRef<A>, isNew: boolean): Entry => ({\n  ref,\n  isNew,\n  isChanged: false,\n  expected: ref.versioned,\n  newValue: ref.versioned.value\n})\n\nexport const unsafeGet = (self: Entry): unknown => {\n  return self.newValue\n}\n\n/** @internal */\nexport const unsafeSet = (self: Entry, value: unknown): void => {\n  self.isChanged = true\n  self.newValue = value\n}\n\n/** @internal */\nexport const commit = (self: Entry): void => {\n  self.ref.versioned = new Versioned.Versioned(self.newValue)\n}\n\n/** @internal */\nexport const copy = (self: Entry): Entry => ({\n  ref: self.ref,\n  isNew: self.isNew,\n  isChanged: self.isChanged,\n  expected: self.expected,\n  newValue: self.newValue\n})\n\n/** @internal */\nexport const isValid = (self: Entry): boolean => {\n  return self.ref.versioned === self.expected\n}\n\n/** @internal */\nexport const isInvalid = (self: Entry): boolean => {\n  return self.ref.versioned !== self.expected\n}\n\n/** @internal */\nexport const isChanged = (self: Entry): boolean => {\n  return self.isChanged\n}\n"], "names": ["Versioned", "make", "ref", "isNew", "isChanged", "expected", "versioned", "newValue", "value", "unsafeGet", "self", "unsafeSet", "commit", "copy", "<PERSON><PERSON><PERSON><PERSON>", "isInvalid"], "mappings": ";;;;;;;;;;AACA,OAAO,KAAKA,SAAS,MAAM,gBAAgB;;AAYpC,MAAMC,IAAI,GAAGA,CAAIC,GAAiB,EAAEC,KAAc,GAAA,CAAa;QACpED,GAAG;QACHC,KAAK;QACLC,SAAS,EAAE,KAAK;QAChBC,QAAQ,EAAEH,GAAG,CAACI,SAAS;QACvBC,QAAQ,EAAEL,GAAG,CAACI,SAAS,CAACE,KAAAA;KACzB,CAAC;AAEK,MAAMC,SAAS,IAAIC,IAAW,IAAa;IAChD,OAAOA,IAAI,CAACH,QAAQ;AACtB,CAAC;AAGM,MAAMI,SAAS,GAAGA,CAACD,IAAW,EAAEF,KAAc,KAAU;IAC7DE,IAAI,CAACN,SAAS,GAAG,IAAI;IACrBM,IAAI,CAACH,QAAQ,GAAGC,KAAK;AACvB,CAAC;AAGM,MAAMI,MAAM,IAAIF,IAAW,IAAU;IAC1CA,IAAI,CAACR,GAAG,CAACI,SAAS,GAAG,0KAAIN,SAAS,CAACA,EAAS,CAACU,IAAI,CAACH,QAAQ,CAAC;AAC7D,CAAC;AAGM,MAAMM,IAAI,IAAIH,IAAW,GAAA,CAAa;QAC3CR,GAAG,EAAEQ,IAAI,CAACR,GAAG;QACbC,KAAK,EAAEO,IAAI,CAACP,KAAK;QACjBC,SAAS,EAAEM,IAAI,CAACN,SAAS;QACzBC,QAAQ,EAAEK,IAAI,CAACL,QAAQ;QACvBE,QAAQ,EAAEG,IAAI,CAACH,QAAAA;KAChB,CAAC;AAGK,MAAMO,OAAO,IAAIJ,IAAW,IAAa;IAC9C,OAAOA,IAAI,CAACR,GAAG,CAACI,SAAS,KAAKI,IAAI,CAACL,QAAQ;AAC7C,CAAC;AAGM,MAAMU,SAAS,IAAIL,IAAW,IAAa;IAChD,OAAOA,IAAI,CAACR,GAAG,CAACI,SAAS,KAAKI,IAAI,CAACL,QAAQ;AAC7C,CAAC;AAGM,MAAMD,SAAS,IAAIM,IAAW,IAAa;IAChD,OAAOA,IAAI,CAACN,SAAS;AACvB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "file": "journal.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/journal.ts"], "sourcesContent": ["import type * as TRef from \"../../TRef.js\"\nimport * as Entry from \"./entry.js\"\nimport type * as TxnId from \"./txnId.js\"\n\n/** @internal */\nexport type Journal = Map<TRef.TRef<any>, Entry.Entry>\n\n/** @internal */\nexport type Todo = () => unknown\n\n/** @internal */\nexport type JournalAnalysis = JournalAnalysisInvalid | JournalAnalysisReadWrite | JournalAnalysisReadOnly\n\n/** @internal */\nexport const JournalAnalysisInvalid = \"Invalid\" as const\n\n/** @internal */\nexport type JournalAnalysisInvalid = typeof JournalAnalysisInvalid\n\n/** @internal */\nexport const JournalAnalysisReadWrite = \"ReadWrite\" as const\n\n/** @internal */\nexport type JournalAnalysisReadWrite = typeof JournalAnalysisReadWrite\n\n/** @internal */\nexport const JournalAnalysisReadOnly = \"ReadOnly\" as const\n\n/** @internal */\nexport type JournalAnalysisReadOnly = typeof JournalAnalysisReadOnly\n\n/** @internal */\nexport const commitJournal = (journal: Journal) => {\n  for (const entry of journal) {\n    Entry.commit(entry[1])\n  }\n}\n\n/**\n * Analyzes the journal, determining whether it is valid and whether it is\n * read only in a single pass. Note that information on whether the\n * journal is read only will only be accurate if the journal is valid, due\n * to short-circuiting that occurs on an invalid journal.\n *\n * @internal\n */\nexport const analyzeJournal = (journal: Journal): JournalAnalysis => {\n  let val: JournalAnalysis = JournalAnalysisReadOnly\n  for (const [, entry] of journal) {\n    val = Entry.isInvalid(entry) ? JournalAnalysisInvalid : Entry.isChanged(entry) ? JournalAnalysisReadWrite : val\n    if (val === JournalAnalysisInvalid) {\n      return val\n    }\n  }\n  return val\n}\n\n/** @internal */\nexport const prepareResetJournal = (journal: Journal): () => void => {\n  const saved: Journal = new Map<TRef.TRef<unknown>, Entry.Entry>()\n  for (const entry of journal) {\n    saved.set(entry[0], Entry.copy(entry[1]))\n  }\n  return () => {\n    journal.clear()\n    for (const entry of saved) {\n      journal.set(entry[0], entry[1])\n    }\n  }\n}\n\n/** @internal */\nexport const collectTodos = (journal: Journal): Map<TxnId.TxnId, Todo> => {\n  const allTodos: Map<TxnId.TxnId, Todo> = new Map()\n  for (const [, entry] of journal) {\n    for (const todo of entry.ref.todos) {\n      allTodos.set(todo[0], todo[1])\n    }\n    entry.ref.todos = new Map()\n  }\n  return allTodos\n}\n\n/** @internal */\nexport const execTodos = (todos: Map<TxnId.TxnId, Todo>) => {\n  const todosSorted = Array.from(todos.entries()).sort((x, y) => x[0] - y[0])\n  for (const [_, todo] of todosSorted) {\n    todo()\n  }\n}\n\n/** @internal */\nexport const addTodo = (\n  txnId: TxnId.TxnId,\n  journal: Journal,\n  todoEffect: Todo\n): boolean => {\n  let added = false\n  for (const [, entry] of journal) {\n    if (!entry.ref.todos.has(txnId)) {\n      entry.ref.todos.set(txnId, todoEffect)\n      added = true\n    }\n  }\n  return added\n}\n\n/** @internal */\nexport const isValid = (journal: Journal): boolean => {\n  let valid = true\n  for (const [, entry] of journal) {\n    valid = Entry.isValid(entry)\n    if (!valid) {\n      return valid\n    }\n  }\n  return valid\n}\n\n/** @internal */\nexport const isInvalid = (journal: Journal): boolean => {\n  return !isValid(journal)\n}\n"], "names": ["Entry", "JournalAnalysisInvalid", "JournalAnalysisReadWrite", "JournalAnalysisReadOnly", "commitJournal", "journal", "entry", "commit", "analyzeJournal", "val", "isInvalid", "isChanged", "prepareResetJournal", "saved", "Map", "set", "copy", "clear", "collectTodos", "allTodos", "todo", "ref", "todos", "execTodos", "todosSorted", "Array", "from", "entries", "sort", "x", "y", "_", "addTodo", "txnId", "todoEffect", "added", "has", "<PERSON><PERSON><PERSON><PERSON>", "valid"], "mappings": ";;;;;;;;;;;;;AACA,OAAO,KAAKA,KAAK,MAAM,YAAY;;AAa5B,MAAMC,sBAAsB,GAAG,SAAkB;AAMjD,MAAMC,wBAAwB,GAAG,WAAoB;AAMrD,MAAMC,uBAAuB,GAAG,UAAmB;AAMnD,MAAMC,aAAa,IAAIC,OAAgB,IAAI;IAChD,KAAK,MAAMC,KAAK,IAAID,OAAO,CAAE;6KAC3BL,KAAK,CAACO,IAAAA,AAAM,EAACD,KAAK,CAAC,CAAC,CAAC,CAAC;IACxB;AACF,CAAC;AAUM,MAAME,cAAc,IAAIH,OAAgB,IAAqB;IAClE,IAAII,GAAG,GAAoBN,uBAAuB;IAClD,KAAK,MAAM,GAAGG,KAAK,CAAC,IAAID,OAAO,CAAE;QAC/BI,GAAG,OAAGT,KAAK,CAACU,wKAAAA,AAAS,EAACJ,KAAK,CAAC,GAAGL,sBAAsB,yKAAGD,KAAK,CAACW,MAAAA,AAAS,EAACL,KAAK,CAAC,GAAGJ,wBAAwB,GAAGO,GAAG;QAC/G,IAAIA,GAAG,KAAKR,sBAAsB,EAAE;YAClC,OAAOQ,GAAG;QACZ;IACF;IACA,OAAOA,GAAG;AACZ,CAAC;AAGM,MAAMG,mBAAmB,IAAIP,OAAgB,IAAgB;IAClE,MAAMQ,KAAK,GAAY,IAAIC,GAAG,EAAmC;IACjE,KAAK,MAAMR,KAAK,IAAID,OAAO,CAAE;QAC3BQ,KAAK,CAACE,GAAG,CAACT,KAAK,CAAC,CAAC,CAAC,wKAAEN,KAAK,CAACgB,CAAAA,AAAI,EAACV,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,OAAO,MAAK;QACVD,OAAO,CAACY,KAAK,EAAE;QACf,KAAK,MAAMX,KAAK,IAAIO,KAAK,CAAE;YACzBR,OAAO,CAACU,GAAG,CAACT,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;QACjC;IACF,CAAC;AACH,CAAC;AAGM,MAAMY,YAAY,IAAIb,OAAgB,IAA4B;IACvE,MAAMc,QAAQ,GAA2B,IAAIL,GAAG,EAAE;IAClD,KAAK,MAAM,GAAGR,KAAK,CAAC,IAAID,OAAO,CAAE;QAC/B,KAAK,MAAMe,IAAI,IAAId,KAAK,CAACe,GAAG,CAACC,KAAK,CAAE;YAClCH,QAAQ,CAACJ,GAAG,CAACK,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC;QACAd,KAAK,CAACe,GAAG,CAACC,KAAK,GAAG,IAAIR,GAAG,EAAE;IAC7B;IACA,OAAOK,QAAQ;AACjB,CAAC;AAGM,MAAMI,SAAS,IAAID,KAA6B,IAAI;IACzD,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAACK,OAAO,EAAE,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,KAAK,MAAM,CAACC,CAAC,EAAEX,IAAI,CAAC,IAAII,WAAW,CAAE;QACnCJ,IAAI,EAAE;IACR;AACF,CAAC;AAGM,MAAMY,OAAO,GAAGA,CACrBC,KAAkB,EAClB5B,OAAgB,EAChB6B,UAAgB,KACL;IACX,IAAIC,KAAK,GAAG,KAAK;IACjB,KAAK,MAAM,GAAG7B,KAAK,CAAC,IAAID,OAAO,CAAE;QAC/B,IAAI,CAACC,KAAK,CAACe,GAAG,CAACC,KAAK,CAACc,GAAG,CAACH,KAAK,CAAC,EAAE;YAC/B3B,KAAK,CAACe,GAAG,CAACC,KAAK,CAACP,GAAG,CAACkB,KAAK,EAAEC,UAAU,CAAC;YACtCC,KAAK,GAAG,IAAI;QACd;IACF;IACA,OAAOA,KAAK;AACd,CAAC;AAGM,MAAME,OAAO,IAAIhC,OAAgB,IAAa;IACnD,IAAIiC,KAAK,GAAG,IAAI;IAChB,KAAK,MAAM,GAAGhC,KAAK,CAAC,IAAID,OAAO,CAAE;QAC/BiC,KAAK,yKAAGtC,KAAK,CAACqC,IAAAA,AAAO,EAAC/B,KAAK,CAAC;QAC5B,IAAI,CAACgC,KAAK,EAAE;YACV,OAAOA,KAAK;QACd;IACF;IACA,OAAOA,KAAK;AACd,CAAC;AAGM,MAAM5B,SAAS,IAAIL,OAAgB,IAAa;IACrD,OAAO,CAACgC,OAAO,CAAChC,OAAO,CAAC;AAC1B,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "file": "stm.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/opCodes/stm.ts"], "sourcesContent": ["/** @internal */\nexport const OP_WITH_STM_RUNTIME = \"WithSTMRuntime\" as const\n\n/** @internal */\nexport type OP_WITH_STM_RUNTIME = typeof OP_WITH_STM_RUNTIME\n\n/** @internal */\nexport const OP_ON_FAILURE = \"OnFailure\" as const\n\n/** @internal */\nexport type OP_ON_FAILURE = typeof OP_ON_FAILURE\n\n/** @internal */\nexport const OP_ON_RETRY = \"OnRetry\" as const\n\n/** @internal */\nexport type OP_ON_RETRY = typeof OP_ON_RETRY\n\n/** @internal */\nexport const OP_ON_SUCCESS = \"OnSuccess\" as const\n\n/** @internal */\nexport type OP_ON_SUCCESS = typeof OP_ON_SUCCESS\n\n/** @internal */\nexport const OP_PROVIDE = \"Provide\" as const\n\n/** @internal */\nexport type OP_PROVIDE = typeof OP_PROVIDE\n\n/** @internal */\nexport const OP_SYNC = \"Sync\" as const\n\n/** @internal */\nexport type OP_SYNC = typeof OP_SYNC\n\n/** @internal */\nexport const OP_SUCCEED = \"Succeed\" as const\n\n/** @internal */\nexport type OP_SUCCEED = typeof OP_SUCCEED\n\n/** @internal */\nexport const OP_RETRY = \"Retry\" as const\n\n/** @internal */\nexport type OP_RETRY = typeof OP_RETRY\n\n/** @internal */\nexport const OP_FAIL = \"Fail\" as const\n\n/** @internal */\nexport type OP_FAIL = typeof OP_FAIL\n\n/** @internal */\nexport const OP_DIE = \"Die\" as const\n\n/** @internal */\nexport type OP_DIE = typeof OP_DIE\n\n/** @internal */\nexport const OP_INTERRUPT = \"Interrupt\" as const\n\n/** @internal */\nexport type OP_INTERRUPT = typeof OP_INTERRUPT\n\n/** @internal */\nexport const OP_TRACED = \"Traced\" as const\n\n/** @internal */\nexport type OP_TRACED = typeof OP_TRACED\n"], "names": ["OP_WITH_STM_RUNTIME", "OP_ON_FAILURE", "OP_ON_RETRY", "OP_ON_SUCCESS", "OP_PROVIDE", "OP_SYNC", "OP_SUCCEED", "OP_RETRY", "OP_FAIL", "OP_DIE", "OP_INTERRUPT", "OP_TRACED"], "mappings": "AAAA,cAAA;;;;;;;;;;;;;;AACO,MAAMA,mBAAmB,GAAG,gBAAyB;AAMrD,MAAMC,aAAa,GAAG,WAAoB;AAM1C,MAAMC,WAAW,GAAG,SAAkB;AAMtC,MAAMC,aAAa,GAAG,WAAoB;AAM1C,MAAMC,UAAU,GAAG,SAAkB;AAMrC,MAAMC,OAAO,GAAG,MAAe;AAM/B,MAAMC,UAAU,GAAG,SAAkB;AAMrC,MAAMC,QAAQ,GAAG,OAAgB;AAMjC,MAAMC,OAAO,GAAG,MAAe;AAM/B,MAAMC,MAAM,GAAG,KAAc;AAM7B,MAAMC,YAAY,GAAG,WAAoB;AAMzC,MAAMC,SAAS,GAAG,QAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "file": "tExit.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/opCodes/tExit.ts"], "sourcesContent": ["/** @internal */\nexport const OP_FAIL = \"Fail\" as const\n\n/** @internal */\nexport type OP_FAIL = typeof OP_FAIL\n\n/** @internal */\nexport const OP_DIE = \"Die\" as const\n\n/** @internal */\nexport type OP_DIE = typeof OP_DIE\n\n/** @internal */\nexport const OP_INTERRUPT = \"Interrupt\" as const\n\n/** @internal */\nexport type OP_INTERRUPT = typeof OP_INTERRUPT\n\n/** @internal */\nexport const OP_SUCCEED = \"Succeed\" as const\n\n/** @internal */\nexport type OP_SUCCEED = typeof OP_SUCCEED\n\n/** @internal */\nexport const OP_RETRY = \"Retry\" as const\n\n/** @internal */\nexport type OP_RETRY = typeof OP_RETRY\n"], "names": ["OP_FAIL", "OP_DIE", "OP_INTERRUPT", "OP_SUCCEED", "OP_RETRY"], "mappings": "AAAA,cAAA;;;;;;;AACO,MAAMA,OAAO,GAAG,MAAe;AAM/B,MAAMC,MAAM,GAAG,KAAc;AAM7B,MAAMC,YAAY,GAAG,WAAoB;AAMzC,MAAMC,UAAU,GAAG,SAAkB;AAMrC,MAAMC,QAAQ,GAAG,OAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "file": "tryCommit.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/opCodes/tryCommit.ts"], "sourcesContent": ["/** @internal */\nexport const OP_DONE = \"Done\" as const\n\n/** @internal */\nexport type OP_DONE = typeof OP_DONE\n\n/** @internal */\nexport const OP_SUSPEND = \"Suspend\" as const\n\n/** @internal */\nexport type OP_SUSPEND = typeof OP_SUSPEND\n"], "names": ["OP_DONE", "OP_SUSPEND"], "mappings": "AAAA,cAAA;;;;AACO,MAAMA,OAAO,GAAG,MAAe;AAM/B,MAAMC,UAAU,GAAG,SAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "file": "stmState.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/opCodes/stmState.ts"], "sourcesContent": ["/** @internal */\nexport const OP_DONE = \"Done\" as const\n\n/** @internal */\nexport type OP_DONE = typeof OP_DONE\n\n/** @internal */\nexport const OP_INTERRUPTED = \"Interrupted\" as const\n\n/** @internal */\nexport type OP_INTERRUPTED = typeof OP_INTERRUPTED\n\n/** @internal */\nexport const OP_RUNNING = \"Running\" as const\n\n/** @internal */\nexport type OP_RUNNING = typeof OP_RUNNING\n"], "names": ["OP_DONE", "OP_INTERRUPTED", "OP_RUNNING"], "mappings": "AAAA,cAAA;;;;;AACO,MAAMA,OAAO,GAAG,MAAe;AAM/B,MAAMC,cAAc,GAAG,aAAsB;AAM7C,MAAMC,UAAU,GAAG,SAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "file": "stmState.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/stmState.ts"], "sourcesContent": ["import * as Equal from \"../../Equal.js\"\nimport * as Exit from \"../../Exit.js\"\nimport { pipe } from \"../../Function.js\"\nimport * as Hash from \"../../Hash.js\"\nimport { hasProperty } from \"../../Predicate.js\"\nimport * as OpCodes from \"./opCodes/stmState.js\"\nimport * as TExitOpCodes from \"./opCodes/tExit.js\"\nimport type * as TExit from \"./tExit.js\"\n\n/** @internal */\nconst STMStateSymbolKey = \"effect/STM/State\"\n\n/** @internal */\nexport const STMStateTypeId = Symbol.for(STMStateSymbolKey)\n\n/** @internal */\nexport type STMStateTypeId = typeof STMStateTypeId\n\n/** @internal */\nexport type STMState<A, E = never> = Done<A, E> | Interrupted | Running\n\n/** @internal */\nexport interface Done<out A, out E = never> extends Equal.Equal {\n  readonly [STMStateTypeId]: STMStateTypeId\n  readonly _tag: OpCodes.OP_DONE\n  readonly exit: Exit.Exit<A, E>\n}\n\n/** @internal */\nexport interface Interrupted extends Equal.Equal {\n  readonly [STMStateTypeId]: STMStateTypeId\n  readonly _tag: OpCodes.OP_INTERRUPTED\n}\n\n/** @internal */\nexport interface Running extends Equal.Equal {\n  readonly [STMStateTypeId]: STMStateTypeId\n  readonly _tag: OpCodes.OP_RUNNING\n}\n\n/** @internal */\nexport const isSTMState = (u: unknown): u is STMState<unknown, unknown> => hasProperty(u, STMStateTypeId)\n\n/** @internal */\nexport const isRunning = <A, E>(self: STMState<A, E>): self is Running => {\n  return self._tag === OpCodes.OP_RUNNING\n}\n\n/** @internal */\nexport const isDone = <A, E>(self: STMState<A, E>): self is Done<A, E> => {\n  return self._tag === OpCodes.OP_DONE\n}\n\n/** @internal */\nexport const isInterrupted = <A, E>(self: STMState<A, E>): self is Interrupted => {\n  return self._tag === OpCodes.OP_INTERRUPTED\n}\n\n/** @internal */\nexport const done = <A, E>(exit: Exit.Exit<A, E>): STMState<A, E> => {\n  return {\n    [STMStateTypeId]: STMStateTypeId,\n    _tag: OpCodes.OP_DONE,\n    exit,\n    [Hash.symbol](): number {\n      return pipe(\n        Hash.hash(STMStateSymbolKey),\n        Hash.combine(Hash.hash(OpCodes.OP_DONE)),\n        Hash.combine(Hash.hash(exit)),\n        Hash.cached(this)\n      )\n    },\n    [Equal.symbol](that: unknown): boolean {\n      return isSTMState(that) && that._tag === OpCodes.OP_DONE && Equal.equals(exit, that.exit)\n    }\n  }\n}\n\nconst interruptedHash = pipe(\n  Hash.hash(STMStateSymbolKey),\n  Hash.combine(Hash.hash(OpCodes.OP_INTERRUPTED)),\n  Hash.combine(Hash.hash(\"interrupted\"))\n)\n\n/** @internal */\nexport const interrupted: STMState<never> = {\n  [STMStateTypeId]: STMStateTypeId,\n  _tag: OpCodes.OP_INTERRUPTED,\n  [Hash.symbol](): number {\n    return interruptedHash\n  },\n  [Equal.symbol](that: unknown): boolean {\n    return isSTMState(that) && that._tag === OpCodes.OP_INTERRUPTED\n  }\n}\n\nconst runningHash = pipe(\n  Hash.hash(STMStateSymbolKey),\n  Hash.combine(Hash.hash(OpCodes.OP_RUNNING)),\n  Hash.combine(Hash.hash(\"running\"))\n)\n\n/** @internal */\nexport const running: STMState<never> = {\n  [STMStateTypeId]: STMStateTypeId,\n  _tag: OpCodes.OP_RUNNING,\n  [Hash.symbol](): number {\n    return runningHash\n  },\n  [Equal.symbol](that: unknown): boolean {\n    return isSTMState(that) && that._tag === OpCodes.OP_RUNNING\n  }\n}\n\n/** @internal */\nexport const fromTExit = <A, E>(tExit: TExit.TExit<A, E>): STMState<A, E> => {\n  switch (tExit._tag) {\n    case TExitOpCodes.OP_FAIL: {\n      return done(Exit.fail(tExit.error))\n    }\n    case TExitOpCodes.OP_DIE: {\n      return done(Exit.die(tExit.defect))\n    }\n    case TExitOpCodes.OP_INTERRUPT: {\n      return done(Exit.interrupt(tExit.fiberId))\n    }\n    case TExitOpCodes.OP_SUCCEED: {\n      return done(Exit.succeed(tExit.value))\n    }\n    case TExitOpCodes.OP_RETRY: {\n      throw new Error(\n        \"BUG: STM.STMState.fromTExit - please report an issue at https://github.com/Effect-TS/effect/issues\"\n      )\n    }\n  }\n}\n"], "names": ["Equal", "Exit", "pipe", "Hash", "hasProperty", "OpCodes", "TExitOpCodes", "STMStateSymbolKey", "STMStateTypeId", "Symbol", "for", "isSTMState", "u", "isRunning", "self", "_tag", "OP_RUNNING", "isDone", "OP_DONE", "isInterrupted", "OP_INTERRUPTED", "done", "exit", "symbol", "hash", "combine", "cached", "that", "equals", "interruptedHash", "interrupted", "runningHash", "running", "fromTExit", "tExit", "OP_FAIL", "fail", "error", "OP_DIE", "die", "defect", "OP_INTERRUPT", "interrupt", "fiberId", "OP_SUCCEED", "succeed", "value", "OP_RETRY", "Error"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,gBAAgB;AACvC,OAAO,KAAKC,IAAI,MAAM,eAAe;AACrC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,OAAO,KAAKC,IAAI,MAAM,eAAe;AACrC,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,YAAY,MAAM,oBAAoB;;;;;;;;AAGlD,cAAA,GACA,MAAMC,iBAAiB,GAAG,kBAAkB;AAGrC,MAAMC,cAAc,GAAA,WAAA,GAAGC,MAAM,CAACC,GAAG,CAACH,iBAAiB,CAAC;AA4BpD,MAAMI,UAAU,IAAIC,CAAU,0JAAsCR,cAAAA,AAAW,EAACQ,CAAC,EAAEJ,cAAc,CAAC;AAGlG,MAAMK,SAAS,IAAUC,IAAoB,IAAqB;IACvE,OAAOA,IAAI,CAACC,IAAI,qLAAKV,OAAO,CAACW,KAAU;AACzC,CAAC;AAGM,MAAMC,MAAM,IAAUH,IAAoB,IAAwB;IACvE,OAAOA,IAAI,CAACC,IAAI,qLAAKV,OAAO,CAACa,EAAO;AACtC,CAAC;AAGM,MAAMC,aAAa,IAAUL,IAAoB,IAAyB;IAC/E,OAAOA,IAAI,CAACC,IAAI,qLAAKV,OAAO,CAACe,SAAc;AAC7C,CAAC;AAGM,MAAMC,IAAI,IAAUC,IAAqB,IAAoB;IAClE,OAAO;QACL,CAACd,cAAc,CAAA,EAAGA,cAAc;QAChCO,IAAI,kLAAEV,OAAO,CAACa,EAAO;QACrBI,IAAI;QACJ,CAACnB,IAAI,CAACoB,kJAAM,CAAA,GAAC;YACX,6JAAOrB,OAAAA,AAAI,mJACTC,IAAI,CAACqB,GAAAA,AAAI,EAACjB,iBAAiB,CAAC,oJAC5BJ,IAAI,CAACsB,KAAAA,AAAO,oJAACtB,IAAI,CAACqB,EAAAA,AAAI,kLAACnB,OAAO,CAACa,EAAO,CAAC,CAAC,oJACxCf,IAAI,CAACsB,KAAO,AAAPA,oJAAQtB,IAAI,CAACqB,EAAAA,AAAI,EAACF,IAAI,CAAC,CAAC,oJAC7BnB,IAAI,CAACuB,IAAAA,AAAM,EAAC,IAAI,CAAC,CAClB;QACH,CAAC;QACD,CAAC1B,KAAK,CAACuB,kJAAM,CAAA,EAAEI,IAAa;YAC1B,OAAOhB,UAAU,CAACgB,IAAI,CAAC,IAAIA,IAAI,CAACZ,IAAI,qLAAKV,OAAO,CAACa,EAAO,uJAAIlB,KAAK,CAAC4B,GAAAA,AAAM,EAACN,IAAI,EAAEK,IAAI,CAACL,IAAI,CAAC;QAC3F;KACD;AACH,CAAC;AAED,MAAMO,eAAe,GAAA,WAAA,yJAAG3B,OAAAA,AAAI,EAAA,WAAA,qJAC1BC,IAAI,CAACqB,EAAAA,AAAI,EAACjB,iBAAiB,CAAC,EAAA,WAAA,qJAC5BJ,IAAI,CAACsB,KAAAA,AAAO,EAAA,WAAA,GAACtB,IAAI,CAACqB,oJAAAA,AAAI,kLAACnB,OAAO,CAACe,SAAc,CAAC,CAAC,EAAA,WAAA,qJAC/CjB,IAAI,CAACsB,KAAAA,AAAO,EAAA,WAAA,qJAACtB,IAAI,CAACqB,EAAAA,AAAI,EAAC,aAAa,CAAC,CAAC,CACvC;AAGM,MAAMM,WAAW,GAAoB;IAC1C,CAACtB,cAAc,CAAA,EAAGA,cAAc;IAChCO,IAAI,kLAAEV,OAAO,CAACe,SAAc;IAC5B,+IAACjB,IAAI,CAACoB,IAAM,CAAA,GAAC;QACX,OAAOM,eAAe;IACxB,CAAC;IACD,gJAAC7B,KAAK,CAACuB,GAAM,CAAA,EAAEI,IAAa;QAC1B,OAAOhB,UAAU,CAACgB,IAAI,CAAC,IAAIA,IAAI,CAACZ,IAAI,qLAAKV,OAAO,CAACe,SAAc;IACjE;CACD;AAED,MAAMW,WAAW,GAAA,WAAA,wJAAG7B,QAAAA,AAAI,EAAA,WAAA,qJACtBC,IAAI,CAACqB,EAAI,AAAJA,EAAKjB,iBAAiB,CAAC,EAAA,WAAA,OAC5BJ,IAAI,CAACsB,mJAAAA,AAAO,EAAA,WAAA,qJAACtB,IAAI,CAACqB,EAAAA,AAAI,kLAACnB,OAAO,CAACW,KAAU,CAAC,CAAC,EAAA,WAAA,qJAC3Cb,IAAI,CAACsB,KAAAA,AAAO,EAAA,WAAA,qJAACtB,IAAI,CAACqB,EAAAA,AAAI,EAAC,SAAS,CAAC,CAAC,CACnC;AAGM,MAAMQ,OAAO,GAAoB;IACtC,CAACxB,cAAc,CAAA,EAAGA,cAAc;IAChCO,IAAI,kLAAEV,OAAO,CAACW,KAAU;IACxB,8IAACb,IAAI,CAACoB,KAAM,CAAA,GAAC;QACX,OAAOQ,WAAW;IACpB,CAAC;IACD,gJAAC/B,KAAK,CAACuB,GAAM,CAAA,EAAEI,IAAa;QAC1B,OAAOhB,UAAU,CAACgB,IAAI,CAAC,IAAIA,IAAI,CAACZ,IAAI,qLAAKV,OAAO,CAACW,KAAU;IAC7D;CACD;AAGM,MAAMiB,SAAS,IAAUC,KAAwB,IAAoB;IAC1E,OAAQA,KAAK,CAACnB,IAAI;QAChB,kLAAKT,UAAoB,EAAR,CAAC6B;YAAS;gBACzB,OAAOd,IAAI,mJAACpB,IAAI,CAACmC,EAAAA,AAAI,EAACF,KAAK,CAACG,KAAK,CAAC,CAAC;YACrC;QACA,iLAAK/B,UAAmB,EAAP,CAACgC;YAAQ;gBACxB,OAAOjB,IAAI,mJAACpB,IAAI,CAACsC,CAAAA,AAAG,EAACL,KAAK,CAACM,MAAM,CAAC,CAAC;YACrC;QACA,kLAAKlC,YAAY,CAACmC,EAAY;YAAE;gBAC9B,OAAOpB,IAAI,EAACpB,IAAI,CAACyC,wJAAAA,AAAS,EAACR,KAAK,CAACS,OAAO,CAAC,CAAC;YAC5C;QACA,kLAAKrC,YAAY,CAACsC,AAAU;YAAE;gBAC5B,OAAOvB,IAAI,mJAACpB,IAAI,CAAC4C,KAAAA,AAAO,EAACX,KAAK,CAACY,KAAK,CAAC,CAAC;YACxC;QACA,kLAAKxC,WAAqB,CAAT,CAACyC;YAAU;gBAC1B,MAAM,IAAIC,KAAK,CACb,oGAAoG,CACrG;YACH;IACF;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "file": "tExit.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/tExit.ts"], "sourcesContent": ["import * as Equal from \"../../Equal.js\"\nimport type * as FiberId from \"../../FiberId.js\"\nimport { pipe } from \"../../Function.js\"\nimport * as Hash from \"../../Hash.js\"\nimport { hasProperty } from \"../../Predicate.js\"\nimport type * as Types from \"../../Types.js\"\nimport * as OpCodes from \"./opCodes/tExit.js\"\n\n/** @internal */\nconst TExitSymbolKey = \"effect/TExit\"\n\n/** @internal */\nexport const TExitTypeId = Symbol.for(TExitSymbolKey)\n\n/** @internal */\nexport type TExitTypeId = typeof TExitTypeId\n\n/** @internal */\nexport type TExit<A, E = never> = Fail<E> | Die | Interrupt | Succeed<A> | Retry\n\n/** @internal */\nexport declare namespace TExit {\n  /** @internal */\n  export interface Variance<out A, out E> {\n    readonly [TExitTypeId]: {\n      readonly _A: Types.Covariant<A>\n      readonly _E: Types.Covariant<E>\n    }\n  }\n}\n\nconst variance = {\n  /* c8 ignore next */\n  _A: (_: never) => _,\n  /* c8 ignore next */\n  _E: (_: never) => _\n}\n\n/** @internal */\nexport interface Fail<out E> extends TExit.Variance<never, E>, Equal.Equal {\n  readonly _tag: OpCodes.OP_FAIL\n  readonly error: E\n}\n\n/** @internal */\nexport interface Die extends TExit.Variance<never, never>, Equal.Equal {\n  readonly _tag: OpCodes.OP_DIE\n  readonly defect: unknown\n}\n\n/** @internal */\nexport interface Interrupt extends TExit.Variance<never, never>, Equal.Equal {\n  readonly _tag: OpCodes.OP_INTERRUPT\n  readonly fiberId: FiberId.FiberId\n}\n\n/** @internal */\nexport interface Succeed<out A> extends TExit.Variance<A, never>, Equal.Equal {\n  readonly _tag: OpCodes.OP_SUCCEED\n  readonly value: A\n}\n\n/** @internal */\nexport interface Retry extends TExit.Variance<never, never>, Equal.Equal {\n  readonly _tag: OpCodes.OP_RETRY\n}\n\n/** @internal */\nexport const isExit = (u: unknown): u is TExit<unknown, unknown> => hasProperty(u, TExitTypeId)\n\n/** @internal */\nexport const isFail = <A, E>(self: TExit<A, E>): self is Fail<E> => {\n  return self._tag === OpCodes.OP_FAIL\n}\n\n/** @internal */\nexport const isDie = <A, E>(self: TExit<A, E>): self is Die => {\n  return self._tag === OpCodes.OP_DIE\n}\n\n/** @internal */\nexport const isInterrupt = <A, E>(self: TExit<A, E>): self is Interrupt => {\n  return self._tag === OpCodes.OP_INTERRUPT\n}\n\n/** @internal */\nexport const isSuccess = <A, E>(self: TExit<A, E>): self is Succeed<A> => {\n  return self._tag === OpCodes.OP_SUCCEED\n}\n\n/** @internal */\nexport const isRetry = <A, E>(self: TExit<A, E>): self is Retry => {\n  return self._tag === OpCodes.OP_RETRY\n}\n\n/** @internal */\nexport const fail = <E>(error: E): TExit<never, E> => ({\n  [TExitTypeId]: variance,\n  _tag: OpCodes.OP_FAIL,\n  error,\n  [Hash.symbol](): number {\n    return pipe(\n      Hash.hash(TExitSymbolKey),\n      Hash.combine(Hash.hash(OpCodes.OP_FAIL)),\n      Hash.combine(Hash.hash(error)),\n      Hash.cached(this)\n    )\n  },\n  [Equal.symbol](that: unknown): boolean {\n    return isExit(that) && that._tag === OpCodes.OP_FAIL && Equal.equals(error, that.error)\n  }\n})\n\n/** @internal */\nexport const die = (defect: unknown): TExit<never> => ({\n  [TExitTypeId]: variance,\n  _tag: OpCodes.OP_DIE,\n  defect,\n  [Hash.symbol](): number {\n    return pipe(\n      Hash.hash(TExitSymbolKey),\n      Hash.combine(Hash.hash(OpCodes.OP_DIE)),\n      Hash.combine(Hash.hash(defect)),\n      Hash.cached(this)\n    )\n  },\n  [Equal.symbol](that: unknown): boolean {\n    return isExit(that) && that._tag === OpCodes.OP_DIE && Equal.equals(defect, that.defect)\n  }\n})\n\n/** @internal */\nexport const interrupt = (fiberId: FiberId.FiberId): TExit<never> => ({\n  [TExitTypeId]: variance,\n  _tag: OpCodes.OP_INTERRUPT,\n  fiberId,\n  [Hash.symbol](): number {\n    return pipe(\n      Hash.hash(TExitSymbolKey),\n      Hash.combine(Hash.hash(OpCodes.OP_INTERRUPT)),\n      Hash.combine(Hash.hash(fiberId)),\n      Hash.cached(this)\n    )\n  },\n  [Equal.symbol](that: unknown): boolean {\n    return isExit(that) && that._tag === OpCodes.OP_INTERRUPT && Equal.equals(fiberId, that.fiberId)\n  }\n})\n\n/** @internal */\nexport const succeed = <A>(value: A): TExit<A> => ({\n  [TExitTypeId]: variance,\n  _tag: OpCodes.OP_SUCCEED,\n  value,\n  [Hash.symbol](): number {\n    return pipe(\n      Hash.hash(TExitSymbolKey),\n      Hash.combine(Hash.hash(OpCodes.OP_SUCCEED)),\n      Hash.combine(Hash.hash(value)),\n      Hash.cached(this)\n    )\n  },\n  [Equal.symbol](that: unknown): boolean {\n    return isExit(that) && that._tag === OpCodes.OP_SUCCEED && Equal.equals(value, that.value)\n  }\n})\n\nconst retryHash = pipe(\n  Hash.hash(TExitSymbolKey),\n  Hash.combine(Hash.hash(OpCodes.OP_RETRY)),\n  Hash.combine(Hash.hash(\"retry\"))\n)\n\n/** @internal */\nexport const retry: TExit<never> = {\n  [TExitTypeId]: variance,\n  _tag: OpCodes.OP_RETRY,\n  [Hash.symbol](): number {\n    return retryHash\n  },\n  [Equal.symbol](that: unknown): boolean {\n    return isExit(that) && isRetry(that)\n  }\n}\n\nconst void_: TExit<void> = succeed(undefined)\nexport {\n  /** @internal */\n  void_ as void\n}\n"], "names": ["Equal", "pipe", "Hash", "hasProperty", "OpCodes", "TExitSymbolKey", "TExitTypeId", "Symbol", "for", "variance", "_A", "_", "_E", "isExit", "u", "isFail", "self", "_tag", "OP_FAIL", "isDie", "OP_DIE", "isInterrupt", "OP_INTERRUPT", "isSuccess", "OP_SUCCEED", "isRetry", "OP_RETRY", "fail", "error", "symbol", "hash", "combine", "cached", "that", "equals", "die", "defect", "interrupt", "fiberId", "succeed", "value", "retryHash", "retry", "void_", "undefined", "void"], "mappings": ";;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,gBAAgB;AAEvC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,OAAO,KAAKC,IAAI,MAAM,eAAe;AACrC,SAASC,WAAW,QAAQ,oBAAoB;AAEhD,OAAO,KAAKC,OAAO,MAAM,oBAAoB;;;;;;AAE7C,cAAA,GACA,MAAMC,cAAc,GAAG,cAAc;AAG9B,MAAMC,WAAW,GAAA,WAAA,GAAGC,MAAM,CAACC,GAAG,CAACH,cAAc,CAAC;AAmBrD,MAAMI,QAAQ,GAAG;IACf,kBAAA,GACAC,EAAE,GAAGC,CAAQ,GAAKA,CAAC;IACnB,kBAAA,GACAC,EAAE,GAAGD,CAAQ,GAAKA;CACnB;AAgCM,MAAME,MAAM,GAAIC,CAAU,2JAAmCX,cAAAA,AAAW,EAACW,CAAC,EAAER,WAAW,CAAC;AAGxF,MAAMS,MAAM,IAAUC,IAAiB,IAAqB;IACjE,OAAOA,IAAI,CAACC,IAAI,kLAAKb,OAAO,CAACc,EAAO;AACtC,CAAC;AAGM,MAAMC,KAAK,GAAUH,IAAiB,IAAiB;IAC5D,OAAOA,IAAI,CAACC,IAAI,kLAAKb,OAAO,CAACgB,CAAM;AACrC,CAAC;AAGM,MAAMC,WAAW,IAAUL,IAAiB,IAAuB;IACxE,OAAOA,IAAI,CAACC,IAAI,kLAAKb,OAAO,CAACkB,OAAY;AAC3C,CAAC;AAGM,MAAMC,SAAS,IAAUP,IAAiB,IAAwB;IACvE,OAAOA,IAAI,CAACC,IAAI,kLAAKb,OAAO,CAACoB,KAAU;AACzC,CAAC;AAGM,MAAMC,OAAO,IAAUT,IAAiB,IAAmB;IAChE,OAAOA,IAAI,CAACC,IAAI,kLAAKb,OAAO,CAACsB,GAAQ;AACvC,CAAC;AAGM,MAAMC,IAAI,IAAOC,KAAQ,GAAA,CAAuB;QACrD,CAACtB,WAAW,CAAA,EAAGG,QAAQ;QACvBQ,IAAI,+KAAEb,OAAO,CAACc,EAAO;QACrBU,KAAK;QACL,+IAAC1B,IAAI,CAAC2B,IAAM,CAAA,GAAC;YACX,6JAAO5B,OAAAA,AAAI,mJACTC,IAAI,CAAC4B,GAAAA,AAAI,EAACzB,cAAc,CAAC,oJACzBH,IAAI,CAAC6B,KAAAA,AAAO,oJAAC7B,IAAI,CAAC4B,EAAAA,AAAI,+KAAC1B,OAAO,CAACc,EAAO,CAAC,CAAC,oJACxChB,IAAI,CAAC6B,KAAAA,AAAO,oJAAC7B,IAAI,CAAC4B,EAAAA,AAAI,EAACF,KAAK,CAAC,CAAC,MAC9B1B,IAAI,CAAC8B,kJAAAA,AAAM,EAAC,IAAI,CAAC,CAClB;QACH,CAAC;QACD,gJAAChC,KAAK,CAAC6B,GAAM,CAAA,EAAEI,IAAa;YAC1B,OAAOpB,MAAM,CAACoB,IAAI,CAAC,IAAIA,IAAI,CAAChB,IAAI,iLAAKb,OAAO,CAACc,GAAO,IAAIlB,KAAK,CAACkC,sJAAAA,AAAM,EAACN,KAAK,EAAEK,IAAI,CAACL,KAAK,CAAC;QACzF;KACD,CAAC;AAGK,MAAMO,GAAG,IAAIC,MAAe,GAAA,CAAoB;QACrD,CAAC9B,WAAW,CAAA,EAAGG,QAAQ;QACvBQ,IAAI,+KAAEb,OAAO,CAACgB,CAAM;QACpBgB,MAAM;QACN,+IAAClC,IAAI,CAAC2B,IAAM,CAAA,GAAC;YACX,6JAAO5B,OAAAA,AAAI,MACTC,IAAI,CAAC4B,gJAAAA,AAAI,EAACzB,cAAc,CAAC,oJACzBH,IAAI,CAAC6B,KAAAA,AAAO,oJAAC7B,IAAI,CAAC4B,EAAAA,AAAI,+KAAC1B,OAAO,CAACgB,CAAM,CAAC,CAAC,oJACvClB,IAAI,CAAC6B,KAAAA,AAAO,oJAAC7B,IAAI,CAAC4B,EAAAA,AAAI,EAACM,MAAM,CAAC,CAAC,oJAC/BlC,IAAI,CAAC8B,IAAAA,AAAM,EAAC,IAAI,CAAC,CAClB;QACH,CAAC;QACD,gJAAChC,KAAK,CAAC6B,GAAM,CAAA,EAAEI,IAAa;YAC1B,OAAOpB,MAAM,CAACoB,IAAI,CAAC,IAAIA,IAAI,CAAChB,IAAI,kLAAKb,OAAO,CAACgB,CAAM,QAAIpB,KAAK,CAACkC,kJAAAA,AAAM,EAACE,MAAM,EAAEH,IAAI,CAACG,MAAM,CAAC;QAC1F;KACD,CAAC;AAGK,MAAMC,SAAS,IAAIC,OAAwB,GAAA,CAAoB;QACpE,CAAChC,WAAW,CAAA,EAAGG,QAAQ;QACvBQ,IAAI,+KAAEb,OAAO,CAACkB,OAAY;QAC1BgB,OAAO;QACP,+IAACpC,IAAI,CAAC2B,IAAM,CAAA,GAAC;YACX,6JAAO5B,OAAAA,AAAI,oJACTC,IAAI,CAAC4B,EAAAA,AAAI,EAACzB,cAAc,CAAC,GACzBH,IAAI,CAAC6B,sJAAAA,AAAO,oJAAC7B,IAAI,CAAC4B,EAAAA,AAAI,+KAAC1B,OAAO,CAACkB,OAAY,CAAC,CAAC,oJAC7CpB,IAAI,CAAC6B,KAAAA,AAAO,EAAC7B,IAAI,CAAC4B,oJAAAA,AAAI,EAACQ,OAAO,CAAC,CAAC,oJAChCpC,IAAI,CAAC8B,IAAAA,AAAM,EAAC,IAAI,CAAC,CAClB;QACH,CAAC;QACD,gJAAChC,KAAK,CAAC6B,GAAM,CAAA,EAAEI,IAAa;YAC1B,OAAOpB,MAAM,CAACoB,IAAI,CAAC,IAAIA,IAAI,CAAChB,IAAI,kLAAKb,OAAO,CAACkB,OAAY,uJAAItB,KAAK,CAACkC,GAAAA,AAAM,EAACI,OAAO,EAAEL,IAAI,CAACK,OAAO,CAAC;QAClG;KACD,CAAC;AAGK,MAAMC,OAAO,IAAOC,KAAQ,GAAA,CAAgB;QACjD,CAAClC,WAAW,CAAA,EAAGG,QAAQ;QACvBQ,IAAI,+KAAEb,OAAO,CAACoB,KAAU;QACxBgB,KAAK;QACL,CAACtC,IAAI,CAAC2B,kJAAM,CAAA,GAAC;YACX,6JAAO5B,OAAAA,AAAI,MACTC,IAAI,CAAC4B,gJAAAA,AAAI,EAACzB,cAAc,CAAC,oJACzBH,IAAI,CAAC6B,KAAAA,AAAO,oJAAC7B,IAAI,CAAC4B,EAAAA,AAAI,EAAC1B,OAAO,CAACoB,kLAAU,CAAC,CAAC,oJAC3CtB,IAAI,CAAC6B,KAAAA,AAAO,oJAAC7B,IAAI,CAAC4B,EAAAA,AAAI,EAACU,KAAK,CAAC,CAAC,EAC9BtC,IAAI,CAAC8B,sJAAAA,AAAM,EAAC,IAAI,CAAC,CAClB;QACH,CAAC;QACD,gJAAChC,KAAK,CAAC6B,GAAM,CAAA,EAAEI,IAAa;YAC1B,OAAOpB,MAAM,CAACoB,IAAI,CAAC,IAAIA,IAAI,CAAChB,IAAI,kLAAKb,OAAO,CAACoB,KAAU,uJAAIxB,KAAK,CAACkC,GAAAA,AAAM,EAACM,KAAK,EAAEP,IAAI,CAACO,KAAK,CAAC;QAC5F;KACD,CAAC;AAEF,MAAMC,SAAS,GAAA,WAAA,yJAAGxC,OAAAA,AAAI,EAAA,WAAA,GACpBC,IAAI,CAAC4B,oJAAAA,AAAI,EAACzB,cAAc,CAAC,EAAA,WAAA,qJACzBH,IAAI,CAAC6B,KAAAA,AAAO,EAAA,WAAA,qJAAC7B,IAAI,CAAC4B,EAAAA,AAAI,+KAAC1B,OAAO,CAACsB,GAAQ,CAAC,CAAC,EAAA,WAAA,GACzCxB,IAAI,CAAC6B,uJAAAA,AAAO,EAAA,WAAA,qJAAC7B,IAAI,CAAC4B,EAAAA,AAAI,EAAC,OAAO,CAAC,CAAC,CACjC;AAGM,MAAMY,KAAK,GAAiB;IACjC,CAACpC,WAAW,CAAA,EAAGG,QAAQ;IACvBQ,IAAI,+KAAEb,OAAO,CAACsB,GAAQ;IACtB,+IAACxB,IAAI,CAAC2B,IAAM,CAAA,GAAC;QACX,OAAOY,SAAS;IAClB,CAAC;IACD,gJAACzC,KAAK,CAAC6B,GAAM,CAAA,EAAEI,IAAa;QAC1B,OAAOpB,MAAM,CAACoB,IAAI,CAAC,IAAIR,OAAO,CAACQ,IAAI,CAAC;IACtC;CACD;AAED,MAAMU,KAAK,GAAA,WAAA,GAAgBJ,OAAO,CAACK,SAAS,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "file": "tryCommit.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/tryCommit.ts"], "sourcesContent": ["import type * as Exit from \"../../Exit.js\"\nimport type * as Journal from \"./journal.js\"\nimport * as OpCodes from \"./opCodes/tryCommit.js\"\n\n/** @internal */\nexport type TryCommit<A, E = never> = Done<A, E> | Suspend\n\n/** @internal */\nexport interface Done<out A, out E> {\n  readonly _tag: OpCodes.OP_DONE\n  readonly exit: Exit.Exit<A, E>\n}\n\n/** @internal */\nexport interface Suspend {\n  readonly _tag: OpCodes.OP_SUSPEND\n  readonly journal: Journal.Journal\n}\n\n/** @internal */\nexport const done = <A, E>(exit: Exit.Exit<A, E>): TryCommit<A, E> => {\n  return {\n    _tag: OpCodes.OP_DONE,\n    exit\n  }\n}\n\n/** @internal */\nexport const suspend = (journal: Journal.Journal): TryCommit<never> => {\n  return {\n    _tag: OpCodes.OP_SUSPEND,\n    journal\n  }\n}\n"], "names": ["OpCodes", "done", "exit", "_tag", "OP_DONE", "suspend", "journal", "OP_SUSPEND"], "mappings": ";;;;AAEA,OAAO,KAAKA,OAAO,MAAM,wBAAwB;;AAkB1C,MAAMC,IAAI,IAAUC,IAAqB,IAAqB;IACnE,OAAO;QACLC,IAAI,mLAAEH,OAAO,CAACI,EAAO;QACrBF;KACD;AACH,CAAC;AAGM,MAAMG,OAAO,IAAIC,OAAwB,IAAsB;IACpE,OAAO;QACLH,IAAI,mLAAEH,OAAO,CAACO,KAAU;QACxBD;KACD;AACH,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "file": "txnId.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/txnId.ts"], "sourcesContent": ["/** @internal */\nexport type TxnId = number & {\n  readonly TransactioId: unique symbol\n}\n\n/** @internal */\nconst txnCounter = { ref: 0 }\n\n/** @internal */\nexport const make = (): TxnId => {\n  const newId = txnCounter.ref + 1\n  txnCounter.ref = newId\n  return newId as TxnId\n}\n"], "names": ["txnCounter", "ref", "make", "newId"], "mappings": "AAKA,cAAA;;;AACA,MAAMA,UAAU,GAAG;IAAEC,GAAG,EAAE;AAAC,CAAE;AAGtB,MAAMC,IAAI,GAAGA,CAAA,KAAY;IAC9B,MAAMC,KAAK,GAAGH,UAAU,CAACC,GAAG,GAAG,CAAC;IAChCD,UAAU,CAACC,GAAG,GAAGE,KAAK;IACtB,OAAOA,KAAc;AACvB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "file": "core.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/core.ts"], "sourcesContent": ["import * as Cause from \"../../Cause.js\"\nimport * as Context from \"../../Context.js\"\nimport * as Effect from \"../../Effect.js\"\nimport * as Either from \"../../Either.js\"\nimport * as Equal from \"../../Equal.js\"\nimport * as Exit from \"../../Exit.js\"\nimport type * as FiberId from \"../../FiberId.js\"\nimport * as FiberRef from \"../../FiberRef.js\"\nimport type { LazyArg } from \"../../Function.js\"\nimport { constVoid, dual, pipe } from \"../../Function.js\"\nimport * as Hash from \"../../Hash.js\"\nimport type * as Option from \"../../Option.js\"\nimport { pipeArguments } from \"../../Pipeable.js\"\nimport { hasProperty } from \"../../Predicate.js\"\nimport type * as Scheduler from \"../../Scheduler.js\"\nimport type * as STM from \"../../STM.js\"\nimport { internalCall, YieldWrap } from \"../../Utils.js\"\nimport { ChannelTypeId } from \"../core-stream.js\"\nimport { withFiberRuntime } from \"../core.js\"\nimport { effectVariance, StreamTypeId } from \"../effectable.js\"\nimport { OP_COMMIT } from \"../opCodes/effect.js\"\nimport { SingleShotGen } from \"../singleShotGen.js\"\nimport { SinkTypeId } from \"../sink.js\"\nimport * as Journal from \"./journal.js\"\nimport * as OpCodes from \"./opCodes/stm.js\"\nimport * as TExitOpCodes from \"./opCodes/tExit.js\"\nimport * as TryCommitOpCodes from \"./opCodes/tryCommit.js\"\nimport * as STMState from \"./stmState.js\"\nimport * as TExit from \"./tExit.js\"\nimport * as TryCommit from \"./tryCommit.js\"\nimport * as TxnId from \"./txnId.js\"\n\n/** @internal */\nconst STMSymbolKey = \"effect/STM\"\n\n/** @internal */\nexport const STMTypeId: STM.STMTypeId = Symbol.for(\n  STMSymbolKey\n) as STM.STMTypeId\n\n/** @internal */\nexport type Primitive =\n  | STMEffect\n  | STMOnFailure\n  | STMOnRetry\n  | STMOnSuccess\n  | STMProvide\n  | STMSync\n  | STMSucceed\n  | STMRetry\n  | STMFail\n  | STMDie\n  | STMInterrupt\n\n/** @internal */\ntype Op<Tag extends string, Body = {}> = STM.STM<never> & Body & {\n  readonly _op: OP_COMMIT\n  readonly effect_instruction_i0: Tag\n}\n\n/** @internal */\ninterface STMEffect extends\n  Op<OpCodes.OP_WITH_STM_RUNTIME, {\n    readonly effect_instruction_i1: (\n      runtime: STMDriver<unknown, unknown, unknown>\n    ) => STM.STM<unknown, unknown, unknown>\n  }>\n{}\n\n/** @internal */\ninterface STMOnFailure extends\n  Op<OpCodes.OP_ON_FAILURE, {\n    readonly effect_instruction_i1: STM.STM<unknown, unknown, unknown>\n    readonly effect_instruction_i2: (error: unknown) => STM.STM<unknown, unknown, unknown>\n  }>\n{}\n\n/** @internal */\ninterface STMOnRetry extends\n  Op<OpCodes.OP_ON_RETRY, {\n    readonly effect_instruction_i1: STM.STM<unknown, unknown, unknown>\n    readonly effect_instruction_i2: () => STM.STM<unknown, unknown, unknown>\n  }>\n{}\n\n/** @internal */\ninterface STMOnSuccess extends\n  Op<OpCodes.OP_ON_SUCCESS, {\n    readonly effect_instruction_i1: STM.STM<unknown, unknown, unknown>\n    readonly effect_instruction_i2: (a: unknown) => STM.STM<unknown, unknown, unknown>\n  }>\n{}\n\n/** @internal */\ninterface STMProvide extends\n  Op<OpCodes.OP_PROVIDE, {\n    readonly effect_instruction_i1: STM.STM<unknown, unknown, unknown>\n    readonly effect_instruction_i2: (context: Context.Context<unknown>) => Context.Context<unknown>\n  }>\n{}\n\n/** @internal */\ninterface STMSync extends\n  Op<OpCodes.OP_SYNC, {\n    readonly effect_instruction_i1: () => unknown\n  }>\n{}\n\n/** @internal */\ninterface STMSucceed extends\n  Op<OpCodes.OP_SUCCEED, {\n    readonly effect_instruction_i1: unknown\n  }>\n{}\n\n/** @internal */\ninterface STMRetry extends Op<OpCodes.OP_RETRY, {}> {}\n\n/** @internal */\ninterface STMFail extends\n  Op<OpCodes.OP_FAIL, {\n    readonly effect_instruction_i1: LazyArg<unknown>\n  }>\n{}\n\n/** @internal */\ninterface STMDie extends\n  Op<OpCodes.OP_DIE, {\n    readonly effect_instruction_i1: LazyArg<unknown>\n  }>\n{}\n\n/** @internal */\ninterface STMInterrupt extends\n  Op<OpCodes.OP_INTERRUPT, {\n    readonly effect_instruction_i1: FiberId.Runtime\n  }>\n{}\n\nconst stmVariance = {\n  /* c8 ignore next */\n  _R: (_: never) => _,\n  /* c8 ignore next */\n  _E: (_: never) => _,\n  /* c8 ignore next */\n  _A: (_: never) => _\n}\n\n/** @internal */\nclass STMPrimitive implements STM.STM<any, any, any> {\n  public _op = OP_COMMIT\n  public effect_instruction_i1: any = undefined\n  public effect_instruction_i2: any = undefined;\n  [Effect.EffectTypeId]: any;\n  [StreamTypeId]: any;\n  [SinkTypeId]: any;\n  [ChannelTypeId]: any\n  get [STMTypeId]() {\n    return stmVariance\n  }\n  constructor(readonly effect_instruction_i0: Primitive[\"effect_instruction_i0\"]) {\n    this[Effect.EffectTypeId] = effectVariance\n    this[StreamTypeId] = stmVariance\n    this[SinkTypeId] = stmVariance\n    this[ChannelTypeId] = stmVariance\n  }\n  [Equal.symbol](this: {}, that: unknown) {\n    return this === that\n  }\n  [Hash.symbol](this: {}) {\n    return Hash.cached(this, Hash.random(this))\n  }\n  [Symbol.iterator]() {\n    return new SingleShotGen(new YieldWrap(this)) as any\n  }\n  commit(this: STM.STM<any, any, any>): Effect.Effect<any, any, any> {\n    return unsafeAtomically(this, constVoid, constVoid)\n  }\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const isSTM = (u: unknown): u is STM.STM<unknown, unknown, unknown> => hasProperty(u, STMTypeId)\n\n/** @internal */\nexport const commit = <A, E, R>(self: STM.STM<A, E, R>): Effect.Effect<A, E, R> =>\n  unsafeAtomically(self, constVoid, constVoid)\n\n/** @internal */\nexport const unsafeAtomically = <A, E, R>(\n  self: STM.STM<A, E, R>,\n  onDone: (exit: Exit.Exit<A, E>) => unknown,\n  onInterrupt: LazyArg<unknown>\n): Effect.Effect<A, E, R> =>\n  withFiberRuntime((state) => {\n    const fiberId = state.id()\n    const env = state.getFiberRef(FiberRef.currentContext) as Context.Context<R>\n    const scheduler = state.getFiberRef(FiberRef.currentScheduler)\n    const priority = state.getFiberRef(FiberRef.currentSchedulingPriority)\n    const commitResult = tryCommitSync(fiberId, self, env, scheduler, priority)\n    switch (commitResult._tag) {\n      case TryCommitOpCodes.OP_DONE: {\n        onDone(commitResult.exit)\n        return commitResult.exit\n      }\n      case TryCommitOpCodes.OP_SUSPEND: {\n        const txnId = TxnId.make()\n        const state: { value: STMState.STMState<A, E> } = { value: STMState.running }\n        const effect = Effect.async(\n          (k: (effect: Effect.Effect<A, E, R>) => unknown): void =>\n            tryCommitAsync(fiberId, self, txnId, state, env, scheduler, priority, k)\n        )\n        return Effect.uninterruptibleMask((restore) =>\n          pipe(\n            restore(effect),\n            Effect.catchAllCause((cause) => {\n              let currentState = state.value\n              if (STMState.isRunning(currentState)) {\n                state.value = STMState.interrupted\n              }\n              currentState = state.value\n              if (STMState.isDone(currentState)) {\n                onDone(currentState.exit)\n                return currentState.exit\n              }\n              onInterrupt()\n              return Effect.failCause(cause)\n            })\n          )\n        )\n      }\n    }\n  })\n\n/** @internal */\nconst tryCommit = <A, E, R>(\n  fiberId: FiberId.FiberId,\n  stm: STM.STM<A, E, R>,\n  state: { value: STMState.STMState<A, E> },\n  env: Context.Context<R>,\n  scheduler: Scheduler.Scheduler,\n  priority: number\n): TryCommit.TryCommit<A, E> => {\n  const journal: Journal.Journal = new Map()\n  const tExit = new STMDriver(stm, journal, fiberId, env).run()\n  const analysis = Journal.analyzeJournal(journal)\n\n  if (analysis === Journal.JournalAnalysisReadWrite) {\n    Journal.commitJournal(journal)\n  } else if (analysis === Journal.JournalAnalysisInvalid) {\n    throw new Error(\n      \"BUG: STM.TryCommit.tryCommit - please report an issue at https://github.com/Effect-TS/effect/issues\"\n    )\n  }\n\n  switch (tExit._tag) {\n    case TExitOpCodes.OP_SUCCEED: {\n      state.value = STMState.fromTExit(tExit)\n      return completeTodos(Exit.succeed(tExit.value), journal, scheduler, priority)\n    }\n    case TExitOpCodes.OP_FAIL: {\n      state.value = STMState.fromTExit(tExit)\n      const cause = Cause.fail(tExit.error)\n      return completeTodos(\n        Exit.failCause(cause),\n        journal,\n        scheduler,\n        priority\n      )\n    }\n    case TExitOpCodes.OP_DIE: {\n      state.value = STMState.fromTExit(tExit)\n      const cause = Cause.die(tExit.defect)\n      return completeTodos(\n        Exit.failCause(cause),\n        journal,\n        scheduler,\n        priority\n      )\n    }\n    case TExitOpCodes.OP_INTERRUPT: {\n      state.value = STMState.fromTExit(tExit)\n      const cause = Cause.interrupt(fiberId)\n      return completeTodos(\n        Exit.failCause(cause),\n        journal,\n        scheduler,\n        priority\n      )\n    }\n    case TExitOpCodes.OP_RETRY: {\n      return TryCommit.suspend(journal)\n    }\n  }\n}\n\n/** @internal */\nconst tryCommitSync = <A, E, R>(\n  fiberId: FiberId.FiberId,\n  stm: STM.STM<A, E, R>,\n  env: Context.Context<R>,\n  scheduler: Scheduler.Scheduler,\n  priority: number\n): TryCommit.TryCommit<A, E> => {\n  const journal: Journal.Journal = new Map()\n  const tExit = new STMDriver(stm, journal, fiberId, env).run()\n  const analysis = Journal.analyzeJournal(journal)\n\n  if (analysis === Journal.JournalAnalysisReadWrite && TExit.isSuccess(tExit)) {\n    Journal.commitJournal(journal)\n  } else if (analysis === Journal.JournalAnalysisInvalid) {\n    throw new Error(\n      \"BUG: STM.TryCommit.tryCommitSync - please report an issue at https://github.com/Effect-TS/effect/issues\"\n    )\n  }\n\n  switch (tExit._tag) {\n    case TExitOpCodes.OP_SUCCEED: {\n      return completeTodos(Exit.succeed(tExit.value), journal, scheduler, priority)\n    }\n    case TExitOpCodes.OP_FAIL: {\n      const cause = Cause.fail(tExit.error)\n      return completeTodos(\n        Exit.failCause(cause),\n        journal,\n        scheduler,\n        priority\n      )\n    }\n    case TExitOpCodes.OP_DIE: {\n      const cause = Cause.die(tExit.defect)\n      return completeTodos(\n        Exit.failCause(cause),\n        journal,\n        scheduler,\n        priority\n      )\n    }\n    case TExitOpCodes.OP_INTERRUPT: {\n      const cause = Cause.interrupt(fiberId)\n      return completeTodos(\n        Exit.failCause(cause),\n        journal,\n        scheduler,\n        priority\n      )\n    }\n    case TExitOpCodes.OP_RETRY: {\n      return TryCommit.suspend(journal)\n    }\n  }\n}\n\n/** @internal */\nconst tryCommitAsync = <A, E, R>(\n  fiberId: FiberId.FiberId,\n  self: STM.STM<A, E, R>,\n  txnId: TxnId.TxnId,\n  state: { value: STMState.STMState<A, E> },\n  context: Context.Context<R>,\n  scheduler: Scheduler.Scheduler,\n  priority: number,\n  k: (effect: Effect.Effect<A, E, R>) => unknown\n) => {\n  if (STMState.isRunning(state.value)) {\n    const result = tryCommit(fiberId, self, state, context, scheduler, priority)\n    switch (result._tag) {\n      case TryCommitOpCodes.OP_DONE: {\n        completeTryCommit(result.exit, k)\n        break\n      }\n      case TryCommitOpCodes.OP_SUSPEND: {\n        Journal.addTodo(\n          txnId,\n          result.journal,\n          () => tryCommitAsync(fiberId, self, txnId, state, context, scheduler, priority, k)\n        )\n        break\n      }\n    }\n  }\n}\n\n/** @internal */\nconst completeTodos = <A, E>(\n  exit: Exit.Exit<A, E>,\n  journal: Journal.Journal,\n  scheduler: Scheduler.Scheduler,\n  priority: number\n): TryCommit.TryCommit<A, E> => {\n  const todos = Journal.collectTodos(journal)\n  if (todos.size > 0) {\n    scheduler.scheduleTask(() => Journal.execTodos(todos), priority)\n  }\n  return TryCommit.done(exit)\n}\n\n/** @internal */\nconst completeTryCommit = <A, E, R>(\n  exit: Exit.Exit<A, E>,\n  k: (effect: Effect.Effect<A, E, R>) => unknown\n): void => {\n  k(exit)\n}\n\n/** @internal */\ntype Continuation = STMOnFailure | STMOnSuccess | STMOnRetry\n\n/** @internal */\nexport const context = <R>(): STM.STM<Context.Context<R>, never, R> =>\n  effect<R, Context.Context<R>>((_, __, env) => env)\n\n/** @internal */\nexport const contextWith = <R0, R>(f: (environment: Context.Context<R0>) => R): STM.STM<R, never, R0> =>\n  map(context<R0>(), f)\n\n/** @internal */\nexport const contextWithSTM = <R0, A, E, R>(\n  f: (environment: Context.Context<R0>) => STM.STM<A, E, R>\n): STM.STM<A, E, R0 | R> => flatMap(context<R0>(), f)\n\n/** @internal */\nexport class STMDriver<in out R, out E, out A> {\n  private contStack: Array<Continuation> = []\n  private env: Context.Context<unknown>\n\n  constructor(\n    readonly self: STM.STM<A, E, R>,\n    readonly journal: Journal.Journal,\n    readonly fiberId: FiberId.FiberId,\n    r0: Context.Context<R>\n  ) {\n    this.env = r0 as Context.Context<unknown>\n  }\n\n  getEnv(): Context.Context<R> {\n    return this.env\n  }\n\n  pushStack(cont: Continuation) {\n    this.contStack.push(cont)\n  }\n\n  popStack() {\n    return this.contStack.pop()\n  }\n\n  nextSuccess() {\n    let current = this.popStack()\n    while (current !== undefined && current.effect_instruction_i0 !== OpCodes.OP_ON_SUCCESS) {\n      current = this.popStack()\n    }\n    return current\n  }\n\n  nextFailure() {\n    let current = this.popStack()\n    while (current !== undefined && current.effect_instruction_i0 !== OpCodes.OP_ON_FAILURE) {\n      current = this.popStack()\n    }\n    return current\n  }\n\n  nextRetry() {\n    let current = this.popStack()\n    while (current !== undefined && current.effect_instruction_i0 !== OpCodes.OP_ON_RETRY) {\n      current = this.popStack()\n    }\n    return current\n  }\n\n  run(): TExit.TExit<A, E> {\n    let curr = this.self as Primitive | Context.Tag<any, any> | Either.Either<any, any> | Option.Option<any> | undefined\n    let exit: TExit.TExit<unknown, unknown> | undefined = undefined\n    while (exit === undefined && curr !== undefined) {\n      try {\n        const current = curr\n        if (current) {\n          switch (current._op) {\n            case \"Tag\": {\n              curr = effect((_, __, env) => Context.unsafeGet(env, current)) as Primitive\n              break\n            }\n            case \"Left\": {\n              curr = fail(current.left) as Primitive\n              break\n            }\n            case \"None\": {\n              curr = fail(new Cause.NoSuchElementException()) as Primitive\n              break\n            }\n            case \"Right\": {\n              curr = succeed(current.right) as Primitive\n              break\n            }\n            case \"Some\": {\n              curr = succeed(current.value) as Primitive\n              break\n            }\n            case \"Commit\": {\n              switch (current.effect_instruction_i0) {\n                case OpCodes.OP_DIE: {\n                  exit = TExit.die(internalCall(() => current.effect_instruction_i1()))\n                  break\n                }\n                case OpCodes.OP_FAIL: {\n                  const cont = this.nextFailure()\n                  if (cont === undefined) {\n                    exit = TExit.fail(internalCall(() => current.effect_instruction_i1()))\n                  } else {\n                    curr = internalCall(() =>\n                      cont.effect_instruction_i2(\n                        internalCall(() => current.effect_instruction_i1())\n                      ) as Primitive\n                    )\n                  }\n                  break\n                }\n                case OpCodes.OP_RETRY: {\n                  const cont = this.nextRetry()\n                  if (cont === undefined) {\n                    exit = TExit.retry\n                  } else {\n                    curr = internalCall(() => cont.effect_instruction_i2() as Primitive)\n                  }\n                  break\n                }\n                case OpCodes.OP_INTERRUPT: {\n                  exit = TExit.interrupt(this.fiberId)\n                  break\n                }\n                case OpCodes.OP_WITH_STM_RUNTIME: {\n                  curr = internalCall(() =>\n                    current.effect_instruction_i1(this as STMDriver<unknown, unknown, unknown>) as Primitive\n                  )\n                  break\n                }\n                case OpCodes.OP_ON_SUCCESS:\n                case OpCodes.OP_ON_FAILURE:\n                case OpCodes.OP_ON_RETRY: {\n                  this.pushStack(current)\n                  curr = current.effect_instruction_i1 as Primitive\n                  break\n                }\n                case OpCodes.OP_PROVIDE: {\n                  const env = this.env\n                  this.env = internalCall(() => current.effect_instruction_i2(env))\n                  curr = pipe(\n                    current.effect_instruction_i1,\n                    ensuring(sync(() => (this.env = env)))\n                  ) as Primitive\n                  break\n                }\n                case OpCodes.OP_SUCCEED: {\n                  const value = current.effect_instruction_i1\n                  const cont = this.nextSuccess()\n                  if (cont === undefined) {\n                    exit = TExit.succeed(value)\n                  } else {\n                    curr = internalCall(() => cont.effect_instruction_i2(value) as Primitive)\n                  }\n                  break\n                }\n                case OpCodes.OP_SYNC: {\n                  const value = internalCall(() => current.effect_instruction_i1())\n                  const cont = this.nextSuccess()\n                  if (cont === undefined) {\n                    exit = TExit.succeed(value)\n                  } else {\n                    curr = internalCall(() => cont.effect_instruction_i2(value) as Primitive)\n                  }\n                  break\n                }\n              }\n              break\n            }\n          }\n        }\n      } catch (e) {\n        curr = die(e) as Primitive\n      }\n    }\n    return exit as TExit.TExit<A, E>\n  }\n}\n\n/** @internal */\nexport const catchAll = dual<\n  <E, B, E1, R1>(\n    f: (e: E) => STM.STM<B, E1, R1>\n  ) => <A, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<B | A, E1, R1 | R>,\n  <A, E, R, B, E1, R1>(\n    self: STM.STM<A, E, R>,\n    f: (e: E) => STM.STM<B, E1, R1>\n  ) => STM.STM<B | A, E1, R1 | R>\n>(2, (self, f) => {\n  const stm = new STMPrimitive(OpCodes.OP_ON_FAILURE)\n  stm.effect_instruction_i1 = self\n  stm.effect_instruction_i2 = f\n  return stm\n})\n\n/** @internal */\nexport const mapInputContext = dual<\n  <R0, R>(\n    f: (context: Context.Context<R0>) => Context.Context<R>\n  ) => <A, E>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<A, E, R0>,\n  <A, E, R0, R>(\n    self: STM.STM<A, E, R>,\n    f: (context: Context.Context<R0>) => Context.Context<R>\n  ) => STM.STM<A, E, R0>\n>(2, (self, f) => {\n  const stm = new STMPrimitive(OpCodes.OP_PROVIDE)\n  stm.effect_instruction_i1 = self\n  stm.effect_instruction_i2 = f\n  return stm\n})\n\n/** @internal */\nexport const die = (defect: unknown): STM.STM<never> => dieSync(() => defect)\n\n/** @internal */\nexport const dieMessage = (message: string): STM.STM<never> => dieSync(() => new Cause.RuntimeException(message))\n\n/** @internal */\nexport const dieSync = (evaluate: LazyArg<unknown>): STM.STM<never> => {\n  const stm = new STMPrimitive(OpCodes.OP_DIE)\n  stm.effect_instruction_i1 = evaluate\n  return stm as any\n}\n\n/** @internal */\nexport const effect = <R, A>(\n  f: (journal: Journal.Journal, fiberId: FiberId.FiberId, environment: Context.Context<R>) => A\n): STM.STM<A, never, R> => withSTMRuntime((_) => succeed(f(_.journal, _.fiberId, _.getEnv())))\n\n/** @internal */\nexport const ensuring = dual<\n  <R1, B>(finalizer: STM.STM<B, never, R1>) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E, R1 | R>,\n  <A, E, R, R1, B>(self: STM.STM<A, E, R>, finalizer: STM.STM<B, never, R1>) => STM.STM<A, E, R1 | R>\n>(2, (self, finalizer) =>\n  matchSTM(self, {\n    onFailure: (e) => zipRight(finalizer, fail(e)),\n    onSuccess: (a) => zipRight(finalizer, succeed(a))\n  }))\n\n/** @internal */\nexport const fail = <E>(error: E): STM.STM<never, E> => failSync(() => error)\n\n/** @internal */\nexport const failSync = <E>(evaluate: LazyArg<E>): STM.STM<never, E> => {\n  const stm = new STMPrimitive(OpCodes.OP_FAIL)\n  stm.effect_instruction_i1 = evaluate\n  return stm as any\n}\n\n/** @internal */\nexport const flatMap = dual<\n  <A, A2, E1, R1>(f: (a: A) => STM.STM<A2, E1, R1>) => <E, R>(self: STM.STM<A, E, R>) => STM.STM<A2, E1 | E, R1 | R>,\n  <A, E, R, A2, E1, R1>(self: STM.STM<A, E, R>, f: (a: A) => STM.STM<A2, E1, R1>) => STM.STM<A2, E1 | E, R1 | R>\n>(2, (self, f) => {\n  const stm = new STMPrimitive(OpCodes.OP_ON_SUCCESS)\n  stm.effect_instruction_i1 = self\n  stm.effect_instruction_i2 = f\n  return stm\n})\n\n/** @internal */\nexport const matchSTM = dual<\n  <E, A1, E1, R1, A, A2, E2, R2>(\n    options: {\n      readonly onFailure: (e: E) => STM.STM<A1, E1, R1>\n      readonly onSuccess: (a: A) => STM.STM<A2, E2, R2>\n    }\n  ) => <R>(self: STM.STM<A, E, R>) => STM.STM<A1 | A2, E1 | E2, R1 | R2 | R>,\n  <A, E, R, A1, E1, R1, A2, E2, R2>(\n    self: STM.STM<A, E, R>,\n    options: {\n      readonly onFailure: (e: E) => STM.STM<A1, E1, R1>\n      readonly onSuccess: (a: A) => STM.STM<A2, E2, R2>\n    }\n  ) => STM.STM<A1 | A2, E1 | E2, R1 | R2 | R>\n>(2, <A, E, R, A1, E1, R1, A2, E2, R2>(\n  self: STM.STM<A, E, R>,\n  { onFailure, onSuccess }: {\n    readonly onFailure: (e: E) => STM.STM<A1, E1, R1>\n    readonly onSuccess: (a: A) => STM.STM<A2, E2, R2>\n  }\n): STM.STM<A1 | A2, E1 | E2, R1 | R2 | R> =>\n  pipe(\n    self,\n    map(Either.right),\n    catchAll((e) => pipe(onFailure(e), map(Either.left))),\n    flatMap((either): STM.STM<A1 | A2, E1 | E2, R | R1 | R2> => {\n      switch (either._tag) {\n        case \"Left\": {\n          return succeed(either.left)\n        }\n        case \"Right\": {\n          return onSuccess(either.right)\n        }\n      }\n    })\n  ))\n\n/** @internal */\nexport const withSTMRuntime = <A, E = never, R = never>(\n  f: (runtime: STMDriver<unknown, unknown, unknown>) => STM.STM<A, E, R>\n): STM.STM<A, E, R> => {\n  const stm = new STMPrimitive(OpCodes.OP_WITH_STM_RUNTIME)\n  stm.effect_instruction_i1 = f\n  return stm\n}\n\n/** @internal */\nexport const interrupt: STM.STM<never> = withSTMRuntime((_) => {\n  const stm = new STMPrimitive(OpCodes.OP_INTERRUPT)\n  stm.effect_instruction_i1 = _.fiberId\n  return stm as any\n})\n\n/** @internal */\nexport const interruptAs = (fiberId: FiberId.FiberId): STM.STM<never> => {\n  const stm = new STMPrimitive(OpCodes.OP_INTERRUPT)\n  stm.effect_instruction_i1 = fiberId\n  return stm as any\n}\n\n/** @internal */\nexport const map = dual<\n  <A, B>(f: (a: A) => B) => <E, R>(self: STM.STM<A, E, R>) => STM.STM<B, E, R>,\n  <A, E, R, B>(self: STM.STM<A, E, R>, f: (a: A) => B) => STM.STM<B, E, R>\n>(2, (self, f) => pipe(self, flatMap((a) => sync(() => f(a)))))\n\n/** @internal */\nexport const orTry = dual<\n  <A1, E1, R1>(\n    that: LazyArg<STM.STM<A1, E1, R1>>\n  ) => <A, E, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<A1 | A, E1 | E, R1 | R>,\n  <A, E, R, A1, E1, R1>(\n    self: STM.STM<A, E, R>,\n    that: LazyArg<STM.STM<A1, E1, R1>>\n  ) => STM.STM<A1 | A, E1 | E, R1 | R>\n>(2, (self, that) => {\n  const stm = new STMPrimitive(OpCodes.OP_ON_RETRY)\n  stm.effect_instruction_i1 = self\n  stm.effect_instruction_i2 = that\n  return stm\n})\n\n/** @internal */\nexport const retry: STM.STM<never> = new STMPrimitive(OpCodes.OP_RETRY)\n\n/** @internal */\nexport const succeed = <A>(value: A): STM.STM<A> => {\n  const stm = new STMPrimitive(OpCodes.OP_SUCCEED)\n  stm.effect_instruction_i1 = value\n  return stm as any\n}\n\n/** @internal */\nexport const sync = <A>(evaluate: () => A): STM.STM<A> => {\n  const stm = new STMPrimitive(OpCodes.OP_SYNC)\n  stm.effect_instruction_i1 = evaluate\n  return stm as any\n}\n\n/** @internal */\nexport const zip = dual<\n  <A1, E1, R1>(\n    that: STM.STM<A1, E1, R1>\n  ) => <A, E, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<[A, A1], E1 | E, R1 | R>,\n  <A, E, R, A1, E1, R1>(\n    self: STM.STM<A, E, R>,\n    that: STM.STM<A1, E1, R1>\n  ) => STM.STM<[A, A1], E1 | E, R1 | R>\n>(2, (self, that) => pipe(self, zipWith(that, (a, a1) => [a, a1])))\n\n/** @internal */\nexport const zipLeft = dual<\n  <A1, E1, R1>(that: STM.STM<A1, E1, R1>) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E1 | E, R1 | R>,\n  <A, E, R, A1, E1, R1>(self: STM.STM<A, E, R>, that: STM.STM<A1, E1, R1>) => STM.STM<A, E1 | E, R1 | R>\n>(2, (self, that) => pipe(self, flatMap((a) => pipe(that, map(() => a)))))\n\n/** @internal */\nexport const zipRight = dual<\n  <A1, E1, R1>(that: STM.STM<A1, E1, R1>) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<A1, E1 | E, R1 | R>,\n  <A, E, R, A1, E1, R1>(self: STM.STM<A, E, R>, that: STM.STM<A1, E1, R1>) => STM.STM<A1, E1 | E, R1 | R>\n>(2, (self, that) => pipe(self, flatMap(() => that)))\n\n/** @internal */\nexport const zipWith = dual<\n  <A1, E1, R1, A, A2>(\n    that: STM.STM<A1, E1, R1>,\n    f: (a: A, b: A1) => A2\n  ) => <E, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<A2, E1 | E, R1 | R>,\n  <A, E, R, A1, E1, R1, A2>(\n    self: STM.STM<A, E, R>,\n    that: STM.STM<A1, E1, R1>,\n    f: (a: A, b: A1) => A2\n  ) => STM.STM<A2, E1 | E, R1 | R>\n>(\n  3,\n  (self, that, f) => pipe(self, flatMap((a) => pipe(that, map((b) => f(a, b)))))\n)\n"], "names": ["Cause", "Context", "Effect", "Either", "Equal", "Exit", "FiberRef", "constVoid", "dual", "pipe", "Hash", "pipeArguments", "hasProperty", "internalCall", "YieldWrap", "ChannelTypeId", "withFiberRuntime", "effectVariance", "StreamTypeId", "OP_COMMIT", "SingleShotGen", "SinkTypeId", "Journal", "OpCodes", "TExitOpCodes", "TryCommitOpCodes", "STMState", "TExit", "TryCommit", "TxnId", "STMSymbolKey", "STMTypeId", "Symbol", "for", "stmVariance", "_R", "_", "_E", "_A", "STMPrimitive", "effect_instruction_i0", "_op", "effect_instruction_i1", "undefined", "effect_instruction_i2", "EffectTypeId", "constructor", "symbol", "that", "cached", "random", "iterator", "commit", "unsafeAtomically", "arguments", "isSTM", "u", "self", "onDone", "onInterrupt", "state", "fiberId", "id", "env", "getFiberRef", "currentContext", "scheduler", "currentScheduler", "priority", "currentSchedulingPriority", "commitResult", "tryCommitSync", "_tag", "OP_DONE", "exit", "OP_SUSPEND", "txnId", "make", "value", "running", "effect", "async", "k", "tryCommitAsync", "uninterruptibleMask", "restore", "catchAllCause", "cause", "currentState", "isRunning", "interrupted", "isDone", "failCause", "tryCommit", "stm", "journal", "Map", "tExit", "STMDriver", "run", "analysis", "analyzeJournal", "JournalAnalysisReadWrite", "commitJournal", "JournalAnalysisInvalid", "Error", "OP_SUCCEED", "fromTExit", "completeTodos", "succeed", "OP_FAIL", "fail", "error", "OP_DIE", "die", "defect", "OP_INTERRUPT", "interrupt", "OP_RETRY", "suspend", "isSuccess", "context", "result", "completeTryCommit", "addTodo", "todos", "collectTodos", "size", "scheduleTask", "execTodos", "done", "__", "contextWith", "f", "map", "contextWithSTM", "flatMap", "contStack", "r0", "getEnv", "pushStack", "cont", "push", "popStack", "pop", "nextSuccess", "current", "OP_ON_SUCCESS", "nextFailure", "OP_ON_FAILURE", "nextRetry", "OP_ON_RETRY", "curr", "unsafeGet", "left", "NoSuchElementException", "right", "retry", "OP_WITH_STM_RUNTIME", "OP_PROVIDE", "ensuring", "sync", "OP_SYNC", "e", "catchAll", "mapInputContext", "dieSync", "dieMessage", "message", "RuntimeException", "evaluate", "withSTMRuntime", "finalizer", "matchSTM", "onFailure", "zipRight", "onSuccess", "a", "failSync", "either", "interruptAs", "orTry", "zip", "zipWith", "a1", "zipLeft", "b"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,gBAAgB;AACvC,OAAO,KAAKC,OAAO,MAAM,kBAAkB;AAC3C,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,OAAO,KAAKC,KAAK,MAAM,gBAAgB;AACvC,OAAO,KAAKC,IAAI,MAAM,eAAe;AAErC,OAAO,KAAKC,QAAQ,MAAM,mBAAmB;AAE7C,SAASC,SAAS,EAAEC,IAAI,EAAEC,IAAI,QAAQ,mBAAmB;AACzD,OAAO,KAAKC,IAAI,MAAM,eAAe;AAErC,SAASC,aAAa,QAAQ,mBAAmB;AACjD,SAASC,WAAW,QAAQ,oBAAoB;AAGhD,SAASC,YAAY,EAAEC,SAAS,QAAQ,gBAAgB;AACxD,SAASC,aAAa,QAAQ,mBAAmB;AACjD,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,cAAc,EAAEC,YAAY,QAAQ,kBAAkB;AAC/D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,UAAU,QAAQ,YAAY;AACvC,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,OAAO,KAAKC,OAAO,MAAM,kBAAkB;AAC3C,OAAO,KAAKC,YAAY,MAAM,oBAAoB;AAClD,OAAO,KAAKC,gBAAgB,MAAM,wBAAwB;AAC1D,OAAO,KAAKC,QAAQ,MAAM,eAAe;AACzC,OAAO,KAAKC,KAAK,MAAM,YAAY;AACnC,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnC,cAAA,GACA,MAAMC,YAAY,GAAG,YAAY;AAG1B,MAAMC,SAAS,GAAA,WAAA,GAAkBC,MAAM,CAACC,GAAG,CAChDH,YAAY,CACI;AAqGlB,MAAMI,WAAW,GAAG;IAClB,kBAAA,GACAC,EAAE,EAAGC,CAAQ,IAAKA,CAAC;IACnB,kBAAA,GACAC,EAAE,EAAGD,CAAQ,IAAKA,CAAC;IACnB,kBAAA,GACAE,EAAE,GAAGF,CAAQ,GAAKA;CACnB;AAED,cAAA,GACA,MAAMG,YAAY;IAWKC,qBAAA,CAAA;IAVdC,GAAG,0KAAGtB,YAAS,CAAA;IACfuB,qBAAqB,GAAQC,SAAS,CAAA;IACtCC,qBAAqB,GAAQD,SAAS,CAAA;IAC7C,iJAACzC,MAAM,CAAC2C,QAAY,CAAA,CAAA;IACpB,iKAAC3B,eAAY,CAAA,CAAA;IACb,2JAACG,aAAU,CAAA,CAAA;IACX,qKAACN,gBAAa,CAAA,CAAA;IACd,IAAA,CAAKgB,SAAS,CAAA,GAAC;QACb,OAAOG,WAAW;IACpB;IACAY,YAAqBN,qBAAyD,CAAA;QAAzD,IAAA,CAAAA,qBAAqB,GAArBA,qBAAqB;QACxC,IAAI,iJAACtC,MAAM,CAAC2C,QAAY,CAAC,mKAAG5B,iBAAc;QAC1C,IAAI,CAACC,+KAAY,CAAC,GAAGgB,WAAW;QAChC,IAAI,2JAACb,aAAU,CAAC,GAAGa,WAAW;QAC9B,IAAI,qKAACnB,gBAAa,CAAC,GAAGmB,WAAW;IACnC;IACA,gJAAC9B,KAAK,CAAC2C,GAAM,CAAA,CAAYC,IAAa,EAAA;QACpC,OAAO,IAAI,KAAKA,IAAI;IACtB;IACA,+IAACtC,IAAI,CAACqC,IAAM,CAAA,GAAC;QACX,yJAAOrC,IAAI,CAACuC,IAAAA,AAAM,EAAC,IAAI,EAAEvC,IAAI,CAACwC,sJAAAA,AAAM,EAAC,IAAI,CAAC,CAAC;IAC7C;IACA,CAAClB,MAAM,CAACmB,QAAQ,CAAA,GAAC;QACf,OAAO,uKAAI/B,gBAAa,CAAC,mJAAIN,YAAS,CAAC,IAAI,CAAC,CAAQ;IACtD;IACAsC,MAAMA,CAAA,EAAA;QACJ,OAAOC,gBAAgB,CAAC,IAAI,oJAAE9C,YAAS,oJAAEA,YAAS,CAAC;IACrD;IACAE,IAAIA,CAAA,EAAA;QACF,OAAOE,sKAAAA,AAAa,EAAC,IAAI,EAAE2C,SAAS,CAAC;IACvC;;AAIK,MAAMC,KAAK,IAAIC,CAAU,0JAA8C5C,cAAAA,AAAW,EAAC4C,CAAC,EAAEzB,SAAS,CAAC;AAGhG,MAAMqB,MAAM,IAAaK,IAAsB,GACpDJ,gBAAgB,CAACI,IAAI,EAAElD,8JAAS,oJAAEA,YAAS,CAAC;AAGvC,MAAM8C,gBAAgB,GAAGA,CAC9BI,IAAsB,EACtBC,MAA0C,EAC1CC,WAA6B,iKAE7B3C,mBAAAA,AAAgB,EAAE4C,KAAK,IAAI;QACzB,MAAMC,OAAO,GAAGD,KAAK,CAACE,EAAE,EAAE;QAC1B,MAAMC,GAAG,GAAGH,KAAK,CAACI,WAAW,mJAAC1D,QAAQ,CAAC2D,QAAc,CAAuB;QAC5E,MAAMC,SAAS,GAAGN,KAAK,CAACI,WAAW,mJAAC1D,QAAQ,CAAC6D,UAAgB,CAAC;QAC9D,MAAMC,QAAQ,GAAGR,KAAK,CAACI,WAAW,mJAAC1D,QAAQ,CAAC+D,mBAAyB,CAAC;QACtE,MAAMC,YAAY,GAAGC,aAAa,CAACV,OAAO,EAAEJ,IAAI,EAAEM,GAAG,EAAEG,SAAS,EAAEE,QAAQ,CAAC;QAC3E,OAAQE,YAAY,CAACE,IAAI;YACvB,sLAAK/C,UAAwB,MAAR,CAACgD;gBAAS;oBAC7Bf,MAAM,CAACY,YAAY,CAACI,IAAI,CAAC;oBACzB,OAAOJ,YAAY,CAACI,IAAI;gBAC1B;YACA,sLAAKjD,aAA2B,GAAX,CAACkD;gBAAY;oBAChC,MAAMC,KAAK,yKAAG/C,KAAK,CAACgD,CAAAA,AAAI,EAAE;oBAC1B,MAAMjB,KAAK,GAAuC;wBAAEkB,KAAK,uKAAEpD,QAAQ,CAACqD,CAAAA;oBAAO,CAAE;oBAC7E,MAAMC,MAAM,uJAAG9E,MAAM,CAAC+E,CAAAA,AAAK,EACxBC,CAA8C,IAC7CC,cAAc,CAACtB,OAAO,EAAEJ,IAAI,EAAEmB,KAAK,EAAEhB,KAAK,EAAEG,GAAG,EAAEG,SAAS,EAAEE,QAAQ,EAAEc,CAAC,CAAC,CAC3E;oBACD,2JAAOhF,MAAM,CAACkF,eAAAA,AAAmB,EAAEC,OAAO,KACxC5E,4JAAAA,AAAI,EACF4E,OAAO,CAACL,MAAM,CAAC,sJACf9E,MAAM,CAACoF,SAAAA,AAAa,GAAEC,KAAK,IAAI;4BAC7B,IAAIC,YAAY,GAAG5B,KAAK,CAACkB,KAAK;4BAC9B,6KAAIpD,QAAQ,CAAC+D,GAAAA,AAAS,EAACD,YAAY,CAAC,EAAE;gCACpC5B,KAAK,CAACkB,KAAK,wKAAGpD,QAAQ,CAACgE,KAAW;4BACpC;4BACAF,YAAY,GAAG5B,KAAK,CAACkB,KAAK;4BAC1B,IAAIpD,QAAQ,CAACiE,yKAAAA,AAAM,EAACH,YAAY,CAAC,EAAE;gCACjC9B,MAAM,CAAC8B,YAAY,CAACd,IAAI,CAAC;gCACzB,OAAOc,YAAY,CAACd,IAAI;4BAC1B;4BACAf,WAAW,EAAE;4BACb,2JAAOzD,MAAM,CAAC0F,KAAS,AAATA,EAAUL,KAAK,CAAC;wBAChC,CAAC,CAAC,CACH,CACF;gBACH;QACF;IACF,CAAC,CAAC;AAEJ,cAAA,GACA,MAAMM,SAAS,GAAGA,CAChBhC,OAAwB,EACxBiC,GAAqB,EACrBlC,KAAyC,EACzCG,GAAuB,EACvBG,SAA8B,EAC9BE,QAAgB,KACa;IAC7B,MAAM2B,OAAO,GAAoB,IAAIC,GAAG,EAAE;IAC1C,MAAMC,KAAK,GAAG,IAAIC,SAAS,CAACJ,GAAG,EAAEC,OAAO,EAAElC,OAAO,EAAEE,GAAG,CAAC,CAACoC,GAAG,EAAE;IAC7D,MAAMC,QAAQ,2KAAG9E,OAAO,CAAC+E,SAAAA,AAAc,EAACN,OAAO,CAAC;IAEhD,IAAIK,QAAQ,yKAAK9E,OAAO,CAACgF,mBAAwB,EAAE;QACjDhF,OAAO,CAACiF,gLAAAA,AAAa,EAACR,OAAO,CAAC;IAChC,CAAC,MAAM,IAAIK,QAAQ,yKAAK9E,OAAO,CAACkF,iBAAsB,EAAE;QACtD,MAAM,IAAIC,KAAK,CACb,qGAAqG,CACtG;IACH;IAEA,OAAQR,KAAK,CAACzB,IAAI;QAChB,kLAAKhD,YAAY,CAACkF,AAAU;YAAE;gBAC5B9C,KAAK,CAACkB,KAAK,4KAAGpD,QAAQ,CAACiF,GAAAA,AAAS,EAACV,KAAK,CAAC;gBACvC,OAAOW,aAAa,KAACvG,IAAI,CAACwG,mJAAO,AAAPA,EAAQZ,KAAK,CAACnB,KAAK,CAAC,EAAEiB,OAAO,EAAE7B,SAAS,EAAEE,QAAQ,CAAC;YAC/E;QACA,kLAAK5C,UAAoB,EAAR,CAACsF;YAAS;gBACzBlD,KAAK,CAACkB,KAAK,4KAAGpD,QAAQ,CAACiF,GAAAA,AAAS,EAACV,KAAK,CAAC;gBACvC,MAAMV,KAAK,GAAGvF,KAAK,CAAC+G,oJAAAA,AAAI,EAACd,KAAK,CAACe,KAAK,CAAC;gBACrC,OAAOJ,aAAa,mJAClBvG,IAAI,CAACuF,OAAS,AAATA,EAAUL,KAAK,CAAC,EACrBQ,OAAO,EACP7B,SAAS,EACTE,QAAQ,CACT;YACH;QACA,kLAAK5C,SAAmB,GAAP,CAACyF;YAAQ;gBACxBrD,KAAK,CAACkB,KAAK,GAAGpD,QAAQ,CAACiF,4KAAAA,AAAS,EAACV,KAAK,CAAC;gBACvC,MAAMV,KAAK,sJAAGvF,KAAK,CAACkH,AAAG,EAACjB,KAAK,CAACkB,MAAM,CAAC;gBACrC,OAAOP,aAAa,mJAClBvG,IAAI,CAACuF,OAAS,AAATA,EAAUL,KAAK,CAAC,EACrBQ,OAAO,EACP7B,SAAS,EACTE,QAAQ,CACT;YACH;QACA,kLAAK5C,YAAY,CAAC4F,EAAY;YAAE;gBAC9BxD,KAAK,CAACkB,KAAK,4KAAGpD,QAAQ,CAACiF,GAAAA,AAAS,EAACV,KAAK,CAAC;gBACvC,MAAMV,KAAK,GAAGvF,KAAK,CAACqH,yJAAAA,AAAS,EAACxD,OAAO,CAAC;gBACtC,OAAO+C,aAAa,mJAClBvG,IAAI,CAACuF,OAAS,AAATA,EAAUL,KAAK,CAAC,EACrBQ,OAAO,EACP7B,SAAS,EACTE,QAAQ,CACT;YACH;QACA,kLAAK5C,WAAqB,CAAT,CAAC8F;YAAU;gBAC1B,QAAO1F,SAAS,CAAC2F,yKAAAA,AAAO,EAACxB,OAAO,CAAC;YACnC;IACF;AACF,CAAC;AAED,cAAA,GACA,MAAMxB,aAAa,GAAGA,CACpBV,OAAwB,EACxBiC,GAAqB,EACrB/B,GAAuB,EACvBG,SAA8B,EAC9BE,QAAgB,KACa;IAC7B,MAAM2B,OAAO,GAAoB,IAAIC,GAAG,EAAE;IAC1C,MAAMC,KAAK,GAAG,IAAIC,SAAS,CAACJ,GAAG,EAAEC,OAAO,EAAElC,OAAO,EAAEE,GAAG,CAAC,CAACoC,GAAG,EAAE;IAC7D,MAAMC,QAAQ,GAAG9E,OAAO,CAAC+E,iLAAc,AAAdA,EAAeN,OAAO,CAAC;IAEhD,IAAIK,QAAQ,yKAAK9E,OAAO,CAACgF,mBAAwB,0KAAI3E,KAAK,CAAC6F,MAAAA,AAAS,EAACvB,KAAK,CAAC,EAAE;gLAC3E3E,OAAO,CAACiF,QAAAA,AAAa,EAACR,OAAO,CAAC;IAChC,CAAC,MAAM,IAAIK,QAAQ,yKAAK9E,OAAO,CAACkF,iBAAsB,EAAE;QACtD,MAAM,IAAIC,KAAK,CACb,yGAAyG,CAC1G;IACH;IAEA,OAAQR,KAAK,CAACzB,IAAI;QAChB,kLAAKhD,YAAY,CAACkF,AAAU;YAAE;gBAC5B,OAAOE,aAAa,mJAACvG,IAAI,CAACwG,KAAAA,AAAO,EAACZ,KAAK,CAACnB,KAAK,CAAC,EAAEiB,OAAO,EAAE7B,SAAS,EAAEE,QAAQ,CAAC;YAC/E;QACA,kLAAK5C,UAAoB,EAAR,CAACsF;YAAS;gBACzB,MAAMvB,KAAK,sJAAGvF,KAAK,CAAC+G,CAAI,AAAJA,EAAKd,KAAK,CAACe,KAAK,CAAC;gBACrC,OAAOJ,aAAa,kJAClBvG,IAAI,CAACuF,QAAAA,AAAS,EAACL,KAAK,CAAC,EACrBQ,OAAO,EACP7B,SAAS,EACTE,QAAQ,CACT;YACH;QACA,kLAAK5C,SAAmB,GAAP,CAACyF;YAAQ;gBACxB,MAAM1B,KAAK,sJAAGvF,KAAK,CAACkH,AAAG,EAACjB,KAAK,CAACkB,MAAM,CAAC;gBACrC,OAAOP,aAAa,mJAClBvG,IAAI,CAACuF,OAAS,AAATA,EAAUL,KAAK,CAAC,EACrBQ,OAAO,EACP7B,SAAS,EACTE,QAAQ,CACT;YACH;QACA,kLAAK5C,YAAY,CAAC4F,EAAY;YAAE;gBAC9B,MAAM7B,KAAK,GAAGvF,KAAK,CAACqH,yJAAAA,AAAS,EAACxD,OAAO,CAAC;gBACtC,OAAO+C,aAAa,mJAClBvG,IAAI,CAACuF,OAAAA,AAAS,EAACL,KAAK,CAAC,EACrBQ,OAAO,EACP7B,SAAS,EACTE,QAAQ,CACT;YACH;QACA,kLAAK5C,WAAqB,CAAT,CAAC8F;YAAU;gBAC1B,iLAAO1F,SAAS,CAAC2F,AAAO,EAACxB,OAAO,CAAC;YACnC;IACF;AACF,CAAC;AAED,cAAA,GACA,MAAMZ,cAAc,GAAGA,CACrBtB,OAAwB,EACxBJ,IAAsB,EACtBmB,KAAkB,EAClBhB,KAAyC,EACzC6D,OAA2B,EAC3BvD,SAA8B,EAC9BE,QAAgB,EAChBc,CAA8C,KAC5C;IACF,6KAAIxD,QAAQ,CAAC+D,GAAAA,AAAS,EAAC7B,KAAK,CAACkB,KAAK,CAAC,EAAE;QACnC,MAAM4C,MAAM,GAAG7B,SAAS,CAAChC,OAAO,EAAEJ,IAAI,EAAEG,KAAK,EAAE6D,OAAO,EAAEvD,SAAS,EAAEE,QAAQ,CAAC;QAC5E,OAAQsD,MAAM,CAAClD,IAAI;YACjB,sLAAK/C,UAAwB,MAAR,CAACgD;gBAAS;oBAC7BkD,iBAAiB,CAACD,MAAM,CAAChD,IAAI,EAAEQ,CAAC,CAAC;oBACjC;gBACF;YACA,sLAAKzD,aAA2B,GAAX,CAACkD;gBAAY;2LAChCrD,OAAO,CAACsG,GAAAA,AAAO,EACbhD,KAAK,EACL8C,MAAM,CAAC3B,OAAO,EACd,IAAMZ,cAAc,CAACtB,OAAO,EAAEJ,IAAI,EAAEmB,KAAK,EAAEhB,KAAK,EAAE6D,OAAO,EAAEvD,SAAS,EAAEE,QAAQ,EAAEc,CAAC,CAAC,CACnF;oBACD;gBACF;QACF;IACF;AACF,CAAC;AAED,cAAA,GACA,MAAM0B,aAAa,GAAGA,CACpBlC,IAAqB,EACrBqB,OAAwB,EACxB7B,SAA8B,EAC9BE,QAAgB,KACa;IAC7B,MAAMyD,KAAK,2KAAGvG,OAAO,CAACwG,OAAAA,AAAY,EAAC/B,OAAO,CAAC;IAC3C,IAAI8B,KAAK,CAACE,IAAI,GAAG,CAAC,EAAE;QAClB7D,SAAS,CAAC8D,YAAY,CAAC,4KAAM1G,OAAO,CAAC2G,IAAAA,AAAS,EAACJ,KAAK,CAAC,EAAEzD,QAAQ,CAAC;IAClE;IACA,iLAAOxC,OAAUsG,AAAI,EAAL,AAAMxD,CAALwD,GAAS,CAAC;AAC7B,CAAC;AAED,cAAA,GACA,MAAMP,iBAAiB,GAAGA,CACxBjD,IAAqB,EACrBQ,CAA8C,KACtC;IACRA,CAAC,CAACR,IAAI,CAAC;AACT,CAAC;AAMM,MAAM+C,OAAO,GAAGA,CAAA,GACrBzC,MAAM,CAAwB,CAAC5C,CAAC,EAAE+F,EAAE,EAAEpE,GAAG,GAAKA,GAAG,CAAC;AAG7C,MAAMqE,WAAW,IAAWC,CAA0C,GAC3EC,GAAG,CAACb,OAAO,EAAM,EAAEY,CAAC,CAAC;AAGhB,MAAME,cAAc,IACzBF,CAAyD,GAC/BG,OAAO,CAACf,OAAO,EAAM,EAAEY,CAAC,CAAC;AAG/C,MAAOnC,SAAS;IAKTzC,IAAA,CAAA;IACAsC,OAAA,CAAA;IACAlC,OAAA,CAAA;IANH4E,SAAS,GAAwB,EAAE,CAAA;IACnC1E,GAAG,CAAA;IAEXjB,YACWW,IAAsB,EACtBsC,OAAwB,EACxBlC,OAAwB,EACjC6E,EAAsB,CAAA;QAHb,IAAA,CAAAjF,IAAI,GAAJA,IAAI;QACJ,IAAA,CAAAsC,OAAO,GAAPA,OAAO;QACP,IAAA,CAAAlC,OAAO,GAAPA,OAAO;QAGhB,IAAI,CAACE,GAAG,GAAG2E,EAA8B;IAC3C;IAEAC,MAAMA,CAAA,EAAA;QACJ,OAAO,IAAI,CAAC5E,GAAG;IACjB;IAEA6E,SAASA,CAACC,IAAkB,EAAA;QAC1B,IAAI,CAACJ,SAAS,CAACK,IAAI,CAACD,IAAI,CAAC;IAC3B;IAEAE,QAAQA,CAAA,EAAA;QACN,OAAO,IAAI,CAACN,SAAS,CAACO,GAAG,EAAE;IAC7B;IAEAC,WAAWA,CAAA,EAAA;QACT,IAAIC,OAAO,GAAG,IAAI,CAACH,QAAQ,EAAE;QAC7B,MAAOG,OAAO,KAAKvG,SAAS,IAAIuG,OAAO,CAAC1G,qBAAqB,gLAAKjB,OAAO,CAAC4H,QAAa,CAAE;YACvFD,OAAO,GAAG,IAAI,CAACH,QAAQ,EAAE;QAC3B;QACA,OAAOG,OAAO;IAChB;IAEAE,WAAWA,CAAA,EAAA;QACT,IAAIF,OAAO,GAAG,IAAI,CAACH,QAAQ,EAAE;QAC7B,MAAOG,OAAO,KAAKvG,SAAS,IAAIuG,OAAO,CAAC1G,qBAAqB,gLAAKjB,OAAO,CAAC8H,QAAa,CAAE;YACvFH,OAAO,GAAG,IAAI,CAACH,QAAQ,EAAE;QAC3B;QACA,OAAOG,OAAO;IAChB;IAEAI,SAASA,CAAA,EAAA;QACP,IAAIJ,OAAO,GAAG,IAAI,CAACH,QAAQ,EAAE;QAC7B,MAAOG,OAAO,KAAKvG,SAAS,IAAIuG,OAAO,CAAC1G,qBAAqB,gLAAKjB,OAAO,CAACgI,MAAW,CAAE;YACrFL,OAAO,GAAG,IAAI,CAACH,QAAQ,EAAE;QAC3B;QACA,OAAOG,OAAO;IAChB;IAEA/C,GAAGA,CAAA,EAAA;QACD,IAAIqD,IAAI,GAAG,IAAI,CAAC/F,IAAoG;QACpH,IAAIiB,IAAI,GAA8C/B,SAAS;QAC/D,MAAO+B,IAAI,KAAK/B,SAAS,IAAI6G,IAAI,KAAK7G,SAAS,CAAE;YAC/C,IAAI;gBACF,MAAMuG,OAAO,GAAGM,IAAI;gBACpB,IAAIN,OAAO,EAAE;oBACX,OAAQA,OAAO,CAACzG,GAAG;wBACjB,KAAK,KAAK;4BAAE;gCACV+G,IAAI,GAAGxE,MAAM,CAAC,CAAC5C,CAAC,EAAE+F,EAAE,EAAEpE,GAAG,wJAAK9D,OAAO,CAACwJ,IAAAA,AAAS,EAAC1F,GAAG,EAAEmF,OAAO,CAAC,CAAc;gCAC3E;4BACF;wBACA,KAAK,MAAM;4BAAE;gCACXM,IAAI,GAAGzC,IAAI,CAACmC,OAAO,CAACQ,IAAI,CAAc;gCACtC;4BACF;wBACA,KAAK,MAAM;4BAAE;gCACXF,IAAI,GAAGzC,IAAI,CAAC,IAAI/G,KAAK,CAAC2J,kKAAsB,EAAE,CAAc;gCAC5D;4BACF;wBACA,KAAK,OAAO;4BAAE;gCACZH,IAAI,GAAG3C,OAAO,CAACqC,OAAO,CAACU,KAAK,CAAc;gCAC1C;4BACF;wBACA,KAAK,MAAM;4BAAE;gCACXJ,IAAI,GAAG3C,OAAO,CAACqC,OAAO,CAACpE,KAAK,CAAc;gCAC1C;4BACF;wBACA,KAAK,QAAQ;4BAAE;gCACb,OAAQoE,OAAO,CAAC1G,qBAAqB;oCACnC,gLAAKjB,OAAO,CAAC0F,CAAM;wCAAE;4CACnBvC,IAAI,yKAAG/C,KAAK,CAACuF,AAAG,qJAACrG,eAAAA,AAAY,EAAC,IAAMqI,OAAO,CAACxG,qBAAqB,EAAE,CAAC,CAAC;4CACrE;wCACF;oCACA,gLAAKnB,OAAO,CAACuF,EAAO;wCAAE;4CACpB,MAAM+B,IAAI,GAAG,IAAI,CAACO,WAAW,EAAE;4CAC/B,IAAIP,IAAI,KAAKlG,SAAS,EAAE;gDACtB+B,IAAI,yKAAG/C,KAAK,CAACoF,CAAAA,AAAI,qJAAClG,eAAAA,AAAY,EAAC,IAAMqI,OAAO,CAACxG,qBAAqB,EAAE,CAAC,CAAC;4CACxE,CAAC,MAAM;gDACL8G,IAAI,qJAAG3I,gBAAAA,AAAY,EAAC,IAClBgI,IAAI,CAACjG,qBAAqB,oJACxB/B,eAAAA,AAAY,EAAC,IAAMqI,OAAO,CAACxG,qBAAqB,EAAE,CAAC,CACvC,CACf;4CACH;4CACA;wCACF;oCACA,gLAAKnB,OAAO,CAAC+F,GAAQ;wCAAE;4CACrB,MAAMuB,IAAI,GAAG,IAAI,CAACS,SAAS,EAAE;4CAC7B,IAAIT,IAAI,KAAKlG,SAAS,EAAE;gDACtB+B,IAAI,qKAAG/C,KAAK,CAACkI,EAAK;4CACpB,CAAC,MAAM;gDACLL,IAAI,OAAG3I,8JAAAA,AAAY,EAAC,IAAMgI,IAAI,CAACjG,qBAAqB,EAAe,CAAC;4CACtE;4CACA;wCACF;oCACA,gLAAKrB,OAAO,CAAC6F,OAAY;wCAAE;4CACzB1C,IAAI,IAAG/C,KAAK,CAAC0F,2KAAAA,AAAS,EAAC,IAAI,CAACxD,OAAO,CAAC;4CACpC;wCACF;oCACA,+KAAKtC,OAAO,CAACuI,eAAmB;wCAAE;4CAChCN,IAAI,sJAAG3I,eAAAA,AAAY,EAAC,IAClBqI,OAAO,CAACxG,qBAAqB,CAAC,IAA4C,CAAc,CACzF;4CACD;wCACF;oCACA,KAAKnB,OAAO,CAAC4H,mLAAa;oCAC1B,gLAAK5H,OAAO,CAAC8H,QAAa;oCAC1B,gLAAK9H,OAAO,CAACgI,MAAW;wCAAE;4CACxB,IAAI,CAACX,SAAS,CAACM,OAAO,CAAC;4CACvBM,IAAI,GAAGN,OAAO,CAACxG,qBAAkC;4CACjD;wCACF;oCACA,gLAAKnB,OAAO,CAACwI,KAAU;wCAAE;4CACvB,MAAMhG,GAAG,GAAG,IAAI,CAACA,GAAG;4CACpB,IAAI,CAACA,GAAG,OAAGlD,8JAAAA,AAAY,EAAC,IAAMqI,OAAO,CAACtG,qBAAqB,CAACmB,GAAG,CAAC,CAAC;4CACjEyF,IAAI,OAAG/I,yJAAAA,AAAI,EACTyI,OAAO,CAACxG,qBAAqB,EAC7BsH,QAAQ,CAACC,IAAI,CAAC,IAAO,IAAI,CAAClG,GAAG,GAAGA,GAAI,CAAC,CAAC,CAC1B;4CACd;wCACF;oCACA,gLAAKxC,OAAO,CAACmF,KAAU;wCAAE;4CACvB,MAAM5B,KAAK,GAAGoE,OAAO,CAACxG,qBAAqB;4CAC3C,MAAMmG,IAAI,GAAG,IAAI,CAACI,WAAW,EAAE;4CAC/B,IAAIJ,IAAI,KAAKlG,SAAS,EAAE;gDACtB+B,IAAI,yKAAG/C,KAAK,CAACkF,IAAAA,AAAO,EAAC/B,KAAK,CAAC;4CAC7B,CAAC,MAAM;gDACL0E,IAAI,sJAAG3I,eAAAA,AAAY,EAAC,IAAMgI,IAAI,CAACjG,qBAAqB,CAACkC,KAAK,CAAc,CAAC;4CAC3E;4CACA;wCACF;oCACA,gLAAKvD,OAAO,CAAC2I,EAAO;wCAAE;4CACpB,MAAMpF,KAAK,sJAAGjE,eAAAA,AAAY,EAAC,IAAMqI,OAAO,CAACxG,qBAAqB,EAAE,CAAC;4CACjE,MAAMmG,IAAI,GAAG,IAAI,CAACI,WAAW,EAAE;4CAC/B,IAAIJ,IAAI,KAAKlG,SAAS,EAAE;gDACtB+B,IAAI,yKAAG/C,KAAK,CAACkF,IAAAA,AAAO,EAAC/B,KAAK,CAAC;4CAC7B,CAAC,MAAM;gDACL0E,IAAI,sJAAG3I,eAAAA,AAAY,EAAC,IAAMgI,IAAI,CAACjG,qBAAqB,CAACkC,KAAK,CAAc,CAAC;4CAC3E;4CACA;wCACF;gCACF;gCACA;4BACF;oBACF;gBACF;YACF,CAAC,CAAC,OAAOqF,CAAC,EAAE;gBACVX,IAAI,GAAGtC,GAAG,CAACiD,CAAC,CAAc;YAC5B;QACF;QACA,OAAOzF,IAAyB;IAClC;;AAIK,MAAM0F,QAAQ,GAAA,WAAA,yJAAG5J,OAAAA,AAAI,EAU1B,CAAC,EAAE,CAACiD,IAAI,EAAE4E,CAAC,KAAI;IACf,MAAMvC,GAAG,GAAG,IAAIvD,YAAY,4KAAChB,OAAO,CAAC8H,QAAa,CAAC;IACnDvD,GAAG,CAACpD,qBAAqB,GAAGe,IAAI;IAChCqC,GAAG,CAAClD,qBAAqB,GAAGyF,CAAC;IAC7B,OAAOvC,GAAG;AACZ,CAAC,CAAC;AAGK,MAAMuE,eAAe,GAAA,WAAA,yJAAG7J,OAAAA,AAAI,EAUjC,CAAC,EAAE,CAACiD,IAAI,EAAE4E,CAAC,KAAI;IACf,MAAMvC,GAAG,GAAG,IAAIvD,YAAY,2KAAChB,OAAO,CAACwI,MAAU,CAAC;IAChDjE,GAAG,CAACpD,qBAAqB,GAAGe,IAAI;IAChCqC,GAAG,CAAClD,qBAAqB,GAAGyF,CAAC;IAC7B,OAAOvC,GAAG;AACZ,CAAC,CAAC;AAGK,MAAMoB,GAAG,IAAIC,MAAe,GAAqBmD,OAAO,CAAC,IAAMnD,MAAM,CAAC;AAGtE,MAAMoD,UAAU,IAAIC,OAAe,GAAqBF,OAAO,CAAC,IAAM,IAAItK,KAAK,CAACyK,4JAAgB,CAACD,OAAO,CAAC,CAAC;AAG1G,MAAMF,OAAO,IAAII,QAA0B,IAAoB;IACpE,MAAM5E,GAAG,GAAG,IAAIvD,YAAY,4KAAChB,OAAO,CAAC0F,CAAM,CAAC;IAC5CnB,GAAG,CAACpD,qBAAqB,GAAGgI,QAAQ;IACpC,OAAO5E,GAAU;AACnB,CAAC;AAGM,MAAMd,MAAM,IACjBqD,CAA6F,GACpEsC,cAAc,EAAEvI,CAAC,GAAKyE,OAAO,CAACwB,CAAC,CAACjG,CAAC,CAAC2D,OAAO,EAAE3D,CAAC,CAACyB,OAAO,EAAEzB,CAAC,CAACuG,MAAM,EAAE,CAAC,CAAC,CAAC;AAGvF,MAAMqB,QAAQ,GAAA,WAAA,yJAAGxJ,OAAAA,AAAI,EAG1B,CAAC,EAAE,CAACiD,IAAI,EAAEmH,SAAS,GACnBC,QAAQ,CAACpH,IAAI,EAAE;QACbqH,SAAS,GAAGX,CAAC,GAAKY,QAAQ,CAACH,SAAS,EAAE7D,IAAI,CAACoD,CAAC,CAAC,CAAC;QAC9Ca,SAAS,GAAGC,CAAC,GAAKF,QAAQ,CAACH,SAAS,EAAE/D,OAAO,CAACoE,CAAC,CAAC;KACjD,CAAC,CAAC;AAGE,MAAMlE,IAAI,IAAOC,KAAQ,GAAwBkE,QAAQ,CAAC,IAAMlE,KAAK,CAAC;AAGtE,MAAMkE,QAAQ,IAAOR,QAAoB,IAAuB;IACrE,MAAM5E,GAAG,GAAG,IAAIvD,YAAY,4KAAChB,OAAO,CAACuF,EAAO,CAAC;IAC7ChB,GAAG,CAACpD,qBAAqB,GAAGgI,QAAQ;IACpC,OAAO5E,GAAU;AACnB,CAAC;AAGM,MAAM0C,OAAO,GAAA,WAAA,yJAAGhI,OAAAA,AAAI,EAGzB,CAAC,EAAE,CAACiD,IAAI,EAAE4E,CAAC,KAAI;IACf,MAAMvC,GAAG,GAAG,IAAIvD,YAAY,4KAAChB,OAAO,CAAC4H,QAAa,CAAC;IACnDrD,GAAG,CAACpD,qBAAqB,GAAGe,IAAI;IAChCqC,GAAG,CAAClD,qBAAqB,GAAGyF,CAAC;IAC7B,OAAOvC,GAAG;AACZ,CAAC,CAAC;AAGK,MAAM+E,QAAQ,GAAA,WAAA,yJAAGrK,OAAAA,AAAI,EAc1B,CAAC,EAAE,CACHiD,IAAsB,EACtB,EAAEqH,SAAS,EAAEE,SAAAA,EAGZ,OAEDvK,yJAAI,AAAJA,EACEgD,IAAI,EACJ6E,GAAG,iJAACnI,MAAM,CAACyJ,CAAK,CAAC,EACjBQ,QAAQ,EAAED,CAAC,yJAAK1J,OAAAA,AAAI,EAACqK,SAAS,CAACX,CAAC,CAAC,EAAE7B,GAAG,iJAACnI,MAAM,CAACuJ,AAAI,CAAC,CAAC,CAAC,EACrDlB,OAAO,EAAE2C,MAAM,IAA4C;QACzD,OAAQA,MAAM,CAAC3G,IAAI;YACjB,KAAK,MAAM;gBAAE;oBACX,OAAOqC,OAAO,CAACsE,MAAM,CAACzB,IAAI,CAAC;gBAC7B;YACA,KAAK,OAAO;gBAAE;oBACZ,OAAOsB,SAAS,CAACG,MAAM,CAACvB,KAAK,CAAC;gBAChC;QACF;IACF,CAAC,CAAC,CACH,CAAC;AAGG,MAAMe,cAAc,IACzBtC,CAAsE,IAClD;IACpB,MAAMvC,GAAG,GAAG,IAAIvD,YAAY,4KAAChB,OAAO,CAACuI,cAAmB,CAAC;IACzDhE,GAAG,CAACpD,qBAAqB,GAAG2F,CAAC;IAC7B,OAAOvC,GAAG;AACZ,CAAC;AAGM,MAAMuB,SAAS,GAAA,WAAA,GAAmBsD,cAAc,EAAEvI,CAAC,IAAI;IAC5D,MAAM0D,GAAG,GAAG,IAAIvD,YAAY,4KAAChB,OAAO,CAAC6F,OAAY,CAAC;IAClDtB,GAAG,CAACpD,qBAAqB,GAAGN,CAAC,CAACyB,OAAO;IACrC,OAAOiC,GAAU;AACnB,CAAC,CAAC;AAGK,MAAMsF,WAAW,IAAIvH,OAAwB,IAAoB;IACtE,MAAMiC,GAAG,GAAG,IAAIvD,YAAY,4KAAChB,OAAO,CAAC6F,OAAY,CAAC;IAClDtB,GAAG,CAACpD,qBAAqB,GAAGmB,OAAO;IACnC,OAAOiC,GAAU;AACnB,CAAC;AAGM,MAAMwC,GAAG,GAAA,WAAA,yJAAG9H,OAAAA,AAAI,EAGrB,CAAC,EAAE,CAACiD,IAAI,EAAE4E,CAAC,yJAAK5H,OAAAA,AAAI,EAACgD,IAAI,EAAE+E,OAAO,EAAEyC,CAAC,GAAKhB,IAAI,CAAC,IAAM5B,CAAC,CAAC4C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAGxD,MAAMI,KAAK,GAAA,WAAA,yJAAG7K,OAAAA,AAAI,EAUvB,CAAC,EAAE,CAACiD,IAAI,EAAET,IAAI,KAAI;IAClB,MAAM8C,GAAG,GAAG,IAAIvD,YAAY,2KAAChB,OAAO,CAACgI,OAAW,CAAC;IACjDzD,GAAG,CAACpD,qBAAqB,GAAGe,IAAI;IAChCqC,GAAG,CAAClD,qBAAqB,GAAGI,IAAI;IAChC,OAAO8C,GAAG;AACZ,CAAC,CAAC;AAGK,MAAM+D,KAAK,GAAA,WAAA,GAAmB,IAAItH,YAAY,4KAAChB,OAAO,CAAC+F,GAAQ,CAAC;AAGhE,MAAMT,OAAO,IAAO/B,KAAQ,IAAgB;IACjD,MAAMgB,GAAG,GAAG,IAAIvD,YAAY,CAAChB,OAAO,CAACmF,gLAAU,CAAC;IAChDZ,GAAG,CAACpD,qBAAqB,GAAGoC,KAAK;IACjC,OAAOgB,GAAU;AACnB,CAAC;AAGM,MAAMmE,IAAI,IAAOS,QAAiB,IAAgB;IACvD,MAAM5E,GAAG,GAAG,IAAIvD,YAAY,4KAAChB,OAAO,CAAC2I,EAAO,CAAC;IAC7CpE,GAAG,CAACpD,qBAAqB,GAAGgI,QAAQ;IACpC,OAAO5E,GAAU;AACnB,CAAC;AAGM,MAAMwF,GAAG,GAAA,WAAA,yJAAG9K,OAAAA,AAAI,EAUrB,CAAC,EAAE,CAACiD,IAAI,EAAET,IAAI,yJAAKvC,OAAAA,AAAI,EAACgD,IAAI,EAAE8H,OAAO,CAACvI,IAAI,EAAE,CAACiI,CAAC,EAAEO,EAAE,GAAK;YAACP,CAAC;YAAEO,EAAE;SAAC,CAAC,CAAC,CAAC;AAG5D,MAAMC,OAAO,GAAA,WAAA,yJAAGjL,OAAAA,AAAI,EAGzB,CAAC,EAAE,CAACiD,IAAI,EAAET,IAAI,IAAKvC,4JAAAA,AAAI,EAACgD,IAAI,EAAE+E,OAAO,EAAEyC,CAAC,yJAAKxK,OAAAA,AAAI,EAACuC,IAAI,EAAEsF,GAAG,CAAC,IAAM2C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAGnE,MAAMF,QAAQ,GAAA,WAAA,IAAGvK,4JAAAA,AAAI,EAG1B,CAAC,EAAE,CAACiD,IAAI,EAAET,IAAI,yJAAKvC,OAAAA,AAAI,EAACgD,IAAI,EAAE+E,OAAO,CAAC,IAAMxF,IAAI,CAAC,CAAC,CAAC;AAG9C,MAAMuI,OAAO,GAAA,WAAA,yJAAG/K,OAAAA,AAAI,EAazB,CAAC,EACD,CAACiD,IAAI,EAAET,IAAI,EAAEqF,CAAC,yJAAK5H,OAAI,AAAJA,EAAKgD,IAAI,EAAE+E,OAAO,EAAEyC,CAAC,yJAAKxK,OAAAA,AAAI,EAACuC,IAAI,EAAEsF,GAAG,EAAEoD,CAAC,GAAKrD,CAAC,CAAC4C,CAAC,EAAES,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1017, "column": 0}, "map": {"version": 3, "file": "strategy.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/opCodes/strategy.ts"], "sourcesContent": ["/** @internal */\nexport const OP_BACKPRESSURE_STRATEGY = \"BackPressure\" as const\n\n/** @internal */\nexport type OP_BACKPRESSURE_STRATEGY = typeof OP_BACKPRESSURE_STRATEGY\n\n/** @internal */\nexport const OP_DROPPING_STRATEGY = \"Dropping\" as const\n\n/** @internal */\nexport type OP_DROPPING_STRATEGY = typeof OP_DROPPING_STRATEGY\n\n/** @internal */\nexport const OP_SLIDING_STRATEGY = \"Sliding\" as const\n\n/** @internal */\nexport type OP_SLIDING_STRATEGY = typeof OP_SLIDING_STRATEGY\n"], "names": ["OP_BACKPRESSURE_STRATEGY", "OP_DROPPING_STRATEGY", "OP_SLIDING_STRATEGY"], "mappings": "AAAA,cAAA;;;;;AACO,MAAMA,wBAAwB,GAAG,cAAuB;AAMxD,MAAMC,oBAAoB,GAAG,UAAmB;AAMhD,MAAMC,mBAAmB,GAAG,SAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "file": "stm.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/stm.ts"], "sourcesContent": ["import * as RA from \"../../Array.js\"\nimport * as Cause from \"../../Cause.js\"\nimport * as Chunk from \"../../Chunk.js\"\nimport * as Context from \"../../Context.js\"\nimport * as Effect from \"../../Effect.js\"\nimport * as Either from \"../../Either.js\"\nimport * as Exit from \"../../Exit.js\"\nimport type * as FiberId from \"../../FiberId.js\"\nimport type { LazyArg } from \"../../Function.js\"\nimport { constFalse, constTrue, constVoid, dual, identity, pipe } from \"../../Function.js\"\nimport * as Option from \"../../Option.js\"\nimport type { Predicate, Refinement } from \"../../Predicate.js\"\nimport * as predicate from \"../../Predicate.js\"\nimport type * as STM from \"../../STM.js\"\nimport type * as Types from \"../../Types.js\"\nimport { yieldWrapGet } from \"../../Utils.js\"\nimport * as effectCore from \"../core.js\"\nimport * as core from \"./core.js\"\nimport * as Journal from \"./journal.js\"\nimport * as STMState from \"./stmState.js\"\n\n/** @internal */\nexport const acquireUseRelease = dual<\n  <A, A2, E2, R2, A3, E3, R3>(\n    use: (resource: A) => STM.STM<A2, E2, R2>,\n    release: (resource: A) => STM.STM<A3, E3, R3>\n  ) => <E, R>(\n    acquire: STM.STM<A, E, R>\n  ) => Effect.Effect<A2, E | E2 | E3, R | R2 | R3>,\n  <A, E, R, A2, E2, R2, A3, E3, R3>(\n    acquire: STM.STM<A, E, R>,\n    use: (resource: A) => STM.STM<A2, E2, R2>,\n    release: (resource: A) => STM.STM<A3, E3, R3>\n  ) => Effect.Effect<A2, E | E2 | E3, R | R2 | R3>\n>(3, <A, E, R, A2, E2, R2, A3, E3, R3>(\n  acquire: STM.STM<A, E, R>,\n  use: (resource: A) => STM.STM<A2, E2, R2>,\n  release: (resource: A) => STM.STM<A3, E3, R3>\n): Effect.Effect<A2, E | E2 | E3, R | R2 | R3> =>\n  Effect.uninterruptibleMask((restore) => {\n    let state: STMState.STMState<A, E> = STMState.running\n    return pipe(\n      restore(\n        core.unsafeAtomically(\n          acquire,\n          (exit) => {\n            state = STMState.done(exit)\n          },\n          () => {\n            state = STMState.interrupted\n          }\n        )\n      ),\n      Effect.matchCauseEffect({\n        onFailure: (cause) => {\n          if (STMState.isDone(state) && Exit.isSuccess(state.exit)) {\n            return pipe(\n              release(state.exit.value),\n              Effect.matchCauseEffect({\n                onFailure: (cause2) => Effect.failCause(Cause.parallel(cause, cause2)),\n                onSuccess: () => Effect.failCause(cause)\n              })\n            )\n          }\n          return Effect.failCause(cause)\n        },\n        onSuccess: (a) =>\n          pipe(\n            restore(use(a)),\n            Effect.matchCauseEffect({\n              onFailure: (cause) =>\n                pipe(\n                  release(a),\n                  Effect.matchCauseEffect({\n                    onFailure: (cause2) => Effect.failCause(Cause.parallel(cause, cause2)),\n                    onSuccess: () => Effect.failCause(cause)\n                  })\n                ),\n              onSuccess: (a2) => pipe(release(a), Effect.as(a2))\n            })\n          )\n      })\n    )\n  }))\n\n/** @internal */\nexport const as = dual<\n  <A2>(value: A2) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<A2, E, R>,\n  <A, E, R, A2>(self: STM.STM<A, E, R>, value: A2) => STM.STM<A2, E, R>\n>(2, (self, value) => pipe(self, core.map(() => value)))\n\n/** @internal */\nexport const asSome = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<Option.Option<A>, E, R> =>\n  pipe(self, core.map(Option.some))\n\n/** @internal */\nexport const asSomeError = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<A, Option.Option<E>, R> =>\n  pipe(self, mapError(Option.some))\n\n/** @internal */\nexport const asVoid = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<void, E, R> => pipe(self, core.map(constVoid))\n\n/** @internal */\nexport const attempt = <A>(evaluate: LazyArg<A>): STM.STM<A, unknown> =>\n  suspend(() => {\n    try {\n      return core.succeed(evaluate())\n    } catch (defect) {\n      return core.fail(defect)\n    }\n  })\n\nexport const bind = dual<\n  <N extends string, K, A, E2, R2>(\n    tag: Exclude<N, keyof K>,\n    f: (_: K) => STM.STM<A, E2, R2>\n  ) => <E, R>(self: STM.STM<K, E, R>) => STM.STM<Types.MergeRecord<K, { [k in N]: A }>, E | E2, R | R2>,\n  <K, E, R, N extends string, A, E2, R2>(\n    self: STM.STM<K, E, R>,\n    tag: Exclude<N, keyof K>,\n    f: (_: K) => STM.STM<A, E2, R2>\n  ) => STM.STM<Types.MergeRecord<K, { [k in N]: A }>, E | E2, R | R2>\n>(3, <K, E, R, N extends string, A, E2, R2>(\n  self: STM.STM<K, E, R>,\n  tag: Exclude<N, keyof K>,\n  f: (_: K) => STM.STM<A, E2, R2>\n) =>\n  core.flatMap(self, (k) =>\n    core.map(\n      f(k),\n      (a): Types.MergeRecord<K, { [k in N]: A }> => ({ ...k, [tag]: a } as any)\n    )))\n\n/* @internal */\nexport const bindTo = dual<\n  <N extends string>(tag: N) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<\n    Record<N, A>,\n    E,\n    R\n  >,\n  <A, E, R, N extends string>(\n    self: STM.STM<A, E, R>,\n    tag: N\n  ) => STM.STM<\n    Record<N, A>,\n    E,\n    R\n  >\n>(\n  2,\n  <A, E, R, N extends string>(self: STM.STM<A, E, R>, tag: N): STM.STM<Record<N, A>, E, R> =>\n    core.map(self, (a) => ({ [tag]: a } as Record<N, A>))\n)\n\n/* @internal */\nexport const let_ = dual<\n  <N extends string, K, A>(\n    tag: Exclude<N, keyof K>,\n    f: (_: K) => A\n  ) => <E, R>(self: STM.STM<K, E, R>) => STM.STM<\n    Types.MergeRecord<K, { [k in N]: A }>,\n    E,\n    R\n  >,\n  <K, E, R, N extends string, A>(\n    self: STM.STM<K, E, R>,\n    tag: Exclude<N, keyof K>,\n    f: (_: K) => A\n  ) => STM.STM<\n    Types.MergeRecord<K, { [k in N]: A }>,\n    E,\n    R\n  >\n>(3, <K, E, R, N extends string, A>(self: STM.STM<K, E, R>, tag: Exclude<N, keyof K>, f: (_: K) => A) =>\n  core.map(\n    self,\n    (k): Types.MergeRecord<K, { [k in N]: A }> => ({ ...k, [tag]: f(k) } as any)\n  ))\n\n/** @internal */\nexport const catchSome = dual<\n  <E, A2, E2, R2>(\n    pf: (error: E) => Option.Option<STM.STM<A2, E2, R2>>\n  ) => <A, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<A2 | A, E | E2, R2 | R>,\n  <A, E, R, A2, E2, R2>(\n    self: STM.STM<A, E, R>,\n    pf: (error: E) => Option.Option<STM.STM<A2, E2, R2>>\n  ) => STM.STM<A2 | A, E | E2, R2 | R>\n>(2, <A, E, R, A2, E2, R2>(\n  self: STM.STM<A, E, R>,\n  pf: (error: E) => Option.Option<STM.STM<A2, E2, R2>>\n): STM.STM<A2 | A, E | E2, R2 | R> =>\n  core.catchAll(\n    self,\n    (e): STM.STM<A | A2, E | E2, R | R2> => Option.getOrElse(pf(e), () => core.fail(e))\n  ))\n\n/** @internal */\nexport const catchTag = dual<\n  <K extends E[\"_tag\"] & string, E extends { _tag: string }, A1, E1, R1>(\n    k: K,\n    f: (e: Extract<E, { _tag: K }>) => STM.STM<A1, E1, R1>\n  ) => <A, R>(self: STM.STM<A, E, R>) => STM.STM<A | A1, Exclude<E, { _tag: K }> | E1, R | R1>,\n  <A, E extends { _tag: string }, R, K extends E[\"_tag\"] & string, A1, E1, R1>(\n    self: STM.STM<A, E, R>,\n    k: K,\n    f: (e: Extract<E, { _tag: K }>) => STM.STM<A1, E1, R1>\n  ) => STM.STM<A | A1, Exclude<E, { _tag: K }> | E1, R | R1>\n>(3, (self, k, f) =>\n  core.catchAll(self, (e) => {\n    if (\"_tag\" in e && e[\"_tag\"] === k) {\n      return f(e as any)\n    }\n    return core.fail(e as any)\n  }))\n\n/** @internal */\nexport const catchTags: {\n  <\n    E extends { _tag: string },\n    Cases extends {\n      [K in E[\"_tag\"]]+?: (error: Extract<E, { _tag: K }>) => STM.STM<any, any, any>\n    }\n  >(\n    cases: Cases\n  ): <A, R>(self: STM.STM<A, E, R>) => STM.STM<\n    | A\n    | {\n      [K in keyof Cases]: Cases[K] extends ((...args: Array<any>) => STM.STM<infer A, any, any>) ? A : never\n    }[keyof Cases],\n    | Exclude<E, { _tag: keyof Cases }>\n    | {\n      [K in keyof Cases]: Cases[K] extends ((...args: Array<any>) => STM.STM<any, infer E, any>) ? E : never\n    }[keyof Cases],\n    | R\n    | {\n      [K in keyof Cases]: Cases[K] extends ((...args: Array<any>) => STM.STM<any, any, infer R>) ? R : never\n    }[keyof Cases]\n  >\n  <\n    R,\n    E extends { _tag: string },\n    A,\n    Cases extends {\n      [K in E[\"_tag\"]]+?: (error: Extract<E, { _tag: K }>) => STM.STM<any, any, any>\n    }\n  >(\n    self: STM.STM<A, E, R>,\n    cases: Cases\n  ): STM.STM<\n    | A\n    | {\n      [K in keyof Cases]: Cases[K] extends ((...args: Array<any>) => STM.STM<infer A, any, any>) ? A : never\n    }[keyof Cases],\n    | Exclude<E, { _tag: keyof Cases }>\n    | {\n      [K in keyof Cases]: Cases[K] extends ((...args: Array<any>) => STM.STM<any, infer E, any>) ? E : never\n    }[keyof Cases],\n    | R\n    | {\n      [K in keyof Cases]: Cases[K] extends ((...args: Array<any>) => STM.STM<any, any, infer R>) ? R : never\n    }[keyof Cases]\n  >\n} = dual(2, (self, cases) =>\n  core.catchAll(self, (e: any) => {\n    const keys = Object.keys(cases)\n    if (\"_tag\" in e && keys.includes(e[\"_tag\"])) {\n      return cases[e[\"_tag\"]](e as any)\n    }\n    return core.fail(e as any)\n  }))\n\n/** @internal */\nexport const check = (predicate: LazyArg<boolean>): STM.STM<void> => suspend(() => predicate() ? void_ : core.retry)\n\n/** @internal */\nexport const collect = dual<\n  <A, A2>(pf: (a: A) => Option.Option<A2>) => <E, R>(self: STM.STM<A, E, R>) => STM.STM<A2, E, R>,\n  <A, E, R, A2>(self: STM.STM<A, E, R>, pf: (a: A) => Option.Option<A2>) => STM.STM<A2, E, R>\n>(2, (self, pf) =>\n  collectSTM(\n    self,\n    (a) => Option.map(pf(a), core.succeed)\n  ))\n\n/** @internal */\nexport const collectSTM = dual<\n  <A, A2, E2, R2>(\n    pf: (a: A) => Option.Option<STM.STM<A2, E2, R2>>\n  ) => <E, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<A2, E2 | E, R2 | R>,\n  <A, E, R, A2, E2, R2>(\n    self: STM.STM<A, E, R>,\n    pf: (a: A) => Option.Option<STM.STM<A2, E2, R2>>\n  ) => STM.STM<A2, E2 | E, R2 | R>\n>(2, (self, pf) =>\n  core.matchSTM(self, {\n    onFailure: core.fail,\n    onSuccess: (a) => {\n      const option = pf(a)\n      return Option.isSome(option) ? option.value : core.retry\n    }\n  }))\n\n/** @internal */\nexport const commitEither = <A, E, R>(self: STM.STM<A, E, R>): Effect.Effect<A, E, R> =>\n  Effect.flatten(core.commit(either(self)))\n\n/** @internal */\nexport const cond = <A, E>(\n  predicate: LazyArg<boolean>,\n  error: LazyArg<E>,\n  result: LazyArg<A>\n): STM.STM<A, E> => {\n  return suspend(\n    () => predicate() ? core.sync(result) : core.failSync(error)\n  )\n}\n\n/** @internal */\nexport const either = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<Either.Either<A, E>, never, R> =>\n  match(self, { onFailure: Either.left, onSuccess: Either.right })\n\n/** @internal */\nexport const eventually = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<A, E, R> =>\n  core.matchSTM(self, { onFailure: () => eventually(self), onSuccess: core.succeed })\n\n/** @internal */\nexport const every = dual<\n  <A, R, E>(\n    predicate: (a: Types.NoInfer<A>) => STM.STM<boolean, E, R>\n  ) => (iterable: Iterable<A>) => STM.STM<boolean, E, R>,\n  <A, R, E>(iterable: Iterable<A>, predicate: (a: A) => STM.STM<boolean, E, R>) => STM.STM<boolean, E, R>\n>(\n  2,\n  <A, R, E>(\n    iterable: Iterable<A>,\n    predicate: (a: A) => STM.STM<boolean, E, R>\n  ): STM.STM<boolean, E, R> =>\n    pipe(\n      core.flatMap(core.sync(() => iterable[Symbol.iterator]()), (iterator) => {\n        const loop: STM.STM<boolean, E, R> = suspend(() => {\n          const next = iterator.next()\n          if (next.done) {\n            return core.succeed(true)\n          }\n          return pipe(\n            predicate(next.value),\n            core.flatMap((bool) => bool ? loop : core.succeed(bool))\n          )\n        })\n        return loop\n      })\n    )\n)\n\n/** @internal */\nexport const exists = dual<\n  <A, R, E>(\n    predicate: (a: Types.NoInfer<A>) => STM.STM<boolean, E, R>\n  ) => (iterable: Iterable<A>) => STM.STM<boolean, E, R>,\n  <A, R, E>(iterable: Iterable<A>, predicate: (a: A) => STM.STM<boolean, E, R>) => STM.STM<boolean, E, R>\n>(\n  2,\n  <A, R, E>(iterable: Iterable<A>, predicate: (a: A) => STM.STM<boolean, E, R>): STM.STM<boolean, E, R> =>\n    core.flatMap(core.sync(() => iterable[Symbol.iterator]()), (iterator) => {\n      const loop: STM.STM<boolean, E, R> = suspend(() => {\n        const next = iterator.next()\n        if (next.done) {\n          return core.succeed(false)\n        }\n        return core.flatMap(\n          predicate(next.value),\n          (bool) => bool ? core.succeed(bool) : loop\n        )\n      })\n      return loop\n    })\n)\n\n/** @internal */\nexport const fiberId: STM.STM<FiberId.FiberId> = core.effect<never, FiberId.FiberId>((_, fiberId) => fiberId)\n\n/** @internal */\nexport const filter = dual<\n  <A, R, E>(\n    predicate: (a: Types.NoInfer<A>) => STM.STM<boolean, E, R>\n  ) => (iterable: Iterable<A>) => STM.STM<Array<A>, E, R>,\n  <A, R, E>(iterable: Iterable<A>, predicate: (a: A) => STM.STM<boolean, E, R>) => STM.STM<Array<A>, E, R>\n>(\n  2,\n  <A, R, E>(iterable: Iterable<A>, predicate: (a: A) => STM.STM<boolean, E, R>): STM.STM<Array<A>, E, R> =>\n    Array.from(iterable).reduce(\n      (acc, curr) =>\n        pipe(\n          acc,\n          core.zipWith(predicate(curr), (as, p) => {\n            if (p) {\n              as.push(curr)\n              return as\n            }\n            return as\n          })\n        ),\n      core.succeed([]) as STM.STM<Array<A>, E, R>\n    )\n)\n\n/** @internal */\nexport const filterNot = dual<\n  <A, R, E>(\n    predicate: (a: Types.NoInfer<A>) => STM.STM<boolean, E, R>\n  ) => (iterable: Iterable<A>) => STM.STM<Array<A>, E, R>,\n  <A, R, E>(iterable: Iterable<A>, predicate: (a: A) => STM.STM<boolean, E, R>) => STM.STM<Array<A>, E, R>\n>(\n  2,\n  <A, R, E>(iterable: Iterable<A>, predicate: (a: A) => STM.STM<boolean, E, R>): STM.STM<Array<A>, E, R> =>\n    filter(iterable, (a) => negate(predicate(a)))\n)\n\n/** @internal */\nexport const filterOrDie: {\n  <A, B extends A>(\n    refinement: Refinement<Types.NoInfer<A>, B>,\n    defect: LazyArg<unknown>\n  ): <E, R>(self: STM.STM<A, E, R>) => STM.STM<B, E, R>\n  <A>(\n    predicate: Predicate<Types.NoInfer<A>>,\n    defect: LazyArg<unknown>\n  ): <E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E, R>\n  <A, E, R, B extends A>(\n    self: STM.STM<A, E, R>,\n    refinement: Refinement<A, B>,\n    defect: LazyArg<unknown>\n  ): STM.STM<B, E, R>\n  <A, E, R>(self: STM.STM<A, E, R>, predicate: Predicate<A>, defect: LazyArg<unknown>): STM.STM<A, E, R>\n} = dual(\n  3,\n  <A, E, R>(self: STM.STM<A, E, R>, predicate: Predicate<A>, defect: LazyArg<unknown>): STM.STM<A, E, R> =>\n    filterOrElse(self, predicate, () => core.dieSync(defect))\n)\n\n/** @internal */\nexport const filterOrDieMessage: {\n  <A, B extends A>(\n    refinement: Refinement<Types.NoInfer<A>, B>,\n    message: string\n  ): <E, R>(self: STM.STM<A, E, R>) => STM.STM<B, E, R>\n  <A>(predicate: Predicate<Types.NoInfer<A>>, message: string): <E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E, R>\n  <A, E, R, B extends A>(self: STM.STM<A, E, R>, refinement: Refinement<A, B>, message: string): STM.STM<B, E, R>\n  <A, E, R>(self: STM.STM<A, E, R>, predicate: Predicate<A>, message: string): STM.STM<A, E, R>\n} = dual(\n  3,\n  <A, E, R>(self: STM.STM<A, E, R>, predicate: Predicate<A>, message: string): STM.STM<A, E, R> =>\n    filterOrElse(self, predicate, () => core.dieMessage(message))\n)\n\n/** @internal */\nexport const filterOrElse: {\n  <A, B extends A, C, E2, R2>(\n    refinement: Refinement<Types.NoInfer<A>, B>,\n    orElse: (a: Types.NoInfer<A>) => STM.STM<C, E2, R2>\n  ): <E, R>(self: STM.STM<A, E, R>) => STM.STM<B | C, E2 | E, R2 | R>\n  <A, B, E2, R2>(\n    predicate: Predicate<Types.NoInfer<A>>,\n    orElse: (a: Types.NoInfer<A>) => STM.STM<B, E2, R2>\n  ): <E, R>(self: STM.STM<A, E, R>) => STM.STM<A | B, E2 | E, R2 | R>\n  <A, E, R, B extends A, C, E2, R2>(\n    self: STM.STM<A, E, R>,\n    refinement: Refinement<A, B>,\n    orElse: (a: A) => STM.STM<C, E2, R2>\n  ): STM.STM<B | C, E | E2, R | R2>\n  <A, E, R, B, E2, R2>(\n    self: STM.STM<A, E, R>,\n    predicate: Predicate<A>,\n    orElse: (a: A) => STM.STM<B, E2, R2>\n  ): STM.STM<A | B, E | E2, R | R2>\n} = dual(\n  3,\n  <A, E, R, B, E2, R2>(\n    self: STM.STM<A, E, R>,\n    predicate: Predicate<A>,\n    orElse: (a: A) => STM.STM<B, E2, R2>\n  ): STM.STM<A | B, E | E2, R | R2> =>\n    core.flatMap(self, (a): STM.STM<A | B, E2, R2> => predicate(a) ? core.succeed(a) : orElse(a))\n)\n\n/** @internal */\nexport const filterOrFail: {\n  <A, B extends A, E2>(\n    refinement: Refinement<Types.NoInfer<A>, B>,\n    orFailWith: (a: Types.NoInfer<A>) => E2\n  ): <E, R>(self: STM.STM<A, E, R>) => STM.STM<B, E2 | E, R>\n  <A, E2>(\n    predicate: Predicate<Types.NoInfer<A>>,\n    orFailWith: (a: Types.NoInfer<A>) => E2\n  ): <E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E2 | E, R>\n  <A, E, R, B extends A, E2>(\n    self: STM.STM<A, E, R>,\n    refinement: Refinement<A, B>,\n    orFailWith: (a: A) => E2\n  ): STM.STM<B, E | E2, R>\n  <A, E, R, E2>(self: STM.STM<A, E, R>, predicate: Predicate<A>, orFailWith: (a: A) => E2): STM.STM<A, E | E2, R>\n} = dual(\n  3,\n  <A, E, R, E2>(self: STM.STM<A, E, R>, predicate: Predicate<A>, orFailWith: (a: A) => E2): STM.STM<A, E | E2, R> =>\n    filterOrElse(\n      self,\n      predicate,\n      (a) => core.failSync(() => orFailWith(a))\n    )\n)\n\n/** @internal */\nexport const flatten = <A, E2, R2, E, R>(self: STM.STM<STM.STM<A, E2, R2>, E, R>): STM.STM<A, E | E2, R | R2> =>\n  core.flatMap(self, identity)\n\n/** @internal */\nexport const flip = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<E, A, R> =>\n  core.matchSTM(self, { onFailure: core.succeed, onSuccess: core.fail })\n\n/** @internal */\nexport const flipWith = dual<\n  <E, A, R, E2, A2, R2>(\n    f: (stm: STM.STM<E, A, R>) => STM.STM<E2, A2, R2>\n  ) => (\n    self: STM.STM<A, E, R>\n  ) => STM.STM<A | A2, E | E2, R | R2>,\n  <A, E, R, E2, A2, R2>(\n    self: STM.STM<A, E, R>,\n    f: (stm: STM.STM<E, A, R>) => STM.STM<E2, A2, R2>\n  ) => STM.STM<A | A2, E | E2, R | R2>\n>(2, (self, f) => flip(f(flip(self))))\n\n/** @internal */\nexport const match = dual<\n  <E, A2, A, A3>(options: {\n    readonly onFailure: (error: E) => A2\n    readonly onSuccess: (value: A) => A3\n  }) => <R>(self: STM.STM<A, E, R>) => STM.STM<A2 | A3, never, R>,\n  <A, E, R, A2, A3>(self: STM.STM<A, E, R>, options: {\n    readonly onFailure: (error: E) => A2\n    readonly onSuccess: (value: A) => A3\n  }) => STM.STM<A2 | A3, never, R>\n>(2, (self, { onFailure, onSuccess }) =>\n  core.matchSTM(self, {\n    onFailure: (e) => core.succeed(onFailure(e)),\n    onSuccess: (a) => core.succeed(onSuccess(a))\n  }))\n\n/** @internal */\nexport const forEach = dual<\n  {\n    <A, A2, E, R>(f: (a: A) => STM.STM<A2, E, R>, options?: {\n      readonly discard?: false | undefined\n    }): (elements: Iterable<A>) => STM.STM<Array<A2>, E, R>\n    <A, A2, E, R>(f: (a: A) => STM.STM<A2, E, R>, options: {\n      readonly discard: true\n    }): (elements: Iterable<A>) => STM.STM<void, E, R>\n  },\n  {\n    <A, A2, E, R>(elements: Iterable<A>, f: (a: A) => STM.STM<A2, E, R>, options?: {\n      readonly discard?: false | undefined\n    }): STM.STM<Array<A2>, E, R>\n    <A, A2, E, R>(elements: Iterable<A>, f: (a: A) => STM.STM<A2, E, R>, options: {\n      readonly discard: true\n    }): STM.STM<void, E, R>\n  }\n>(\n  (args) => predicate.isIterable(args[0]),\n  <A, A2, E, R>(iterable: Iterable<A>, f: (a: A) => STM.STM<A2, E, R>, options?: {\n    readonly discard?: boolean | undefined\n  }): STM.STM<any, E, R> => {\n    if (options?.discard) {\n      return pipe(\n        core.sync(() => iterable[Symbol.iterator]()),\n        core.flatMap((iterator) => {\n          const loop: STM.STM<void, E, R> = suspend(() => {\n            const next = iterator.next()\n            if (next.done) {\n              return void_\n            }\n            return pipe(f(next.value), core.flatMap(() => loop))\n          })\n          return loop\n        })\n      )\n    }\n\n    return suspend(() =>\n      RA.fromIterable(iterable).reduce(\n        (acc, curr) =>\n          core.zipWith(acc, f(curr), (array, elem) => {\n            array.push(elem)\n            return array\n          }),\n        core.succeed([]) as STM.STM<Array<A2>, E, R>\n      )\n    )\n  }\n)\n\n/** @internal */\nexport const fromEither = <A, E>(either: Either.Either<A, E>): STM.STM<A, E> => {\n  switch (either._tag) {\n    case \"Left\": {\n      return core.fail(either.left)\n    }\n    case \"Right\": {\n      return core.succeed(either.right)\n    }\n  }\n}\n\n/** @internal */\nexport const fromOption = <A>(option: Option.Option<A>): STM.STM<A, Option.Option<never>> =>\n  Option.match(option, {\n    onNone: () => core.fail(Option.none()),\n    onSome: core.succeed\n  })\n\n/**\n * Inspired by https://github.com/tusharmath/qio/pull/22 (revised)\n * @internal\n */\nexport const gen: typeof STM.gen = (...args) =>\n  suspend(() => {\n    const f = (args.length === 1)\n      ? args[0]\n      : args[1].bind(args[0])\n    const iterator = f(pipe)\n    const state = iterator.next()\n    const run = (\n      state: IteratorYieldResult<any> | IteratorReturnResult<any>\n    ): STM.STM<any, any, any> =>\n      state.done ?\n        core.succeed(state.value) :\n        core.flatMap(yieldWrapGet(state.value) as any, (val: any) => run(iterator.next(val as never)))\n    return run(state)\n  })\n\n/** @internal */\nexport const head = <A, E, R>(self: STM.STM<Iterable<A>, E, R>): STM.STM<A, Option.Option<E>, R> =>\n  pipe(\n    self,\n    core.matchSTM({\n      onFailure: (e) => core.fail(Option.some(e)),\n      onSuccess: (a) => {\n        const i = a[Symbol.iterator]()\n        const res = i.next()\n        if (res.done) {\n          return core.fail(Option.none())\n        } else {\n          return core.succeed(res.value)\n        }\n      }\n    })\n  )\n\n/** @internal */\nexport const if_ = dual<\n  <A, E1, R1, A2, E2, R2>(\n    options: {\n      readonly onTrue: STM.STM<A, E1, R1>\n      readonly onFalse: STM.STM<A2, E2, R2>\n    }\n  ) => <E = never, R = never>(\n    self: STM.STM<boolean, E, R> | boolean\n  ) => STM.STM<A | A2, E1 | E2 | E, R1 | R2 | R>,\n  {\n    <A, E1, R1, A2, E2, R2, E = never, R = never>(\n      self: boolean,\n      options: {\n        readonly onTrue: STM.STM<A, E1, R1>\n        readonly onFalse: STM.STM<A2, E2, R2>\n      }\n    ): STM.STM<A | A2, E1 | E2 | E, R1 | R2 | R>\n    <E, R, A, E1, R1, A2, E2, R2>(\n      self: STM.STM<boolean, E, R>,\n      options: {\n        readonly onTrue: STM.STM<A, E1, R1>\n        readonly onFalse: STM.STM<A2, E2, R2>\n      }\n    ): STM.STM<A | A2, E1 | E2 | E, R1 | R2 | R>\n  }\n>(\n  (args) => typeof args[0] === \"boolean\" || core.isSTM(args[0]),\n  <E, R, A, E1, R1, A2, E2, R2>(\n    self: STM.STM<boolean, E, R> | boolean,\n    { onFalse, onTrue }: {\n      readonly onTrue: STM.STM<A, E1, R1>\n      readonly onFalse: STM.STM<A2, E2, R2>\n    }\n  ) => {\n    if (typeof self === \"boolean\") {\n      return self ? onTrue : onFalse\n    }\n\n    return core.flatMap(self, (bool): STM.STM<A | A2, E1 | E2 | E, R1 | R2 | R> => bool ? onTrue : onFalse)\n  }\n)\n\n/** @internal */\nexport const ignore = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<void, never, R> =>\n  match(self, { onFailure: () => void_, onSuccess: () => void_ })\n\n/** @internal */\nexport const isFailure = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<boolean, never, R> =>\n  match(self, { onFailure: constTrue, onSuccess: constFalse })\n\n/** @internal */\nexport const isSuccess = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<boolean, never, R> =>\n  match(self, { onFailure: constFalse, onSuccess: constTrue })\n\n/** @internal */\nexport const iterate = <Z, E, R>(\n  initial: Z,\n  options: {\n    readonly while: (z: Z) => boolean\n    readonly body: (z: Z) => STM.STM<Z, E, R>\n  }\n): STM.STM<Z, E, R> => iterateLoop(initial, options.while, options.body)\n\nconst iterateLoop = <Z, E, R>(\n  initial: Z,\n  cont: (z: Z) => boolean,\n  body: (z: Z) => STM.STM<Z, E, R>\n): STM.STM<Z, E, R> => {\n  if (cont(initial)) {\n    return pipe(\n      body(initial),\n      core.flatMap((z) => iterateLoop(z, cont, body))\n    )\n  }\n  return core.succeed(initial)\n}\n\n/** @internal */\nexport const loop: {\n  <Z, A, E, R>(\n    initial: Z,\n    options: {\n      readonly while: (z: Z) => boolean\n      readonly step: (z: Z) => Z\n      readonly body: (z: Z) => STM.STM<A, E, R>\n      readonly discard?: false | undefined\n    }\n  ): STM.STM<Array<A>, E, R>\n  <Z, A, E, R>(\n    initial: Z,\n    options: {\n      readonly while: (z: Z) => boolean\n      readonly step: (z: Z) => Z\n      readonly body: (z: Z) => STM.STM<A, E, R>\n      readonly discard: true\n    }\n  ): STM.STM<void, E, R>\n} = <Z, A, E, R>(\n  initial: Z,\n  options: {\n    readonly while: (z: Z) => boolean\n    readonly step: (z: Z) => Z\n    readonly body: (z: Z) => STM.STM<A, E, R>\n    readonly discard?: boolean | undefined\n  }\n): STM.STM<any, E, R> =>\n  options.discard ?\n    loopDiscardLoop(initial, options.while, options.step, options.body) :\n    core.map(loopLoop(initial, options.while, options.step, options.body), (a) => Array.from(a))\n\nconst loopLoop = <Z, A, E, R>(\n  initial: Z,\n  cont: (z: Z) => boolean,\n  inc: (z: Z) => Z,\n  body: (z: Z) => STM.STM<A, E, R>\n): STM.STM<Chunk.Chunk<A>, E, R> => {\n  if (cont(initial)) {\n    return pipe(\n      body(initial),\n      core.flatMap((a) => pipe(loopLoop(inc(initial), cont, inc, body), core.map(Chunk.append(a))))\n    )\n  }\n  return core.succeed(Chunk.empty<A>())\n}\n\nconst loopDiscardLoop = <Z, R, E, X>(\n  initial: Z,\n  cont: (z: Z) => boolean,\n  inc: (z: Z) => Z,\n  body: (z: Z) => STM.STM<X, E, R>\n): STM.STM<void, E, R> => {\n  if (cont(initial)) {\n    return pipe(\n      body(initial),\n      core.flatMap(() => loopDiscardLoop(inc(initial), cont, inc, body))\n    )\n  }\n  return void_\n}\n\n/** @internal */\nexport const mapAttempt = dual<\n  <A, B>(f: (a: A) => B) => <E, R>(self: STM.STM<A, E, R>) => STM.STM<B, unknown, R>,\n  <A, E, R, B>(self: STM.STM<A, E, R>, f: (a: A) => B) => STM.STM<B, unknown, R>\n>(2, <A, E, R, B>(self: STM.STM<A, E, R>, f: (a: A) => B): STM.STM<B, unknown, R> =>\n  core.matchSTM(self, {\n    onFailure: (e) => core.fail(e),\n    onSuccess: (a) => attempt(() => f(a))\n  }))\n\n/** @internal */\nexport const mapBoth = dual<\n  <E, E2, A, A2>(options: {\n    readonly onFailure: (error: E) => E2\n    readonly onSuccess: (value: A) => A2\n  }) => <R>(self: STM.STM<A, E, R>) => STM.STM<A2, E2, R>,\n  <A, E, R, E2, A2>(self: STM.STM<A, E, R>, options: {\n    readonly onFailure: (error: E) => E2\n    readonly onSuccess: (value: A) => A2\n  }) => STM.STM<A2, E2, R>\n>(2, (self, { onFailure, onSuccess }) =>\n  core.matchSTM(self, {\n    onFailure: (e) => core.fail(onFailure(e)),\n    onSuccess: (a) => core.succeed(onSuccess(a))\n  }))\n\n/** @internal */\nexport const mapError = dual<\n  <E, E2>(f: (error: E) => E2) => <A, R>(self: STM.STM<A, E, R>) => STM.STM<A, E2, R>,\n  <A, E, R, E2>(self: STM.STM<A, E, R>, f: (error: E) => E2) => STM.STM<A, E2, R>\n>(2, (self, f) =>\n  core.matchSTM(self, {\n    onFailure: (e) => core.fail(f(e)),\n    onSuccess: core.succeed\n  }))\n\n/** @internal */\nexport const merge = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<E | A, never, R> =>\n  core.matchSTM(self, { onFailure: (e) => core.succeed(e), onSuccess: core.succeed })\n\n/** @internal */\nexport const mergeAll = dual<\n  <A2, A>(zero: A2, f: (a2: A2, a: A) => A2) => <E, R>(iterable: Iterable<STM.STM<A, E, R>>) => STM.STM<A2, E, R>,\n  <A, E, R, A2>(iterable: Iterable<STM.STM<A, E, R>>, zero: A2, f: (a2: A2, a: A) => A2) => STM.STM<A2, E, R>\n>(\n  3,\n  <A, E, R, A2>(iterable: Iterable<STM.STM<A, E, R>>, zero: A2, f: (a2: A2, a: A) => A2): STM.STM<A2, E, R> =>\n    suspend(() =>\n      Array.from(iterable).reduce(\n        (acc, curr) => pipe(acc, core.zipWith(curr, f)),\n        core.succeed(zero) as STM.STM<A2, E, R>\n      )\n    )\n)\n\n/** @internal */\nexport const negate = <E, R>(self: STM.STM<boolean, E, R>): STM.STM<boolean, E, R> => pipe(self, core.map((b) => !b))\n\n/** @internal */\nexport const none = <A, E, R>(self: STM.STM<Option.Option<A>, E, R>): STM.STM<void, Option.Option<E>, R> =>\n  core.matchSTM(self, {\n    onFailure: (e) => core.fail(Option.some(e)),\n    onSuccess: Option.match({\n      onNone: () => void_,\n      onSome: () => core.fail(Option.none())\n    })\n  })\n\n/** @internal */\nexport const option = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<Option.Option<A>, never, R> =>\n  match(self, { onFailure: () => Option.none(), onSuccess: Option.some })\n\n/** @internal */\nexport const orDie = <A, E, R>(self: STM.STM<A, E, R>): STM.STM<A, never, R> => pipe(self, orDieWith(identity))\n\n/** @internal */\nexport const orDieWith = dual<\n  <E>(f: (error: E) => unknown) => <A, R>(self: STM.STM<A, E, R>) => STM.STM<A, never, R>,\n  <A, E, R>(self: STM.STM<A, E, R>, f: (error: E) => unknown) => STM.STM<A, never, R>\n>(2, (self, f) => pipe(self, mapError(f), core.catchAll(core.die)))\n\n/** @internal */\nexport const orElse = dual<\n  <A2, E2, R2>(that: LazyArg<STM.STM<A2, E2, R2>>) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<A2 | A, E2, R2 | R>,\n  <A, E, R, A2, E2, R2>(self: STM.STM<A, E, R>, that: LazyArg<STM.STM<A2, E2, R2>>) => STM.STM<A2 | A, E2, R2 | R>\n>(\n  2,\n  <A, E, R, A2, E2, R2>(self: STM.STM<A, E, R>, that: LazyArg<STM.STM<A2, E2, R2>>): STM.STM<A2 | A, E2, R2 | R> =>\n    core.flatMap(core.effect<R, LazyArg<void>>((journal) => Journal.prepareResetJournal(journal)), (reset) =>\n      pipe(\n        core.orTry(self, () => core.flatMap(core.sync(reset), that)),\n        core.catchAll(() => core.flatMap(core.sync(reset), that))\n      ))\n)\n\n/** @internal */\nexport const orElseEither = dual<\n  <A2, E2, R2>(\n    that: LazyArg<STM.STM<A2, E2, R2>>\n  ) => <A, E, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<Either.Either<A2, A>, E2, R2 | R>,\n  <A, E, R, A2, E2, R2>(\n    self: STM.STM<A, E, R>,\n    that: LazyArg<STM.STM<A2, E2, R2>>\n  ) => STM.STM<Either.Either<A2, A>, E2, R2 | R>\n>(\n  2,\n  <A, E, R, A2, E2, R2>(\n    self: STM.STM<A, E, R>,\n    that: LazyArg<STM.STM<A2, E2, R2>>\n  ): STM.STM<Either.Either<A2, A>, E2, R2 | R> =>\n    orElse(core.map(self, Either.left), () => core.map(that(), Either.right))\n)\n\n/** @internal */\nexport const orElseFail = dual<\n  <E2>(error: LazyArg<E2>) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E2, R>,\n  <A, E, R, E2>(self: STM.STM<A, E, R>, error: LazyArg<E2>) => STM.STM<A, E2, R>\n>(\n  2,\n  <A, E, R, E2>(self: STM.STM<A, E, R>, error: LazyArg<E2>): STM.STM<A, E2, R> =>\n    orElse(self, () => core.failSync(error))\n)\n\n/** @internal */\nexport const orElseOptional = dual<\n  <A2, E2, R2>(\n    that: LazyArg<STM.STM<A2, Option.Option<E2>, R2>>\n  ) => <A, E, R>(\n    self: STM.STM<A, Option.Option<E>, R>\n  ) => STM.STM<A2 | A, Option.Option<E2 | E>, R2 | R>,\n  <A, E, R, A2, E2, R2>(\n    self: STM.STM<A, Option.Option<E>, R>,\n    that: LazyArg<STM.STM<A2, Option.Option<E2>, R2>>\n  ) => STM.STM<A2 | A, Option.Option<E2 | E>, R2 | R>\n>(\n  2,\n  <A, E, R, A2, E2, R2>(\n    self: STM.STM<A, Option.Option<E>, R>,\n    that: LazyArg<STM.STM<A2, Option.Option<E2>, R2>>\n  ): STM.STM<A2 | A, Option.Option<E2 | E>, R2 | R> =>\n    core.catchAll(\n      self,\n      Option.match({\n        onNone: that,\n        onSome: (e) => core.fail(Option.some<E | E2>(e))\n      })\n    )\n)\n\n/** @internal */\nexport const orElseSucceed = dual<\n  <A2>(value: LazyArg<A2>) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<A2 | A, never, R>,\n  <A, E, R, A2>(self: STM.STM<A, E, R>, value: LazyArg<A2>) => STM.STM<A2 | A, never, R>\n>(\n  2,\n  <A, E, R, A2>(self: STM.STM<A, E, R>, value: LazyArg<A2>): STM.STM<A2 | A, never, R> =>\n    orElse(self, () => core.sync(value))\n)\n\n/** @internal */\nexport const provideContext = dual<\n  <R>(env: Context.Context<R>) => <A, E>(self: STM.STM<A, E, R>) => STM.STM<A, E>,\n  <A, E, R>(self: STM.STM<A, E, R>, env: Context.Context<R>) => STM.STM<A, E>\n>(2, (self, env) => core.mapInputContext(self, (_: Context.Context<never>) => env))\n\n/** @internal */\nexport const provideSomeContext = dual<\n  <R>(context: Context.Context<R>) => <R1, E, A>(self: STM.STM<A, E, R1>) => STM.STM<A, E, Exclude<R1, R>>,\n  <R, R1, E, A>(self: STM.STM<A, E, R1>, context: Context.Context<R>) => STM.STM<A, E, Exclude<R1, R>>\n>(2, <R, R1, E, A>(\n  self: STM.STM<A, E, R1>,\n  context: Context.Context<R>\n): STM.STM<A, E, Exclude<R1, R>> =>\n  core.mapInputContext(\n    self,\n    (parent: Context.Context<Exclude<R1, R>>): Context.Context<R1> => Context.merge(parent, context) as any\n  ))\n\n/** @internal */\nexport const provideService = dual<\n  <I, S>(\n    tag: Context.Tag<I, S>,\n    resource: Types.NoInfer<S>\n  ) => <A, E, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<A, E, Exclude<R, I>>,\n  <A, E, R, I, S>(\n    self: STM.STM<A, E, R>,\n    tag: Context.Tag<I, S>,\n    resource: Types.NoInfer<S>\n  ) => STM.STM<A, E, Exclude<R, I>>\n>(3, (self, tag, resource) => provideServiceSTM(self, tag, core.succeed(resource)))\n\n/** @internal */\nexport const provideServiceSTM = dual<\n  <I, S, E1, R1>(\n    tag: Context.Tag<I, S>,\n    stm: STM.STM<Types.NoInfer<S>, E1, R1>\n  ) => <A, E, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<A, E1 | E, R1 | Exclude<R, I>>,\n  <A, E, R, I, S, E1, R1>(\n    self: STM.STM<A, E, R>,\n    tag: Context.Tag<I, S>,\n    stm: STM.STM<Types.NoInfer<S>, E1, R1>\n  ) => STM.STM<A, E1 | E, R1 | Exclude<R, I>>\n>(3, <A, E, R, I, S, E1, R1>(\n  self: STM.STM<A, E, R>,\n  tag: Context.Tag<I, S>,\n  stm: STM.STM<Types.NoInfer<S>, E1, R1>\n): STM.STM<A, E1 | E, R1 | Exclude<R, I>> =>\n  core.contextWithSTM((env: Context.Context<R1 | Exclude<R, I>>) =>\n    core.flatMap(\n      stm,\n      (service) =>\n        provideContext(\n          self,\n          Context.add(env, tag, service) as Context.Context<R | R1>\n        )\n    )\n  ))\n\n/** @internal */\nexport const reduce = dual<\n  <S, A, E, R>(zero: S, f: (s: S, a: A) => STM.STM<S, E, R>) => (iterable: Iterable<A>) => STM.STM<S, E, R>,\n  <S, A, E, R>(iterable: Iterable<A>, zero: S, f: (s: S, a: A) => STM.STM<S, E, R>) => STM.STM<S, E, R>\n>(\n  3,\n  <S, A, R, E>(iterable: Iterable<A>, zero: S, f: (s: S, a: A) => STM.STM<S, E, R>): STM.STM<S, E, R> =>\n    suspend(() =>\n      Array.from(iterable).reduce(\n        (acc, curr) => pipe(acc, core.flatMap((s) => f(s, curr))),\n        core.succeed(zero) as STM.STM<S, E, R>\n      )\n    )\n)\n\n/** @internal */\nexport const reduceAll = dual<\n  <A, E2, R2>(\n    initial: STM.STM<A, E2, R2>,\n    f: (x: A, y: A) => A\n  ) => <E, R>(\n    iterable: Iterable<STM.STM<A, E, R>>\n  ) => STM.STM<A, E2 | E, R2 | R>,\n  <A, E, R, E2, R2>(\n    iterable: Iterable<STM.STM<A, E, R>>,\n    initial: STM.STM<A, E2, R2>,\n    f: (x: A, y: A) => A\n  ) => STM.STM<A, E2 | E, R2 | R>\n>(3, <A, E, R, E2, R2>(\n  iterable: Iterable<STM.STM<A, E, R>>,\n  initial: STM.STM<A, E2, R2>,\n  f: (x: A, y: A) => A\n): STM.STM<A, E2 | E, R2 | R> =>\n  suspend(() =>\n    Array.from(iterable).reduce(\n      (acc, curr) => pipe(acc, core.zipWith(curr, f)),\n      initial as STM.STM<A, E | E2, R | R2>\n    )\n  ))\n\n/** @internal */\nexport const reduceRight = dual<\n  <S, A, R, E>(zero: S, f: (s: S, a: A) => STM.STM<S, E, R>) => (iterable: Iterable<A>) => STM.STM<S, E, R>,\n  <S, A, R, E>(iterable: Iterable<A>, zero: S, f: (s: S, a: A) => STM.STM<S, E, R>) => STM.STM<S, E, R>\n>(\n  3,\n  <S, A, R, E>(iterable: Iterable<A>, zero: S, f: (s: S, a: A) => STM.STM<S, E, R>): STM.STM<S, E, R> =>\n    suspend(() =>\n      Array.from(iterable).reduceRight(\n        (acc, curr) => pipe(acc, core.flatMap((s) => f(s, curr))),\n        core.succeed(zero) as STM.STM<S, E, R>\n      )\n    )\n)\n\n/** @internal */\nexport const refineOrDie = dual<\n  <E, E2>(pf: (error: E) => Option.Option<E2>) => <A, R>(self: STM.STM<A, E, R>) => STM.STM<A, E2, R>,\n  <A, E, R, E2>(self: STM.STM<A, E, R>, pf: (error: E) => Option.Option<E2>) => STM.STM<A, E2, R>\n>(2, (self, pf) => refineOrDieWith(self, pf, identity))\n\n/** @internal */\nexport const refineOrDieWith = dual<\n  <E, E2>(\n    pf: (error: E) => Option.Option<E2>,\n    f: (error: E) => unknown\n  ) => <A, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<A, E2, R>,\n  <A, E, R, E2>(\n    self: STM.STM<A, E, R>,\n    pf: (error: E) => Option.Option<E2>,\n    f: (error: E) => unknown\n  ) => STM.STM<A, E2, R>\n>(3, (self, pf, f) =>\n  core.catchAll(\n    self,\n    (e) =>\n      Option.match(pf(e), {\n        onNone: () => core.die(f(e)),\n        onSome: core.fail\n      })\n  ))\n\n/** @internal */\nexport const reject = dual<\n  <A, E2>(pf: (a: A) => Option.Option<E2>) => <E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E2 | E, R>,\n  <A, E, R, E2>(self: STM.STM<A, E, R>, pf: (a: A) => Option.Option<E2>) => STM.STM<A, E2 | E, R>\n>(2, (self, pf) =>\n  rejectSTM(\n    self,\n    (a) => Option.map(pf(a), core.fail)\n  ))\n\n/** @internal */\nexport const rejectSTM = dual<\n  <A, E2, R2>(\n    pf: (a: A) => Option.Option<STM.STM<E2, E2, R2>>\n  ) => <E, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<A, E2 | E, R2 | R>,\n  <A, E, R, E2, R2>(\n    self: STM.STM<A, E, R>,\n    pf: (a: A) => Option.Option<STM.STM<E2, E2, R2>>\n  ) => STM.STM<A, E2 | E, R2 | R>\n>(2, (self, pf) =>\n  core.flatMap(self, (a) =>\n    Option.match(pf(a), {\n      onNone: () => core.succeed(a),\n      onSome: core.flatMap(core.fail)\n    })))\n\n/** @internal */\nexport const repeatUntil = dual<\n  <A>(predicate: Predicate<A>) => <E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E, R>,\n  <A, E, R>(self: STM.STM<A, E, R>, predicate: Predicate<A>) => STM.STM<A, E, R>\n>(2, (self, predicate) => repeatUntilLoop(self, predicate))\n\nconst repeatUntilLoop = <A, E, R>(self: STM.STM<A, E, R>, predicate: Predicate<A>): STM.STM<A, E, R> =>\n  core.flatMap(self, (a) =>\n    predicate(a) ?\n      core.succeed(a) :\n      repeatUntilLoop(self, predicate))\n\n/** @internal */\nexport const repeatWhile = dual<\n  <A>(predicate: Predicate<A>) => <E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E, R>,\n  <A, E, R>(self: STM.STM<A, E, R>, predicate: Predicate<A>) => STM.STM<A, E, R>\n>(2, (self, predicate) => repeatWhileLoop(self, predicate))\n\nconst repeatWhileLoop = <A, E, R>(self: STM.STM<A, E, R>, predicate: Predicate<A>): STM.STM<A, E, R> =>\n  pipe(\n    core.flatMap(self, (a) =>\n      predicate(a) ?\n        repeatWhileLoop(self, predicate) :\n        core.succeed(a))\n  )\n\n/** @internal */\nexport const replicate = dual<\n  (n: number) => <A, E, R>(self: STM.STM<A, E, R>) => Array<STM.STM<A, E, R>>,\n  <A, E, R>(self: STM.STM<A, E, R>, n: number) => Array<STM.STM<A, E, R>>\n>(2, (self, n) => Array.from({ length: n }, () => self))\n\n/** @internal */\nexport const replicateSTM = dual<\n  (n: number) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<Array<A>, E, R>,\n  <A, E, R>(self: STM.STM<A, E, R>, n: number) => STM.STM<Array<A>, E, R>\n>(2, (self, n) => all(replicate(self, n)))\n\n/** @internal */\nexport const replicateSTMDiscard = dual<\n  (n: number) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<void, E, R>,\n  <A, E, R>(self: STM.STM<A, E, R>, n: number) => STM.STM<void, E, R>\n>(2, (self, n) => all(replicate(self, n), { discard: true }))\n\n/** @internal */\nexport const retryUntil = dual<\n  {\n    <A, B extends A>(refinement: Refinement<Types.NoInfer<A>, B>): <E, R>(self: STM.STM<A, E, R>) => STM.STM<B, E, R>\n    <A>(predicate: Predicate<A>): <E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E, R>\n  },\n  {\n    <A, E, R, B extends A>(self: STM.STM<A, E, R>, refinement: Refinement<A, B>): STM.STM<B, E, R>\n    <A, E, R>(self: STM.STM<A, E, R>, predicate: Predicate<A>): STM.STM<A, E, R>\n  }\n>(\n  2,\n  <A, E, R>(self: STM.STM<A, E, R>, predicate: Predicate<A>) =>\n    core.matchSTM(self, { onFailure: core.fail, onSuccess: (a) => predicate(a) ? core.succeed(a) : core.retry })\n)\n\n/** @internal */\nexport const retryWhile = dual<\n  <A>(predicate: Predicate<A>) => <E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E, R>,\n  <A, E, R>(self: STM.STM<A, E, R>, predicate: Predicate<A>) => STM.STM<A, E, R>\n>(\n  2,\n  (self, predicate) =>\n    core.matchSTM(self, { onFailure: core.fail, onSuccess: (a) => !predicate(a) ? core.succeed(a) : core.retry })\n)\n\n/** @internal */\nexport const partition = dual<\n  <A, A2, E, R>(\n    f: (a: A) => STM.STM<A2, E, R>\n  ) => (\n    elements: Iterable<A>\n  ) => STM.STM<[excluded: Array<E>, satisfying: Array<A2>], never, R>,\n  <A, A2, E, R>(\n    elements: Iterable<A>,\n    f: (a: A) => STM.STM<A2, E, R>\n  ) => STM.STM<[excluded: Array<E>, satisfying: Array<A2>], never, R>\n>(2, (elements, f) =>\n  pipe(\n    forEach(elements, (a) => either(f(a))),\n    core.map((as) => effectCore.partitionMap(as, identity))\n  ))\n\n/** @internal */\nexport const some = <A, E, R>(self: STM.STM<Option.Option<A>, E, R>): STM.STM<A, Option.Option<E>, R> =>\n  core.matchSTM(self, {\n    onFailure: (e) => core.fail(Option.some(e)),\n    onSuccess: Option.match({\n      onNone: () => core.fail(Option.none()),\n      onSome: core.succeed\n    })\n  })\n\n/* @internal */\nexport const all = ((\n  input: Iterable<STM.All.STMAny> | Record<string, STM.All.STMAny>,\n  options?: STM.All.Options\n): STM.STM<any, any, any> => {\n  if (Symbol.iterator in input) {\n    return forEach(input, identity, options as any)\n  } else if (options?.discard) {\n    return forEach(Object.values(input), identity, options as any)\n  }\n\n  return core.map(\n    forEach(\n      Object.entries(input),\n      ([_, e]) => core.map(e, (a) => [_, a] as const)\n    ),\n    (values) => {\n      const res = {}\n      for (const [k, v] of values) {\n        ;(res as any)[k] = v\n      }\n      return res\n    }\n  )\n}) as STM.All.Signature\n\n/** @internal */\nexport const succeedNone: STM.STM<Option.Option<never>> = core.succeed(Option.none())\n\n/** @internal */\nexport const succeedSome = <A>(value: A): STM.STM<Option.Option<A>> => core.succeed(Option.some(value))\n\n/** @internal */\nexport const summarized = dual<\n  <A2, E2, R2, A3>(\n    summary: STM.STM<A2, E2, R2>,\n    f: (before: A2, after: A2) => A3\n  ) => <A, E, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<[A3, A], E2 | E, R2 | R>,\n  <A, E, R, A2, E2, R2, A3>(\n    self: STM.STM<A, E, R>,\n    summary: STM.STM<A2, E2, R2>,\n    f: (before: A2, after: A2) => A3\n  ) => STM.STM<[A3, A], E2 | E, R2 | R>\n>(3, (self, summary, f) =>\n  core.flatMap(summary, (start) =>\n    core.flatMap(self, (value) =>\n      core.map(\n        summary,\n        (end) => [f(start, end), value]\n      ))))\n\n/** @internal */\nexport const suspend = <A, E, R>(evaluate: LazyArg<STM.STM<A, E, R>>): STM.STM<A, E, R> => flatten(core.sync(evaluate))\n\n/** @internal */\nexport const tap: {\n  <A, X, E2, R2>(f: (a: A) => STM.STM<X, E2, R2>): <E, R>(self: STM.STM<A, E, R>) => STM.STM<A, E2 | E, R2 | R>\n  <A, E, R, X, E2, R2>(self: STM.STM<A, E, R>, f: (a: A) => STM.STM<X, E2, R2>): STM.STM<A, E | E2, R | R2>\n} = dual(\n  2,\n  <A, E, R, X, E2, R2>(self: STM.STM<A, E, R>, f: (a: A) => STM.STM<X, E2, R2>): STM.STM<A, E | E2, R | R2> =>\n    core.flatMap(self, (a) => as(f(a), a))\n)\n\n/** @internal */\nexport const tapBoth = dual<\n  <XE extends E, A2, E2, R2, XA extends A, A3, E3, R3, A, E>(\n    options: {\n      readonly onFailure: (error: XE) => STM.STM<A2, E2, R2>\n      readonly onSuccess: (value: XA) => STM.STM<A3, E3, R3>\n    }\n  ) => <R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<A, E | E2 | E3, R2 | R3 | R>,\n  <A, E, R, XE extends E, A2, E2, R2, XA extends A, A3, E3, R3>(\n    self: STM.STM<A, E, R>,\n    options: {\n      readonly onFailure: (error: XE) => STM.STM<A2, E2, R2>\n      readonly onSuccess: (value: XA) => STM.STM<A3, E3, R3>\n    }\n  ) => STM.STM<A, E | E2 | E3, R2 | R3 | R>\n>(2, (self, { onFailure, onSuccess }) =>\n  core.matchSTM(self, {\n    onFailure: (e) => pipe(onFailure(e as any), core.zipRight(core.fail(e))),\n    onSuccess: (a) => pipe(onSuccess(a as any), as(a))\n  }))\n\n/** @internal */\nexport const tapError: {\n  <E, X, E2, R2>(\n    f: (error: Types.NoInfer<E>) => STM.STM<X, E2, R2>\n  ): <A, R>(self: STM.STM<A, E, R>) => STM.STM<A, E | E2, R2 | R>\n  <A, E, R, X, E2, R2>(self: STM.STM<A, E, R>, f: (error: E) => STM.STM<X, E2, R2>): STM.STM<A, E | E2, R | R2>\n} = dual(\n  2,\n  <A, E, R, X, E2, R2>(self: STM.STM<A, E, R>, f: (error: E) => STM.STM<X, E2, R2>): STM.STM<A, E | E2, R | R2> =>\n    core.matchSTM(self, {\n      onFailure: (e) => core.zipRight(f(e), core.fail(e)),\n      onSuccess: core.succeed\n    })\n)\n\n/** @internal */\nexport const try_: {\n  <A, E>(options: {\n    readonly try: LazyArg<A>\n    readonly catch: (u: unknown) => E\n  }): STM.STM<A, E>\n  <A>(try_: LazyArg<A>): STM.STM<A, unknown>\n} = <A, E>(\n  arg: LazyArg<A> | {\n    readonly try: LazyArg<A>\n    readonly catch: (u: unknown) => E\n  }\n) => {\n  const evaluate = typeof arg === \"function\" ? arg : arg.try\n  return suspend(() => {\n    try {\n      return core.succeed(evaluate())\n    } catch (error) {\n      return core.fail(\"catch\" in arg ? arg.catch(error) : error)\n    }\n  })\n}\n\n/** @internal */\nconst void_: STM.STM<void> = core.succeed(void 0)\nexport {\n  /** @internal */\n  void_ as void\n}\n\n/** @internal */\nexport const unless = dual<\n  (predicate: LazyArg<boolean>) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<Option.Option<A>, E, R>,\n  <A, E, R>(self: STM.STM<A, E, R>, predicate: LazyArg<boolean>) => STM.STM<Option.Option<A>, E, R>\n>(2, (self, predicate) =>\n  suspend(\n    () => predicate() ? succeedNone : asSome(self)\n  ))\n\n/** @internal */\nexport const unlessSTM = dual<\n  <E2, R2>(\n    predicate: STM.STM<boolean, E2, R2>\n  ) => <A, E, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<Option.Option<A>, E2 | E, R2 | R>,\n  <A, E, R, E2, R2>(\n    self: STM.STM<A, E, R>,\n    predicate: STM.STM<boolean, E2, R2>\n  ) => STM.STM<Option.Option<A>, E2 | E, R2 | R>\n>(2, (self, predicate) =>\n  core.flatMap(\n    predicate,\n    (bool) => bool ? succeedNone : asSome(self)\n  ))\n\n/** @internal */\nexport const unsome = <A, E, R>(self: STM.STM<A, Option.Option<E>, R>): STM.STM<Option.Option<A>, E, R> =>\n  core.matchSTM(self, {\n    onFailure: Option.match({\n      onNone: () => core.succeed(Option.none()),\n      onSome: core.fail\n    }),\n    onSuccess: (a) => core.succeed(Option.some(a))\n  })\n\n/** @internal */\nexport const validateAll = dual<\n  <A, B, E, R>(\n    f: (a: A) => STM.STM<B, E, R>\n  ) => (\n    elements: Iterable<A>\n  ) => STM.STM<Array<B>, RA.NonEmptyArray<E>, R>,\n  <A, B, E, R>(\n    elements: Iterable<A>,\n    f: (a: A) => STM.STM<B, E, R>\n  ) => STM.STM<Array<B>, RA.NonEmptyArray<E>, R>\n>(\n  2,\n  (elements, f) =>\n    core.flatMap(partition(elements, f), ([errors, values]) =>\n      RA.isNonEmptyArray(errors) ?\n        core.fail(errors) :\n        core.succeed(values))\n)\n\n/** @internal */\nexport const validateFirst = dual<\n  <A, B, E, R>(f: (a: A) => STM.STM<B, E, R>) => (elements: Iterable<A>) => STM.STM<B, Array<E>, R>,\n  <A, B, E, R>(elements: Iterable<A>, f: (a: A) => STM.STM<B, E, R>) => STM.STM<B, Array<E>, R>\n>(2, (elements, f) => flip(forEach(elements, (a) => flip(f(a)))))\n\n/** @internal */\nexport const when = dual<\n  (predicate: LazyArg<boolean>) => <A, E, R>(self: STM.STM<A, E, R>) => STM.STM<Option.Option<A>, E, R>,\n  <A, E, R>(self: STM.STM<A, E, R>, predicate: LazyArg<boolean>) => STM.STM<Option.Option<A>, E, R>\n>(2, (self, predicate) =>\n  suspend(\n    () => predicate() ? asSome(self) : succeedNone\n  ))\n\n/** @internal */\nexport const whenSTM = dual<\n  <E2, R2>(\n    predicate: STM.STM<boolean, E2, R2>\n  ) => <A, E, R>(\n    self: STM.STM<A, E, R>\n  ) => STM.STM<Option.Option<A>, E2 | E, R2 | R>,\n  <A, E, R, E2, R2>(\n    self: STM.STM<A, E, R>,\n    predicate: STM.STM<boolean, E2, R2>\n  ) => STM.STM<Option.Option<A>, E2 | E, R2 | R>\n>(2, (self, predicate) =>\n  core.flatMap(\n    predicate,\n    (bool) => bool ? asSome(self) : succeedNone\n  ))\n"], "names": ["RA", "Cause", "Chunk", "Context", "Effect", "Either", "Exit", "constFalse", "constTrue", "constVoid", "dual", "identity", "pipe", "Option", "predicate", "yieldWrapGet", "effectCore", "core", "Journal", "STMState", "acquireUseRelease", "acquire", "use", "release", "uninterruptibleMask", "restore", "state", "running", "unsafeAtomically", "exit", "done", "interrupted", "matchCauseEffect", "onFailure", "cause", "isDone", "isSuccess", "value", "cause2", "failCause", "parallel", "onSuccess", "a", "a2", "as", "self", "map", "asSome", "some", "asSomeError", "mapError", "asVoid", "attempt", "evaluate", "suspend", "succeed", "defect", "fail", "bind", "tag", "f", "flatMap", "k", "bindTo", "let_", "catchSome", "pf", "catchAll", "e", "getOr<PERSON><PERSON>e", "catchTag", "catchTags", "cases", "keys", "Object", "includes", "check", "void_", "retry", "collect", "collectSTM", "matchSTM", "option", "isSome", "commit<PERSON>ither", "flatten", "commit", "either", "cond", "error", "result", "sync", "failSync", "match", "left", "right", "eventually", "every", "iterable", "Symbol", "iterator", "loop", "next", "bool", "exists", "fiberId", "effect", "_", "filter", "Array", "from", "reduce", "acc", "curr", "zipWith", "p", "push", "filterNot", "negate", "filterOr<PERSON>ie", "filterOrElse", "dieSync", "filterOrDieMessage", "message", "dieMessage", "orElse", "filterOrFail", "orFailWith", "flip", "flipWith", "for<PERSON>ach", "args", "isIterable", "options", "discard", "fromIterable", "array", "elem", "fromEither", "_tag", "fromOption", "onNone", "none", "onSome", "gen", "length", "run", "val", "head", "i", "res", "if_", "isSTM", "onFalse", "onTrue", "ignore", "isFailure", "iterate", "initial", "iterateLoop", "while", "body", "cont", "z", "loopDiscardLoop", "step", "loopLoop", "inc", "append", "empty", "mapAttempt", "mapBoth", "merge", "mergeAll", "zero", "b", "<PERSON><PERSON><PERSON>", "orDieWith", "die", "that", "journal", "prepareResetJournal", "reset", "orTry", "or<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orElseFail", "orElseOptional", "orElseSucceed", "provideContext", "env", "mapInputContext", "provideSomeContext", "context", "parent", "provideService", "resource", "provideServiceSTM", "stm", "contextWithSTM", "service", "add", "s", "reduceAll", "reduceRight", "refineOrDie", "refineOrDieWith", "reject", "rejectSTM", "repeatUntil", "repeatUntilLoop", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replicate", "n", "replicateSTM", "all", "replicateSTMDiscard", "retryUntil", "re<PERSON><PERSON><PERSON><PERSON>", "partition", "elements", "partitionMap", "input", "values", "entries", "v", "<PERSON><PERSON><PERSON>", "succeedSome", "summarized", "summary", "start", "end", "tap", "tapBoth", "zipRight", "tapError", "try_", "arg", "try", "catch", "void", "unless", "unlessSTM", "unsome", "validateAll", "errors", "isNonEmptyArray", "validate<PERSON><PERSON><PERSON>", "when", "whenSTM"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,EAAE,MAAM,gBAAgB;AACpC,OAAO,KAAKC,KAAK,MAAM,gBAAgB;AACvC,OAAO,KAAKC,KAAK,MAAM,gBAAgB;AACvC,OAAO,KAAKC,OAAO,MAAM,kBAAkB;AAC3C,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,OAAO,KAAKC,IAAI,MAAM,eAAe;AAGrC,SAASC,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,mBAAmB;AAC1F,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AAEzC,OAAO,KAAKC,SAAS,MAAM,oBAAoB;AAG/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,KAAKC,UAAU,MAAM,YAAY;AACxC,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,OAAO,KAAKC,QAAQ,MAAM,eAAe;;;;;;;;;;;;;;;;AAGlC,MAAMC,iBAAiB,GAAA,WAAA,yJAAGV,OAAAA,AAAI,EAYnC,CAAC,EAAE,CACHW,OAAyB,EACzBC,GAAyC,EACzCC,OAA6C,uJAE7CnB,MAAM,CAACoB,eAAAA,AAAmB,GAAEC,OAAO,IAAI;QACrC,IAAIC,KAAK,wKAA4BP,QAAQ,CAACQ,CAAO;QACrD,6JAAOf,OAAAA,AAAI,EACTa,OAAO,sKACLR,IAAI,CAACW,cAAAA,AAAgB,EACnBP,OAAO,EACNQ,IAAI,IAAI;YACPH,KAAK,4KAAGP,OAASW,AAAI,CAAL,CAACA,AAAKD,IAAI,CAAC;QAC7B,CAAC,EACD,MAAK;YACHH,KAAK,wKAAGP,QAAQ,CAACY,KAAW;QAC9B,CAAC,CACF,CACF,sJACD3B,MAAM,CAAC4B,YAAgB,AAAhBA,EAAiB;YACtBC,SAAS,GAAGC,KAAK,IAAI;gBACnB,6KAAIf,QAAQ,CAAO,AAANgB,EAAOT,KAAK,CAAC,sJAAIpB,IAAI,CAAC8B,OAAAA,AAAS,EAACV,KAAK,CAACG,IAAI,CAAC,EAAE;oBACxD,6JAAOjB,OAAAA,AAAI,EACTW,OAAO,CAACG,KAAK,CAACG,IAAI,CAACQ,KAAK,CAAC,sJACzBjC,MAAM,CAAC4B,YAAAA,AAAgB,EAAC;wBACtBC,SAAS,EAAGK,MAAM,uJAAKlC,MAAM,CAACmC,MAAAA,AAAS,qJAACtC,KAAK,CAACuC,KAAAA,AAAQ,EAACN,KAAK,EAAEI,MAAM,CAAC,CAAC;wBACtEG,SAAS,EAAEA,CAAA,uJAAMrC,MAAM,CAACmC,KAAAA,AAAS,EAACL,KAAK;qBACxC,CAAC,CACH;gBACH;gBACA,OAAO9B,MAAM,CAACmC,yJAAAA,AAAS,EAACL,KAAK,CAAC;YAChC,CAAC;YACDO,SAAS,GAAGC,CAAC,yJACX9B,OAAAA,AAAI,EACFa,OAAO,CAACH,GAAG,CAACoB,CAAC,CAAC,CAAC,sJACftC,MAAM,CAAC4B,YAAAA,AAAgB,EAAC;oBACtBC,SAAS,GAAGC,KAAK,wJACftB,QAAAA,AAAI,EACFW,OAAO,CAACmB,CAAC,CAAC,sJACVtC,MAAM,CAAC4B,YAAgB,AAAhBA,EAAiB;4BACtBC,SAAS,GAAGK,MAAM,GAAKlC,MAAM,CAACmC,yJAAS,AAATA,qJAAUtC,KAAK,CAACuC,KAAAA,AAAQ,EAACN,KAAK,EAAEI,MAAM,CAAC,CAAC;4BACtEG,SAAS,EAAEA,CAAA,uJAAMrC,MAAM,CAACmC,KAAAA,AAAS,EAACL,KAAK;yBACxC,CAAC,CACH;oBACHO,SAAS,EAAGE,EAAE,IAAK/B,6JAAAA,AAAI,EAACW,OAAO,CAACmB,CAAC,CAAC,sJAAEtC,KAAOwC,AAAE,CAAH,CAACA,AAAGD,EAAE,CAAC;iBAClD,CAAC;SAEP,CAAC,CACH;IACH,CAAC,CAAC,CAAC;AAGE,MAAMC,EAAE,GAAA,WAAA,GAAGlC,6JAAAA,AAAI,EAGpB,CAAC,EAAE,CAACmC,IAAI,EAAER,KAAK,yJAAKzB,OAAAA,AAAI,EAACiC,IAAI,uKAAE5B,IAAI,CAAC6B,CAAAA,AAAG,EAAC,IAAMT,KAAK,CAAC,CAAC,CAAC;AAGjD,MAAMU,MAAM,GAAaF,IAAsB,yJACpDjC,QAAAA,AAAI,EAACiC,IAAI,uKAAE5B,IAAI,CAAC6B,CAAAA,AAAG,kJAACjC,MAAM,CAACmC,AAAI,CAAC,CAAC;AAG5B,MAAMC,WAAW,IAAaJ,IAAsB,yJACzDjC,OAAI,AAAJA,EAAKiC,IAAI,EAAEK,QAAQ,iJAACrC,MAAM,CAACmC,AAAI,CAAC,CAAC;AAG5B,MAAMG,MAAM,IAAaN,IAAsB,yJAA0BjC,OAAAA,AAAI,EAACiC,IAAI,uKAAE5B,IAAI,CAAC6B,CAAAA,AAAG,oJAACrC,YAAS,CAAC,CAAC;AAGxG,MAAM2C,OAAO,IAAOC,QAAoB,GAC7CC,OAAO,CAAC,MAAK;QACX,IAAI;YACF,4KAAOrC,IAAI,CAACsC,KAAAA,AAAO,EAACF,QAAQ,EAAE,CAAC;QACjC,CAAC,CAAC,OAAOG,MAAM,EAAE;YACf,4KAAOvC,IAAI,CAACwC,EAAAA,AAAI,EAACD,MAAM,CAAC;QAC1B;IACF,CAAC,CAAC;AAEG,MAAME,IAAI,GAAA,WAAA,yJAAGhD,OAAAA,AAAI,EAUtB,CAAC,EAAE,CACHmC,IAAsB,EACtBc,GAAwB,EACxBC,CAA+B,wKAE/B3C,IAAI,CAAC4C,KAAAA,AAAO,EAAChB,IAAI,EAAGiB,CAAC,QACnB7C,IAAI,CAAC6B,kKAAG,AAAHA,EACHc,CAAC,CAACE,CAAC,CAAC,GACHpB,CAAC,GAAA,CAA6C;gBAAE,GAAGoB,CAAC;gBAAE,CAACH,GAAG,CAAA,EAAGjB;YAAC,CAAU,EAC1E,CAAC,CAAC;AAGA,MAAMqB,MAAM,GAAA,WAAA,yJAAGrD,OAAAA,AAAI,EAexB,CAAC,EACD,CAA4BmC,IAAsB,EAAEc,GAAM,wKACxD1C,IAAI,CAAC6B,CAAG,AAAHA,EAAID,IAAI,GAAGH,CAAC,GAAA,CAAM;YAAE,CAACiB,GAAG,CAAA,EAAGjB;QAAC,CAAmB,EAAC,CACxD;AAGM,MAAMsB,IAAI,GAAA,WAAA,yJAAGtD,OAAAA,AAAI,EAkBtB,CAAC,EAAE,CAA+BmC,IAAsB,EAAEc,GAAwB,EAAEC,CAAc,wKAClG3C,IAAI,CAAC6B,CAAAA,AAAG,EACND,IAAI,GACHiB,CAAC,GAAA,CAA6C;YAAE,GAAGA,CAAC;YAAE,CAACH,GAAG,CAAA,EAAGC,CAAC,CAACE,CAAC;QAAC,CAAU,EAC7E,CAAC;AAGG,MAAMG,SAAS,GAAA,WAAA,yJAAGvD,OAAAA,AAAI,EAU3B,CAAC,EAAE,CACHmC,IAAsB,EACtBqB,EAAoD,wKAEpDjD,IAAI,CAACkD,MAAAA,AAAQ,EACXtB,IAAI,EACHuB,CAAC,QAAsCvD,MAAM,CAACwD,qJAAAA,AAAS,EAACH,EAAE,CAACE,CAAC,CAAC,EAAE,yKAAMnD,IAAI,CAACwC,EAAAA,AAAI,EAACW,CAAC,CAAC,CAAC,CACpF,CAAC;AAGG,MAAME,QAAQ,GAAA,WAAA,yJAAG5D,OAAAA,AAAI,EAU1B,CAAC,EAAE,CAACmC,IAAI,EAAEiB,CAAC,EAAEF,CAAC,wKACd3C,IAAI,CAACkD,MAAAA,AAAQ,EAACtB,IAAI,GAAGuB,CAAC,IAAI;QACxB,IAAI,MAAM,IAAIA,CAAC,IAAIA,CAAC,CAAC,MAAM,CAAC,KAAKN,CAAC,EAAE;YAClC,OAAOF,CAAC,CAACQ,CAAQ,CAAC;QACpB;QACA,4KAAOnD,IAAI,CAACwC,EAAI,AAAJA,EAAKW,CAAQ,CAAC;IAC5B,CAAC,CAAC,CAAC;AAGE,MAAMG,SAAS,GAAA,WAAA,IA8ClB7D,4JAAAA,AAAI,EAAC,CAAC,EAAE,CAACmC,IAAI,EAAE2B,KAAK,uKACtBvD,IAAI,CAACkD,OAAAA,AAAQ,EAACtB,IAAI,GAAGuB,CAAM,IAAI;QAC7B,MAAMK,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,KAAK,CAAC;QAC/B,IAAI,MAAM,IAAIJ,CAAC,IAAIK,IAAI,CAACE,QAAQ,CAACP,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE;YAC3C,OAAOI,KAAK,CAACJ,CAAC,CAAC,MAAM,CAAC,CAAC,CAACA,CAAQ,CAAC;QACnC;QACA,4KAAOnD,IAAI,CAACwC,EAAAA,AAAI,EAACW,CAAQ,CAAC;IAC5B,CAAC,CAAC,CAAC;AAGE,MAAMQ,KAAK,IAAI9D,SAA2B,GAAoBwC,OAAO,CAAC,IAAMxC,SAAS,EAAE,GAAG+D,KAAK,oKAAG5D,IAAI,CAAC6D,GAAK,CAAC;AAG7G,MAAMC,OAAO,GAAA,WAAA,yJAAGrE,OAAAA,AAAI,EAGzB,CAAC,EAAE,CAACmC,IAAI,EAAEqB,EAAE,GACZc,UAAU,CACRnC,IAAI,GACHH,CAAC,uJAAK7B,MAAM,AAACiC,AAAG,CAAHA,CAAIoB,EAAE,CAACxB,CAAC,CAAC,EAAEzB,IAAI,CAACsC,sKAAO,CAAC,CACvC,CAAC;AAGG,MAAMyB,UAAU,GAAA,WAAA,yJAAGtE,OAAAA,AAAI,EAU5B,CAAC,EAAE,CAACmC,IAAI,EAAEqB,EAAE,OACZjD,IAAI,CAACgE,uKAAAA,AAAQ,EAACpC,IAAI,EAAE;QAClBZ,SAAS,mKAAEhB,IAAI,CAACwC,EAAI;QACpBhB,SAAS,GAAGC,CAAC,IAAI;YACf,MAAMwC,MAAM,GAAGhB,EAAE,CAACxB,CAAC,CAAC;YACpB,2JAAO7B,MAAM,CAACsE,EAAAA,AAAM,EAACD,MAAM,CAAC,GAAGA,MAAM,CAAC7C,KAAK,oKAAGpB,IAAI,CAAC6D,GAAK;QAC1D;KACD,CAAC,CAAC;AAGE,MAAMM,YAAY,IAAavC,IAAsB,GAC1DzC,MAAM,CAACiF,uJAAAA,AAAO,EAACpE,IAAI,CAACqE,yKAAAA,AAAM,EAACC,MAAM,CAAC1C,IAAI,CAAC,CAAC,CAAC;AAGpC,MAAM2C,IAAI,GAAGA,CAClB1E,SAA2B,EAC3B2E,KAAiB,EACjBC,MAAkB,KACD;IACjB,OAAOpC,OAAO,CACZ,IAAMxC,SAAS,EAAE,wKAAGG,IAAI,CAAC0E,EAAAA,AAAI,EAACD,MAAM,CAAC,wKAAGzE,IAAI,CAAC2E,MAAAA,AAAQ,EAACH,KAAK,CAAC,CAC7D;AACH,CAAC;AAGM,MAAMF,MAAM,IAAa1C,IAAsB,GACpDgD,KAAK,CAAChD,IAAI,EAAE;QAAEZ,SAAS,kJAAE5B,MAAM,CAACyF,AAAI;QAAErD,SAAS,EAAEpC,MAAM,CAAC0F,iJAAAA;IAAK,CAAE,CAAC;AAG3D,MAAMC,UAAU,IAAanD,IAAsB,wKACxD5B,IAAI,CAACgE,MAAAA,AAAQ,EAACpC,IAAI,EAAE;QAAEZ,SAAS,EAAEA,CAAA,GAAM+D,UAAU,CAACnD,IAAI,CAAC;QAAEJ,SAAS,mKAAExB,IAAI,CAACsC,KAAAA;IAAO,CAAE,CAAC;AAG9E,MAAM0C,KAAK,GAAA,WAAA,yJAAGvF,OAAAA,AAAI,EAMvB,CAAC,EACD,CACEwF,QAAqB,EACrBpF,SAA2C,yJAE3CF,OAAAA,AAAI,MACFK,IAAI,CAAC4C,sKAAAA,AAAO,uKAAC5C,IAAI,CAAC0E,EAAAA,AAAI,EAAC,IAAMO,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAAC,EAAE,CAAC,GAAGA,QAAQ,IAAI;QACtE,MAAMC,IAAI,GAA2B/C,OAAO,CAAC,MAAK;YAChD,MAAMgD,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;YAC5B,IAAIA,IAAI,CAACxE,IAAI,EAAE;gBACb,4KAAOb,IAAI,CAACsC,KAAAA,AAAO,EAAC,IAAI,CAAC;YAC3B;YACA,4JAAO3C,QAAAA,AAAI,EACTE,SAAS,CAACwF,IAAI,CAACjE,KAAK,CAAC,uKACrBpB,IAAI,CAAC4C,KAAAA,AAAO,GAAE0C,IAAI,GAAKA,IAAI,GAAGF,IAAI,OAAGpF,IAAI,CAACsC,sKAAO,AAAPA,EAAQgD,IAAI,CAAC,CAAC,CACzD;QACH,CAAC,CAAC;QACF,OAAOF,IAAI;IACb,CAAC,CAAC,CACH,CACJ;AAGM,MAAMG,MAAM,GAAA,WAAA,wJAAG9F,QAAAA,AAAI,EAMxB,CAAC,EACD,CAAUwF,QAAqB,EAAEpF,SAA2C,wKAC1EG,IAAI,CAAC4C,KAAAA,AAAO,uKAAC5C,IAAI,CAAC0E,EAAAA,AAAI,EAAC,IAAMO,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAAC,EAAE,CAAC,GAAGA,QAAQ,IAAI;QACtE,MAAMC,IAAI,GAA2B/C,OAAO,CAAC,MAAK;YAChD,MAAMgD,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;YAC5B,IAAIA,IAAI,CAACxE,IAAI,EAAE;gBACb,4KAAOb,IAAI,CAACsC,KAAAA,AAAO,EAAC,KAAK,CAAC;YAC5B;YACA,4KAAOtC,IAAI,CAAC4C,KAAO,AAAPA,EACV/C,SAAS,CAACwF,IAAI,CAACjE,KAAK,CAAC,GACpBkE,IAAI,GAAKA,IAAI,IAAGtF,IAAI,CAACsC,yKAAAA,AAAO,EAACgD,IAAI,CAAC,GAAGF,IAAI,CAC3C;QACH,CAAC,CAAC;QACF,OAAOA,IAAI;IACb,CAAC,CAAC,CACL;AAGM,MAAMI,OAAO,GAAA,WAAA,wKAA6BxF,IAAI,CAACyF,IAAAA,AAAM,EAAyB,CAACC,CAAC,EAAEF,OAAO,GAAKA,OAAO,CAAC;AAGtG,MAAMG,MAAM,GAAA,WAAA,yJAAGlG,OAAAA,AAAI,EAMxB,CAAC,EACD,CAAUwF,QAAqB,EAAEpF,SAA2C,GAC1E+F,KAAK,CAACC,IAAI,CAACZ,QAAQ,CAAC,CAACa,MAAM,CACzB,CAACC,GAAG,EAAEC,IAAI,yJACRrG,OAAI,AAAJA,EACEoG,GAAG,uKACH/F,IAAI,CAACiG,KAAAA,AAAO,EAACpG,SAAS,CAACmG,IAAI,CAAC,EAAE,CAACrE,EAAE,EAAEuE,CAAC,KAAI;YACtC,IAAIA,CAAC,EAAE;gBACLvE,EAAE,CAACwE,IAAI,CAACH,IAAI,CAAC;gBACb,OAAOrE,EAAE;YACX;YACA,OAAOA,EAAE;QACX,CAAC,CAAC,CACH,uKACH3B,IAAI,CAACsC,KAAAA,AAAO,EAAC,EAAE,CAA4B,CAC5C,CACJ;AAGM,MAAM8D,SAAS,GAAA,WAAA,yJAAG3G,OAAAA,AAAI,EAM3B,CAAC,EACD,CAAUwF,QAAqB,EAAEpF,SAA2C,GAC1E8F,MAAM,CAACV,QAAQ,GAAGxD,CAAC,GAAK4E,MAAM,CAACxG,SAAS,CAAC4B,CAAC,CAAC,CAAC,CAAC,CAChD;AAGM,MAAM6E,WAAW,GAAA,WAAA,wJAepB7G,QAAI,AAAJA,EACF,CAAC,EACD,CAAUmC,IAAsB,EAAE/B,SAAuB,EAAE0C,MAAwB,GACjFgE,YAAY,CAAC3E,IAAI,EAAE/B,SAAS,EAAE,IAAMG,IAAI,CAACwG,0KAAO,AAAPA,EAAQjE,MAAM,CAAC,CAAC,CAC5D;AAGM,MAAMkE,kBAAkB,GAAA,WAAA,yJAQ3BhH,OAAAA,AAAI,EACN,CAAC,EACD,CAAUmC,IAAsB,EAAE/B,SAAuB,EAAE6G,OAAe,GACxEH,YAAY,CAAC3E,IAAI,EAAE/B,SAAS,EAAE,yKAAMG,IAAI,CAAC2G,QAAAA,AAAU,EAACD,OAAO,CAAC,CAAC,CAChE;AAGM,MAAMH,YAAY,GAAA,WAAA,yJAmBrB9G,OAAAA,AAAI,EACN,CAAC,EACD,CACEmC,IAAsB,EACtB/B,SAAuB,EACvB+G,MAAoC,GAEpC5G,IAAI,CAAC4C,0KAAAA,AAAO,EAAChB,IAAI,GAAGH,CAAC,GAA6B5B,SAAS,CAAC4B,CAAC,CAAC,wKAAGzB,IAAI,CAACsC,KAAO,AAAPA,EAAQb,CAAC,CAAC,GAAGmF,MAAM,CAACnF,CAAC,CAAC,CAAC,CAChG;AAGM,MAAMoF,YAAY,GAAA,WAAA,IAerBpH,4JAAAA,AAAI,EACN,CAAC,EACD,CAAcmC,IAAsB,EAAE/B,SAAuB,EAAEiH,UAAwB,GACrFP,YAAY,CACV3E,IAAI,EACJ/B,SAAS,GACR4B,CAAC,wKAAKzB,IAAI,CAAC2E,MAAAA,AAAQ,EAAC,IAAMmC,UAAU,CAACrF,CAAC,CAAC,CAAC,CAC1C,CACJ;AAGM,MAAM2C,OAAO,IAAqBxC,IAAuC,uKAC9E5B,IAAI,CAAC4C,MAAAA,AAAO,EAAChB,IAAI,oJAAElC,WAAQ,CAAC;AAGvB,MAAMqH,IAAI,IAAanF,IAAsB,wKAClD5B,IAAI,CAACgE,MAAAA,AAAQ,EAACpC,IAAI,EAAE;QAAEZ,SAAS,mKAAEhB,IAAI,CAACsC,KAAO;QAAEd,SAAS,mKAAExB,IAAI,CAACwC,EAAAA;IAAI,CAAE,CAAC;AAGjE,MAAMwE,QAAQ,GAAA,WAAA,yJAAGvH,OAAAA,AAAI,EAU1B,CAAC,EAAE,CAACmC,IAAI,EAAEe,CAAC,GAAKoE,IAAI,CAACpE,CAAC,CAACoE,IAAI,CAACnF,IAAI,CAAC,CAAC,CAAC,CAAC;AAG/B,MAAMgD,KAAK,GAAA,WAAA,IAAGnF,4JAAAA,AAAI,EASvB,CAAC,EAAE,CAACmC,IAAI,EAAE,EAAEZ,SAAS,EAAEQ,SAAAA,EAAW,wKAClCxB,IAAI,CAACgE,MAAAA,AAAQ,EAACpC,IAAI,EAAE;QAClBZ,SAAS,EAAGmC,CAAC,IAAKnD,IAAI,CAACsC,0KAAO,AAAPA,EAAQtB,SAAS,CAACmC,CAAC,CAAC,CAAC;QAC5C3B,SAAS,GAAGC,CAAC,wKAAKzB,IAAI,CAACsC,KAAAA,AAAO,EAACd,SAAS,CAACC,CAAC,CAAC;KAC5C,CAAC,CAAC;AAGE,MAAMwF,OAAO,GAAA,WAAA,IAAGxH,4JAAAA,AAAI,GAkBxByH,IAAI,yJAAKrH,SAAS,CAACsH,IAAAA,AAAU,EAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EACvC,CAAcjC,QAAqB,EAAEtC,CAA8B,EAAEyE,OAEpE,KAAwB;IACvB,IAAIA,OAAO,EAAEC,OAAO,EAAE;QACpB,6JAAO1H,OAAAA,AAAI,uKACTK,IAAI,CAAC0E,EAAAA,AAAI,EAAC,IAAMO,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAAC,EAAE,CAAC,uKAC5CnF,IAAI,CAAC4C,KAAO,AAAPA,GAASuC,QAAQ,IAAI;YACxB,MAAMC,IAAI,GAAwB/C,OAAO,CAAC,MAAK;gBAC7C,MAAMgD,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;gBAC5B,IAAIA,IAAI,CAACxE,IAAI,EAAE;oBACb,OAAO+C,KAAK;gBACd;gBACA,WAAOjE,yJAAAA,AAAI,EAACgD,CAAC,CAAC0C,IAAI,CAACjE,KAAK,CAAC,uKAAEpB,IAAI,CAAC4C,KAAAA,AAAO,EAAC,IAAMwC,IAAI,CAAC,CAAC;YACtD,CAAC,CAAC;YACF,OAAOA,IAAI;QACb,CAAC,CAAC,CACH;IACH;IAEA,OAAO/C,OAAO,CAAC,KACbtD,EAAE,CAACuI,8JAAAA,AAAY,EAACrC,QAAQ,CAAC,CAACa,MAAM,CAC9B,CAACC,GAAG,EAAEC,IAAI,wKACRhG,IAAI,CAACiG,KAAAA,AAAO,EAACF,GAAG,EAAEpD,CAAC,CAACqD,IAAI,CAAC,EAAE,CAACuB,KAAK,EAAEC,IAAI,KAAI;gBACzCD,KAAK,CAACpB,IAAI,CAACqB,IAAI,CAAC;gBAChB,OAAOD,KAAK;YACd,CAAC,CAAC,uKACJvH,IAAI,CAACsC,KAAAA,AAAO,EAAC,EAAE,CAA6B,CAC7C,CACF;AACH,CAAC,CACF;AAGM,MAAMmF,UAAU,IAAUnD,MAA2B,IAAmB;IAC7E,OAAQA,MAAM,CAACoD,IAAI;QACjB,KAAK,MAAM;YAAE;gBACX,QAAO1H,IAAI,CAACwC,sKAAAA,AAAI,EAAC8B,MAAM,CAACO,IAAI,CAAC;YAC/B;QACA,KAAK,OAAO;YAAE;gBACZ,4KAAO7E,IAAI,CAACsC,KAAAA,AAAO,EAACgC,MAAM,CAACQ,KAAK,CAAC;YACnC;IACF;AACF,CAAC;AAGM,MAAM6C,UAAU,IAAO1D,MAAwB,uJACpDrE,MAAM,CAACgF,CAAAA,AAAK,EAACX,MAAM,EAAE;QACnB2D,MAAM,EAAEA,CAAA,wKAAM5H,IAAI,CAACwC,EAAI,AAAJA,sJAAK5C,MAAM,CAAK,AAAJiI,EAAM,CAAC;QACtCC,MAAM,mKAAE9H,IAAI,CAACsC,KAAAA;KACd,CAAC;AAMG,MAAMyF,GAAG,GAAmBA,CAAC,GAAGb,IAAI,GACzC7E,OAAO,CAAC,MAAK;QACX,MAAMM,CAAC,GAAIuE,IAAI,CAACc,MAAM,KAAK,CAAC,GACxBd,IAAI,CAAC,CAAC,CAAC,GACPA,IAAI,CAAC,CAAC,CAAC,CAACzE,IAAI,CAACyE,IAAI,CAAC,CAAC,CAAC,CAAC;QACzB,MAAM/B,QAAQ,GAAGxC,CAAC,mJAAChD,OAAI,CAAC;QACxB,MAAMc,KAAK,GAAG0E,QAAQ,CAACE,IAAI,EAAE;QAC7B,MAAM4C,GAAG,IACPxH,KAA2D,GAE3DA,KAAK,CAACI,IAAI,wKACRb,IAAI,CAACsC,KAAAA,AAAO,EAAC7B,KAAK,CAACW,KAAK,CAAC,wKACzBpB,IAAI,CAAC4C,KAAAA,AAAO,qJAAC9C,eAAAA,AAAY,EAACW,KAAK,CAACW,KAAK,CAAQ,GAAG8G,GAAQ,GAAKD,GAAG,CAAC9C,QAAQ,CAACE,IAAI,CAAC6C,GAAY,CAAC,CAAC,CAAC;QAClG,OAAOD,GAAG,CAACxH,KAAK,CAAC;IACnB,CAAC,CAAC;AAGG,MAAM0H,IAAI,GAAavG,IAAgC,IAC5DjC,6JAAAA,AAAI,EACFiC,IAAI,uKACJ5B,IAAI,CAACgE,MAAAA,AAAQ,EAAC;QACZhD,SAAS,GAAGmC,CAAC,wKAAKnD,IAAI,CAACwC,EAAAA,AAAI,sJAAC5C,MAAM,CAACmC,AAAI,EAACoB,CAAC,CAAC,CAAC;QAC3C3B,SAAS,GAAGC,CAAC,IAAI;YACf,MAAM2G,CAAC,GAAG3G,CAAC,CAACyD,MAAM,CAACC,QAAQ,CAAC,EAAE;YAC9B,MAAMkD,GAAG,GAAGD,CAAC,CAAC/C,IAAI,EAAE;YACpB,IAAIgD,GAAG,CAACxH,IAAI,EAAE;gBACZ,OAAOb,IAAI,CAACwC,uKAAAA,AAAI,sJAAC5C,MAAM,CAACiI,AAAI,AAAJA,EAAM,CAAC;YACjC,CAAC,MAAM;gBACL,2KAAO7H,IAAI,CAACsC,MAAAA,AAAO,EAAC+F,GAAG,CAACjH,KAAK,CAAC;YAChC;QACF;KACD,CAAC,CACH;AAGI,MAAMkH,GAAG,GAAA,WAAA,yJAAG7I,OAAAA,AAAI,GA0BpByH,IAAI,GAAK,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,wKAAIlH,IAAI,CAACuI,IAAK,AAALA,EAAMrB,IAAI,CAAC,CAAC,CAAC,CAAC,EAC7D,CACEtF,IAAsC,EACtC,EAAE4G,OAAO,EAAEC,MAAAA,EAGV,KACC;IACF,IAAI,OAAO7G,IAAI,KAAK,SAAS,EAAE;QAC7B,OAAOA,IAAI,GAAG6G,MAAM,GAAGD,OAAO;IAChC;IAEA,OAAOxI,IAAI,CAAC4C,0KAAAA,AAAO,EAAChB,IAAI,GAAG0D,IAAI,GAAgDA,IAAI,GAAGmD,MAAM,GAAGD,OAAO,CAAC;AACzG,CAAC,CACF;AAGM,MAAME,MAAM,IAAa9G,IAAsB,GACpDgD,KAAK,CAAChD,IAAI,EAAE;QAAEZ,SAAS,EAAEA,CAAA,GAAM4C,KAAK;QAAEpC,SAAS,EAAEA,CAAA,GAAMoC;IAAK,CAAE,CAAC;AAG1D,MAAM+E,SAAS,IAAa/G,IAAsB,GACvDgD,KAAK,CAAChD,IAAI,EAAE;QAAEZ,SAAS,oJAAEzB,YAAS;QAAEiC,SAAS,oJAAElC,aAAAA;IAAU,CAAE,CAAC;AAGvD,MAAM6B,SAAS,IAAaS,IAAsB,GACvDgD,KAAK,CAAChD,IAAI,EAAE;QAAEZ,SAAS,oJAAE1B,aAAU;QAAEkC,SAAS,EAAEjC,8JAAAA;IAAS,CAAE,CAAC;AAGvD,MAAMqJ,OAAO,GAAGA,CACrBC,OAAU,EACVzB,OAGC,GACoB0B,WAAW,CAACD,OAAO,EAAEzB,OAAO,CAAC2B,KAAK,EAAE3B,OAAO,CAAC4B,IAAI,CAAC;AAExE,MAAMF,WAAW,GAAGA,CAClBD,OAAU,EACVI,IAAuB,EACvBD,IAAgC,KACZ;IACpB,IAAIC,IAAI,CAACJ,OAAO,CAAC,EAAE;QACjB,OAAOlJ,6JAAAA,AAAI,EACTqJ,IAAI,CAACH,OAAO,CAAC,uKACb7I,IAAI,CAAC4C,KAAAA,AAAO,GAAEsG,CAAC,GAAKJ,WAAW,CAACI,CAAC,EAAED,IAAI,EAAED,IAAI,CAAC,CAAC,CAChD;IACH;IACA,QAAOhJ,IAAI,CAACsC,yKAAAA,AAAO,EAACuG,OAAO,CAAC;AAC9B,CAAC;AAGM,MAAMzD,IAAI,GAmBbA,CACFyD,OAAU,EACVzB,OAKC,GAEDA,OAAO,CAACC,OAAO,GACb8B,eAAe,CAACN,OAAO,EAAEzB,OAAO,CAAC2B,KAAK,EAAE3B,OAAO,CAACgC,IAAI,EAAEhC,OAAO,CAAC4B,IAAI,CAAC,uKACnEhJ,IAAI,CAAC6B,EAAAA,AAAG,EAACwH,QAAQ,CAACR,OAAO,EAAEzB,OAAO,CAAC2B,KAAK,EAAE3B,OAAO,CAACgC,IAAI,EAAEhC,OAAO,CAAC4B,IAAI,CAAC,GAAGvH,CAAC,GAAKmE,KAAK,CAACC,IAAI,CAACpE,CAAC,CAAC,CAAC;AAEhG,MAAM4H,QAAQ,GAAGA,CACfR,OAAU,EACVI,IAAuB,EACvBK,GAAgB,EAChBN,IAAgC,KACC;IACjC,IAAIC,IAAI,CAACJ,OAAO,CAAC,EAAE;QACjB,6JAAOlJ,OAAAA,AAAI,EACTqJ,IAAI,CAACH,OAAO,CAAC,uKACb7I,IAAI,CAAC4C,KAAAA,AAAO,GAAEnB,CAAC,IAAK9B,4JAAAA,AAAI,EAAC0J,QAAQ,CAACC,GAAG,CAACT,OAAO,CAAC,EAAEI,IAAI,EAAEK,GAAG,EAAEN,IAAI,CAAC,uKAAEhJ,IAAI,CAAC6B,CAAAA,AAAG,qJAAC5C,KAAK,CAACsK,GAAAA,AAAM,EAAC9H,CAAC,CAAC,CAAC,CAAC,CAAC,CAC9F;IACH;IACA,2KAAOzB,IAAI,CAACsC,MAAO,AAAPA,qJAAQrD,KAAK,CAACuK,EAAAA,AAAK,EAAK,CAAC;AACvC,CAAC;AAED,MAAML,eAAe,GAAGA,CACtBN,OAAU,EACVI,IAAuB,EACvBK,GAAgB,EAChBN,IAAgC,KACT;IACvB,IAAIC,IAAI,CAACJ,OAAO,CAAC,EAAE;QACjB,WAAOlJ,yJAAAA,AAAI,EACTqJ,IAAI,CAACH,OAAO,CAAC,uKACb7I,IAAI,CAAC4C,KAAAA,AAAO,EAAC,IAAMuG,eAAe,CAACG,GAAG,CAACT,OAAO,CAAC,EAAEI,IAAI,EAAEK,GAAG,EAAEN,IAAI,CAAC,CAAC,CACnE;IACH;IACA,OAAOpF,KAAK;AACd,CAAC;AAGM,MAAM6F,UAAU,GAAA,WAAA,wJAAGhK,QAAAA,AAAI,EAG5B,CAAC,EAAE,CAAamC,IAAsB,EAAEe,CAAc,wKACtD3C,IAAI,CAACgE,MAAAA,AAAQ,EAACpC,IAAI,EAAE;QAClBZ,SAAS,GAAGmC,CAAC,wKAAKnD,IAAI,CAACwC,EAAAA,AAAI,EAACW,CAAC,CAAC;QAC9B3B,SAAS,GAAGC,CAAC,GAAKU,OAAO,CAAC,IAAMQ,CAAC,CAAClB,CAAC,CAAC;KACrC,CAAC,CAAC;AAGE,MAAMiI,OAAO,GAAA,WAAA,yJAAGjK,OAAAA,AAAI,EASzB,CAAC,EAAE,CAACmC,IAAI,EAAE,EAAEZ,SAAS,EAAEQ,SAAAA,EAAW,OAClCxB,IAAI,CAACgE,uKAAAA,AAAQ,EAACpC,IAAI,EAAE;QAClBZ,SAAS,GAAGmC,CAAC,wKAAKnD,IAAI,CAACwC,EAAAA,AAAI,EAACxB,SAAS,CAACmC,CAAC,CAAC,CAAC;QACzC3B,SAAS,GAAGC,CAAC,GAAKzB,IAAI,CAACsC,0KAAAA,AAAO,EAACd,SAAS,CAACC,CAAC,CAAC;KAC5C,CAAC,CAAC;AAGE,MAAMQ,QAAQ,GAAA,WAAA,yJAAGxC,OAAAA,AAAI,EAG1B,CAAC,EAAE,CAACmC,IAAI,EAAEe,CAAC,GACX3C,IAAI,CAACgE,2KAAAA,AAAQ,EAACpC,IAAI,EAAE;QAClBZ,SAAS,GAAGmC,CAAC,wKAAKnD,IAAI,CAACwC,EAAAA,AAAI,EAACG,CAAC,CAACQ,CAAC,CAAC,CAAC;QACjC3B,SAAS,mKAAExB,IAAI,CAACsC,KAAAA;KACjB,CAAC,CAAC;AAGE,MAAMqH,KAAK,IAAa/H,IAAsB,OACnD5B,IAAI,CAACgE,uKAAAA,AAAQ,EAACpC,IAAI,EAAE;QAAEZ,SAAS,GAAGmC,CAAC,wKAAKnD,IAAI,CAACsC,KAAAA,AAAO,EAACa,CAAC,CAAC;QAAE3B,SAAS,mKAAExB,IAAI,CAACsC,KAAAA;IAAO,CAAE,CAAC;AAG9E,MAAMsH,QAAQ,GAAA,WAAA,yJAAGnK,OAAAA,AAAI,EAI1B,CAAC,EACD,CAAcwF,QAAoC,EAAE4E,IAAQ,EAAElH,CAAuB,GACnFN,OAAO,CAAC,IACNuD,KAAK,CAACC,IAAI,CAACZ,QAAQ,CAAC,CAACa,MAAM,CACzB,CAACC,GAAG,EAAEC,IAAI,GAAKrG,6JAAAA,AAAI,EAACoG,GAAG,uKAAE/F,IAAI,CAACiG,KAAAA,AAAO,EAACD,IAAI,EAAErD,CAAC,CAAC,CAAC,uKAC/C3C,IAAI,CAACsC,KAAAA,AAAO,EAACuH,IAAI,CAAsB,CACxC,CACF,CACJ;AAGM,MAAMxD,MAAM,IAAUzE,IAA4B,GAA6BjC,6JAAAA,AAAI,EAACiC,IAAI,uKAAE5B,IAAI,CAAC6B,CAAG,AAAHA,GAAKiI,CAAC,GAAK,CAACA,CAAC,CAAC,CAAC;AAG9G,MAAMjC,IAAI,IAAajG,IAAqC,GACjE5B,IAAI,CAACgE,2KAAAA,AAAQ,EAACpC,IAAI,EAAE;QAClBZ,SAAS,GAAGmC,CAAC,IAAKnD,IAAI,CAACwC,sKAAAA,AAAI,sJAAC5C,MAAM,CAACmC,AAAI,EAACoB,CAAC,CAAC,CAAC;QAC3C3B,SAAS,sJAAE5B,MAAM,CAACgF,CAAAA,AAAK,EAAC;YACtBgD,MAAM,EAAEA,CAAA,GAAMhE,KAAK;YACnBkE,MAAM,EAAEA,CAAA,GAAM9H,IAAI,CAACwC,uKAAAA,AAAI,GAAC5C,MAAM,CAACiI,mJAAAA,AAAI,EAAE;SACtC;KACF,CAAC;AAGG,MAAM5D,MAAM,IAAarC,IAAsB,GACpDgD,KAAK,CAAChD,IAAI,EAAE;QAAEZ,SAAS,EAAEA,CAAA,uJAAMpB,MAAM,CAACiI,AAAI,EAAE;QAAErG,SAAS,kJAAE5B,MAAM,CAACmC;IAAI,CAAE,CAAC;AAGlE,MAAMgI,KAAK,IAAanI,IAAsB,yJAA2BjC,OAAAA,AAAI,EAACiC,IAAI,EAAEoI,SAAS,mJAACtK,WAAQ,CAAC,CAAC;AAGxG,MAAMsK,SAAS,GAAA,WAAA,GAAGvK,6JAAAA,AAAI,EAG3B,CAAC,EAAE,CAACmC,IAAI,EAAEe,CAAC,yJAAKhD,OAAAA,AAAI,EAACiC,IAAI,EAAEK,QAAQ,CAACU,CAAC,CAAC,uKAAE3C,IAAI,CAACkD,MAAAA,AAAQ,mKAAClD,IAAI,CAACiK,CAAG,CAAC,CAAC,CAAC;AAG5D,MAAMrD,MAAM,GAAA,WAAA,yJAAGnH,OAAI,AAAJA,EAIpB,CAAC,EACD,CAAsBmC,IAAsB,EAAEsI,IAAkC,GAC9ElK,IAAI,CAAC4C,0KAAAA,AAAO,uKAAC5C,IAAI,CAACyF,IAAM,AAANA,GAA0B0E,OAAO,GAAKlK,OAAO,CAACmK,sLAAAA,AAAmB,EAACD,OAAO,CAAC,CAAC,GAAGE,KAAK,yJACnG1K,OAAAA,AAAI,uKACFK,IAAI,CAACsK,GAAAA,AAAK,EAAC1I,IAAI,EAAE,IAAM5B,IAAI,CAAC4C,0KAAO,AAAPA,uKAAQ5C,IAAI,CAAC0E,EAAAA,AAAI,EAAC2F,KAAK,CAAC,EAAEH,IAAI,CAAC,CAAC,uKAC5DlK,IAAI,CAACkD,MAAQ,AAARA,EAAS,QAAMlD,IAAI,CAAC4C,sKAAAA,AAAO,uKAAC5C,IAAI,CAAC0E,EAAAA,AAAI,EAAC2F,KAAK,CAAC,EAAEH,IAAI,CAAC,CAAC,CAC1D,CAAC,CACP;AAGM,MAAMK,YAAY,GAAA,WAAA,yJAAG9K,OAAAA,AAAI,EAW9B,CAAC,EACD,CACEmC,IAAsB,EACtBsI,IAAkC,GAElCtD,MAAM,sKAAC5G,IAAI,CAAC6B,CAAAA,AAAG,EAACD,IAAI,kJAAExC,MAAM,CAAK,AAAJyF,CAAK,EAAE,yKAAM7E,IAAI,CAAC6B,CAAAA,AAAG,EAACqI,IAAI,EAAE,kJAAE9K,MAAM,CAAC0F,CAAK,CAAC,CAAC,CAC5E;AAGM,MAAM0F,UAAU,GAAA,WAAA,GAAG/K,6JAAAA,AAAI,EAI5B,CAAC,EACD,CAAcmC,IAAsB,EAAE4C,KAAkB,GACtDoC,MAAM,CAAChF,IAAI,EAAE,yKAAM5B,IAAI,CAAC2E,MAAAA,AAAQ,EAACH,KAAK,CAAC,CAAC,CAC3C;AAGM,MAAMiG,cAAc,GAAA,WAAA,yJAAGhL,OAAAA,AAAI,EAWhC,CAAC,EACD,CACEmC,IAAqC,EACrCsI,IAAiD,OAEjDlK,IAAI,CAACkD,uKAAAA,AAAQ,EACXtB,IAAI,sJACJhC,MAAM,CAACgF,CAAAA,AAAK,EAAC;QACXgD,MAAM,EAAEsC,IAAI;QACZpC,MAAM,GAAG3E,CAAC,wKAAKnD,IAAI,CAACwC,EAAAA,AAAI,EAAC5C,MAAM,CAACmC,oJAAAA,AAAI,EAASoB,CAAC,CAAC;KAChD,CAAC,CACH,CACJ;AAGM,MAAMuH,aAAa,GAAA,WAAA,yJAAGjL,OAAAA,AAAI,EAI/B,CAAC,EACD,CAAcmC,IAAsB,EAAER,KAAkB,GACtDwF,MAAM,CAAChF,IAAI,EAAE,yKAAM5B,IAAI,CAAC0E,EAAI,AAAJA,EAAKtD,KAAK,CAAC,CAAC,CACvC;AAGM,MAAMuJ,cAAc,GAAA,WAAA,yJAAGlL,OAAAA,AAAI,EAGhC,CAAC,EAAE,CAACmC,IAAI,EAAEgJ,GAAG,wKAAK5K,IAAI,CAAC6K,aAAAA,AAAe,EAACjJ,IAAI,GAAG8D,CAAyB,GAAKkF,GAAG,CAAC,CAAC;AAG5E,MAAME,kBAAkB,GAAA,WAAA,yJAAGrL,OAAAA,AAAI,EAGpC,CAAC,EAAE,CACHmC,IAAuB,EACvBmJ,OAA2B,GAE3B/K,IAAI,CAAC6K,kLAAAA,AAAe,EAClBjJ,IAAI,GACHoJ,MAAuC,wJAA0B9L,OAAO,CAACyK,AAAK,EAACqB,MAAM,EAAED,OAAO,CAAQ,CACxG,CAAC;AAGG,MAAME,cAAc,GAAA,WAAA,OAAGxL,yJAAAA,AAAI,EAYhC,CAAC,EAAE,CAACmC,IAAI,EAAEc,GAAG,EAAEwI,QAAQ,GAAKC,iBAAiB,CAACvJ,IAAI,EAAEc,GAAG,GAAE1C,IAAI,CAACsC,yKAAAA,AAAO,EAAC4I,QAAQ,CAAC,CAAC,CAAC;AAG5E,MAAMC,iBAAiB,GAAA,WAAA,yJAAG1L,OAAI,AAAJA,EAY/B,CAAC,EAAE,CACHmC,IAAsB,EACtBc,GAAsB,EACtB0I,GAAsC,wKAEtCpL,IAAI,CAACqL,YAAAA,AAAc,GAAET,GAAwC,wKAC3D5K,IAAI,CAAC4C,KAAAA,AAAO,EACVwI,GAAG,GACFE,OAAO,GACNX,cAAc,CACZ/I,IAAI,uJACJ1C,MAAQqM,AAAG,CAAJ,CAACA,AAAIX,GAAG,EAAElI,GAAG,EAAE4I,OAAO,CAA4B,CAC1D,CACJ,CACF,CAAC;AAGG,MAAMxF,MAAM,GAAA,WAAA,GAAGrG,6JAAI,AAAJA,EAIpB,CAAC,EACD,CAAawF,QAAqB,EAAE4E,IAAO,EAAElH,CAAmC,GAC9EN,OAAO,CAAC,IACNuD,KAAK,CAACC,IAAI,CAACZ,QAAQ,CAAC,CAACa,MAAM,CACzB,CAACC,GAAG,EAAEC,IAAI,yJAAKrG,OAAAA,AAAI,EAACoG,GAAG,MAAE/F,IAAI,CAAC4C,sKAAAA,AAAO,GAAE4I,CAAC,GAAK7I,CAAC,CAAC6I,CAAC,EAAExF,IAAI,CAAC,CAAC,CAAC,uKACzDhG,IAAI,CAACsC,KAAAA,AAAO,EAACuH,IAAI,CAAqB,CACvC,CACF,CACJ;AAGM,MAAM4B,SAAS,GAAA,WAAA,yJAAGhM,OAAAA,AAAI,EAY3B,CAAC,EAAE,CACHwF,QAAoC,EACpC4D,OAA2B,EAC3BlG,CAAoB,GAEpBN,OAAO,CAAC,IACNuD,KAAK,CAACC,IAAI,CAACZ,QAAQ,CAAC,CAACa,MAAM,CACzB,CAACC,GAAG,EAAEC,IAAI,yJAAKrG,OAAAA,AAAI,EAACoG,GAAG,MAAE/F,IAAI,CAACiG,sKAAAA,AAAO,EAACD,IAAI,EAAErD,CAAC,CAAC,CAAC,EAC/CkG,OAAqC,CACtC,CACF,CAAC;AAGG,MAAM6C,WAAW,GAAA,WAAA,yJAAGjM,OAAAA,AAAI,EAI7B,CAAC,EACD,CAAawF,QAAqB,EAAE4E,IAAO,EAAElH,CAAmC,GAC9EN,OAAO,CAAC,IACNuD,KAAK,CAACC,IAAI,CAACZ,QAAQ,CAAC,CAACyG,WAAW,CAC9B,CAAC3F,GAAG,EAAEC,IAAI,yJAAKrG,OAAAA,AAAI,EAACoG,GAAG,uKAAE/F,IAAI,CAAC4C,KAAAA,AAAO,GAAE4I,CAAC,GAAK7I,CAAC,CAAC6I,CAAC,EAAExF,IAAI,CAAC,CAAC,CAAC,uKACzDhG,IAAI,CAACsC,KAAAA,AAAO,EAACuH,IAAI,CAAqB,CACvC,CACF,CACJ;AAGM,MAAM8B,WAAW,GAAA,WAAA,GAAGlM,6JAAAA,AAAI,EAG7B,CAAC,EAAE,CAACmC,IAAI,EAAEqB,EAAE,GAAK2I,eAAe,CAAChK,IAAI,EAAEqB,EAAE,oJAAEvD,WAAQ,CAAC,CAAC;AAGhD,MAAMkM,eAAe,GAAA,WAAA,yJAAGnM,OAAI,AAAJA,EAY7B,CAAC,EAAE,CAACmC,IAAI,EAAEqB,EAAE,EAAEN,CAAC,IACf3C,IAAI,CAACkD,0KAAAA,AAAQ,EACXtB,IAAI,GACHuB,CAAC,uJACAvD,MAAM,CAACgF,CAAAA,AAAK,EAAC3B,EAAE,CAACE,CAAC,CAAC,EAAE;YAClByE,MAAM,EAAEA,CAAA,wKAAM5H,IAAI,CAACiK,CAAG,AAAHA,EAAItH,CAAC,CAACQ,CAAC,CAAC,CAAC;YAC5B2E,MAAM,kKAAE9H,IAAI,CAACwC,GAAAA;SACd,CAAC,CACL,CAAC;AAGG,MAAMqJ,MAAM,GAAA,WAAA,yJAAGpM,OAAAA,AAAI,EAGxB,CAAC,EAAE,CAACmC,IAAI,EAAEqB,EAAE,GACZ6I,SAAS,CACPlK,IAAI,GACHH,CAAC,OAAK7B,MAAM,CAACiC,+IAAAA,AAAG,EAACoB,EAAE,CAACxB,CAAC,CAAC,mKAAEzB,IAAI,CAACwC,EAAI,CAAC,CACpC,CAAC;AAGG,MAAMsJ,SAAS,GAAA,WAAA,yJAAGrM,OAAAA,AAAI,EAU3B,CAAC,EAAE,CAACmC,IAAI,EAAEqB,EAAE,uKACZjD,IAAI,CAAC4C,MAAAA,AAAO,EAAChB,IAAI,GAAGH,CAAC,uJACnB7B,MAAM,CAACgF,CAAAA,AAAK,EAAC3B,EAAE,CAACxB,CAAC,CAAC,EAAE;YAClBmG,MAAM,EAAEA,CAAA,wKAAM5H,IAAI,CAACsC,KAAO,AAAPA,EAAQb,CAAC,CAAC;YAC7BqG,MAAM,MAAE9H,IAAI,CAAC4C,sKAAAA,AAAO,mKAAC5C,IAAI,CAACwC,EAAI;SAC/B,CAAC,CAAC,CAAC;AAGD,MAAMuJ,WAAW,GAAA,WAAA,yJAAGtM,OAAAA,AAAI,EAG7B,CAAC,EAAE,CAACmC,IAAI,EAAE/B,SAAS,GAAKmM,eAAe,CAACpK,IAAI,EAAE/B,SAAS,CAAC,CAAC;AAE3D,MAAMmM,eAAe,GAAGA,CAAUpK,IAAsB,EAAE/B,SAAuB,wKAC/EG,IAAI,CAAC4C,KAAO,AAAPA,EAAQhB,IAAI,GAAGH,CAAC,GACnB5B,SAAS,CAAC4B,CAAC,CAAC,wKACVzB,IAAI,CAACsC,KAAAA,AAAO,EAACb,CAAC,CAAC,GACfuK,eAAe,CAACpK,IAAI,EAAE/B,SAAS,CAAC,CAAC;AAGhC,MAAMoM,WAAW,GAAA,WAAA,yJAAGxM,OAAI,AAAJA,EAGzB,CAAC,EAAE,CAACmC,IAAI,EAAE/B,SAAS,GAAKqM,eAAe,CAACtK,IAAI,EAAE/B,SAAS,CAAC,CAAC;AAE3D,MAAMqM,eAAe,GAAGA,CAAUtK,IAAsB,EAAE/B,SAAuB,GAC/EF,6JAAAA,AAAI,uKACFK,IAAI,CAAC4C,KAAAA,AAAO,EAAChB,IAAI,GAAGH,CAAC,GACnB5B,SAAS,CAAC4B,CAAC,CAAC,GACVyK,eAAe,CAACtK,IAAI,EAAE/B,SAAS,CAAC,wKAChCG,IAAI,CAACsC,KAAAA,AAAO,EAACb,CAAC,CAAC,CAAC,CACrB;AAGI,MAAM0K,SAAS,GAAA,WAAA,yJAAG1M,OAAAA,AAAI,EAG3B,CAAC,EAAE,CAACmC,IAAI,EAAEwK,CAAC,GAAKxG,KAAK,CAACC,IAAI,CAAC;QAAEmC,MAAM,EAAEoE;IAAC,CAAE,EAAE,IAAMxK,IAAI,CAAC,CAAC;AAGjD,MAAMyK,YAAY,GAAA,WAAA,yJAAG5M,OAAAA,AAAI,EAG9B,CAAC,EAAE,CAACmC,IAAI,EAAEwK,CAAC,GAAKE,GAAG,CAACH,SAAS,CAACvK,IAAI,EAAEwK,CAAC,CAAC,CAAC,CAAC;AAGnC,MAAMG,mBAAmB,GAAA,WAAA,IAAG9M,4JAAAA,AAAI,EAGrC,CAAC,EAAE,CAACmC,IAAI,EAAEwK,CAAC,GAAKE,GAAG,CAACH,SAAS,CAACvK,IAAI,EAAEwK,CAAC,CAAC,EAAE;QAAE/E,OAAO,EAAE;IAAI,CAAE,CAAC,CAAC;AAGtD,MAAMmF,UAAU,GAAA,WAAA,OAAG/M,yJAAAA,AAAI,EAU5B,CAAC,EACD,CAAUmC,IAAsB,EAAE/B,SAAuB,wKACvDG,IAAI,CAACgE,MAAAA,AAAQ,EAACpC,IAAI,EAAE;QAAEZ,SAAS,mKAAEhB,IAAI,CAACwC,EAAI;QAAEhB,SAAS,EAAGC,CAAC,IAAK5B,SAAS,CAAC4B,CAAC,CAAC,wKAAGzB,IAAI,CAACsC,KAAAA,AAAO,EAACb,CAAC,CAAC,oKAAGzB,IAAI,CAAC6D,GAAAA;IAAK,CAAE,CAAC,CAC/G;AAGM,MAAM4I,UAAU,GAAA,WAAA,GAAGhN,6JAAAA,AAAI,EAI5B,CAAC,EACD,CAACmC,IAAI,EAAE/B,SAAS,wKACdG,IAAI,CAACgE,MAAAA,AAAQ,EAACpC,IAAI,EAAE;QAAEZ,SAAS,EAAEhB,IAAI,CAACwC,mKAAI;QAAEhB,SAAS,EAAGC,CAAC,IAAK,CAAC5B,SAAS,CAAC4B,CAAC,CAAC,wKAAGzB,IAAI,CAACsC,KAAAA,AAAO,EAACb,CAAC,CAAC,oKAAGzB,IAAI,CAAC6D,GAAAA;IAAK,CAAE,CAAC,CAChH;AAGM,MAAM6I,SAAS,GAAA,WAAA,yJAAGjN,OAAAA,AAAI,EAU3B,CAAC,EAAE,CAACkN,QAAQ,EAAEhK,CAAC,yJACfhD,OAAAA,AAAI,EACFsH,OAAO,CAAC0F,QAAQ,GAAGlL,CAAC,GAAK6C,MAAM,CAAC3B,CAAC,CAAClB,CAAC,CAAC,CAAC,CAAC,uKACtCzB,IAAI,CAAC6B,CAAAA,AAAG,GAAEF,EAAE,iKAAK5B,UAAU,CAAC6M,IAAY,AAAZA,EAAajL,EAAE,oJAAEjC,WAAQ,CAAC,CAAC,CACxD,CAAC;AAGG,MAAMqC,IAAI,GAAaH,IAAqC,KACjE5B,IAAI,CAACgE,0KAAQ,AAARA,EAASpC,IAAI,EAAE;QAClBZ,SAAS,GAAGmC,CAAC,wKAAKnD,IAAI,CAACwC,EAAAA,AAAI,sJAAC5C,MAAM,CAACmC,AAAI,EAACoB,CAAC,CAAC,CAAC;QAC3C3B,SAAS,qJAAE5B,MAAM,CAACgF,EAAAA,AAAK,EAAC;YACtBgD,MAAM,EAAEA,CAAA,wKAAM5H,IAAI,CAACwC,EAAAA,AAAI,sJAAC5C,MAAM,CAAK,AAAJiI,EAAM,CAAC;YACtCC,MAAM,mKAAE9H,IAAI,CAACsC,KAAAA;SACd;KACF,CAAC;AAGG,MAAMgK,GAAG,GAAIA,CAClBO,KAAgE,EAChEzF,OAAyB,KACC;IAC1B,IAAIlC,MAAM,CAACC,QAAQ,IAAI0H,KAAK,EAAE;QAC5B,OAAO5F,OAAO,CAAC4F,KAAK,oJAAEnN,WAAQ,EAAE0H,OAAc,CAAC;IACjD,CAAC,MAAM,IAAIA,OAAO,EAAEC,OAAO,EAAE;QAC3B,OAAOJ,OAAO,CAACxD,MAAM,CAACqJ,MAAM,CAACD,KAAK,CAAC,EAAEnN,6JAAQ,EAAE0H,OAAc,CAAC;IAChE;IAEA,4KAAOpH,IAAI,CAAC6B,CAAAA,AAAG,EACboF,OAAO,CACLxD,MAAM,CAACsJ,OAAO,CAACF,KAAK,CAAC,EACrB,CAAC,CAACnH,CAAC,EAAEvC,CAAC,CAAC,OAAKnD,IAAI,CAAC6B,kKAAAA,AAAG,EAACsB,CAAC,GAAG1B,CAAC,GAAK;gBAACiE,CAAC;gBAAEjE,CAAC;aAAU,CAAC,CAChD,GACAqL,MAAM,IAAI;QACT,MAAMzE,GAAG,GAAG,CAAA,CAAE;QACd,KAAK,MAAM,CAACxF,CAAC,EAAEmK,CAAC,CAAC,IAAIF,MAAM,CAAE;;YACzBzE,GAAW,CAACxF,CAAC,CAAC,GAAGmK,CAAC;QACtB;QACA,OAAO3E,GAAG;IACZ,CAAC,CACF;AACH,CAAuB;AAGhB,MAAM4E,WAAW,GAAA,WAAA,wKAAkCjN,IAAI,CAACsC,KAAAA,AAAO,EAAA,WAAA,uJAAC1C,MAAM,CAACiI,AAAI,EAAE,CAAC;AAG9E,MAAMqF,WAAW,GAAO9L,KAAQ,yKAAgCpB,IAAI,CAACsC,KAAO,AAAPA,sJAAQ1C,MAAM,CAACmC,AAAI,EAACX,KAAK,CAAC,CAAC;AAGhG,MAAM+L,UAAU,GAAA,WAAA,wJAAG1N,QAAAA,AAAI,EAY5B,CAAC,EAAE,CAACmC,IAAI,EAAEwL,OAAO,EAAEzK,CAAC,wKACpB3C,IAAI,CAAC4C,KAAAA,AAAO,EAACwK,OAAO,GAAGC,KAAK,wKAC1BrN,IAAI,CAAC4C,KAAAA,AAAO,EAAChB,IAAI,GAAGR,KAAK,OACvBpB,IAAI,CAAC6B,kKAAAA,AAAG,EACNuL,OAAO,GACNE,GAAG,GAAK;oBAAC3K,CAAC,CAAC0K,KAAK,EAAEC,GAAG,CAAC;oBAAElM,KAAK;iBAAC,CAChC,CAAC,CAAC,CAAC;AAGH,MAAMiB,OAAO,IAAaD,QAAmC,GAAuBgC,OAAO,sKAACpE,IAAI,CAAC0E,EAAAA,AAAI,EAACtC,QAAQ,CAAC,CAAC;AAGhH,MAAMmL,GAAG,GAAA,WAAA,yJAGZ9N,OAAAA,AAAI,EACN,CAAC,EACD,CAAqBmC,IAAsB,EAAEe,CAA+B,wKAC1E3C,IAAI,CAAC4C,KAAAA,AAAO,EAAChB,IAAI,GAAGH,CAAC,GAAKE,EAAE,CAACgB,CAAC,CAAClB,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CACzC;AAGM,MAAM+L,OAAO,GAAA,WAAA,yJAAG/N,OAAAA,AAAI,EAgBzB,CAAC,EAAE,CAACmC,IAAI,EAAE,EAAEZ,SAAS,EAAEQ,SAAAA,EAAW,IAClCxB,IAAI,CAACgE,0KAAAA,AAAQ,EAACpC,IAAI,EAAE;QAClBZ,SAAS,GAAGmC,CAAC,yJAAKxD,OAAAA,AAAI,EAACqB,SAAS,CAACmC,CAAQ,CAAC,GAAEnD,IAAI,CAACyN,0KAAAA,AAAQ,uKAACzN,IAAI,CAACwC,EAAAA,AAAI,EAACW,CAAC,CAAC,CAAC,CAAC;QACxE3B,SAAS,GAAGC,CAAC,IAAK9B,4JAAAA,AAAI,EAAC6B,SAAS,CAACC,CAAQ,CAAC,EAAEE,EAAE,CAACF,CAAC,CAAC;KAClD,CAAC,CAAC;AAGE,MAAMiM,QAAQ,GAAA,WAAA,yJAKjBjO,OAAAA,AAAI,EACN,CAAC,EACD,CAAqBmC,IAAsB,EAAEe,CAAmC,GAC9E3C,IAAI,CAACgE,2KAAAA,AAAQ,EAACpC,IAAI,EAAE;QAClBZ,SAAS,GAAGmC,CAAC,wKAAKnD,IAAI,CAACyN,MAAQ,AAARA,EAAS9K,CAAC,CAACQ,CAAC,CAAC,uKAAEnD,IAAI,CAACwC,EAAAA,AAAI,EAACW,CAAC,CAAC,CAAC;QACnD3B,SAAS,EAAExB,IAAI,CAACsC,sKAAAA;KACjB,CAAC,CACL;AAGM,MAAMqL,IAAI,IAOfC,GAGC,IACC;IACF,MAAMxL,QAAQ,GAAG,OAAOwL,GAAG,KAAK,UAAU,GAAGA,GAAG,GAAGA,GAAG,CAACC,GAAG;IAC1D,OAAOxL,OAAO,CAAC,MAAK;QAClB,IAAI;YACF,QAAOrC,IAAI,CAACsC,yKAAAA,AAAO,EAACF,QAAQ,EAAE,CAAC;QACjC,CAAC,CAAC,OAAOoC,KAAK,EAAE;YACd,4KAAOxE,IAAI,CAACwC,EAAAA,AAAI,EAAC,OAAO,IAAIoL,GAAG,GAAGA,GAAG,CAACE,KAAK,CAACtJ,KAAK,CAAC,GAAGA,KAAK,CAAC;QAC7D;IACF,CAAC,CAAC;AACJ,CAAC;AAED,cAAA,GACA,MAAMZ,KAAK,GAAA,WAAA,wKAAkB5D,IAAI,CAACsC,KAAAA,AAAO,EAAC,KAAK,CAAC,CAAC;;AAO1C,MAAM0L,MAAM,GAAA,WAAA,GAAGvO,6JAAAA,AAAI,EAGxB,CAAC,EAAE,CAACmC,IAAI,EAAE/B,SAAS,GACnBwC,OAAO,CACL,IAAMxC,SAAS,EAAE,GAAGoN,WAAW,GAAGnL,MAAM,CAACF,IAAI,CAAC,CAC/C,CAAC;AAGG,MAAMqM,SAAS,GAAA,WAAA,IAAGxO,4JAAAA,AAAI,EAU3B,CAAC,EAAE,CAACmC,IAAI,EAAE/B,SAAS,wKACnBG,IAAI,CAAC4C,KAAAA,AAAO,EACV/C,SAAS,GACRyF,IAAI,GAAKA,IAAI,GAAG2H,WAAW,GAAGnL,MAAM,CAACF,IAAI,CAAC,CAC5C,CAAC;AAGG,MAAMsM,MAAM,IAAatM,IAAqC,wKACnE5B,IAAI,CAACgE,MAAAA,AAAQ,EAACpC,IAAI,EAAE;QAClBZ,SAAS,sJAAEpB,MAAM,CAACgF,CAAAA,AAAK,EAAC;YACtBgD,MAAM,EAAEA,CAAA,wKAAM5H,IAAI,CAACsC,KAAAA,AAAO,sJAAC1C,MAAM,CAACiI,AAAI,EAAE,CAAC;YACzCC,MAAM,EAAE9H,IAAI,CAACwC,mKAAAA;SACd,CAAC;QACFhB,SAAS,GAAGC,CAAC,wKAAKzB,IAAI,CAACsC,KAAAA,AAAO,sJAAC1C,MAAM,CAACmC,AAAI,EAACN,CAAC,CAAC;KAC9C,CAAC;AAGG,MAAM0M,WAAW,GAAA,WAAA,yJAAG1O,OAAAA,AAAI,EAW7B,CAAC,EACD,CAACkN,QAAQ,EAAEhK,CAAC,wKACV3C,IAAI,CAAC4C,KAAAA,AAAO,EAAC8J,SAAS,CAACC,QAAQ,EAAEhK,CAAC,CAAC,EAAE,CAAC,CAACyL,MAAM,EAAEtB,MAAM,CAAC,sJACpD/N,EAAE,CAACsP,eAAe,AAAfA,EAAgBD,MAAM,CAAC,IACxBpO,IAAI,CAACwC,sKAAAA,AAAI,EAAC4L,MAAM,CAAC,wKACjBpO,IAAI,CAACsC,KAAAA,AAAO,EAACwK,MAAM,CAAC,CAAC,CAC5B;AAGM,MAAMwB,aAAa,GAAA,WAAA,IAAG7O,4JAAAA,AAAI,EAG/B,CAAC,EAAE,CAACkN,QAAQ,EAAEhK,CAAC,GAAKoE,IAAI,CAACE,OAAO,CAAC0F,QAAQ,GAAGlL,CAAC,GAAKsF,IAAI,CAACpE,CAAC,CAAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAG1D,MAAM8M,IAAI,GAAA,WAAA,yJAAG9O,OAAAA,AAAI,EAGtB,CAAC,EAAE,CAACmC,IAAI,EAAE/B,SAAS,GACnBwC,OAAO,CACL,IAAMxC,SAAS,EAAE,GAAGiC,MAAM,CAACF,IAAI,CAAC,GAAGqL,WAAW,CAC/C,CAAC;AAGG,MAAMuB,OAAO,GAAA,WAAA,yJAAG/O,OAAAA,AAAI,EAUzB,CAAC,EAAE,CAACmC,IAAI,EAAE/B,SAAS,wKACnBG,IAAI,CAAC4C,KAAAA,AAAO,EACV/C,SAAS,GACRyF,IAAI,GAAKA,IAAI,GAAGxD,MAAM,CAACF,IAAI,CAAC,GAAGqL,WAAW,CAC5C,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1522, "column": 0}, "map": {"version": 3, "file": "tRef.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/tRef.ts"], "sourcesContent": ["import { dual } from \"../../Function.js\"\nimport * as Option from \"../../Option.js\"\nimport type { Pipeable } from \"../../Pipeable.js\"\nimport { pipeArguments } from \"../../Pipeable.js\"\nimport type * as STM from \"../../STM.js\"\nimport type * as TRef from \"../../TRef.js\"\nimport * as core from \"./core.js\"\nimport * as Entry from \"./entry.js\"\nimport type * as Journal from \"./journal.js\"\nimport type * as TxnId from \"./txnId.js\"\nimport * as Versioned from \"./versioned.js\"\n\n/** @internal */\nconst TRefSymbolKey = \"effect/TRef\"\n\n/** @internal */\nexport const TRefTypeId: TRef.TRefTypeId = Symbol.for(\n  TRefSymbolKey\n) as TRef.TRefTypeId\n\nexport const tRefVariance = {\n  /* c8 ignore next */\n  _A: (_: any) => _\n}\n\n/** @internal */\nexport class TRefImpl<in out A> implements TRef.TRef<A>, Pipeable {\n  readonly [TRefTypeId] = tRefVariance\n  /** @internal */\n  todos: Map<TxnId.TxnId, Journal.Todo>\n  /** @internal */\n  versioned: Versioned.Versioned<A>\n  constructor(value: A) {\n    this.versioned = new Versioned.Versioned(value)\n    this.todos = new Map()\n  }\n  modify<B>(f: (a: A) => readonly [B, A]): STM.STM<B> {\n    return core.effect<never, B>((journal) => {\n      const entry = getOrMakeEntry(this, journal)\n      const [retValue, newValue] = f(Entry.unsafeGet(entry) as A)\n      Entry.unsafeSet(entry, newValue)\n      return retValue\n    })\n  }\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const make = <A>(value: A): STM.STM<TRef.TRef<A>> =>\n  core.effect<never, TRef.TRef<A>>((journal) => {\n    const ref = new TRefImpl(value)\n    journal.set(ref, Entry.make(ref, true))\n    return ref\n  })\n\n/** @internal */\nexport const get = <A>(self: TRef.TRef<A>) => self.modify((a) => [a, a])\n\n/** @internal */\nexport const set = dual<\n  <A>(value: A) => (self: TRef.TRef<A>) => STM.STM<void>,\n  <A>(self: TRef.TRef<A>, value: A) => STM.STM<void>\n>(\n  2,\n  <A>(self: TRef.TRef<A>, value: A): STM.STM<void> => self.modify((): [void, A] => [void 0, value])\n)\n\n/** @internal */\nexport const getAndSet = dual<\n  <A>(value: A) => (self: TRef.TRef<A>) => STM.STM<A>,\n  <A>(self: TRef.TRef<A>, value: A) => STM.STM<A>\n>(2, (self, value) => self.modify((a) => [a, value]))\n\n/** @internal */\nexport const getAndUpdate = dual<\n  <A>(f: (a: A) => A) => (self: TRef.TRef<A>) => STM.STM<A>,\n  <A>(self: TRef.TRef<A>, f: (a: A) => A) => STM.STM<A>\n>(2, (self, f) => self.modify((a) => [a, f(a)]))\n\n/** @internal */\nexport const getAndUpdateSome = dual<\n  <A>(f: (a: A) => Option.Option<A>) => (self: TRef.TRef<A>) => STM.STM<A>,\n  <A>(self: TRef.TRef<A>, f: (a: A) => Option.Option<A>) => STM.STM<A>\n>(2, (self, f) =>\n  self.modify((a) =>\n    Option.match(f(a), {\n      onNone: () => [a, a],\n      onSome: (b) => [a, b]\n    })\n  ))\n\n/** @internal */\nexport const setAndGet = dual<\n  <A>(value: A) => (self: TRef.TRef<A>) => STM.STM<A>,\n  <A>(self: TRef.TRef<A>, value: A) => STM.STM<A>\n>(2, (self, value) => self.modify(() => [value, value]))\n\n/** @internal */\nexport const modify = dual<\n  <A, B>(f: (a: A) => readonly [B, A]) => (self: TRef.TRef<A>) => STM.STM<B>,\n  <A, B>(self: TRef.TRef<A>, f: (a: A) => readonly [B, A]) => STM.STM<B>\n>(2, (self, f) => self.modify(f))\n\n/** @internal */\nexport const modifySome = dual<\n  <A, B>(fallback: B, f: (a: A) => Option.Option<readonly [B, A]>) => (self: TRef.TRef<A>) => STM.STM<B>,\n  <A, B>(self: TRef.TRef<A>, fallback: B, f: (a: A) => Option.Option<readonly [B, A]>) => STM.STM<B>\n>(3, (self, fallback, f) =>\n  self.modify((a) =>\n    Option.match(f(a), {\n      onNone: () => [fallback, a],\n      onSome: (b) => b\n    })\n  ))\n\n/** @internal */\nexport const update = dual<\n  <A>(f: (a: A) => A) => (self: TRef.TRef<A>) => STM.STM<void>,\n  <A>(self: TRef.TRef<A>, f: (a: A) => A) => STM.STM<void>\n>(2, (self, f) => self.modify((a) => [void 0, f(a)]))\n\n/** @internal */\nexport const updateAndGet = dual<\n  <A>(f: (a: A) => A) => (self: TRef.TRef<A>) => STM.STM<A>,\n  <A>(self: TRef.TRef<A>, f: (a: A) => A) => STM.STM<A>\n>(2, (self, f) =>\n  self.modify((a) => {\n    const b = f(a)\n    return [b, b]\n  }))\n\n/** @internal */\nexport const updateSome = dual<\n  <A>(f: (a: A) => Option.Option<A>) => (self: TRef.TRef<A>) => STM.STM<void>,\n  <A>(self: TRef.TRef<A>, f: (a: A) => Option.Option<A>) => STM.STM<void>\n>(\n  2,\n  (self, f) =>\n    self.modify((a) => [\n      void 0,\n      Option.match(f(a), {\n        onNone: () => a,\n        onSome: (b) => b\n      })\n    ])\n)\n\n/** @internal */\nexport const updateSomeAndGet = dual<\n  <A>(f: (a: A) => Option.Option<A>) => (self: TRef.TRef<A>) => STM.STM<A>,\n  <A>(self: TRef.TRef<A>, f: (a: A) => Option.Option<A>) => STM.STM<A>\n>(\n  2,\n  (self, f) =>\n    self.modify((a) =>\n      Option.match(f(a), {\n        onNone: () => [a, a],\n        onSome: (b) => [b, b]\n      })\n    )\n)\n\n/** @internal */\nconst getOrMakeEntry = <A>(self: TRef.TRef<A>, journal: Journal.Journal): Entry.Entry => {\n  if (journal.has(self)) {\n    return journal.get(self)!\n  }\n  const entry = Entry.make(self, false)\n  journal.set(self, entry)\n  return entry\n}\n\n/** @internal */\nexport const unsafeGet: {\n  (journal: Journal.Journal): <A>(self: TRef.TRef<A>) => A\n  <A>(self: TRef.TRef<A>, journal: Journal.Journal): A\n} = dual<\n  (journal: Journal.Journal) => <A>(self: TRef.TRef<A>) => A,\n  <A>(self: TRef.TRef<A>, journal: Journal.Journal) => A\n>(2, <A>(self: TRef.TRef<A>, journal: Journal.Journal) => Entry.unsafeGet(getOrMakeEntry(self, journal)) as A)\n\n/** @internal */\nexport const unsafeSet: {\n  <A>(value: A, journal: Journal.Journal): (self: TRef.TRef<A>) => void\n  <A>(self: TRef.TRef<A>, value: A, journal: Journal.Journal): void\n} = dual<\n  <A>(value: A, journal: Journal.Journal) => (self: TRef.TRef<A>) => void,\n  <A>(self: TRef.TRef<A>, value: A, journal: Journal.Journal) => void\n>(3, (self, value, journal) => {\n  const entry = getOrMakeEntry(self, journal)\n  Entry.unsafeSet(entry, value)\n  return undefined\n})\n"], "names": ["dual", "Option", "pipeArguments", "core", "Entry", "Versioned", "TRefSymbolKey", "TRefTypeId", "Symbol", "for", "tRefV<PERSON>ce", "_A", "_", "TRefImpl", "todos", "versioned", "constructor", "value", "Map", "modify", "f", "effect", "journal", "entry", "getOrMakeEntry", "retValue", "newValue", "unsafeGet", "unsafeSet", "pipe", "arguments", "make", "ref", "set", "get", "self", "a", "getAndSet", "getAndUpdate", "getAndUpdateSome", "match", "onNone", "onSome", "b", "setAndGet", "modifySome", "fallback", "update", "updateAndGet", "updateSome", "updateSomeAndGet", "has", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,SAASA,IAAI,QAAQ,mBAAmB;AACxC,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AAEzC,SAASC,aAAa,QAAQ,mBAAmB;AAGjD,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,KAAK,MAAM,YAAY;AAGnC,OAAO,KAAKC,SAAS,MAAM,gBAAgB;;;;;;;AAE3C,cAAA,GACA,MAAMC,aAAa,GAAG,aAAa;AAG5B,MAAMC,UAAU,GAAA,WAAA,GAAoBC,MAAM,CAACC,GAAG,CACnDH,aAAa,CACK;AAEb,MAAMI,YAAY,GAAG;IAC1B,kBAAA,GACAC,EAAE,GAAGC,CAAM,GAAKA;CACjB;AAGK,MAAOC,QAAQ;IACV,CAACN,UAAU,CAAA,GAAIG,YAAY,CAAA;IACpC,cAAA,GACAI,KAAK,CAAA;IACL,cAAA,GACAC,SAAS,CAAA;IACTC,YAAYC,KAAQ,CAAA;QAClB,IAAI,CAACF,SAAS,GAAG,0KAAIV,SAAS,CAACA,EAAS,CAACY,KAAK,CAAC;QAC/C,IAAI,CAACH,KAAK,GAAG,IAAII,GAAG,EAAE;IACxB;IACAC,MAAMA,CAAIC,CAA4B,EAAA;QACpC,4KAAOjB,IAAI,CAACkB,IAAAA,AAAM,GAAYC,OAAO,IAAI;YACvC,MAAMC,KAAK,GAAGC,cAAc,CAAC,IAAI,EAAEF,OAAO,CAAC;YAC3C,MAAM,CAACG,QAAQ,EAAEC,QAAQ,CAAC,GAAGN,CAAC,uKAAChB,KAAK,CAACuB,MAAAA,AAAS,EAACJ,KAAK,CAAM,CAAC;kLAC3DnB,KAAK,CAACwB,MAAAA,AAAS,EAACL,KAAK,EAAEG,QAAQ,CAAC;YAChC,OAAOD,QAAQ;QACjB,CAAC,CAAC;IACJ;IACAI,IAAIA,CAAA,EAAA;QACF,6JAAO3B,gBAAAA,AAAa,EAAC,IAAI,EAAE4B,SAAS,CAAC;IACvC;;AAIK,MAAMC,IAAI,IAAOd,KAAQ,GAC9Bd,IAAI,CAACkB,yKAAAA,AAAM,GAAuBC,OAAO,IAAI;QAC3C,MAAMU,GAAG,GAAG,IAAInB,QAAQ,CAACI,KAAK,CAAC;QAC/BK,OAAO,CAACW,GAAG,CAACD,GAAG,wKAAE5B,KAAK,CAAC2B,CAAAA,AAAI,EAACC,GAAG,EAAE,IAAI,CAAC,CAAC;QACvC,OAAOA,GAAG;IACZ,CAAC,CAAC;AAGG,MAAME,GAAG,IAAOC,IAAkB,GAAKA,IAAI,CAAChB,MAAM,EAAEiB,CAAC,GAAK;YAACA,CAAC;YAAEA,CAAC;SAAC,CAAC;AAGjE,MAAMH,GAAG,GAAA,WAAA,GAAGjC,6JAAAA,AAAI,EAIrB,CAAC,EACD,CAAImC,IAAkB,EAAElB,KAAQ,GAAoBkB,IAAI,CAAChB,MAAM,CAAC,IAAiB;YAAC,KAAK,CAAC;YAAEF,KAAK;SAAC,CAAC,CAClG;AAGM,MAAMoB,SAAS,GAAA,WAAA,yJAAGrC,OAAAA,AAAI,EAG3B,CAAC,EAAE,CAACmC,IAAI,EAAElB,KAAK,GAAKkB,IAAI,CAAChB,MAAM,CAAEiB,CAAC,IAAK;YAACA,CAAC;YAAEnB,KAAK;SAAC,CAAC,CAAC;AAG9C,MAAMqB,YAAY,GAAA,WAAA,yJAAGtC,OAAAA,AAAI,EAG9B,CAAC,EAAE,CAACmC,IAAI,EAAEf,CAAC,GAAKe,IAAI,CAAChB,MAAM,EAAEiB,CAAC,GAAK;YAACA,CAAC;YAAEhB,CAAC,CAACgB,CAAC,CAAC;SAAC,CAAC,CAAC;AAGzC,MAAMG,gBAAgB,GAAA,WAAA,yJAAGvC,OAAAA,AAAI,EAGlC,CAAC,EAAE,CAACmC,IAAI,EAAEf,CAAC,GACXe,IAAI,CAAChB,MAAM,EAAEiB,CAAC,uJACZnC,MAAM,CAACuC,CAAAA,AAAK,EAACpB,CAAC,CAACgB,CAAC,CAAC,EAAE;YACjBK,MAAM,EAAEA,CAAA,GAAM;oBAACL,CAAC;oBAAEA,CAAC;iBAAC;YACpBM,MAAM,GAAGC,CAAC,GAAK;oBAACP,CAAC;oBAAEO,CAAC;iBAAA;SACrB,CAAC,CACH,CAAC;AAGG,MAAMC,SAAS,GAAA,WAAA,IAAG5C,4JAAAA,AAAI,EAG3B,CAAC,EAAE,CAACmC,IAAI,EAAElB,KAAK,GAAKkB,IAAI,CAAChB,MAAM,CAAC,IAAM;YAACF,KAAK;YAAEA,KAAK;SAAC,CAAC,CAAC;AAGjD,MAAME,MAAM,GAAA,WAAA,yJAAGnB,OAAAA,AAAI,EAGxB,CAAC,EAAE,CAACmC,IAAI,EAAEf,CAAC,GAAKe,IAAI,CAAChB,MAAM,CAACC,CAAC,CAAC,CAAC;AAG1B,MAAMyB,UAAU,GAAA,WAAA,yJAAG7C,OAAAA,AAAI,EAG5B,CAAC,EAAE,CAACmC,IAAI,EAAEW,QAAQ,EAAE1B,CAAC,GACrBe,IAAI,CAAChB,MAAM,EAAEiB,CAAC,uJACZnC,MAAM,CAACuC,CAAAA,AAAK,EAACpB,CAAC,CAACgB,CAAC,CAAC,EAAE;YACjBK,MAAM,EAAEA,CAAA,GAAM;oBAACK,QAAQ;oBAAEV,CAAC;iBAAC;YAC3BM,MAAM,EAAGC,CAAC,IAAKA;SAChB,CAAC,CACH,CAAC;AAGG,MAAMI,MAAM,GAAA,WAAA,yJAAG/C,OAAAA,AAAI,EAGxB,CAAC,EAAE,CAACmC,IAAI,EAAEf,CAAC,GAAKe,IAAI,CAAChB,MAAM,CAAEiB,CAAC,IAAK;YAAC,KAAK,CAAC;YAAEhB,CAAC,CAACgB,CAAC,CAAC;SAAC,CAAC,CAAC;AAG9C,MAAMY,YAAY,GAAA,WAAA,yJAAGhD,OAAAA,AAAI,EAG9B,CAAC,EAAE,CAACmC,IAAI,EAAEf,CAAC,GACXe,IAAI,CAAChB,MAAM,EAAEiB,CAAC,IAAI;QAChB,MAAMO,CAAC,GAAGvB,CAAC,CAACgB,CAAC,CAAC;QACd,OAAO;YAACO,CAAC;YAAEA,CAAC;SAAC;IACf,CAAC,CAAC,CAAC;AAGE,MAAMM,UAAU,GAAA,WAAA,yJAAGjD,OAAAA,AAAI,EAI5B,CAAC,EACD,CAACmC,IAAI,EAAEf,CAAC,GACNe,IAAI,CAAChB,MAAM,EAAEiB,CAAC,GAAK;YACjB,KAAK,CAAC;gKACNnC,MAAM,CAACuC,CAAAA,AAAK,EAACpB,CAAC,CAACgB,CAAC,CAAC,EAAE;gBACjBK,MAAM,EAAEA,CAAA,GAAML,CAAC;gBACfM,MAAM,GAAGC,CAAC,GAAKA;aAChB,CAAC;SACH,CAAC,CACL;AAGM,MAAMO,gBAAgB,GAAA,WAAA,yJAAGlD,OAAAA,AAAI,EAIlC,CAAC,EACD,CAACmC,IAAI,EAAEf,CAAC,GACNe,IAAI,CAAChB,MAAM,EAAEiB,CAAC,IACZnC,MAAM,CAACuC,oJAAAA,AAAK,EAACpB,CAAC,CAACgB,CAAC,CAAC,EAAE;YACjBK,MAAM,EAAEA,CAAA,GAAM;oBAACL,CAAC;oBAAEA,CAAC;iBAAC;YACpBM,MAAM,GAAGC,CAAC,GAAK;oBAACA,CAAC;oBAAEA,CAAC;iBAAA;SACrB,CAAC,CACH,CACJ;AAED,cAAA,GACA,MAAMnB,cAAc,GAAGA,CAAIW,IAAkB,EAAEb,OAAwB,KAAiB;IACtF,IAAIA,OAAO,CAAC6B,GAAG,CAAChB,IAAI,CAAC,EAAE;QACrB,OAAOb,OAAO,CAACY,GAAG,CAACC,IAAI,CAAE;IAC3B;IACA,MAAMZ,KAAK,yKAAGnB,KAAK,CAAC2B,CAAAA,AAAI,EAACI,IAAI,EAAE,KAAK,CAAC;IACrCb,OAAO,CAACW,GAAG,CAACE,IAAI,EAAEZ,KAAK,CAAC;IACxB,OAAOA,KAAK;AACd,CAAC;AAGM,MAAMI,SAAS,GAAA,WAAA,GAGlB3B,6JAAAA,AAAI,EAGN,CAAC,EAAE,CAAImC,IAAkB,EAAEb,OAAwB,yKAAKlB,KAAK,CAACuB,MAAAA,AAAS,EAACH,cAAc,CAACW,IAAI,EAAEb,OAAO,CAAC,CAAM,CAAC;AAGvG,MAAMM,SAAS,GAAA,WAAA,yJAGlB5B,OAAAA,AAAI,EAGN,CAAC,EAAE,CAACmC,IAAI,EAAElB,KAAK,EAAEK,OAAO,KAAI;IAC5B,MAAMC,KAAK,GAAGC,cAAc,CAACW,IAAI,EAAEb,OAAO,CAAC;0KAC3ClB,KAAK,CAACwB,MAAAA,AAAS,EAACL,KAAK,EAAEN,KAAK,CAAC;IAC7B,OAAOmC,SAAS;AAClB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1670, "column": 0}, "map": {"version": 3, "file": "tQueue.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/tQueue.ts"], "sourcesContent": ["import * as RA from \"../../Array.js\"\nimport * as Chunk from \"../../Chunk.js\"\nimport { dual, pipe } from \"../../Function.js\"\nimport * as Option from \"../../Option.js\"\nimport { hasProperty, type Predicate } from \"../../Predicate.js\"\nimport type * as STM from \"../../STM.js\"\nimport type * as TQueue from \"../../TQueue.js\"\nimport type * as TRef from \"../../TRef.js\"\nimport * as core from \"./core.js\"\nimport * as OpCodes from \"./opCodes/strategy.js\"\nimport * as stm from \"./stm.js\"\nimport * as tRef from \"./tRef.js\"\n\nconst TEnqueueSymbolKey = \"effect/TQueue/TEnqueue\"\n\n/** @internal */\nexport const TEnqueueTypeId: TQueue.TEnqueueTypeId = Symbol.for(TEnqueueSymbolKey) as TQueue.TEnqueueTypeId\n\nconst TDequeueSymbolKey = \"effect/TQueue/TDequeue\"\n\n/** @internal */\nexport const TDequeueTypeId: TQueue.TDequeueTypeId = Symbol.for(TDequeueSymbolKey) as TQueue.TDequeueTypeId\n\n/**\n * A `Strategy` describes how the queue will handle values if the queue is at\n * capacity.\n *\n * @internal\n */\nexport type TQueueStrategy = BackPressure | Dropping | Sliding\n\n/**\n * A strategy that retries if the queue is at capacity.\n *\n * @internal\n */\nexport interface BackPressure {\n  readonly _tag: OpCodes.OP_BACKPRESSURE_STRATEGY\n}\n\n/**\n * A strategy that drops new values if the queue is at capacity.\n *\n * @internal\n */\nexport interface Dropping {\n  readonly _tag: OpCodes.OP_DROPPING_STRATEGY\n}\n\n/**\n * A strategy that drops old values if the queue is at capacity.\n *\n * @internal\n */\nexport interface Sliding {\n  readonly _tag: OpCodes.OP_SLIDING_STRATEGY\n}\n\n/** @internal */\nexport const BackPressure: TQueueStrategy = {\n  _tag: OpCodes.OP_BACKPRESSURE_STRATEGY\n}\n\n/** @internal */\nexport const Dropping: TQueueStrategy = {\n  _tag: OpCodes.OP_DROPPING_STRATEGY\n}\n\n/** @internal */\nexport const Sliding: TQueueStrategy = {\n  _tag: OpCodes.OP_SLIDING_STRATEGY\n}\n\n/** @internal */\nexport const tDequeueVariance = {\n  /* c8 ignore next */\n  _Out: (_: never) => _\n}\n\n/** @internal */\nexport const tEnqueueVariance = {\n  /* c8 ignore next */\n  _In: (_: unknown) => _\n}\n\nclass TQueueImpl<in out A> implements TQueue.TQueue<A> {\n  readonly [TDequeueTypeId] = tDequeueVariance\n  readonly [TEnqueueTypeId] = tEnqueueVariance\n  constructor(\n    readonly ref: TRef.TRef<Array<A> | undefined>,\n    readonly requestedCapacity: number,\n    readonly strategy: TQueueStrategy\n  ) {}\n\n  capacity(): number {\n    return this.requestedCapacity\n  }\n\n  size: STM.STM<number> = core.withSTMRuntime((runtime) => {\n    const queue = tRef.unsafeGet(this.ref, runtime.journal)\n    if (queue === undefined) {\n      return core.interruptAs(runtime.fiberId)\n    }\n    return core.succeed(queue.length)\n  })\n\n  isFull: STM.STM<boolean> = core.map(this.size, (size) => size === this.requestedCapacity)\n\n  isEmpty: STM.STM<boolean> = core.map(this.size, (size) => size === 0)\n\n  shutdown: STM.STM<void> = core.withSTMRuntime((runtime) => {\n    tRef.unsafeSet(this.ref, void 0, runtime.journal)\n    return stm.void\n  })\n\n  isShutdown: STM.STM<boolean> = core.effect<never, boolean>((journal) => {\n    const queue = tRef.unsafeGet(this.ref, journal)\n    return queue === undefined\n  })\n\n  awaitShutdown: STM.STM<void> = core.flatMap(\n    this.isShutdown,\n    (isShutdown) => isShutdown ? stm.void : core.retry\n  )\n\n  offer(value: A): STM.STM<boolean> {\n    return core.withSTMRuntime((runtime) => {\n      const queue = pipe(this.ref, tRef.unsafeGet(runtime.journal))\n      if (queue === undefined) {\n        return core.interruptAs(runtime.fiberId)\n      }\n      if (queue.length < this.requestedCapacity) {\n        queue.push(value)\n        tRef.unsafeSet(this.ref, queue, runtime.journal)\n        return core.succeed(true)\n      }\n      switch (this.strategy._tag) {\n        case OpCodes.OP_BACKPRESSURE_STRATEGY: {\n          return core.retry\n        }\n        case OpCodes.OP_DROPPING_STRATEGY: {\n          return core.succeed(false)\n        }\n        case OpCodes.OP_SLIDING_STRATEGY: {\n          if (queue.length === 0) {\n            return core.succeed(true)\n          }\n          queue.shift()\n          queue.push(value)\n          tRef.unsafeSet(this.ref, queue, runtime.journal)\n          return core.succeed(true)\n        }\n      }\n    })\n  }\n\n  offerAll(iterable: Iterable<A>): STM.STM<boolean> {\n    return core.withSTMRuntime((runtime) => {\n      const as = Array.from(iterable)\n      const queue = tRef.unsafeGet(this.ref, runtime.journal)\n      if (queue === undefined) {\n        return core.interruptAs(runtime.fiberId)\n      }\n      if (queue.length + as.length <= this.requestedCapacity) {\n        tRef.unsafeSet(this.ref, [...queue, ...as], runtime.journal)\n        return core.succeed(true)\n      }\n      switch (this.strategy._tag) {\n        case OpCodes.OP_BACKPRESSURE_STRATEGY: {\n          return core.retry\n        }\n        case OpCodes.OP_DROPPING_STRATEGY: {\n          const forQueue = as.slice(0, this.requestedCapacity - queue.length)\n          tRef.unsafeSet(this.ref, [...queue, ...forQueue], runtime.journal)\n          return core.succeed(false)\n        }\n        case OpCodes.OP_SLIDING_STRATEGY: {\n          const forQueue = as.slice(0, this.requestedCapacity - queue.length)\n          const toDrop = queue.length + forQueue.length - this.requestedCapacity\n          const newQueue = queue.slice(toDrop)\n          tRef.unsafeSet(this.ref, [...newQueue, ...forQueue], runtime.journal)\n          return core.succeed(true)\n        }\n      }\n    })\n  }\n\n  peek: STM.STM<A> = core.withSTMRuntime((runtime) => {\n    const queue = tRef.unsafeGet(this.ref, runtime.journal)\n    if (queue === undefined) {\n      return core.interruptAs(runtime.fiberId)\n    }\n    if (queue.length === 0) {\n      return core.retry\n    }\n    return core.succeed(queue[0])\n  })\n\n  peekOption: STM.STM<Option.Option<A>> = core.withSTMRuntime((runtime) => {\n    const queue = tRef.unsafeGet(this.ref, runtime.journal)\n    if (queue === undefined) {\n      return core.interruptAs(runtime.fiberId)\n    }\n    return core.succeed(Option.fromNullable(queue[0]))\n  })\n\n  take: STM.STM<A> = core.withSTMRuntime((runtime) => {\n    const queue = tRef.unsafeGet(this.ref, runtime.journal)\n    if (queue === undefined) {\n      return core.interruptAs(runtime.fiberId)\n    }\n    if (queue.length === 0) {\n      return core.retry\n    }\n    const dequeued = queue.shift()!\n    tRef.unsafeSet(this.ref, queue, runtime.journal)\n    return core.succeed(dequeued)\n  })\n\n  takeAll: STM.STM<Array<A>> = core.withSTMRuntime((runtime) => {\n    const queue = tRef.unsafeGet(this.ref, runtime.journal)\n    if (queue === undefined) {\n      return core.interruptAs(runtime.fiberId)\n    }\n    tRef.unsafeSet(this.ref, [], runtime.journal)\n    return core.succeed(queue)\n  })\n\n  takeUpTo(max: number): STM.STM<Array<A>> {\n    return core.withSTMRuntime((runtime) => {\n      const queue = tRef.unsafeGet(this.ref, runtime.journal)\n      if (queue === undefined) {\n        return core.interruptAs(runtime.fiberId)\n      }\n      const [toTake, remaining] = Chunk.splitAt(Chunk.unsafeFromArray(queue), max)\n      tRef.unsafeSet<Array<A> | undefined>(this.ref, Array.from(remaining), runtime.journal)\n      return core.succeed(Array.from(toTake))\n    })\n  }\n}\n\n/** @internal */\nexport const isTQueue = (u: unknown): u is TQueue.TQueue<unknown> => {\n  return isTEnqueue(u) && isTDequeue(u)\n}\n\n/** @internal */\nexport const isTEnqueue = (u: unknown): u is TQueue.TEnqueue<unknown> => hasProperty(u, TEnqueueTypeId)\n\n/** @internal */\nexport const isTDequeue = (u: unknown): u is TQueue.TDequeue<unknown> => hasProperty(u, TDequeueTypeId)\n\n/** @internal */\nexport const awaitShutdown = <A>(self: TQueue.TDequeue<A> | TQueue.TEnqueue<A>): STM.STM<void> => self.awaitShutdown\n\n/** @internal */\nexport const bounded = <A>(requestedCapacity: number): STM.STM<TQueue.TQueue<A>> =>\n  makeQueue<A>(requestedCapacity, BackPressure)\n\n/** @internal */\nexport const capacity = <A>(self: TQueue.TDequeue<A> | TQueue.TEnqueue<A>): number => {\n  return self.capacity()\n}\n\n/** @internal */\nexport const dropping = <A>(requestedCapacity: number): STM.STM<TQueue.TQueue<A>> =>\n  makeQueue<A>(requestedCapacity, Dropping)\n\n/** @internal */\nexport const isEmpty = <A>(self: TQueue.TDequeue<A> | TQueue.TEnqueue<A>): STM.STM<boolean> => self.isEmpty\n\n/** @internal */\nexport const isFull = <A>(self: TQueue.TDequeue<A> | TQueue.TEnqueue<A>): STM.STM<boolean> => self.isFull\n\n/** @internal */\nexport const isShutdown = <A>(self: TQueue.TDequeue<A> | TQueue.TEnqueue<A>): STM.STM<boolean> => self.isShutdown\n\n/** @internal */\nexport const offer = dual<\n  <A>(value: A) => (self: TQueue.TEnqueue<A>) => STM.STM<void>,\n  <A>(self: TQueue.TEnqueue<A>, value: A) => STM.STM<void>\n>(2, (self, value) => self.offer(value))\n\n/** @internal */\nexport const offerAll = dual<\n  <A>(iterable: Iterable<A>) => (self: TQueue.TEnqueue<A>) => STM.STM<boolean>,\n  <A>(self: TQueue.TEnqueue<A>, iterable: Iterable<A>) => STM.STM<boolean>\n>(2, (self, iterable) => self.offerAll(iterable))\n\n/** @internal */\nexport const peek = <A>(self: TQueue.TDequeue<A>): STM.STM<A> => self.peek\n\n/** @internal */\nexport const peekOption = <A>(self: TQueue.TDequeue<A>): STM.STM<Option.Option<A>> => self.peekOption\n\n/** @internal */\nexport const poll = <A>(self: TQueue.TDequeue<A>): STM.STM<Option.Option<A>> =>\n  pipe(self.takeUpTo(1), core.map(RA.head))\n\n/** @internal */\nexport const seek = dual<\n  <A>(predicate: Predicate<A>) => (self: TQueue.TDequeue<A>) => STM.STM<A>,\n  <A>(self: TQueue.TDequeue<A>, predicate: Predicate<A>) => STM.STM<A>\n>(2, (self, predicate) => seekLoop(self, predicate))\n\nconst seekLoop = <A>(self: TQueue.TDequeue<A>, predicate: Predicate<A>): STM.STM<A> =>\n  core.flatMap(\n    self.take,\n    (a) => predicate(a) ? core.succeed(a) : seekLoop(self, predicate)\n  )\n\n/** @internal */\nexport const shutdown = <A>(self: TQueue.TDequeue<A> | TQueue.TEnqueue<A>): STM.STM<void> => self.shutdown\n\n/** @internal */\nexport const size = <A>(self: TQueue.TDequeue<A> | TQueue.TEnqueue<A>): STM.STM<number> => self.size\n\n/** @internal */\nexport const sliding = <A>(requestedCapacity: number): STM.STM<TQueue.TQueue<A>> =>\n  makeQueue<A>(requestedCapacity, Sliding)\n\n/** @internal */\nexport const take = <A>(self: TQueue.TDequeue<A>): STM.STM<A> => self.take\n\n/** @internal */\nexport const takeAll = <A>(self: TQueue.TDequeue<A>): STM.STM<Array<A>> => self.takeAll\n\n/** @internal */\nexport const takeBetween = dual<\n  (min: number, max: number) => <A>(self: TQueue.TDequeue<A>) => STM.STM<Array<A>>,\n  <A>(self: TQueue.TDequeue<A>, min: number, max: number) => STM.STM<Array<A>>\n>(\n  3,\n  <A>(self: TQueue.TDequeue<A>, min: number, max: number): STM.STM<Array<A>> =>\n    stm.suspend(() => {\n      const takeRemainder = (\n        min: number,\n        max: number,\n        acc: Chunk.Chunk<A>\n      ): STM.STM<Chunk.Chunk<A>> => {\n        if (max < min) {\n          return core.succeed(acc)\n        }\n        return pipe(\n          self.takeUpTo(max),\n          core.flatMap((taken) => {\n            const remaining = min - taken.length\n            if (remaining === 1) {\n              return pipe(\n                self.take,\n                core.map((a) => pipe(acc, Chunk.appendAll(Chunk.unsafeFromArray(taken)), Chunk.append(a)))\n              )\n            }\n            if (remaining > 1) {\n              return pipe(\n                self.take,\n                core.flatMap((a) =>\n                  takeRemainder(\n                    remaining - 1,\n                    max - taken.length - 1,\n                    pipe(acc, Chunk.appendAll(Chunk.unsafeFromArray(taken)), Chunk.append(a))\n                  )\n                )\n              )\n            }\n            return core.succeed(pipe(acc, Chunk.appendAll(Chunk.unsafeFromArray(taken))))\n          })\n        )\n      }\n      return core.map(takeRemainder(min, max, Chunk.empty<A>()), (c) => Array.from(c))\n    })\n)\n\n/** @internal */\nexport const takeN = dual<\n  (n: number) => <A>(self: TQueue.TDequeue<A>) => STM.STM<Array<A>>,\n  <A>(self: TQueue.TDequeue<A>, n: number) => STM.STM<Array<A>>\n>(2, (self, n) => pipe(self, takeBetween(n, n)))\n\n/** @internal */\nexport const takeUpTo = dual<\n  (max: number) => <A>(self: TQueue.TDequeue<A>) => STM.STM<Array<A>>,\n  <A>(self: TQueue.TDequeue<A>, max: number) => STM.STM<Array<A>>\n>(2, (self, max) => self.takeUpTo(max))\n\n/** @internal */\nexport const unbounded = <A>(): STM.STM<TQueue.TQueue<A>> => makeQueue<A>(Number.MAX_SAFE_INTEGER, Dropping)\n\nconst makeQueue = <A>(requestedCapacity: number, strategy: TQueueStrategy): STM.STM<TQueue.TQueue<A>> =>\n  core.map(\n    tRef.make<Array<A> | undefined>([]),\n    (ref) => new TQueueImpl<A>(ref, requestedCapacity, strategy)\n  )\n"], "names": ["RA", "Chunk", "dual", "pipe", "Option", "hasProperty", "core", "OpCodes", "stm", "tRef", "TEnqueueSymbolKey", "TEnqueueTypeId", "Symbol", "for", "TDequeueSymbolKey", "TDequeueTypeId", "BackPressure", "_tag", "OP_BACKPRESSURE_STRATEGY", "Dropping", "OP_DROPPING_STRATEGY", "Sliding", "OP_SLIDING_STRATEGY", "tDequeueVariance", "_Out", "_", "tEnqueueVariance", "_In", "TQueueImpl", "ref", "requestedCapacity", "strategy", "constructor", "capacity", "size", "withSTMRuntime", "runtime", "queue", "unsafeGet", "journal", "undefined", "interruptAs", "fiberId", "succeed", "length", "isFull", "map", "isEmpty", "shutdown", "unsafeSet", "void", "isShutdown", "effect", "await<PERSON><PERSON><PERSON>down", "flatMap", "retry", "offer", "value", "push", "shift", "offerAll", "iterable", "as", "Array", "from", "forQueue", "slice", "toDrop", "newQueue", "peek", "peekOption", "fromNullable", "take", "dequeued", "takeAll", "takeUpTo", "max", "toTake", "remaining", "splitAt", "unsafeFromArray", "isTQueue", "u", "isTEnqueue", "isTDequeue", "self", "bounded", "makeQueue", "dropping", "poll", "head", "seek", "predicate", "seekLoop", "a", "sliding", "takeBetween", "min", "suspend", "<PERSON><PERSON><PERSON><PERSON>", "acc", "taken", "appendAll", "append", "empty", "c", "takeN", "n", "unbounded", "Number", "MAX_SAFE_INTEGER", "make"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,EAAE,MAAM,gBAAgB;AACpC,OAAO,KAAKC,KAAK,MAAM,gBAAgB;AACvC,SAASC,IAAI,EAAEC,IAAI,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,SAASC,WAAW,QAAwB,oBAAoB;AAIhE,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,GAAG,MAAM,UAAU;AAC/B,OAAO,KAAKC,IAAI,MAAM,WAAW;;;;;;;;;;AAEjC,MAAMC,iBAAiB,GAAG,wBAAwB;AAG3C,MAAMC,cAAc,GAAA,WAAA,GAA0BC,MAAM,CAACC,GAAG,CAACH,iBAAiB,CAA0B;AAE3G,MAAMI,iBAAiB,GAAG,wBAAwB;AAG3C,MAAMC,cAAc,GAAA,WAAA,GAA0BH,MAAM,CAACC,GAAG,CAACC,iBAAiB,CAA0B;AAsCpG,MAAME,YAAY,GAAmB;IAC1CC,IAAI,kLAAEV,OAAO,CAACW,mBAAAA;CACf;AAGM,MAAMC,QAAQ,GAAmB;IACtCF,IAAI,kLAAEV,OAAO,CAACa,eAAAA;CACf;AAGM,MAAMC,OAAO,GAAmB;IACrCJ,IAAI,kLAAEV,OAAO,CAACe,cAAAA;CACf;AAGM,MAAMC,gBAAgB,GAAG;IAC9B,kBAAA,GACAC,IAAI,GAAGC,CAAQ,GAAKA;CACrB;AAGM,MAAMC,gBAAgB,GAAG;IAC9B,kBAAA,GACAC,GAAG,GAAGF,CAAU,GAAKA;CACtB;AAED,MAAMG,UAAU;IAIHC,GAAA,CAAA;IACAC,iBAAA,CAAA;IACAC,QAAA,CAAA;IALF,CAAChB,cAAc,CAAA,GAAIQ,gBAAgB,CAAA;IACnC,CAACZ,cAAc,CAAA,GAAIe,gBAAgB,CAAA;IAC5CM,YACWH,GAAoC,EACpCC,iBAAyB,EACzBC,QAAwB,CAAA;QAFxB,IAAA,CAAAF,GAAG,GAAHA,GAAG;QACH,IAAA,CAAAC,iBAAiB,GAAjBA,iBAAiB;QACjB,IAAA,CAAAC,QAAQ,GAARA,QAAQ;IAChB;IAEHE,QAAQA,CAAA,EAAA;QACN,OAAO,IAAI,CAACH,iBAAiB;IAC/B;IAEAI,IAAI,GAAA,WAAA,wKAAoB5B,IAAI,CAAC6B,YAAAA,AAAc,GAAEC,OAAO,IAAI;QACtD,MAAMC,KAAK,uKAAG5B,IAAI,CAAC6B,QAAAA,AAAS,EAAC,IAAI,CAACT,GAAG,EAAEO,OAAO,CAACG,OAAO,CAAC;QACvD,IAAIF,KAAK,KAAKG,SAAS,EAAE;YACvB,4KAAOlC,IAAI,CAACmC,SAAAA,AAAW,EAACL,OAAO,CAACM,OAAO,CAAC;QAC1C;QACA,QAAOpC,IAAI,CAACqC,yKAAAA,AAAO,EAACN,KAAK,CAACO,MAAM,CAAC;IACnC,CAAC,CAAC,CAAA;IAEFC,MAAM,GAAA,WAAA,wKAAqBvC,IAAI,CAACwC,CAAAA,AAAG,EAAC,IAAI,CAACZ,IAAI,GAAGA,IAAI,GAAKA,IAAI,KAAK,IAAI,CAACJ,iBAAiB,CAAC,CAAA;IAEzFiB,OAAO,GAAA,WAAA,wKAAqBzC,IAAI,CAACwC,CAAAA,AAAG,EAAC,IAAI,CAACZ,IAAI,GAAGA,IAAI,GAAKA,IAAI,KAAK,CAAC,CAAC,CAAA;IAErEc,QAAQ,GAAA,WAAA,wKAAkB1C,IAAI,CAAC6B,YAAAA,AAAc,GAAEC,OAAO,IAAI;6KACxD3B,IAAI,CAACwC,OAAAA,AAAS,EAAC,IAAI,CAACpB,GAAG,EAAE,KAAK,CAAC,EAAEO,OAAO,CAACG,OAAO,CAAC;QACjD,uKAAO/B,GAAG,CAAC0C,GAAI;IACjB,CAAC,CAAC,CAAA;IAEFC,UAAU,GAAA,WAAA,wKAAqB7C,IAAI,CAAC8C,IAAAA,AAAM,GAAkBb,OAAO,IAAI;QACrE,MAAMF,KAAK,uKAAG5B,IAAI,CAAC6B,QAAAA,AAAS,EAAC,IAAI,CAACT,GAAG,EAAEU,OAAO,CAAC;QAC/C,OAAOF,KAAK,KAAKG,SAAS;IAC5B,CAAC,CAAC,CAAA;IAEFa,aAAa,GAAA,WAAA,wKAAkB/C,IAAI,CAACgD,KAAAA,AAAO,EACzC,IAAI,CAACH,UAAU,GACdA,UAAU,GAAKA,UAAU,GAAG3C,GAAG,CAAC0C,mKAAI,oKAAG5C,IAAI,CAACiD,GAAK,CACnD,CAAA;IAEDC,KAAKA,CAACC,KAAQ,EAAA;QACZ,4KAAOnD,IAAI,CAAC6B,YAAAA,AAAc,GAAEC,OAAO,IAAI;YACrC,MAAMC,KAAK,wJAAGlC,QAAAA,AAAI,EAAC,IAAI,CAAC0B,GAAG,uKAAEpB,IAAI,CAAC6B,OAAAA,AAAS,EAACF,OAAO,CAACG,OAAO,CAAC,CAAC;YAC7D,IAAIF,KAAK,KAAKG,SAAS,EAAE;gBACvB,4KAAOlC,IAAI,CAACmC,SAAAA,AAAW,EAACL,OAAO,CAACM,OAAO,CAAC;YAC1C;YACA,IAAIL,KAAK,CAACO,MAAM,GAAG,IAAI,CAACd,iBAAiB,EAAE;gBACzCO,KAAK,CAACqB,IAAI,CAACD,KAAK,CAAC;iBACjBhD,IAAI,CAACwC,2KAAAA,AAAS,EAAC,IAAI,CAACpB,GAAG,EAAEQ,KAAK,EAAED,OAAO,CAACG,OAAO,CAAC;gBAChD,4KAAOjC,IAAI,CAACqC,KAAAA,AAAO,EAAC,IAAI,CAAC;YAC3B;YACA,OAAQ,IAAI,CAACZ,QAAQ,CAACd,IAAI;gBACxB,qLAAKV,OAAO,CAACW,mBAAwB;oBAAE;wBACrC,wKAAOZ,IAAI,CAACiD,GAAK;oBACnB;gBACA,qLAAKhD,OAAO,CAACa,eAAoB;oBAAE;wBACjC,OAAOd,IAAI,CAACqC,0KAAAA,AAAO,EAAC,KAAK,CAAC;oBAC5B;gBACA,qLAAKpC,OAAO,CAACe,cAAmB;oBAAE;wBAChC,IAAIe,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;4BACtB,4KAAOtC,IAAI,CAACqC,KAAAA,AAAO,EAAC,IAAI,CAAC;wBAC3B;wBACAN,KAAK,CAACsB,KAAK,EAAE;wBACbtB,KAAK,CAACqB,IAAI,CAACD,KAAK,CAAC;6LACjBhD,IAAI,CAACwC,OAAAA,AAAS,EAAC,IAAI,CAACpB,GAAG,EAAEQ,KAAK,EAAED,OAAO,CAACG,OAAO,CAAC;wBAChD,4KAAOjC,IAAI,CAACqC,KAAAA,AAAO,EAAC,IAAI,CAAC;oBAC3B;YACF;QACF,CAAC,CAAC;IACJ;IAEAiB,QAAQA,CAACC,QAAqB,EAAA;QAC5B,4KAAOvD,IAAI,CAAC6B,YAAAA,AAAc,GAAEC,OAAO,IAAI;YACrC,MAAM0B,EAAE,GAAGC,KAAK,CAACC,IAAI,CAACH,QAAQ,CAAC;YAC/B,MAAMxB,KAAK,wKAAG5B,IAAI,CAAC6B,OAAAA,AAAS,EAAC,IAAI,CAACT,GAAG,EAAEO,OAAO,CAACG,OAAO,CAAC;YACvD,IAAIF,KAAK,KAAKG,SAAS,EAAE;gBACvB,QAAOlC,IAAI,CAACmC,6KAAAA,AAAW,EAACL,OAAO,CAACM,OAAO,CAAC;YAC1C;YACA,IAAIL,KAAK,CAACO,MAAM,GAAGkB,EAAE,CAAClB,MAAM,IAAI,IAAI,CAACd,iBAAiB,EAAE;qLACtDrB,IAAI,CAACwC,OAAAA,AAAS,EAAC,IAAI,CAACpB,GAAG,EAAE,CAAC;uBAAGQ,KAAK,EAAE;uBAAGyB,EAAE;iBAAC,EAAE1B,OAAO,CAACG,OAAO,CAAC;gBAC5D,4KAAOjC,IAAI,CAACqC,KAAAA,AAAO,EAAC,IAAI,CAAC;YAC3B;YACA,OAAQ,IAAI,CAACZ,QAAQ,CAACd,IAAI;gBACxB,qLAAKV,OAAO,CAACW,mBAAwB;oBAAE;wBACrC,wKAAOZ,IAAI,CAACiD,GAAK;oBACnB;gBACA,qLAAKhD,OAAO,CAACa,eAAoB;oBAAE;wBACjC,MAAM6C,QAAQ,GAAGH,EAAE,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAACpC,iBAAiB,GAAGO,KAAK,CAACO,MAAM,CAAC;6LACnEnC,IAAI,CAACwC,OAAAA,AAAS,EAAC,IAAI,CAACpB,GAAG,EAAE,CAAC;+BAAGQ,KAAK,EAAE;+BAAG4B,QAAQ;yBAAC,EAAE7B,OAAO,CAACG,OAAO,CAAC;wBAClE,4KAAOjC,IAAI,CAACqC,KAAAA,AAAO,EAAC,KAAK,CAAC;oBAC5B;gBACA,qLAAKpC,OAAO,CAACe,cAAmB;oBAAE;wBAChC,MAAM2C,QAAQ,GAAGH,EAAE,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAACpC,iBAAiB,GAAGO,KAAK,CAACO,MAAM,CAAC;wBACnE,MAAMuB,MAAM,GAAG9B,KAAK,CAACO,MAAM,GAAGqB,QAAQ,CAACrB,MAAM,GAAG,IAAI,CAACd,iBAAiB;wBACtE,MAAMsC,QAAQ,GAAG/B,KAAK,CAAC6B,KAAK,CAACC,MAAM,CAAC;6LACpC1D,IAAI,CAACwC,OAAAA,AAAS,EAAC,IAAI,CAACpB,GAAG,EAAE,CAAC;+BAAGuC,QAAQ,EAAE;+BAAGH,QAAQ;yBAAC,EAAE7B,OAAO,CAACG,OAAO,CAAC;wBACrE,4KAAOjC,IAAI,CAACqC,KAAAA,AAAO,EAAC,IAAI,CAAC;oBAC3B;YACF;QACF,CAAC,CAAC;IACJ;IAEA0B,IAAI,GAAA,WAAA,GAAe/D,IAAI,CAAC6B,iLAAAA,AAAc,GAAEC,OAAO,IAAI;QACjD,MAAMC,KAAK,wKAAG5B,IAAI,CAAC6B,OAAAA,AAAS,EAAC,IAAI,CAACT,GAAG,EAAEO,OAAO,CAACG,OAAO,CAAC;QACvD,IAAIF,KAAK,KAAKG,SAAS,EAAE;YACvB,4KAAOlC,IAAI,CAACmC,SAAAA,AAAW,EAACL,OAAO,CAACM,OAAO,CAAC;QAC1C;QACA,IAAIL,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;YACtB,OAAOtC,IAAI,CAACiD,oKAAK;QACnB;QACA,4KAAOjD,IAAI,CAACqC,KAAAA,AAAO,EAACN,KAAK,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAA;IAEFiC,UAAU,GAAA,WAAA,uKAA8BhE,IAAI,CAAC6B,aAAAA,AAAc,GAAEC,OAAO,IAAI;QACtE,MAAMC,KAAK,wKAAG5B,IAAI,CAAC6B,OAAAA,AAAS,EAAC,IAAI,CAACT,GAAG,EAAEO,OAAO,CAACG,OAAO,CAAC;QACvD,IAAIF,KAAK,KAAKG,SAAS,EAAE;YACvB,WAAOlC,IAAI,CAACmC,0KAAAA,AAAW,EAACL,OAAO,CAACM,OAAO,CAAC;QAC1C;QACA,4KAAOpC,IAAI,CAACqC,KAAAA,AAAO,MAACvC,MAAM,CAACmE,wJAAAA,AAAY,EAAClC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAA;IAEFmC,IAAI,GAAA,WAAA,wKAAelE,IAAI,CAAC6B,YAAAA,AAAc,GAAEC,OAAO,IAAI;QACjD,MAAMC,KAAK,IAAG5B,IAAI,CAAC6B,2KAAAA,AAAS,EAAC,IAAI,CAACT,GAAG,EAAEO,OAAO,CAACG,OAAO,CAAC;QACvD,IAAIF,KAAK,KAAKG,SAAS,EAAE;YACvB,WAAOlC,IAAI,CAACmC,0KAAAA,AAAW,EAACL,OAAO,CAACM,OAAO,CAAC;QAC1C;QACA,IAAIL,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;YACtB,wKAAOtC,IAAI,CAACiD,GAAK;QACnB;QACA,MAAMkB,QAAQ,GAAGpC,KAAK,CAACsB,KAAK,EAAG;QAC/BlD,IAAI,CAACwC,4KAAAA,AAAS,EAAC,IAAI,CAACpB,GAAG,EAAEQ,KAAK,EAAED,OAAO,CAACG,OAAO,CAAC;QAChD,4KAAOjC,IAAI,CAACqC,KAAAA,AAAO,EAAC8B,QAAQ,CAAC;IAC/B,CAAC,CAAC,CAAA;IAEFC,OAAO,GAAA,WAAA,wKAAsBpE,IAAI,CAAC6B,YAAAA,AAAc,GAAEC,OAAO,IAAI;QAC3D,MAAMC,KAAK,GAAG5B,IAAI,CAAC6B,4KAAAA,AAAS,EAAC,IAAI,CAACT,GAAG,EAAEO,OAAO,CAACG,OAAO,CAAC;QACvD,IAAIF,KAAK,KAAKG,SAAS,EAAE;YACvB,4KAAOlC,IAAI,CAACmC,SAAAA,AAAW,EAACL,OAAO,CAACM,OAAO,CAAC;QAC1C;SACAjC,IAAI,CAACwC,2KAAAA,AAAS,EAAC,IAAI,CAACpB,GAAG,EAAE,EAAE,EAAEO,OAAO,CAACG,OAAO,CAAC;QAC7C,WAAOjC,IAAI,CAACqC,sKAAAA,AAAO,EAACN,KAAK,CAAC;IAC5B,CAAC,CAAC,CAAA;IAEFsC,QAAQA,CAACC,GAAW,EAAA;QAClB,4KAAOtE,IAAI,CAAC6B,YAAAA,AAAc,GAAEC,OAAO,IAAI;YACrC,MAAMC,KAAK,GAAG5B,IAAI,CAAC6B,4KAAAA,AAAS,EAAC,IAAI,CAACT,GAAG,EAAEO,OAAO,CAACG,OAAO,CAAC;YACvD,IAAIF,KAAK,KAAKG,SAAS,EAAE;gBACvB,4KAAOlC,IAAI,CAACmC,SAAAA,AAAW,EAACL,OAAO,CAACM,OAAO,CAAC;YAC1C;YACA,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAG7E,KAAK,CAAC8E,uJAAAA,AAAO,qJAAC9E,KAAK,CAAC+E,YAAAA,AAAe,EAAC3C,KAAK,CAAC,EAAEuC,GAAG,CAAC;iLAC5EnE,IAAI,CAACwC,OAAAA,AAAS,EAAuB,IAAI,CAACpB,GAAG,EAAEkC,KAAK,CAACC,IAAI,CAACc,SAAS,CAAC,EAAE1C,OAAO,CAACG,OAAO,CAAC;YACtF,4KAAOjC,IAAI,CAACqC,KAAAA,AAAO,EAACoB,KAAK,CAACC,IAAI,CAACa,MAAM,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ;;AAIK,MAAMI,QAAQ,IAAIC,CAAU,IAAiC;IAClE,OAAOC,UAAU,CAACD,CAAC,CAAC,IAAIE,UAAU,CAACF,CAAC,CAAC;AACvC,CAAC;AAGM,MAAMC,UAAU,IAAID,CAAU,0JAAoC7E,cAAAA,AAAW,EAAC6E,CAAC,EAAEvE,cAAc,CAAC;AAGhG,MAAMyE,UAAU,IAAIF,CAAU,GAAoC7E,qKAAAA,AAAW,EAAC6E,CAAC,EAAEnE,cAAc,CAAC;AAGhG,MAAMsC,aAAa,IAAOgC,IAA6C,GAAoBA,IAAI,CAAChC,aAAa;AAG7G,MAAMiC,OAAO,IAAOxD,iBAAyB,GAClDyD,SAAS,CAAIzD,iBAAiB,EAAEd,YAAY,CAAC;AAGxC,MAAMiB,QAAQ,IAAOoD,IAA6C,IAAY;IACnF,OAAOA,IAAI,CAACpD,QAAQ,EAAE;AACxB,CAAC;AAGM,MAAMuD,QAAQ,IAAO1D,iBAAyB,GACnDyD,SAAS,CAAIzD,iBAAiB,EAAEX,QAAQ,CAAC;AAGpC,MAAM4B,OAAO,IAAOsC,IAA6C,GAAuBA,IAAI,CAACtC,OAAO;AAGpG,MAAMF,MAAM,IAAOwC,IAA6C,GAAuBA,IAAI,CAACxC,MAAM;AAGlG,MAAMM,UAAU,IAAOkC,IAA6C,GAAuBA,IAAI,CAAClC,UAAU;AAG1G,MAAMK,KAAK,GAAA,WAAA,yJAAGtD,OAAAA,AAAI,EAGvB,CAAC,EAAE,CAACmF,IAAI,EAAE5B,KAAK,GAAK4B,IAAI,CAAC7B,KAAK,CAACC,KAAK,CAAC,CAAC;AAGjC,MAAMG,QAAQ,GAAA,WAAA,yJAAG1D,OAAAA,AAAI,EAG1B,CAAC,EAAE,CAACmF,IAAI,EAAExB,QAAQ,GAAKwB,IAAI,CAACzB,QAAQ,CAACC,QAAQ,CAAC,CAAC;AAG1C,MAAMQ,IAAI,IAAOgB,IAAwB,GAAiBA,IAAI,CAAChB,IAAI;AAGnE,MAAMC,UAAU,GAAOe,IAAwB,IAAgCA,IAAI,CAACf,UAAU;AAG9F,MAAMmB,IAAI,IAAOJ,IAAwB,yJAC9ClF,OAAAA,AAAI,EAACkF,IAAI,CAACV,QAAQ,CAAC,CAAC,CAAC,MAAErE,IAAI,CAACwC,kKAAAA,AAAG,iJAAC9C,EAAE,CAAC0F,IAAI,CAAC,CAAC;AAGpC,MAAMC,IAAI,GAAA,WAAA,yJAAGzF,OAAAA,AAAI,EAGtB,CAAC,EAAE,CAACmF,IAAI,EAAEO,SAAS,GAAKC,QAAQ,CAACR,IAAI,EAAEO,SAAS,CAAC,CAAC;AAEpD,MAAMC,QAAQ,GAAGA,CAAIR,IAAwB,EAAEO,SAAuB,wKACpEtF,IAAI,CAACgD,KAAAA,AAAO,EACV+B,IAAI,CAACb,IAAI,GACRsB,CAAC,GAAKF,SAAS,CAACE,CAAC,CAAC,wKAAGxF,IAAI,CAACqC,KAAAA,AAAO,EAACmD,CAAC,CAAC,GAAGD,QAAQ,CAACR,IAAI,EAAEO,SAAS,CAAC,CAClE;AAGI,MAAM5C,QAAQ,IAAOqC,IAA6C,GAAoBA,IAAI,CAACrC,QAAQ;AAGnG,MAAMd,IAAI,GAAOmD,IAA6C,IAAsBA,IAAI,CAACnD,IAAI;AAG7F,MAAM6D,OAAO,IAAOjE,iBAAyB,GAClDyD,SAAS,CAAIzD,iBAAiB,EAAET,OAAO,CAAC;AAGnC,MAAMmD,IAAI,IAAOa,IAAwB,GAAiBA,IAAI,CAACb,IAAI;AAGnE,MAAME,OAAO,IAAOW,IAAwB,GAAwBA,IAAI,CAACX,OAAO;AAGhF,MAAMsB,WAAW,GAAA,WAAA,yJAAG9F,OAAAA,AAAI,EAI7B,CAAC,EACD,CAAImF,IAAwB,EAAEY,GAAW,EAAErB,GAAW,GACpDpE,GAAG,CAAC0F,0KAAAA,AAAO,EAAC,MAAK;QACf,MAAMC,aAAa,GAAGA,CACpBF,GAAW,EACXrB,GAAW,EACXwB,GAAmB,KACQ;YAC3B,IAAIxB,GAAG,GAAGqB,GAAG,EAAE;gBACb,4KAAO3F,IAAI,CAACqC,KAAAA,AAAO,EAACyD,GAAG,CAAC;YAC1B;YACA,6JAAOjG,OAAAA,AAAI,EACTkF,IAAI,CAACV,QAAQ,CAACC,GAAG,CAAC,uKAClBtE,IAAI,CAACgD,KAAAA,AAAO,GAAE+C,KAAK,IAAI;gBACrB,MAAMvB,SAAS,GAAGmB,GAAG,GAAGI,KAAK,CAACzD,MAAM;gBACpC,IAAIkC,SAAS,KAAK,CAAC,EAAE;oBACnB,6JAAO3E,OAAAA,AAAI,EACTkF,IAAI,CAACb,IAAI,uKACTlE,IAAI,CAACwC,CAAAA,AAAG,GAAEgD,CAAC,yJAAK3F,OAAAA,AAAI,EAACiG,GAAG,GAAEnG,KAAK,CAACqG,wJAAAA,AAAS,qJAACrG,KAAK,CAAC+E,YAAAA,AAAe,EAACqB,KAAK,CAAC,CAAC,MAAEpG,KAAK,CAACsG,kJAAAA,AAAM,EAACT,CAAC,CAAC,CAAC,CAAC,CAC3F;gBACH;gBACA,IAAIhB,SAAS,GAAG,CAAC,EAAE;oBACjB,6JAAO3E,OAAAA,AAAI,EACTkF,IAAI,CAACb,IAAI,GACTlE,IAAI,CAACgD,yKAAAA,AAAO,GAAEwC,CAAC,GACbK,aAAa,CACXrB,SAAS,GAAG,CAAC,EACbF,GAAG,GAAGyB,KAAK,CAACzD,MAAM,GAAG,CAAC,EACtBzC,6JAAAA,AAAI,EAACiG,GAAG,qJAAEnG,KAAK,CAACqG,MAAAA,AAAS,qJAACrG,KAAK,CAAC+E,YAAAA,AAAe,EAACqB,KAAK,CAAC,CAAC,GAAEpG,KAAK,CAACsG,qJAAAA,AAAM,EAACT,CAAC,CAAC,CAAC,CAC1E,CACF,CACF;gBACH;gBACA,4KAAOxF,IAAI,CAACqC,KAAAA,AAAO,wJAACxC,OAAAA,AAAI,EAACiG,GAAG,qJAAEnG,KAAK,CAACqG,MAAAA,AAAS,qJAACrG,KAAK,CAAC+E,YAAAA,AAAe,EAACqB,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/E,CAAC,CAAC,CACH;QACH,CAAC;QACD,QAAO/F,IAAI,CAACwC,qKAAAA,AAAG,EAACqD,aAAa,CAACF,GAAG,EAAErB,GAAG,qJAAE3E,KAAK,CAACuG,EAAAA,AAAK,EAAK,CAAC,GAAGC,CAAC,IAAK1C,KAAK,CAACC,IAAI,CAACyC,CAAC,CAAC,CAAC;IAClF,CAAC,CAAC,CACL;AAGM,MAAMC,KAAK,GAAA,WAAA,yJAAGxG,OAAAA,AAAI,EAGvB,CAAC,EAAE,CAACmF,IAAI,EAAEsB,CAAC,GAAKxG,6JAAAA,AAAI,EAACkF,IAAI,EAAEW,WAAW,CAACW,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC;AAGzC,MAAMhC,QAAQ,GAAA,WAAA,IAAGzE,4JAAAA,AAAI,EAG1B,CAAC,EAAE,CAACmF,IAAI,EAAET,GAAG,GAAKS,IAAI,CAACV,QAAQ,CAACC,GAAG,CAAC,CAAC;AAGhC,MAAMgC,SAAS,GAAGA,CAAA,GAAoCrB,SAAS,CAAIsB,MAAM,CAACC,gBAAgB,EAAE3F,QAAQ,CAAC;AAE5G,MAAMoE,SAAS,GAAGA,CAAIzD,iBAAyB,EAAEC,QAAwB,wKACvEzB,IAAI,CAACwC,CAAAA,AAAG,uKACNrC,IAAI,CAACsG,EAAAA,AAAI,EAAuB,EAAE,CAAC,GAClClF,GAAG,GAAK,IAAID,UAAU,CAAIC,GAAG,EAAEC,iBAAiB,EAAEC,QAAQ,CAAC,CAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1951, "column": 0}, "map": {"version": 3, "file": "tPubSub.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/stm/tPubSub.ts"], "sourcesContent": ["import * as RA from \"../../Array.js\"\nimport * as Effect from \"../../Effect.js\"\nimport { dual, identity, pipe } from \"../../Function.js\"\nimport * as HashSet from \"../../HashSet.js\"\nimport * as Option from \"../../Option.js\"\nimport type * as Scope from \"../../Scope.js\"\nimport type * as STM from \"../../STM.js\"\nimport type * as TPubSub from \"../../TPubSub.js\"\nimport type * as TQueue from \"../../TQueue.js\"\nimport type * as TRef from \"../../TRef.js\"\nimport * as core from \"./core.js\"\nimport * as OpCodes from \"./opCodes/strategy.js\"\nimport * as stm from \"./stm.js\"\nimport * as tQueue from \"./tQueue.js\"\nimport * as tRef from \"./tRef.js\"\n\n/** @internal */\nconst TPubSubSymbolKey = \"effect/TPubSub\"\n\n/** @internal */\nexport const TPubSubTypeId: TPubSub.TPubSubTypeId = Symbol.for(TPubSubSymbolKey) as TPubSub.TPubSubTypeId\n\nconst AbsentValue = Symbol.for(\"effect/TPubSub/AbsentValue\")\ntype AbsentValue = typeof AbsentValue\n\n/** @internal */\nexport interface Node<in out A> {\n  readonly head: A | AbsentValue\n  readonly subscribers: number\n  readonly tail: TRef.TRef<Node<A> | undefined>\n}\n\n/** @internal */\nexport const makeNode = <A>(\n  head: A | AbsentValue,\n  subscribers: number,\n  tail: TRef.TRef<Node<A> | undefined>\n): Node<A> => ({\n  head,\n  subscribers,\n  tail\n})\n\n/** @internal */\nclass TPubSubImpl<in out A> implements TPubSub.TPubSub<A> {\n  readonly [TPubSubTypeId] = {\n    _A: (_: any) => _\n  }\n  readonly [tQueue.TEnqueueTypeId] = tQueue.tEnqueueVariance\n  constructor(\n    readonly pubsubSize: TRef.TRef<number>,\n    readonly publisherHead: TRef.TRef<TRef.TRef<Node<A> | undefined>>,\n    readonly publisherTail: TRef.TRef<TRef.TRef<Node<A> | undefined> | undefined>,\n    readonly requestedCapacity: number,\n    readonly strategy: tQueue.TQueueStrategy,\n    readonly subscriberCount: TRef.TRef<number>,\n    readonly subscribers: TRef.TRef<HashSet.HashSet<TRef.TRef<TRef.TRef<Node<A>> | undefined>>>\n  ) {}\n\n  isShutdown: STM.STM<boolean> = core.effect<never, boolean>((journal) => {\n    const currentPublisherTail = tRef.unsafeGet(this.publisherTail, journal)\n    return currentPublisherTail === undefined\n  })\n\n  awaitShutdown: STM.STM<void> = core.flatMap(\n    this.isShutdown,\n    (isShutdown) => isShutdown ? stm.void : core.retry\n  )\n\n  capacity(): number {\n    return this.requestedCapacity\n  }\n\n  size: STM.STM<number> = core.withSTMRuntime((runtime) => {\n    const currentPublisherTail = tRef.unsafeGet(this.publisherTail, runtime.journal)\n    if (currentPublisherTail === undefined) {\n      return core.interruptAs(runtime.fiberId)\n    }\n    return core.succeed(tRef.unsafeGet(this.pubsubSize, runtime.journal))\n  })\n\n  isEmpty: STM.STM<boolean> = core.map(this.size, (size) => size === 0)\n\n  isFull: STM.STM<boolean> = core.map(this.size, (size) => size === this.capacity())\n\n  offer(value: A): STM.STM<boolean> {\n    return core.withSTMRuntime((runtime) => {\n      const currentPublisherTail = tRef.unsafeGet(this.publisherTail, runtime.journal)\n      if (currentPublisherTail === undefined) {\n        return core.interruptAs(runtime.fiberId)\n      }\n      const currentSubscriberCount = tRef.unsafeGet(this.subscriberCount, runtime.journal)\n      if (currentSubscriberCount === 0) {\n        return core.succeed(true)\n      }\n      const currentPubSubSize = tRef.unsafeGet(this.pubsubSize, runtime.journal)\n      if (currentPubSubSize < this.requestedCapacity) {\n        const updatedPublisherTail: TRef.TRef<Node<A> | undefined> = new tRef.TRefImpl<Node<A> | undefined>(void 0)\n        const updatedNode = makeNode(value, currentSubscriberCount, updatedPublisherTail)\n        tRef.unsafeSet<Node<A> | undefined>(currentPublisherTail, updatedNode, runtime.journal)\n        tRef.unsafeSet<TRef.TRef<Node<A> | undefined> | undefined>(\n          this.publisherTail,\n          updatedPublisherTail,\n          runtime.journal\n        )\n        tRef.unsafeSet(this.pubsubSize, currentPubSubSize + 1, runtime.journal)\n        return core.succeed(true)\n      }\n      switch (this.strategy._tag) {\n        case OpCodes.OP_BACKPRESSURE_STRATEGY: {\n          return core.retry\n        }\n        case OpCodes.OP_DROPPING_STRATEGY: {\n          return core.succeed(false)\n        }\n        case OpCodes.OP_SLIDING_STRATEGY: {\n          if (this.requestedCapacity > 0) {\n            let currentPublisherHead: TRef.TRef<Node<A> | undefined> = tRef.unsafeGet(\n              this.publisherHead,\n              runtime.journal\n            )\n            let loop = true\n            while (loop) {\n              const node = tRef.unsafeGet(currentPublisherHead, runtime.journal)\n              if (node === undefined) {\n                return core.retry\n              }\n              const head = node.head\n              const tail = node.tail\n              if (head !== AbsentValue) {\n                const updatedNode = makeNode<A>(AbsentValue, node.subscribers, node.tail as any)\n                tRef.unsafeSet<Node<A> | undefined>(\n                  currentPublisherHead as any,\n                  updatedNode as any,\n                  runtime.journal\n                )\n                tRef.unsafeSet(this.publisherHead, tail, runtime.journal)\n                loop = false\n              } else {\n                currentPublisherHead = tail\n              }\n            }\n          }\n          const updatedPublisherTail: TRef.TRef<Node<A> | undefined> = new tRef.TRefImpl<Node<A> | undefined>(void 0)\n          const updatedNode = makeNode(value, currentSubscriberCount, updatedPublisherTail)\n          tRef.unsafeSet<Node<A> | undefined>(currentPublisherTail, updatedNode, runtime.journal)\n          tRef.unsafeSet<TRef.TRef<Node<A> | undefined> | undefined>(\n            this.publisherTail,\n            updatedPublisherTail,\n            runtime.journal\n          )\n          return core.succeed(true)\n        }\n      }\n    })\n  }\n\n  offerAll(iterable: Iterable<A>): STM.STM<boolean> {\n    return core.map(\n      stm.forEach(iterable, (a) => this.offer(a)),\n      RA.every(identity)\n    )\n  }\n\n  shutdown: STM.STM<void> = core.effect<never, void>((journal) => {\n    const currentPublisherTail = tRef.unsafeGet(this.publisherTail, journal)\n    if (currentPublisherTail !== undefined) {\n      tRef.unsafeSet<TRef.TRef<Node<A> | undefined> | undefined>(this.publisherTail, void 0, journal)\n      const currentSubscribers = tRef.unsafeGet(this.subscribers, journal)\n      HashSet.forEach(currentSubscribers, (subscriber) => {\n        tRef.unsafeSet<TRef.TRef<Node<A>> | undefined>(subscriber, void 0, journal)\n      })\n      tRef.unsafeSet(this.subscribers, HashSet.empty<TRef.TRef<TRef.TRef<Node<A>> | undefined>>(), journal)\n    }\n  })\n}\n\n/** @internal */\nclass TPubSubSubscriptionImpl<in out A> implements TQueue.TDequeue<A> {\n  readonly [TPubSubTypeId]: TPubSub.TPubSubTypeId = TPubSubTypeId\n  readonly [tQueue.TDequeueTypeId] = tQueue.tDequeueVariance\n  constructor(\n    readonly pubsubSize: TRef.TRef<number>,\n    readonly publisherHead: TRef.TRef<TRef.TRef<Node<A> | undefined>>,\n    readonly requestedCapacity: number,\n    readonly subscriberHead: TRef.TRef<TRef.TRef<Node<A> | undefined> | undefined>,\n    readonly subscriberCount: TRef.TRef<number>,\n    readonly subscribers: TRef.TRef<HashSet.HashSet<TRef.TRef<TRef.TRef<Node<A>> | undefined>>>\n  ) {}\n\n  isShutdown: STM.STM<boolean> = core.effect<never, boolean>((journal) => {\n    const currentSubscriberHead = tRef.unsafeGet(this.subscriberHead, journal)\n    return currentSubscriberHead === undefined\n  })\n\n  awaitShutdown: STM.STM<void> = core.flatMap(\n    this.isShutdown,\n    (isShutdown) => isShutdown ? stm.void : core.retry\n  )\n\n  capacity(): number {\n    return this.requestedCapacity\n  }\n\n  size: STM.STM<number> = core.withSTMRuntime((runtime) => {\n    let currentSubscriberHead = tRef.unsafeGet(this.subscriberHead, runtime.journal)\n    if (currentSubscriberHead === undefined) {\n      return core.interruptAs(runtime.fiberId)\n    }\n    let loop = true\n    let size = 0\n    while (loop) {\n      const node = tRef.unsafeGet(currentSubscriberHead, runtime.journal)\n      if (node === undefined) {\n        loop = false\n      } else {\n        const head = node.head\n        const tail: TRef.TRef<Node<A> | undefined> = node.tail\n        if (head !== AbsentValue) {\n          size = size + 1\n          if (size >= Number.MAX_SAFE_INTEGER) {\n            loop = false\n          }\n        }\n        currentSubscriberHead = tail\n      }\n    }\n    return core.succeed(size)\n  })\n\n  isEmpty: STM.STM<boolean> = core.map(this.size, (size) => size === 0)\n\n  isFull: STM.STM<boolean> = core.map(this.size, (size) => size === this.capacity())\n\n  peek: STM.STM<A> = core.withSTMRuntime((runtime) => {\n    let currentSubscriberHead = tRef.unsafeGet(this.subscriberHead, runtime.journal)\n    if (currentSubscriberHead === undefined) {\n      return core.interruptAs(runtime.fiberId)\n    }\n    let value: A | AbsentValue = AbsentValue\n    let loop = true\n    while (loop) {\n      const node = tRef.unsafeGet(currentSubscriberHead, runtime.journal)\n      if (node === undefined) {\n        return core.retry\n      }\n      const head = node.head\n      const tail: TRef.TRef<Node<A> | undefined> = node.tail\n      if (head !== AbsentValue) {\n        value = head\n        loop = false\n      } else {\n        currentSubscriberHead = tail\n      }\n    }\n    return core.succeed(value as A)\n  })\n\n  peekOption: STM.STM<Option.Option<A>> = core.withSTMRuntime((runtime) => {\n    let currentSubscriberHead = tRef.unsafeGet(this.subscriberHead, runtime.journal)\n    if (currentSubscriberHead === undefined) {\n      return core.interruptAs(runtime.fiberId)\n    }\n    let value: Option.Option<A> = Option.none()\n    let loop = true\n    while (loop) {\n      const node = tRef.unsafeGet(currentSubscriberHead, runtime.journal)\n      if (node === undefined) {\n        value = Option.none()\n        loop = false\n      } else {\n        const head = node.head\n        const tail: TRef.TRef<Node<A> | undefined> = node.tail\n        if (head !== AbsentValue) {\n          value = Option.some(head)\n          loop = false\n        } else {\n          currentSubscriberHead = tail\n        }\n      }\n    }\n    return core.succeed(value)\n  })\n\n  shutdown: STM.STM<void> = core.effect<never, void>((journal) => {\n    let currentSubscriberHead = tRef.unsafeGet(this.subscriberHead, journal)\n    if (currentSubscriberHead !== undefined) {\n      tRef.unsafeSet<TRef.TRef<Node<A> | undefined> | undefined>(this.subscriberHead, void 0, journal)\n      let loop = true\n      while (loop) {\n        const node = tRef.unsafeGet(currentSubscriberHead, journal)\n        if (node === undefined) {\n          loop = false\n        } else {\n          const head = node.head\n          const tail: TRef.TRef<Node<A> | undefined> = node.tail\n          if (head !== AbsentValue) {\n            const subscribers = node.subscribers\n            if (subscribers === 1) {\n              const size = tRef.unsafeGet(this.pubsubSize, journal)\n              const updatedNode = makeNode<A>(AbsentValue, 0, tail)\n              tRef.unsafeSet<Node<A> | undefined>(currentSubscriberHead, updatedNode, journal)\n              tRef.unsafeSet(this.publisherHead, tail as any, journal)\n              tRef.unsafeSet(this.pubsubSize, size - 1, journal)\n            } else {\n              const updatedNode = makeNode(head, subscribers - 1, tail)\n              tRef.unsafeSet<Node<A> | undefined>(currentSubscriberHead, updatedNode, journal)\n            }\n          }\n          currentSubscriberHead = tail\n        }\n      }\n      const currentSubscriberCount = tRef.unsafeGet(this.subscriberCount, journal)\n      tRef.unsafeSet(this.subscriberCount, currentSubscriberCount - 1, journal)\n      tRef.unsafeSet(\n        this.subscribers,\n        HashSet.remove(\n          tRef.unsafeGet(this.subscribers, journal),\n          this.subscriberHead as any\n        ),\n        journal\n      )\n    }\n  })\n\n  take: STM.STM<A> = core.withSTMRuntime((runtime) => {\n    let currentSubscriberHead = tRef.unsafeGet(this.subscriberHead, runtime.journal)\n    if (currentSubscriberHead === undefined) {\n      return core.interruptAs(runtime.fiberId)\n    }\n    let value: A | AbsentValue = AbsentValue\n    let loop = true\n    while (loop) {\n      const node = tRef.unsafeGet(currentSubscriberHead, runtime.journal)\n      if (node === undefined) {\n        return core.retry\n      }\n      const head = node.head\n      const tail: TRef.TRef<Node<A> | undefined> = node.tail\n      if (head !== AbsentValue) {\n        const subscribers = node.subscribers\n        if (subscribers === 1) {\n          const size = tRef.unsafeGet(this.pubsubSize, runtime.journal)\n          const updatedNode = makeNode<A>(AbsentValue, 0, tail)\n          tRef.unsafeSet<Node<A> | undefined>(currentSubscriberHead, updatedNode, runtime.journal)\n          tRef.unsafeSet(this.publisherHead, tail as any, runtime.journal)\n          tRef.unsafeSet(this.pubsubSize, size - 1, runtime.journal)\n        } else {\n          const updatedNode = makeNode(head, subscribers - 1, tail)\n          tRef.unsafeSet<Node<A> | undefined>(currentSubscriberHead, updatedNode, runtime.journal)\n        }\n        tRef.unsafeSet<TRef.TRef<Node<A> | undefined> | undefined>(\n          this.subscriberHead,\n          tail,\n          runtime.journal\n        )\n        value = head\n        loop = false\n      } else {\n        currentSubscriberHead = tail\n      }\n    }\n    return core.succeed(value as A)\n  })\n\n  takeAll: STM.STM<Array<A>> = this.takeUpTo(Number.POSITIVE_INFINITY)\n\n  takeUpTo(max: number): STM.STM<Array<A>> {\n    return core.withSTMRuntime((runtime) => {\n      let currentSubscriberHead = tRef.unsafeGet(this.subscriberHead, runtime.journal)\n      if (currentSubscriberHead === undefined) {\n        return core.interruptAs(runtime.fiberId)\n      }\n      const builder: Array<A> = []\n      let n = 0\n      while (n !== max) {\n        const node = tRef.unsafeGet(currentSubscriberHead, runtime.journal)\n        if (node === undefined) {\n          n = max\n        } else {\n          const head = node.head\n          const tail: TRef.TRef<Node<A> | undefined> = node.tail\n          if (head !== AbsentValue) {\n            const subscribers = node.subscribers\n            if (subscribers === 1) {\n              const size = tRef.unsafeGet(this.pubsubSize, runtime.journal)\n              const updatedNode = makeNode<A>(AbsentValue, 0, tail)\n              tRef.unsafeSet<Node<A> | undefined>(currentSubscriberHead, updatedNode, runtime.journal)\n              tRef.unsafeSet(this.publisherHead, tail as any, runtime.journal)\n              tRef.unsafeSet(this.pubsubSize, size - 1, runtime.journal)\n            } else {\n              const updatedNode = makeNode(head, subscribers - 1, tail)\n              tRef.unsafeSet<Node<A> | undefined>(currentSubscriberHead, updatedNode, runtime.journal)\n            }\n            builder.push(head)\n            n = n + 1\n          }\n          currentSubscriberHead = tail\n        }\n      }\n      tRef.unsafeSet<TRef.TRef<Node<A> | undefined> | undefined>(\n        this.subscriberHead,\n        currentSubscriberHead,\n        runtime.journal\n      )\n      return core.succeed(builder)\n    })\n  }\n}\n\n/** @internal */\nconst makeTPubSub = <A>(\n  requestedCapacity: number,\n  strategy: tQueue.TQueueStrategy\n): STM.STM<TPubSub.TPubSub<A>> =>\n  pipe(\n    stm.all([\n      tRef.make<Node<A> | undefined>(void 0),\n      tRef.make(0)\n    ]),\n    core.flatMap(([empty, pubsubSize]) =>\n      pipe(\n        stm.all([\n          tRef.make(empty),\n          tRef.make(empty),\n          tRef.make(0),\n          tRef.make(HashSet.empty())\n        ]),\n        core.map(([publisherHead, publisherTail, subscriberCount, subscribers]) =>\n          new TPubSubImpl(\n            pubsubSize,\n            publisherHead,\n            publisherTail as any,\n            requestedCapacity,\n            strategy,\n            subscriberCount,\n            subscribers as any\n          )\n        )\n      )\n    )\n  )\n\nconst makeSubscription = <A>(\n  pubsubSize: TRef.TRef<number>,\n  publisherHead: TRef.TRef<TRef.TRef<Node<A> | undefined>>,\n  publisherTail: TRef.TRef<TRef.TRef<Node<A> | undefined> | undefined>,\n  requestedCapacity: number,\n  subscriberCount: TRef.TRef<number>,\n  subscribers: TRef.TRef<HashSet.HashSet<TRef.TRef<TRef.TRef<Node<A>> | undefined>>>\n): STM.STM<TQueue.TDequeue<A>> =>\n  pipe(\n    tRef.get(publisherTail),\n    core.flatMap((currentPublisherTail) =>\n      pipe(\n        stm.all([\n          tRef.make(currentPublisherTail),\n          tRef.get(subscriberCount),\n          tRef.get(subscribers)\n        ]),\n        stm.tap(([_, currentSubscriberCount]) =>\n          pipe(\n            subscriberCount,\n            tRef.set(currentSubscriberCount + 1)\n          )\n        ),\n        stm.tap(([subscriberHead, _, currentSubscribers]) =>\n          pipe(\n            subscribers as any,\n            tRef.set(pipe(currentSubscribers as any, HashSet.add(subscriberHead)))\n          )\n        ),\n        core.map(([subscriberHead]) =>\n          new TPubSubSubscriptionImpl(\n            pubsubSize,\n            publisherHead,\n            requestedCapacity,\n            subscriberHead as any,\n            subscriberCount,\n            subscribers\n          )\n        )\n      )\n    )\n  )\n\n/** @internal */\nexport const awaitShutdown = <A>(self: TPubSub.TPubSub<A>): STM.STM<void> => self.awaitShutdown\n\n/** @internal */\nexport const bounded = <A>(requestedCapacity: number): STM.STM<TPubSub.TPubSub<A>> =>\n  makeTPubSub<A>(requestedCapacity, tQueue.BackPressure)\n\n/** @internal */\nexport const capacity = <A>(self: TPubSub.TPubSub<A>): number => self.capacity()\n\n/** @internal */\nexport const dropping = <A>(requestedCapacity: number): STM.STM<TPubSub.TPubSub<A>> =>\n  makeTPubSub<A>(requestedCapacity, tQueue.Dropping)\n\n/** @internal */\nexport const isEmpty = <A>(self: TPubSub.TPubSub<A>): STM.STM<boolean> => self.isEmpty\n\n/** @internal */\nexport const isFull = <A>(self: TPubSub.TPubSub<A>): STM.STM<boolean> => self.isFull\n\n/** @internal */\nexport const isShutdown = <A>(self: TPubSub.TPubSub<A>): STM.STM<boolean> => self.isShutdown\n\n/** @internal */\nexport const publish = dual<\n  <A>(value: A) => (self: TPubSub.TPubSub<A>) => STM.STM<boolean>,\n  <A>(self: TPubSub.TPubSub<A>, value: A) => STM.STM<boolean>\n>(2, (self, value) => self.offer(value))\n\n/** @internal */\nexport const publishAll = dual<\n  <A>(iterable: Iterable<A>) => (self: TPubSub.TPubSub<A>) => STM.STM<boolean>,\n  <A>(self: TPubSub.TPubSub<A>, iterable: Iterable<A>) => STM.STM<boolean>\n>(2, (self, iterable) => self.offerAll(iterable))\n\n/** @internal */\nexport const size = <A>(self: TPubSub.TPubSub<A>): STM.STM<number> => self.size\n\n/** @internal */\nexport const shutdown = <A>(self: TPubSub.TPubSub<A>): STM.STM<void> => self.shutdown\n\n/** @internal */\nexport const sliding = <A>(requestedCapacity: number): STM.STM<TPubSub.TPubSub<A>> =>\n  makeTPubSub<A>(requestedCapacity, tQueue.Sliding)\n\n/** @internal */\nexport const subscribe = <A>(self: TPubSub.TPubSub<A>): STM.STM<TQueue.TDequeue<A>> =>\n  makeSubscription(\n    self.pubsubSize,\n    self.publisherHead,\n    self.publisherTail,\n    self.requestedCapacity,\n    self.subscriberCount,\n    self.subscribers\n  )\n\n/** @internal */\nexport const subscribeScoped = <A>(self: TPubSub.TPubSub<A>): Effect.Effect<TQueue.TDequeue<A>, never, Scope.Scope> =>\n  Effect.acquireRelease(\n    subscribe(self),\n    (dequeue) => tQueue.shutdown(dequeue)\n  )\n\n/** @internal */\nexport const unbounded = <A>(): STM.STM<TPubSub.TPubSub<A>> => makeTPubSub<A>(Number.MAX_SAFE_INTEGER, tQueue.Dropping)\n"], "names": ["RA", "Effect", "dual", "identity", "pipe", "HashSet", "Option", "core", "OpCodes", "stm", "tQueue", "tRef", "TPubSubSymbolKey", "TPubSubTypeId", "Symbol", "for", "AbsentValue", "makeNode", "head", "subscribers", "tail", "TPubSubImpl", "pubsubSize", "publisherHead", "publisherTail", "requestedCapacity", "strategy", "subscriberCount", "_A", "_", "TEnqueueTypeId", "tEnqueueVariance", "constructor", "isShutdown", "effect", "journal", "currentPublisherTail", "unsafeGet", "undefined", "await<PERSON><PERSON><PERSON>down", "flatMap", "void", "retry", "capacity", "size", "withSTMRuntime", "runtime", "interruptAs", "fiberId", "succeed", "isEmpty", "map", "isFull", "offer", "value", "currentSubscriberCount", "currentPubSubSize", "updatedPublisherTail", "TRefImpl", "updatedNode", "unsafeSet", "_tag", "OP_BACKPRESSURE_STRATEGY", "OP_DROPPING_STRATEGY", "OP_SLIDING_STRATEGY", "currentPublisherHead", "loop", "node", "offerAll", "iterable", "for<PERSON>ach", "a", "every", "shutdown", "currentSubscribers", "subscriber", "empty", "TPubSubSubscriptionImpl", "subscriberHead", "TDequeueTypeId", "tDequeueVariance", "currentSubscriberHead", "Number", "MAX_SAFE_INTEGER", "peek", "peekOption", "none", "some", "remove", "take", "takeAll", "takeUpTo", "POSITIVE_INFINITY", "max", "builder", "n", "push", "makeTPubSub", "all", "make", "makeSubscription", "get", "tap", "set", "add", "self", "bounded", "BackPressure", "dropping", "Dropping", "publish", "publishAll", "sliding", "Sliding", "subscribe", "subscribeScoped", "acquireRelease", "dequeue", "unbounded"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,EAAE,MAAM,gBAAgB;AACpC,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,SAASC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,mBAAmB;AACxD,OAAO,KAAKC,OAAO,MAAM,kBAAkB;AAC3C,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AAMzC,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,GAAG,MAAM,UAAU;AAC/B,OAAO,KAAKC,MAAM,MAAM,aAAa;AACrC,OAAO,KAAKC,IAAI,MAAM,WAAW;;;;;;;;;;;AAEjC,cAAA,GACA,MAAMC,gBAAgB,GAAG,gBAAgB;AAGlC,MAAMC,aAAa,GAAA,WAAA,GAA0BC,MAAM,CAACC,GAAG,CAACH,gBAAgB,CAA0B;AAEzG,MAAMI,WAAW,GAAA,WAAA,GAAGF,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC;AAWrD,MAAME,QAAQ,GAAGA,CACtBC,IAAqB,EACrBC,WAAmB,EACnBC,IAAoC,GAAA,CACvB;QACbF,IAAI;QACJC,WAAW;QACXC;KACD,CAAC;AAEF,cAAA,GACA,MAAMC,WAAW;IAMJC,UAAA,CAAA;IACAC,aAAA,CAAA;IACAC,aAAA,CAAA;IACAC,iBAAA,CAAA;IACAC,QAAA,CAAA;IACAC,eAAA,CAAA;IACAR,WAAA,CAAA;IAXF,CAACN,aAAa,CAAA,GAAI;QACzBe,EAAE,GAAGC,CAAM,GAAKA;KACjB,CAAA;IACQ,oKAACnB,MAAM,CAACoB,UAAc,CAAA,sKAAIpB,MAAM,CAACqB,YAAgB,CAAA;IAC1DC,YACWV,UAA6B,EAC7BC,aAAwD,EACxDC,aAAoE,EACpEC,iBAAyB,EACzBC,QAA+B,EAC/BC,eAAkC,EAClCR,WAAkF,CAAA;QANlF,IAAA,CAAAG,UAAU,GAAVA,UAAU;QACV,IAAA,CAAAC,aAAa,GAAbA,aAAa;QACb,IAAA,CAAAC,aAAa,GAAbA,aAAa;QACb,IAAA,CAAAC,iBAAiB,GAAjBA,iBAAiB;QACjB,IAAA,CAAAC,QAAQ,GAARA,QAAQ;QACR,IAAA,CAAAC,eAAe,GAAfA,eAAe;QACf,IAAA,CAAAR,WAAW,GAAXA,WAAW;IACnB;IAEHc,UAAU,GAAA,WAAA,wKAAqB1B,IAAI,CAAC2B,IAAAA,AAAM,GAAkBC,OAAO,IAAI;QACrE,MAAMC,oBAAoB,OAAGzB,IAAI,CAAC0B,wKAAAA,AAAS,EAAC,IAAI,CAACb,aAAa,EAAEW,OAAO,CAAC;QACxE,OAAOC,oBAAoB,KAAKE,SAAS;IAC3C,CAAC,CAAC,CAAA;IAEFC,aAAa,GAAA,WAAA,wKAAkBhC,IAAI,CAACiC,KAAAA,AAAO,EACzC,IAAI,CAACP,UAAU,GACdA,UAAU,GAAKA,UAAU,mKAAGxB,GAAG,CAACgC,GAAI,GAAGlC,IAAI,CAACmC,oKAAK,CACnD,CAAA;IAEDC,QAAQA,CAAA,EAAA;QACN,OAAO,IAAI,CAAClB,iBAAiB;IAC/B;IAEAmB,IAAI,GAAA,WAAA,wKAAoBrC,IAAI,CAACsC,YAAAA,AAAc,GAAEC,OAAO,IAAI;QACtD,MAAMV,oBAAoB,wKAAGzB,IAAI,CAAC0B,OAAAA,AAAS,EAAC,IAAI,CAACb,aAAa,EAAEsB,OAAO,CAACX,OAAO,CAAC;QAChF,IAAIC,oBAAoB,KAAKE,SAAS,EAAE;YACtC,OAAO/B,IAAI,CAACwC,8KAAAA,AAAW,EAACD,OAAO,CAACE,OAAO,CAAC;QAC1C;QACA,4KAAOzC,IAAI,CAAC0C,KAAAA,AAAO,uKAACtC,IAAI,CAAC0B,OAAAA,AAAS,EAAC,IAAI,CAACf,UAAU,EAAEwB,OAAO,CAACX,OAAO,CAAC,CAAC;IACvE,CAAC,CAAC,CAAA;IAEFe,OAAO,GAAA,WAAA,wKAAqB3C,IAAI,CAAC4C,CAAAA,AAAG,EAAC,IAAI,CAACP,IAAI,GAAGA,IAAI,GAAKA,IAAI,KAAK,CAAC,CAAC,CAAA;IAErEQ,MAAM,GAAA,WAAA,wKAAqB7C,IAAI,CAAC4C,CAAAA,AAAG,EAAC,IAAI,CAACP,IAAI,GAAGA,IAAI,GAAKA,IAAI,KAAK,IAAI,CAACD,QAAQ,EAAE,CAAC,CAAA;IAElFU,KAAKA,CAACC,KAAQ,EAAA;QACZ,4KAAO/C,IAAI,CAACsC,YAAAA,AAAc,GAAEC,OAAO,IAAI;YACrC,MAAMV,oBAAoB,wKAAGzB,IAAI,CAAC0B,OAAAA,AAAS,EAAC,IAAI,CAACb,aAAa,EAAEsB,OAAO,CAACX,OAAO,CAAC;YAChF,IAAIC,oBAAoB,KAAKE,SAAS,EAAE;gBACtC,OAAO/B,IAAI,CAACwC,8KAAAA,AAAW,EAACD,OAAO,CAACE,OAAO,CAAC;YAC1C;YACA,MAAMO,sBAAsB,wKAAG5C,IAAI,CAAC0B,OAAAA,AAAS,EAAC,IAAI,CAACV,eAAe,EAAEmB,OAAO,CAACX,OAAO,CAAC;YACpF,IAAIoB,sBAAsB,KAAK,CAAC,EAAE;gBAChC,OAAOhD,IAAI,CAAC0C,0KAAAA,AAAO,EAAC,IAAI,CAAC;YAC3B;YACA,MAAMO,iBAAiB,wKAAG7C,IAAI,CAAC0B,OAAAA,AAAS,EAAC,IAAI,CAACf,UAAU,EAAEwB,OAAO,CAACX,OAAO,CAAC;YAC1E,IAAIqB,iBAAiB,GAAG,IAAI,CAAC/B,iBAAiB,EAAE;gBAC9C,MAAMgC,oBAAoB,GAAmC,qKAAI9C,IAAI,CAAC+C,MAAQ,CAAsB,KAAK,CAAC,CAAC;gBAC3G,MAAMC,WAAW,GAAG1C,QAAQ,CAACqC,KAAK,EAAEC,sBAAsB,EAAEE,oBAAoB,CAAC;gBACjF9C,IAAI,CAACiD,4KAAAA,AAAS,EAAsBxB,oBAAoB,EAAEuB,WAAW,EAAEb,OAAO,CAACX,OAAO,CAAC;oLACvFxB,IAAI,CAACiD,QAAAA,AAAS,EACZ,IAAI,CAACpC,aAAa,EAClBiC,oBAAoB,EACpBX,OAAO,CAACX,OAAO,CAChB;qLACDxB,IAAI,CAACiD,OAAAA,AAAS,EAAC,IAAI,CAACtC,UAAU,EAAEkC,iBAAiB,GAAG,CAAC,EAAEV,OAAO,CAACX,OAAO,CAAC;gBACvE,4KAAO5B,IAAI,CAAC0C,KAAAA,AAAO,EAAC,IAAI,CAAC;YAC3B;YACA,OAAQ,IAAI,CAACvB,QAAQ,CAACmC,IAAI;gBACxB,qLAAKrD,OAAO,CAACsD,mBAAwB;oBAAE;wBACrC,OAAOvD,IAAI,CAACmC,oKAAK;oBACnB;gBACA,qLAAKlC,OAAO,CAACuD,eAAoB;oBAAE;wBACjC,4KAAOxD,IAAI,CAAC0C,KAAAA,AAAO,EAAC,KAAK,CAAC;oBAC5B;gBACA,qLAAKzC,OAAO,CAACwD,cAAmB;oBAAE;wBAChC,IAAI,IAAI,CAACvC,iBAAiB,GAAG,CAAC,EAAE;4BAC9B,IAAIwC,oBAAoB,wKAAmCtD,IAAI,CAAC0B,OAAAA,AAAS,EACvE,IAAI,CAACd,aAAa,EAClBuB,OAAO,CAACX,OAAO,CAChB;4BACD,IAAI+B,IAAI,GAAG,IAAI;4BACf,MAAOA,IAAI,CAAE;gCACX,MAAMC,IAAI,wKAAGxD,IAAI,CAAC0B,OAAAA,AAAS,EAAC4B,oBAAoB,EAAEnB,OAAO,CAACX,OAAO,CAAC;gCAClE,IAAIgC,IAAI,KAAK7B,SAAS,EAAE;oCACtB,wKAAO/B,IAAI,CAACmC,GAAK;gCACnB;gCACA,MAAMxB,IAAI,GAAGiD,IAAI,CAACjD,IAAI;gCACtB,MAAME,IAAI,GAAG+C,IAAI,CAAC/C,IAAI;gCACtB,IAAIF,IAAI,KAAKF,WAAW,EAAE;oCACxB,MAAM2C,WAAW,GAAG1C,QAAQ,CAAID,WAAW,EAAEmD,IAAI,CAAChD,WAAW,EAAEgD,IAAI,CAAC/C,IAAW,CAAC;yMAChFT,IAAI,CAACiD,OAAAA,AAAS,EACZK,oBAA2B,EAC3BN,WAAkB,EAClBb,OAAO,CAACX,OAAO,CAChB;yMACDxB,IAAI,CAACiD,OAAAA,AAAS,EAAC,IAAI,CAACrC,aAAa,EAAEH,IAAI,EAAE0B,OAAO,CAACX,OAAO,CAAC;oCACzD+B,IAAI,GAAG,KAAK;gCACd,CAAC,MAAM;oCACLD,oBAAoB,GAAG7C,IAAI;gCAC7B;4BACF;wBACF;wBACA,MAAMqC,oBAAoB,GAAmC,qKAAI9C,IAAI,CAAC+C,MAAQ,CAAsB,KAAK,CAAC,CAAC;wBAC3G,MAAMC,WAAW,GAAG1C,QAAQ,CAACqC,KAAK,EAAEC,sBAAsB,EAAEE,oBAAoB,CAAC;6LACjF9C,IAAI,CAACiD,OAAAA,AAAS,EAAsBxB,oBAAoB,EAAEuB,WAAW,EAAEb,OAAO,CAACX,OAAO,CAAC;4BACvFxB,IAAI,CAACiD,wKAAAA,AAAS,EACZ,IAAI,CAACpC,aAAa,EAClBiC,oBAAoB,EACpBX,OAAO,CAACX,OAAO,CAChB;wBACD,4KAAO5B,IAAI,CAAC0C,KAAAA,AAAO,EAAC,IAAI,CAAC;oBAC3B;YACF;QACF,CAAC,CAAC;IACJ;IAEAmB,QAAQA,CAACC,QAAqB,EAAA;QAC5B,4KAAO9D,IAAI,CAAC4C,CAAAA,AAAG,sKACb1C,GAAG,CAAC6D,MAAAA,AAAO,EAACD,QAAQ,GAAGE,CAAC,GAAK,IAAI,CAAClB,KAAK,CAACkB,CAAC,CAAC,CAAC,qJAC3CvE,EAAE,CAACwE,KAAAA,AAAK,oJAACrE,WAAQ,CAAC,CACnB;IACH;IAEAsE,QAAQ,GAAA,WAAA,IAAkBlE,IAAI,CAAC2B,wKAAAA,AAAM,GAAeC,OAAO,IAAI;QAC7D,MAAMC,oBAAoB,wKAAGzB,IAAI,CAAC0B,OAAAA,AAAS,EAAC,IAAI,CAACb,aAAa,EAAEW,OAAO,CAAC;QACxE,IAAIC,oBAAoB,KAAKE,SAAS,EAAE;iLACtC3B,IAAI,CAACiD,OAAAA,AAAS,EAA6C,IAAI,CAACpC,aAAa,EAAE,KAAK,CAAC,EAAEW,OAAO,CAAC;YAC/F,MAAMuC,kBAAkB,wKAAG/D,IAAI,CAAC0B,OAAAA,AAAS,EAAC,IAAI,CAAClB,WAAW,EAAEgB,OAAO,CAAC;gBACpE9B,OAAO,CAACiE,mJAAAA,AAAO,EAACI,kBAAkB,GAAGC,UAAU,IAAI;qLACjDhE,IAAI,CAACiD,OAAS,AAATA,EAA0Ce,UAAU,EAAE,KAAK,CAAC,EAAExC,OAAO,CAAC;YAC7E,CAAC,CAAC;iLACFxB,IAAI,CAACiD,OAAAA,AAAS,EAAC,IAAI,CAACzC,WAAW,MAAEd,OAAO,CAACuE,iJAAK,AAALA,EAAkD,GAAEzC,OAAO,CAAC;QACvG;IACF,CAAC,CAAC,CAAA;;AAGJ,cAAA,GACA,MAAM0C,uBAAuB;IAIhBvD,UAAA,CAAA;IACAC,aAAA,CAAA;IACAE,iBAAA,CAAA;IACAqD,cAAA,CAAA;IACAnD,eAAA,CAAA;IACAR,WAAA,CAAA;IARF,CAACN,aAAa,CAAA,GAA2BA,aAAa,CAAA;IACtD,oKAACH,MAAM,CAACqE,UAAc,CAAA,GAAIrE,MAAM,CAACsE,+KAAgB,CAAA;IAC1DhD,YACWV,UAA6B,EAC7BC,aAAwD,EACxDE,iBAAyB,EACzBqD,cAAqE,EACrEnD,eAAkC,EAClCR,WAAkF,CAAA;QALlF,IAAA,CAAAG,UAAU,GAAVA,UAAU;QACV,IAAA,CAAAC,aAAa,GAAbA,aAAa;QACb,IAAA,CAAAE,iBAAiB,GAAjBA,iBAAiB;QACjB,IAAA,CAAAqD,cAAc,GAAdA,cAAc;QACd,IAAA,CAAAnD,eAAe,GAAfA,eAAe;QACf,IAAA,CAAAR,WAAW,GAAXA,WAAW;IACnB;IAEHc,UAAU,GAAA,WAAA,OAAqB1B,IAAI,CAAC2B,qKAAAA,AAAM,GAAkBC,OAAO,IAAI;QACrE,MAAM8C,qBAAqB,wKAAGtE,IAAI,CAAC0B,OAAAA,AAAS,EAAC,IAAI,CAACyC,cAAc,EAAE3C,OAAO,CAAC;QAC1E,OAAO8C,qBAAqB,KAAK3C,SAAS;IAC5C,CAAC,CAAC,CAAA;IAEFC,aAAa,GAAA,WAAA,wKAAkBhC,IAAI,CAACiC,KAAAA,AAAO,EACzC,IAAI,CAACP,UAAU,GACdA,UAAU,GAAKA,UAAU,mKAAGxB,GAAG,CAACgC,GAAI,GAAGlC,IAAI,CAACmC,oKAAK,CACnD,CAAA;IAEDC,QAAQA,CAAA,EAAA;QACN,OAAO,IAAI,CAAClB,iBAAiB;IAC/B;IAEAmB,IAAI,GAAA,WAAA,GAAoBrC,IAAI,CAACsC,iLAAAA,AAAc,GAAEC,OAAO,IAAI;QACtD,IAAImC,qBAAqB,wKAAGtE,IAAI,CAAC0B,OAAAA,AAAS,EAAC,IAAI,CAACyC,cAAc,EAAEhC,OAAO,CAACX,OAAO,CAAC;QAChF,IAAI8C,qBAAqB,KAAK3C,SAAS,EAAE;YACvC,4KAAO/B,IAAI,CAACwC,SAAAA,AAAW,EAACD,OAAO,CAACE,OAAO,CAAC;QAC1C;QACA,IAAIkB,IAAI,GAAG,IAAI;QACf,IAAItB,IAAI,GAAG,CAAC;QACZ,MAAOsB,IAAI,CAAE;YACX,MAAMC,IAAI,wKAAGxD,IAAI,CAAC0B,OAAAA,AAAS,EAAC4C,qBAAqB,EAAEnC,OAAO,CAACX,OAAO,CAAC;YACnE,IAAIgC,IAAI,KAAK7B,SAAS,EAAE;gBACtB4B,IAAI,GAAG,KAAK;YACd,CAAC,MAAM;gBACL,MAAMhD,IAAI,GAAGiD,IAAI,CAACjD,IAAI;gBACtB,MAAME,IAAI,GAAmC+C,IAAI,CAAC/C,IAAI;gBACtD,IAAIF,IAAI,KAAKF,WAAW,EAAE;oBACxB4B,IAAI,GAAGA,IAAI,GAAG,CAAC;oBACf,IAAIA,IAAI,IAAIsC,MAAM,CAACC,gBAAgB,EAAE;wBACnCjB,IAAI,GAAG,KAAK;oBACd;gBACF;gBACAe,qBAAqB,GAAG7D,IAAI;YAC9B;QACF;QACA,4KAAOb,IAAI,CAAC0C,KAAAA,AAAO,EAACL,IAAI,CAAC;IAC3B,CAAC,CAAC,CAAA;IAEFM,OAAO,GAAA,WAAA,wKAAqB3C,IAAI,CAAC4C,CAAAA,AAAG,EAAC,IAAI,CAACP,IAAI,GAAGA,IAAI,GAAKA,IAAI,KAAK,CAAC,CAAC,CAAA;IAErEQ,MAAM,GAAA,WAAA,wKAAqB7C,IAAI,CAAC4C,CAAAA,AAAG,EAAC,IAAI,CAACP,IAAI,GAAGA,IAAI,GAAKA,IAAI,KAAK,IAAI,CAACD,QAAQ,EAAE,CAAC,CAAA;IAElFyC,IAAI,GAAA,WAAA,wKAAe7E,IAAI,CAACsC,YAAAA,AAAc,GAAEC,OAAO,IAAI;QACjD,IAAImC,qBAAqB,OAAGtE,IAAI,CAAC0B,wKAAAA,AAAS,EAAC,IAAI,CAACyC,cAAc,EAAEhC,OAAO,CAACX,OAAO,CAAC;QAChF,IAAI8C,qBAAqB,KAAK3C,SAAS,EAAE;YACvC,4KAAO/B,IAAI,CAACwC,SAAAA,AAAW,EAACD,OAAO,CAACE,OAAO,CAAC;QAC1C;QACA,IAAIM,KAAK,GAAoBtC,WAAW;QACxC,IAAIkD,IAAI,GAAG,IAAI;QACf,MAAOA,IAAI,CAAE;YACX,MAAMC,IAAI,wKAAGxD,IAAI,CAAC0B,OAAAA,AAAS,EAAC4C,qBAAqB,EAAEnC,OAAO,CAACX,OAAO,CAAC;YACnE,IAAIgC,IAAI,KAAK7B,SAAS,EAAE;gBACtB,uKAAO/B,IAAI,CAACmC,IAAK;YACnB;YACA,MAAMxB,IAAI,GAAGiD,IAAI,CAACjD,IAAI;YACtB,MAAME,IAAI,GAAmC+C,IAAI,CAAC/C,IAAI;YACtD,IAAIF,IAAI,KAAKF,WAAW,EAAE;gBACxBsC,KAAK,GAAGpC,IAAI;gBACZgD,IAAI,GAAG,KAAK;YACd,CAAC,MAAM;gBACLe,qBAAqB,GAAG7D,IAAI;YAC9B;QACF;QACA,4KAAOb,IAAI,CAAC0C,KAAAA,AAAO,EAACK,KAAU,CAAC;IACjC,CAAC,CAAC,CAAA;IAEF+B,UAAU,GAAA,WAAA,GAA8B9E,IAAI,CAACsC,iLAAAA,AAAc,GAAEC,OAAO,IAAI;QACtE,IAAImC,qBAAqB,OAAGtE,IAAI,CAAC0B,wKAAAA,AAAS,EAAC,IAAI,CAACyC,cAAc,EAAEhC,OAAO,CAACX,OAAO,CAAC;QAChF,IAAI8C,qBAAqB,KAAK3C,SAAS,EAAE;YACvC,2KAAO/B,IAAI,CAACwC,UAAAA,AAAW,EAACD,OAAO,CAACE,OAAO,CAAC;QAC1C;QACA,IAAIM,KAAK,uJAAqBhD,MAAM,CAACgF,AAAI,EAAE;QAC3C,IAAIpB,IAAI,GAAG,IAAI;QACf,MAAOA,IAAI,CAAE;YACX,MAAMC,IAAI,wKAAGxD,IAAI,CAAC0B,OAAAA,AAAS,EAAC4C,qBAAqB,EAAEnC,OAAO,CAACX,OAAO,CAAC;YACnE,IAAIgC,IAAI,KAAK7B,SAAS,EAAE;gBACtBgB,KAAK,uJAAGhD,MAAM,CAAK,AAAJgF,EAAM;gBACrBpB,IAAI,GAAG,KAAK;YACd,CAAC,MAAM;gBACL,MAAMhD,IAAI,GAAGiD,IAAI,CAACjD,IAAI;gBACtB,MAAME,IAAI,GAAmC+C,IAAI,CAAC/C,IAAI;gBACtD,IAAIF,IAAI,KAAKF,WAAW,EAAE;oBACxBsC,KAAK,uJAAGhD,MAAM,CAACiF,AAAI,EAACrE,IAAI,CAAC;oBACzBgD,IAAI,GAAG,KAAK;gBACd,CAAC,MAAM;oBACLe,qBAAqB,GAAG7D,IAAI;gBAC9B;YACF;QACF;QACA,4KAAOb,IAAI,CAAC0C,KAAAA,AAAO,EAACK,KAAK,CAAC;IAC5B,CAAC,CAAC,CAAA;IAEFmB,QAAQ,GAAA,WAAA,wKAAkBlE,IAAI,CAAC2B,IAAAA,AAAM,GAAeC,OAAO,IAAI;QAC7D,IAAI8C,qBAAqB,IAAGtE,IAAI,CAAC0B,2KAAAA,AAAS,EAAC,IAAI,CAACyC,cAAc,EAAE3C,OAAO,CAAC;QACxE,IAAI8C,qBAAqB,KAAK3C,SAAS,EAAE;iLACvC3B,IAAI,CAACiD,OAAAA,AAAS,EAA6C,IAAI,CAACkB,cAAc,EAAE,KAAK,CAAC,EAAE3C,OAAO,CAAC;YAChG,IAAI+B,IAAI,GAAG,IAAI;YACf,MAAOA,IAAI,CAAE;gBACX,MAAMC,IAAI,wKAAGxD,IAAI,CAAC0B,OAAAA,AAAS,EAAC4C,qBAAqB,EAAE9C,OAAO,CAAC;gBAC3D,IAAIgC,IAAI,KAAK7B,SAAS,EAAE;oBACtB4B,IAAI,GAAG,KAAK;gBACd,CAAC,MAAM;oBACL,MAAMhD,IAAI,GAAGiD,IAAI,CAACjD,IAAI;oBACtB,MAAME,IAAI,GAAmC+C,IAAI,CAAC/C,IAAI;oBACtD,IAAIF,IAAI,KAAKF,WAAW,EAAE;wBACxB,MAAMG,WAAW,GAAGgD,IAAI,CAAChD,WAAW;wBACpC,IAAIA,WAAW,KAAK,CAAC,EAAE;4BACrB,MAAMyB,IAAI,wKAAGjC,IAAI,CAAC0B,OAAS,AAATA,EAAU,IAAI,CAACf,UAAU,EAAEa,OAAO,CAAC;4BACrD,MAAMwB,WAAW,GAAG1C,QAAQ,CAAID,WAAW,EAAE,CAAC,EAAEI,IAAI,CAAC;4BACrDT,IAAI,CAACiD,4KAAAA,AAAS,EAAsBqB,qBAAqB,EAAEtB,WAAW,EAAExB,OAAO,CAAC;iMAChFxB,IAAI,CAACiD,OAAS,AAATA,EAAU,IAAI,CAACrC,aAAa,EAAEH,IAAW,EAAEe,OAAO,CAAC;gMACxDxB,IAAI,CAACiD,QAAAA,AAAS,EAAC,IAAI,CAACtC,UAAU,EAAEsB,IAAI,GAAG,CAAC,EAAET,OAAO,CAAC;wBACpD,CAAC,MAAM;4BACL,MAAMwB,WAAW,GAAG1C,QAAQ,CAACC,IAAI,EAAEC,WAAW,GAAG,CAAC,EAAEC,IAAI,CAAC;iMACzDT,IAAI,CAACiD,OAAAA,AAAS,EAAsBqB,qBAAqB,EAAEtB,WAAW,EAAExB,OAAO,CAAC;wBAClF;oBACF;oBACA8C,qBAAqB,GAAG7D,IAAI;gBAC9B;YACF;YACA,MAAMmC,sBAAsB,wKAAG5C,IAAI,CAAC0B,OAAAA,AAAS,EAAC,IAAI,CAACV,eAAe,EAAEQ,OAAO,CAAC;YAC5ExB,IAAI,CAACiD,4KAAAA,AAAS,EAAC,IAAI,CAACjC,eAAe,EAAE4B,sBAAsB,GAAG,CAAC,EAAEpB,OAAO,CAAC;iLACzExB,IAAI,CAACiD,OAAAA,AAAS,EACZ,IAAI,CAACzC,WAAW,GAChBd,OAAO,CAACmF,qJAAAA,AAAM,uKACZ7E,IAAI,CAAC0B,OAAAA,AAAS,EAAC,IAAI,CAAClB,WAAW,EAAEgB,OAAO,CAAC,EACzC,IAAI,CAAC2C,cAAqB,CAC3B,EACD3C,OAAO,CACR;QACH;IACF,CAAC,CAAC,CAAA;IAEFsD,IAAI,GAAA,WAAA,wKAAelF,IAAI,CAACsC,YAAAA,AAAc,GAAEC,OAAO,IAAI;QACjD,IAAImC,qBAAqB,OAAGtE,IAAI,CAAC0B,wKAAAA,AAAS,EAAC,IAAI,CAACyC,cAAc,EAAEhC,OAAO,CAACX,OAAO,CAAC;QAChF,IAAI8C,qBAAqB,KAAK3C,SAAS,EAAE;YACvC,QAAO/B,IAAI,CAACwC,6KAAAA,AAAW,EAACD,OAAO,CAACE,OAAO,CAAC;QAC1C;QACA,IAAIM,KAAK,GAAoBtC,WAAW;QACxC,IAAIkD,IAAI,GAAG,IAAI;QACf,MAAOA,IAAI,CAAE;YACX,MAAMC,IAAI,wKAAGxD,IAAI,CAAC0B,OAAAA,AAAS,EAAC4C,qBAAqB,EAAEnC,OAAO,CAACX,OAAO,CAAC;YACnE,IAAIgC,IAAI,KAAK7B,SAAS,EAAE;gBACtB,wKAAO/B,IAAI,CAACmC,GAAK;YACnB;YACA,MAAMxB,IAAI,GAAGiD,IAAI,CAACjD,IAAI;YACtB,MAAME,IAAI,GAAmC+C,IAAI,CAAC/C,IAAI;YACtD,IAAIF,IAAI,KAAKF,WAAW,EAAE;gBACxB,MAAMG,WAAW,GAAGgD,IAAI,CAAChD,WAAW;gBACpC,IAAIA,WAAW,KAAK,CAAC,EAAE;oBACrB,MAAMyB,IAAI,OAAGjC,IAAI,CAAC0B,wKAAS,AAATA,EAAU,IAAI,CAACf,UAAU,EAAEwB,OAAO,CAACX,OAAO,CAAC;oBAC7D,MAAMwB,WAAW,GAAG1C,QAAQ,CAAID,WAAW,EAAE,CAAC,EAAEI,IAAI,CAAC;oBACrDT,IAAI,CAACiD,4KAAAA,AAAS,EAAsBqB,qBAAqB,EAAEtB,WAAW,EAAEb,OAAO,CAACX,OAAO,CAAC;yLACxFxB,IAAI,CAACiD,OAAAA,AAAS,EAAC,IAAI,CAACrC,aAAa,EAAEH,IAAW,EAAE0B,OAAO,CAACX,OAAO,CAAC;yLAChExB,IAAI,CAACiD,OAAAA,AAAS,EAAC,IAAI,CAACtC,UAAU,EAAEsB,IAAI,GAAG,CAAC,EAAEE,OAAO,CAACX,OAAO,CAAC;gBAC5D,CAAC,MAAM;oBACL,MAAMwB,WAAW,GAAG1C,QAAQ,CAACC,IAAI,EAAEC,WAAW,GAAG,CAAC,EAAEC,IAAI,CAAC;yLACzDT,IAAI,CAACiD,OAAAA,AAAS,EAAsBqB,qBAAqB,EAAEtB,WAAW,EAAEb,OAAO,CAACX,OAAO,CAAC;gBAC1F;iBACAxB,IAAI,CAACiD,2KAAAA,AAAS,EACZ,IAAI,CAACkB,cAAc,EACnB1D,IAAI,EACJ0B,OAAO,CAACX,OAAO,CAChB;gBACDmB,KAAK,GAAGpC,IAAI;gBACZgD,IAAI,GAAG,KAAK;YACd,CAAC,MAAM;gBACLe,qBAAqB,GAAG7D,IAAI;YAC9B;QACF;QACA,2KAAOb,IAAI,CAAC0C,MAAO,AAAPA,EAAQK,KAAU,CAAC;IACjC,CAAC,CAAC,CAAA;IAEFoC,OAAO,GAAA,WAAA,GAAsB,IAAI,CAACC,QAAQ,CAACT,MAAM,CAACU,iBAAiB,CAAC,CAAA;IAEpED,QAAQA,CAACE,GAAW,EAAA;QAClB,OAAOtF,IAAI,CAACsC,iLAAAA,AAAc,GAAEC,OAAO,IAAI;YACrC,IAAImC,qBAAqB,wKAAGtE,IAAI,CAAC0B,OAAS,AAATA,EAAU,IAAI,CAACyC,cAAc,EAAEhC,OAAO,CAACX,OAAO,CAAC;YAChF,IAAI8C,qBAAqB,KAAK3C,SAAS,EAAE;gBACvC,WAAO/B,IAAI,CAACwC,0KAAAA,AAAW,EAACD,OAAO,CAACE,OAAO,CAAC;YAC1C;YACA,MAAM8C,OAAO,GAAa,EAAE;YAC5B,IAAIC,CAAC,GAAG,CAAC;YACT,MAAOA,CAAC,KAAKF,GAAG,CAAE;gBAChB,MAAM1B,IAAI,wKAAGxD,IAAI,CAAC0B,OAAAA,AAAS,EAAC4C,qBAAqB,EAAEnC,OAAO,CAACX,OAAO,CAAC;gBACnE,IAAIgC,IAAI,KAAK7B,SAAS,EAAE;oBACtByD,CAAC,GAAGF,GAAG;gBACT,CAAC,MAAM;oBACL,MAAM3E,IAAI,GAAGiD,IAAI,CAACjD,IAAI;oBACtB,MAAME,IAAI,GAAmC+C,IAAI,CAAC/C,IAAI;oBACtD,IAAIF,IAAI,KAAKF,WAAW,EAAE;wBACxB,MAAMG,WAAW,GAAGgD,IAAI,CAAChD,WAAW;wBACpC,IAAIA,WAAW,KAAK,CAAC,EAAE;4BACrB,MAAMyB,IAAI,IAAGjC,IAAI,CAAC0B,2KAAAA,AAAS,EAAC,IAAI,CAACf,UAAU,EAAEwB,OAAO,CAACX,OAAO,CAAC;4BAC7D,MAAMwB,WAAW,GAAG1C,QAAQ,CAAID,WAAW,EAAE,CAAC,EAAEI,IAAI,CAAC;gMACrDT,IAAI,CAACiD,QAAAA,AAAS,EAAsBqB,qBAAqB,EAAEtB,WAAW,EAAEb,OAAO,CAACX,OAAO,CAAC;iMACxFxB,IAAI,CAACiD,OAAAA,AAAS,EAAC,IAAI,CAACrC,aAAa,EAAEH,IAAW,EAAE0B,OAAO,CAACX,OAAO,CAAC;iMAChExB,IAAI,CAACiD,OAAAA,AAAS,EAAC,IAAI,CAACtC,UAAU,EAAEsB,IAAI,GAAG,CAAC,EAAEE,OAAO,CAACX,OAAO,CAAC;wBAC5D,CAAC,MAAM;4BACL,MAAMwB,WAAW,GAAG1C,QAAQ,CAACC,IAAI,EAAEC,WAAW,GAAG,CAAC,EAAEC,IAAI,CAAC;iMACzDT,IAAI,CAACiD,OAAAA,AAAS,EAAsBqB,qBAAqB,EAAEtB,WAAW,EAAEb,OAAO,CAACX,OAAO,CAAC;wBAC1F;wBACA2D,OAAO,CAACE,IAAI,CAAC9E,IAAI,CAAC;wBAClB6E,CAAC,GAAGA,CAAC,GAAG,CAAC;oBACX;oBACAd,qBAAqB,GAAG7D,IAAI;gBAC9B;YACF;iLACAT,IAAI,CAACiD,OAAAA,AAAS,EACZ,IAAI,CAACkB,cAAc,EACnBG,qBAAqB,EACrBnC,OAAO,CAACX,OAAO,CAChB;YACD,4KAAO5B,IAAI,CAAC0C,KAAAA,AAAO,EAAC6C,OAAO,CAAC;QAC9B,CAAC,CAAC;IACJ;;AAGF,cAAA,GACA,MAAMG,WAAW,GAAGA,CAClBxE,iBAAyB,EACzBC,QAA+B,yJAE/BtB,OAAI,AAAJA,sKACEK,GAAG,CAACyF,EAAAA,AAAG,EAAC;6KACNvF,IAAI,CAACwF,EAAAA,AAAI,EAAsB,KAAK,CAAC,CAAC;6KACtCxF,IAAI,CAACwF,EAAAA,AAAI,EAAC,CAAC,CAAC;KACb,CAAC,uKACF5F,IAAI,CAACiC,KAAAA,AAAO,EAAC,CAAC,CAACoC,KAAK,EAAEtD,UAAU,CAAC,yJAC/BlB,OAAAA,AAAI,sKACFK,GAAG,CAACyF,EAAAA,AAAG,EAAC;YACNvF,IAAI,CAACwF,uKAAAA,AAAI,EAACvB,KAAK,CAAC;iLAChBjE,IAAI,CAACwF,EAAAA,AAAI,EAACvB,KAAK,CAAC;YAChBjE,IAAI,CAACwF,uKAAI,AAAJA,EAAK,CAAC,CAAC;iLACZxF,IAAI,CAACwF,EAAAA,AAAI,uJAAC9F,OAAO,CAACuE,AAAK,EAAE,CAAC;SAC3B,CAAC,uKACFrE,IAAI,CAAC4C,CAAAA,AAAG,EAAC,CAAC,CAAC5B,aAAa,EAAEC,aAAa,EAAEG,eAAe,EAAER,WAAW,CAAC,GACpE,IAAIE,WAAW,CACbC,UAAU,EACVC,aAAa,EACbC,aAAoB,EACpBC,iBAAiB,EACjBC,QAAQ,EACRC,eAAe,EACfR,WAAkB,CACnB,CACF,CACF,CACF,CACF;AAEH,MAAMiF,gBAAgB,GAAGA,CACvB9E,UAA6B,EAC7BC,aAAwD,EACxDC,aAAoE,EACpEC,iBAAyB,EACzBE,eAAkC,EAClCR,WAAkF,yJAElFf,OAAAA,AAAI,EACFO,IAAI,CAAC0F,sKAAAA,AAAG,EAAC7E,aAAa,CAAC,uKACvBjB,IAAI,CAACiC,KAAO,AAAPA,GAASJ,oBAAoB,yJAChChC,OAAAA,AAAI,sKACFK,GAAG,CAACyF,EAAAA,AAAG,EAAC;YACNvF,IAAI,CAACwF,uKAAAA,AAAI,EAAC/D,oBAAoB,CAAC;iLAC/BzB,IAAI,CAAC0F,CAAAA,AAAG,EAAC1E,eAAe,CAAC;iLACzBhB,IAAI,CAAC0F,CAAAA,AAAG,EAAClF,WAAW,CAAC;SACtB,CAAC,GACFV,GAAG,CAAC6F,qKAAAA,AAAG,EAAC,CAAC,CAACzE,CAAC,EAAE0B,sBAAsB,CAAC,yJAClCnD,OAAI,AAAJA,EACEuB,eAAe,uKACfhB,IAAI,CAAC4F,CAAAA,AAAG,EAAChD,sBAAsB,GAAG,CAAC,CAAC,CACrC,CACF,EACD9C,GAAG,CAAC6F,sKAAAA,AAAG,EAAC,CAAC,CAACxB,cAAc,EAAEjD,CAAC,EAAE6C,kBAAkB,CAAC,yJAC9CtE,OAAAA,AAAI,EACFe,WAAkB,uKAClBR,IAAI,CAAC4F,CAAAA,AAAG,GAACnG,4JAAAA,AAAI,EAACsE,kBAAyB,uJAAErE,MAAQmG,AAAG,CAAJ,CAACA,AAAI1B,cAAc,CAAC,CAAC,CAAC,CACvE,CACF,uKACDvE,IAAI,CAAC4C,CAAAA,AAAG,EAAC,CAAC,CAAC2B,cAAc,CAAC,GACxB,IAAID,uBAAuB,CACzBvD,UAAU,EACVC,aAAa,EACbE,iBAAiB,EACjBqD,cAAqB,EACrBnD,eAAe,EACfR,WAAW,CACZ,CACF,CACF,CACF,CACF;AAGI,MAAMoB,aAAa,IAAOkE,IAAwB,GAAoBA,IAAI,CAAClE,aAAa;AAGxF,MAAMmE,OAAO,IAAOjF,iBAAyB,GAClDwE,WAAW,CAAIxE,iBAAiB,qKAAEf,MAAM,CAACiG,QAAY,CAAC;AAGjD,MAAMhE,QAAQ,IAAO8D,IAAwB,GAAaA,IAAI,CAAC9D,QAAQ,EAAE;AAGzE,MAAMiE,QAAQ,GAAOnF,iBAAyB,IACnDwE,WAAW,CAAIxE,iBAAiB,qKAAEf,MAAM,CAACmG,IAAQ,CAAC;AAG7C,MAAM3D,OAAO,IAAOuD,IAAwB,GAAuBA,IAAI,CAACvD,OAAO;AAG/E,MAAME,MAAM,IAAOqD,IAAwB,GAAuBA,IAAI,CAACrD,MAAM;AAG7E,MAAMnB,UAAU,IAAOwE,IAAwB,GAAuBA,IAAI,CAACxE,UAAU;AAGrF,MAAM6E,OAAO,GAAA,WAAA,yJAAG5G,OAAI,AAAJA,EAGrB,CAAC,EAAE,CAACuG,IAAI,EAAEnD,KAAK,GAAKmD,IAAI,CAACpD,KAAK,CAACC,KAAK,CAAC,CAAC;AAGjC,MAAMyD,UAAU,GAAA,WAAA,IAAG7G,4JAAAA,AAAI,EAG5B,CAAC,EAAE,CAACuG,IAAI,EAAEpC,QAAQ,GAAKoC,IAAI,CAACrC,QAAQ,CAACC,QAAQ,CAAC,CAAC;AAG1C,MAAMzB,IAAI,IAAO6D,IAAwB,GAAsBA,IAAI,CAAC7D,IAAI;AAGxE,MAAM6B,QAAQ,IAAOgC,IAAwB,GAAoBA,IAAI,CAAChC,QAAQ;AAG9E,MAAMuC,OAAO,IAAOvF,iBAAyB,GAClDwE,WAAW,CAAIxE,iBAAiB,qKAAEf,MAAM,CAACuG,GAAO,CAAC;AAG5C,MAAMC,SAAS,IAAOT,IAAwB,GACnDL,gBAAgB,CACdK,IAAI,CAACnF,UAAU,EACfmF,IAAI,CAAClF,aAAa,EAClBkF,IAAI,CAACjF,aAAa,EAClBiF,IAAI,CAAChF,iBAAiB,EACtBgF,IAAI,CAAC9E,eAAe,EACpB8E,IAAI,CAACtF,WAAW,CACjB;AAGI,MAAMgG,eAAe,IAAOV,IAAwB,uJACzDxG,MAAM,CAACmH,UAAAA,AAAc,EACnBF,SAAS,CAACT,IAAI,CAAC,GACdY,OAAO,0KAAK3G,MAAM,CAAC+D,IAAAA,AAAQ,EAAC4C,OAAO,CAAC,CACtC;AAGI,MAAMC,SAAS,GAAGA,CAAA,GAAsCrB,WAAW,CAAIf,MAAM,CAACC,gBAAgB,qKAAEzE,MAAM,CAACmG,IAAQ,CAAC", "ignoreList": [0], "debugId": null}}]}