{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "sink.js", "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/node_modules/effect/src/internal/sink.ts"], "sourcesContent": ["import * as Arr from \"../Array.js\"\nimport * as Cause from \"../Cause.js\"\nimport type * as Channel from \"../Channel.js\"\nimport * as Chunk from \"../Chunk.js\"\nimport * as Clock from \"../Clock.js\"\nimport type * as Context from \"../Context.js\"\nimport * as Duration from \"../Duration.js\"\nimport * as Effect from \"../Effect.js\"\nimport * as Either from \"../Either.js\"\nimport * as Exit from \"../Exit.js\"\nimport { constTrue, dual, identity, pipe } from \"../Function.js\"\nimport type { LazyArg } from \"../Function.js\"\nimport * as HashMap from \"../HashMap.js\"\nimport * as HashSet from \"../HashSet.js\"\nimport type * as MergeDecision from \"../MergeDecision.js\"\nimport * as Option from \"../Option.js\"\nimport { pipeArguments } from \"../Pipeable.js\"\nimport { hasProperty, type Predicate, type Refinement } from \"../Predicate.js\"\nimport * as PubSub from \"../PubSub.js\"\nimport * as Queue from \"../Queue.js\"\nimport * as Ref from \"../Ref.js\"\nimport * as Scope from \"../Scope.js\"\nimport type * as Sink from \"../Sink.js\"\nimport type * as Types from \"../Types.js\"\nimport * as channel from \"./channel.js\"\nimport * as mergeDecision from \"./channel/mergeDecision.js\"\nimport * as core from \"./core-stream.js\"\n\n/** @internal */\nexport const SinkTypeId: Sink.SinkTypeId = Symbol.for(\"effect/Sink\") as Sink.SinkTypeId\n\nconst sinkVariance = {\n  /* c8 ignore next */\n  _A: (_: never) => _,\n  /* c8 ignore next */\n  _In: (_: unknown) => _,\n  /* c8 ignore next */\n  _L: (_: never) => _,\n  /* c8 ignore next */\n  _E: (_: never) => _,\n  /* c8 ignore next */\n  _R: (_: never) => _\n}\n\n/** @internal */\nexport class SinkImpl<out A, in In = unknown, out L = never, out E = never, out R = never>\n  implements Sink.Sink<A, In, L, E, R>\n{\n  readonly [SinkTypeId] = sinkVariance\n  constructor(\n    readonly channel: Channel.Channel<Chunk.Chunk<L>, Chunk.Chunk<In>, E, never, A, unknown, R>\n  ) {\n  }\n  pipe() {\n    return pipeArguments(this, arguments)\n  }\n}\n\n/** @internal */\nexport const isSink = (u: unknown): u is Sink.Sink<unknown, unknown, unknown, unknown, unknown> =>\n  hasProperty(u, SinkTypeId)\n\n/** @internal */\nexport const suspend = <A, In, L, E, R>(evaluate: LazyArg<Sink.Sink<A, In, L, E, R>>): Sink.Sink<A, In, L, E, R> =>\n  new SinkImpl(core.suspend(() => toChannel(evaluate())))\n\n/** @internal */\nexport const as = dual<\n  <A2>(a: A2) => <A, In, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A2, In, L, E, R>,\n  <A, In, L, E, R, A2>(self: Sink.Sink<A, In, L, E, R>, a: A2) => Sink.Sink<A2, In, L, E, R>\n>(\n  2,\n  (self, a) => pipe(self, map(() => a))\n)\n\n/** @internal */\nexport const collectAll = <In>(): Sink.Sink<Chunk.Chunk<In>, In> => new SinkImpl(collectAllLoop(Chunk.empty()))\n\n/** @internal */\nconst collectAllLoop = <In>(\n  acc: Chunk.Chunk<In>\n): Channel.Channel<never, Chunk.Chunk<In>, never, never, Chunk.Chunk<In>, unknown> =>\n  core.readWithCause({\n    onInput: (chunk: Chunk.Chunk<In>) => collectAllLoop(pipe(acc, Chunk.appendAll(chunk))),\n    onFailure: core.failCause,\n    onDone: () => core.succeed(acc)\n  })\n\n/** @internal */\nexport const collectAllN = <In>(n: number): Sink.Sink<Chunk.Chunk<In>, In, In> =>\n  suspend(() => fromChannel(collectAllNLoop(n, Chunk.empty())))\n\n/** @internal */\nconst collectAllNLoop = <In>(\n  n: number,\n  acc: Chunk.Chunk<In>\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, never, never, Chunk.Chunk<In>, unknown> =>\n  core.readWithCause({\n    onInput: (chunk: Chunk.Chunk<In>) => {\n      const [collected, leftovers] = Chunk.splitAt(chunk, n)\n      if (collected.length < n) {\n        return collectAllNLoop(n - collected.length, Chunk.appendAll(acc, collected))\n      }\n      if (Chunk.isEmpty(leftovers)) {\n        return core.succeed(Chunk.appendAll(acc, collected))\n      }\n      return core.flatMap(core.write(leftovers), () => core.succeed(Chunk.appendAll(acc, collected)))\n    },\n    onFailure: core.failCause,\n    onDone: () => core.succeed(acc)\n  })\n\n/** @internal */\nexport const collectAllFrom = <A, In, L extends In, E, R>(\n  self: Sink.Sink<A, In, L, E, R>\n): Sink.Sink<Chunk.Chunk<A>, In, L, E, R> =>\n  collectAllWhileWith(self, {\n    initial: Chunk.empty<A>(),\n    while: constTrue,\n    body: (chunk, a) => pipe(chunk, Chunk.append(a))\n  })\n\n/** @internal */\nexport const collectAllToMap = <In, K>(\n  key: (input: In) => K,\n  merge: (x: In, y: In) => In\n): Sink.Sink<HashMap.HashMap<K, In>, In> => {\n  return pipe(\n    foldLeftChunks(HashMap.empty<K, In>(), (map, chunk) =>\n      pipe(\n        chunk,\n        Chunk.reduce(map, (map, input) => {\n          const k: K = key(input)\n          const v: In = pipe(map, HashMap.has(k)) ?\n            merge(pipe(map, HashMap.unsafeGet(k)), input) :\n            input\n          return pipe(map, HashMap.set(k, v))\n        })\n      ))\n  )\n}\n\n/** @internal */\nexport const collectAllToMapN = <In, K>(\n  n: number,\n  key: (input: In) => K,\n  merge: (x: In, y: In) => In\n): Sink.Sink<HashMap.HashMap<K, In>, In, In> => {\n  return foldWeighted<HashMap.HashMap<K, In>, In>({\n    initial: HashMap.empty(),\n    maxCost: n,\n    cost: (acc, input) => pipe(acc, HashMap.has(key(input))) ? 0 : 1,\n    body: (acc, input) => {\n      const k: K = key(input)\n      const v: In = pipe(acc, HashMap.has(k)) ?\n        merge(pipe(acc, HashMap.unsafeGet(k)), input) :\n        input\n      return pipe(acc, HashMap.set(k, v))\n    }\n  })\n}\n\n/** @internal */\nexport const collectAllToSet = <In>(): Sink.Sink<HashSet.HashSet<In>, In> =>\n  foldLeftChunks<HashSet.HashSet<In>, In>(\n    HashSet.empty(),\n    (acc, chunk) => pipe(chunk, Chunk.reduce(acc, (acc, input) => pipe(acc, HashSet.add(input))))\n  )\n\n/** @internal */\nexport const collectAllToSetN = <In>(n: number): Sink.Sink<HashSet.HashSet<In>, In, In> =>\n  foldWeighted<HashSet.HashSet<In>, In>({\n    initial: HashSet.empty(),\n    maxCost: n,\n    cost: (acc, input) => HashSet.has(acc, input) ? 0 : 1,\n    body: (acc, input) => HashSet.add(acc, input)\n  })\n\n/** @internal */\nexport const collectAllUntil = <In>(p: Predicate<In>): Sink.Sink<Chunk.Chunk<In>, In, In> => {\n  return pipe(\n    fold<[Chunk.Chunk<In>, boolean], In>(\n      [Chunk.empty(), true],\n      (tuple) => tuple[1],\n      ([chunk, _], input) => [pipe(chunk, Chunk.append(input)), !p(input)]\n    ),\n    map((tuple) => tuple[0])\n  )\n}\n\n/** @internal */\nexport const collectAllUntilEffect = <In, E, R>(p: (input: In) => Effect.Effect<boolean, E, R>) => {\n  return pipe(\n    foldEffect<[Chunk.Chunk<In>, boolean], In, E, R>(\n      [Chunk.empty(), true],\n      (tuple) => tuple[1],\n      ([chunk, _], input) => pipe(p(input), Effect.map((bool) => [pipe(chunk, Chunk.append(input)), !bool]))\n    ),\n    map((tuple) => tuple[0])\n  )\n}\n\n/** @internal */\nexport const collectAllWhile: {\n  <In, Out extends In>(refinement: Refinement<In, Out>): Sink.Sink<Chunk.Chunk<Out>, In, In>\n  <In>(predicate: Predicate<In>): Sink.Sink<Chunk.Chunk<In>, In, In>\n} = <In>(predicate: Predicate<In>): Sink.Sink<Chunk.Chunk<In>, In, In> =>\n  fromChannel(collectAllWhileReader(predicate, Chunk.empty()))\n\n/** @internal */\nconst collectAllWhileReader = <In>(\n  predicate: Predicate<In>,\n  done: Chunk.Chunk<In>\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, never, never, Chunk.Chunk<In>, unknown> =>\n  core.readWith({\n    onInput: (input: Chunk.Chunk<In>) => {\n      const [collected, leftovers] = pipe(Chunk.toReadonlyArray(input), Arr.span(predicate))\n      if (leftovers.length === 0) {\n        return collectAllWhileReader(\n          predicate,\n          pipe(done, Chunk.appendAll(Chunk.unsafeFromArray(collected)))\n        )\n      }\n      return pipe(\n        core.write(Chunk.unsafeFromArray(leftovers)),\n        channel.zipRight(core.succeed(pipe(done, Chunk.appendAll(Chunk.unsafeFromArray(collected)))))\n      )\n    },\n    onFailure: core.fail,\n    onDone: () => core.succeed(done)\n  })\n\n/** @internal */\nexport const collectAllWhileEffect = <In, E, R>(\n  predicate: (input: In) => Effect.Effect<boolean, E, R>\n): Sink.Sink<Chunk.Chunk<In>, In, In, E, R> => fromChannel(collectAllWhileEffectReader(predicate, Chunk.empty()))\n\n/** @internal */\nconst collectAllWhileEffectReader = <In, R, E>(\n  predicate: (input: In) => Effect.Effect<boolean, E, R>,\n  done: Chunk.Chunk<In>\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, E, never, Chunk.Chunk<In>, unknown, R> =>\n  core.readWith({\n    onInput: (input: Chunk.Chunk<In>) =>\n      pipe(\n        core.fromEffect(pipe(input, Effect.takeWhile(predicate), Effect.map(Chunk.unsafeFromArray))),\n        core.flatMap((collected) => {\n          const leftovers = pipe(input, Chunk.drop(collected.length))\n          if (Chunk.isEmpty(leftovers)) {\n            return collectAllWhileEffectReader(predicate, pipe(done, Chunk.appendAll(collected)))\n          }\n          return pipe(core.write(leftovers), channel.zipRight(core.succeed(pipe(done, Chunk.appendAll(collected)))))\n        })\n      ),\n    onFailure: core.fail,\n    onDone: () => core.succeed(done)\n  })\n\n/** @internal */\nexport const collectAllWhileWith: {\n  <A, S>(\n    options: {\n      readonly initial: S\n      readonly while: Predicate<A>\n      readonly body: (s: S, a: A) => S\n    }\n  ): <In, L extends In, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<S, In, L, E, R>\n  <A, In, L extends In, E, R, S>(\n    self: Sink.Sink<A, In, L, E, R>,\n    options: {\n      readonly initial: S\n      readonly while: Predicate<A>\n      readonly body: (s: S, a: A) => S\n    }\n  ): Sink.Sink<S, In, L, E, R>\n} = dual(\n  2,\n  <A, In, L extends In, E, R, S>(\n    self: Sink.Sink<A, In, L, E, R>,\n    options: {\n      readonly initial: S\n      readonly while: Predicate<A>\n      readonly body: (s: S, a: A) => S\n    }\n  ): Sink.Sink<S, In, L, E, R> => {\n    const refs = pipe(\n      Ref.make(Chunk.empty<In>()),\n      Effect.zip(Ref.make(false))\n    )\n    const newChannel = pipe(\n      core.fromEffect(refs),\n      core.flatMap(([leftoversRef, upstreamDoneRef]) => {\n        const upstreamMarker: Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, never, never, unknown, unknown> = core\n          .readWith({\n            onInput: (input) => pipe(core.write(input), core.flatMap(() => upstreamMarker)),\n            onFailure: core.fail,\n            onDone: (done) => pipe(core.fromEffect(Ref.set(upstreamDoneRef, true)), channel.as(done))\n          })\n        return pipe(\n          upstreamMarker,\n          core.pipeTo(channel.bufferChunk(leftoversRef)),\n          core.pipeTo(\n            collectAllWhileWithLoop(self, leftoversRef, upstreamDoneRef, options.initial, options.while, options.body)\n          )\n        )\n      })\n    )\n    return new SinkImpl(newChannel)\n  }\n)\n\nconst collectAllWhileWithLoop = <Z, In, L extends In, E, R, S>(\n  self: Sink.Sink<Z, In, L, E, R>,\n  leftoversRef: Ref.Ref<Chunk.Chunk<In>>,\n  upstreamDoneRef: Ref.Ref<boolean>,\n  currentResult: S,\n  p: Predicate<Z>,\n  f: (s: S, z: Z) => S\n): Channel.Channel<Chunk.Chunk<L>, Chunk.Chunk<In>, E, never, S, unknown, R> => {\n  return pipe(\n    toChannel(self),\n    channel.doneCollect,\n    channel.foldChannel({\n      onFailure: core.fail,\n      onSuccess: ([leftovers, doneValue]) =>\n        p(doneValue)\n          ? pipe(\n            core.fromEffect(\n              Ref.set(leftoversRef, Chunk.flatten(leftovers as Chunk.Chunk<Chunk.Chunk<In>>))\n            ),\n            core.flatMap(() =>\n              pipe(\n                core.fromEffect(Ref.get(upstreamDoneRef)),\n                core.flatMap((upstreamDone) => {\n                  const accumulatedResult = f(currentResult, doneValue)\n                  return upstreamDone\n                    ? pipe(core.write(Chunk.flatten(leftovers)), channel.as(accumulatedResult))\n                    : collectAllWhileWithLoop(self, leftoversRef, upstreamDoneRef, accumulatedResult, p, f)\n                })\n              )\n            )\n          )\n          : pipe(core.write(Chunk.flatten(leftovers)), channel.as(currentResult))\n    })\n  )\n}\n\n/** @internal */\nexport const collectLeftover = <A, In, L, E, R>(\n  self: Sink.Sink<A, In, L, E, R>\n): Sink.Sink<[A, Chunk.Chunk<L>], In, never, E, R> =>\n  new SinkImpl(pipe(core.collectElements(toChannel(self)), channel.map(([chunks, z]) => [z, Chunk.flatten(chunks)])))\n\n/** @internal */\nexport const mapInput = dual<\n  <In0, In>(f: (input: In0) => In) => <A, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In0, L, E, R>,\n  <A, In, L, E, R, In0>(self: Sink.Sink<A, In, L, E, R>, f: (input: In0) => In) => Sink.Sink<A, In0, L, E, R>\n>(\n  2,\n  <A, In, L, E, R, In0>(self: Sink.Sink<A, In, L, E, R>, f: (input: In0) => In): Sink.Sink<A, In0, L, E, R> =>\n    pipe(self, mapInputChunks(Chunk.map(f)))\n)\n\n/** @internal */\nexport const mapInputEffect = dual<\n  <In0, In, E2, R2>(\n    f: (input: In0) => Effect.Effect<In, E2, R2>\n  ) => <A, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In0, L, E2 | E, R2 | R>,\n  <A, In, L, E, R, In0, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    f: (input: In0) => Effect.Effect<In, E2, R2>\n  ) => Sink.Sink<A, In0, L, E2 | E, R2 | R>\n>(\n  2,\n  <A, In, L, E, R, In0, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    f: (input: In0) => Effect.Effect<In, E2, R2>\n  ): Sink.Sink<A, In0, L, E | E2, R | R2> =>\n    mapInputChunksEffect(\n      self,\n      (chunk) =>\n        Effect.map(\n          Effect.forEach(chunk, (v) => f(v)),\n          Chunk.unsafeFromArray\n        )\n    )\n)\n\n/** @internal */\nexport const mapInputChunks = dual<\n  <In0, In>(\n    f: (chunk: Chunk.Chunk<In0>) => Chunk.Chunk<In>\n  ) => <A, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In0, L, E, R>,\n  <A, In, L, E, R, In0>(\n    self: Sink.Sink<A, In, L, E, R>,\n    f: (chunk: Chunk.Chunk<In0>) => Chunk.Chunk<In>\n  ) => Sink.Sink<A, In0, L, E, R>\n>(\n  2,\n  <A, In, L, E, R, In0>(\n    self: Sink.Sink<A, In, L, E, R>,\n    f: (chunk: Chunk.Chunk<In0>) => Chunk.Chunk<In>\n  ): Sink.Sink<A, In0, L, E, R> => {\n    const loop: Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In0>, never, never, unknown, unknown, R> = core.readWith({\n      onInput: (chunk) => pipe(core.write(f(chunk)), core.flatMap(() => loop)),\n      onFailure: core.fail,\n      onDone: core.succeed\n    })\n    return new SinkImpl(pipe(loop, core.pipeTo(toChannel(self))))\n  }\n)\n\n/** @internal */\nexport const mapInputChunksEffect = dual<\n  <In0, In, E2, R2>(\n    f: (chunk: Chunk.Chunk<In0>) => Effect.Effect<Chunk.Chunk<In>, E2, R2>\n  ) => <A, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In0, L, E2 | E, R2 | R>,\n  <A, In, L, E, R, In0, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    f: (chunk: Chunk.Chunk<In0>) => Effect.Effect<Chunk.Chunk<In>, E2, R2>\n  ) => Sink.Sink<A, In0, L, E2 | E, R2 | R>\n>(\n  2,\n  <A, In, L, E, R, In0, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    f: (chunk: Chunk.Chunk<In0>) => Effect.Effect<Chunk.Chunk<In>, E2, R2>\n  ): Sink.Sink<A, In0, L, E | E2, R | R2> => {\n    const loop: Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In0>, E2, never, unknown, unknown, R | R2> = core\n      .readWith({\n        onInput: (chunk) => pipe(core.fromEffect(f(chunk)), core.flatMap(core.write), core.flatMap(() => loop)),\n        onFailure: core.fail,\n        onDone: core.succeed\n      })\n    return new SinkImpl(pipe(loop, channel.pipeToOrFail(toChannel(self))))\n  }\n)\n\n/** @internal */\nexport const die = (defect: unknown): Sink.Sink<never, unknown> => failCause(Cause.die(defect))\n\n/** @internal */\nexport const dieMessage = (message: string): Sink.Sink<never, unknown> =>\n  failCause(Cause.die(new Cause.RuntimeException(message)))\n\n/** @internal */\nexport const dieSync = (evaluate: LazyArg<unknown>): Sink.Sink<never, unknown> =>\n  failCauseSync(() => Cause.die(evaluate()))\n\n/** @internal */\nexport const dimap = dual<\n  <In0, In, A, A2>(\n    options: {\n      readonly onInput: (input: In0) => In\n      readonly onDone: (a: A) => A2\n    }\n  ) => <L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A2, In0, L, E, R>,\n  <A, In, L, E, R, In0, A2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    options: {\n      readonly onInput: (input: In0) => In\n      readonly onDone: (a: A) => A2\n    }\n  ) => Sink.Sink<A2, In0, L, E, R>\n>(\n  2,\n  <A, In, L, E, R, In0, A2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    options: {\n      readonly onInput: (input: In0) => In\n      readonly onDone: (a: A) => A2\n    }\n  ): Sink.Sink<A2, In0, L, E, R> => map(mapInput(self, options.onInput), options.onDone)\n)\n\n/** @internal */\nexport const dimapEffect = dual<\n  <In0, In, E2, R2, A, A2, E3, R3>(\n    options: {\n      readonly onInput: (input: In0) => Effect.Effect<In, E2, R2>\n      readonly onDone: (a: A) => Effect.Effect<A2, E3, R3>\n    }\n  ) => <L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A2, In0, L, E2 | E3 | E, R2 | R3 | R>,\n  <A, In, L, E, R, In0, E2, R2, A2, E3, R3>(\n    self: Sink.Sink<A, In, L, E, R>,\n    options: {\n      readonly onInput: (input: In0) => Effect.Effect<In, E2, R2>\n      readonly onDone: (a: A) => Effect.Effect<A2, E3, R3>\n    }\n  ) => Sink.Sink<A2, In0, L, E2 | E3 | E, R2 | R3 | R>\n>(\n  2,\n  (self, options) =>\n    mapEffect(\n      mapInputEffect(self, options.onInput),\n      options.onDone\n    )\n)\n\n/** @internal */\nexport const dimapChunks = dual<\n  <In0, In, A, A2>(\n    options: {\n      readonly onInput: (chunk: Chunk.Chunk<In0>) => Chunk.Chunk<In>\n      readonly onDone: (a: A) => A2\n    }\n  ) => <L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A2, In0, L, E, R>,\n  <A, In, L, E, R, In0, A2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    options: {\n      readonly onInput: (chunk: Chunk.Chunk<In0>) => Chunk.Chunk<In>\n      readonly onDone: (a: A) => A2\n    }\n  ) => Sink.Sink<A2, In0, L, E, R>\n>(\n  2,\n  (self, options) =>\n    map(\n      mapInputChunks(self, options.onInput),\n      options.onDone\n    )\n)\n\n/** @internal */\nexport const dimapChunksEffect = dual<\n  <In0, In, E2, R2, A, A2, E3, R3>(\n    options: {\n      readonly onInput: (chunk: Chunk.Chunk<In0>) => Effect.Effect<Chunk.Chunk<In>, E2, R2>\n      readonly onDone: (a: A) => Effect.Effect<A2, E3, R3>\n    }\n  ) => <L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A2, In0, L, E2 | E3 | E, R2 | R3 | R>,\n  <A, In, L, E, R, In0, E2, R2, A2, E3, R3>(\n    self: Sink.Sink<A, In, L, E, R>,\n    options: {\n      readonly onInput: (chunk: Chunk.Chunk<In0>) => Effect.Effect<Chunk.Chunk<In>, E2, R2>\n      readonly onDone: (a: A) => Effect.Effect<A2, E3, R3>\n    }\n  ) => Sink.Sink<A2, In0, L, E2 | E3 | E, R2 | R3 | R>\n>(\n  2,\n  (self, options) => mapEffect(mapInputChunksEffect(self, options.onInput), options.onDone)\n)\n\n/** @internal */\nexport const drain: Sink.Sink<void, unknown> = new SinkImpl(\n  channel.drain(channel.identityChannel())\n)\n\n/** @internal */\nexport const drop = <In>(n: number): Sink.Sink<unknown, In, In> => suspend(() => new SinkImpl(dropLoop(n)))\n\n/** @internal */\nconst dropLoop = <In>(\n  n: number\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, never, never, unknown, unknown> =>\n  core.readWith({\n    onInput: (input: Chunk.Chunk<In>) => {\n      const dropped = pipe(input, Chunk.drop(n))\n      const leftover = Math.max(n - input.length, 0)\n      const more = Chunk.isEmpty(input) || leftover > 0\n      if (more) {\n        return dropLoop(leftover)\n      }\n      return pipe(\n        core.write(dropped),\n        channel.zipRight(channel.identityChannel<Chunk.Chunk<In>, never, unknown>())\n      )\n    },\n    onFailure: core.fail,\n    onDone: () => core.void\n  })\n\n/** @internal */\nexport const dropUntil = <In>(predicate: Predicate<In>): Sink.Sink<unknown, In, In> =>\n  new SinkImpl(\n    pipe(toChannel(dropWhile((input: In) => !predicate(input))), channel.pipeToOrFail(toChannel(drop<In>(1))))\n  )\n\n/** @internal */\nexport const dropUntilEffect = <In, E, R>(\n  predicate: (input: In) => Effect.Effect<boolean, E, R>\n): Sink.Sink<unknown, In, In, E, R> => suspend(() => new SinkImpl(dropUntilEffectReader(predicate)))\n\n/** @internal */\nconst dropUntilEffectReader = <In, R, E>(\n  predicate: (input: In) => Effect.Effect<boolean, E, R>\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, E, E, unknown, unknown, R> =>\n  core.readWith({\n    onInput: (input: Chunk.Chunk<In>) =>\n      pipe(\n        input,\n        Effect.dropUntil(predicate),\n        Effect.map((leftover) => {\n          const more = leftover.length === 0\n          return more ?\n            dropUntilEffectReader(predicate) :\n            pipe(\n              core.write(Chunk.unsafeFromArray(leftover)),\n              channel.zipRight(channel.identityChannel<Chunk.Chunk<In>, E, unknown>())\n            )\n        }),\n        channel.unwrap\n      ),\n    onFailure: core.fail,\n    onDone: () => core.void\n  })\n\n/** @internal */\nexport const dropWhile = <In>(predicate: Predicate<In>): Sink.Sink<unknown, In, In> =>\n  new SinkImpl(dropWhileReader(predicate))\n\n/** @internal */\nconst dropWhileReader = <In>(\n  predicate: Predicate<In>\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, never, never, unknown, unknown> =>\n  core.readWith({\n    onInput: (input: Chunk.Chunk<In>) => {\n      const out = pipe(input, Chunk.dropWhile(predicate))\n      if (Chunk.isEmpty(out)) {\n        return dropWhileReader(predicate)\n      }\n      return pipe(core.write(out), channel.zipRight(channel.identityChannel<Chunk.Chunk<In>, never, unknown>()))\n    },\n    onFailure: core.fail,\n    onDone: core.succeedNow\n  })\n\n/** @internal */\nexport const dropWhileEffect = <In, E, R>(\n  predicate: (input: In) => Effect.Effect<boolean, E, R>\n): Sink.Sink<unknown, In, In, E, R> => suspend(() => new SinkImpl(dropWhileEffectReader(predicate)))\n\n/** @internal */\nconst dropWhileEffectReader = <In, R, E>(\n  predicate: (input: In) => Effect.Effect<boolean, E, R>\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, E, E, unknown, unknown, R> =>\n  core.readWith({\n    onInput: (input: Chunk.Chunk<In>) =>\n      pipe(\n        input,\n        Effect.dropWhile(predicate),\n        Effect.map((leftover) => {\n          const more = leftover.length === 0\n          return more ?\n            dropWhileEffectReader(predicate) :\n            pipe(\n              core.write(Chunk.unsafeFromArray(leftover)),\n              channel.zipRight(channel.identityChannel<Chunk.Chunk<In>, E, unknown>())\n            )\n        }),\n        channel.unwrap\n      ),\n    onFailure: core.fail,\n    onDone: () => core.void\n  })\n\n/** @internal */\nexport const ensuring = dual<\n  <X, R2>(\n    finalizer: Effect.Effect<X, never, R2>\n  ) => <A, In, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In, L, E, R2 | R>,\n  <A, In, L, E, R, X, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    finalizer: Effect.Effect<X, never, R2>\n  ) => Sink.Sink<A, In, L, E, R2 | R>\n>(\n  2,\n  (self, finalizer) => new SinkImpl(pipe(self, toChannel, channel.ensuring(finalizer)))\n)\n\n/** @internal */\nexport const ensuringWith = dual<\n  <A, E, X, R2>(\n    finalizer: (exit: Exit.Exit<A, E>) => Effect.Effect<X, never, R2>\n  ) => <In, L, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In, L, E, R2 | R>,\n  <A, In, L, E, R, X, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    finalizer: (exit: Exit.Exit<A, E>) => Effect.Effect<X, never, R2>\n  ) => Sink.Sink<A, In, L, E, R2 | R>\n>(\n  2,\n  (self, finalizer) => new SinkImpl(pipe(self, toChannel, core.ensuringWith(finalizer)))\n)\n\n/** @internal */\nexport const context = <R>(): Sink.Sink<Context.Context<R>, unknown, never, never, R> => fromEffect(Effect.context<R>())\n\n/** @internal */\nexport const contextWith = <R, Z>(\n  f: (context: Context.Context<R>) => Z\n): Sink.Sink<Z, unknown, never, never, R> => pipe(context<R>(), map(f))\n\n/** @internal */\nexport const contextWithEffect = <R0, A, E, R>(\n  f: (context: Context.Context<R0>) => Effect.Effect<A, E, R>\n): Sink.Sink<A, unknown, never, E, R0 | R> => pipe(context<R0>(), mapEffect(f))\n\n/** @internal */\nexport const contextWithSink = <R0, A, In, L, E, R>(\n  f: (context: Context.Context<R0>) => Sink.Sink<A, In, L, E, R>\n): Sink.Sink<A, In, L, E, R0 | R> =>\n  new SinkImpl(channel.unwrap(pipe(Effect.contextWith((context) => toChannel(f(context))))))\n\n/** @internal */\nexport const every = <In>(predicate: Predicate<In>): Sink.Sink<boolean, In, In> =>\n  fold(true, identity, (acc, input) => acc && predicate(input))\n\n/** @internal */\nexport const fail = <E>(e: E): Sink.Sink<never, unknown, never, E> => new SinkImpl(core.fail(e))\n\n/** @internal */\nexport const failSync = <E>(evaluate: LazyArg<E>): Sink.Sink<never, unknown, never, E> =>\n  new SinkImpl(core.failSync(evaluate))\n\n/** @internal */\nexport const failCause = <E>(cause: Cause.Cause<E>): Sink.Sink<never, unknown, never, E> =>\n  new SinkImpl(core.failCause(cause))\n\n/** @internal */\nexport const failCauseSync = <E>(evaluate: LazyArg<Cause.Cause<E>>): Sink.Sink<never, unknown, never, E> =>\n  new SinkImpl(core.failCauseSync(evaluate))\n\n/** @internal */\nexport const filterInput: {\n  <In, In1 extends In, In2 extends In1>(\n    f: Refinement<In1, In2>\n  ): <A, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In2, L, E, R>\n  <In, In1 extends In>(f: Predicate<In1>): <A, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In1, L, E, R>\n} = <In, In1 extends In>(f: Predicate<In1>) => {\n  return <A, L, E, R>(self: Sink.Sink<A, In, L, E, R>): Sink.Sink<A, In1, L, E, R> =>\n    pipe(self, mapInputChunks(Chunk.filter(f)))\n}\n\n/** @internal */\nexport const filterInputEffect = dual<\n  <In, In1 extends In, E2, R2>(\n    f: (input: In1) => Effect.Effect<boolean, E2, R2>\n  ) => <A, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In1, L, E2 | E, R2 | R>,\n  <A, In, L, E, R, In1 extends In, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    f: (input: In1) => Effect.Effect<boolean, E2, R2>\n  ) => Sink.Sink<A, In1, L, E2 | E, R2 | R>\n>(\n  2,\n  (self, f) =>\n    mapInputChunksEffect(\n      self,\n      (chunk) => Effect.map(Effect.filter(chunk, f), Chunk.unsafeFromArray)\n    )\n)\n\n/** @internal */\nexport const findEffect = dual<\n  <A, E2, R2>(\n    f: (a: A) => Effect.Effect<boolean, E2, R2>\n  ) => <In, L extends In, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<Option.Option<A>, In, L, E2 | E, R2 | R>,\n  <A, In, L extends In, E, R, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    f: (a: A) => Effect.Effect<boolean, E2, R2>\n  ) => Sink.Sink<Option.Option<A>, In, L, E2 | E, R2 | R>\n>(\n  2,\n  <A, In, L extends In, E, R, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    f: (a: A) => Effect.Effect<boolean, E2, R2>\n  ): Sink.Sink<Option.Option<A>, In, L, E2 | E, R2 | R> => {\n    const newChannel = pipe(\n      core.fromEffect(pipe(\n        Ref.make(Chunk.empty<In>()),\n        Effect.zip(Ref.make(false))\n      )),\n      core.flatMap(([leftoversRef, upstreamDoneRef]) => {\n        const upstreamMarker: Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, never, never, unknown, unknown> = core\n          .readWith({\n            onInput: (input) => pipe(core.write(input), core.flatMap(() => upstreamMarker)),\n            onFailure: core.fail,\n            onDone: (done) => pipe(core.fromEffect(Ref.set(upstreamDoneRef, true)), channel.as(done))\n          })\n        const loop: Channel.Channel<Chunk.Chunk<L>, Chunk.Chunk<In>, E | E2, never, Option.Option<A>, unknown, R | R2> =\n          channel.foldChannel(core.collectElements(toChannel(self)), {\n            onFailure: core.fail,\n            onSuccess: ([leftovers, doneValue]) =>\n              pipe(\n                core.fromEffect(f(doneValue)),\n                core.flatMap((satisfied) =>\n                  pipe(\n                    core.fromEffect(Ref.set(leftoversRef, Chunk.flatten(leftovers))),\n                    channel.zipRight(\n                      pipe(\n                        core.fromEffect(Ref.get(upstreamDoneRef)),\n                        core.flatMap((upstreamDone) => {\n                          if (satisfied) {\n                            return pipe(core.write(Chunk.flatten(leftovers)), channel.as(Option.some(doneValue)))\n                          }\n                          if (upstreamDone) {\n                            return pipe(core.write(Chunk.flatten(leftovers)), channel.as(Option.none()))\n                          }\n                          return loop\n                        })\n                      )\n                    )\n                  )\n                )\n              )\n          })\n        return pipe(upstreamMarker, core.pipeTo(channel.bufferChunk(leftoversRef)), core.pipeTo(loop))\n      })\n    )\n    return new SinkImpl(newChannel)\n  }\n)\n\n/** @internal */\nexport const fold = <S, In>(\n  s: S,\n  contFn: Predicate<S>,\n  f: (s: S, input: In) => S\n): Sink.Sink<S, In, In> => suspend(() => new SinkImpl(foldReader(s, contFn, f)))\n\n/** @internal */\nconst foldReader = <S, In>(\n  s: S,\n  contFn: Predicate<S>,\n  f: (z: S, input: In) => S\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, never, never, S, unknown> => {\n  if (!contFn(s)) {\n    return core.succeedNow(s)\n  }\n  return core.readWith({\n    onInput: (input: Chunk.Chunk<In>) => {\n      const [nextS, leftovers] = foldChunkSplit(s, input, contFn, f, 0, input.length)\n      if (Chunk.isNonEmpty(leftovers)) {\n        return pipe(core.write(leftovers), channel.as(nextS))\n      }\n      return foldReader(nextS, contFn, f)\n    },\n    onFailure: core.fail,\n    onDone: () => core.succeedNow(s)\n  })\n}\n\n/** @internal */\nconst foldChunkSplit = <S, In>(\n  s: S,\n  chunk: Chunk.Chunk<In>,\n  contFn: Predicate<S>,\n  f: (z: S, input: In) => S,\n  index: number,\n  length: number\n): [S, Chunk.Chunk<In>] => {\n  if (index === length) {\n    return [s, Chunk.empty()]\n  }\n  const s1 = f(s, pipe(chunk, Chunk.unsafeGet(index)))\n  if (contFn(s1)) {\n    return foldChunkSplit(s1, chunk, contFn, f, index + 1, length)\n  }\n  return [s1, pipe(chunk, Chunk.drop(index + 1))]\n}\n\n/** @internal */\nexport const foldSink = dual<\n  <E, A1, In, In1 extends In, L1, E1, R1, A, A2, In2 extends In, L2, E2, R2>(\n    options: {\n      readonly onFailure: (err: E) => Sink.Sink<A1, In1, L1, E1, R1>\n      readonly onSuccess: (a: A) => Sink.Sink<A2, In2, L2, E2, R2>\n    }\n  ) => <L, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A1 | A2, In1 & In2, L1 | L2, E1 | E2, R1 | R2 | R>,\n  <A, In, L, E, R, A1, In1 extends In, L1, E1, R1, A2, In2 extends In, L2, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    options: {\n      readonly onFailure: (err: E) => Sink.Sink<A1, In1, L1, E1, R1>\n      readonly onSuccess: (a: A) => Sink.Sink<A2, In2, L2, E2, R2>\n    }\n  ) => Sink.Sink<A1 | A2, In1 & In2, L1 | L2, E1 | E2, R1 | R2 | R>\n>(\n  2,\n  <A, In, L, E, R, A1, In1 extends In, L1, E1, R1, A2, In2 extends In, L2, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    options: {\n      readonly onFailure: (err: E) => Sink.Sink<A1, In1, L1, E1, R1>\n      readonly onSuccess: (z: A) => Sink.Sink<A2, In2, L2, E2, R2>\n    }\n  ): Sink.Sink<A1 | A2, In1 & In2, L1 | L2, E1 | E2, R | R1 | R2> => {\n    const newChannel: Channel.Channel<\n      Chunk.Chunk<L1 | L2>,\n      Chunk.Chunk<In1 & In2>,\n      E1 | E2,\n      never,\n      A1 | A2,\n      unknown,\n      R | R1 | R2\n    > = pipe(\n      toChannel(self),\n      core.collectElements,\n      channel.foldChannel({\n        onFailure: (error) => toChannel(options.onFailure(error)),\n        onSuccess: ([leftovers, z]) =>\n          core.suspend(() => {\n            const leftoversRef = {\n              ref: pipe(leftovers, Chunk.filter(Chunk.isNonEmpty)) as Chunk.Chunk<Chunk.Chunk<L1 | L2>>\n            }\n            const refReader = pipe(\n              core.sync(() => {\n                const ref = leftoversRef.ref\n                leftoversRef.ref = Chunk.empty()\n                return ref\n              }),\n              // This cast is safe because of the L1 >: L <: In1 bound. It follows that\n              // L <: In1 and therefore Chunk[L] can be safely cast to Chunk[In1].\n              core.flatMap((chunk) => channel.writeChunk(chunk as Chunk.Chunk<Chunk.Chunk<In1 & In2>>))\n            )\n            const passthrough = channel.identityChannel<Chunk.Chunk<In1 & In2>, never, unknown>()\n            const continuationSink = pipe(\n              refReader,\n              channel.zipRight(passthrough),\n              core.pipeTo(toChannel(options.onSuccess(z)))\n            )\n            return core.flatMap(\n              core.collectElements(continuationSink),\n              ([newLeftovers, z1]) =>\n                pipe(\n                  core.succeed(leftoversRef.ref),\n                  core.flatMap(channel.writeChunk),\n                  channel.zipRight(channel.writeChunk(newLeftovers)),\n                  channel.as(z1)\n                )\n            )\n          })\n      })\n    )\n    return new SinkImpl(newChannel)\n  }\n)\n\n/** @internal */\nexport const foldChunks = <S, In>(\n  s: S,\n  contFn: Predicate<S>,\n  f: (s: S, chunk: Chunk.Chunk<In>) => S\n): Sink.Sink<S, In> => suspend(() => new SinkImpl(foldChunksReader(s, contFn, f)))\n\n/** @internal */\nconst foldChunksReader = <S, In>(\n  s: S,\n  contFn: Predicate<S>,\n  f: (s: S, chunk: Chunk.Chunk<In>) => S\n): Channel.Channel<never, Chunk.Chunk<In>, never, never, S, unknown> => {\n  if (!contFn(s)) {\n    return core.succeedNow(s)\n  }\n  return core.readWith({\n    onInput: (input: Chunk.Chunk<In>) => foldChunksReader(f(s, input), contFn, f),\n    onFailure: core.fail,\n    onDone: () => core.succeedNow(s)\n  })\n}\n\n/** @internal */\nexport const foldChunksEffect = <S, In, E, R>(\n  s: S,\n  contFn: Predicate<S>,\n  f: (s: S, chunk: Chunk.Chunk<In>) => Effect.Effect<S, E, R>\n): Sink.Sink<S, In, In, E, R> => suspend(() => new SinkImpl(foldChunksEffectReader(s, contFn, f)))\n\n/** @internal */\nconst foldChunksEffectReader = <S, R, E, In>(\n  s: S,\n  contFn: Predicate<S>,\n  f: (s: S, chunk: Chunk.Chunk<In>) => Effect.Effect<S, E, R>\n): Channel.Channel<never, Chunk.Chunk<In>, E, E, S, unknown, R> => {\n  if (!contFn(s)) {\n    return core.succeedNow(s)\n  }\n  return core.readWith({\n    onInput: (input: Chunk.Chunk<In>) =>\n      pipe(\n        core.fromEffect(f(s, input)),\n        core.flatMap((s) => foldChunksEffectReader(s, contFn, f))\n      ),\n    onFailure: core.fail,\n    onDone: () => core.succeedNow(s)\n  })\n}\n\n/** @internal */\nexport const foldEffect = <S, In, E, R>(\n  s: S,\n  contFn: Predicate<S>,\n  f: (s: S, input: In) => Effect.Effect<S, E, R>\n): Sink.Sink<S, In, In, E, R> => suspend(() => new SinkImpl(foldEffectReader(s, contFn, f)))\n\n/** @internal */\nconst foldEffectReader = <S, In, R, E>(\n  s: S,\n  contFn: Predicate<S>,\n  f: (s: S, input: In) => Effect.Effect<S, E, R>\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, E, E, S, unknown, R> => {\n  if (!contFn(s)) {\n    return core.succeedNow(s)\n  }\n  return core.readWith({\n    onInput: (input: Chunk.Chunk<In>) =>\n      pipe(\n        core.fromEffect(foldChunkSplitEffect(s, input, contFn, f)),\n        core.flatMap(([nextS, leftovers]) =>\n          pipe(\n            leftovers,\n            Option.match({\n              onNone: () => foldEffectReader(nextS, contFn, f),\n              onSome: (leftover) => pipe(core.write(leftover), channel.as(nextS))\n            })\n          )\n        )\n      ),\n    onFailure: core.fail,\n    onDone: () => core.succeedNow(s)\n  })\n}\n\n/** @internal */\nconst foldChunkSplitEffect = <S, R, E, In>(\n  s: S,\n  chunk: Chunk.Chunk<In>,\n  contFn: Predicate<S>,\n  f: (s: S, input: In) => Effect.Effect<S, E, R>\n): Effect.Effect<[S, Option.Option<Chunk.Chunk<In>>], E, R> =>\n  foldChunkSplitEffectInternal(s, chunk, 0, chunk.length, contFn, f)\n\n/** @internal */\nconst foldChunkSplitEffectInternal = <S, R, E, In>(\n  s: S,\n  chunk: Chunk.Chunk<In>,\n  index: number,\n  length: number,\n  contFn: Predicate<S>,\n  f: (s: S, input: In) => Effect.Effect<S, E, R>\n): Effect.Effect<[S, Option.Option<Chunk.Chunk<In>>], E, R> => {\n  if (index === length) {\n    return Effect.succeed([s, Option.none()])\n  }\n  return pipe(\n    f(s, pipe(chunk, Chunk.unsafeGet(index))),\n    Effect.flatMap((s1) =>\n      contFn(s1) ?\n        foldChunkSplitEffectInternal(s1, chunk, index + 1, length, contFn, f) :\n        Effect.succeed([s1, Option.some(pipe(chunk, Chunk.drop(index + 1)))])\n    )\n  )\n}\n\n/** @internal */\nexport const foldLeft = <S, In>(s: S, f: (s: S, input: In) => S): Sink.Sink<S, In> =>\n  ignoreLeftover(fold(s, constTrue, f))\n\n/** @internal */\nexport const foldLeftChunks = <S, In>(\n  s: S,\n  f: (s: S, chunk: Chunk.Chunk<In>) => S\n): Sink.Sink<S, In> => foldChunks(s, constTrue, f)\n\n/** @internal */\nexport const foldLeftChunksEffect = <S, In, E, R>(\n  s: S,\n  f: (s: S, chunk: Chunk.Chunk<In>) => Effect.Effect<S, E, R>\n): Sink.Sink<S, In, never, E, R> => ignoreLeftover(foldChunksEffect(s, constTrue, f))\n\n/** @internal */\nexport const foldLeftEffect = <S, In, E, R>(\n  s: S,\n  f: (s: S, input: In) => Effect.Effect<S, E, R>\n): Sink.Sink<S, In, In, E, R> => foldEffect(s, constTrue, f)\n\n/** @internal */\nexport const foldUntil = <S, In>(s: S, max: number, f: (s: S, input: In) => S): Sink.Sink<S, In, In> =>\n  pipe(\n    fold<[S, number], In>(\n      [s, 0],\n      (tuple) => tuple[1] < max,\n      ([output, count], input) => [f(output, input), count + 1]\n    ),\n    map((tuple) => tuple[0])\n  )\n\n/** @internal */\nexport const foldUntilEffect = <S, In, E, R>(\n  s: S,\n  max: number,\n  f: (s: S, input: In) => Effect.Effect<S, E, R>\n): Sink.Sink<S, In, In, E, R> =>\n  pipe(\n    foldEffect(\n      [s, 0 as number] as const,\n      (tuple) => tuple[1] < max,\n      ([output, count], input: In) => pipe(f(output, input), Effect.map((s) => [s, count + 1] as const))\n    ),\n    map((tuple) => tuple[0])\n  )\n\n/** @internal */\nexport const foldWeighted = <S, In>(\n  options: {\n    readonly initial: S\n    readonly maxCost: number\n    readonly cost: (s: S, input: In) => number\n    readonly body: (s: S, input: In) => S\n  }\n): Sink.Sink<S, In, In> =>\n  foldWeightedDecompose({\n    ...options,\n    decompose: Chunk.of\n  })\n\n/** @internal */\nexport const foldWeightedDecompose = <S, In>(\n  options: {\n    readonly initial: S\n    readonly maxCost: number\n    readonly cost: (s: S, input: In) => number\n    readonly decompose: (input: In) => Chunk.Chunk<In>\n    readonly body: (s: S, input: In) => S\n  }\n): Sink.Sink<S, In, In> =>\n  suspend(() =>\n    new SinkImpl(\n      foldWeightedDecomposeLoop(\n        options.initial,\n        0,\n        false,\n        options.maxCost,\n        options.cost,\n        options.decompose,\n        options.body\n      )\n    )\n  )\n\n/** @internal */\nconst foldWeightedDecomposeLoop = <S, In>(\n  s: S,\n  cost: number,\n  dirty: boolean,\n  max: number,\n  costFn: (s: S, input: In) => number,\n  decompose: (input: In) => Chunk.Chunk<In>,\n  f: (s: S, input: In) => S\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, never, never, S, unknown> =>\n  core.readWith({\n    onInput: (input: Chunk.Chunk<In>) => {\n      const [nextS, nextCost, nextDirty, leftovers] = foldWeightedDecomposeFold(\n        input,\n        0,\n        s,\n        cost,\n        dirty,\n        max,\n        costFn,\n        decompose,\n        f\n      )\n      if (Chunk.isNonEmpty(leftovers)) {\n        return pipe(core.write(leftovers), channel.zipRight(core.succeedNow(nextS)))\n      }\n      if (cost > max) {\n        return core.succeedNow(nextS)\n      }\n      return foldWeightedDecomposeLoop(nextS, nextCost, nextDirty, max, costFn, decompose, f)\n    },\n    onFailure: core.fail,\n    onDone: () => core.succeedNow(s)\n  })\n\n/** @internal */\nconst foldWeightedDecomposeFold = <In, S>(\n  input: Chunk.Chunk<In>,\n  index: number,\n  s: S,\n  cost: number,\n  dirty: boolean,\n  max: number,\n  costFn: (s: S, input: In) => number,\n  decompose: (input: In) => Chunk.Chunk<In>,\n  f: (s: S, input: In) => S\n): [S, number, boolean, Chunk.Chunk<In>] => {\n  if (index === input.length) {\n    return [s, cost, dirty, Chunk.empty<In>()]\n  }\n  const elem = pipe(input, Chunk.unsafeGet(index))\n  const total = cost + costFn(s, elem)\n  if (total <= max) {\n    return foldWeightedDecomposeFold(input, index + 1, f(s, elem), total, true, max, costFn, decompose, f)\n  }\n  const decomposed = decompose(elem)\n  if (decomposed.length <= 1 && !dirty) {\n    // If `elem` cannot be decomposed, we need to cross the `max` threshold. To\n    // minimize \"injury\", we only allow this when we haven't added anything else\n    // to the aggregate (dirty = false).\n    return [f(s, elem), total, true, pipe(input, Chunk.drop(index + 1))]\n  }\n  if (decomposed.length <= 1 && dirty) {\n    // If the state is dirty and `elem` cannot be decomposed, we stop folding\n    // and include `elem` in the leftovers.\n    return [s, cost, dirty, pipe(input, Chunk.drop(index))]\n  }\n  // `elem` got decomposed, so we will recurse with the decomposed elements pushed\n  // into the chunk we're processing and see if we can aggregate further.\n  const next = pipe(decomposed, Chunk.appendAll(pipe(input, Chunk.drop(index + 1))))\n  return foldWeightedDecomposeFold(next, 0, s, cost, dirty, max, costFn, decompose, f)\n}\n\n/** @internal */\nexport const foldWeightedDecomposeEffect = <S, In, E, R, E2, R2, E3, R3>(\n  options: {\n    readonly initial: S\n    readonly maxCost: number\n    readonly cost: (s: S, input: In) => Effect.Effect<number, E, R>\n    readonly decompose: (input: In) => Effect.Effect<Chunk.Chunk<In>, E2, R2>\n    readonly body: (s: S, input: In) => Effect.Effect<S, E3, R3>\n  }\n): Sink.Sink<S, In, In, E | E2 | E3, R | R2 | R3> =>\n  suspend(() =>\n    new SinkImpl(\n      foldWeightedDecomposeEffectLoop(\n        options.initial,\n        options.maxCost,\n        options.cost,\n        options.decompose,\n        options.body,\n        0,\n        false\n      )\n    )\n  )\n\n/** @internal */\nexport const foldWeightedEffect = <S, In, E, R, E2, R2>(\n  options: {\n    readonly initial: S\n    readonly maxCost: number\n    readonly cost: (s: S, input: In) => Effect.Effect<number, E, R>\n    readonly body: (s: S, input: In) => Effect.Effect<S, E2, R2>\n  }\n): Sink.Sink<S, In, In, E | E2, R | R2> =>\n  foldWeightedDecomposeEffect({\n    ...options,\n    decompose: (input) => Effect.succeed(Chunk.of(input))\n  })\n\nconst foldWeightedDecomposeEffectLoop = <S, In, E, R, E2, R2, E3, R3>(\n  s: S,\n  max: number,\n  costFn: (s: S, input: In) => Effect.Effect<number, E, R>,\n  decompose: (input: In) => Effect.Effect<Chunk.Chunk<In>, E2, R2>,\n  f: (s: S, input: In) => Effect.Effect<S, E3, R3>,\n  cost: number,\n  dirty: boolean\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, E | E2 | E3, E | E2 | E3, S, unknown, R | R2 | R3> =>\n  core.readWith({\n    onInput: (input: Chunk.Chunk<In>) =>\n      pipe(\n        core.fromEffect(foldWeightedDecomposeEffectFold(s, max, costFn, decompose, f, input, dirty, cost, 0)),\n        core.flatMap(([nextS, nextCost, nextDirty, leftovers]) => {\n          if (Chunk.isNonEmpty(leftovers)) {\n            return pipe(core.write(leftovers), channel.zipRight(core.succeedNow(nextS)))\n          }\n          if (cost > max) {\n            return core.succeedNow(nextS)\n          }\n          return foldWeightedDecomposeEffectLoop(nextS, max, costFn, decompose, f, nextCost, nextDirty)\n        })\n      ),\n    onFailure: core.fail,\n    onDone: () => core.succeedNow(s)\n  })\n\n/** @internal */\nconst foldWeightedDecomposeEffectFold = <S, In, E, R, E2, R2, E3, R3>(\n  s: S,\n  max: number,\n  costFn: (s: S, input: In) => Effect.Effect<number, E, R>,\n  decompose: (input: In) => Effect.Effect<Chunk.Chunk<In>, E2, R2>,\n  f: (s: S, input: In) => Effect.Effect<S, E3, R3>,\n  input: Chunk.Chunk<In>,\n  dirty: boolean,\n  cost: number,\n  index: number\n): Effect.Effect<[S, number, boolean, Chunk.Chunk<In>], E | E2 | E3, R | R2 | R3> => {\n  if (index === input.length) {\n    return Effect.succeed([s, cost, dirty, Chunk.empty<In>()])\n  }\n  const elem = pipe(input, Chunk.unsafeGet(index))\n  return pipe(\n    costFn(s, elem),\n    Effect.map((newCost) => cost + newCost),\n    Effect.flatMap((total) => {\n      if (total <= max) {\n        return pipe(\n          f(s, elem),\n          Effect.flatMap((s) =>\n            foldWeightedDecomposeEffectFold(s, max, costFn, decompose, f, input, true, total, index + 1)\n          )\n        )\n      }\n      return pipe(\n        decompose(elem),\n        Effect.flatMap((decomposed) => {\n          if (decomposed.length <= 1 && !dirty) {\n            // If `elem` cannot be decomposed, we need to cross the `max` threshold. To\n            // minimize \"injury\", we only allow this when we haven't added anything else\n            // to the aggregate (dirty = false).\n            return pipe(\n              f(s, elem),\n              Effect.map((s) => [s, total, true, pipe(input, Chunk.drop(index + 1))])\n            )\n          }\n          if (decomposed.length <= 1 && dirty) {\n            // If the state is dirty and `elem` cannot be decomposed, we stop folding\n            // and include `elem` in th leftovers.\n            return Effect.succeed([s, cost, dirty, pipe(input, Chunk.drop(index))])\n          }\n          // `elem` got decomposed, so we will recurse with the decomposed elements pushed\n          // into the chunk we're processing and see if we can aggregate further.\n          const next = pipe(decomposed, Chunk.appendAll(pipe(input, Chunk.drop(index + 1))))\n          return foldWeightedDecomposeEffectFold(s, max, costFn, decompose, f, next, dirty, cost, 0)\n        })\n      )\n    })\n  )\n}\n\n/** @internal */\nexport const flatMap = dual<\n  <A, A1, In, In1 extends In, L1, E1, R1>(\n    f: (a: A) => Sink.Sink<A1, In1, L1, E1, R1>\n  ) => <L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A1, In & In1, L | L1, E1 | E, R1 | R>,\n  <A, In, L, E, R, A1, In1 extends In, L1, E1, R1>(\n    self: Sink.Sink<A, In, L, E, R>,\n    f: (a: A) => Sink.Sink<A1, In1, L1, E1, R1>\n  ) => Sink.Sink<A1, In & In1, L | L1, E1 | E, R1 | R>\n>(\n  2,\n  (self, f) => foldSink(self, { onFailure: fail, onSuccess: f })\n)\n\n/** @internal */\nexport const forEach = <In, X, E, R>(f: (input: In) => Effect.Effect<X, E, R>): Sink.Sink<void, In, never, E, R> => {\n  const process: Channel.Channel<never, Chunk.Chunk<In>, E, E, void, unknown, R> = core.readWithCause({\n    onInput: (input: Chunk.Chunk<In>) =>\n      pipe(core.fromEffect(Effect.forEach(input, (v) => f(v), { discard: true })), core.flatMap(() => process)),\n    onFailure: core.failCause,\n    onDone: () => core.void\n  })\n  return new SinkImpl(process)\n}\n\n/** @internal */\nexport const forEachChunk = <In, X, E, R>(\n  f: (input: Chunk.Chunk<In>) => Effect.Effect<X, E, R>\n): Sink.Sink<void, In, never, E, R> => {\n  const process: Channel.Channel<never, Chunk.Chunk<In>, E, E, void, unknown, R> = core.readWithCause({\n    onInput: (input: Chunk.Chunk<In>) => pipe(core.fromEffect(f(input)), core.flatMap(() => process)),\n    onFailure: core.failCause,\n    onDone: () => core.void\n  })\n  return new SinkImpl(process)\n}\n\n/** @internal */\nexport const forEachWhile = <In, E, R>(\n  f: (input: In) => Effect.Effect<boolean, E, R>\n): Sink.Sink<void, In, In, E, R> => {\n  const process: Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, E, E, void, unknown, R> = core.readWithCause({\n    onInput: (input: Chunk.Chunk<In>) => forEachWhileReader(f, input, 0, input.length, process),\n    onFailure: core.failCause,\n    onDone: () => core.void\n  })\n  return new SinkImpl(process)\n}\n\n/** @internal */\nconst forEachWhileReader = <In, E, R>(\n  f: (input: In) => Effect.Effect<boolean, E, R>,\n  input: Chunk.Chunk<In>,\n  index: number,\n  length: number,\n  cont: Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, E, E, void, unknown, R>\n): Channel.Channel<Chunk.Chunk<In>, Chunk.Chunk<In>, E, E, void, unknown, R> => {\n  if (index === length) {\n    return cont\n  }\n  return pipe(\n    core.fromEffect(f(pipe(input, Chunk.unsafeGet(index)))),\n    core.flatMap((bool) =>\n      bool ?\n        forEachWhileReader(f, input, index + 1, length, cont) :\n        core.write(pipe(input, Chunk.drop(index)))\n    ),\n    channel.catchAll((error) => pipe(core.write(pipe(input, Chunk.drop(index))), channel.zipRight(core.fail(error))))\n  )\n}\n\n/** @internal */\nexport const forEachChunkWhile = <In, E, R>(\n  f: (input: Chunk.Chunk<In>) => Effect.Effect<boolean, E, R>\n): Sink.Sink<void, In, In, E, R> => {\n  const reader: Channel.Channel<never, Chunk.Chunk<In>, E, E, void, unknown, R> = core.readWith({\n    onInput: (input: Chunk.Chunk<In>) =>\n      pipe(\n        core.fromEffect(f(input)),\n        core.flatMap((cont) => cont ? reader : core.void)\n      ),\n    onFailure: core.fail,\n    onDone: () => core.void\n  })\n  return new SinkImpl(reader)\n}\n\n/** @internal */\nexport const fromChannel = <L, In, E, A, R>(\n  channel: Channel.Channel<Chunk.Chunk<L>, Chunk.Chunk<In>, E, never, A, unknown, R>\n): Sink.Sink<A, In, L, E, R> => new SinkImpl(channel)\n\n/** @internal */\nexport const fromEffect = <A, E, R>(effect: Effect.Effect<A, E, R>): Sink.Sink<A, unknown, never, E, R> =>\n  new SinkImpl(core.fromEffect(effect))\n\n/** @internal */\nexport const fromPubSub = <In>(\n  pubsub: PubSub.PubSub<In>,\n  options?: {\n    readonly shutdown?: boolean | undefined\n  }\n): Sink.Sink<void, In> => fromQueue(pubsub, options)\n\n/** @internal */\nexport const fromPush = <In, L0, R0, L, R>(\n  push: Effect.Effect<\n    (_: Option.Option<Chunk.Chunk<In>>) => Effect.Effect<void, readonly [Either.Either<R0, L0>, Chunk.Chunk<L>], R>,\n    never,\n    R\n  >\n): Sink.Sink<R0, In, L, L0, Exclude<R, Scope.Scope>> =>\n  new SinkImpl(channel.unwrapScoped(pipe(push, Effect.map(fromPushPull))))\n\nconst fromPushPull = <In, Z, E, L, R>(\n  push: (\n    option: Option.Option<Chunk.Chunk<In>>\n  ) => Effect.Effect<void, readonly [Either.Either<Z, E>, Chunk.Chunk<L>], R>\n): Channel.Channel<Chunk.Chunk<L>, Chunk.Chunk<In>, E, never, Z, unknown, R> =>\n  core.readWith({\n    onInput: (input: Chunk.Chunk<In>) =>\n      channel.foldChannel(core.fromEffect(push(Option.some(input))), {\n        onFailure: ([either, leftovers]) =>\n          Either.match(either, {\n            onLeft: (error) => pipe(core.write(leftovers), channel.zipRight(core.fail(error))),\n            onRight: (z) => pipe(core.write(leftovers), channel.zipRight(core.succeedNow(z)))\n          }),\n        onSuccess: () => fromPushPull(push)\n      }),\n    onFailure: core.fail,\n    onDone: () =>\n      channel.foldChannel(core.fromEffect(push(Option.none())), {\n        onFailure: ([either, leftovers]) =>\n          Either.match(either, {\n            onLeft: (error) => pipe(core.write(leftovers), channel.zipRight(core.fail(error))),\n            onRight: (z) => pipe(core.write(leftovers), channel.zipRight(core.succeedNow(z)))\n          }),\n        onSuccess: () =>\n          core.fromEffect(\n            Effect.dieMessage(\n              \"BUG: Sink.fromPush - please report an issue at https://github.com/Effect-TS/effect/issues\"\n            )\n          )\n      })\n  })\n\n/** @internal */\nexport const fromQueue = <In>(\n  queue: Queue.Enqueue<In>,\n  options?: {\n    readonly shutdown?: boolean | undefined\n  }\n): Sink.Sink<void, In> =>\n  options?.shutdown ?\n    unwrapScoped(\n      Effect.map(\n        Effect.acquireRelease(Effect.succeed(queue), Queue.shutdown),\n        fromQueue\n      )\n    ) :\n    forEachChunk((input: Chunk.Chunk<In>) => pipe(Queue.offerAll(queue, input)))\n\n/** @internal */\nexport const head = <In>(): Sink.Sink<Option.Option<In>, In, In> =>\n  fold(\n    Option.none() as Option.Option<In>,\n    Option.isNone,\n    (option, input) =>\n      Option.match(option, {\n        onNone: () => Option.some(input),\n        onSome: () => option\n      })\n  )\n\n/** @internal */\nexport const ignoreLeftover = <A, In, L, E, R>(self: Sink.Sink<A, In, L, E, R>): Sink.Sink<A, In, never, E, R> =>\n  new SinkImpl(channel.drain(toChannel(self)))\n\n/** @internal */\nexport const last = <In>(): Sink.Sink<Option.Option<In>, In, In> =>\n  foldLeftChunks(Option.none<In>(), (s, input) => Option.orElse(Chunk.last(input), () => s))\n\n/** @internal */\nexport const leftover = <L>(chunk: Chunk.Chunk<L>): Sink.Sink<void, unknown, L> =>\n  new SinkImpl(core.suspend(() => core.write(chunk)))\n\n/** @internal */\nexport const map = dual<\n  <A, A2>(f: (a: A) => A2) => <In, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A2, In, L, E, R>,\n  <A, In, L, E, R, A2>(self: Sink.Sink<A, In, L, E, R>, f: (a: A) => A2) => Sink.Sink<A2, In, L, E, R>\n>(2, (self, f) => {\n  return new SinkImpl(pipe(toChannel(self), channel.map(f)))\n})\n\n/** @internal */\nexport const mapEffect = dual<\n  <A, A2, E2, R2>(\n    f: (a: A) => Effect.Effect<A2, E2, R2>\n  ) => <In, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A2, In, L, E2 | E, R2 | R>,\n  <A, In, L, E, R, A2, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    f: (a: A) => Effect.Effect<A2, E2, R2>\n  ) => Sink.Sink<A2, In, L, E2 | E, R2 | R>\n>(\n  2,\n  (self, f) => new SinkImpl(pipe(toChannel(self), channel.mapEffect(f)))\n)\n\n/** @internal */\nexport const mapError = dual<\n  <E, E2>(f: (error: E) => E2) => <A, In, L, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In, L, E2, R>,\n  <A, In, L, E, R, E2>(self: Sink.Sink<A, In, L, E, R>, f: (error: E) => E2) => Sink.Sink<A, In, L, E2, R>\n>(\n  2,\n  (self, f) => new SinkImpl(pipe(toChannel(self), channel.mapError(f)))\n)\n\n/** @internal */\nexport const mapLeftover = dual<\n  <L, L2>(f: (leftover: L) => L2) => <A, In, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In, L2, E, R>,\n  <A, In, L, E, R, L2>(self: Sink.Sink<A, In, L, E, R>, f: (leftover: L) => L2) => Sink.Sink<A, In, L2, E, R>\n>(\n  2,\n  (self, f) => new SinkImpl(pipe(toChannel(self), channel.mapOut(Chunk.map(f))))\n)\n\n/** @internal */\nexport const never: Sink.Sink<never, unknown> = fromEffect(Effect.never)\n\n/** @internal */\nexport const orElse = dual<\n  <A2, In2, L2, E2, R2>(\n    that: LazyArg<Sink.Sink<A2, In2, L2, E2, R2>>\n  ) => <A, In, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A2 | A, In & In2, L2 | L, E2 | E, R2 | R>,\n  <A, In, L, E, R, A2, In2, L2, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: LazyArg<Sink.Sink<A2, In2, L2, E2, R2>>\n  ) => Sink.Sink<A2 | A, In & In2, L2 | L, E2 | E, R2 | R>\n>(\n  2,\n  <A, In, L, E, R, A2, In2, L2, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: LazyArg<Sink.Sink<A2, In2, L2, E2, R2>>\n  ): Sink.Sink<A | A2, In & In2, L | L2, E | E2, R | R2> =>\n    new SinkImpl<A | A2, In & In2, L | L2, E | E2, R | R2>(\n      pipe(toChannel(self), channel.orElse(() => toChannel(that())))\n    )\n)\n\n/** @internal */\nexport const provideContext = dual<\n  <R>(context: Context.Context<R>) => <A, In, L, E>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In, L, E>,\n  <A, In, L, E, R>(self: Sink.Sink<A, In, L, E, R>, context: Context.Context<R>) => Sink.Sink<A, In, L, E>\n>(\n  2,\n  (self, context) => new SinkImpl(pipe(toChannel(self), core.provideContext(context)))\n)\n\n/** @internal */\nexport const race = dual<\n  <R1, E1, In1, L1, A1>(\n    that: Sink.Sink<A1, In1, L1, E1, R1>\n  ) => <A, In, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A1 | A, In & In1, L1 | L, E1 | E, R1 | R>,\n  <A, In, L, E, R, A1, In1, L1, E1, R1>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: Sink.Sink<A1, In1, L1, E1, R1>\n  ) => Sink.Sink<A1 | A, In & In1, L1 | L, E1 | E, R1 | R>\n>(\n  2,\n  (self, that) => pipe(self, raceBoth(that), map(Either.merge))\n)\n\n/** @internal */\nexport const raceBoth = dual<\n  <A1, In1, L1, E1, R1>(\n    that: Sink.Sink<A1, In1, L1, E1, R1>,\n    options?: {\n      readonly capacity?: number | undefined\n    }\n  ) => <A, In, L, E, R>(\n    self: Sink.Sink<A, In, L, E, R>\n  ) => Sink.Sink<Either.Either<A1, A>, In & In1, L1 | L, E1 | E, R1 | R>,\n  <A, In, L, E, R, A1, In1, L1, E1, R1>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: Sink.Sink<A1, In1, L1, E1, R1>,\n    options?: {\n      readonly capacity?: number | undefined\n    }\n  ) => Sink.Sink<Either.Either<A1, A>, In & In1, L1 | L, E1 | E, R1 | R>\n>(\n  (args) => isSink(args[1]),\n  (self, that, options) =>\n    raceWith(self, {\n      other: that,\n      onSelfDone: (selfDone) => mergeDecision.Done(Effect.map(selfDone, Either.left)),\n      onOtherDone: (thatDone) => mergeDecision.Done(Effect.map(thatDone, Either.right)),\n      capacity: options?.capacity ?? 16\n    })\n)\n\n/** @internal */\nexport const raceWith = dual<\n  <A2, In2, L2, E2, R2, A, E, A3, A4>(\n    options: {\n      readonly other: Sink.Sink<A2, In2, L2, E2, R2>\n      readonly onSelfDone: (exit: Exit.Exit<A, E>) => MergeDecision.MergeDecision<R2, E2, A2, E2 | E, A3>\n      readonly onOtherDone: (exit: Exit.Exit<A2, E2>) => MergeDecision.MergeDecision<R2, E, A, E2 | E, A4>\n      readonly capacity?: number | undefined\n    }\n  ) => <In, L, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A3 | A4, In & In2, L2 | L, E2 | E, R2 | R>,\n  <A, In, L, E, R, A2, In2, L2, E2, R2, A3, A4>(\n    self: Sink.Sink<A, In, L, E, R>,\n    options: {\n      readonly other: Sink.Sink<A2, In2, L2, E2, R2>\n      readonly onSelfDone: (exit: Exit.Exit<A, E>) => MergeDecision.MergeDecision<R2, E2, A2, E2 | E, A3>\n      readonly onOtherDone: (exit: Exit.Exit<A2, E2>) => MergeDecision.MergeDecision<R2, E, A, E2 | E, A4>\n      readonly capacity?: number | undefined\n    }\n  ) => Sink.Sink<A3 | A4, In & In2, L2 | L, E2 | E, R2 | R>\n>(\n  2,\n  <A, In, L, E, R, A2, In2, L2, E2, R2, A3, A4>(\n    self: Sink.Sink<A, In, L, E, R>,\n    options: {\n      readonly other: Sink.Sink<A2, In2, L2, E2, R2>\n      readonly onSelfDone: (exit: Exit.Exit<A, E>) => MergeDecision.MergeDecision<R2, E2, A2, E2 | E, A3>\n      readonly onOtherDone: (exit: Exit.Exit<A2, E2>) => MergeDecision.MergeDecision<R2, E, A, E2 | E, A4>\n      readonly capacity?: number | undefined\n    }\n  ): Sink.Sink<A3 | A4, In & In2, L2 | L, E2 | E, R2 | R> => {\n    function race(scope: Scope.Scope) {\n      return Effect.gen(function*() {\n        const pubsub = yield* PubSub.bounded<\n          Either.Either<Chunk.Chunk<In & In2>, Exit.Exit<unknown>>\n        >(options?.capacity ?? 16)\n        const subscription1 = yield* Scope.extend(PubSub.subscribe(pubsub), scope)\n        const subscription2 = yield* Scope.extend(PubSub.subscribe(pubsub), scope)\n        const reader = channel.toPubSub(pubsub)\n        const writer = channel.fromQueue(subscription1).pipe(\n          core.pipeTo(toChannel(self)),\n          channel.zipLeft(core.fromEffect(Queue.shutdown(subscription1))),\n          channel.mergeWith({\n            other: channel.fromQueue(subscription2).pipe(\n              core.pipeTo(toChannel(options.other)),\n              channel.zipLeft(core.fromEffect(Queue.shutdown(subscription2)))\n            ),\n            onSelfDone: options.onSelfDone,\n            onOtherDone: options.onOtherDone\n          })\n        )\n        const racedChannel = channel.mergeWith(reader, {\n          other: writer,\n          onSelfDone: () => mergeDecision.Await(identity),\n          onOtherDone: (exit) => mergeDecision.Done(exit)\n        }) as Channel.Channel<\n          Chunk.Chunk<L | L2>,\n          Chunk.Chunk<In & In2>,\n          E | E2,\n          never,\n          A3 | A4,\n          unknown,\n          R | R2\n        >\n        return new SinkImpl(racedChannel)\n      })\n    }\n    return unwrapScopedWith(race)\n  }\n)\n\n/** @internal */\nexport const refineOrDie = dual<\n  <E, E2>(\n    pf: (error: E) => Option.Option<E2>\n  ) => <A, In, L, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In, L, E2, R>,\n  <A, In, L, E, R, E2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    pf: (error: E) => Option.Option<E2>\n  ) => Sink.Sink<A, In, L, E2, R>\n>(\n  2,\n  (self, pf) => pipe(self, refineOrDieWith(pf, identity))\n)\n\n/** @internal */\nexport const refineOrDieWith = dual<\n  <E, E2>(\n    pf: (error: E) => Option.Option<E2>,\n    f: (error: E) => unknown\n  ) => <A, In, L, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In, L, E2, R>,\n  <A, In, L, E, R, E2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    pf: (error: E) => Option.Option<E2>,\n    f: (error: E) => unknown\n  ) => Sink.Sink<A, In, L, E2, R>\n>(\n  3,\n  (self, pf, f) => {\n    const newChannel = pipe(\n      self,\n      toChannel,\n      channel.catchAll((error) =>\n        Option.match(pf(error), {\n          onNone: () => core.failCauseSync(() => Cause.die(f(error))),\n          onSome: core.fail\n        })\n      )\n    )\n    return new SinkImpl(newChannel)\n  }\n)\n\n/** @internal */\nexport const service = <I, S>(\n  tag: Context.Tag<I, S>\n): Sink.Sink<S, unknown, never, never, I> => serviceWith(tag, identity)\n\n/** @internal */\nexport const serviceWith = <I, S, Z>(\n  tag: Context.Tag<I, S>,\n  f: (service: Types.NoInfer<S>) => Z\n): Sink.Sink<Z, unknown, never, never, I> => fromEffect(Effect.map(tag, f))\n\n/** @internal */\nexport const serviceWithEffect = <I, S, R, E, Z>(\n  tag: Context.Tag<I, S>,\n  f: (service: Types.NoInfer<S>) => Effect.Effect<Z, E, R>\n): Sink.Sink<Z, unknown, never, E, R | I> => fromEffect(Effect.flatMap(tag, f))\n\n/** @internal */\nexport const serviceWithSink = <I, S, R, E, In, L, Z>(\n  tag: Context.Tag<I, S>,\n  f: (service: Types.NoInfer<S>) => Sink.Sink<Z, In, L, E, R>\n): Sink.Sink<Z, In, L, E, R | I> =>\n  new SinkImpl(pipe(Effect.map(tag, (service) => toChannel(f(service))), channel.unwrap))\n\n/** @internal */\nexport const some = <In>(predicate: Predicate<In>): Sink.Sink<boolean, In, In> =>\n  fold(false, (bool) => !bool, (acc, input) => acc || predicate(input))\n\n/** @internal */\nexport const splitWhere = dual<\n  <In>(f: Predicate<In>) => <A, L extends In, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In, In, E, R>,\n  <A, In, L extends In, E, R>(self: Sink.Sink<A, In, L, E, R>, f: Predicate<In>) => Sink.Sink<A, In, In, E, R>\n>(2, <A, In, L extends In, E, R>(self: Sink.Sink<A, In, L, E, R>, f: Predicate<In>): Sink.Sink<A, In, In, E, R> => {\n  const newChannel = pipe(\n    core.fromEffect(Ref.make(Chunk.empty<In>())),\n    core.flatMap((ref) =>\n      pipe(\n        splitWhereSplitter<In, E>(false, ref, f),\n        channel.pipeToOrFail(toChannel(self)),\n        core.collectElements,\n        core.flatMap(([leftovers, z]) =>\n          pipe(\n            core.fromEffect(Ref.get(ref)),\n            core.flatMap((leftover) =>\n              pipe(\n                core.write<Chunk.Chunk<In>>(pipe(leftover, Chunk.appendAll(Chunk.flatten(leftovers)))),\n                channel.zipRight(core.succeed(z))\n              )\n            )\n          )\n        )\n      )\n    )\n  )\n  return new SinkImpl(newChannel)\n})\n\n/** @internal */\nconst splitWhereSplitter = <A, E>(\n  written: boolean,\n  leftovers: Ref.Ref<Chunk.Chunk<A>>,\n  f: Predicate<A>\n): Channel.Channel<Chunk.Chunk<A>, Chunk.Chunk<A>, E, never, unknown, unknown> =>\n  core.readWithCause({\n    onInput: (input) => {\n      if (Chunk.isEmpty(input)) {\n        return splitWhereSplitter(written, leftovers, f)\n      }\n      if (written) {\n        const index = indexWhere(input, f)\n        if (index === -1) {\n          return channel.zipRight(\n            core.write(input),\n            splitWhereSplitter<A, E>(true, leftovers, f)\n          )\n        }\n        const [left, right] = Chunk.splitAt(input, index)\n        return channel.zipRight(\n          core.write(left),\n          core.fromEffect(Ref.set(leftovers, right))\n        )\n      }\n      const index = indexWhere(input, f, 1)\n      if (index === -1) {\n        return channel.zipRight(\n          core.write(input),\n          splitWhereSplitter<A, E>(true, leftovers, f)\n        )\n      }\n      const [left, right] = pipe(input, Chunk.splitAt(Math.max(index, 1)))\n      return channel.zipRight(core.write(left), core.fromEffect(Ref.set(leftovers, right)))\n    },\n    onFailure: core.failCause,\n    onDone: core.succeed\n  })\n\n/** @internal */\nconst indexWhere = <A>(self: Chunk.Chunk<A>, predicate: Predicate<A>, from = 0): number => {\n  const iterator = self[Symbol.iterator]()\n  let index = 0\n  let result = -1\n  let next: IteratorResult<A, any>\n  while (result < 0 && (next = iterator.next()) && !next.done) {\n    const a = next.value\n    if (index >= from && predicate(a)) {\n      result = index\n    }\n    index = index + 1\n  }\n  return result\n}\n\n/** @internal */\nexport const succeed = <A>(a: A): Sink.Sink<A, unknown> => new SinkImpl(core.succeed(a))\n\n/** @internal */\nexport const sum: Sink.Sink<number, number> = foldLeftChunks(\n  0,\n  (acc, chunk) => acc + Chunk.reduce(chunk, 0, (s, a) => s + a)\n)\n\n/** @internal */\nexport const summarized = dual<\n  <A2, E2, R2, A3>(\n    summary: Effect.Effect<A2, E2, R2>,\n    f: (start: A2, end: A2) => A3\n  ) => <A, In, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<[A, A3], In, L, E2 | E, R2 | R>,\n  <A, In, L, E, R, A2, E2, R2, A3>(\n    self: Sink.Sink<A, In, L, E, R>,\n    summary: Effect.Effect<A2, E2, R2>,\n    f: (start: A2, end: A2) => A3\n  ) => Sink.Sink<[A, A3], In, L, E2 | E, R2 | R>\n>(\n  3,\n  (self, summary, f) => {\n    const newChannel = pipe(\n      core.fromEffect(summary),\n      core.flatMap((start) =>\n        pipe(\n          self,\n          toChannel,\n          core.flatMap((done) =>\n            pipe(\n              core.fromEffect(summary),\n              channel.map((end) => [done, f(start, end)])\n            )\n          )\n        )\n      )\n    )\n    return new SinkImpl(newChannel)\n  }\n)\n\n/** @internal */\nexport const sync = <A>(evaluate: LazyArg<A>): Sink.Sink<A, unknown> => new SinkImpl(core.sync(evaluate))\n\n/** @internal */\nexport const take = <In>(n: number): Sink.Sink<Chunk.Chunk<In>, In, In> =>\n  pipe(\n    foldChunks<Chunk.Chunk<In>, In>(\n      Chunk.empty(),\n      (chunk) => chunk.length < n,\n      (acc, chunk) => pipe(acc, Chunk.appendAll(chunk))\n    ),\n    flatMap((acc) => {\n      const [taken, leftover] = pipe(acc, Chunk.splitAt(n))\n      return new SinkImpl(pipe(core.write(leftover), channel.zipRight(core.succeedNow(taken))))\n    })\n  )\n\n/** @internal */\nexport const toChannel = <A, In, L, E, R>(\n  self: Sink.Sink<A, In, L, E, R>\n): Channel.Channel<Chunk.Chunk<L>, Chunk.Chunk<In>, E, never, A, unknown, R> =>\n  Effect.isEffect(self) ?\n    toChannel(fromEffect(self as Effect.Effect<A, E, R>)) :\n    (self as SinkImpl<A, In, L, E, R>).channel\n\n/** @internal */\nexport const unwrap = <A, In, L, E2, R2, E, R>(\n  effect: Effect.Effect<Sink.Sink<A, In, L, E2, R2>, E, R>\n): Sink.Sink<A, In, L, E | E2, R | R2> =>\n  new SinkImpl(\n    channel.unwrap(pipe(effect, Effect.map((sink) => toChannel(sink))))\n  )\n\n/** @internal */\nexport const unwrapScoped = <A, In, L, E, R>(\n  effect: Effect.Effect<Sink.Sink<A, In, L, E, R>, E, R>\n): Sink.Sink<A, In, L, E, Exclude<R, Scope.Scope>> =>\n  new SinkImpl(\n    channel.unwrapScoped(effect.pipe(\n      Effect.map((sink) => toChannel(sink))\n    ))\n  )\n\n/** @internal */\nexport const unwrapScopedWith = <A, In, L, E, R>(\n  f: (scope: Scope.Scope) => Effect.Effect<Sink.Sink<A, In, L, E, R>, E, R>\n): Sink.Sink<A, In, L, E, R> =>\n  new SinkImpl(\n    channel.unwrapScopedWith((scope) =>\n      f(scope).pipe(\n        Effect.map((sink) => toChannel(sink))\n      )\n    )\n  )\n\n/** @internal */\nexport const withDuration = <A, In, L, E, R>(\n  self: Sink.Sink<A, In, L, E, R>\n): Sink.Sink<[A, Duration.Duration], In, L, E, R> =>\n  pipe(self, summarized(Clock.currentTimeMillis, (start, end) => Duration.millis(end - start)))\n\n/** @internal */\nexport const zip = dual<\n  <A2, In, In2 extends In, L2, E2, R2>(\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ) => <A, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<[A, A2], In & In2, L | L2, E2 | E, R2 | R>,\n  <A, In, L, E, R, A2, In2 extends In, L2, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ) => Sink.Sink<[A, A2], In & In2, L | L2, E2 | E, R2 | R>\n>(\n  (args) => isSink(args[1]),\n  <A, In, L, E, R, A2, In2 extends In, L2, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ): Sink.Sink<[A, A2], In & In2, L | L2, E2 | E, R2 | R> => zipWith(self, that, (z, z2) => [z, z2], options)\n)\n\n/** @internal */\nexport const zipLeft = dual<\n  <A2, In, In2 extends In, L2, E2, R2>(\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ) => <A, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A, In & In2, L | L2, E2 | E, R2 | R>,\n  <A, In, L, E, R, A2, In2 extends In, L2, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ) => Sink.Sink<A, In & In2, L | L2, E2 | E, R2 | R>\n>(\n  (args) => isSink(args[1]),\n  <A, In, L, E, R, A2, In2 extends In, L2, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ): Sink.Sink<A, In & In2, L | L2, E2 | E, R2 | R> => zipWith(self, that, (z, _) => z, options)\n)\n\n/** @internal */\nexport const zipRight = dual<\n  <A2, In, In2 extends In, L2, E2, R2>(\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ) => <A, L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A2, In & In2, L | L2, E2 | E, R2 | R>,\n  <A, In, L, E, R, A2, In2 extends In, L2, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ) => Sink.Sink<A2, In & In2, L | L2, E2 | E, R2 | R>\n>(\n  (args) => isSink(args[1]),\n  <A, In, L, E, R, A2, In2 extends In, L2, E2, R2>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ): Sink.Sink<A2, In & In2, L | L2, E2 | E, R2 | R> => zipWith(self, that, (_, z2) => z2, options)\n)\n\n/** @internal */\nexport const zipWith = dual<\n  <A2, In, In2 extends In, L2, E2, R2, A, A3>(\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    f: (a: A, a2: A2) => A3,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ) => <L, E, R>(self: Sink.Sink<A, In, L, E, R>) => Sink.Sink<A3, In & In2, L | L2, E2 | E, R2 | R>,\n  <A, In, L, E, R, A2, In2 extends In, L2, E2, R2, A3>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    f: (a: A, a2: A2) => A3,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ) => Sink.Sink<A3, In & In2, L | L2, E2 | E, R2 | R>\n>(\n  (args) => isSink(args[1]),\n  <A, In, L, E, R, A2, In2 extends In, L2, E2, R2, A3>(\n    self: Sink.Sink<A, In, L, E, R>,\n    that: Sink.Sink<A2, In2, L2, E2, R2>,\n    f: (a: A, a2: A2) => A3,\n    options?: {\n      readonly concurrent?: boolean | undefined\n    }\n  ): Sink.Sink<A3, In & In2, L | L2, E2 | E, R2 | R> =>\n    options?.concurrent ?\n      raceWith(self, {\n        other: that,\n        onSelfDone: Exit.match({\n          onFailure: (cause) => mergeDecision.Done(Effect.failCause(cause)),\n          onSuccess: (leftZ) =>\n            mergeDecision.Await<R | R2, E2, A2, E | E2, A3>(\n              Exit.match({\n                onFailure: Effect.failCause,\n                onSuccess: (rightZ) => Effect.succeed(f(leftZ, rightZ))\n              })\n            )\n        }),\n        onOtherDone: Exit.match({\n          onFailure: (cause) => mergeDecision.Done(Effect.failCause(cause)),\n          onSuccess: (rightZ) =>\n            mergeDecision.Await<R | R2, E, A, E | E2, A3>(\n              Exit.match({\n                onFailure: Effect.failCause,\n                onSuccess: (leftZ) => Effect.succeed(f(leftZ, rightZ))\n              })\n            )\n        })\n      }) :\n      flatMap(self, (z) => map(that, (z2) => f(z, z2)))\n)\n\n// Circular with Channel\n\n/** @internal */\nexport const channelToSink = <OutElem, InElem, OutErr, InErr, OutDone, Env>(\n  self: Channel.Channel<Chunk.Chunk<OutElem>, Chunk.Chunk<InElem>, OutErr, InErr, OutDone, unknown, Env>\n): Sink.Sink<OutDone, InElem, OutElem, OutErr, Env> => new SinkImpl(self)\n\n// Constants\n\n/** @internal */\nexport const count: Sink.Sink<number, unknown> = foldLeftChunks(\n  0,\n  (acc, chunk) => acc + chunk.length\n)\n\n/** @internal */\nexport const mkString: Sink.Sink<string, unknown> = suspend(() => {\n  const strings: Array<string> = []\n  return pipe(\n    foldLeftChunks<void, unknown>(void 0, (_, elems) =>\n      Chunk.map(elems, (elem) => {\n        strings.push(String(elem))\n      })),\n    map(() => strings.join(\"\"))\n  )\n})\n\n/** @internal */\nexport const timed: Sink.Sink<Duration.Duration, unknown> = pipe(\n  withDuration(drain),\n  map((tuple) => tuple[1])\n)\n"], "names": ["Arr", "Cause", "Chunk", "Clock", "Duration", "Effect", "Either", "Exit", "constTrue", "dual", "identity", "pipe", "HashMap", "HashSet", "Option", "pipeArguments", "hasProperty", "PubSub", "Queue", "Ref", "<PERSON><PERSON>", "channel", "mergeDecision", "core", "SinkTypeId", "Symbol", "for", "sink<PERSON><PERSON>ce", "_A", "_", "_In", "_L", "_E", "_R", "SinkImpl", "constructor", "arguments", "isSink", "u", "suspend", "evaluate", "toChannel", "as", "self", "a", "map", "collectAll", "collectAllLoop", "empty", "acc", "readWithCause", "onInput", "chunk", "appendAll", "onFailure", "failCause", "onDone", "succeed", "collectAllN", "n", "fromChannel", "collectAllNLoop", "collected", "leftovers", "splitAt", "length", "isEmpty", "flatMap", "write", "collectAllFrom", "collectAllWhileWith", "initial", "while", "body", "append", "collectAllToMap", "key", "merge", "foldLeftChunks", "reduce", "input", "k", "v", "has", "unsafeGet", "set", "collectAllToMapN", "foldWeighted", "maxCost", "cost", "collectAllToSet", "add", "collectAllToSetN", "collectAllUntil", "p", "fold", "tuple", "collectAllUntilEffect", "foldEffect", "bool", "collectAllWhile", "predicate", "collectAllWhile<PERSON><PERSON><PERSON>", "done", "readWith", "toReadonlyArray", "span", "unsafeFromArray", "zipRight", "fail", "collectAllWhileEffect", "collectAllWhileEffectReader", "fromEffect", "<PERSON><PERSON><PERSON><PERSON>", "drop", "options", "refs", "make", "zip", "newChannel", "leftoversRef", "upstreamDoneRef", "upstreamMarker", "pipeTo", "bufferChunk", "collectAllWhileWithLoop", "currentResult", "f", "doneCollect", "foldChannel", "onSuccess", "doneValue", "flatten", "get", "upstreamDone", "accumulatedResult", "collectLeftover", "collectElements", "chunks", "z", "mapInput", "mapInputChunks", "mapInputEffect", "mapInputChunksEffect", "for<PERSON>ach", "loop", "pipeToOrFail", "die", "defect", "dieMessage", "message", "RuntimeException", "dieSync", "failCauseSync", "dimap", "dimapEffect", "mapEffect", "dimapChunks", "dimapChunksEffect", "drain", "identityChannel", "dropLoop", "dropped", "leftover", "Math", "max", "more", "void", "dropUntil", "<PERSON><PERSON><PERSON><PERSON>", "dropUntilEffect", "dropUntilEffectReader", "unwrap", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out", "<PERSON><PERSON><PERSON>", "dropWhileEffect", "dropWhileEffectReader", "ensuring", "finalizer", "ensuringWith", "context", "contextWith", "contextWithEffect", "contextWithSink", "every", "e", "failSync", "cause", "filterInput", "filter", "filterInputEffect", "findEffect", "satisfied", "some", "none", "s", "contFn", "foldReader", "nextS", "foldChunkSplit", "isNonEmpty", "index", "s1", "foldSink", "error", "ref", "refReader", "sync", "writeChunk", "passthrough", "continuationSink", "newLeftovers", "z1", "foldChunks", "foldChunksReader", "foldChunksEffect", "foldChunksEffectReader", "foldEffectReader", "foldChunkSplitEffect", "match", "onNone", "onSome", "foldChunkSplitEffectInternal", "foldLeft", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foldLeftChunksEffect", "foldLeftEffect", "foldUntil", "output", "count", "foldUntilEffect", "foldWeightedDecompose", "decompose", "of", "foldWeightedDecomposeLoop", "dirty", "costFn", "nextCost", "nextDirty", "foldWeightedDecomposeFold", "elem", "total", "decomposed", "next", "foldWeightedDecomposeEffect", "foldWeightedDecomposeEffectLoop", "foldWeightedEffect", "foldWeightedDecomposeEffectFold", "newCost", "process", "discard", "forEachChunk", "forEach<PERSON><PERSON>e", "for<PERSON>ach<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cont", "catchAll", "forEachChunkWhile", "reader", "effect", "fromPubSub", "pubsub", "fromQueue", "fromPush", "push", "unwrapScoped", "fromPushPull", "either", "onLeft", "onRight", "queue", "shutdown", "acquireRelease", "offerAll", "head", "isNone", "option", "last", "orElse", "mapError", "mapLeftover", "mapOut", "never", "that", "provideContext", "race", "raceBoth", "args", "raceWith", "other", "onSelfDone", "selfDone", "Done", "left", "onOtherDone", "thatDone", "right", "capacity", "scope", "gen", "bounded", "subscription1", "extend", "subscribe", "subscription2", "toPubSub", "writer", "zipLeft", "mergeWith", "racedChannel", "Await", "exit", "unwrapScopedWith", "refineOrDie", "pf", "refineOrDieWith", "service", "tag", "serviceWith", "serviceWithEffect", "serviceWithSink", "splitWhere", "splitWhereSplitter", "written", "indexWhere", "from", "iterator", "result", "value", "sum", "summarized", "summary", "start", "end", "take", "taken", "isEffect", "sink", "withDuration", "currentTimeMillis", "millis", "zipWith", "z2", "concurrent", "leftZ", "rightZ", "channelToSink", "mkString", "strings", "elems", "String", "join", "timed"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,GAAG,MAAM,aAAa;AAClC,OAAO,KAAKC,KAAK,MAAM,aAAa;AAEpC,OAAO,KAAKC,KAAK,MAAM,aAAa;AACpC,OAAO,KAAKC,KAAK,MAAM,aAAa;AAEpC,OAAO,KAAKC,QAAQ,MAAM,gBAAgB;AAC1C,OAAO,KAAKC,MAAM,MAAM,cAAc;AACtC,OAAO,KAAKC,MAAM,MAAM,cAAc;AACtC,OAAO,KAAKC,IAAI,MAAM,YAAY;AAClC,SAASC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,gBAAgB;AAEhE,OAAO,KAAKC,OAAO,MAAM,eAAe;AACxC,OAAO,KAAKC,OAAO,MAAM,eAAe;AAExC,OAAO,KAAKC,MAAM,MAAM,cAAc;AACtC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,WAAW,QAAyC,iBAAiB;AAC9E,OAAO,KAAKC,MAAM,MAAM,cAAc;AACtC,OAAO,KAAKC,KAAK,MAAM,aAAa;AACpC,OAAO,KAAKC,GAAG,MAAM,WAAW;AAChC,OAAO,KAAKC,KAAK,MAAM,aAAa;AAGpC,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,OAAO,KAAKC,aAAa,MAAM,4BAA4B;AAC3D,OAAO,KAAKC,IAAI,MAAM,kBAAkB;;;;;;;;;;;;;;;;;;;;;;AAGjC,MAAMC,UAAU,GAAA,WAAA,GAAoBC,MAAM,CAACC,GAAG,CAAC,aAAa,CAAoB;AAEvF,MAAMC,YAAY,GAAG;IACnB,kBAAA,GACAC,EAAE,GAAGC,CAAQ,GAAKA,CAAC;IACnB,kBAAA,GACAC,GAAG,GAAGD,CAAU,GAAKA,CAAC;IACtB,kBAAA,GACAE,EAAE,GAAGF,CAAQ,GAAKA,CAAC;IACnB,kBAAA,GACAG,EAAE,GAAGH,CAAQ,GAAKA,CAAC;IACnB,kBAAA,GACAI,EAAE,GAAGJ,CAAQ,GAAKA;CACnB;AAGK,MAAOK,QAAQ;IAKRb,OAAA,CAAA;IAFF,CAACG,UAAU,CAAA,GAAIG,YAAY,CAAA;IACpCQ,YACWd,OAAkF,CAAA;QAAlF,IAAA,CAAAA,OAAO,GAAPA,OAAO;IAElB;IACAV,IAAIA,CAAA,EAAA;QACF,6JAAOI,gBAAa,AAAbA,EAAc,IAAI,EAAEqB,SAAS,CAAC;IACvC;;AAIK,MAAMC,MAAM,IAAIC,CAAU,0JAC/BtB,cAAW,AAAXA,EAAYsB,CAAC,EAAEd,UAAU,CAAC;AAGrB,MAAMe,OAAO,IAAoBC,QAA4C,GAClF,IAAIN,QAAQ,yKAACX,IAAI,CAACgB,KAAAA,AAAO,EAAC,IAAME,SAAS,CAACD,QAAQ,EAAE,CAAC,CAAC,CAAC;AAGlD,MAAME,EAAE,GAAA,WAAA,wJAAGjC,QAAAA,AAAI,EAIpB,CAAC,EACD,CAACkC,IAAI,EAAEC,CAAC,yJAAKjC,OAAAA,AAAI,EAACgC,IAAI,EAAEE,GAAG,CAAC,IAAMD,CAAC,CAAC,CAAC,CACtC;AAGM,MAAME,UAAU,GAAGA,CAAA,GAA0C,IAAIZ,QAAQ,CAACa,cAAc,KAAC7C,KAAK,CAAC8C,iJAAAA,AAAK,EAAE,CAAC,CAAC;AAE/G,cAAA,GACA,MAAMD,cAAc,IAClBE,GAAoB,OAEpB1B,IAAI,CAAC2B,+KAAAA,AAAa,EAAC;QACjBC,OAAO,GAAGC,KAAsB,GAAKL,cAAc,uJAACpC,OAAI,AAAJA,EAAKsC,GAAG,qJAAE/C,KAAK,CAACmD,MAAAA,AAAS,EAACD,KAAK,CAAC,CAAC,CAAC;QACtFE,SAAS,sKAAE/B,IAAI,CAACgC,OAAS;QACzBC,MAAM,EAAEA,CAAA,2KAAMjC,IAAI,CAACkC,KAAAA,AAAO,EAACR,GAAG;KAC/B,CAAC;AAGG,MAAMS,WAAW,IAAQC,CAAS,GACvCpB,OAAO,CAAC,IAAMqB,WAAW,CAACC,eAAe,CAACF,CAAC,oJAAEzD,KAAK,CAAC8C,GAAAA,AAAK,EAAE,CAAC,CAAC,CAAC;AAE/D,cAAA,GACA,MAAMa,eAAe,GAAGA,CACtBF,CAAS,EACTV,GAAoB,2KAEpB1B,IAAI,CAAC2B,WAAAA,AAAa,EAAC;QACjBC,OAAO,EAAGC,KAAsB,IAAI;YAClC,MAAM,CAACU,SAAS,EAAEC,SAAS,CAAC,sJAAG7D,KAAK,CAAC8D,IAAAA,AAAO,EAACZ,KAAK,EAAEO,CAAC,CAAC;YACtD,IAAIG,SAAS,CAACG,MAAM,GAAGN,CAAC,EAAE;gBACxB,OAAOE,eAAe,CAACF,CAAC,GAAGG,SAAS,CAACG,MAAM,EAAE/D,KAAK,CAACmD,yJAAAA,AAAS,EAACJ,GAAG,EAAEa,SAAS,CAAC,CAAC;YAC/E;YACA,KAAI5D,KAAK,CAACgE,sJAAAA,AAAO,EAACH,SAAS,CAAC,EAAE;gBAC5B,+KAAOxC,IAAI,CAACkC,KAAAA,AAAO,qJAACvD,KAAK,CAACmD,MAAAA,AAAS,EAACJ,GAAG,EAAEa,SAAS,CAAC,CAAC;YACtD;YACA,+KAAOvC,IAAI,CAAC4C,KAAAA,AAAO,0KAAC5C,IAAI,CAAC6C,GAAAA,AAAK,EAACL,SAAS,CAAC,EAAE,4KAAMxC,IAAI,CAACkC,KAAAA,AAAO,qJAACvD,KAAK,CAACmD,MAAAA,AAAS,EAACJ,GAAG,EAAEa,SAAS,CAAC,CAAC,CAAC;QACjG,CAAC;QACDR,SAAS,sKAAE/B,IAAI,CAACgC,OAAS;QACzBC,MAAM,EAAEA,CAAA,2KAAMjC,IAAI,CAACkC,KAAAA,AAAO,EAACR,GAAG;KAC/B,CAAC;AAGG,MAAMoB,cAAc,GACzB1B,IAA+B,IAE/B2B,mBAAmB,CAAC3B,IAAI,EAAE;QACxB4B,OAAO,EAAErE,KAAK,CAAC8C,qJAAAA,AAAK,EAAK;QACzBwB,KAAK,oJAAEhE,YAAS;QAChBiE,IAAI,EAAEA,CAACrB,KAAK,EAAER,CAAC,GAAKjC,6JAAAA,AAAI,EAACyC,KAAK,qJAAElD,KAAK,CAACwE,GAAM,AAANA,EAAO9B,CAAC,CAAC;KAChD,CAAC;AAGG,MAAM+B,eAAe,GAAGA,CAC7BC,GAAqB,EACrBC,KAA2B,KACc;IACzC,6JAAOlE,OAAAA,AAAI,EACTmE,cAAc,sJAAClE,OAAO,CAACoC,AAAK,EAAS,GAAE,CAACH,GAAG,EAAEO,KAAK,yJAChDzC,OAAAA,AAAI,EACFyC,KAAK,qJACLlD,KAAK,CAAC6E,GAAAA,AAAM,EAAClC,GAAG,EAAE,CAACA,GAAG,EAAEmC,KAAK,KAAI;YAC/B,MAAMC,CAAC,GAAML,GAAG,CAACI,KAAK,CAAC;YACvB,MAAME,CAAC,yJAAOvE,OAAAA,AAAI,EAACkC,GAAG,uJAAEjC,MAAQuE,AAAG,CAAJ,CAACA,AAAIF,CAAC,CAAC,CAAC,GACrCJ,KAAK,uJAAClE,OAAAA,AAAI,EAACkC,GAAG,uJAAEjC,OAAO,CAACwE,IAAAA,AAAS,EAACH,CAAC,CAAC,CAAC,EAAED,KAAK,CAAC,GAC7CA,KAAK;YACP,OAAOrE,6JAAAA,AAAI,EAACkC,GAAG,EAAEjC,OAAO,CAACyE,mJAAAA,AAAG,EAACJ,CAAC,EAAEC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CACH,CAAC,CACL;AACH,CAAC;AAGM,MAAMI,gBAAgB,GAAGA,CAC9B3B,CAAS,EACTiB,GAAqB,EACrBC,KAA2B,KACkB;IAC7C,OAAOU,YAAY,CAA6B;QAC9ChB,OAAO,uJAAE3D,OAAO,CAACoC,AAAK,AAALA,EAAO;QACxBwC,OAAO,EAAE7B,CAAC;QACV8B,IAAI,EAAEA,CAACxC,GAAG,EAAE+B,KAAK,yJAAKrE,OAAAA,AAAI,EAACsC,GAAG,uJAAErC,MAAQuE,AAAG,CAAJ,CAACA,AAAIP,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAChEP,IAAI,EAAEA,CAACxB,GAAG,EAAE+B,KAAK,KAAI;YACnB,MAAMC,CAAC,GAAML,GAAG,CAACI,KAAK,CAAC;YACvB,MAAME,CAAC,yJAAOvE,OAAAA,AAAI,EAACsC,GAAG,uJAAErC,MAAQuE,AAAG,CAAJ,CAACA,AAAIF,CAAC,CAAC,CAAC,GACrCJ,KAAK,uJAAClE,OAAAA,AAAI,EAACsC,GAAG,uJAAErC,OAAO,CAACwE,IAAAA,AAAS,EAACH,CAAC,CAAC,CAAC,EAAED,KAAK,CAAC,GAC7CA,KAAK;YACP,6JAAOrE,OAAI,AAAJA,EAAKsC,GAAG,uJAAErC,MAAQyE,AAAG,CAAJ,CAACA,AAAIJ,CAAC,EAAEC,CAAC,CAAC,CAAC;QACrC;KACD,CAAC;AACJ,CAAC;AAGM,MAAMQ,eAAe,GAAGA,CAAA,GAC7BZ,cAAc,sJACZjE,OAAO,CAACmC,AAAK,EAAE,GACf,CAACC,GAAG,EAAEG,KAAK,wJAAKzC,QAAAA,AAAI,EAACyC,KAAK,qJAAElD,KAAK,CAAC6E,GAAAA,AAAM,EAAC9B,GAAG,EAAE,CAACA,GAAG,EAAE+B,KAAK,OAAKrE,yJAAI,AAAJA,EAAKsC,GAAG,uJAAEpC,MAAQ8E,AAAG,CAAJ,CAAKX,AAAJW,KAAS,CAAC,CAAC,CAAC,CAAC,CAC9F;AAGI,MAAMC,gBAAgB,GAAQjC,CAAS,IAC5C4B,YAAY,CAA0B;QACpChB,OAAO,EAAE1D,OAAO,CAACmC,qJAAAA,AAAK,EAAE;QACxBwC,OAAO,EAAE7B,CAAC;QACV8B,IAAI,EAAEA,CAACxC,GAAG,EAAE+B,KAAK,OAAKnE,OAAO,CAACsE,+IAAAA,AAAG,EAAClC,GAAG,EAAE+B,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;QACrDP,IAAI,EAAEA,CAACxB,GAAG,EAAE+B,KAAK,wJAAKnE,MAAQ8E,AAAG,CAAJ,CAACA,AAAI1C,GAAG,EAAE+B,KAAK;KAC7C,CAAC;AAGG,MAAMa,eAAe,IAAQC,CAAgB,IAAwC;IAC1F,6JAAOnF,OAAAA,AAAI,EACToF,IAAI,CACF;2JAAC7F,KAAK,CAAC8C,EAAAA,AAAK,EAAE;QAAE,IAAI;KAAC,GACpBgD,KAAK,GAAKA,KAAK,CAAC,CAAC,CAAC,EACnB,CAAC,CAAC5C,KAAK,EAAEvB,CAAC,CAAC,EAAEmD,KAAK,GAAK;kKAACrE,OAAAA,AAAI,EAACyC,KAAK,qJAAElD,KAAK,CAACwE,GAAAA,AAAM,EAACM,KAAK,CAAC,CAAC;YAAE,CAACc,CAAC,CAACd,KAAK,CAAC;SAAC,CACrE,EACDnC,GAAG,EAAEmD,KAAK,GAAKA,KAAK,CAAC,CAAC,CAAC,CAAC,CACzB;AACH,CAAC;AAGM,MAAMC,qBAAqB,GAAcH,CAA8C,IAAI;IAChG,6JAAOnF,OAAAA,AAAI,EACTuF,UAAU,CACR;2JAAChG,KAAK,CAAC8C,EAAAA,AAAK,EAAE;QAAE,IAAI;KAAC,GACpBgD,KAAK,GAAKA,KAAK,CAAC,CAAC,CAAC,EACnB,CAAC,CAAC5C,KAAK,EAAEvB,CAAC,CAAC,EAAEmD,KAAK,yJAAKrE,OAAAA,AAAI,EAACmF,CAAC,CAACd,KAAK,CAAC,sJAAE3E,MAAM,AAACwC,AAAG,CAAHA,EAAKsD,IAAI,GAAK;sKAACxF,OAAAA,AAAI,EAACyC,KAAK,qJAAElD,KAAK,CAACwE,GAAM,AAANA,EAAOM,KAAK,CAAC,CAAC;gBAAE,CAACmB,IAAI;aAAC,CAAC,CAAC,CACvG,EACDtD,GAAG,CAAEmD,KAAK,IAAKA,KAAK,CAAC,CAAC,CAAC,CAAC,CACzB;AACH,CAAC;AAGM,MAAMI,eAAe,IAGnBC,SAAwB,GAC/BzC,WAAW,CAAC0C,qBAAqB,CAACD,SAAS,qJAAEnG,KAAK,CAAC8C,EAAAA,AAAK,EAAE,CAAC,CAAC;AAE9D,cAAA,GACA,MAAMsD,qBAAqB,GAAGA,CAC5BD,SAAwB,EACxBE,IAAqB,2KAErBhF,IAAI,CAACiF,MAAAA,AAAQ,EAAC;QACZrD,OAAO,GAAG6B,KAAsB,IAAI;YAClC,MAAM,CAAClB,SAAS,EAAEC,SAAS,CAAC,yJAAGpD,OAAAA,AAAI,qJAACT,KAAK,CAACuG,YAAAA,AAAe,EAACzB,KAAK,CAAC,qJAAEhF,GAAG,CAAC0G,GAAAA,AAAI,EAACL,SAAS,CAAC,CAAC;YACtF,IAAItC,SAAS,CAACE,MAAM,KAAK,CAAC,EAAE;gBAC1B,OAAOqC,qBAAqB,CAC1BD,SAAS,wJACT1F,OAAAA,AAAI,EAAC4F,IAAI,qJAAErG,KAAK,CAACmD,MAAAA,AAAS,qJAACnD,KAAK,CAACyG,YAAAA,AAAe,EAAC7C,SAAS,CAAC,CAAC,CAAC,CAC9D;YACH;YACA,6JAAOnD,OAAAA,AAAI,0KACTY,IAAI,CAAC6C,GAAAA,AAAK,qJAAClE,KAAK,CAACyG,YAAAA,AAAe,EAAC5C,SAAS,CAAC,CAAC,mKAC5C1C,OAAO,CAACuF,GAAAA,AAAQ,0KAACrF,IAAI,CAACkC,KAAO,AAAPA,EAAQ9C,6JAAI,AAAJA,EAAK4F,IAAI,qJAAErG,KAAK,CAACmD,MAAAA,AAAS,qJAACnD,KAAK,CAACyG,YAAAA,AAAe,EAAC7C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAC9F;QACH,CAAC;QACDR,SAAS,EAAE/B,IAAI,CAACsF,sKAAI;QACpBrD,MAAM,EAAEA,CAAA,2KAAMjC,IAAI,CAACkC,KAAAA,AAAO,EAAC8C,IAAI;KAChC,CAAC;AAGG,MAAMO,qBAAqB,IAChCT,SAAsD,GACTzC,WAAW,CAACmD,2BAA2B,CAACV,SAAS,qJAAEnG,KAAK,CAAC8C,EAAK,AAALA,EAAO,CAAC,CAAC;AAEjH,cAAA,GACA,MAAM+D,2BAA2B,GAAGA,CAClCV,SAAsD,EACtDE,IAAqB,OAErBhF,IAAI,CAACiF,0KAAAA,AAAQ,EAAC;QACZrD,OAAO,GAAG6B,KAAsB,yJAC9BrE,OAAI,AAAJA,0KACEY,IAAI,CAACyF,QAAAA,AAAU,EAACrG,6JAAAA,AAAI,EAACqE,KAAK,sJAAE3E,MAAM,CAAC4G,KAAAA,AAAS,EAACZ,SAAS,CAAC,sJAAEhG,MAAM,AAACwC,AAAG,CAAHA,gJAAI3C,KAAK,CAACyG,YAAe,CAAC,CAAC,CAAC,0KAC5FpF,IAAI,CAAC4C,KAAAA,AAAO,GAAEL,SAAS,IAAI;gBACzB,MAAMC,SAAS,yJAAGpD,OAAAA,AAAI,EAACqE,KAAK,qJAAE9E,KAAK,CAACgH,CAAAA,AAAI,EAACpD,SAAS,CAACG,MAAM,CAAC,CAAC;gBAC3D,uJAAI/D,KAAK,CAACgE,IAAAA,AAAO,EAACH,SAAS,CAAC,EAAE;oBAC5B,OAAOgD,2BAA2B,CAACV,SAAS,EAAE1F,6JAAAA,AAAI,EAAC4F,IAAI,qJAAErG,KAAK,CAACmD,MAAAA,AAAS,EAACS,SAAS,CAAC,CAAC,CAAC;gBACvF;gBACA,4JAAOnD,QAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAAA,AAAK,EAACL,SAAS,CAAC,mKAAE1C,OAAO,CAACuF,GAAAA,AAAQ,0KAACrF,IAAI,CAACkC,KAAO,AAAPA,wJAAQ9C,OAAAA,AAAI,EAAC4F,IAAI,qJAAErG,KAAK,CAACmD,MAAAA,AAAS,EAACS,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5G,CAAC,CAAC,CACH;QACHR,SAAS,qKAAE/B,IAAI,CAACsF,GAAI;QACpBrD,MAAM,EAAEA,CAAA,2KAAMjC,IAAI,CAACkC,KAAAA,AAAO,EAAC8C,IAAI;KAChC,CAAC;AAGG,MAAMjC,mBAAmB,GAAA,WAAA,yJAgB5B7D,OAAI,AAAJA,EACF,CAAC,EACD,CACEkC,IAA+B,EAC/BwE,OAIC,KAC4B;IAC7B,MAAMC,IAAI,yJAAGzG,OAAAA,AAAI,mJACfQ,GAAG,CAACkG,GAAI,AAAJA,qJAAKnH,KAAK,CAAC8C,EAAK,AAALA,EAAW,CAAC,uJAC3B3C,MAAM,AAACiH,AAAG,CAAHA,kJAAInG,GAAG,CAACkG,GAAI,AAAJA,EAAK,KAAK,CAAC,CAAC,CAC5B;IACD,MAAME,UAAU,wJAAG5G,QAAAA,AAAI,0KACrBY,IAAI,CAACyF,QAAAA,AAAU,EAACI,IAAI,CAAC,0KACrB7F,IAAI,CAAC4C,KAAAA,AAAO,EAAC,CAAC,CAACqD,YAAY,EAAEC,eAAe,CAAC,KAAI;QAC/C,MAAMC,cAAc,2KAAsFnG,IAAI,CAC3GiF,MAAAA,AAAQ,EAAC;YACRrD,OAAO,GAAG6B,KAAK,OAAKrE,yJAAI,AAAJA,0KAAKY,IAAI,CAAC6C,GAAK,AAALA,EAAMY,KAAK,CAAC,EAAEzD,IAAI,CAAC4C,6KAAAA,AAAO,EAAC,IAAMuD,cAAc,CAAC,CAAC;YAC/EpE,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;YACpBrD,MAAM,GAAG+C,IAAI,yJAAK5F,OAAAA,AAAI,0KAACY,IAAI,CAACyF,QAAAA,AAAU,MAAC7F,GAAG,CAACkE,+IAAG,AAAHA,EAAIoC,eAAe,EAAE,IAAI,CAAC,CAAC,mKAAEpG,KAAQqB,AAAE,EAAH,AAAI6D,CAAH7D,GAAO,CAAC;SACzF,CAAC;QACJ,6JAAO/B,OAAI,AAAJA,EACL+G,cAAc,0KACdnG,IAAI,CAACoG,IAAAA,AAAM,kKAACtG,OAAO,CAACuG,OAAAA,AAAW,EAACJ,YAAY,CAAC,CAAC,0KAC9CjG,IAAI,CAACoG,IAAAA,AAAM,EACTE,uBAAuB,CAAClF,IAAI,EAAE6E,YAAY,EAAEC,eAAe,EAAEN,OAAO,CAAC5C,OAAO,EAAE4C,OAAO,CAAC3C,KAAK,EAAE2C,OAAO,CAAC1C,IAAI,CAAC,CAC3G,CACF;IACH,CAAC,CAAC,CACH;IACD,OAAO,IAAIvC,QAAQ,CAACqF,UAAU,CAAC;AACjC,CAAC,CACF;AAED,MAAMM,uBAAuB,GAAGA,CAC9BlF,IAA+B,EAC/B6E,YAAsC,EACtCC,eAAiC,EACjCK,aAAgB,EAChBhC,CAAe,EACfiC,CAAoB,KACyD;IAC7E,6JAAOpH,OAAAA,AAAI,EACT8B,SAAS,CAACE,IAAI,CAAC,+JACftB,OAAO,CAAC2G,MAAW,kKACnB3G,OAAO,CAAC4G,OAAAA,AAAW,EAAC;QAClB3E,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBqB,SAAS,EAAEA,CAAC,CAACnE,SAAS,EAAEoE,SAAS,CAAC,GAChCrC,CAAC,CAACqC,SAAS,CAAC,yJACRxH,OAAI,AAAJA,EACAY,IAAI,CAACyF,gLAAU,AAAVA,EACH7F,GAAG,CAACkE,mJAAAA,AAAG,EAACmC,YAAY,qJAAEtH,KAAK,CAACkI,IAAAA,AAAO,EAACrE,SAAyC,CAAC,CAAC,CAChF,0KACDxC,IAAI,CAAC4C,KAAAA,AAAO,EAAC,0JACXxD,OAAAA,AAAI,0KACFY,IAAI,CAACyF,QAAAA,AAAU,mJAAC7F,GAAG,CAACkH,EAAAA,AAAG,EAACZ,eAAe,CAAC,CAAC,0KACzClG,IAAI,CAAC4C,KAAAA,AAAO,GAAEmE,YAAY,IAAI;oBAC5B,MAAMC,iBAAiB,GAAGR,CAAC,CAACD,aAAa,EAAEK,SAAS,CAAC;oBACrD,OAAOG,YAAY,yJACf3H,OAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAAA,AAAK,qJAAClE,KAAK,CAACkI,IAAAA,AAAO,EAACrE,SAAS,CAAC,CAAC,mKAAE1C,KAAQqB,AAAE,EAAH,AAAI6F,CAAH7F,gBAAoB,CAAC,CAAC,GACzEmF,uBAAuB,CAAClF,IAAI,EAAE6E,YAAY,EAAEC,eAAe,EAAEc,iBAAiB,EAAEzC,CAAC,EAAEiC,CAAC,CAAC;gBAC3F,CAAC,CAAC,CACH,CACF,CACF,yJACCpH,OAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAAA,AAAK,EAAClE,KAAK,CAACkI,uJAAAA,AAAO,EAACrE,SAAS,CAAC,CAAC,mKAAE1C,KAAU,AAAFqB,EAAGoF,AAAJ,CAACpF,YAAgB,CAAC;KAC3E,CAAC,CACH;AACH,CAAC;AAGM,MAAM8F,eAAe,IAC1B7F,IAA+B,GAE/B,IAAIT,QAAQ,KAACvB,yJAAAA,AAAI,0KAACY,IAAI,CAACkH,aAAAA,AAAe,EAAChG,SAAS,CAACE,IAAI,CAAC,CAAC,mKAAEtB,MAAQwB,AAAG,CAAJ,CAACA,AAAI,CAAC,CAAC6F,MAAM,EAAEC,CAAC,CAAC,GAAK;YAACA,CAAC;+JAAEzI,KAAK,CAACkI,IAAAA,AAAO,EAACM,MAAM,CAAC;SAAC,CAAC,CAAC,CAAC;AAG9G,MAAME,QAAQ,GAAA,WAAA,GAAGnI,6JAAAA,AAAI,EAI1B,CAAC,EACD,CAAsBkC,IAA+B,EAAEoF,CAAqB,yJAC1EpH,OAAAA,AAAI,EAACgC,IAAI,EAAEkG,cAAc,oJAAC3I,KAAK,CAAC2C,AAAG,EAACkF,CAAC,CAAC,CAAC,CAAC,CAC3C;AAGM,MAAMe,cAAc,GAAA,WAAA,yJAAGrI,OAAI,AAAJA,EAS5B,CAAC,EACD,CACEkC,IAA+B,EAC/BoF,CAA4C,GAE5CgB,oBAAoB,CAClBpG,IAAI,GACHS,KAAK,IACJ/C,MAAM,CAACwC,kJAAAA,AAAG,sJACRxC,MAAM,CAAC2I,GAAAA,AAAO,EAAC5F,KAAK,GAAG8B,CAAC,GAAK6C,CAAC,CAAC7C,CAAC,CAAC,CAAC,iJAClChF,KAAK,CAACyG,YAAe,CACtB,CACJ,CACJ;AAGM,MAAMkC,cAAc,GAAA,WAAA,yJAAGpI,OAAAA,AAAI,EAShC,CAAC,EACD,CACEkC,IAA+B,EAC/BoF,CAA+C,KACjB;IAC9B,MAAMkB,IAAI,2KAA0F1H,IAAI,CAACiF,MAAQ,AAARA,EAAS;QAChHrD,OAAO,GAAGC,KAAK,yJAAKzC,OAAAA,AAAI,MAACY,IAAI,CAAC6C,uKAAAA,AAAK,EAAC2D,CAAC,CAAC3E,KAAK,CAAC,CAAC,0KAAE7B,IAAI,CAAC4C,KAAAA,AAAO,EAAC,IAAM8E,IAAI,CAAC,CAAC;QACxE3F,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBrD,MAAM,sKAAEjC,IAAI,CAACkC,KAAAA;KACd,CAAC;IACF,OAAO,IAAIvB,QAAQ,uJAACvB,OAAAA,AAAI,EAACsI,IAAI,yKAAE1H,IAAI,CAACoG,KAAAA,AAAM,EAAClF,SAAS,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CACF;AAGM,MAAMoG,oBAAoB,GAAA,WAAA,yJAAGtI,OAAAA,AAAI,EAStC,CAAC,EACD,CACEkC,IAA+B,EAC/BoF,CAAsE,KAC9B;IACxC,MAAMkB,IAAI,GAA4F1H,IAAI,CACvGiF,8KAAAA,AAAQ,EAAC;QACRrD,OAAO,GAAGC,KAAK,yJAAKzC,OAAAA,AAAI,0KAACY,IAAI,CAACyF,QAAAA,AAAU,EAACe,CAAC,CAAC3E,KAAK,CAAC,CAAC,0KAAE7B,IAAI,CAAC4C,KAAAA,AAAO,sKAAC5C,IAAI,CAAC6C,GAAK,CAAC,0KAAE7C,IAAI,CAAC4C,KAAO,AAAPA,EAAQ,IAAM8E,IAAI,CAAC,CAAC;QACvG3F,SAAS,EAAE/B,IAAI,CAACsF,sKAAI;QACpBrD,MAAM,EAAEjC,IAAI,CAACkC,yKAAAA;KACd,CAAC;IACJ,OAAO,IAAIvB,QAAQ,uJAACvB,OAAAA,AAAI,EAACsI,IAAI,mKAAE5H,OAAO,CAAC6H,OAAAA,AAAY,EAACzG,SAAS,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;AACxE,CAAC,CACF;AAGM,MAAMwG,GAAG,IAAIC,MAAe,GAAgC7F,SAAS,mJAACtD,KAAK,CAACkJ,CAAAA,AAAG,EAACC,MAAM,CAAC,CAAC;AAGxF,MAAMC,UAAU,IAAIC,OAAe,GACxC/F,SAAS,oJAACtD,KAAK,CAACkJ,AAAG,EAAC,mJAAIlJ,KAAK,CAACsJ,aAAgB,CAACD,OAAO,CAAC,CAAC,CAAC;AAGpD,MAAME,OAAO,IAAIhH,QAA0B,GAChDiH,aAAa,CAAC,KAAMxJ,KAAK,CAACkJ,kJAAG,AAAHA,EAAI3G,QAAQ,EAAE,CAAC,CAAC;AAGrC,MAAMkH,KAAK,GAAA,WAAA,IAAGjJ,4JAAAA,AAAI,EAevB,CAAC,EACD,CACEkC,IAA+B,EAC/BwE,OAGC,GAC+BtE,GAAG,CAAC+F,QAAQ,CAACjG,IAAI,EAAEwE,OAAO,CAAChE,OAAO,CAAC,EAAEgE,OAAO,CAAC3D,MAAM,CAAC,CACvF;AAGM,MAAMmG,WAAW,GAAA,WAAA,OAAGlJ,yJAAI,AAAJA,EAezB,CAAC,EACD,CAACkC,IAAI,EAAEwE,OAAO,GACZyC,SAAS,CACPd,cAAc,CAACnG,IAAI,EAAEwE,OAAO,CAAChE,OAAO,CAAC,EACrCgE,OAAO,CAAC3D,MAAM,CACf,CACJ;AAGM,MAAMqG,WAAW,GAAA,WAAA,GAAGpJ,6JAAAA,AAAI,EAe7B,CAAC,EACD,CAACkC,IAAI,EAAEwE,OAAO,GACZtE,GAAG,CACDgG,cAAc,CAAClG,IAAI,EAAEwE,OAAO,CAAChE,OAAO,CAAC,EACrCgE,OAAO,CAAC3D,MAAM,CACf,CACJ;AAGM,MAAMsG,iBAAiB,GAAA,WAAA,yJAAGrJ,OAAAA,AAAI,EAenC,CAAC,EACD,CAACkC,IAAI,EAAEwE,OAAO,GAAKyC,SAAS,CAACb,oBAAoB,CAACpG,IAAI,EAAEwE,OAAO,CAAChE,OAAO,CAAC,EAAEgE,OAAO,CAAC3D,MAAM,CAAC,CAC1F;AAGM,MAAMuG,KAAK,GAAA,WAAA,GAA6B,IAAI7H,QAAQ,CAAA,WAAA,oKACzDb,OAAO,CAAC0I,AAAK,EAAA,WAAA,oKAAC1I,OAAO,CAAC2I,UAAAA,AAAe,EAAE,CAAC,CACzC;AAGM,MAAM9C,IAAI,IAAQvD,CAAS,GAAiCpB,OAAO,CAAC,IAAM,IAAIL,QAAQ,CAAC+H,QAAQ,CAACtG,CAAC,CAAC,CAAC,CAAC;AAE3G,cAAA,GACA,MAAMsG,QAAQ,IACZtG,CAAS,2KAETpC,IAAI,CAACiF,MAAAA,AAAQ,EAAC;QACZrD,OAAO,GAAG6B,KAAsB,IAAI;YAClC,MAAMkF,OAAO,yJAAGvJ,OAAAA,AAAI,EAACqE,KAAK,EAAE9E,KAAK,CAACgH,oJAAAA,AAAI,EAACvD,CAAC,CAAC,CAAC;YAC1C,MAAMwG,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC1G,CAAC,GAAGqB,KAAK,CAACf,MAAM,EAAE,CAAC,CAAC;YAC9C,MAAMqG,IAAI,GAAGpK,KAAK,CAACgE,uJAAAA,AAAO,EAACc,KAAK,CAAC,IAAImF,QAAQ,GAAG,CAAC;YACjD,IAAIG,IAAI,EAAE;gBACR,OAAOL,QAAQ,CAACE,QAAQ,CAAC;YAC3B;YACA,6JAAOxJ,OAAAA,AAAI,0KACTY,IAAI,CAAC6C,GAAAA,AAAK,EAAC8F,OAAO,CAAC,mKACnB7I,OAAO,CAACuF,GAAAA,AAAQ,mKAACvF,OAAO,CAAC2I,UAAe,AAAfA,EAAkD,CAAC,CAC7E;QACH,CAAC;QACD1G,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBrD,MAAM,EAAEA,CAAA,uKAAMjC,IAAI,CAACgJ,EAAAA;KACpB,CAAC;AAGG,MAAMC,SAAS,IAAQnE,SAAwB,GACpD,IAAInE,QAAQ,CACVvB,6JAAAA,AAAI,EAAC8B,SAAS,CAACgI,SAAS,EAAEzF,KAAS,GAAK,CAACqB,SAAS,CAACrB,KAAK,CAAC,CAAC,CAAC,mKAAE3D,OAAO,CAAC6H,OAAAA,AAAY,EAACzG,SAAS,CAACyE,IAAI,CAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAC3G;AAGI,MAAMwD,eAAe,IAC1BrE,SAAsD,GACjB9D,OAAO,CAAC,IAAM,IAAIL,QAAQ,CAACyI,qBAAqB,CAACtE,SAAS,CAAC,CAAC,CAAC;AAEpG,cAAA,GACA,MAAMsE,qBAAqB,IACzBtE,SAAsD,2KAEtD9E,IAAI,CAACiF,MAAAA,AAAQ,EAAC;QACZrD,OAAO,GAAG6B,KAAsB,yJAC9BrE,OAAAA,AAAI,EACFqE,KAAK,MACL3E,MAAM,CAACmK,qJAAAA,AAAS,EAACnE,SAAS,CAAC,sJAC3BhG,MAAM,AAACwC,AAAG,CAAHA,EAAKsH,QAAQ,IAAI;gBACtB,MAAMG,IAAI,GAAGH,QAAQ,CAAClG,MAAM,KAAK,CAAC;gBAClC,OAAOqG,IAAI,GACTK,qBAAqB,CAACtE,SAAS,CAAC,yJAChC1F,OAAAA,AAAI,0KACFY,IAAI,CAAC6C,GAAAA,AAAK,qJAAClE,KAAK,CAACyG,YAAAA,AAAe,EAACwD,QAAQ,CAAC,CAAC,mKAC3C9I,OAAO,CAACuF,GAAAA,AAAQ,mKAACvF,OAAO,CAAC2I,UAAAA,AAAe,EAA+B,CAAC,CACzE;YACL,CAAC,CAAC,+JACF3I,OAAO,CAACuJ,CAAM,CACf;QACHtH,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBrD,MAAM,EAAEA,CAAA,uKAAMjC,IAAI,CAACgJ,EAAAA;KACpB,CAAC;AAGG,MAAME,SAAS,IAAQpE,SAAwB,GACpD,IAAInE,QAAQ,CAAC2I,eAAe,CAACxE,SAAS,CAAC,CAAC;AAE1C,cAAA,GACA,MAAMwE,eAAe,IACnBxE,SAAwB,0KAExB9E,IAAI,CAACiF,OAAAA,AAAQ,EAAC;QACZrD,OAAO,GAAG6B,KAAsB,IAAI;YAClC,MAAM8F,GAAG,yJAAGnK,OAAAA,AAAI,EAACqE,KAAK,qJAAE9E,KAAK,CAACuK,MAAS,AAATA,EAAUpE,SAAS,CAAC,CAAC;YACnD,KAAInG,KAAK,CAACgE,sJAAAA,AAAO,EAAC4G,GAAG,CAAC,EAAE;gBACtB,OAAOD,eAAe,CAACxE,SAAS,CAAC;YACnC;YACA,6JAAO1F,OAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAK,AAALA,EAAM0G,GAAG,CAAC,mKAAEzJ,OAAO,CAACuF,GAAAA,AAAQ,mKAACvF,OAAO,CAAC2I,UAAAA,AAAe,EAAmC,CAAC,CAAC;QAC5G,CAAC;QACD1G,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBrD,MAAM,sKAAEjC,IAAI,CAACwJ,QAAAA;KACd,CAAC;AAGG,MAAMC,eAAe,IAC1B3E,SAAsD,GACjB9D,OAAO,CAAC,IAAM,IAAIL,QAAQ,CAAC+I,qBAAqB,CAAC5E,SAAS,CAAC,CAAC,CAAC;AAEpG,cAAA,GACA,MAAM4E,qBAAqB,IACzB5E,SAAsD,2KAEtD9E,IAAI,CAACiF,MAAAA,AAAQ,EAAC;QACZrD,OAAO,GAAG6B,KAAsB,yJAC9BrE,OAAAA,AAAI,EACFqE,KAAK,qJACL3E,MAAM,CAACoK,MAAS,AAATA,EAAUpE,SAAS,CAAC,sJAC3BhG,MAAM,AAACwC,AAAG,CAAHA,EAAKsH,QAAQ,IAAI;gBACtB,MAAMG,IAAI,GAAGH,QAAQ,CAAClG,MAAM,KAAK,CAAC;gBAClC,OAAOqG,IAAI,GACTW,qBAAqB,CAAC5E,SAAS,CAAC,OAChC1F,yJAAAA,AAAI,0KACFY,IAAI,CAAC6C,GAAAA,AAAK,qJAAClE,KAAK,CAACyG,YAAAA,AAAe,EAACwD,QAAQ,CAAC,CAAC,mKAC3C9I,OAAO,CAACuF,GAAAA,AAAQ,GAACvF,OAAO,CAAC2I,0KAAe,AAAfA,EAA8C,CAAC,CACzE;YACL,CAAC,CAAC,+JACF3I,OAAO,CAACuJ,CAAM,CACf;QACHtH,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBrD,MAAM,EAAEA,CAAA,uKAAMjC,IAAI,CAACgJ,EAAAA;KACpB,CAAC;AAGG,MAAMW,QAAQ,GAAA,WAAA,GAAGzK,6JAAI,AAAJA,EAStB,CAAC,EACD,CAACkC,IAAI,EAAEwI,SAAS,GAAK,IAAIjJ,QAAQ,uJAACvB,OAAAA,AAAI,EAACgC,IAAI,EAAEF,SAAS,GAAEpB,OAAO,CAAC6J,mKAAAA,AAAQ,EAACC,SAAS,CAAC,CAAC,CAAC,CACtF;AAGM,MAAMC,YAAY,GAAA,WAAA,yJAAG3K,OAAAA,AAAI,EAS9B,CAAC,EACD,CAACkC,IAAI,EAAEwI,SAAS,GAAK,IAAIjJ,QAAQ,CAACvB,6JAAAA,AAAI,EAACgC,IAAI,EAAEF,SAAS,yKAAElB,IAAI,CAAC6J,WAAAA,AAAY,EAACD,SAAS,CAAC,CAAC,CAAC,CACvF;AAGM,MAAME,OAAO,GAAGA,CAAA,GAAkErE,UAAU,qJAAC3G,MAAM,CAACgL,GAAAA,AAAO,EAAK,CAAC;AAGjH,MAAMC,WAAW,IACtBvD,CAAqC,yJACMpH,OAAAA,AAAI,EAAC0K,OAAO,EAAK,EAAExI,GAAG,CAACkF,CAAC,CAAC,CAAC;AAGhE,MAAMwD,iBAAiB,IAC5BxD,CAA2D,yJACfpH,OAAAA,AAAI,EAAC0K,OAAO,EAAM,EAAEzB,SAAS,CAAC7B,CAAC,CAAC,CAAC;AAGxE,MAAMyD,eAAe,IAC1BzD,CAA8D,GAE9D,IAAI7F,QAAQ,kKAACb,OAAO,CAACuJ,CAAAA,AAAM,wJAACjK,OAAAA,AAAI,sJAACN,MAAM,CAACiL,OAAAA,AAAW,GAAED,OAAO,GAAK5I,SAAS,CAACsF,CAAC,CAACsD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAGrF,MAAMI,KAAK,IAAQpF,SAAwB,GAChDN,IAAI,CAAC,IAAI,EAAErF,6JAAQ,EAAE,CAACuC,GAAG,EAAE+B,KAAK,GAAK/B,GAAG,IAAIoD,SAAS,CAACrB,KAAK,CAAC,CAAC;AAGxD,MAAM6B,IAAI,IAAO6E,CAAI,GAA0C,IAAIxJ,QAAQ,wKAACX,IAAI,CAACsF,GAAAA,AAAI,EAAC6E,CAAC,CAAC,CAAC;AAGzF,MAAMC,QAAQ,IAAOnJ,QAAoB,GAC9C,IAAIN,QAAQ,yKAACX,IAAI,CAACoK,MAAAA,AAAQ,EAACnJ,QAAQ,CAAC,CAAC;AAGhC,MAAMe,SAAS,IAAOqI,KAAqB,GAChD,IAAI1J,QAAQ,yKAACX,IAAI,CAACgC,OAAAA,AAAS,EAACqI,KAAK,CAAC,CAAC;AAG9B,MAAMnC,aAAa,IAAOjH,QAAiC,GAChE,IAAIN,QAAQ,yKAACX,IAAI,CAACkI,WAAAA,AAAa,EAACjH,QAAQ,CAAC,CAAC;AAGrC,MAAMqJ,WAAW,GAKC9D,CAAiB,IAAI;IAC5C,QAAoBpF,IAA+B,yJACjDhC,OAAI,AAAJA,EAAKgC,IAAI,EAAEkG,cAAc,oJAAC3I,KAAK,CAAC4L,GAAAA,AAAM,EAAC/D,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC;AAGM,MAAMgE,iBAAiB,GAAA,WAAA,yJAAGtL,OAAAA,AAAI,EASnC,CAAC,EACD,CAACkC,IAAI,EAAEoF,CAAC,GACNgB,oBAAoB,CAClBpG,IAAI,GACHS,KAAK,uJAAK/C,MAAU,AAAHwC,AAAD,CAACA,qJAAIxC,MAAM,CAACyL,EAAAA,AAAM,EAAC1I,KAAK,EAAE2E,CAAC,CAAC,iJAAE7H,KAAK,CAACyG,YAAe,CAAC,CACtE,CACJ;AAGM,MAAMqF,UAAU,GAAA,WAAA,wJAAGvL,QAAI,AAAJA,EASxB,CAAC,EACD,CACEkC,IAA+B,EAC/BoF,CAA2C,KACW;IACtD,MAAMR,UAAU,yJAAG5G,OAAAA,AAAI,0KACrBY,IAAI,CAACyF,QAAAA,AAAU,wJAACrG,OAAAA,AAAI,mJAClBQ,GAAG,CAACkG,GAAAA,AAAI,qJAACnH,KAAK,CAAC8C,EAAAA,AAAK,EAAM,CAAC,uJAC3B3C,MAAM,AAACiH,AAAG,CAAHA,kJAAInG,GAAG,CAACkG,GAAAA,AAAI,EAAC,KAAK,CAAC,CAAC,CAC5B,CAAC,0KACF9F,IAAI,CAAC4C,KAAAA,AAAO,EAAC,CAAC,CAACqD,YAAY,EAAEC,eAAe,CAAC,KAAI;QAC/C,MAAMC,cAAc,2KAAsFnG,IAAI,CAC3GiF,MAAAA,AAAQ,EAAC;YACRrD,OAAO,GAAG6B,KAAK,yJAAKrE,OAAAA,AAAI,EAACY,IAAI,CAAC6C,2KAAK,AAALA,EAAMY,KAAK,CAAC,0KAAEzD,IAAI,CAAC4C,KAAAA,AAAO,EAAC,IAAMuD,cAAc,CAAC,CAAC;YAC/EpE,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;YACpBrD,MAAM,GAAG+C,IAAI,yJAAK5F,OAAAA,AAAI,GAACY,IAAI,CAACyF,+KAAAA,AAAU,mJAAC7F,GAAG,CAACkE,EAAAA,AAAG,EAACoC,eAAe,EAAE,IAAI,CAAC,CAAC,mKAAEpG,KAAQqB,AAAE,EAAH,AAAI6D,CAAH7D,GAAO,CAAC;SACzF,CAAC;QACJ,MAAMuG,IAAI,oKACR5H,OAAO,CAAC4G,MAAW,AAAXA,0KAAY1G,IAAI,CAACkH,aAAe,AAAfA,EAAgBhG,SAAS,CAACE,IAAI,CAAC,CAAC,EAAE;YACzDW,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;YACpBqB,SAAS,EAAEA,CAAC,CAACnE,SAAS,EAAEoE,SAAS,CAAC,OAChCxH,yJAAAA,AAAI,0KACFY,IAAI,CAACyF,QAAAA,AAAU,EAACe,CAAC,CAACI,SAAS,CAAC,CAAC,0KAC7B5G,IAAI,CAAC4C,KAAAA,AAAO,GAAE8H,SAAS,yJACrBtL,OAAAA,AAAI,0KACFY,IAAI,CAACyF,QAAAA,AAAU,mJAAC7F,GAAG,CAACkE,EAAAA,AAAG,EAACmC,YAAY,qJAAEtH,KAAK,CAACkI,IAAAA,AAAO,EAACrE,SAAS,CAAC,CAAC,CAAC,mKAChE1C,OAAO,CAACuF,GAAAA,AAAQ,wJACdjG,OAAAA,AAAI,0KACFY,IAAI,CAACyF,QAAAA,AAAU,mJAAC7F,GAAG,CAACkH,EAAAA,AAAG,EAACZ,eAAe,CAAC,CAAC,0KACzClG,IAAI,CAAC4C,KAAAA,AAAO,GAAEmE,YAAY,IAAI;wBAC5B,IAAI2D,SAAS,EAAE;4BACb,4JAAOtL,QAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAAA,AAAK,qJAAClE,KAAK,CAACkI,IAAAA,AAAO,EAACrE,SAAS,CAAC,CAAC,mKAAE1C,KAAU,AAAFqB,EAAD,AAAI5B,CAAH4B,KAAS,CAACwJ,oJAAAA,AAAI,EAAC/D,SAAS,CAAC,CAAC,CAAC;wBACvF;wBACA,IAAIG,YAAY,EAAE;4BAChB,6JAAO3H,OAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAAA,AAAK,qJAAClE,KAAK,CAACkI,IAAAA,AAAO,EAACrE,SAAS,CAAC,CAAC,kKAAE1C,MAAQqB,AAAE,CAAH,CAACA,oJAAG5B,MAAM,CAACqL,AAAI,EAAE,CAAC,CAAC;wBAC9E;wBACA,OAAOlD,IAAI;oBACb,CAAC,CAAC,CACH,CACF,CACF,CACF;SAEN,CAAC;QACJ,6JAAOtI,OAAAA,AAAI,EAAC+G,cAAc,yKAAEnG,IAAI,CAACoG,KAAAA,AAAM,mKAACtG,OAAO,CAACuG,MAAAA,AAAW,EAACJ,YAAY,CAAC,CAAC,0KAAEjG,IAAI,CAACoG,IAAM,AAANA,EAAOsB,IAAI,CAAC,CAAC;IAChG,CAAC,CAAC,CACH;IACD,OAAO,IAAI/G,QAAQ,CAACqF,UAAU,CAAC;AACjC,CAAC,CACF;AAGM,MAAMxB,IAAI,GAAGA,CAClBqG,CAAI,EACJC,MAAoB,EACpBtE,CAAyB,GACAxF,OAAO,CAAC,IAAM,IAAIL,QAAQ,CAACoK,UAAU,CAACF,CAAC,EAAEC,MAAM,EAAEtE,CAAC,CAAC,CAAC,CAAC;AAEhF,cAAA,GACA,MAAMuE,UAAU,GAAGA,CACjBF,CAAI,EACJC,MAAoB,EACpBtE,CAAyB,KACsD;IAC/E,IAAI,CAACsE,MAAM,CAACD,CAAC,CAAC,EAAE;QACd,OAAO7K,IAAI,CAACwJ,gLAAAA,AAAU,EAACqB,CAAC,CAAC;IAC3B;IACA,+KAAO7K,IAAI,CAACiF,MAAAA,AAAQ,EAAC;QACnBrD,OAAO,GAAG6B,KAAsB,IAAI;YAClC,MAAM,CAACuH,KAAK,EAAExI,SAAS,CAAC,GAAGyI,cAAc,CAACJ,CAAC,EAAEpH,KAAK,EAAEqH,MAAM,EAAEtE,CAAC,EAAE,CAAC,EAAE/C,KAAK,CAACf,MAAM,CAAC;YAC/E,uJAAI/D,KAAK,CAACuM,OAAAA,AAAU,EAAC1I,SAAS,CAAC,EAAE;gBAC/B,6JAAOpD,OAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAAA,AAAK,EAACL,SAAS,CAAC,MAAE1C,OAAO,CAACqB,0JAAAA,AAAE,EAAC6J,KAAK,CAAC,CAAC;YACvD;YACA,OAAOD,UAAU,CAACC,KAAK,EAAEF,MAAM,EAAEtE,CAAC,CAAC;QACrC,CAAC;QACDzE,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBrD,MAAM,EAAEA,CAAA,2KAAMjC,IAAI,CAACwJ,QAAAA,AAAU,EAACqB,CAAC;KAChC,CAAC;AACJ,CAAC;AAED,cAAA,GACA,MAAMI,cAAc,GAAGA,CACrBJ,CAAI,EACJhJ,KAAsB,EACtBiJ,MAAoB,EACpBtE,CAAyB,EACzB2E,KAAa,EACbzI,MAAc,KACU;IACxB,IAAIyI,KAAK,KAAKzI,MAAM,EAAE;QACpB,OAAO;YAACmI,CAAC;+JAAElM,KAAK,CAAC8C,EAAAA,AAAK,EAAE;SAAC;IAC3B;IACA,MAAM2J,EAAE,GAAG5E,CAAC,CAACqE,CAAC,uJAAEzL,QAAI,AAAJA,EAAKyC,KAAK,qJAAElD,KAAK,CAACkF,MAAAA,AAAS,EAACsH,KAAK,CAAC,CAAC,CAAC;IACpD,IAAIL,MAAM,CAACM,EAAE,CAAC,EAAE;QACd,OAAOH,cAAc,CAACG,EAAE,EAAEvJ,KAAK,EAAEiJ,MAAM,EAAEtE,CAAC,EAAE2E,KAAK,GAAG,CAAC,EAAEzI,MAAM,CAAC;IAChE;IACA,OAAO;QAAC0I,EAAE;8JAAEhM,OAAAA,AAAI,EAACyC,KAAK,qJAAElD,KAAK,CAACgH,CAAAA,AAAI,EAACwF,KAAK,GAAG,CAAC,CAAC,CAAC;KAAC;AACjD,CAAC;AAGM,MAAME,QAAQ,GAAA,WAAA,yJAAGnM,OAAAA,AAAI,EAe1B,CAAC,EACD,CACEkC,IAA+B,EAC/BwE,OAGC,KAC+D;IAChE,MAAMI,UAAU,yJAQZ5G,OAAAA,AAAI,EACN8B,SAAS,CAACE,IAAI,CAAC,sKACfpB,IAAI,CAACkH,aAAe,mKACpBpH,OAAO,CAAC4G,MAAW,AAAXA,EAAY;QAClB3E,SAAS,EAAGuJ,KAAK,IAAKpK,SAAS,CAAC0E,OAAO,CAAC7D,SAAS,CAACuJ,KAAK,CAAC,CAAC;QACzD3E,SAAS,EAAEA,CAAC,CAACnE,SAAS,EAAE4E,CAAC,CAAC,2KACxBpH,IAAI,CAACgB,KAAAA,AAAO,EAAC,MAAK;gBAChB,MAAMiF,YAAY,GAAG;oBACnBsF,GAAG,GAAEnM,4JAAAA,AAAI,EAACoD,SAAS,qJAAE7D,KAAK,CAAC4L,GAAAA,AAAM,iJAAC5L,KAAK,CAACuM,OAAU,CAAC;iBACpD;gBACD,MAAMM,SAAS,yJAAGpM,OAAI,AAAJA,yKAChBY,IAAI,CAACyL,GAAAA,AAAI,EAAC,MAAK;oBACb,MAAMF,GAAG,GAAGtF,YAAY,CAACsF,GAAG;oBAC5BtF,YAAY,CAACsF,GAAG,GAAG5M,KAAK,CAAC8C,qJAAAA,AAAK,EAAE;oBAChC,OAAO8J,GAAG;gBACZ,CAAC,CAAC,EACF,yEAAA;gBACA,oEAAA;wLACAvL,IAAI,CAAC4C,KAAAA,AAAO,GAAEf,KAAK,GAAK/B,OAAO,CAAC4L,sKAAU,AAAVA,EAAW7J,KAA4C,CAAC,CAAC,CAC1F;gBACD,MAAM8J,WAAW,oKAAG7L,OAAO,CAAC2I,UAAAA,AAAe,EAA0C;gBACrF,MAAMmD,gBAAgB,yJAAGxM,OAAAA,AAAI,EAC3BoM,SAAS,EACT1L,OAAO,CAACuF,oKAAQ,AAARA,EAASsG,WAAW,CAAC,0KAC7B3L,IAAI,CAACoG,IAAAA,AAAM,EAAClF,SAAS,CAAC0E,OAAO,CAACe,SAAS,CAACS,CAAC,CAAC,CAAC,CAAC,CAC7C;gBACD,WAAOpH,IAAI,CAAC4C,yKAAAA,AAAO,0KACjB5C,IAAI,CAACkH,aAAe,AAAfA,EAAgB0E,gBAAgB,CAAC,EACtC,CAAC,CAACC,YAAY,EAAEC,EAAE,CAAC,wJACjB1M,QAAI,AAAJA,0KACEY,IAAI,CAACkC,KAAAA,AAAO,EAAC+D,YAAY,CAACsF,GAAG,CAAC,0KAC9BvL,IAAI,CAAC4C,KAAAA,AAAO,+JAAC9C,OAAO,CAAC4L,KAAU,CAAC,MAChC5L,OAAO,CAACuF,gKAAAA,AAAQ,mKAACvF,OAAO,CAAC4L,KAAAA,AAAU,EAACG,YAAY,CAAC,CAAC,mKAClD/L,KAAQqB,AAAE,EAAH,AAAI2K,CAAH3K,CAAK,CAAC,CACf,CACJ;YACH,CAAC;KACJ,CAAC,CACH;IACD,OAAO,IAAIR,QAAQ,CAACqF,UAAU,CAAC;AACjC,CAAC,CACF;AAGM,MAAM+F,UAAU,GAAGA,CACxBlB,CAAI,EACJC,MAAoB,EACpBtE,CAAsC,GACjBxF,OAAO,CAAC,IAAM,IAAIL,QAAQ,CAACqL,gBAAgB,CAACnB,CAAC,EAAEC,MAAM,EAAEtE,CAAC,CAAC,CAAC,CAAC;AAElF,cAAA,GACA,MAAMwF,gBAAgB,GAAGA,CACvBnB,CAAI,EACJC,MAAoB,EACpBtE,CAAsC,KAC+B;IACrE,IAAI,CAACsE,MAAM,CAACD,CAAC,CAAC,EAAE;QACd,+KAAO7K,IAAI,CAACwJ,QAAAA,AAAU,EAACqB,CAAC,CAAC;IAC3B;IACA,+KAAO7K,IAAI,CAACiF,MAAAA,AAAQ,EAAC;QACnBrD,OAAO,GAAG6B,KAAsB,GAAKuI,gBAAgB,CAACxF,CAAC,CAACqE,CAAC,EAAEpH,KAAK,CAAC,EAAEqH,MAAM,EAAEtE,CAAC,CAAC;QAC7EzE,SAAS,EAAE/B,IAAI,CAACsF,sKAAI;QACpBrD,MAAM,EAAEA,CAAA,2KAAMjC,IAAI,CAACwJ,QAAAA,AAAU,EAACqB,CAAC;KAChC,CAAC;AACJ,CAAC;AAGM,MAAMoB,gBAAgB,GAAGA,CAC9BpB,CAAI,EACJC,MAAoB,EACpBtE,CAA2D,GAC5BxF,OAAO,CAAC,IAAM,IAAIL,QAAQ,CAACuL,sBAAsB,CAACrB,CAAC,EAAEC,MAAM,EAAEtE,CAAC,CAAC,CAAC,CAAC;AAElG,cAAA,GACA,MAAM0F,sBAAsB,GAAGA,CAC7BrB,CAAI,EACJC,MAAoB,EACpBtE,CAA2D,KACK;IAChE,IAAI,CAACsE,MAAM,CAACD,CAAC,CAAC,EAAE;QACd,+KAAO7K,IAAI,CAACwJ,QAAU,AAAVA,EAAWqB,CAAC,CAAC;IAC3B;IACA,+KAAO7K,IAAI,CAACiF,MAAAA,AAAQ,EAAC;QACnBrD,OAAO,GAAG6B,KAAsB,yJAC9BrE,OAAI,AAAJA,0KACEY,IAAI,CAACyF,QAAAA,AAAU,EAACe,CAAC,CAACqE,CAAC,EAAEpH,KAAK,CAAC,CAAC,0KAC5BzD,IAAI,CAAC4C,KAAAA,AAAO,GAAEiI,CAAC,GAAKqB,sBAAsB,CAACrB,CAAC,EAAEC,MAAM,EAAEtE,CAAC,CAAC,CAAC,CAC1D;QACHzE,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBrD,MAAM,EAAEA,CAAA,2KAAMjC,IAAI,CAACwJ,QAAAA,AAAU,EAACqB,CAAC;KAChC,CAAC;AACJ,CAAC;AAGM,MAAMlG,UAAU,GAAGA,CACxBkG,CAAI,EACJC,MAAoB,EACpBtE,CAA8C,GACfxF,OAAO,CAAC,IAAM,IAAIL,QAAQ,CAACwL,gBAAgB,CAACtB,CAAC,EAAEC,MAAM,EAAEtE,CAAC,CAAC,CAAC,CAAC;AAE5F,cAAA,GACA,MAAM2F,gBAAgB,GAAGA,CACvBtB,CAAI,EACJC,MAAoB,EACpBtE,CAA8C,KAC4B;IAC1E,IAAI,CAACsE,MAAM,CAACD,CAAC,CAAC,EAAE;QACd,+KAAO7K,IAAI,CAACwJ,QAAAA,AAAU,EAACqB,CAAC,CAAC;IAC3B;IACA,QAAO7K,IAAI,CAACiF,6KAAAA,AAAQ,EAAC;QACnBrD,OAAO,GAAG6B,KAAsB,yJAC9BrE,OAAAA,AAAI,0KACFY,IAAI,CAACyF,QAAAA,AAAU,EAAC2G,oBAAoB,CAACvB,CAAC,EAAEpH,KAAK,EAAEqH,MAAM,EAAEtE,CAAC,CAAC,CAAC,GAC1DxG,IAAI,CAAC4C,4KAAO,AAAPA,EAAQ,CAAC,CAACoI,KAAK,EAAExI,SAAS,CAAC,yJAC9BpD,OAAAA,AAAI,EACFoD,SAAS,sJACTjD,MAAM,CAAC8M,CAAAA,AAAK,EAAC;oBACXC,MAAM,EAAEA,CAAA,GAAMH,gBAAgB,CAACnB,KAAK,EAAEF,MAAM,EAAEtE,CAAC,CAAC;oBAChD+F,MAAM,GAAG3D,QAAQ,yJAAKxJ,OAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAAA,AAAK,EAAC+F,QAAQ,CAAC,MAAE9I,OAAO,CAACqB,0JAAAA,AAAE,EAAC6J,KAAK,CAAC;iBACnE,CAAC,CACH,CACF,CACF;QACHjJ,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBrD,MAAM,EAAEA,CAAA,2KAAMjC,IAAI,CAACwJ,QAAAA,AAAU,EAACqB,CAAC;KAChC,CAAC;AACJ,CAAC;AAED,cAAA,GACA,MAAMuB,oBAAoB,GAAGA,CAC3BvB,CAAI,EACJhJ,KAAsB,EACtBiJ,MAAoB,EACpBtE,CAA8C,GAE9CgG,4BAA4B,CAAC3B,CAAC,EAAEhJ,KAAK,EAAE,CAAC,EAAEA,KAAK,CAACa,MAAM,EAAEoI,MAAM,EAAEtE,CAAC,CAAC;AAEpE,cAAA,GACA,MAAMgG,4BAA4B,GAAGA,CACnC3B,CAAI,EACJhJ,KAAsB,EACtBsJ,KAAa,EACbzI,MAAc,EACdoI,MAAoB,EACpBtE,CAA8C,KACc;IAC5D,IAAI2E,KAAK,KAAKzI,MAAM,EAAE;QACpB,0JAAO5D,MAAM,CAACoD,IAAAA,AAAO,EAAC;YAAC2I,CAAC;gKAAEtL,MAAM,CAACqL,AAAI,EAAE;SAAC,CAAC;IAC3C;IACA,6JAAOxL,OAAAA,AAAI,EACToH,CAAC,CAACqE,CAAC,uJAAEzL,QAAAA,AAAI,EAACyC,KAAK,qJAAElD,KAAK,CAACkF,MAAAA,AAAS,EAACsH,KAAK,CAAC,CAAC,CAAC,sJACzCrM,MAAM,CAAC8D,GAAAA,AAAO,GAAEwI,EAAE,GAChBN,MAAM,CAACM,EAAE,CAAC,GACRoB,4BAA4B,CAACpB,EAAE,EAAEvJ,KAAK,EAAEsJ,KAAK,GAAG,CAAC,EAAEzI,MAAM,EAAEoI,MAAM,EAAEtE,CAAC,CAAC,GACrE1H,MAAM,CAACoD,uJAAAA,AAAO,EAAC;YAACkJ,EAAE;gKAAE7L,MAAM,CAACoL,AAAI,wJAACvL,OAAAA,AAAI,EAACyC,KAAK,oJAAElD,KAAK,CAACgH,EAAAA,AAAI,EAACwF,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;SAAC,CAAC,CACxE,CACF;AACH,CAAC;AAGM,MAAMsB,QAAQ,GAAGA,CAAQ5B,CAAI,EAAErE,CAAyB,GAC7DkG,cAAc,CAAClI,IAAI,CAACqG,CAAC,oJAAE5L,YAAS,EAAEuH,CAAC,CAAC,CAAC;AAGhC,MAAMjD,cAAc,GAAGA,CAC5BsH,CAAI,EACJrE,CAAsC,GACjBuF,UAAU,CAAClB,CAAC,oJAAE5L,YAAS,EAAEuH,CAAC,CAAC;AAG3C,MAAMmG,oBAAoB,GAAGA,CAClC9B,CAAI,EACJrE,CAA2D,GACzBkG,cAAc,CAACT,gBAAgB,CAACpB,CAAC,EAAE5L,8JAAS,EAAEuH,CAAC,CAAC,CAAC;AAG9E,MAAMoG,cAAc,GAAGA,CAC5B/B,CAAI,EACJrE,CAA8C,GACf7B,UAAU,CAACkG,CAAC,oJAAE5L,YAAS,EAAEuH,CAAC,CAAC;AAGrD,MAAMqG,SAAS,GAAGA,CAAQhC,CAAI,EAAE/B,GAAW,EAAEtC,CAAyB,GAC3EpH,6JAAAA,AAAI,EACFoF,IAAI,CACF;QAACqG,CAAC;QAAE,CAAC;KAAC,GACLpG,KAAK,GAAKA,KAAK,CAAC,CAAC,CAAC,GAAGqE,GAAG,EACzB,CAAC,CAACgE,MAAM,EAAEC,KAAK,CAAC,EAAEtJ,KAAK,GAAK;YAAC+C,CAAC,CAACsG,MAAM,EAAErJ,KAAK,CAAC;YAAEsJ,KAAK,GAAG,CAAC;SAAC,CAC1D,EACDzL,GAAG,EAAEmD,KAAK,GAAKA,KAAK,CAAC,CAAC,CAAC,CAAC,CACzB;AAGI,MAAMuI,eAAe,GAAGA,CAC7BnC,CAAI,EACJ/B,GAAW,EACXtC,CAA8C,yJAE9CpH,OAAAA,AAAI,EACFuF,UAAU,CACR;QAACkG,CAAC;QAAE,CAAW;KAAU,GACxBpG,KAAK,GAAKA,KAAK,CAAC,CAAC,CAAC,GAAGqE,GAAG,EACzB,CAAC,CAACgE,MAAM,EAAEC,KAAK,CAAC,EAAEtJ,KAAS,yJAAKrE,OAAI,AAAJA,EAAKoH,CAAC,CAACsG,MAAM,EAAErJ,KAAK,CAAC,sJAAE3E,MAAM,AAACwC,AAAG,CAAHA,EAAKuJ,CAAC,GAAK;gBAACA,CAAC;gBAAEkC,KAAK,GAAG,CAAC;aAAU,CAAC,CAAC,CACnG,EACDzL,GAAG,EAAEmD,KAAK,GAAKA,KAAK,CAAC,CAAC,CAAC,CAAC,CACzB;AAGI,MAAMT,YAAY,IACvB4B,OAKC,GAEDqH,qBAAqB,CAAC;QACpB,GAAGrH,OAAO;QACVsH,SAAS,EAAEvO,KAAK,CAACwO,8IAAAA;KAClB,CAAC;AAGG,MAAMF,qBAAqB,IAChCrH,OAMC,GAED5E,OAAO,CAAC,IACN,IAAIL,QAAQ,CACVyM,yBAAyB,CACvBxH,OAAO,CAAC5C,OAAO,EACf,CAAC,EACD,KAAK,EACL4C,OAAO,CAAC3B,OAAO,EACf2B,OAAO,CAAC1B,IAAI,EACZ0B,OAAO,CAACsH,SAAS,EACjBtH,OAAO,CAAC1C,IAAI,CACb,CACF,CACF;AAEH,cAAA,GACA,MAAMkK,yBAAyB,GAAGA,CAChCvC,CAAI,EACJ3G,IAAY,EACZmJ,KAAc,EACdvE,GAAW,EACXwE,MAAmC,EACnCJ,SAAyC,EACzC1G,CAAyB,2KAEzBxG,IAAI,CAACiF,MAAAA,AAAQ,EAAC;QACZrD,OAAO,GAAG6B,KAAsB,IAAI;YAClC,MAAM,CAACuH,KAAK,EAAEuC,QAAQ,EAAEC,SAAS,EAAEhL,SAAS,CAAC,GAAGiL,yBAAyB,CACvEhK,KAAK,EACL,CAAC,EACDoH,CAAC,EACD3G,IAAI,EACJmJ,KAAK,EACLvE,GAAG,EACHwE,MAAM,EACNJ,SAAS,EACT1G,CAAC,CACF;YACD,IAAI7H,KAAK,CAACuM,0JAAAA,AAAU,EAAC1I,SAAS,CAAC,EAAE;gBAC/B,6JAAOpD,OAAAA,AAAI,GAACY,IAAI,CAAC6C,0KAAAA,AAAK,EAACL,SAAS,CAAC,mKAAE1C,OAAO,CAACuF,GAAAA,AAAQ,0KAACrF,IAAI,CAACwJ,QAAAA,AAAU,EAACwB,KAAK,CAAC,CAAC,CAAC;YAC9E;YACA,IAAI9G,IAAI,GAAG4E,GAAG,EAAE;gBACd,WAAO9I,IAAI,CAACwJ,4KAAAA,AAAU,EAACwB,KAAK,CAAC;YAC/B;YACA,OAAOoC,yBAAyB,CAACpC,KAAK,EAAEuC,QAAQ,EAAEC,SAAS,EAAE1E,GAAG,EAAEwE,MAAM,EAAEJ,SAAS,EAAE1G,CAAC,CAAC;QACzF,CAAC;QACDzE,SAAS,EAAE/B,IAAI,CAACsF,sKAAI;QACpBrD,MAAM,EAAEA,CAAA,2KAAMjC,IAAI,CAACwJ,QAAAA,AAAU,EAACqB,CAAC;KAChC,CAAC;AAEJ,cAAA,GACA,MAAM4C,yBAAyB,GAAGA,CAChChK,KAAsB,EACtB0H,KAAa,EACbN,CAAI,EACJ3G,IAAY,EACZmJ,KAAc,EACdvE,GAAW,EACXwE,MAAmC,EACnCJ,SAAyC,EACzC1G,CAAyB,KACgB;IACzC,IAAI2E,KAAK,KAAK1H,KAAK,CAACf,MAAM,EAAE;QAC1B,OAAO;YAACmI,CAAC;YAAE3G,IAAI;YAAEmJ,KAAK;YAAE1O,KAAK,CAAC8C,qJAAK,AAALA,EAAW;SAAC;IAC5C;IACA,MAAMiM,IAAI,yJAAGtO,OAAAA,AAAI,EAACqE,KAAK,MAAE9E,KAAK,CAACkF,qJAAAA,AAAS,EAACsH,KAAK,CAAC,CAAC;IAChD,MAAMwC,KAAK,GAAGzJ,IAAI,GAAGoJ,MAAM,CAACzC,CAAC,EAAE6C,IAAI,CAAC;IACpC,IAAIC,KAAK,IAAI7E,GAAG,EAAE;QAChB,OAAO2E,yBAAyB,CAAChK,KAAK,EAAE0H,KAAK,GAAG,CAAC,EAAE3E,CAAC,CAACqE,CAAC,EAAE6C,IAAI,CAAC,EAAEC,KAAK,EAAE,IAAI,EAAE7E,GAAG,EAAEwE,MAAM,EAAEJ,SAAS,EAAE1G,CAAC,CAAC;IACxG;IACA,MAAMoH,UAAU,GAAGV,SAAS,CAACQ,IAAI,CAAC;IAClC,IAAIE,UAAU,CAAClL,MAAM,IAAI,CAAC,IAAI,CAAC2K,KAAK,EAAE;QACpC,2EAAA;QACA,4EAAA;QACA,oCAAA;QACA,OAAO;YAAC7G,CAAC,CAACqE,CAAC,EAAE6C,IAAI,CAAC;YAAEC,KAAK;YAAE,IAAI;aAAEvO,4JAAAA,AAAI,EAACqE,KAAK,qJAAE9E,KAAK,CAACgH,CAAAA,AAAI,EAACwF,KAAK,GAAG,CAAC,CAAC,CAAC;SAAC;IACtE;IACA,IAAIyC,UAAU,CAAClL,MAAM,IAAI,CAAC,IAAI2K,KAAK,EAAE;QACnC,yEAAA;QACA,uCAAA;QACA,OAAO;YAACxC,CAAC;YAAE3G,IAAI;YAAEmJ,KAAK;kKAAEjO,OAAAA,AAAI,EAACqE,KAAK,qJAAE9E,KAAK,CAACgH,CAAAA,AAAI,EAACwF,KAAK,CAAC,CAAC;SAAC;IACzD;IACA,gFAAA;IACA,uEAAA;IACA,MAAM0C,IAAI,yJAAGzO,OAAI,AAAJA,EAAKwO,UAAU,qJAAEjP,KAAK,CAACmD,MAAAA,AAAS,wJAAC1C,OAAAA,AAAI,EAACqE,KAAK,qJAAE9E,KAAK,CAACgH,CAAAA,AAAI,EAACwF,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAClF,OAAOsC,yBAAyB,CAACI,IAAI,EAAE,CAAC,EAAEhD,CAAC,EAAE3G,IAAI,EAAEmJ,KAAK,EAAEvE,GAAG,EAAEwE,MAAM,EAAEJ,SAAS,EAAE1G,CAAC,CAAC;AACtF,CAAC;AAGM,MAAMsH,2BAA2B,IACtClI,OAMC,GAED5E,OAAO,CAAC,IACN,IAAIL,QAAQ,CACVoN,+BAA+B,CAC7BnI,OAAO,CAAC5C,OAAO,EACf4C,OAAO,CAAC3B,OAAO,EACf2B,OAAO,CAAC1B,IAAI,EACZ0B,OAAO,CAACsH,SAAS,EACjBtH,OAAO,CAAC1C,IAAI,EACZ,CAAC,EACD,KAAK,CACN,CACF,CACF;AAGI,MAAM8K,kBAAkB,IAC7BpI,OAKC,GAEDkI,2BAA2B,CAAC;QAC1B,GAAGlI,OAAO;QACVsH,SAAS,GAAGzJ,KAAK,uJAAK3E,MAAM,CAACoD,GAAAA,AAAO,qJAACvD,KAAK,AAACwO,AAAE,CAAFA,CAAG1J,KAAK,CAAC;KACrD,CAAC;AAEJ,MAAMsK,+BAA+B,GAAGA,CACtClD,CAAI,EACJ/B,GAAW,EACXwE,MAAwD,EACxDJ,SAAgE,EAChE1G,CAAgD,EAChDtC,IAAY,EACZmJ,KAAc,2KAEdrN,IAAI,CAACiF,MAAAA,AAAQ,EAAC;QACZrD,OAAO,GAAG6B,KAAsB,yJAC9BrE,OAAI,AAAJA,0KACEY,IAAI,CAACyF,QAAAA,AAAU,EAACwI,+BAA+B,CAACpD,CAAC,EAAE/B,GAAG,EAAEwE,MAAM,EAAEJ,SAAS,EAAE1G,CAAC,EAAE/C,KAAK,EAAE4J,KAAK,EAAEnJ,IAAI,EAAE,CAAC,CAAC,CAAC,MACrGlE,IAAI,CAAC4C,yKAAAA,AAAO,EAAC,CAAC,CAACoI,KAAK,EAAEuC,QAAQ,EAAEC,SAAS,EAAEhL,SAAS,CAAC,KAAI;gBACvD,uJAAI7D,KAAK,CAACuM,OAAAA,AAAU,EAAC1I,SAAS,CAAC,EAAE;oBAC/B,OAAOpD,6JAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAAA,AAAK,EAACL,SAAS,CAAC,mKAAE1C,OAAO,CAACuF,GAAAA,AAAQ,0KAACrF,IAAI,CAACwJ,QAAAA,AAAU,EAACwB,KAAK,CAAC,CAAC,CAAC;gBAC9E;gBACA,IAAI9G,IAAI,GAAG4E,GAAG,EAAE;oBACd,+KAAO9I,IAAI,CAACwJ,QAAAA,AAAU,EAACwB,KAAK,CAAC;gBAC/B;gBACA,OAAO+C,+BAA+B,CAAC/C,KAAK,EAAElC,GAAG,EAAEwE,MAAM,EAAEJ,SAAS,EAAE1G,CAAC,EAAE+G,QAAQ,EAAEC,SAAS,CAAC;YAC/F,CAAC,CAAC,CACH;QACHzL,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBrD,MAAM,EAAEA,CAAA,2KAAMjC,IAAI,CAACwJ,QAAAA,AAAU,EAACqB,CAAC;KAChC,CAAC;AAEJ,cAAA,GACA,MAAMoD,+BAA+B,GAAGA,CACtCpD,CAAI,EACJ/B,GAAW,EACXwE,MAAwD,EACxDJ,SAAgE,EAChE1G,CAAgD,EAChD/C,KAAsB,EACtB4J,KAAc,EACdnJ,IAAY,EACZiH,KAAa,KACqE;IAClF,IAAIA,KAAK,KAAK1H,KAAK,CAACf,MAAM,EAAE;QAC1B,2JAAO5D,MAAM,CAACoD,GAAAA,AAAO,EAAC;YAAC2I,CAAC;YAAE3G,IAAI;YAAEmJ,KAAK;aAAE1O,KAAK,CAAC8C,oJAAAA,AAAK,EAAM;SAAC,CAAC;IAC5D;IACA,MAAMiM,IAAI,yJAAGtO,OAAAA,AAAI,EAACqE,KAAK,qJAAE9E,KAAK,CAACkF,MAAAA,AAAS,EAACsH,KAAK,CAAC,CAAC;IAChD,OAAO/L,6JAAAA,AAAI,EACTkO,MAAM,CAACzC,CAAC,EAAE6C,IAAI,CAAC,sJACf5O,MAAM,AAACwC,AAAG,CAAHA,EAAK4M,OAAO,GAAKhK,IAAI,GAAGgK,OAAO,CAAC,sJACvCpP,MAAM,CAAC8D,GAAAA,AAAO,GAAE+K,KAAK,IAAI;QACvB,IAAIA,KAAK,IAAI7E,GAAG,EAAE;YAChB,6JAAO1J,OAAAA,AAAI,EACToH,CAAC,CAACqE,CAAC,EAAE6C,IAAI,CAAC,sJACV5O,MAAM,CAAC8D,GAAAA,AAAO,GAAEiI,CAAC,GACfoD,+BAA+B,CAACpD,CAAC,EAAE/B,GAAG,EAAEwE,MAAM,EAAEJ,SAAS,EAAE1G,CAAC,EAAE/C,KAAK,EAAE,IAAI,EAAEkK,KAAK,EAAExC,KAAK,GAAG,CAAC,CAAC,CAC7F,CACF;QACH;QACA,6JAAO/L,OAAAA,AAAI,EACT8N,SAAS,CAACQ,IAAI,CAAC,sJACf5O,MAAM,CAAC8D,GAAAA,AAAO,GAAEgL,UAAU,IAAI;YAC5B,IAAIA,UAAU,CAAClL,MAAM,IAAI,CAAC,IAAI,CAAC2K,KAAK,EAAE;gBACpC,2EAAA;gBACA,4EAAA;gBACA,oCAAA;gBACA,6JAAOjO,OAAAA,AAAI,EACToH,CAAC,CAACqE,CAAC,EAAE6C,IAAI,CAAC,MACV5O,MAAM,CAACwC,+IAAG,AAAHA,GAAKuJ,CAAC,GAAK;wBAACA,CAAC;wBAAE8C,KAAK;wBAAE,IAAI;8KAAEvO,OAAAA,AAAI,EAACqE,KAAK,qJAAE9E,KAAK,CAACgH,CAAI,AAAJA,EAAKwF,KAAK,GAAG,CAAC,CAAC,CAAC;qBAAC,CAAC,CACxE;YACH;YACA,IAAIyC,UAAU,CAAClL,MAAM,IAAI,CAAC,IAAI2K,KAAK,EAAE;gBACnC,yEAAA;gBACA,sCAAA;gBACA,QAAOvO,MAAM,CAACoD,sJAAAA,AAAO,EAAC;oBAAC2I,CAAC;oBAAE3G,IAAI;oBAAEmJ,KAAK;0KAAEjO,OAAAA,AAAI,EAACqE,KAAK,qJAAE9E,KAAK,CAACgH,CAAAA,AAAI,EAACwF,KAAK,CAAC,CAAC;iBAAC,CAAC;YACzE;YACA,gFAAA;YACA,uEAAA;YACA,MAAM0C,IAAI,GAAGzO,6JAAAA,AAAI,EAACwO,UAAU,qJAAEjP,KAAK,CAACmD,MAAAA,AAAS,wJAAC1C,OAAI,AAAJA,EAAKqE,KAAK,oJAAE9E,KAAK,CAACgH,EAAAA,AAAI,EAACwF,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAClF,OAAO8C,+BAA+B,CAACpD,CAAC,EAAE/B,GAAG,EAAEwE,MAAM,EAAEJ,SAAS,EAAE1G,CAAC,EAAEqH,IAAI,EAAER,KAAK,EAAEnJ,IAAI,EAAE,CAAC,CAAC;QAC5F,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;AACH,CAAC;AAGM,MAAMtB,OAAO,GAAA,WAAA,yJAAG1D,OAAAA,AAAI,EASzB,CAAC,EACD,CAACkC,IAAI,EAAEoF,CAAC,GAAK6E,QAAQ,CAACjK,IAAI,EAAE;QAAEW,SAAS,EAAEuD,IAAI;QAAEqB,SAAS,EAAEH;IAAC,CAAE,CAAC,CAC/D;AAGM,MAAMiB,OAAO,IAAiBjB,CAAwC,IAAsC;IACjH,MAAM2H,OAAO,2KAAoEnO,IAAI,CAAC2B,WAAAA,AAAa,EAAC;QAClGC,OAAO,EAAG6B,KAAsB,IAC9BrE,6JAAAA,AAAI,EAACY,IAAI,CAACyF,gLAAU,AAAVA,sJAAW3G,MAAM,CAAC2I,GAAAA,AAAO,EAAChE,KAAK,GAAGE,CAAC,GAAK6C,CAAC,CAAC7C,CAAC,CAAC,EAAE;gBAAEyK,OAAO,EAAE;YAAI,CAAE,CAAC,CAAC,yKAAEpO,IAAI,CAAC4C,MAAAA,AAAO,EAAC,IAAMuL,OAAO,CAAC,CAAC;QAC3GpM,SAAS,sKAAE/B,IAAI,CAACgC,OAAS;QACzBC,MAAM,EAAEA,CAAA,uKAAMjC,IAAI,CAACgJ,EAAAA;KACpB,CAAC;IACF,OAAO,IAAIrI,QAAQ,CAACwN,OAAO,CAAC;AAC9B,CAAC;AAGM,MAAME,YAAY,GACvB7H,CAAqD,IACjB;IACpC,MAAM2H,OAAO,2KAAoEnO,IAAI,CAAC2B,WAAAA,AAAa,EAAC;QAClGC,OAAO,GAAG6B,KAAsB,yJAAKrE,OAAAA,AAAI,yKAACY,IAAI,CAACyF,SAAAA,AAAU,EAACe,CAAC,CAAC/C,KAAK,CAAC,CAAC,yKAAEzD,IAAI,CAAC4C,MAAAA,AAAO,EAAC,IAAMuL,OAAO,CAAC,CAAC;QACjGpM,SAAS,sKAAE/B,IAAI,CAACgC,OAAS;QACzBC,MAAM,EAAEA,CAAA,uKAAMjC,IAAI,CAACgJ,EAAAA;KACpB,CAAC;IACF,OAAO,IAAIrI,QAAQ,CAACwN,OAAO,CAAC;AAC9B,CAAC;AAGM,MAAMG,YAAY,IACvB9H,CAA8C,IACb;IACjC,MAAM2H,OAAO,2KAA8EnO,IAAI,CAAC2B,WAAa,AAAbA,EAAc;QAC5GC,OAAO,GAAG6B,KAAsB,GAAK8K,kBAAkB,CAAC/H,CAAC,EAAE/C,KAAK,EAAE,CAAC,EAAEA,KAAK,CAACf,MAAM,EAAEyL,OAAO,CAAC;QAC3FpM,SAAS,sKAAE/B,IAAI,CAACgC,OAAS;QACzBC,MAAM,EAAEA,CAAA,GAAMjC,IAAI,CAACgJ,sKAAAA;KACpB,CAAC;IACF,OAAO,IAAIrI,QAAQ,CAACwN,OAAO,CAAC;AAC9B,CAAC;AAED,cAAA,GACA,MAAMI,kBAAkB,GAAGA,CACzB/H,CAA8C,EAC9C/C,KAAsB,EACtB0H,KAAa,EACbzI,MAAc,EACd8L,IAA+E,KACF;IAC7E,IAAIrD,KAAK,KAAKzI,MAAM,EAAE;QACpB,OAAO8L,IAAI;IACb;IACA,6JAAOpP,OAAAA,AAAI,0KACTY,IAAI,CAACyF,QAAAA,AAAU,EAACe,CAAC,EAACpH,4JAAAA,AAAI,EAACqE,KAAK,qJAAE9E,KAAK,CAACkF,MAAAA,AAAS,EAACsH,KAAK,CAAC,CAAC,CAAC,CAAC,0KACvDnL,IAAI,CAAC4C,KAAAA,AAAO,GAAEgC,IAAI,GAChBA,IAAI,GACF2J,kBAAkB,CAAC/H,CAAC,EAAE/C,KAAK,EAAE0H,KAAK,GAAG,CAAC,EAAEzI,MAAM,EAAE8L,IAAI,CAAC,2KACrDxO,IAAI,CAAC6C,GAAAA,AAAK,wJAACzD,OAAAA,AAAI,EAACqE,KAAK,oJAAE9E,KAAK,CAACgH,EAAI,AAAJA,EAAKwF,KAAK,CAAC,CAAC,CAAC,CAC7C,mKACDrL,OAAO,CAAC2O,GAAAA,AAAQ,GAAEnD,KAAK,yJAAKlM,OAAAA,AAAI,EAACY,IAAI,CAAC6C,2KAAAA,AAAK,wJAACzD,OAAAA,AAAI,EAACqE,KAAK,qJAAE9E,KAAK,CAACgH,CAAAA,AAAI,EAACwF,KAAK,CAAC,CAAC,CAAC,mKAAErL,OAAO,CAACuF,GAAQ,AAARA,0KAASrF,IAAI,CAACsF,EAAAA,AAAI,EAACgG,KAAK,CAAC,CAAC,CAAC,CAAC,CAClH;AACH,CAAC;AAGM,MAAMoD,iBAAiB,IAC5BlI,CAA2D,IAC1B;IACjC,MAAMmI,MAAM,2KAAoE3O,IAAI,CAACiF,MAAAA,AAAQ,EAAC;QAC5FrD,OAAO,GAAG6B,KAAsB,yJAC9BrE,OAAI,AAAJA,yKACEY,IAAI,CAACyF,SAAAA,AAAU,EAACe,CAAC,CAAC/C,KAAK,CAAC,CAAC,0KACzBzD,IAAI,CAAC4C,KAAAA,AAAO,GAAE4L,IAAI,GAAKA,IAAI,GAAGG,MAAM,uKAAG3O,IAAI,CAACgJ,EAAI,CAAC,CAClD;QACHjH,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBrD,MAAM,EAAEA,CAAA,GAAMjC,IAAI,CAACgJ,sKAAAA;KACpB,CAAC;IACF,OAAO,IAAIrI,QAAQ,CAACgO,MAAM,CAAC;AAC7B,CAAC;AAGM,MAAMtM,WAAW,IACtBvC,OAAkF,GACpD,IAAIa,QAAQ,CAACb,OAAO,CAAC;AAG9C,MAAM2F,UAAU,IAAamJ,MAA8B,GAChE,IAAIjO,QAAQ,CAACX,IAAI,CAACyF,gLAAAA,AAAU,EAACmJ,MAAM,CAAC,CAAC;AAGhC,MAAMC,UAAU,GAAGA,CACxBC,MAAyB,EACzBlJ,OAEC,GACuBmJ,SAAS,CAACD,MAAM,EAAElJ,OAAO,CAAC;AAG7C,MAAMoJ,QAAQ,IACnBC,IAIC,GAED,IAAItO,QAAQ,kKAACb,OAAO,CAACoP,OAAY,AAAZA,GAAa9P,4JAAI,AAAJA,EAAK6P,IAAI,sJAAEnQ,MAAM,AAACwC,AAAG,CAAHA,CAAI6N,YAAY,CAAC,CAAC,CAAC,CAAC;AAE1E,MAAMA,YAAY,IAChBF,IAE2E,2KAE3EjP,IAAI,CAACiF,MAAQ,AAARA,EAAS;QACZrD,OAAO,GAAG6B,KAAsB,oKAC9B3D,OAAO,CAAC4G,MAAAA,AAAW,0KAAC1G,IAAI,CAACyF,QAAAA,AAAU,EAACwJ,IAAI,CAAC1P,MAAM,CAACoL,oJAAAA,AAAI,EAAClH,KAAK,CAAC,CAAC,CAAC,EAAE;gBAC7D1B,SAAS,EAAEA,CAAC,CAACqN,MAAM,EAAE5M,SAAS,CAAC,uJAC7BzD,MAAM,CAACsN,CAAAA,AAAK,EAAC+C,MAAM,EAAE;wBACnBC,MAAM,EAAG/D,KAAK,IAAKlM,6JAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAAA,AAAK,EAACL,SAAS,CAAC,mKAAE1C,OAAO,CAACuF,GAAAA,AAAQ,EAACrF,IAAI,CAACsF,0KAAAA,AAAI,EAACgG,KAAK,CAAC,CAAC,CAAC;wBAClFgE,OAAO,GAAGlI,CAAC,yJAAKhI,OAAI,AAAJA,0KAAKY,IAAI,CAAC6C,GAAK,AAALA,EAAML,SAAS,CAAC,kKAAE1C,OAAO,CAACuF,IAAAA,AAAQ,0KAACrF,IAAI,CAACwJ,QAAAA,AAAU,EAACpC,CAAC,CAAC,CAAC;qBACjF,CAAC;gBACJT,SAAS,EAAEA,CAAA,GAAMwI,YAAY,CAACF,IAAI;aACnC,CAAC;QACJlN,SAAS,sKAAE/B,IAAI,CAACsF,EAAI;QACpBrD,MAAM,EAAEA,CAAA,oKACNnC,OAAO,CAAC4G,MAAAA,AAAW,0KAAC1G,IAAI,CAACyF,QAAAA,AAAU,EAACwJ,IAAI,qJAAC1P,MAAM,CAACqL,AAAI,EAAE,CAAC,CAAC,GAAE;gBACxD7I,SAAS,EAAEA,CAAC,CAACqN,MAAM,EAAE5M,SAAS,CAAC,GAC7BzD,MAAM,CAACsN,qJAAAA,AAAK,EAAC+C,MAAM,EAAE;wBACnBC,MAAM,GAAG/D,KAAK,yJAAKlM,OAAI,AAAJA,0KAAKY,IAAI,CAAC6C,GAAAA,AAAK,EAACL,SAAS,CAAC,mKAAE1C,OAAO,CAACuF,GAAAA,AAAQ,yKAACrF,IAAI,CAACsF,GAAAA,AAAI,EAACgG,KAAK,CAAC,CAAC,CAAC;wBAClFgE,OAAO,GAAGlI,CAAC,yJAAKhI,OAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAK,AAALA,EAAML,SAAS,CAAC,mKAAE1C,OAAO,CAACuF,GAAAA,AAAQ,0KAACrF,IAAI,CAACwJ,QAAAA,AAAU,EAACpC,CAAC,CAAC,CAAC;qBACjF,CAAC;gBACJT,SAAS,EAAEA,CAAA,2KACT3G,IAAI,CAACyF,QAAAA,AAAU,sJACb3G,MAAM,CAACgJ,MAAAA,AAAU,EACf,2FAA2F,CAC5F;aAEN;KACJ,CAAC;AAGG,MAAMiH,SAAS,GAAGA,CACvBQ,KAAwB,EACxB3J,OAEC,GAEDA,OAAO,EAAE4J,QAAQ,GACfN,YAAY,qJACVpQ,MAAM,AAACwC,AAAG,CAAHA,qJACLxC,MAAM,CAAC2Q,UAAAA,AAAc,sJAAC3Q,MAAM,CAACoD,GAAAA,AAAO,EAACqN,KAAK,CAAC,iJAAE5P,KAAK,CAAC6P,KAAQ,CAAC,EAC5DT,SAAS,CACV,CACF,GACDV,YAAY,EAAE5K,KAAsB,yJAAKrE,OAAAA,AAAI,qJAACO,KAAK,CAAC+P,KAAAA,AAAQ,EAACH,KAAK,EAAE9L,KAAK,CAAC,CAAC,CAAC;AAGzE,MAAMkM,IAAI,GAAGA,CAAA,GAClBnL,IAAI,KACFjF,MAAM,CAACqL,gJAAAA,AAAI,EAAuB,mJAClCrL,MAAM,CAACqQ,EAAM,EACb,CAACC,MAAM,EAAEpM,KAAK,uJACZlE,MAAM,CAAC8M,CAAAA,AAAK,EAACwD,MAAM,EAAE;YACnBvD,MAAM,EAAEA,CAAA,uJAAM/M,MAAM,CAAK,AAAJoL,EAAKlH,KAAK,CAAC;YAChC8I,MAAM,EAAEA,CAAA,GAAMsD;SACf,CAAC,CACL;AAGI,MAAMnD,cAAc,IAAoBtL,IAA+B,GAC5E,IAAIT,QAAQ,kKAACb,OAAO,CAAC0I,AAAK,EAACtH,SAAS,CAACE,IAAI,CAAC,CAAC,CAAC;AAGvC,MAAM0O,IAAI,GAAGA,CAAA,GAClBvM,cAAc,qJAAChE,MAAM,CAACqL,AAAI,EAAM,GAAE,CAACC,CAAC,EAAEpH,KAAK,IAAKlE,MAAM,CAACwQ,qJAAAA,AAAM,qJAACpR,KAAK,CAACmR,CAAAA,AAAI,EAACrM,KAAK,CAAC,EAAE,IAAMoH,CAAC,CAAC,CAAC;AAGrF,MAAMjC,QAAQ,IAAO/G,KAAqB,GAC/C,IAAIlB,QAAQ,CAACX,IAAI,CAACgB,6KAAAA,AAAO,EAAC,4KAAMhB,IAAI,CAAC6C,GAAAA,AAAK,EAAChB,KAAK,CAAC,CAAC,CAAC;AAG9C,MAAMP,GAAG,GAAA,WAAA,yJAAGpC,OAAI,AAAJA,EAGjB,CAAC,EAAE,CAACkC,IAAI,EAAEoF,CAAC,KAAI;IACf,OAAO,IAAI7F,QAAQ,CAACvB,6JAAAA,AAAI,EAAC8B,SAAS,CAACE,IAAI,CAAC,mKAAEtB,MAAQwB,AAAG,CAAJ,CAACA,AAAIkF,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC;AAGK,MAAM6B,SAAS,GAAA,WAAA,IAAGnJ,4JAAAA,AAAI,EAS3B,CAAC,EACD,CAACkC,IAAI,EAAEoF,CAAC,GAAK,IAAI7F,QAAQ,uJAACvB,OAAAA,AAAI,EAAC8B,SAAS,CAACE,IAAI,CAAC,MAAEtB,OAAO,CAACuI,iKAAAA,AAAS,EAAC7B,CAAC,CAAC,CAAC,CAAC,CACvE;AAGM,MAAMwJ,QAAQ,GAAA,WAAA,yJAAG9Q,OAAAA,AAAI,EAI1B,CAAC,EACD,CAACkC,IAAI,EAAEoF,CAAC,GAAK,IAAI7F,QAAQ,CAACvB,6JAAAA,AAAI,EAAC8B,SAAS,CAACE,IAAI,CAAC,mKAAEtB,OAAO,CAACkQ,GAAAA,AAAQ,EAACxJ,CAAC,CAAC,CAAC,CAAC,CACtE;AAGM,MAAMyJ,WAAW,GAAA,WAAA,IAAG/Q,4JAAAA,AAAI,EAI7B,CAAC,EACD,CAACkC,IAAI,EAAEoF,CAAC,GAAK,IAAI7F,QAAQ,uJAACvB,OAAAA,AAAI,EAAC8B,SAAS,CAACE,IAAI,CAAC,mKAAEtB,OAAO,CAACoQ,CAAM,AAANA,EAAOvR,KAAK,CAAC2C,mJAAAA,AAAG,EAACkF,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/E;AAGM,MAAM2J,KAAK,GAAA,WAAA,GAA8B1K,UAAU,iJAAC3G,MAAM,CAACqR,CAAK,CAAC;AAGjE,MAAMJ,MAAM,GAAA,WAAA,yJAAG7Q,OAAAA,AAAI,EASxB,CAAC,EACD,CACEkC,IAA+B,EAC/BgP,IAA6C,GAE7C,IAAIzP,QAAQ,sJACVvB,QAAAA,AAAI,EAAC8B,SAAS,CAACE,IAAI,CAAC,mKAAEtB,OAAO,CAACiQ,CAAAA,AAAM,EAAC,IAAM7O,SAAS,CAACkP,IAAI,EAAE,CAAC,CAAC,CAAC,CAC/D,CACJ;AAGM,MAAMC,cAAc,GAAA,WAAA,yJAAGnR,OAAAA,AAAI,EAIhC,CAAC,EACD,CAACkC,IAAI,EAAE0I,OAAO,GAAK,IAAInJ,QAAQ,uJAACvB,OAAAA,AAAI,EAAC8B,SAAS,CAACE,IAAI,CAAC,0KAAEpB,IAAI,CAACqQ,YAAAA,AAAc,EAACvG,OAAO,CAAC,CAAC,CAAC,CACrF;AAGM,MAAMwG,IAAI,GAAA,WAAA,yJAAGpR,OAAAA,AAAI,EAStB,CAAC,EACD,CAACkC,IAAI,EAAEgP,IAAI,yJAAKhR,OAAAA,AAAI,EAACgC,IAAI,EAAEmP,QAAQ,CAACH,IAAI,CAAC,EAAE9O,GAAG,iJAACvC,MAAM,CAACuE,CAAK,CAAC,CAAC,CAC9D;AAGM,MAAMiN,QAAQ,GAAA,WAAA,yJAAGrR,OAAAA,AAAI,GAiBzBsR,IAAI,GAAK1P,MAAM,CAAC0P,IAAI,CAAC,CAAC,CAAC,CAAC,EACzB,CAACpP,IAAI,EAAEgP,IAAI,EAAExK,OAAO,GAClB6K,QAAQ,CAACrP,IAAI,EAAE;QACbsP,KAAK,EAAEN,IAAI;QACXO,UAAU,GAAGC,QAAQ,qLAAK7Q,OAAc8Q,AAAI,EAAC/R,IAAN,CAAC+R,CAAW,CAACvP,mJAAAA,AAAG,EAACsP,QAAQ,kJAAE7R,MAAM,CAAK,AAAJ+R,CAAK,CAAC;QAC/EC,WAAW,GAAGC,QAAQ,qLAAKjR,OAAc8Q,AAAI,EAAC/R,IAAN,CAAC+R,CAAW,CAACvP,mJAAAA,AAAG,EAAC0P,QAAQ,kJAAEjS,MAAM,CAACkS,CAAK,CAAC,CAAC;QACjFC,QAAQ,EAAEtL,OAAO,EAAEsL,QAAQ,IAAI;KAChC,CAAC,CACL;AAGM,MAAMT,QAAQ,GAAA,WAAA,GAAGvR,6JAAAA,AAAI,EAmB1B,CAAC,EACD,CACEkC,IAA+B,EAC/BwE,OAKC,KACuD;IACxD,SAAS0K,IAAIA,CAACa,KAAkB;QAC9B,2JAAOrS,MAAM,AAACsS,AAAG,CAAHA,CAAI,aAAS;YACzB,MAAMtC,MAAM,GAAG,QAAOpP,MAAM,CAAC2R,sJAAO,AAAPA,EAE3BzL,OAAO,EAAEsL,QAAQ,IAAI,EAAE,CAAC;YAC1B,MAAMI,aAAa,GAAG,0JAAOzR,KAAK,CAAC0R,GAAAA,AAAM,GAAC7R,MAAM,CAAC8R,wJAAAA,AAAS,EAAC1C,MAAM,CAAC,EAAEqC,KAAK,CAAC;YAC1E,MAAMM,aAAa,GAAG,0JAAO5R,KAAK,CAAC0R,GAAAA,AAAM,GAAC7R,MAAM,CAAC8R,wJAAAA,AAAS,EAAC1C,MAAM,CAAC,EAAEqC,KAAK,CAAC;YAC1E,MAAMxC,MAAM,oKAAG7O,OAAO,CAAC4R,GAAAA,AAAQ,EAAC5C,MAAM,CAAC;YACvC,MAAM6C,MAAM,GAAG7R,OAAO,CAACiP,qKAAAA,AAAS,EAACuC,aAAa,CAAC,CAAClS,IAAI,yKAClDY,IAAI,CAACoG,IAAM,AAANA,EAAOlF,SAAS,CAACE,IAAI,CAAC,CAAC,GAC5BtB,OAAO,CAAC8R,kKAAAA,AAAO,0KAAC5R,IAAI,CAACyF,QAAAA,AAAU,GAAC9F,KAAK,CAAC6P,uJAAQ,AAARA,EAAS8B,aAAa,CAAC,CAAC,CAAC,mKAC/DxR,OAAO,CAAC+R,IAAAA,AAAS,EAAC;gBAChBnB,KAAK,mKAAE5Q,OAAO,CAACiP,IAAAA,AAAS,EAAC0C,aAAa,CAAC,CAACrS,IAAI,CAC1CY,IAAI,CAACoG,4KAAAA,AAAM,EAAClF,SAAS,CAAC0E,OAAO,CAAC8K,KAAK,CAAC,CAAC,mKACrC5Q,OAAO,CAAC8R,EAAO,AAAPA,0KAAQ5R,IAAI,CAACyF,QAAAA,AAAU,qJAAC9F,KAAK,CAAC6P,KAAAA,AAAQ,EAACiC,aAAa,CAAC,CAAC,CAAC,CAChE;gBACDd,UAAU,EAAE/K,OAAO,CAAC+K,UAAU;gBAC9BI,WAAW,EAAEnL,OAAO,CAACmL,WAAAA;aACtB,CAAC,CACH;YACD,MAAMe,YAAY,oKAAGhS,OAAO,CAAC+R,IAAAA,AAAS,EAAClD,MAAM,EAAE;gBAC7C+B,KAAK,EAAEiB,MAAM;gBACbhB,UAAU,EAAEA,CAAA,qLAAM5Q,QAAcgS,AAAK,EAAC5S,GAAP,CAAC4S,yJAAc,CAAC;gBAC/ChB,WAAW,EAAGiB,IAAI,sLAAKjS,OAAc8Q,AAAI,EAACmB,IAAN,AAAU,CAATnB;aACtC,CAQA;YACD,OAAO,IAAIlQ,QAAQ,CAACmR,YAAY,CAAC;QACnC,CAAC,CAAC;IACJ;IACA,OAAOG,gBAAgB,CAAC3B,IAAI,CAAC;AAC/B,CAAC,CACF;AAGM,MAAM4B,WAAW,GAAA,WAAA,yJAAGhT,OAAAA,AAAI,EAS7B,CAAC,EACD,CAACkC,IAAI,EAAE+Q,EAAE,yJAAK/S,OAAAA,AAAI,EAACgC,IAAI,EAAEgR,eAAe,CAACD,EAAE,oJAAEhT,WAAQ,CAAC,CAAC,CACxD;AAGM,MAAMiT,eAAe,GAAA,WAAA,yJAAGlT,OAAAA,AAAI,EAWjC,CAAC,EACD,CAACkC,IAAI,EAAE+Q,EAAE,EAAE3L,CAAC,KAAI;IACd,MAAMR,UAAU,yJAAG5G,OAAI,AAAJA,EACjBgC,IAAI,EACJF,SAAS,kKACTpB,OAAO,CAAC2O,IAAAA,AAAQ,GAAEnD,KAAK,uJACrB/L,MAAM,CAAC8M,CAAAA,AAAK,EAAC8F,EAAE,CAAC7G,KAAK,CAAC,EAAE;YACtBgB,MAAM,EAAEA,CAAA,2KAAMtM,IAAI,CAACkI,WAAAA,AAAa,EAAC,IAAMxJ,KAAK,CAACkJ,mJAAAA,AAAG,EAACpB,CAAC,CAAC8E,KAAK,CAAC,CAAC,CAAC;YAC3DiB,MAAM,sKAAEvM,IAAI,CAACsF,EAAAA;SACd,CAAC,CACH,CACF;IACD,OAAO,IAAI3E,QAAQ,CAACqF,UAAU,CAAC;AACjC,CAAC,CACF;AAGM,MAAMqM,OAAO,IAClBC,GAAsB,GACqBC,WAAW,CAACD,GAAG,oJAAEnT,WAAQ,CAAC;AAGhE,MAAMoT,WAAW,GAAGA,CACzBD,GAAsB,EACtB9L,CAAmC,GACQf,UAAU,KAAC3G,MAAM,CAACwC,+IAAAA,AAAG,EAACgR,GAAG,EAAE9L,CAAC,CAAC,CAAC;AAGpE,MAAMgM,iBAAiB,GAAGA,CAC/BF,GAAsB,EACtB9L,CAAwD,GACbf,UAAU,qJAAC3G,MAAM,CAAC8D,GAAAA,AAAO,EAAC0P,GAAG,EAAE9L,CAAC,CAAC,CAAC;AAGxE,MAAMiM,eAAe,GAAGA,CAC7BH,GAAsB,EACtB9L,CAA2D,GAE3D,IAAI7F,QAAQ,uJAACvB,OAAAA,AAAI,sJAACN,MAAM,AAACwC,AAAG,CAAHA,CAAIgR,GAAG,GAAGD,OAAO,GAAKnR,SAAS,CAACsF,CAAC,CAAC6L,OAAO,CAAC,CAAC,CAAC,8JAAEvS,OAAO,CAACuJ,EAAM,CAAC,CAAC;AAGlF,MAAMsB,IAAI,IAAQ7F,SAAwB,GAC/CN,IAAI,CAAC,KAAK,GAAGI,IAAI,GAAK,CAACA,IAAI,EAAE,CAAClD,GAAG,EAAE+B,KAAK,GAAK/B,GAAG,IAAIoD,SAAS,CAACrB,KAAK,CAAC,CAAC;AAGhE,MAAMiP,UAAU,GAAA,WAAA,yJAAGxT,OAAI,AAAJA,EAGxB,CAAC,EAAE,CAA4BkC,IAA+B,EAAEoF,CAAgB,KAAgC;IAChH,MAAMR,UAAU,yJAAG5G,OAAAA,AAAI,0KACrBY,IAAI,CAACyF,QAAAA,AAAU,mJAAC7F,GAAG,CAACkG,GAAAA,AAAI,oJAACnH,KAAK,CAAC8C,GAAK,AAALA,EAAW,CAAC,CAAC,2KAC5CzB,IAAI,CAAC4C,KAAAA,AAAO,GAAE2I,GAAG,yJACfnM,OAAI,AAAJA,EACEuT,kBAAkB,CAAQ,KAAK,EAAEpH,GAAG,EAAE/E,CAAC,CAAC,kKACxC1G,OAAO,CAAC6H,QAAAA,AAAY,EAACzG,SAAS,CAACE,IAAI,CAAC,CAAC,sKACrCpB,IAAI,CAACkH,aAAe,0KACpBlH,IAAI,CAAC4C,KAAAA,AAAO,EAAC,CAAC,CAACJ,SAAS,EAAE4E,CAAC,CAAC,OAC1BhI,yJAAAA,AAAI,0KACFY,IAAI,CAACyF,QAAAA,AAAU,mJAAC7F,GAAG,CAACkH,EAAAA,AAAG,EAACyE,GAAG,CAAC,CAAC,0KAC7BvL,IAAI,CAAC4C,KAAAA,AAAO,GAAEgG,QAAQ,yJACpBxJ,OAAI,AAAJA,0KACEY,IAAI,CAAC6C,GAAAA,AAAK,wJAAkBzD,OAAAA,AAAI,EAACwJ,QAAQ,EAAEjK,KAAK,CAACmD,yJAAAA,AAAS,qJAACnD,KAAK,CAACkI,IAAAA,AAAO,EAACrE,SAAS,CAAC,CAAC,CAAC,CAAC,mKACtF1C,OAAO,CAACuF,GAAAA,AAAQ,GAACrF,IAAI,CAACkC,4KAAAA,AAAO,EAACkF,CAAC,CAAC,CAAC,CAClC,CACF,CACF,CACF,CACF,CACF,CACF;IACD,OAAO,IAAIzG,QAAQ,CAACqF,UAAU,CAAC;AACjC,CAAC,CAAC;AAEF,cAAA,GACA,MAAM2M,kBAAkB,GAAGA,CACzBC,OAAgB,EAChBpQ,SAAkC,EAClCgE,CAAe,IAEfxG,IAAI,CAAC2B,kLAAAA,AAAa,EAAC;QACjBC,OAAO,GAAG6B,KAAK,IAAI;YACjB,uJAAI9E,KAAK,CAACgE,IAAO,AAAPA,EAAQc,KAAK,CAAC,EAAE;gBACxB,OAAOkP,kBAAkB,CAACC,OAAO,EAAEpQ,SAAS,EAAEgE,CAAC,CAAC;YAClD;YACA,IAAIoM,OAAO,EAAE;gBACX,MAAMzH,KAAK,GAAG0H,UAAU,CAACpP,KAAK,EAAE+C,CAAC,CAAC;gBAClC,IAAI2E,KAAK,KAAK,CAAC,CAAC,EAAE;oBAChB,wKAAOrL,OAAO,CAACuF,GAAAA,AAAQ,0KACrBrF,IAAI,CAAC6C,GAAK,AAALA,EAAMY,KAAK,CAAC,EACjBkP,kBAAkB,CAAO,IAAI,EAAEnQ,SAAS,EAAEgE,CAAC,CAAC,CAC7C;gBACH;gBACA,MAAM,CAACsK,IAAI,EAAEG,KAAK,CAAC,IAAGtS,KAAK,CAAC8D,sJAAAA,AAAO,EAACgB,KAAK,EAAE0H,KAAK,CAAC;gBACjD,wKAAOrL,OAAO,CAACuF,GAAAA,AAAQ,0KACrBrF,IAAI,CAAC6C,GAAAA,AAAK,EAACiO,IAAI,CAAC,yKAChB9Q,IAAI,CAACyF,SAAAA,AAAU,mJAAC7F,GAAG,CAACkE,EAAAA,AAAG,EAACtB,SAAS,EAAEyO,KAAK,CAAC,CAAC,CAC3C;YACH;YACA,MAAM9F,KAAK,GAAG0H,UAAU,CAACpP,KAAK,EAAE+C,CAAC,EAAE,CAAC,CAAC;YACrC,IAAI2E,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB,wKAAOrL,OAAO,CAACuF,GAAAA,AAAQ,0KACrBrF,IAAI,CAAC6C,GAAAA,AAAK,EAACY,KAAK,CAAC,EACjBkP,kBAAkB,CAAO,IAAI,EAAEnQ,SAAS,EAAEgE,CAAC,CAAC,CAC7C;YACH;YACA,MAAM,CAACsK,IAAI,EAAEG,KAAK,CAAC,IAAG7R,4JAAAA,AAAI,EAACqE,KAAK,qJAAE9E,KAAK,CAAC8D,IAAAA,AAAO,EAACoG,IAAI,CAACC,GAAG,CAACqC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;YACpE,WAAOrL,OAAO,CAACuF,gKAAAA,AAAQ,0KAACrF,IAAI,CAAC6C,GAAAA,AAAK,EAACiO,IAAI,CAAC,MAAE9Q,IAAI,CAACyF,4KAAAA,AAAU,mJAAC7F,GAAG,CAACkE,EAAAA,AAAG,EAACtB,SAAS,EAAEyO,KAAK,CAAC,CAAC,CAAC;QACvF,CAAC;QACDlP,SAAS,sKAAE/B,IAAI,CAACgC,OAAS;QACzBC,MAAM,qKAAEjC,IAAI,CAACkC,MAAAA;KACd,CAAC;AAEJ,cAAA,GACA,MAAM2Q,UAAU,GAAGA,CAAIzR,IAAoB,EAAE0D,SAAuB,EAAEgO,IAAI,GAAG,CAAC,KAAY;IACxF,MAAMC,QAAQ,GAAG3R,IAAI,CAAClB,MAAM,CAAC6S,QAAQ,CAAC,EAAE;IACxC,IAAI5H,KAAK,GAAG,CAAC;IACb,IAAI6H,MAAM,GAAG,CAAC,CAAC;IACf,IAAInF,IAA4B;IAChC,MAAOmF,MAAM,GAAG,CAAC,IAAA,CAAKnF,IAAI,GAAGkF,QAAQ,CAAClF,IAAI,EAAE,CAAC,IAAI,CAACA,IAAI,CAAC7I,IAAI,CAAE;QAC3D,MAAM3D,CAAC,GAAGwM,IAAI,CAACoF,KAAK;QACpB,IAAI9H,KAAK,IAAI2H,IAAI,IAAIhO,SAAS,CAACzD,CAAC,CAAC,EAAE;YACjC2R,MAAM,GAAG7H,KAAK;QAChB;QACAA,KAAK,GAAGA,KAAK,GAAG,CAAC;IACnB;IACA,OAAO6H,MAAM;AACf,CAAC;AAGM,MAAM9Q,OAAO,GAAOb,CAAI,IAA4B,IAAIV,QAAQ,yKAACX,IAAI,CAACkC,KAAO,AAAPA,EAAQb,CAAC,CAAC,CAAC;AAGjF,MAAM6R,GAAG,GAAA,WAAA,GAA8B3P,cAAc,CAC1D,CAAC,EACD,CAAC7B,GAAG,EAAEG,KAAK,GAAKH,GAAG,GAAG/C,KAAK,CAAC6E,sJAAAA,AAAM,EAAC3B,KAAK,EAAE,CAAC,EAAE,CAACgJ,CAAC,EAAExJ,CAAC,GAAKwJ,CAAC,GAAGxJ,CAAC,CAAC,CAC9D;AAGM,MAAM8R,UAAU,GAAA,WAAA,OAAGjU,yJAAI,AAAJA,EAWxB,CAAC,EACD,CAACkC,IAAI,EAAEgS,OAAO,EAAE5M,CAAC,KAAI;IACnB,MAAMR,UAAU,yJAAG5G,OAAAA,AAAI,GACrBY,IAAI,CAACyF,+KAAAA,AAAU,EAAC2N,OAAO,CAAC,0KACxBpT,IAAI,CAAC4C,KAAAA,AAAO,GAAEyQ,KAAK,GACjBjU,6JAAAA,AAAI,EACFgC,IAAI,EACJF,SAAS,0KACTlB,IAAI,CAAC4C,KAAAA,AAAO,GAAEoC,IAAI,IAChB5F,4JAAAA,AAAI,0KACFY,IAAI,CAACyF,QAAAA,AAAU,EAAC2N,OAAO,CAAC,mKACxBtT,MAAQwB,AAAG,CAAJ,CAACA,CAAKgS,GAAG,GAAK;oBAACtO,IAAI;oBAAEwB,CAAC,CAAC6M,KAAK,EAAEC,GAAG,CAAC;iBAAC,CAAC,CAC5C,CACF,CACF,CACF,CACF;IACD,OAAO,IAAI3S,QAAQ,CAACqF,UAAU,CAAC;AACjC,CAAC,CACF;AAGM,MAAMyF,IAAI,GAAOxK,QAAoB,IAA4B,IAAIN,QAAQ,yKAACX,IAAI,CAACyL,EAAAA,AAAI,EAACxK,QAAQ,CAAC,CAAC;AAGlG,MAAMsS,IAAI,IAAQnR,CAAS,yJAChChD,OAAI,AAAJA,EACE2M,UAAU,CACRpN,KAAK,CAAC8C,qJAAAA,AAAK,EAAE,IACZI,KAAK,GAAKA,KAAK,CAACa,MAAM,GAAGN,CAAC,EAC3B,CAACV,GAAG,EAAEG,KAAK,yJAAKzC,OAAAA,AAAI,EAACsC,GAAG,oJAAE/C,KAAK,CAACmD,OAAAA,AAAS,EAACD,KAAK,CAAC,CAAC,CAClD,EACDe,OAAO,EAAElB,GAAG,IAAI;QACd,MAAM,CAAC8R,KAAK,EAAE5K,QAAQ,CAAC,yJAAGxJ,OAAAA,AAAI,EAACsC,GAAG,qJAAE/C,KAAK,CAAC8D,IAAAA,AAAO,EAACL,CAAC,CAAC,CAAC;QACrD,OAAO,IAAIzB,QAAQ,uJAACvB,OAAAA,AAAI,0KAACY,IAAI,CAAC6C,GAAAA,AAAK,EAAC+F,QAAQ,CAAC,EAAE9I,OAAO,CAACuF,oKAAAA,AAAQ,0KAACrF,IAAI,CAACwJ,QAAAA,AAAU,EAACgK,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3F,CAAC,CAAC,CACH;AAGI,MAAMtS,SAAS,IACpBE,IAA+B,uJAE/BtC,MAAM,CAAC2U,IAAAA,AAAQ,EAACrS,IAAI,CAAC,GACnBF,SAAS,CAACuE,UAAU,CAACrE,IAA8B,CAAC,CAAC,GACpDA,IAAiC,CAACtB,OAAO;AAGvC,MAAMuJ,MAAM,IACjBuF,MAAwD,GAExD,IAAIjO,QAAQ,kKACVb,OAAO,CAACuJ,CAAAA,AAAM,MAACjK,yJAAAA,AAAI,EAACwP,MAAM,sJAAE9P,MAAM,AAACwC,AAAG,CAAHA,EAAKoS,IAAI,GAAKxS,SAAS,CAACwS,IAAI,CAAC,CAAC,CAAC,CAAC,CACpE;AAGI,MAAMxE,YAAY,GACvBN,MAAsD,IAEtD,IAAIjO,QAAQ,kKACVb,OAAO,CAACoP,OAAAA,AAAY,EAACN,MAAM,CAACxP,IAAI,qJAC9BN,MAAOwC,AAAG,AAAJ,CAACA,EAAKoS,IAAI,GAAKxS,SAAS,CAACwS,IAAI,CAAC,CAAC,CACtC,CAAC,CACH;AAGI,MAAMzB,gBAAgB,IAC3BzL,CAAyE,GAEzE,IAAI7F,QAAQ,kKACVb,OAAO,CAACmS,WAAgB,AAAhBA,GAAkBd,KAAK,GAC7B3K,CAAC,CAAC2K,KAAK,CAAC,CAAC/R,IAAI,qJACXN,MAAM,AAACwC,AAAG,CAAHA,CAAKoS,IAAI,IAAKxS,SAAS,CAACwS,IAAI,CAAC,CAAC,CACtC,CACF,CACF;AAGI,MAAMC,YAAY,IACvBvS,IAA+B,yJAE/BhC,OAAAA,AAAI,EAACgC,IAAI,EAAE+R,UAAU,gJAACvU,KAAK,CAACgV,cAAiB,EAAE,CAACP,KAAK,EAAEC,GAAG,yJAAKzU,QAAQ,CAACgV,AAAM,EAACP,GAAG,GAAGD,KAAK,CAAC,CAAC,CAAC;AAGxF,MAAMtN,GAAG,GAAA,WAAA,GAAG7G,6JAAAA,AAAI,GAepBsR,IAAI,GAAK1P,MAAM,CAAC0P,IAAI,CAAC,CAAC,CAAC,CAAC,EACzB,CACEpP,IAA+B,EAC/BgP,IAAoC,EACpCxK,OAEC,GACwDkO,OAAO,CAAC1S,IAAI,EAAEgP,IAAI,EAAE,CAAChJ,CAAC,EAAE2M,EAAE,GAAK;YAAC3M,CAAC;YAAE2M,EAAE;SAAC,EAAEnO,OAAO,CAAC,CAC5G;AAGM,MAAMgM,OAAO,GAAA,WAAA,yJAAG1S,OAAAA,AAAI,GAexBsR,IAAI,GAAK1P,MAAM,CAAC0P,IAAI,CAAC,CAAC,CAAC,CAAC,EACzB,CACEpP,IAA+B,EAC/BgP,IAAoC,EACpCxK,OAEC,GACkDkO,OAAO,CAAC1S,IAAI,EAAEgP,IAAI,EAAE,CAAChJ,CAAC,EAAE9G,CAAC,GAAK8G,CAAC,EAAExB,OAAO,CAAC,CAC/F;AAGM,MAAMP,QAAQ,GAAA,WAAA,yJAAGnG,OAAAA,AAAI,GAezBsR,IAAI,GAAK1P,MAAM,CAAC0P,IAAI,CAAC,CAAC,CAAC,CAAC,EACzB,CACEpP,IAA+B,EAC/BgP,IAAoC,EACpCxK,OAEC,GACmDkO,OAAO,CAAC1S,IAAI,EAAEgP,IAAI,EAAE,CAAC9P,CAAC,EAAEyT,EAAE,GAAKA,EAAE,EAAEnO,OAAO,CAAC,CAClG;AAGM,MAAMkO,OAAO,GAAA,WAAA,yJAAG5U,OAAAA,AAAI,GAiBxBsR,IAAI,GAAK1P,MAAM,CAAC0P,IAAI,CAAC,CAAC,CAAC,CAAC,EACzB,CACEpP,IAA+B,EAC/BgP,IAAoC,EACpC5J,CAAuB,EACvBZ,OAEC,GAEDA,OAAO,EAAEoO,UAAU,GACjBvD,QAAQ,CAACrP,IAAI,EAAE;QACbsP,KAAK,EAAEN,IAAI;QACXO,UAAU,MAAE3R,IAAI,CAACqN,iJAAAA,AAAK,EAAC;YACrBtK,SAAS,GAAGsI,KAAK,qLAAKtK,OAAkB,AAAJ8Q,MAAK/R,AAAN,CAAC+R,KAAW,CAAC7O,qJAAAA,AAAS,EAACqI,KAAK,CAAC,CAAC;YACjE1D,SAAS,GAAGsN,KAAK,qLACflU,QAAcgS,AAAK,GACjB/S,EADW,CAAC+S,CACR,CAAC1F,oJAAK,AAALA,EAAM;oBACTtK,SAAS,kJAAEjD,MAAM,CAACkD,KAAS;oBAC3B2E,SAAS,GAAGuN,MAAM,OAAKpV,MAAM,CAACoD,mJAAAA,AAAO,EAACsE,CAAC,CAACyN,KAAK,EAAEC,MAAM,CAAC;iBACvD,CAAC;SAEP,CAAC;QACFnD,WAAW,oJAAE/R,IAAI,CAACqN,GAAAA,AAAK,EAAC;YACtBtK,SAAS,GAAGsI,KAAK,qLAAKtK,OAAc8Q,AAAI,MAAL,CAACA,+IAAK/R,MAAM,CAACkD,KAAAA,AAAS,EAACqI,KAAK,CAAC,CAAC;YACjE1D,SAAS,GAAGuN,MAAM,qLAChBnU,QAAcgS,AAAK,KAAN,CAACA,8IACZ/S,IAAI,CAACqN,GAAAA,AAAK,EAAC;oBACTtK,SAAS,kJAAEjD,MAAM,CAACkD,KAAS;oBAC3B2E,SAAS,GAAGsN,KAAK,uJAAKnV,MAAM,CAACoD,GAAO,AAAPA,EAAQsE,CAAC,CAACyN,KAAK,EAAEC,MAAM,CAAC;iBACtD,CAAC;SAEP;KACF,CAAC,GACFtR,OAAO,CAACxB,IAAI,GAAGgG,CAAC,GAAK9F,GAAG,CAAC8O,IAAI,GAAG2D,EAAE,GAAKvN,CAAC,CAACY,CAAC,EAAE2M,EAAE,CAAC,CAAC,CAAC,CACtD;AAKM,MAAMI,aAAa,GACxB/S,IAAsG,IACjD,IAAIT,QAAQ,CAACS,IAAI,CAAC;AAKlE,MAAM2L,KAAK,GAAA,WAAA,GAA+BxJ,cAAc,CAC7D,CAAC,EACD,CAAC7B,GAAG,EAAEG,KAAK,GAAKH,GAAG,GAAGG,KAAK,CAACa,MAAM,CACnC;AAGM,MAAM0R,QAAQ,GAAA,WAAA,GAA+BpT,OAAO,CAAC,MAAK;IAC/D,MAAMqT,OAAO,GAAkB,EAAE;IACjC,OAAOjV,6JAAAA,AAAI,EACTmE,cAAc,CAAgB,KAAK,CAAC,EAAE,CAACjD,CAAC,EAAEgU,KAAK,sJAC7C3V,KAAK,CAAC2C,AAAG,AAAHA,EAAIgT,KAAK,GAAG5G,IAAI,IAAI;YACxB2G,OAAO,CAACpF,IAAI,CAACsF,MAAM,CAAC7G,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,EACLpM,GAAG,CAAC,IAAM+S,OAAO,CAACG,IAAI,CAAC,EAAE,CAAC,CAAC,CAC5B;AACH,CAAC,CAAC;AAGK,MAAMC,KAAK,GAAA,WAAA,yJAA0CrV,OAAAA,AAAI,EAAA,WAAA,GAC9DuU,YAAY,CAACnL,KAAK,CAAC,EAAA,WAAA,GACnBlH,GAAG,EAAEmD,KAAK,GAAKA,KAAK,CAAC,CAAC,CAAC,CAAC,CACzB", "ignoreList": [0], "debugId": null}}]}