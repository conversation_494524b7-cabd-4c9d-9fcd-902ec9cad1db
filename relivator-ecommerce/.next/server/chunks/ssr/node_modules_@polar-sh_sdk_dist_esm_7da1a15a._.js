module.exports = {

"[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "ERR": (()=>ERR),
    "OK": (()=>OK),
    "unwrap": (()=>unwrap),
    "unwrapAsync": (()=>unwrapAsync)
});
function OK(value) {
    return {
        ok: true,
        value
    };
}
function ERR(error) {
    return {
        ok: false,
        error
    };
}
function unwrap(r) {
    if (!r.ok) {
        throw r.error;
    }
    return r.value;
}
async function unwrapAsync(pr) {
    const r = await pr;
    if (!r.ok) {
        throw r.error;
    }
    return r.value;
} //# sourceMappingURL=fp.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/types/async.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "APIPromise": (()=>APIPromise)
});
var __classPrivateFieldSet = this && this.__classPrivateFieldSet || function(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
var __classPrivateFieldGet = this && this.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _APIPromise_promise, _APIPromise_unwrapped, _a;
class APIPromise {
    constructor(p){
        _APIPromise_promise.set(this, void 0);
        _APIPromise_unwrapped.set(this, void 0);
        this[_a] = "APIPromise";
        __classPrivateFieldSet(this, _APIPromise_promise, p instanceof Promise ? p : Promise.resolve(p), "f");
        __classPrivateFieldSet(this, _APIPromise_unwrapped, p instanceof Promise ? __classPrivateFieldGet(this, _APIPromise_promise, "f").then(([value])=>value) : Promise.resolve(p[0]), "f");
    }
    then(onfulfilled, onrejected) {
        return __classPrivateFieldGet(this, _APIPromise_promise, "f").then(onfulfilled ? ([value])=>onfulfilled(value) : void 0, onrejected);
    }
    catch(onrejected) {
        return __classPrivateFieldGet(this, _APIPromise_unwrapped, "f").catch(onrejected);
    }
    finally(onfinally) {
        return __classPrivateFieldGet(this, _APIPromise_unwrapped, "f").finally(onfinally);
    }
    $inspect() {
        return __classPrivateFieldGet(this, _APIPromise_promise, "f");
    }
}
_APIPromise_promise = new WeakMap(), _APIPromise_unwrapped = new WeakMap(), _a = Symbol.toStringTag; //# sourceMappingURL=async.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "URL_OVERRIDE": (()=>URL_OVERRIDE),
    "createPageIterator": (()=>createPageIterator),
    "haltIterator": (()=>haltIterator),
    "unwrapResultIterator": (()=>unwrapResultIterator)
});
function createPageIterator(page, halt) {
    return {
        [Symbol.asyncIterator]: async function* paginator() {
            yield page;
            if (halt(page)) {
                return;
            }
            let p = page;
            for(p = await p.next(); p != null; p = await p.next()){
                yield p;
                if (halt(p)) {
                    return;
                }
            }
        }
    };
}
function haltIterator(v) {
    return {
        ...v,
        next: ()=>null,
        [Symbol.asyncIterator]: async function* paginator() {
            yield v;
        }
    };
}
async function unwrapResultIterator(iteratorPromise) {
    const resultIter = await iteratorPromise;
    if (!resultIter.ok) {
        throw resultIter.error;
    }
    return {
        ...resultIter.value,
        next: unwrapPaginator(resultIter.next),
        "~next": resultIter["~next"],
        [Symbol.asyncIterator]: async function* paginator() {
            for await (const page of resultIter){
                if (!page.ok) {
                    throw page.error;
                }
                yield page.value;
            }
        }
    };
}
function unwrapPaginator(paginator) {
    return ()=>{
        const nextResult = paginator();
        if (nextResult == null) {
            return null;
        }
        return nextResult.then((res)=>{
            if (!res.ok) {
                throw res.error;
            }
            const out = {
                ...res.value,
                next: unwrapPaginator(res.next)
            };
            return out;
        });
    };
}
const URL_OVERRIDE = Symbol("URL_OVERRIDE"); //# sourceMappingURL=operations.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/types/rfcdate.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "RFCDate": (()=>RFCDate)
});
const dateRE = /^\d{4}-\d{2}-\d{2}$/;
class RFCDate {
    /**
     * Creates a new RFCDate instance using today's date.
     */ static today() {
        return new RFCDate(new Date());
    }
    /**
     * Creates a new RFCDate instance using the provided input.
     * If a string is used then in must be in the format YYYY-MM-DD.
     *
     * @param date A Date object or a date string in YYYY-MM-DD format
     * @example
     * new RFCDate("2022-01-01")
     * @example
     * new RFCDate(new Date())
     */ constructor(date){
        if (typeof date === "string" && !dateRE.test(date)) {
            throw new RangeError("RFCDate: date strings must be in the format YYYY-MM-DD: " + date);
        }
        const value = new Date(date);
        if (isNaN(+value)) {
            throw new RangeError("RFCDate: invalid date provided: " + date);
        }
        this.serialized = value.toISOString().slice(0, "YYYY-MM-DD".length);
        if (!dateRE.test(this.serialized)) {
            throw new TypeError(`RFCDate: failed to build valid date with given value: ${date} serialized to ${this.serialized}`);
        }
    }
    toJSON() {
        return this.toString();
    }
    toString() {
        return this.serialized;
    }
} //# sourceMappingURL=rfcdate.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/schemas.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "collectExtraKeys": (()=>collectExtraKeys),
    "parse": (()=>parse),
    "safeParse": (()=>safeParse)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$ZodError$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/ZodError.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$sdkvalidationerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/sdkvalidationerror.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-rsc] (ecmascript)");
;
;
;
function parse(rawValue, fn, errorMessage) {
    try {
        return fn(rawValue);
    } catch (err) {
        if (err instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$ZodError$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ZodError"]) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$sdkvalidationerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKValidationError"](errorMessage, err, rawValue);
        }
        throw err;
    }
}
function safeParse(rawValue, fn, errorMessage) {
    try {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OK"])(fn(rawValue));
    } catch (err) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERR"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$sdkvalidationerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKValidationError"](errorMessage, err, rawValue));
    }
}
function collectExtraKeys(obj, extrasKey, optional) {
    return obj.transform((val)=>{
        const extras = {};
        const { shape } = obj;
        for (const [key] of Object.entries(val)){
            if (key in shape) {
                continue;
            }
            const v = val[key];
            if (typeof v === "undefined") {
                continue;
            }
            extras[key] = v;
            delete val[key];
        }
        if (optional && Object.keys(extras).length === 0) {
            return val;
        }
        return {
            ...val,
            [extrasKey]: extras
        };
    });
} //# sourceMappingURL=schemas.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/primitives.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "abortSignalAny": (()=>abortSignalAny),
    "allRequired": (()=>allRequired),
    "combineSignals": (()=>combineSignals),
    "compactMap": (()=>compactMap),
    "invariant": (()=>invariant),
    "remap": (()=>remap)
});
class InvariantError extends Error {
    constructor(message){
        super(message);
        this.name = "InvariantError";
    }
}
function invariant(condition, message) {
    if (!condition) {
        throw new InvariantError(message);
    }
}
function remap(inp, mappings) {
    let out = {};
    if (!Object.keys(mappings).length) {
        out = inp;
        return out;
    }
    for (const [k, v] of Object.entries(inp)){
        const j = mappings[k];
        if (j === null) {
            continue;
        }
        out[j ?? k] = v;
    }
    return out;
}
function combineSignals(...signals) {
    const filtered = [];
    for (const signal of signals){
        if (signal) {
            filtered.push(signal);
        }
    }
    switch(filtered.length){
        case 0:
        case 1:
            return filtered[0] || null;
        default:
            if ("any" in AbortSignal && typeof AbortSignal.any === "function") {
                return AbortSignal.any(filtered);
            }
            return abortSignalAny(filtered);
    }
}
function abortSignalAny(signals) {
    const controller = new AbortController();
    const result = controller.signal;
    if (!signals.length) {
        return controller.signal;
    }
    if (signals.length === 1) {
        return signals[0] || controller.signal;
    }
    for (const signal of signals){
        if (signal.aborted) {
            return signal;
        }
    }
    function abort() {
        controller.abort(this.reason);
        clean();
    }
    const signalRefs = [];
    function clean() {
        for (const signalRef of signalRefs){
            const signal = signalRef.deref();
            if (signal) {
                signal.removeEventListener("abort", abort);
            }
        }
    }
    for (const signal of signals){
        signalRefs.push(new WeakRef(signal));
        signal.addEventListener("abort", abort);
    }
    return result;
}
function compactMap(values) {
    const out = {};
    for (const [k, v] of Object.entries(values)){
        if (typeof v !== "undefined") {
            out[k] = v;
        }
    }
    return out;
}
function allRequired(v) {
    if (Object.values(v).every((x)=>x == null)) {
        return void 0;
    }
    return v;
} //# sourceMappingURL=primitives.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/url.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "pathToFunc": (()=>pathToFunc)
});
const hasOwn = Object.prototype.hasOwnProperty;
function pathToFunc(pathPattern, options) {
    const paramRE = /\{([a-zA-Z0-9_]+?)\}/g;
    return function buildURLPath(params = {}) {
        return pathPattern.replace(paramRE, function(_, placeholder) {
            if (!hasOwn.call(params, placeholder)) {
                throw new Error(`Parameter '${placeholder}' is required`);
            }
            const value = params[placeholder];
            if (typeof value !== "string" && typeof value !== "number") {
                throw new Error(`Parameter '${placeholder}' must be a string or number`);
            }
            return options?.charEncoding === "percent" ? encodeURIComponent(`${value}`) : `${value}`;
        });
    };
} //# sourceMappingURL=url.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/config.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "SDK_METADATA": (()=>SDK_METADATA),
    "ServerList": (()=>ServerList),
    "ServerProduction": (()=>ServerProduction),
    "ServerSandbox": (()=>ServerSandbox),
    "serverURLFromOptions": (()=>serverURLFromOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$url$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/url.js [app-rsc] (ecmascript)");
;
const ServerProduction = "production";
const ServerSandbox = "sandbox";
const ServerList = {
    [ServerProduction]: "https://api.polar.sh",
    [ServerSandbox]: "https://sandbox-api.polar.sh"
};
function serverURLFromOptions(options) {
    let serverURL = options.serverURL;
    const params = {};
    if (!serverURL) {
        const server = options.server ?? ServerProduction;
        serverURL = ServerList[server] || "";
    }
    const u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$url$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pathToFunc"])(serverURL)(params);
    return new URL(u);
}
const SDK_METADATA = {
    language: "typescript",
    openapiDocVersion: "0.1.0",
    sdkVersion: "0.32.16",
    genVersion: "2.605.6",
    userAgent: "speakeasy-sdk/typescript 0.32.16 2.605.6 0.1.0 @polar-sh/sdk"
}; //# sourceMappingURL=config.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/files.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ /**
 * Consumes a stream and returns a concatenated array buffer. Useful in
 * situations where we need to read the whole file because it forms part of a
 * larger payload containing other fields, and we can't modify the underlying
 * request structure.
 */ __turbopack_context__.s({
    "readableStreamToArrayBuffer": (()=>readableStreamToArrayBuffer)
});
async function readableStreamToArrayBuffer(readable) {
    const reader = readable.getReader();
    const chunks = [];
    let totalLength = 0;
    let done = false;
    while(!done){
        const { value, done: doneReading } = await reader.read();
        if (doneReading) {
            done = true;
        } else {
            chunks.push(value);
            totalLength += value.length;
        }
    }
    const concatenatedChunks = new Uint8Array(totalLength);
    let offset = 0;
    for (const chunk of chunks){
        concatenatedChunks.set(chunk, offset);
        offset += chunk.length;
    }
    return concatenatedChunks.buffer;
} //# sourceMappingURL=files.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/base64.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "bytesFromBase64": (()=>bytesFromBase64),
    "bytesToBase64": (()=>bytesToBase64),
    "stringFromBase64": (()=>stringFromBase64),
    "stringFromBytes": (()=>stringFromBytes),
    "stringToBase64": (()=>stringToBase64),
    "stringToBytes": (()=>stringToBytes),
    "zodInbound": (()=>zodInbound),
    "zodOutbound": (()=>zodOutbound)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
function bytesToBase64(u8arr) {
    return btoa(String.fromCodePoint(...u8arr));
}
function bytesFromBase64(encoded) {
    return Uint8Array.from(atob(encoded), (c)=>c.charCodeAt(0));
}
function stringToBytes(str) {
    return new TextEncoder().encode(str);
}
function stringFromBytes(u8arr) {
    return new TextDecoder().decode(u8arr);
}
function stringToBase64(str) {
    return bytesToBase64(stringToBytes(str));
}
function stringFromBase64(b64str) {
    return stringFromBytes(bytesFromBase64(b64str));
}
const zodOutbound = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(Uint8Array).or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().transform(stringToBytes));
const zodInbound = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(Uint8Array).or((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().transform(bytesFromBase64)); //# sourceMappingURL=base64.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/is-plain-object.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ /*
MIT License

Copyright (c) Sindre Sorhus <<EMAIL>> (https://sindresorhus.com)

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
the Software, and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/ // Taken from https://github.com/sindresorhus/is-plain-obj/blob/97f38e8836f86a642cce98fc6ab3058bc36df181/index.js
__turbopack_context__.s({
    "isPlainObject": (()=>isPlainObject)
});
function isPlainObject(value) {
    if (typeof value !== "object" || value === null) {
        return false;
    }
    const prototype = Object.getPrototypeOf(value);
    return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in value) && !(Symbol.iterator in value);
} //# sourceMappingURL=is-plain-object.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/encodings.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "EncodingError": (()=>EncodingError),
    "appendForm": (()=>appendForm),
    "encodeBodyForm": (()=>encodeBodyForm),
    "encodeDeepObject": (()=>encodeDeepObject),
    "encodeDeepObjectObject": (()=>encodeDeepObjectObject),
    "encodeDeepObjectQuery": (()=>encodeDeepObjectQuery),
    "encodeForm": (()=>encodeForm),
    "encodeFormQuery": (()=>encodeFormQuery),
    "encodeJSON": (()=>encodeJSON),
    "encodeJSONQuery": (()=>encodeJSONQuery),
    "encodeLabel": (()=>encodeLabel),
    "encodeMatrix": (()=>encodeMatrix),
    "encodePipeDelimited": (()=>encodePipeDelimited),
    "encodePipeDelimitedQuery": (()=>encodePipeDelimitedQuery),
    "encodeSimple": (()=>encodeSimple),
    "encodeSpaceDelimited": (()=>encodeSpaceDelimited),
    "encodeSpaceDelimitedQuery": (()=>encodeSpaceDelimitedQuery),
    "queryEncoder": (()=>queryEncoder),
    "queryJoin": (()=>queryJoin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$base64$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/base64.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/is-plain-object.js [app-rsc] (ecmascript)");
;
;
class EncodingError extends Error {
    constructor(message){
        super(message);
        this.name = "EncodingError";
    }
}
function encodeMatrix(key, value, options) {
    let out = "";
    const pairs = options?.explode ? explode(key, value) : [
        [
            key,
            value
        ]
    ];
    if (pairs.every(([_, v])=>v == null)) {
        return;
    }
    const encodeString = (v)=>{
        return options?.charEncoding === "percent" ? encodeURIComponent(v) : v;
    };
    const encodeValue = (v)=>encodeString(serializeValue(v));
    pairs.forEach(([pk, pv])=>{
        let tmp = "";
        let encValue = null;
        if (pv == null) {
            return;
        } else if (Array.isArray(pv)) {
            encValue = mapDefined(pv, (v)=>`${encodeValue(v)}`)?.join(",");
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(pv)) {
            const mapped = mapDefinedEntries(Object.entries(pv), ([k, v])=>{
                return `,${encodeString(k)},${encodeValue(v)}`;
            });
            encValue = mapped?.join("").slice(1);
        } else {
            encValue = `${encodeValue(pv)}`;
        }
        if (encValue == null) {
            return;
        }
        const keyPrefix = encodeString(pk);
        tmp = `${keyPrefix}=${encValue}`;
        // trim trailing '=' if value was empty
        if (tmp === `${keyPrefix}=`) {
            tmp = tmp.slice(0, -1);
        }
        // If we end up with the nothing then skip forward
        if (!tmp) {
            return;
        }
        out += `;${tmp}`;
    });
    return out;
}
function encodeLabel(key, value, options) {
    let out = "";
    const pairs = options?.explode ? explode(key, value) : [
        [
            key,
            value
        ]
    ];
    if (pairs.every(([_, v])=>v == null)) {
        return;
    }
    const encodeString = (v)=>{
        return options?.charEncoding === "percent" ? encodeURIComponent(v) : v;
    };
    const encodeValue = (v)=>encodeString(serializeValue(v));
    pairs.forEach(([pk, pv])=>{
        let encValue = "";
        if (pv == null) {
            return;
        } else if (Array.isArray(pv)) {
            encValue = mapDefined(pv, (v)=>`${encodeValue(v)}`)?.join(".");
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(pv)) {
            const mapped = mapDefinedEntries(Object.entries(pv), ([k, v])=>{
                return `.${encodeString(k)}.${encodeValue(v)}`;
            });
            encValue = mapped?.join("").slice(1);
        } else {
            const k = options?.explode && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(value) ? `${encodeString(pk)}=` : "";
            encValue = `${k}${encodeValue(pv)}`;
        }
        out += encValue == null ? "" : `.${encValue}`;
    });
    return out;
}
function formEncoder(sep) {
    return (key, value, options)=>{
        let out = "";
        const pairs = options?.explode ? explode(key, value) : [
            [
                key,
                value
            ]
        ];
        if (pairs.every(([_, v])=>v == null)) {
            return;
        }
        const encodeString = (v)=>{
            return options?.charEncoding === "percent" ? encodeURIComponent(v) : v;
        };
        const encodeValue = (v)=>encodeString(serializeValue(v));
        const encodedSep = encodeString(sep);
        pairs.forEach(([pk, pv])=>{
            let tmp = "";
            let encValue = null;
            if (pv == null) {
                return;
            } else if (Array.isArray(pv)) {
                encValue = mapDefined(pv, (v)=>`${encodeValue(v)}`)?.join(encodedSep);
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(pv)) {
                encValue = mapDefinedEntries(Object.entries(pv), ([k, v])=>{
                    return `${encodeString(k)}${encodedSep}${encodeValue(v)}`;
                })?.join(encodedSep);
            } else {
                encValue = `${encodeValue(pv)}`;
            }
            if (encValue == null) {
                return;
            }
            tmp = `${encodeString(pk)}=${encValue}`;
            // If we end up with the nothing then skip forward
            if (!tmp || tmp === "=") {
                return;
            }
            out += `&${tmp}`;
        });
        return out.slice(1);
    };
}
const encodeForm = formEncoder(",");
const encodeSpaceDelimited = formEncoder(" ");
const encodePipeDelimited = formEncoder("|");
function encodeBodyForm(key, value, options) {
    let out = "";
    const pairs = options?.explode ? explode(key, value) : [
        [
            key,
            value
        ]
    ];
    const encodeString = (v)=>{
        return options?.charEncoding === "percent" ? encodeURIComponent(v) : v;
    };
    const encodeValue = (v)=>encodeString(serializeValue(v));
    pairs.forEach(([pk, pv])=>{
        let tmp = "";
        let encValue = "";
        if (pv == null) {
            return;
        } else if (Array.isArray(pv)) {
            encValue = JSON.stringify(pv, jsonReplacer);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(pv)) {
            encValue = JSON.stringify(pv, jsonReplacer);
        } else {
            encValue = `${encodeValue(pv)}`;
        }
        tmp = `${encodeString(pk)}=${encValue}`;
        // If we end up with the nothing then skip forward
        if (!tmp || tmp === "=") {
            return;
        }
        out += `&${tmp}`;
    });
    return out.slice(1);
}
function encodeDeepObject(key, value, options) {
    if (value == null) {
        return;
    }
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(value)) {
        throw new EncodingError(`Value of parameter '${key}' which uses deepObject encoding must be an object or null`);
    }
    return encodeDeepObjectObject(key, value, options);
}
function encodeDeepObjectObject(key, value, options) {
    if (value == null) {
        return;
    }
    let out = "";
    const encodeString = (v)=>{
        return options?.charEncoding === "percent" ? encodeURIComponent(v) : v;
    };
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(value)) {
        throw new EncodingError(`Expected parameter '${key}' to be an object.`);
    }
    Object.entries(value).forEach(([ck, cv])=>{
        if (cv == null) {
            return;
        }
        const pk = `${key}[${ck}]`;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(cv)) {
            const objOut = encodeDeepObjectObject(pk, cv, options);
            out += objOut == null ? "" : `&${objOut}`;
            return;
        }
        const pairs = Array.isArray(cv) ? cv : [
            cv
        ];
        const encoded = mapDefined(pairs, (v)=>{
            return `${encodeString(pk)}=${encodeString(serializeValue(v))}`;
        })?.join("&");
        out += encoded == null ? "" : `&${encoded}`;
    });
    return out.slice(1);
}
function encodeJSON(key, value, options) {
    if (typeof value === "undefined") {
        return;
    }
    const encodeString = (v)=>{
        return options?.charEncoding === "percent" ? encodeURIComponent(v) : v;
    };
    const encVal = encodeString(JSON.stringify(value, jsonReplacer));
    return options?.explode ? encVal : `${encodeString(key)}=${encVal}`;
}
const encodeSimple = (key, value, options)=>{
    let out = "";
    const pairs = options?.explode ? explode(key, value) : [
        [
            key,
            value
        ]
    ];
    if (pairs.every(([_, v])=>v == null)) {
        return;
    }
    const encodeString = (v)=>{
        return options?.charEncoding === "percent" ? encodeURIComponent(v) : v;
    };
    const encodeValue = (v)=>encodeString(serializeValue(v));
    pairs.forEach(([pk, pv])=>{
        let tmp = "";
        if (pv == null) {
            return;
        } else if (Array.isArray(pv)) {
            tmp = mapDefined(pv, (v)=>`${encodeValue(v)}`)?.join(",");
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(pv)) {
            const mapped = mapDefinedEntries(Object.entries(pv), ([k, v])=>{
                return `,${encodeString(k)},${encodeValue(v)}`;
            });
            tmp = mapped?.join("").slice(1);
        } else {
            const k = options?.explode && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(value) ? `${pk}=` : "";
            tmp = `${k}${encodeValue(pv)}`;
        }
        out += tmp ? `,${tmp}` : "";
    });
    return out.slice(1);
};
function explode(key, value) {
    if (Array.isArray(value)) {
        return value.map((v)=>[
                key,
                v
            ]);
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(value)) {
        const o = value ?? {};
        return Object.entries(o).map(([k, v])=>[
                k,
                v
            ]);
    } else {
        return [
            [
                key,
                value
            ]
        ];
    }
}
function serializeValue(value) {
    if (value == null) {
        return "";
    } else if (value instanceof Date) {
        return value.toISOString();
    } else if (value instanceof Uint8Array) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$base64$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["bytesToBase64"])(value);
    } else if (typeof value === "object") {
        return JSON.stringify(value, jsonReplacer);
    }
    return `${value}`;
}
function jsonReplacer(_, value) {
    if (value instanceof Uint8Array) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$base64$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["bytesToBase64"])(value);
    } else {
        return value;
    }
}
function mapDefined(inp, mapper) {
    const res = inp.reduce((acc, v)=>{
        if (v == null) {
            return acc;
        }
        const m = mapper(v);
        if (m == null) {
            return acc;
        }
        acc.push(m);
        return acc;
    }, []);
    return res.length ? res : null;
}
function mapDefinedEntries(inp, mapper) {
    const acc = [];
    for (const [k, v] of inp){
        if (v == null) {
            continue;
        }
        const m = mapper([
            k,
            v
        ]);
        if (m == null) {
            continue;
        }
        acc.push(m);
    }
    return acc.length ? acc : null;
}
function queryJoin(...args) {
    return args.filter(Boolean).join("&");
}
function queryEncoder(f) {
    const bulkEncode = function(values, options) {
        const opts = {
            ...options,
            explode: options?.explode ?? true,
            charEncoding: options?.charEncoding ?? "percent"
        };
        const encoded = Object.entries(values).map(([key, value])=>{
            return f(key, value, opts);
        });
        return queryJoin(...encoded);
    };
    return bulkEncode;
}
const encodeJSONQuery = queryEncoder(encodeJSON);
const encodeFormQuery = queryEncoder(encodeForm);
const encodeSpaceDelimitedQuery = queryEncoder(encodeSpaceDelimited);
const encodePipeDelimitedQuery = queryEncoder(encodePipeDelimited);
const encodeDeepObjectQuery = queryEncoder(encodeDeepObject);
function appendForm(fd, key, value, fileName) {
    if (value == null) {
        return;
    } else if (value instanceof Blob && fileName) {
        fd.append(key, value, fileName);
    } else if (value instanceof Blob) {
        fd.append(key, value);
    } else {
        fd.append(key, String(value));
    }
} //# sourceMappingURL=encodings.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/dlv.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ /*
MIT License

Copyright (c) 2024 Jason Miller <<EMAIL>> (http://jasonformat.com)

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
the Software, and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/ /**
 * @param obj The object to walk
 * @param key The key path to walk the object with
 * @param def A default value to return if the result is undefined
 *
 * @example
 * dlv(obj, "a.b.c.d")
 * @example
 * dlv(object, ["a", "b", "c", "d"])
 * @example
 * dlv(object, "foo.bar.baz", "Hello, default value!")
 */ __turbopack_context__.s({
    "dlv": (()=>dlv)
});
function dlv(obj, key, def, p, undef) {
    key = Array.isArray(key) ? key : key.split(".");
    for(p = 0; p < key.length; p++){
        const k = key[p];
        obj = k != null && obj ? obj[k] : undef;
    }
    return obj === undef ? def : obj;
} //# sourceMappingURL=dlv.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/env.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "env": (()=>env),
    "envSchema": (()=>envSchema),
    "resetEnv": (()=>resetEnv)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$dlv$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/dlv.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
;
const envSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    POLAR_ACCESS_TOKEN: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])().optional(),
    POLAR_DEBUG: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["coerce"].boolean().optional()
});
let envMemo = undefined;
function env() {
    if (envMemo) {
        return envMemo;
    }
    envMemo = envSchema.parse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$dlv$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dlv"])(globalThis, "process.env") ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$dlv$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dlv"])(globalThis, "Deno.env") ?? {});
    return envMemo;
}
function resetEnv() {
    envMemo = undefined;
} //# sourceMappingURL=env.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/http.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "HTTPClient": (()=>HTTPClient),
    "isAbortError": (()=>isAbortError),
    "isConnectionError": (()=>isConnectionError),
    "isTimeoutError": (()=>isTimeoutError),
    "matchContentType": (()=>matchContentType),
    "matchResponse": (()=>matchResponse),
    "matchStatusCode": (()=>matchStatusCode)
});
const DEFAULT_FETCHER = (input, init)=>{
    // If input is a Request and init is undefined, Bun will discard the method,
    // headers, body and other options that were set on the request object.
    // Node.js and browers would ignore an undefined init value. This check is
    // therefore needed for interop with Bun.
    if (init == null) {
        return fetch(input);
    } else {
        return fetch(input, init);
    }
};
class HTTPClient {
    constructor(options = {}){
        this.options = options;
        this.requestHooks = [];
        this.requestErrorHooks = [];
        this.responseHooks = [];
        this.fetcher = options.fetcher || DEFAULT_FETCHER;
    }
    async request(request) {
        let req = request;
        for (const hook of this.requestHooks){
            const nextRequest = await hook(req);
            if (nextRequest) {
                req = nextRequest;
            }
        }
        try {
            const res = await this.fetcher(req);
            for (const hook of this.responseHooks){
                await hook(res, req);
            }
            return res;
        } catch (err) {
            for (const hook of this.requestErrorHooks){
                await hook(err, req);
            }
            throw err;
        }
    }
    addHook(...args) {
        if (args[0] === "beforeRequest") {
            this.requestHooks.push(args[1]);
        } else if (args[0] === "requestError") {
            this.requestErrorHooks.push(args[1]);
        } else if (args[0] === "response") {
            this.responseHooks.push(args[1]);
        } else {
            throw new Error(`Invalid hook type: ${args[0]}`);
        }
        return this;
    }
    removeHook(...args) {
        let target;
        if (args[0] === "beforeRequest") {
            target = this.requestHooks;
        } else if (args[0] === "requestError") {
            target = this.requestErrorHooks;
        } else if (args[0] === "response") {
            target = this.responseHooks;
        } else {
            throw new Error(`Invalid hook type: ${args[0]}`);
        }
        const index = target.findIndex((v)=>v === args[1]);
        if (index >= 0) {
            target.splice(index, 1);
        }
        return this;
    }
    clone() {
        const child = new HTTPClient(this.options);
        child.requestHooks = this.requestHooks.slice();
        child.requestErrorHooks = this.requestErrorHooks.slice();
        child.responseHooks = this.responseHooks.slice();
        return child;
    }
}
// A semicolon surrounded by optional whitespace characters is used to separate
// segments in a media type string.
const mediaParamSeparator = /\s*;\s*/g;
function matchContentType(response, pattern) {
    // `*` is a special case which means anything is acceptable.
    if (pattern === "*") {
        return true;
    }
    let contentType = response.headers.get("content-type")?.trim() || "application/octet-stream";
    contentType = contentType.toLowerCase();
    const wantParts = pattern.toLowerCase().trim().split(mediaParamSeparator);
    const [wantType = "", ...wantParams] = wantParts;
    if (wantType.split("/").length !== 2) {
        return false;
    }
    const gotParts = contentType.split(mediaParamSeparator);
    const [gotType = "", ...gotParams] = gotParts;
    const [type = "", subtype = ""] = gotType.split("/");
    if (!type || !subtype) {
        return false;
    }
    if (wantType !== "*/*" && gotType !== wantType && `${type}/*` !== wantType && `*/${subtype}` !== wantType) {
        return false;
    }
    if (gotParams.length < wantParams.length) {
        return false;
    }
    const params = new Set(gotParams);
    for (const wantParam of wantParams){
        if (!params.has(wantParam)) {
            return false;
        }
    }
    return true;
}
const codeRangeRE = new RegExp("^[0-9]xx$", "i");
function matchStatusCode(response, codes) {
    const actual = `${response.status}`;
    const expectedCodes = Array.isArray(codes) ? codes : [
        codes
    ];
    if (!expectedCodes.length) {
        return false;
    }
    return expectedCodes.some((ec)=>{
        const code = `${ec}`;
        if (code === "default") {
            return true;
        }
        if (!codeRangeRE.test(`${code}`)) {
            return code === actual;
        }
        const expectFamily = code.charAt(0);
        if (!expectFamily) {
            throw new Error("Invalid status code range");
        }
        const actualFamily = actual.charAt(0);
        if (!actualFamily) {
            throw new Error(`Invalid response status code: ${actual}`);
        }
        return actualFamily === expectFamily;
    });
}
function matchResponse(response, code, contentTypePattern) {
    return matchStatusCode(response, code) && matchContentType(response, contentTypePattern);
}
function isConnectionError(err) {
    if (typeof err !== "object" || err == null) {
        return false;
    }
    // Covers fetch in Deno as well
    const isBrowserErr = err instanceof TypeError && err.message.toLowerCase().startsWith("failed to fetch");
    const isNodeErr = err instanceof TypeError && err.message.toLowerCase().startsWith("fetch failed");
    const isBunErr = "name" in err && err.name === "ConnectionError";
    const isGenericErr = "code" in err && typeof err.code === "string" && err.code.toLowerCase() === "econnreset";
    return isBrowserErr || isNodeErr || isGenericErr || isBunErr;
}
function isTimeoutError(err) {
    if (typeof err !== "object" || err == null) {
        return false;
    }
    // Fetch in browser, Node.js, Bun, Deno
    const isNative = "name" in err && err.name === "TimeoutError";
    const isLegacyNative = "code" in err && err.code === 23;
    // Node.js HTTP client and Axios
    const isGenericErr = "code" in err && typeof err.code === "string" && err.code.toLowerCase() === "econnaborted";
    return isNative || isLegacyNative || isGenericErr;
}
function isAbortError(err) {
    if (typeof err !== "object" || err == null) {
        return false;
    }
    // Fetch in browser, Node.js, Bun, Deno
    const isNative = "name" in err && err.name === "AbortError";
    const isLegacyNative = "code" in err && err.code === 20;
    // Node.js HTTP client and Axios
    const isGenericErr = "code" in err && typeof err.code === "string" && err.code.toLowerCase() === "econnaborted";
    return isNative || isLegacyNative || isGenericErr;
} //# sourceMappingURL=http.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/retries.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "PermanentError": (()=>PermanentError),
    "TemporaryError": (()=>TemporaryError),
    "retry": (()=>retry)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/http.js [app-rsc] (ecmascript)");
;
const defaultBackoff = {
    initialInterval: 500,
    maxInterval: 60000,
    exponent: 1.5,
    maxElapsedTime: 3600000
};
class PermanentError extends Error {
    constructor(message, options){
        let msg = message;
        if (options?.cause) {
            msg += `: ${options.cause}`;
        }
        super(msg, options);
        this.name = "PermanentError";
        // In older runtimes, the cause field would not have been assigned through
        // the super() call.
        if (typeof this.cause === "undefined") {
            this.cause = options?.cause;
        }
        Object.setPrototypeOf(this, PermanentError.prototype);
    }
}
class TemporaryError extends Error {
    constructor(message, response){
        super(message);
        this.response = response;
        this.name = "TemporaryError";
        Object.setPrototypeOf(this, TemporaryError.prototype);
    }
}
async function retry(fetchFn, options) {
    switch(options.config.strategy){
        case "backoff":
            return retryBackoff(wrapFetcher(fetchFn, {
                statusCodes: options.statusCodes,
                retryConnectionErrors: !!options.config.retryConnectionErrors
            }), options.config.backoff ?? defaultBackoff);
        default:
            return await fetchFn();
    }
}
function wrapFetcher(fn, options) {
    return async ()=>{
        try {
            const res = await fn();
            if (isRetryableResponse(res, options.statusCodes)) {
                throw new TemporaryError("Response failed with retryable status code", res);
            }
            return res;
        } catch (err) {
            if (err instanceof TemporaryError) {
                throw err;
            }
            if (options.retryConnectionErrors && ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTimeoutError"])(err) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isConnectionError"])(err))) {
                throw err;
            }
            throw new PermanentError("Permanent error", {
                cause: err
            });
        }
    };
}
const codeRangeRE = new RegExp("^[0-9]xx$", "i");
function isRetryableResponse(res, statusCodes) {
    const actual = `${res.status}`;
    return statusCodes.some((code)=>{
        if (!codeRangeRE.test(code)) {
            return code === actual;
        }
        const expectFamily = code.charAt(0);
        if (!expectFamily) {
            throw new Error("Invalid status code range");
        }
        const actualFamily = actual.charAt(0);
        if (!actualFamily) {
            throw new Error(`Invalid response status code: ${actual}`);
        }
        return actualFamily === expectFamily;
    });
}
async function retryBackoff(fn, strategy) {
    const { maxElapsedTime, initialInterval, exponent, maxInterval } = strategy;
    const start = Date.now();
    let x = 0;
    while(true){
        try {
            const res = await fn();
            return res;
        } catch (err) {
            if (err instanceof PermanentError) {
                throw err.cause;
            }
            const elapsed = Date.now() - start;
            if (elapsed > maxElapsedTime) {
                if (err instanceof TemporaryError) {
                    return err.response;
                }
                throw err;
            }
            let retryInterval = 0;
            if (err instanceof TemporaryError) {
                retryInterval = retryIntervalFromResponse(err.response);
            }
            if (retryInterval <= 0) {
                retryInterval = initialInterval * Math.pow(x, exponent) + Math.random() * 1000;
            }
            const d = Math.min(retryInterval, maxInterval);
            await delay(d);
            x++;
        }
    }
}
function retryIntervalFromResponse(res) {
    const retryVal = res.headers.get("retry-after") || "";
    if (!retryVal) {
        return 0;
    }
    const parsedNumber = Number(retryVal);
    if (Number.isInteger(parsedNumber)) {
        return parsedNumber * 1000;
    }
    const parsedDate = Date.parse(retryVal);
    if (Number.isInteger(parsedDate)) {
        const deltaMS = parsedDate - Date.now();
        return deltaMS > 0 ? Math.ceil(deltaMS) : 0;
    }
    return 0;
}
async function delay(delay) {
    return new Promise((resolve)=>setTimeout(resolve, delay));
} //# sourceMappingURL=retries.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "ClientSDK": (()=>ClientSDK)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$hooks$2f$hooks$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/hooks/hooks.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$httpclienterrors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/httpclienterrors.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$base64$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/base64.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$config$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/config.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$encodings$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/encodings.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$env$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/env.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/http.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$retries$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/retries.js [app-rsc] (ecmascript)");
var __classPrivateFieldSet = this && this.__classPrivateFieldSet || function(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
var __classPrivateFieldGet = this && this.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _ClientSDK_httpClient, _ClientSDK_hooks, _ClientSDK_logger;
;
;
;
;
;
;
;
;
;
const gt = typeof globalThis === "undefined" ? null : globalThis;
const webWorkerLike = typeof gt === "object" && gt != null && "importScripts" in gt && typeof gt["importScripts"] === "function";
const isBrowserLike = webWorkerLike || typeof navigator !== "undefined" && "serviceWorker" in navigator || "undefined" === "object" && typeof window.document !== "undefined";
class ClientSDK {
    constructor(options = {}){
        _ClientSDK_httpClient.set(this, void 0);
        _ClientSDK_hooks.set(this, void 0);
        _ClientSDK_logger.set(this, void 0);
        const opt = options;
        if (typeof opt === "object" && opt != null && "hooks" in opt && opt.hooks instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$hooks$2f$hooks$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKHooks"]) {
            __classPrivateFieldSet(this, _ClientSDK_hooks, opt.hooks, "f");
        } else {
            __classPrivateFieldSet(this, _ClientSDK_hooks, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$hooks$2f$hooks$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKHooks"](), "f");
        }
        this._options = {
            ...options,
            hooks: __classPrivateFieldGet(this, _ClientSDK_hooks, "f")
        };
        const url = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$config$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["serverURLFromOptions"])(options);
        if (url) {
            url.pathname = url.pathname.replace(/\/+$/, "") + "/";
        }
        const { baseURL, client } = __classPrivateFieldGet(this, _ClientSDK_hooks, "f").sdkInit({
            baseURL: url,
            client: options.httpClient || new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["HTTPClient"]()
        });
        this._baseURL = baseURL;
        __classPrivateFieldSet(this, _ClientSDK_httpClient, client, "f");
        __classPrivateFieldSet(this, _ClientSDK_logger, options.debugLogger, "f");
        if (!__classPrivateFieldGet(this, _ClientSDK_logger, "f") && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$env$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["env"])().POLAR_DEBUG) {
            __classPrivateFieldSet(this, _ClientSDK_logger, console, "f");
        }
    }
    _createRequest(context, conf, options) {
        const { method, path, query, headers: opHeaders, security } = conf;
        const base = conf.baseURL ?? this._baseURL;
        if (!base) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERR"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$httpclienterrors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["InvalidRequestError"]("No base URL provided for operation"));
        }
        const reqURL = new URL(base);
        const inputURL = new URL(path, reqURL);
        if (path) {
            reqURL.pathname += reqURL.pathname.endsWith("/") ? "" : "/";
            reqURL.pathname += inputURL.pathname.replace(/^\/+/, "");
        }
        let finalQuery = query || "";
        const secQuery = [];
        for (const [k, v] of Object.entries(security?.queryParams || {})){
            const q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$encodings$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["encodeForm"])(k, v, {
                charEncoding: "percent"
            });
            if (typeof q !== "undefined") {
                secQuery.push(q);
            }
        }
        if (secQuery.length) {
            finalQuery += `&${secQuery.join("&")}`;
        }
        if (finalQuery) {
            const q = finalQuery.startsWith("&") ? finalQuery.slice(1) : finalQuery;
            reqURL.search = `?${q}`;
        }
        const headers = new Headers(opHeaders);
        const username = security?.basic.username;
        const password = security?.basic.password;
        if (username != null || password != null) {
            const encoded = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$base64$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["stringToBase64"])([
                username || "",
                password || ""
            ].join(":"));
            headers.set("Authorization", `Basic ${encoded}`);
        }
        const securityHeaders = new Headers(security?.headers || {});
        for (const [k, v] of securityHeaders){
            headers.set(k, v);
        }
        let cookie = headers.get("cookie") || "";
        for (const [k, v] of Object.entries(security?.cookies || {})){
            cookie += `; ${k}=${v}`;
        }
        cookie = cookie.startsWith("; ") ? cookie.slice(2) : cookie;
        headers.set("cookie", cookie);
        const userHeaders = new Headers(options?.headers ?? options?.fetchOptions?.headers);
        for (const [k, v] of userHeaders){
            headers.set(k, v);
        }
        // Only set user agent header in non-browser-like environments since CORS
        // policy disallows setting it in browsers e.g. Chrome throws an error.
        if (!isBrowserLike) {
            headers.set(conf.uaHeader ?? "user-agent", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$config$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDK_METADATA"].userAgent);
        }
        const fetchOptions = {
            ...options?.fetchOptions,
            ...options
        };
        if (!fetchOptions?.signal && conf.timeoutMs && conf.timeoutMs > 0) {
            const timeoutSignal = AbortSignal.timeout(conf.timeoutMs);
            fetchOptions.signal = timeoutSignal;
        }
        if (conf.body instanceof ReadableStream) {
            Object.assign(fetchOptions, {
                duplex: "half"
            });
        }
        let input;
        try {
            input = __classPrivateFieldGet(this, _ClientSDK_hooks, "f").beforeCreateRequest(context, {
                url: reqURL,
                options: {
                    ...fetchOptions,
                    body: conf.body ?? null,
                    headers,
                    method
                }
            });
        } catch (err) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERR"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$httpclienterrors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["UnexpectedClientError"]("Create request hook failed to execute", {
                cause: err
            }));
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OK"])(new Request(input.url, input.options));
    }
    async _do(request, options) {
        const { context, errorCodes } = options;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$retries$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"])(async ()=>{
            const req = await __classPrivateFieldGet(this, _ClientSDK_hooks, "f").beforeRequest(context, request.clone());
            await logRequest(__classPrivateFieldGet(this, _ClientSDK_logger, "f"), req).catch((e)=>__classPrivateFieldGet(this, _ClientSDK_logger, "f")?.log("Failed to log request:", e));
            let response = await __classPrivateFieldGet(this, _ClientSDK_httpClient, "f").request(req);
            try {
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchStatusCode"])(response, errorCodes)) {
                    const result = await __classPrivateFieldGet(this, _ClientSDK_hooks, "f").afterError(context, response, null);
                    if (result.error) {
                        throw result.error;
                    }
                    response = result.response || response;
                } else {
                    response = await __classPrivateFieldGet(this, _ClientSDK_hooks, "f").afterSuccess(context, response);
                }
            } finally{
                await logResponse(__classPrivateFieldGet(this, _ClientSDK_logger, "f"), response, req).catch((e)=>__classPrivateFieldGet(this, _ClientSDK_logger, "f")?.log("Failed to log response:", e));
            }
            return response;
        }, {
            config: options.retryConfig,
            statusCodes: options.retryCodes
        }).then((r)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OK"])(r), (err)=>{
            switch(true){
                case (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isAbortError"])(err):
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERR"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$httpclienterrors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RequestAbortedError"]("Request aborted by client", {
                        cause: err
                    }));
                case (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTimeoutError"])(err):
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERR"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$httpclienterrors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RequestTimeoutError"]("Request timed out", {
                        cause: err
                    }));
                case (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isConnectionError"])(err):
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERR"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$httpclienterrors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ConnectionError"]("Unable to make request", {
                        cause: err
                    }));
                default:
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ERR"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$httpclienterrors$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["UnexpectedClientError"]("Unexpected HTTP client error", {
                        cause: err
                    }));
            }
        });
    }
}
_ClientSDK_httpClient = new WeakMap(), _ClientSDK_hooks = new WeakMap(), _ClientSDK_logger = new WeakMap();
const jsonLikeContentTypeRE = /(application|text)\/.*?\+*json.*/;
const jsonlLikeContentTypeRE = /(application|text)\/(.*?\+*\bjsonl\b.*|.*?\+*\bx-ndjson\b.*)/;
async function logRequest(logger, req) {
    if (!logger) {
        return;
    }
    const contentType = req.headers.get("content-type");
    const ct = contentType?.split(";")[0] || "";
    logger.group(`> Request: ${req.method} ${req.url}`);
    logger.group("Headers:");
    for (const [k, v] of req.headers.entries()){
        logger.log(`${k}: ${v}`);
    }
    logger.groupEnd();
    logger.group("Body:");
    switch(true){
        case jsonLikeContentTypeRE.test(ct):
            logger.log(await req.clone().json());
            break;
        case ct.startsWith("text/"):
            logger.log(await req.clone().text());
            break;
        case ct === "multipart/form-data":
            {
                const body = await req.clone().formData();
                for (const [k, v] of body){
                    const vlabel = v instanceof Blob ? "<Blob>" : v;
                    logger.log(`${k}: ${vlabel}`);
                }
                break;
            }
        default:
            logger.log(`<${contentType}>`);
            break;
    }
    logger.groupEnd();
    logger.groupEnd();
}
async function logResponse(logger, res, req) {
    if (!logger) {
        return;
    }
    const contentType = res.headers.get("content-type");
    const ct = contentType?.split(";")[0] || "";
    logger.group(`< Response: ${req.method} ${req.url}`);
    logger.log("Status Code:", res.status, res.statusText);
    logger.group("Headers:");
    for (const [k, v] of res.headers.entries()){
        logger.log(`${k}: ${v}`);
    }
    logger.groupEnd();
    logger.group("Body:");
    switch(true){
        case (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchContentType"])(res, "application/json") || jsonLikeContentTypeRE.test(ct) && !jsonlLikeContentTypeRE.test(ct):
            logger.log(await res.clone().json());
            break;
        case (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchContentType"])(res, "application/jsonl") || jsonlLikeContentTypeRE.test(ct):
            logger.log(await res.clone().text());
            break;
        case (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchContentType"])(res, "text/event-stream"):
            logger.log(`<${contentType}>`);
            break;
        case (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchContentType"])(res, "text/*"):
            logger.log(await res.clone().text());
            break;
        case (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchContentType"])(res, "multipart/form-data"):
            {
                const body = await res.clone().formData();
                for (const [k, v] of body){
                    const vlabel = v instanceof Blob ? "<Blob>" : v;
                    logger.log(`${k}: ${vlabel}`);
                }
                break;
            }
        default:
            logger.log(`<${contentType}>`);
            break;
    }
    logger.groupEnd();
    logger.groupEnd();
} //# sourceMappingURL=sdks.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/matchers.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "bytes": (()=>bytes),
    "bytesErr": (()=>bytesErr),
    "discardResponseBody": (()=>discardResponseBody),
    "fail": (()=>fail),
    "json": (()=>json),
    "jsonErr": (()=>jsonErr),
    "jsonl": (()=>jsonl),
    "jsonlErr": (()=>jsonlErr),
    "match": (()=>match),
    "nil": (()=>nil),
    "nilErr": (()=>nilErr),
    "sse": (()=>sse),
    "sseErr": (()=>sseErr),
    "stream": (()=>stream),
    "streamErr": (()=>streamErr),
    "text": (()=>text),
    "textErr": (()=>textErr),
    "unpackHeaders": (()=>unpackHeaders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$sdkerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/sdkerror.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/http.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/is-plain-object.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$schemas$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/schemas.js [app-rsc] (ecmascript)");
;
;
;
;
const DEFAULT_CONTENT_TYPES = {
    jsonl: "application/jsonl",
    json: "application/json",
    text: "text/plain",
    bytes: "application/octet-stream",
    stream: "application/octet-stream",
    sse: "text/event-stream",
    nil: "*",
    fail: "*"
};
function jsonErr(codes, schema, options) {
    return {
        ...options,
        err: true,
        enc: "json",
        codes,
        schema
    };
}
function json(codes, schema, options) {
    return {
        ...options,
        enc: "json",
        codes,
        schema
    };
}
function jsonl(codes, schema, options) {
    return {
        ...options,
        enc: "jsonl",
        codes,
        schema
    };
}
function jsonlErr(codes, schema, options) {
    return {
        ...options,
        err: true,
        enc: "jsonl",
        codes,
        schema
    };
}
function textErr(codes, schema, options) {
    return {
        ...options,
        err: true,
        enc: "text",
        codes,
        schema
    };
}
function text(codes, schema, options) {
    return {
        ...options,
        enc: "text",
        codes,
        schema
    };
}
function bytesErr(codes, schema, options) {
    return {
        ...options,
        err: true,
        enc: "bytes",
        codes,
        schema
    };
}
function bytes(codes, schema, options) {
    return {
        ...options,
        enc: "bytes",
        codes,
        schema
    };
}
function streamErr(codes, schema, options) {
    return {
        ...options,
        err: true,
        enc: "stream",
        codes,
        schema
    };
}
function stream(codes, schema, options) {
    return {
        ...options,
        enc: "stream",
        codes,
        schema
    };
}
function sseErr(codes, schema, options) {
    return {
        ...options,
        err: true,
        enc: "sse",
        codes,
        schema
    };
}
function sse(codes, schema, options) {
    return {
        ...options,
        enc: "sse",
        codes,
        schema
    };
}
function nilErr(codes, schema, options) {
    return {
        ...options,
        err: true,
        enc: "nil",
        codes,
        schema
    };
}
function nil(codes, schema, options) {
    return {
        ...options,
        enc: "nil",
        codes,
        schema
    };
}
function fail(codes) {
    return {
        enc: "fail",
        codes
    };
}
function match(...matchers) {
    return async function matchFunc(response, options) {
        let raw;
        let matcher;
        for (const match of matchers){
            const { codes } = match;
            const ctpattern = "ctype" in match ? match.ctype : DEFAULT_CONTENT_TYPES[match.enc];
            if (ctpattern && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchResponse"])(response, codes, ctpattern)) {
                matcher = match;
                break;
            } else if (!ctpattern && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$http$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchStatusCode"])(response, codes)) {
                matcher = match;
                break;
            }
        }
        if (!matcher) {
            const responseBody = await response.text();
            return [
                {
                    ok: false,
                    error: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$sdkerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKError"]("Unexpected API response status or content-type", response, responseBody)
                },
                responseBody
            ];
        }
        const encoding = matcher.enc;
        switch(encoding){
            case "json":
                raw = await response.json();
                break;
            case "jsonl":
                raw = response.body;
                break;
            case "bytes":
                raw = new Uint8Array(await response.arrayBuffer());
                break;
            case "stream":
                raw = response.body;
                break;
            case "text":
                raw = await response.text();
                break;
            case "sse":
                raw = response.body;
                break;
            case "nil":
                raw = await discardResponseBody(response);
                break;
            case "fail":
                raw = await response.text();
                break;
            default:
                encoding;
                throw new Error(`Unsupported response type: ${encoding}`);
        }
        if (matcher.enc === "fail") {
            return [
                {
                    ok: false,
                    error: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$sdkerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKError"]("API error occurred", response, typeof raw === "string" ? raw : "")
                },
                raw
            ];
        }
        const resultKey = matcher.key || options?.resultKey;
        let data;
        if ("err" in matcher) {
            data = {
                ...options?.extraFields,
                ...matcher.hdrs ? {
                    Headers: unpackHeaders(response.headers)
                } : null,
                ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(raw) ? raw : null
            };
        } else if (resultKey) {
            data = {
                ...options?.extraFields,
                ...matcher.hdrs ? {
                    Headers: unpackHeaders(response.headers)
                } : null,
                [resultKey]: raw
            };
        } else if (matcher.hdrs) {
            data = {
                ...options?.extraFields,
                ...matcher.hdrs ? {
                    Headers: unpackHeaders(response.headers)
                } : null,
                ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$is$2d$plain$2d$object$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPlainObject"])(raw) ? raw : null
            };
        } else {
            data = raw;
        }
        if ("err" in matcher) {
            const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$schemas$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["safeParse"])(data, (v)=>matcher.schema.parse(v), "Response validation failed");
            return [
                result.ok ? {
                    ok: false,
                    error: result.value
                } : result,
                raw
            ];
        } else {
            return [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$schemas$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["safeParse"])(data, (v)=>matcher.schema.parse(v), "Response validation failed"),
                raw
            ];
        }
    };
}
const headerValRE = /, */;
function unpackHeaders(headers) {
    const out = {};
    for (const [k, v] of headers.entries()){
        out[k] = v.split(headerValRE);
    }
    return out;
}
async function discardResponseBody(res) {
    const reader = res.body?.getReader();
    if (reader == null) {
        return;
    }
    try {
        let done = false;
        while(!done){
            const res = await reader.read();
            done = res.done;
        }
    } finally{
        reader.releaseLock();
    }
} //# sourceMappingURL=matchers.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/lib/security.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "SecurityError": (()=>SecurityError),
    "SecurityErrorCode": (()=>SecurityErrorCode),
    "extractSecurity": (()=>extractSecurity),
    "resolveGlobalSecurity": (()=>resolveGlobalSecurity),
    "resolveSecurity": (()=>resolveSecurity)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$env$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/env.js [app-rsc] (ecmascript)");
;
var SecurityErrorCode;
(function(SecurityErrorCode) {
    SecurityErrorCode["Incomplete"] = "incomplete";
    SecurityErrorCode["UnrecognisedSecurityType"] = "unrecognized_security_type";
})(SecurityErrorCode || (SecurityErrorCode = {}));
class SecurityError extends Error {
    constructor(code, message){
        super(message);
        this.code = code;
        this.name = "SecurityError";
    }
    static incomplete() {
        return new SecurityError(SecurityErrorCode.Incomplete, "Security requirements not met in order to perform the operation");
    }
    static unrecognizedType(type) {
        return new SecurityError(SecurityErrorCode.UnrecognisedSecurityType, `Unrecognised security type: ${type}`);
    }
}
function resolveSecurity(...options) {
    const state = {
        basic: {},
        headers: {},
        queryParams: {},
        cookies: {},
        oauth2: {
            type: "none"
        }
    };
    const option = options.find((opts)=>{
        return opts.every((o)=>{
            if (o.value == null) {
                return false;
            } else if (o.type === "http:basic") {
                return o.value.username != null || o.value.password != null;
            } else if (o.type === "http:custom") {
                return null;
            } else if (o.type === "oauth2:password") {
                return typeof o.value === "string" && !!o.value;
            } else if (o.type === "oauth2:client_credentials") {
                if (typeof o.value == "string") {
                    return !!o.value;
                }
                return o.value.clientID != null || o.value.clientSecret != null;
            } else if (typeof o.value === "string") {
                return !!o.value;
            } else {
                throw new Error(`Unrecognized security type: ${o.type} (value type: ${typeof o.value})`);
            }
        });
    });
    if (option == null) {
        return null;
    }
    option.forEach((spec)=>{
        if (spec.value == null) {
            return;
        }
        const { type } = spec;
        switch(type){
            case "apiKey:header":
                state.headers[spec.fieldName] = spec.value;
                break;
            case "apiKey:query":
                state.queryParams[spec.fieldName] = spec.value;
                break;
            case "apiKey:cookie":
                state.cookies[spec.fieldName] = spec.value;
                break;
            case "http:basic":
                applyBasic(state, spec);
                break;
            case "http:custom":
                break;
            case "http:bearer":
                applyBearer(state, spec);
                break;
            case "oauth2":
                applyBearer(state, spec);
                break;
            case "oauth2:password":
                applyBearer(state, spec);
                break;
            case "oauth2:client_credentials":
                break;
            case "openIdConnect":
                applyBearer(state, spec);
                break;
            default:
                spec;
                throw SecurityError.unrecognizedType(type);
        }
    });
    return state;
}
function applyBasic(state, spec) {
    if (spec.value == null) {
        return;
    }
    state.basic = spec.value;
}
function applyBearer(state, spec) {
    if (typeof spec.value !== "string" || !spec.value) {
        return;
    }
    let value = spec.value;
    if (value.slice(0, 7).toLowerCase() !== "bearer ") {
        value = `Bearer ${value}`;
    }
    if (spec.fieldName !== undefined) {
        state.headers[spec.fieldName] = value;
    }
}
function resolveGlobalSecurity(security) {
    return resolveSecurity([
        {
            fieldName: "Authorization",
            type: "http:bearer",
            value: security?.accessToken ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$env$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["env"])().POLAR_ACCESS_TOKEN
        }
    ]);
}
async function extractSecurity(sec) {
    if (sec == null) {
        return;
    }
    return typeof sec === "function" ? sec() : sec;
} //# sourceMappingURL=security.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/webhooks.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WebhookVerificationError": (()=>WebhookVerificationError),
    "validateEvent": (()=>validateEvent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$standardwebhooks$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/standardwebhooks/dist/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitcreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookbenefitcreatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitgrantcreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookbenefitgrantcreatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitgrantrevokedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookbenefitgrantrevokedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitgrantupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookbenefitgrantupdatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitgrantcycledpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookbenefitgrantcycledpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookbenefitupdatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcheckoutcreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookcheckoutcreatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcheckoutupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookcheckoutupdatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookordercreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookordercreatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookorderrefundedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookorderrefundedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookorderupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookorderupdatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookorderpaidpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookorderpaidpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookorganizationupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookorganizationupdatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookproductcreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookproductcreatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookproductupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookproductupdatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookrefundcreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookrefundcreatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookrefundupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookrefundupdatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptionactivepayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhooksubscriptionactivepayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptioncanceledpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhooksubscriptioncanceledpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptioncreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhooksubscriptioncreatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptionrevokedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhooksubscriptionrevokedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptionuncanceledpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhooksubscriptionuncanceledpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptionupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhooksubscriptionupdatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcustomercreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookcustomercreatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcustomerupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookcustomerupdatedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcustomerdeletedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookcustomerdeletedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcustomerstatechangedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/webhookcustomerstatechangedpayload.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$sdkvalidationerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/sdkvalidationerror.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
class WebhookVerificationError extends Error {
    constructor(message){
        super(message);
        this.message = message;
    }
}
const parseEvent = (parsed)=>{
    try {
        switch(parsed.type){
            case "customer.created":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcustomercreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookCustomerCreatedPayload$inboundSchema"].parse(parsed);
            case "customer.updated":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcustomerupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookCustomerUpdatedPayload$inboundSchema"].parse(parsed);
            case "customer.deleted":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcustomerdeletedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookCustomerDeletedPayload$inboundSchema"].parse(parsed);
            case "customer.state_changed":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcustomerstatechangedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookCustomerStateChangedPayload$inboundSchema"].parse(parsed);
            case "benefit.created":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitcreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookBenefitCreatedPayload$inboundSchema"].parse(parsed);
            case "benefit_grant.created":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitgrantcreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookBenefitGrantCreatedPayload$inboundSchema"].parse(parsed);
            case "benefit_grant.cycled":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitgrantcycledpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookBenefitGrantCycledPayload$inboundSchema"].parse(parsed);
            case "benefit_grant.revoked":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitgrantrevokedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookBenefitGrantRevokedPayload$inboundSchema"].parse(parsed);
            case "benefit_grant.updated":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitgrantupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookBenefitGrantUpdatedPayload$inboundSchema"].parse(parsed);
            case "benefit.updated":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookbenefitupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookBenefitUpdatedPayload$inboundSchema"].parse(parsed);
            case "checkout.created":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcheckoutcreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookCheckoutCreatedPayload$inboundSchema"].parse(parsed);
            case "checkout.updated":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookcheckoutupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookCheckoutUpdatedPayload$inboundSchema"].parse(parsed);
            case "order.created":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookordercreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookOrderCreatedPayload$inboundSchema"].parse(parsed);
            case "order.paid":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookorderpaidpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookOrderPaidPayload$inboundSchema"].parse(parsed);
            case "order.updated":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookorderupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookOrderUpdatedPayload$inboundSchema"].parse(parsed);
            case "order.refunded":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookorderrefundedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookOrderRefundedPayload$inboundSchema"].parse(parsed);
            case "organization.updated":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookorganizationupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookOrganizationUpdatedPayload$inboundSchema"].parse(parsed);
            case "product.created":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookproductcreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookProductCreatedPayload$inboundSchema"].parse(parsed);
            case "product.updated":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookproductupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookProductUpdatedPayload$inboundSchema"].parse(parsed);
            case "refund.created":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookrefundcreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookRefundCreatedPayload$inboundSchema"].parse(parsed);
            case "refund.updated":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhookrefundupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookRefundUpdatedPayload$inboundSchema"].parse(parsed);
            case "subscription.active":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptionactivepayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookSubscriptionActivePayload$inboundSchema"].parse(parsed);
            case "subscription.canceled":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptioncanceledpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookSubscriptionCanceledPayload$inboundSchema"].parse(parsed);
            case "subscription.created":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptioncreatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookSubscriptionCreatedPayload$inboundSchema"].parse(parsed);
            case "subscription.revoked":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptionrevokedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookSubscriptionRevokedPayload$inboundSchema"].parse(parsed);
            case "subscription.uncanceled":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptionuncanceledpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookSubscriptionUncanceledPayload$inboundSchema"].parse(parsed);
            case "subscription.updated":
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$webhooksubscriptionupdatedpayload$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookSubscriptionUpdatedPayload$inboundSchema"].parse(parsed);
            default:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$sdkvalidationerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKValidationError"](`Unknown event type: ${parsed.type}`, parsed.type, parsed);
        }
    } catch (error) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$sdkvalidationerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SDKValidationError"]("Failed to parse event", error, parsed);
    }
};
const validateEvent = (body, headers, secret)=>{
    const base64Secret = Buffer.from(secret, "utf-8").toString("base64");
    const webhook = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$standardwebhooks$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Webhook"](base64Secret);
    try {
        const parsed = webhook.verify(body, headers);
        return parseEvent(parsed);
    } catch (error) {
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$standardwebhooks$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["WebhookVerificationError"]) {
            throw new WebhookVerificationError(error.message);
        }
        throw error;
    }
};
;
 //# sourceMappingURL=webhooks.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/hooks/registration.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * This file is only ever generated once on the first generation and then is free to be modified.
 * Any hooks you wish to add should be registered in the initHooks function. Feel free to define them
 * in this file or in separate files in the hooks folder.
 */ // @ts-expect-error remove this line when you add your first hook and hooks is used
__turbopack_context__.s({
    "initHooks": (()=>initHooks)
});
function initHooks(hooks) {
// Add hooks by calling hooks.register{ClientInit/BeforeCreateRequest/BeforeRequest/AfterSuccess/AfterError}Hook
// with an instance of a hook that implements that specific Hook interface
// Hooks are registered per SDK instance, and are valid for the lifetime of the SDK instance
} //# sourceMappingURL=registration.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/hooks/hooks.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "SDKHooks": (()=>SDKHooks)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$hooks$2f$registration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/hooks/registration.js [app-rsc] (ecmascript)");
;
class SDKHooks {
    constructor(){
        this.sdkInitHooks = [];
        this.beforeCreateRequestHooks = [];
        this.beforeRequestHooks = [];
        this.afterSuccessHooks = [];
        this.afterErrorHooks = [];
        const presetHooks = [];
        for (const hook of presetHooks){
            if ("sdkInit" in hook) {
                this.registerSDKInitHook(hook);
            }
            if ("beforeCreateRequest" in hook) {
                this.registerBeforeCreateRequestHook(hook);
            }
            if ("beforeRequest" in hook) {
                this.registerBeforeRequestHook(hook);
            }
            if ("afterSuccess" in hook) {
                this.registerAfterSuccessHook(hook);
            }
            if ("afterError" in hook) {
                this.registerAfterErrorHook(hook);
            }
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$hooks$2f$registration$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["initHooks"])(this);
    }
    registerSDKInitHook(hook) {
        this.sdkInitHooks.push(hook);
    }
    registerBeforeCreateRequestHook(hook) {
        this.beforeCreateRequestHooks.push(hook);
    }
    registerBeforeRequestHook(hook) {
        this.beforeRequestHooks.push(hook);
    }
    registerAfterSuccessHook(hook) {
        this.afterSuccessHooks.push(hook);
    }
    registerAfterErrorHook(hook) {
        this.afterErrorHooks.push(hook);
    }
    sdkInit(opts) {
        return this.sdkInitHooks.reduce((opts, hook)=>hook.sdkInit(opts), opts);
    }
    beforeCreateRequest(hookCtx, input) {
        let inp = input;
        for (const hook of this.beforeCreateRequestHooks){
            inp = hook.beforeCreateRequest(hookCtx, inp);
        }
        return inp;
    }
    async beforeRequest(hookCtx, request) {
        let req = request;
        for (const hook of this.beforeRequestHooks){
            req = await hook.beforeRequest(hookCtx, req);
        }
        return req;
    }
    async afterSuccess(hookCtx, response) {
        let res = response;
        for (const hook of this.afterSuccessHooks){
            res = await hook.afterSuccess(hookCtx, res);
        }
        return res;
    }
    async afterError(hookCtx, response, error) {
        let res = response;
        let err = error;
        for (const hook of this.afterErrorHooks){
            const result = await hook.afterError(hookCtx, res, err);
            res = result.response;
            err = result.error;
        }
        return {
            response: res,
            error: err
        };
    }
} //# sourceMappingURL=hooks.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$config$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/config.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$files$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/files.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$sdk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/sdk.js [app-rsc] (ecmascript)"); //# sourceMappingURL=index.js.map
;
;
;
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$config$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/config.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$files$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/files.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$sdk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/sdk.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),

};

//# sourceMappingURL=node_modules_%40polar-sh_sdk_dist_esm_7da1a15a._.js.map