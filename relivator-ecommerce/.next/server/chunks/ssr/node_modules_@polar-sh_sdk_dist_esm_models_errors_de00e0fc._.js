module.exports = {

"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/sdkvalidationerror.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "SDKValidationError": (()=>SDKValidationError),
    "formatZodError": (()=>formatZodError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$ZodError$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/ZodError.js [app-rsc] (ecmascript)");
;
class SDKValidationError extends Error {
    constructor(message, cause, rawValue){
        super(`${message}: ${cause}`);
        this.name = "SDKValidationError";
        this.cause = cause;
        this.rawValue = rawValue;
        this.rawMessage = message;
    }
    /**
     * Return a pretty-formatted error message if the underlying validation error
     * is a ZodError or some other recognized error type, otherwise return the
     * default error message.
     */ pretty() {
        if (this.cause instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$ZodError$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ZodError"]) {
            return `${this.rawMessage}\n${formatZodError(this.cause)}`;
        } else {
            return this.toString();
        }
    }
}
function formatZodError(err, level = 0) {
    let pre = "  ".repeat(level);
    pre = level > 0 ? `│${pre}` : pre;
    pre += " ".repeat(level);
    let message = "";
    const append = (str)=>message += `\n${pre}${str}`;
    const len = err.issues.length;
    const headline = len === 1 ? `${len} issue found` : `${len} issues found`;
    if (len) {
        append(`┌ ${headline}:`);
    }
    for (const issue of err.issues){
        let path = issue.path.join(".");
        path = path ? `<root>.${path}` : "<root>";
        append(`│ • [${path}]: ${issue.message} (${issue.code})`);
        switch(issue.code){
            case "invalid_literal":
            case "invalid_type":
                {
                    append(`│     Want: ${issue.expected}`);
                    append(`│      Got: ${issue.received}`);
                    break;
                }
            case "unrecognized_keys":
                {
                    append(`│     Keys: ${issue.keys.join(", ")}`);
                    break;
                }
            case "invalid_enum_value":
                {
                    append(`│     Allowed: ${issue.options.join(", ")}`);
                    append(`│         Got: ${issue.received}`);
                    break;
                }
            case "invalid_union_discriminator":
                {
                    append(`│     Allowed: ${issue.options.join(", ")}`);
                    break;
                }
            case "invalid_union":
                {
                    const len = issue.unionErrors.length;
                    append(`│   ✖︎ Attemped to deserialize into one of ${len} union members:`);
                    issue.unionErrors.forEach((err, i)=>{
                        append(`│   ✖︎ Member ${i + 1} of ${len}`);
                        append(`${formatZodError(err, level + 1)}`);
                    });
                }
        }
    }
    if (err.issues.length) {
        append(`└─*`);
    }
    return message.slice(1);
} //# sourceMappingURL=sdkvalidationerror.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/httpclienterrors.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ /**
 * Base class for all HTTP errors.
 */ __turbopack_context__.s({
    "ConnectionError": (()=>ConnectionError),
    "HTTPClientError": (()=>HTTPClientError),
    "InvalidRequestError": (()=>InvalidRequestError),
    "RequestAbortedError": (()=>RequestAbortedError),
    "RequestTimeoutError": (()=>RequestTimeoutError),
    "UnexpectedClientError": (()=>UnexpectedClientError)
});
class HTTPClientError extends Error {
    constructor(message, opts){
        let msg = message;
        if (opts?.cause) {
            msg += `: ${opts.cause}`;
        }
        super(msg, opts);
        this.name = "HTTPClientError";
        // In older runtimes, the cause field would not have been assigned through
        // the super() call.
        if (typeof this.cause === "undefined") {
            this.cause = opts?.cause;
        }
    }
}
class UnexpectedClientError extends HTTPClientError {
    constructor(){
        super(...arguments);
        this.name = "UnexpectedClientError";
    }
}
class InvalidRequestError extends HTTPClientError {
    constructor(){
        super(...arguments);
        this.name = "InvalidRequestError";
    }
}
class RequestAbortedError extends HTTPClientError {
    constructor(){
        super(...arguments);
        this.name = "RequestAbortedError";
    }
}
class RequestTimeoutError extends HTTPClientError {
    constructor(){
        super(...arguments);
        this.name = "RequestTimeoutError";
    }
}
class ConnectionError extends HTTPClientError {
    constructor(){
        super(...arguments);
        this.name = "ConnectionError";
    }
} //# sourceMappingURL=httpclienterrors.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/sdkerror.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "SDKError": (()=>SDKError)
});
class SDKError extends Error {
    constructor(message, rawResponse, body = ""){
        const statusCode = rawResponse.status;
        const contentType = rawResponse.headers.get("content-type") || "";
        const bodyString = body.length > 0 ? `\n${body}` : "";
        super(`${message}: Status ${statusCode} Content-Type ${contentType} Body ${bodyString}`);
        this.rawResponse = rawResponse;
        this.body = body;
        this.statusCode = statusCode;
        this.contentType = contentType;
        this.name = "SDKError";
    }
} //# sourceMappingURL=sdkerror.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/httpvalidationerror.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "HTTPValidationError": (()=>HTTPValidationError),
    "HTTPValidationError$": (()=>HTTPValidationError$),
    "HTTPValidationError$inboundSchema": (()=>HTTPValidationError$inboundSchema),
    "HTTPValidationError$outboundSchema": (()=>HTTPValidationError$outboundSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$validationerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/components/validationerror.js [app-rsc] (ecmascript)");
;
;
class HTTPValidationError extends Error {
    constructor(err){
        const message = "message" in err && typeof err.message === "string" ? err.message : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        if (err.detail != null) this.detail = err.detail;
        this.name = "HTTPValidationError";
    }
}
const HTTPValidationError$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["array"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$validationerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ValidationError$inboundSchema"]).optional()
}).transform((v)=>{
    return new HTTPValidationError(v);
});
const HTTPValidationError$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(HTTPValidationError).transform((v)=>v.data$).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["array"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$components$2f$validationerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ValidationError$outboundSchema"]).optional()
}));
var HTTPValidationError$;
(function(HTTPValidationError$) {
    /** @deprecated use `HTTPValidationError$inboundSchema` instead. */ HTTPValidationError$.inboundSchema = HTTPValidationError$inboundSchema;
    /** @deprecated use `HTTPValidationError$outboundSchema` instead. */ HTTPValidationError$.outboundSchema = HTTPValidationError$outboundSchema;
})(HTTPValidationError$ || (HTTPValidationError$ = {})); //# sourceMappingURL=httpvalidationerror.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/notpermitted.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "NotPermitted": (()=>NotPermitted),
    "NotPermitted$": (()=>NotPermitted$),
    "NotPermitted$inboundSchema": (()=>NotPermitted$inboundSchema),
    "NotPermitted$outboundSchema": (()=>NotPermitted$outboundSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
class NotPermitted extends Error {
    constructor(err){
        const message = "message" in err && typeof err.message === "string" ? err.message : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        this.error = err.error;
        this.detail = err.detail;
        this.name = "NotPermitted";
    }
}
const NotPermitted$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("NotPermitted"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}).transform((v)=>{
    return new NotPermitted(v);
});
const NotPermitted$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(NotPermitted).transform((v)=>v.data$).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("NotPermitted").default("NotPermitted"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}));
var NotPermitted$;
(function(NotPermitted$) {
    /** @deprecated use `NotPermitted$inboundSchema` instead. */ NotPermitted$.inboundSchema = NotPermitted$inboundSchema;
    /** @deprecated use `NotPermitted$outboundSchema` instead. */ NotPermitted$.outboundSchema = NotPermitted$outboundSchema;
})(NotPermitted$ || (NotPermitted$ = {})); //# sourceMappingURL=notpermitted.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/resourcenotfound.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "ResourceNotFound": (()=>ResourceNotFound),
    "ResourceNotFound$": (()=>ResourceNotFound$),
    "ResourceNotFound$inboundSchema": (()=>ResourceNotFound$inboundSchema),
    "ResourceNotFound$outboundSchema": (()=>ResourceNotFound$outboundSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
class ResourceNotFound extends Error {
    constructor(err){
        const message = "message" in err && typeof err.message === "string" ? err.message : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        this.error = err.error;
        this.detail = err.detail;
        this.name = "ResourceNotFound";
    }
}
const ResourceNotFound$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("ResourceNotFound"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}).transform((v)=>{
    return new ResourceNotFound(v);
});
const ResourceNotFound$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(ResourceNotFound).transform((v)=>v.data$).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("ResourceNotFound").default("ResourceNotFound"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}));
var ResourceNotFound$;
(function(ResourceNotFound$) {
    /** @deprecated use `ResourceNotFound$inboundSchema` instead. */ ResourceNotFound$.inboundSchema = ResourceNotFound$inboundSchema;
    /** @deprecated use `ResourceNotFound$outboundSchema` instead. */ ResourceNotFound$.outboundSchema = ResourceNotFound$outboundSchema;
})(ResourceNotFound$ || (ResourceNotFound$ = {})); //# sourceMappingURL=resourcenotfound.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/alreadyactivesubscriptionerror.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "AlreadyActiveSubscriptionError": (()=>AlreadyActiveSubscriptionError),
    "AlreadyActiveSubscriptionError$": (()=>AlreadyActiveSubscriptionError$),
    "AlreadyActiveSubscriptionError$inboundSchema": (()=>AlreadyActiveSubscriptionError$inboundSchema),
    "AlreadyActiveSubscriptionError$outboundSchema": (()=>AlreadyActiveSubscriptionError$outboundSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
class AlreadyActiveSubscriptionError extends Error {
    constructor(err){
        const message = "message" in err && typeof err.message === "string" ? err.message : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        this.error = err.error;
        this.detail = err.detail;
        this.name = "AlreadyActiveSubscriptionError";
    }
}
const AlreadyActiveSubscriptionError$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("AlreadyActiveSubscriptionError"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}).transform((v)=>{
    return new AlreadyActiveSubscriptionError(v);
});
const AlreadyActiveSubscriptionError$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(AlreadyActiveSubscriptionError).transform((v)=>v.data$).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("AlreadyActiveSubscriptionError").default("AlreadyActiveSubscriptionError"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}));
var AlreadyActiveSubscriptionError$;
(function(AlreadyActiveSubscriptionError$) {
    /** @deprecated use `AlreadyActiveSubscriptionError$inboundSchema` instead. */ AlreadyActiveSubscriptionError$.inboundSchema = AlreadyActiveSubscriptionError$inboundSchema;
    /** @deprecated use `AlreadyActiveSubscriptionError$outboundSchema` instead. */ AlreadyActiveSubscriptionError$.outboundSchema = AlreadyActiveSubscriptionError$outboundSchema;
})(AlreadyActiveSubscriptionError$ || (AlreadyActiveSubscriptionError$ = {})); //# sourceMappingURL=alreadyactivesubscriptionerror.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/notopencheckout.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "NotOpenCheckout": (()=>NotOpenCheckout),
    "NotOpenCheckout$": (()=>NotOpenCheckout$),
    "NotOpenCheckout$inboundSchema": (()=>NotOpenCheckout$inboundSchema),
    "NotOpenCheckout$outboundSchema": (()=>NotOpenCheckout$outboundSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
class NotOpenCheckout extends Error {
    constructor(err){
        const message = "message" in err && typeof err.message === "string" ? err.message : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        this.error = err.error;
        this.detail = err.detail;
        this.name = "NotOpenCheckout";
    }
}
const NotOpenCheckout$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("NotOpenCheckout"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}).transform((v)=>{
    return new NotOpenCheckout(v);
});
const NotOpenCheckout$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(NotOpenCheckout).transform((v)=>v.data$).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("NotOpenCheckout").default("NotOpenCheckout"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}));
var NotOpenCheckout$;
(function(NotOpenCheckout$) {
    /** @deprecated use `NotOpenCheckout$inboundSchema` instead. */ NotOpenCheckout$.inboundSchema = NotOpenCheckout$inboundSchema;
    /** @deprecated use `NotOpenCheckout$outboundSchema` instead. */ NotOpenCheckout$.outboundSchema = NotOpenCheckout$outboundSchema;
})(NotOpenCheckout$ || (NotOpenCheckout$ = {})); //# sourceMappingURL=notopencheckout.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/checkoutforbiddenerror.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "CheckoutForbiddenError$": (()=>CheckoutForbiddenError$),
    "CheckoutForbiddenError$inboundSchema": (()=>CheckoutForbiddenError$inboundSchema),
    "CheckoutForbiddenError$outboundSchema": (()=>CheckoutForbiddenError$outboundSchema),
    "checkoutForbiddenErrorFromJSON": (()=>checkoutForbiddenErrorFromJSON),
    "checkoutForbiddenErrorToJSON": (()=>checkoutForbiddenErrorToJSON)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$schemas$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/schemas.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$alreadyactivesubscriptionerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/alreadyactivesubscriptionerror.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$notopencheckout$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/notopencheckout.js [app-rsc] (ecmascript)");
;
;
;
;
const CheckoutForbiddenError$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["union"])([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$alreadyactivesubscriptionerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AlreadyActiveSubscriptionError$inboundSchema"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$notopencheckout$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NotOpenCheckout$inboundSchema"]
]);
const CheckoutForbiddenError$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["union"])([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$alreadyactivesubscriptionerror$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["AlreadyActiveSubscriptionError$outboundSchema"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$models$2f$errors$2f$notopencheckout$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NotOpenCheckout$outboundSchema"]
]);
var CheckoutForbiddenError$;
(function(CheckoutForbiddenError$) {
    /** @deprecated use `CheckoutForbiddenError$inboundSchema` instead. */ CheckoutForbiddenError$.inboundSchema = CheckoutForbiddenError$inboundSchema;
    /** @deprecated use `CheckoutForbiddenError$outboundSchema` instead. */ CheckoutForbiddenError$.outboundSchema = CheckoutForbiddenError$outboundSchema;
})(CheckoutForbiddenError$ || (CheckoutForbiddenError$ = {}));
function checkoutForbiddenErrorToJSON(checkoutForbiddenError) {
    return JSON.stringify(CheckoutForbiddenError$outboundSchema.parse(checkoutForbiddenError));
}
function checkoutForbiddenErrorFromJSON(jsonString) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$schemas$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["safeParse"])(jsonString, (x)=>CheckoutForbiddenError$inboundSchema.parse(JSON.parse(x)), `Failed to parse 'CheckoutForbiddenError' from JSON`);
} //# sourceMappingURL=checkoutforbiddenerror.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/expiredcheckouterror.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "ExpiredCheckoutError": (()=>ExpiredCheckoutError),
    "ExpiredCheckoutError$": (()=>ExpiredCheckoutError$),
    "ExpiredCheckoutError$inboundSchema": (()=>ExpiredCheckoutError$inboundSchema),
    "ExpiredCheckoutError$outboundSchema": (()=>ExpiredCheckoutError$outboundSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
class ExpiredCheckoutError extends Error {
    constructor(err){
        const message = "message" in err && typeof err.message === "string" ? err.message : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        this.error = err.error;
        this.detail = err.detail;
        this.name = "ExpiredCheckoutError";
    }
}
const ExpiredCheckoutError$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("ExpiredCheckoutError"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}).transform((v)=>{
    return new ExpiredCheckoutError(v);
});
const ExpiredCheckoutError$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(ExpiredCheckoutError).transform((v)=>v.data$).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("ExpiredCheckoutError").default("ExpiredCheckoutError"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}));
var ExpiredCheckoutError$;
(function(ExpiredCheckoutError$) {
    /** @deprecated use `ExpiredCheckoutError$inboundSchema` instead. */ ExpiredCheckoutError$.inboundSchema = ExpiredCheckoutError$inboundSchema;
    /** @deprecated use `ExpiredCheckoutError$outboundSchema` instead. */ ExpiredCheckoutError$.outboundSchema = ExpiredCheckoutError$outboundSchema;
})(ExpiredCheckoutError$ || (ExpiredCheckoutError$ = {})); //# sourceMappingURL=expiredcheckouterror.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/paymenterror.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "PaymentError": (()=>PaymentError),
    "PaymentError$": (()=>PaymentError$),
    "PaymentError$inboundSchema": (()=>PaymentError$inboundSchema),
    "PaymentError$outboundSchema": (()=>PaymentError$outboundSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
class PaymentError extends Error {
    constructor(err){
        const message = "message" in err && typeof err.message === "string" ? err.message : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        this.error = err.error;
        this.detail = err.detail;
        this.name = "PaymentError";
    }
}
const PaymentError$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("PaymentError"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}).transform((v)=>{
    return new PaymentError(v);
});
const PaymentError$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(PaymentError).transform((v)=>v.data$).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("PaymentError").default("PaymentError"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}));
var PaymentError$;
(function(PaymentError$) {
    /** @deprecated use `PaymentError$inboundSchema` instead. */ PaymentError$.inboundSchema = PaymentError$inboundSchema;
    /** @deprecated use `PaymentError$outboundSchema` instead. */ PaymentError$.outboundSchema = PaymentError$outboundSchema;
})(PaymentError$ || (PaymentError$ = {})); //# sourceMappingURL=paymenterror.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/unauthorized.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Unauthorized": (()=>Unauthorized),
    "Unauthorized$": (()=>Unauthorized$),
    "Unauthorized$inboundSchema": (()=>Unauthorized$inboundSchema),
    "Unauthorized$outboundSchema": (()=>Unauthorized$outboundSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
class Unauthorized extends Error {
    constructor(err){
        const message = "message" in err && typeof err.message === "string" ? err.message : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        this.error = err.error;
        this.detail = err.detail;
        this.name = "Unauthorized";
    }
}
const Unauthorized$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("Unauthorized"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}).transform((v)=>{
    return new Unauthorized(v);
});
const Unauthorized$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(Unauthorized).transform((v)=>v.data$).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("Unauthorized").default("Unauthorized"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}));
var Unauthorized$;
(function(Unauthorized$) {
    /** @deprecated use `Unauthorized$inboundSchema` instead. */ Unauthorized$.inboundSchema = Unauthorized$inboundSchema;
    /** @deprecated use `Unauthorized$outboundSchema` instead. */ Unauthorized$.outboundSchema = Unauthorized$outboundSchema;
})(Unauthorized$ || (Unauthorized$ = {})); //# sourceMappingURL=unauthorized.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/alreadycanceledsubscription.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "AlreadyCanceledSubscription": (()=>AlreadyCanceledSubscription),
    "AlreadyCanceledSubscription$": (()=>AlreadyCanceledSubscription$),
    "AlreadyCanceledSubscription$inboundSchema": (()=>AlreadyCanceledSubscription$inboundSchema),
    "AlreadyCanceledSubscription$outboundSchema": (()=>AlreadyCanceledSubscription$outboundSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
class AlreadyCanceledSubscription extends Error {
    constructor(err){
        const message = "message" in err && typeof err.message === "string" ? err.message : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        this.error = err.error;
        this.detail = err.detail;
        this.name = "AlreadyCanceledSubscription";
    }
}
const AlreadyCanceledSubscription$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("AlreadyCanceledSubscription"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}).transform((v)=>{
    return new AlreadyCanceledSubscription(v);
});
const AlreadyCanceledSubscription$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(AlreadyCanceledSubscription).transform((v)=>v.data$).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("AlreadyCanceledSubscription").default("AlreadyCanceledSubscription"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}));
var AlreadyCanceledSubscription$;
(function(AlreadyCanceledSubscription$) {
    /** @deprecated use `AlreadyCanceledSubscription$inboundSchema` instead. */ AlreadyCanceledSubscription$.inboundSchema = AlreadyCanceledSubscription$inboundSchema;
    /** @deprecated use `AlreadyCanceledSubscription$outboundSchema` instead. */ AlreadyCanceledSubscription$.outboundSchema = AlreadyCanceledSubscription$outboundSchema;
})(AlreadyCanceledSubscription$ || (AlreadyCanceledSubscription$ = {})); //# sourceMappingURL=alreadycanceledsubscription.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/refundamounttoohigh.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "RefundAmountTooHigh": (()=>RefundAmountTooHigh),
    "RefundAmountTooHigh$": (()=>RefundAmountTooHigh$),
    "RefundAmountTooHigh$inboundSchema": (()=>RefundAmountTooHigh$inboundSchema),
    "RefundAmountTooHigh$outboundSchema": (()=>RefundAmountTooHigh$outboundSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
class RefundAmountTooHigh extends Error {
    constructor(err){
        const message = "message" in err && typeof err.message === "string" ? err.message : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        this.error = err.error;
        this.detail = err.detail;
        this.name = "RefundAmountTooHigh";
    }
}
const RefundAmountTooHigh$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("RefundAmountTooHigh"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}).transform((v)=>{
    return new RefundAmountTooHigh(v);
});
const RefundAmountTooHigh$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(RefundAmountTooHigh).transform((v)=>v.data$).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("RefundAmountTooHigh").default("RefundAmountTooHigh"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}));
var RefundAmountTooHigh$;
(function(RefundAmountTooHigh$) {
    /** @deprecated use `RefundAmountTooHigh$inboundSchema` instead. */ RefundAmountTooHigh$.inboundSchema = RefundAmountTooHigh$inboundSchema;
    /** @deprecated use `RefundAmountTooHigh$outboundSchema` instead. */ RefundAmountTooHigh$.outboundSchema = RefundAmountTooHigh$outboundSchema;
})(RefundAmountTooHigh$ || (RefundAmountTooHigh$ = {})); //# sourceMappingURL=refundamounttoohigh.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/models/errors/refundedalready.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "RefundedAlready": (()=>RefundedAlready),
    "RefundedAlready$": (()=>RefundedAlready$),
    "RefundedAlready$inboundSchema": (()=>RefundedAlready$inboundSchema),
    "RefundedAlready$outboundSchema": (()=>RefundedAlready$outboundSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/types.js [app-rsc] (ecmascript)");
;
class RefundedAlready extends Error {
    constructor(err){
        const message = "message" in err && typeof err.message === "string" ? err.message : `API error occurred: ${JSON.stringify(err)}`;
        super(message);
        this.data$ = err;
        this.error = err.error;
        this.detail = err.detail;
        this.name = "RefundedAlready";
    }
}
const RefundedAlready$inboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("RefundedAlready"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}).transform((v)=>{
    return new RefundedAlready(v);
});
const RefundedAlready$outboundSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["instanceof"])(RefundedAlready).transform((v)=>v.data$).pipe((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["object"])({
    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["literal"])("RefundedAlready").default("RefundedAlready"),
    detail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["string"])()
}));
var RefundedAlready$;
(function(RefundedAlready$) {
    /** @deprecated use `RefundedAlready$inboundSchema` instead. */ RefundedAlready$.inboundSchema = RefundedAlready$inboundSchema;
    /** @deprecated use `RefundedAlready$outboundSchema` instead. */ RefundedAlready$.outboundSchema = RefundedAlready$outboundSchema;
})(RefundedAlready$ || (RefundedAlready$ = {})); //# sourceMappingURL=refundedalready.js.map
}}),

};

//# sourceMappingURL=node_modules_%40polar-sh_sdk_dist_esm_models_errors_de00e0fc._.js.map