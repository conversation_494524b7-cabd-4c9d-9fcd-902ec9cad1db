{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/banners/u24.tsx"], "sourcesContent": ["\"use client\";\n\nimport type React from \"react\";\n\nimport { animate } from \"animejs\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { useEffect, useRef, useState } from \"react\";\n\ninterface United24BannerProps {\n  animateGradient?: boolean;\n  onClose?: () => void;\n  showCloseButton?: boolean;\n}\n\nconst United24Banner: React.FC<United24BannerProps> = ({\n  animateGradient = true,\n  onClose,\n  showCloseButton = true,\n}) => {\n  const [isVisible, setIsVisible] = useState(true);\n  const bannerRef = useRef<HTMLDivElement>(null);\n  const animationsRef = useRef<ReturnType<typeof animate>[]>([]);\n\n  const handleClose = () => {\n    if (bannerRef.current) {\n      // Stop any existing animations\n      for (const animation of animationsRef.current) {\n        animation.pause();\n      }\n\n      // Create close animation\n      const animation = animate(bannerRef.current, {\n        duration: 500,\n        ease: \"outQuad\",\n        height: 0,\n        onComplete: () => {\n          setIsVisible(false);\n          if (onClose) {\n            onClose();\n          }\n        },\n        opacity: 0,\n        paddingBottom: 0,\n        paddingTop: 0,\n      });\n\n      // Store the animation reference\n      animationsRef.current = [animation];\n    }\n  };\n\n  useEffect(() => {\n    if (!isVisible || !bannerRef.current) return;\n\n    // Clear previous animations\n    for (const animation of animationsRef.current) {\n      animation.pause();\n    }\n\n    // Initialize new animations\n    const animations: ReturnType<typeof animate>[] = [];\n    const banner = bannerRef.current;\n\n    // Get elements within the banner\n    const contentElements = banner.querySelectorAll(\".banner-content\");\n    const logoElements = banner.querySelectorAll(\".banner-logo\");\n    const buttonElements = banner.querySelectorAll(\".banner-button\");\n\n    // Entrance animation for content\n    const contentAnimation = animate(contentElements, {\n      delay: (_, i) => i * 100, // Stagger effect\n      duration: 800,\n      ease: \"outExpo\",\n      opacity: [0, 1],\n      translateY: [20, 0],\n    });\n    animations.push(contentAnimation);\n\n    // Logo animation\n    const logoAnimation = animate(logoElements, {\n      duration: 1000,\n      ease: \"outElastic(1, 0.5)\",\n      opacity: [0, 1],\n      rotate: [\"-15deg\", \"0deg\"],\n      scale: [0.8, 1],\n    });\n    animations.push(logoAnimation);\n\n    // Button animation\n    const buttonAnimation = animate(buttonElements, {\n      delay: 100,\n      duration: 200,\n      ease: \"outExpo\",\n      opacity: [0, 0.9],\n    });\n    animations.push(buttonAnimation);\n\n    // Ukrainian flag color gradient animation\n    if (animateGradient) {\n      const gradientAnimation = animate(banner, {\n        backgroundPosition: [\"100% 50%\", \"0% 50%\", \"100% 50%\"],\n        duration: 45000,\n        ease: \"linear\",\n        loop: true,\n      });\n      animations.push(gradientAnimation);\n    }\n\n    // Store animations for cleanup\n    animationsRef.current = animations;\n\n    return () => {\n      // Clean up all animations when component unmounts\n      for (const anim of animations) {\n        anim.pause();\n      }\n    };\n  }, [isVisible, animateGradient]);\n\n  if (!isVisible) return null;\n\n  // Generate background classes or styles based on gradient animation and theme\n  const bannerClasses = `w-full border-b border-black/10 dark:border-white/10 relative z-50 overflow-hidden banner-gradient-bg rounded-none md:rounded-xl ${\n    !animateGradient ? \"bg-[#ffd700] dark:bg-[#0057b7]\" : \"\"\n  }`;\n\n  // Determine the background style based on animateGradient prop\n  const backgroundStyle = animateGradient\n    ? {\n        background:\n          \"linear-gradient(90deg, #0057b7 0%, #0057b7 40%, #ffd700 60%, #ffd700 100%)\",\n        backgroundSize: \"200% 100%\",\n      }\n    : {};\n\n  // Dynamic button class based on gradient animation state\n  const buttonClasses = animateGradient\n    ? \"banner-button bg-[#0057b7] text-[#ffd700] font-bold py-2 px-7 rounded-lg hover:bg-white hover:text-[#0057b7] uppercase opacity-95 hover:opacity-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#ffd700]/70 focus:ring-offset-2\"\n    : \"banner-button bg-[#0057b7] dark:bg-[#ffd700] hover:bg-white dark:hover:bg-white text-[#ffd700] dark:text-blue-800 hover:text-[#0057b7] font-bold py-2 px-7 rounded-lg uppercase opacity-95 hover:opacity-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#ffd700]/70 focus:ring-offset-2\";\n\n  // Text color classes based on theme and gradient state\n  const textColorClasses = animateGradient\n    ? \"text-white drop-shadow-sm\"\n    : \"text-blue-800 dark:text-white drop-shadow-sm\";\n\n  return (\n    <div\n      aria-label=\"Support Ukraine banner\"\n      className={bannerClasses}\n      ref={bannerRef}\n      role=\"banner\"\n      style={backgroundStyle}\n    >\n      <div\n        className={`\n          container mx-auto flex flex-col items-center justify-between gap-4\n          px-4 py-4\n          md:flex-row md:gap-0\n        `}\n      >\n        <div\n          className={`\n            banner-content mb-4 flex flex-col items-center gap-3\n            md:mb-0 md:flex-row md:gap-5\n          `}\n        >\n          <div\n            className={`\n              banner-logo relative mb-3 flex-shrink-0\n              md:mr-5 md:mb-0\n            `}\n          >\n            {/* Use Image component with different sources based on theme */}\n            <Image\n              alt=\"United24 Logo\"\n              className={`\n                block h-auto w-24 rounded-lg shadow\n                dark:hidden\n              `}\n              height={48}\n              priority\n              src=\"/u24.svg\"\n              width={96}\n            />\n            <Image\n              alt=\"United24 Logo\"\n              className={`\n                hidden h-auto w-24 rounded-lg shadow\n                dark:block\n              `}\n              height={48}\n              priority\n              src=\"/u24_white.svg\"\n              width={96}\n            />\n          </div>\n          <p\n            className={`\n              banner-content text-center text-base font-semibold\n              md:text-left\n              ${textColorClasses}\n            `}\n          >\n            Stand with Ukraine. Help fund drones, medkits, and victory. Every\n            dollar helps stop{\" \"}\n            <Link\n              className={`\n                underline underline-offset-4 transition-colors duration-200\n                hover:text-[#0057b7]\n                dark:hover:text-[#ffd700]\n              `}\n              href=\"https://war.ukraine.ua/russia-war-crimes\"\n              rel=\"noopener noreferrer\"\n              target=\"_blank\"\n            >\n              russia's war crimes\n            </Link>{\" \"}\n            and saves lives. Donate now, it matters.\n          </p>\n        </div>\n\n        <div\n          className={`\n            banner-content flex items-center gap-2\n            md:gap-4\n          `}\n        >\n          <Link\n            aria-label=\"Donate to support Ukraine\"\n            className={buttonClasses}\n            href=\"https://u24.gov.ua\"\n            rel=\"noopener noreferrer\"\n            target=\"_blank\"\n          >\n            Donate\n          </Link>\n\n          {showCloseButton && (\n            <button\n              aria-label=\"Close Ukraine support banner\"\n              className={`\n                banner-content ml-2 rounded-full p-2 opacity-80\n                transition-opacity duration-200\n                focus:ring-opacity-50 focus:ring-2 focus:ring-current\n                focus:outline-none\n                hover:opacity-100\n                md:ml-4\n                ${textColorClasses}\n              `}\n              onClick={handleClose}\n              type=\"button\"\n            >\n              <svg\n                aria-hidden=\"true\"\n                className=\"h-5 w-5\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <title>Close</title>\n                <path\n                  clipRule=\"evenodd\"\n                  d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\"\n                  fillRule=\"evenodd\"\n                />\n              </svg>\n            </button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default United24Banner;\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AAPA;;;;;;AAeA,MAAM,iBAAgD,CAAC,EACrD,kBAAkB,IAAI,EACtB,OAAO,EACP,kBAAkB,IAAI,EACvB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgC,EAAE;IAE7D,MAAM,cAAc;QAClB,IAAI,UAAU,OAAO,EAAE;YACrB,+BAA+B;YAC/B,KAAK,MAAM,aAAa,cAAc,OAAO,CAAE;gBAC7C,UAAU,KAAK;YACjB;YAEA,yBAAyB;YACzB,MAAM,YAAY,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO,EAAE;gBAC3C,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,YAAY;oBACV,aAAa;oBACb,IAAI,SAAS;wBACX;oBACF;gBACF;gBACA,SAAS;gBACT,eAAe;gBACf,YAAY;YACd;YAEA,gCAAgC;YAChC,cAAc,OAAO,GAAG;gBAAC;aAAU;QACrC;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,UAAU,OAAO,EAAE;QAEtC,4BAA4B;QAC5B,KAAK,MAAM,aAAa,cAAc,OAAO,CAAE;YAC7C,UAAU,KAAK;QACjB;QAEA,4BAA4B;QAC5B,MAAM,aAA2C,EAAE;QACnD,MAAM,SAAS,UAAU,OAAO;QAEhC,iCAAiC;QACjC,MAAM,kBAAkB,OAAO,gBAAgB,CAAC;QAChD,MAAM,eAAe,OAAO,gBAAgB,CAAC;QAC7C,MAAM,iBAAiB,OAAO,gBAAgB,CAAC;QAE/C,iCAAiC;QACjC,MAAM,mBAAmB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;YAChD,OAAO,CAAC,GAAG,IAAM,IAAI;YACrB,UAAU;YACV,MAAM;YACN,SAAS;gBAAC;gBAAG;aAAE;YACf,YAAY;gBAAC;gBAAI;aAAE;QACrB;QACA,WAAW,IAAI,CAAC;QAEhB,iBAAiB;QACjB,MAAM,gBAAgB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,cAAc;YAC1C,UAAU;YACV,MAAM;YACN,SAAS;gBAAC;gBAAG;aAAE;YACf,QAAQ;gBAAC;gBAAU;aAAO;YAC1B,OAAO;gBAAC;gBAAK;aAAE;QACjB;QACA,WAAW,IAAI,CAAC;QAEhB,mBAAmB;QACnB,MAAM,kBAAkB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;YAC9C,OAAO;YACP,UAAU;YACV,MAAM;YACN,SAAS;gBAAC;gBAAG;aAAI;QACnB;QACA,WAAW,IAAI,CAAC;QAEhB,0CAA0C;QAC1C,IAAI,iBAAiB;YACnB,MAAM,oBAAoB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;gBACxC,oBAAoB;oBAAC;oBAAY;oBAAU;iBAAW;gBACtD,UAAU;gBACV,MAAM;gBACN,MAAM;YACR;YACA,WAAW,IAAI,CAAC;QAClB;QAEA,+BAA+B;QAC/B,cAAc,OAAO,GAAG;QAExB,OAAO;YACL,kDAAkD;YAClD,KAAK,MAAM,QAAQ,WAAY;gBAC7B,KAAK,KAAK;YACZ;QACF;IACF,GAAG;QAAC;QAAW;KAAgB;IAE/B,IAAI,CAAC,WAAW,OAAO;IAEvB,8EAA8E;IAC9E,MAAM,gBAAgB,CAAC,iIAAiI,EACtJ,CAAC,kBAAkB,mCAAmC,IACtD;IAEF,+DAA+D;IAC/D,MAAM,kBAAkB,kBACpB;QACE,YACE;QACF,gBAAgB;IAClB,IACA,CAAC;IAEL,yDAAyD;IACzD,MAAM,gBAAgB,kBAClB,gQACA;IAEJ,uDAAuD;IACvD,MAAM,mBAAmB,kBACrB,8BACA;IAEJ,qBACE,8OAAC;QACC,cAAW;QACX,WAAW;QACX,KAAK;QACL,MAAK;QACL,OAAO;kBAEP,cAAA,8OAAC;YACC,WAAW,CAAC;;;;QAIZ,CAAC;;8BAED,8OAAC;oBACC,WAAW,CAAC;;;UAGZ,CAAC;;sCAED,8OAAC;4BACC,WAAW,CAAC;;;YAGZ,CAAC;;8CAGD,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,WAAW,CAAC;;;cAGZ,CAAC;oCACD,QAAQ;oCACR,QAAQ;oCACR,KAAI;oCACJ,OAAO;;;;;;8CAET,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,WAAW,CAAC;;;cAGZ,CAAC;oCACD,QAAQ;oCACR,QAAQ;oCACR,KAAI;oCACJ,OAAO;;;;;;;;;;;;sCAGX,8OAAC;4BACC,WAAW,CAAC;;;cAGV,EAAE,iBAAiB;YACrB,CAAC;;gCACF;gCAEmB;8CAClB,8OAAC,4JAAA,CAAA,UAAI;oCACH,WAAW,CAAC;;;;cAIZ,CAAC;oCACD,MAAK;oCACL,KAAI;oCACJ,QAAO;8CACR;;;;;;gCAEO;gCAAI;;;;;;;;;;;;;8BAKhB,8OAAC;oBACC,WAAW,CAAC;;;UAGZ,CAAC;;sCAED,8OAAC,4JAAA,CAAA,UAAI;4BACH,cAAW;4BACX,WAAW;4BACX,MAAK;4BACL,KAAI;4BACJ,QAAO;sCACR;;;;;;wBAIA,iCACC,8OAAC;4BACC,cAAW;4BACX,WAAW,CAAC;;;;;;;gBAOV,EAAE,iBAAiB;cACrB,CAAC;4BACD,SAAS;4BACT,MAAK;sCAEL,cAAA,8OAAC;gCACC,eAAY;gCACZ,WAAU;gCACV,MAAK;gCACL,SAAQ;gCACR,OAAM;;kDAEN,8OAAC;kDAAM;;;;;;kDACP,8OAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B;uCAEe", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/product-card.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Heart, ShoppingCart, Star } from \"lucide-react\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport * as React from \"react\";\n\nimport { cn } from \"~/lib/cn\";\nimport { Badge } from \"~/ui/primitives/badge\";\nimport { <PERSON><PERSON> } from \"~/ui/primitives/button\";\nimport { Card, CardContent, CardFooter } from \"~/ui/primitives/card\";\n\ntype ProductCardProps = Omit<\n  React.HTMLAttributes<HTMLDivElement>,\n  \"onError\"\n> & {\n  onAddToCart?: (productId: string) => void;\n  onAddToWishlist?: (productId: string) => void;\n  product: {\n    category: string;\n    id: string;\n    image: string;\n    inStock?: boolean;\n    name: string;\n    originalPrice?: number;\n    price: number;\n    rating?: number;\n  };\n  variant?: \"compact\" | \"default\";\n};\n\nexport function ProductCard({\n  className,\n  onAddToCart,\n  onAddToWishlist,\n  product,\n  variant = \"default\",\n  ...props\n}: ProductCardProps) {\n  const [isHovered, setIsHovered] = React.useState(false);\n  const [isAddingToCart, setIsAddingToCart] = React.useState(false);\n  const [isInWishlist, setIsInWishlist] = React.useState(false);\n\n  const handleAddToCart = (e: React.MouseEvent) => {\n    e.preventDefault();\n    if (onAddToCart) {\n      setIsAddingToCart(true);\n      // Simulate API call\n      setTimeout(() => {\n        onAddToCart(product.id);\n        setIsAddingToCart(false);\n      }, 600);\n    }\n  };\n\n  const handleAddToWishlist = (e: React.MouseEvent) => {\n    e.preventDefault();\n    if (onAddToWishlist) {\n      setIsInWishlist(!isInWishlist);\n      onAddToWishlist(product.id);\n    }\n  };\n\n  const discount = product.originalPrice\n    ? Math.round(\n        ((product.originalPrice - product.price) / product.originalPrice) * 100\n      )\n    : 0;\n\n  const renderStars = () => {\n    const rating = product.rating ?? 0;\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 >= 0.5;\n\n    return (\n      <div className=\"flex items-center\">\n        {Array.from({ length: 5 }).map((_, i) => (\n          <Star\n            className={cn(\n              \"h-4 w-4\",\n              i < fullStars\n                ? \"fill-yellow-400 text-yellow-400\"\n                : i === fullStars && hasHalfStar\n                ? \"fill-yellow-400/50 text-yellow-400\"\n                : \"stroke-muted/40 text-muted\"\n            )}\n            key={`star-${product.id}-position-${i + 1}`}\n          />\n        ))}\n        {rating > 0 && (\n          <span className=\"ml-1 text-xs text-muted-foreground\">\n            {rating.toFixed(1)}\n          </span>\n        )}\n      </div>\n    );\n  };\n\n  return (\n    <div className={cn(\"group\", className)} {...props}>\n      <Link href={`/products/${product.id}`}>\n        <Card\n          className={cn(\n            `\n              relative h-full overflow-hidden rounded-lg py-0 transition-all\n              duration-200 ease-in-out\n              hover:shadow-md\n            `,\n            isHovered && \"ring-1 ring-primary/20\"\n          )}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n        >\n          <div className=\"relative aspect-square overflow-hidden rounded-t-lg\">\n            {product.image && (\n              <Image\n                alt={product.name}\n                className={cn(\n                  \"object-cover transition-transform duration-300 ease-in-out\",\n                  isHovered && \"scale-105\"\n                )}\n                fill\n                sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                src={product.image}\n              />\n            )}\n\n            {/* Category badge */}\n            <Badge\n              className={`\n                absolute top-2 left-2 bg-background/80 backdrop-blur-sm\n              `}\n              variant=\"outline\"\n            >\n              {product.category}\n            </Badge>\n\n            {/* Discount badge */}\n            {discount > 0 && (\n              <Badge\n                className={`\n                absolute top-2 right-2 bg-destructive\n                text-destructive-foreground\n              `}\n              >\n                {discount}% OFF\n              </Badge>\n            )}\n\n            {/* Wishlist button */}\n            <Button\n              className={cn(\n                `\n                  absolute right-2 bottom-2 z-10 rounded-full bg-background/80\n                  backdrop-blur-sm transition-opacity duration-300\n                `,\n                !isHovered && !isInWishlist && \"opacity-0\"\n              )}\n              onClick={handleAddToWishlist}\n              size=\"icon\"\n              type=\"button\"\n              variant=\"outline\"\n            >\n              <Heart\n                className={cn(\n                  \"h-4 w-4\",\n                  isInWishlist\n                    ? \"fill-destructive text-destructive\"\n                    : \"text-muted-foreground\"\n                )}\n              />\n              <span className=\"sr-only\">Add to wishlist</span>\n            </Button>\n          </div>\n\n          <CardContent className=\"p-4 pt-4\">\n            {/* Product name with line clamp */}\n            <h3\n              className={`\n                line-clamp-2 text-base font-medium transition-colors\n                group-hover:text-primary\n              `}\n            >\n              {product.name}\n            </h3>\n\n            {variant === \"default\" && (\n              <>\n                <div className=\"mt-1.5\">{renderStars()}</div>\n                <div className=\"mt-2 flex items-center gap-1.5\">\n                  <span className=\"font-medium text-foreground\">\n                    ${product.price.toFixed(2)}\n                  </span>\n                  {product.originalPrice ? (\n                    <span className=\"text-sm text-muted-foreground line-through\">\n                      ${product.originalPrice.toFixed(2)}\n                    </span>\n                  ) : null}\n                </div>\n              </>\n            )}\n          </CardContent>\n\n          {variant === \"default\" && (\n            <CardFooter className=\"p-4 pt-0\">\n              <Button\n                className={cn(\n                  \"w-full gap-2 transition-all\",\n                  isAddingToCart && \"opacity-70\"\n                )}\n                disabled={isAddingToCart}\n                onClick={handleAddToCart}\n              >\n                {isAddingToCart ? (\n                  <div\n                    className={`\n                      h-4 w-4 animate-spin rounded-full border-2\n                      border-background border-t-transparent\n                    `}\n                  />\n                ) : (\n                  <ShoppingCart className=\"h-4 w-4\" />\n                )}\n                Add to Cart\n              </Button>\n            </CardFooter>\n          )}\n\n          {variant === \"compact\" && (\n            <CardFooter className=\"p-4 pt-0\">\n              <div className=\"flex w-full items-center justify-between\">\n                <div className=\"flex items-center gap-1.5\">\n                  <span className=\"font-medium text-foreground\">\n                    ${product.price.toFixed(2)}\n                  </span>\n                  {product.originalPrice ? (\n                    <span className=\"text-sm text-muted-foreground line-through\">\n                      ${product.originalPrice.toFixed(2)}\n                    </span>\n                  ) : null}\n                </div>\n                <Button\n                  className=\"h-8 w-8 rounded-full\"\n                  disabled={isAddingToCart}\n                  onClick={handleAddToCart}\n                  size=\"icon\"\n                  variant=\"ghost\"\n                >\n                  {isAddingToCart ? (\n                    <div\n                      className={`\n                        h-4 w-4 animate-spin rounded-full border-2\n                        border-primary border-t-transparent\n                      `}\n                    />\n                  ) : (\n                    <ShoppingCart className=\"h-4 w-4\" />\n                  )}\n                  <span className=\"sr-only\">Add to cart</span>\n                </Button>\n              </div>\n            </CardFooter>\n          )}\n\n          {!product.inStock && (\n            <div\n              className={`\n                absolute inset-0 flex items-center justify-center\n                bg-background/80 backdrop-blur-sm\n              `}\n            >\n              <Badge className=\"px-3 py-1 text-sm\" variant=\"destructive\">\n                Out of Stock\n              </Badge>\n            </div>\n          )}\n        </Card>\n      </Link>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAVA;;;;;;;;;;AA+BO,SAAS,YAAY,EAC1B,SAAS,EACT,WAAW,EACX,eAAe,EACf,OAAO,EACP,UAAU,SAAS,EACnB,GAAG,OACc;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEvD,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,IAAI,aAAa;YACf,kBAAkB;YAClB,oBAAoB;YACpB,WAAW;gBACT,YAAY,QAAQ,EAAE;gBACtB,kBAAkB;YACpB,GAAG;QACL;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,EAAE,cAAc;QAChB,IAAI,iBAAiB;YACnB,gBAAgB,CAAC;YACjB,gBAAgB,QAAQ,EAAE;QAC5B;IACF;IAEA,MAAM,WAAW,QAAQ,aAAa,GAClC,KAAK,KAAK,CACR,AAAC,CAAC,QAAQ,aAAa,GAAG,QAAQ,KAAK,IAAI,QAAQ,aAAa,GAAI,OAEtE;IAEJ,MAAM,cAAc;QAClB,MAAM,SAAS,QAAQ,MAAM,IAAI;QACjC,MAAM,YAAY,KAAK,KAAK,CAAC;QAC7B,MAAM,cAAc,SAAS,KAAK;QAElC,qBACE,8OAAC;YAAI,WAAU;;gBACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC,kMAAA,CAAA,OAAI;wBACH,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,WACA,IAAI,YACA,oCACA,MAAM,aAAa,cACnB,uCACA;uBAED,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC,UAAU,EAAE,IAAI,GAAG;;;;;gBAG9C,SAAS,mBACR,8OAAC;oBAAK,WAAU;8BACb,OAAO,OAAO,CAAC;;;;;;;;;;;;IAK1B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,SAAS;QAAa,GAAG,KAAK;kBAC/C,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;sBACnC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBACH,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;YAID,CAAC,EACD,aAAa;gBAEf,cAAc,IAAM,aAAa;gBACjC,cAAc,IAAM,aAAa;;kCAEjC,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,KAAK,kBACZ,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,QAAQ,IAAI;gCACjB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,8DACA,aAAa;gCAEf,IAAI;gCACJ,OAAM;gCACN,KAAK,QAAQ,KAAK;;;;;;0CAKtB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,WAAW,CAAC;;cAEZ,CAAC;gCACD,SAAQ;0CAEP,QAAQ,QAAQ;;;;;;4BAIlB,WAAW,mBACV,8OAAC,iIAAA,CAAA,QAAK;gCACJ,WAAW,CAAC;;;cAGd,CAAC;;oCAEE;oCAAS;;;;;;;0CAKd,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;gBAGD,CAAC,EACD,CAAC,aAAa,CAAC,gBAAgB;gCAEjC,SAAS;gCACT,MAAK;gCACL,MAAK;gCACL,SAAQ;;kDAER,8OAAC,oMAAA,CAAA,QAAK;wCACJ,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,WACA,eACI,sCACA;;;;;;kDAGR,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;kCAI9B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,8OAAC;gCACC,WAAW,CAAC;;;cAGZ,CAAC;0CAEA,QAAQ,IAAI;;;;;;4BAGd,YAAY,2BACX;;kDACE,8OAAC;wCAAI,WAAU;kDAAU;;;;;;kDACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDAA8B;oDAC1C,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;4CAEzB,QAAQ,aAAa,iBACpB,8OAAC;gDAAK,WAAU;;oDAA6C;oDACzD,QAAQ,aAAa,CAAC,OAAO,CAAC;;;;;;uDAEhC;;;;;;;;;;;;;;;oBAMX,YAAY,2BACX,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,+BACA,kBAAkB;4BAEpB,UAAU;4BACV,SAAS;;gCAER,+BACC,8OAAC;oCACC,WAAW,CAAC;;;oBAGZ,CAAC;;;;;yDAGH,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCACxB;;;;;;;;;;;;oBAMP,YAAY,2BACX,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDAA8B;gDAC1C,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;wCAEzB,QAAQ,aAAa,iBACpB,8OAAC;4CAAK,WAAU;;gDAA6C;gDACzD,QAAQ,aAAa,CAAC,OAAO,CAAC;;;;;;mDAEhC;;;;;;;8CAEN,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,UAAU;oCACV,SAAS;oCACT,MAAK;oCACL,SAAQ;;wCAEP,+BACC,8OAAC;4CACC,WAAW,CAAC;;;sBAGZ,CAAC;;;;;iEAGH,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDAE1B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;oBAMjC,CAAC,QAAQ,OAAO,kBACf,8OAAC;wBACC,WAAW,CAAC;;;cAGZ,CAAC;kCAED,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;4BAAoB,SAAQ;sCAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzE", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/testimonial.tsx"], "sourcesContent": ["import { cn } from \"~/lib/cn\";\nimport { Avatar, AvatarImage } from \"~/ui/primitives/avatar\";\n\nexport interface TestimonialAuthor {\n  avatar: string;\n  handle: string;\n  name: string;\n}\n\nexport interface TestimonialCardProps {\n  author: TestimonialAuthor;\n  className?: string;\n  href?: string;\n  text: string;\n}\n\nexport function TestimonialCard({\n  author,\n  className,\n  href,\n  text,\n}: TestimonialCardProps) {\n  const Card = href ? \"a\" : \"div\";\n\n  return (\n    <Card\n      {...(href ? { href } : {})}\n      className={cn(\n        \"flex flex-col rounded-lg border-t\",\n        \"bg-gradient-to-b from-muted/50 to-muted/10\",\n        `\n          p-4 text-start\n          sm:p-6\n        `,\n        \"hover:from-muted/60 hover:to-muted/20\",\n        `\n          max-w-[320px]\n          sm:max-w-[320px]\n        `,\n        \"transition-colors duration-300\",\n        className,\n      )}\n    >\n      <div className=\"flex items-center gap-3\">\n        <Avatar className=\"h-12 w-12\">\n          <AvatarImage alt={author.name} src={author.avatar} />\n        </Avatar>\n        <div className=\"flex flex-col items-start\">\n          <h3 className=\"text-md leading-none font-semibold\">{author.name}</h3>\n          <p className=\"text-sm text-muted-foreground\">{author.handle}</p>\n        </div>\n      </div>\n      <p\n        className={`\n          sm:text-md\n          mt-4 text-sm text-muted-foreground\n        `}\n      >\n        {text}\n      </p>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAeO,SAAS,gBAAgB,EAC9B,MAAM,EACN,SAAS,EACT,IAAI,EACJ,IAAI,EACiB;IACrB,MAAM,OAAO,OAAO,MAAM;IAE1B,qBACE,8OAAC;QACE,GAAI,OAAO;YAAE;QAAK,IAAI,CAAC,CAAC;QACzB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,qCACA,8CACA,CAAC;;;QAGD,CAAC,EACD,yCACA,CAAC;;;QAGD,CAAC,EACD,kCACA;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;kCAChB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,KAAK,OAAO,IAAI;4BAAE,KAAK,OAAO,MAAM;;;;;;;;;;;kCAEnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC,OAAO,IAAI;;;;;;0CAC/D,8OAAC;gCAAE,WAAU;0CAAiC,OAAO,MAAM;;;;;;;;;;;;;;;;;;0BAG/D,8OAAC;gBACC,WAAW,CAAC;;;QAGZ,CAAC;0BAEA;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/testimonials/testimonials-with-marquee.tsx"], "sourcesContent": ["\"use client\";\n\nimport { animate } from \"animejs\";\nimport React, { useEffect, useRef } from \"react\";\n\nimport { cn } from \"~/lib/cn\";\nimport {\n  type TestimonialAuthor,\n  TestimonialCard,\n} from \"~/ui/primitives/testimonial\";\n\ninterface TestimonialsSectionProps {\n  className?: string;\n  description: string;\n  testimonials: {\n    author: TestimonialAuthor;\n    href?: string;\n    text: string;\n  }[];\n  title: string;\n}\n\nexport function TestimonialsSection({\n  className,\n  description,\n  testimonials,\n  title,\n}: TestimonialsSectionProps) {\n  const marqueeRef = useRef<HTMLDivElement>(null);\n  const animationRef = useRef<null | ReturnType<typeof animate>>(null);\n  const isHoveredRef = useRef(false);\n\n  useEffect(() => {\n    if (!marqueeRef.current) return;\n\n    // Calculate total width for accurate animation\n    const marqueeElement = marqueeRef.current;\n    const itemWidth = marqueeElement.scrollWidth / 4; // 4 sets of testimonials\n\n    // Create marquee animation\n    const setupAnimation = () => {\n      if (animationRef.current) {\n        animationRef.current.pause();\n      }\n\n      animationRef.current = animate(marqueeElement, {\n        duration: 40000, // 40s same as CSS\n        easing: \"linear\",\n        loop: true,\n        translateX: [\"0px\", `-${itemWidth}px`],\n      });\n\n      // Pause animation if already hovered\n      if (isHoveredRef.current) {\n        animationRef.current.pause();\n      }\n    };\n\n    // Initial setup\n    setupAnimation();\n\n    // Handle resize\n    const handleResize = () => {\n      setupAnimation();\n    };\n\n    window.addEventListener(\"resize\", handleResize);\n\n    return () => {\n      window.removeEventListener(\"resize\", handleResize);\n      if (animationRef.current) {\n        animationRef.current.pause();\n      }\n    };\n  }, []);\n\n  // Handle hover interactions\n  const handleMouseEnter = () => {\n    isHoveredRef.current = true;\n    if (animationRef.current) {\n      animationRef.current.pause();\n    }\n  };\n\n  const handleMouseLeave = () => {\n    isHoveredRef.current = false;\n    if (animationRef.current) {\n      animationRef.current.play();\n    }\n  };\n\n  return (\n    <section\n      className={cn(\n        \"bg-background text-foreground\",\n        `\n          px-0 py-12\n          sm:py-24\n          md:py-32\n        `,\n        className,\n      )}\n    >\n      <div\n        className={`\n          max-w-container mx-auto flex flex-col items-center gap-4 text-center\n          sm:gap-16\n        `}\n      >\n        <div\n          className={`\n            flex flex-col items-center gap-4 px-4\n            sm:gap-8\n          `}\n        >\n          <h2\n            className={`\n              max-w-[720px] text-3xl leading-tight font-semibold\n              sm:text-5xl sm:leading-tight\n            `}\n          >\n            {title}\n          </h2>\n          <p\n            className={`\n              text-md max-w-[600px] font-medium text-muted-foreground\n              sm:text-xl\n            `}\n          >\n            {description}\n          </p>\n        </div>\n\n        <div\n          className={`\n            relative flex w-full flex-col items-center justify-center\n            overflow-hidden\n          `}\n        >\n          <div\n            className={`\n              flex flex-row overflow-hidden p-2\n              [gap:var(--gap)]\n              [--gap:1rem]\n            `}\n            onMouseEnter={handleMouseEnter}\n            onMouseLeave={handleMouseLeave}\n          >\n            <div\n              className={`\n                flex shrink-0 flex-row justify-around\n                [gap:var(--gap)]\n              `}\n              ref={marqueeRef}\n              style={{ translate: \"none\" }}\n            >\n              {[...Array(4)].map((_, setIndex) =>\n                testimonials.map((testimonial, i) => (\n                  <TestimonialCard\n                    // Using UUID or other unique identifier would be better here,\n                    // but for static content this is acceptable\n                    key={`testimonial-${testimonial.author.name}-${setIndex}-${i}`}\n                    {...testimonial}\n                  />\n                )),\n              )}\n            </div>\n          </div>\n\n          <div\n            className={`\n              pointer-events-none absolute inset-y-0 left-0 hidden w-1/3\n              bg-gradient-to-r from-background\n              sm:block\n            `}\n          />\n          <div\n            className={`\n              pointer-events-none absolute inset-y-0 right-0 hidden w-1/3\n              bg-gradient-to-l from-background\n              sm:block\n            `}\n          />\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAsBO,SAAS,oBAAoB,EAClC,SAAS,EACT,WAAW,EACX,YAAY,EACZ,KAAK,EACoB;IACzB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqC;IAC/D,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,+CAA+C;QAC/C,MAAM,iBAAiB,WAAW,OAAO;QACzC,MAAM,YAAY,eAAe,WAAW,GAAG,GAAG,yBAAyB;QAE3E,2BAA2B;QAC3B,MAAM,iBAAiB;YACrB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK;YAC5B;YAEA,aAAa,OAAO,GAAG,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;gBAC7C,UAAU;gBACV,QAAQ;gBACR,MAAM;gBACN,YAAY;oBAAC;oBAAO,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC;iBAAC;YACxC;YAEA,qCAAqC;YACrC,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK;YAC5B;QACF;QAEA,gBAAgB;QAChB;QAEA,gBAAgB;QAChB,MAAM,eAAe;YACnB;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK;YAC5B;QACF;IACF,GAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,mBAAmB;QACvB,aAAa,OAAO,GAAG;QACvB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK;QAC5B;IACF;IAEA,MAAM,mBAAmB;QACvB,aAAa,OAAO,GAAG;QACvB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,IAAI;QAC3B;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,iCACA,CAAC;;;;QAID,CAAC,EACD;kBAGF,cAAA,8OAAC;YACC,WAAW,CAAC;;;QAGZ,CAAC;;8BAED,8OAAC;oBACC,WAAW,CAAC;;;UAGZ,CAAC;;sCAED,8OAAC;4BACC,WAAW,CAAC;;;YAGZ,CAAC;sCAEA;;;;;;sCAEH,8OAAC;4BACC,WAAW,CAAC;;;YAGZ,CAAC;sCAEA;;;;;;;;;;;;8BAIL,8OAAC;oBACC,WAAW,CAAC;;;UAGZ,CAAC;;sCAED,8OAAC;4BACC,WAAW,CAAC;;;;YAIZ,CAAC;4BACD,cAAc;4BACd,cAAc;sCAEd,cAAA,8OAAC;gCACC,WAAW,CAAC;;;cAGZ,CAAC;gCACD,KAAK;gCACL,OAAO;oCAAE,WAAW;gCAAO;0CAE1B;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,WACrB,aAAa,GAAG,CAAC,CAAC,aAAa,kBAC7B,8OAAC,uIAAA,CAAA,kBAAe;4CAIb,GAAG,WAAW;2CADV,CAAC,YAAY,EAAE,YAAY,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,GAAG;;;;;;;;;;;;;;;sCAQxE,8OAAC;4BACC,WAAW,CAAC;;;;YAIZ,CAAC;;;;;;sCAEH,8OAAC;4BACC,WAAW,CAAC;;;;YAIZ,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}