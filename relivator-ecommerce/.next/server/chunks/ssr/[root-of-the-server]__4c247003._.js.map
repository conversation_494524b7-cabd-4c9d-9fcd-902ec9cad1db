{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/banners/u24.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/ui/components/banners/u24.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/components/banners/u24.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/banners/u24.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/ui/components/banners/u24.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/components/banners/u24.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/lib/queries/github.ts"], "sourcesContent": ["import \"server-only\";\nimport { unstable_cache as cache } from \"next/cache\";\nimport { ofetch } from \"ofetch\";\n\nimport { SYSTEM_CONFIG } from \"~/app\";\n\nexport async function getGithubStars() {\n  if (!SYSTEM_CONFIG.repoStars) {\n    return null;\n  }\n\n  return await cache(\n    async () => {\n      try {\n        const data = await ofetch<{ repo: { stargazers_count: number } }>(\n          `https://regh.reliverse.org/repos/${SYSTEM_CONFIG.repoOwner}/${SYSTEM_CONFIG.repoName}`,\n          {\n            headers: {\n              Accept: \"application/vnd.github+json\",\n            },\n          },\n        );\n\n        if (\n          data?.repo?.stargazers_count !== undefined &&\n          typeof data.repo.stargazers_count === \"number\"\n        ) {\n          return data.repo.stargazers_count;\n        }\n        console.warn(\"github api response format unexpected:\", data);\n        return null;\n      } catch (error) {\n        console.error(\"failed to fetch github stars:\", error);\n        return null;\n      }\n    },\n    [\"github-stars\", SYSTEM_CONFIG.repoOwner, SYSTEM_CONFIG.repoName],\n    {\n      revalidate: 3600,\n      tags: [\"github-stars\"],\n    },\n  )();\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAEA;;;;;AAEO,eAAe;IACpB,IAAI,CAAC,0GAAA,CAAA,gBAAa,CAAC,SAAS,EAAE;QAC5B,OAAO;IACT;IAEA,OAAO,MAAM,CAAA,GAAA,6HAAA,CAAA,iBAAK,AAAD,EACf;QACE,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EACtB,CAAC,iCAAiC,EAAE,0GAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,CAAC,EAAE,0GAAA,CAAA,gBAAa,CAAC,QAAQ,EAAE,EACvF;gBACE,SAAS;oBACP,QAAQ;gBACV;YACF;YAGF,IACE,MAAM,MAAM,qBAAqB,aACjC,OAAO,KAAK,IAAI,CAAC,gBAAgB,KAAK,UACtC;gBACA,OAAO,KAAK,IAAI,CAAC,gBAAgB;YACnC;YACA,QAAQ,IAAI,CAAC,0CAA0C;YACvD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF,GACA;QAAC;QAAgB,0GAAA,CAAA,gBAAa,CAAC,SAAS;QAAE,0GAAA,CAAA,gBAAa,CAAC,QAAQ;KAAC,EACjE;QACE,YAAY;QACZ,MAAM;YAAC;SAAe;IACxB;AAEJ", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/icons/github.tsx"], "sourcesContent": ["export function GitHubIcon(props: React.ComponentProps<\"svg\">) {\n  return (\n    <svg\n      role=\"img\"\n      viewBox=\"0 0 24 24\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <title>GitHub</title>\n      <path\n        d=\"M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAO,SAAS,WAAW,KAAkC;IAC3D,qBACE,8OAAC;QACC,MAAK;QACL,SAAQ;QACR,OAAM;QACL,GAAG,KAAK;;0BAET,8OAAC;0BAAM;;;;;;0BACP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/hero-badge.tsx"], "sourcesContent": ["import Link from \"next/link\";\n\nimport { SEO_CONFIG, SYSTEM_CONFIG } from \"~/app\";\nimport { getGithubStars } from \"~/lib/queries/github\";\n\nimport { GitHubIcon } from \"./icons/github\";\n\nexport async function HeroBadge() {\n  const githubStars = await getGithubStars();\n\n  return (\n    <Link\n      className={`\n        inline-flex items-center rounded-lg bg-primary/10 px-3 py-1 text-sm\n        font-semibold text-primary\n      `}\n      href={\n        SYSTEM_CONFIG.repoStars\n          ? `https://github.com/${SYSTEM_CONFIG.repoOwner}/${SYSTEM_CONFIG.repoName}`\n          : \"/products\"\n      }\n      rel={SYSTEM_CONFIG.repoStars ? \"noopener noreferrer\" : undefined}\n      target={SYSTEM_CONFIG.repoStars ? \"_blank\" : undefined}\n    >\n      {SYSTEM_CONFIG.repoStars ? (\n        <div className=\"flex items-center gap-1\">\n          <span>{SEO_CONFIG.fullName}</span>\n          <span className=\"text-muted-foreground\">|</span>\n          <GitHubIcon className=\"h-3.5 w-3.5\" />\n          {githubStars && (\n            <span>⭐ {githubStars.toLocaleString()} stars on GitHub</span>\n          )}\n        </div>\n      ) : (\n        SEO_CONFIG.fullName\n      )}\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AAEA;;;;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,WAAW,CAAC;;;MAGZ,CAAC;QACD,MACE,0GAAA,CAAA,gBAAa,CAAC,SAAS,GACnB,CAAC,mBAAmB,EAAE,0GAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,CAAC,EAAE,0GAAA,CAAA,gBAAa,CAAC,QAAQ,EAAE,GACzE;QAEN,KAAK,0GAAA,CAAA,gBAAa,CAAC,SAAS,GAAG,wBAAwB;QACvD,QAAQ,0GAAA,CAAA,gBAAa,CAAC,SAAS,GAAG,WAAW;kBAE5C,0GAAA,CAAA,gBAAa,CAAC,SAAS,iBACtB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;8BAAM,0GAAA,CAAA,aAAU,CAAC,QAAQ;;;;;;8BAC1B,8OAAC;oBAAK,WAAU;8BAAwB;;;;;;8BACxC,8OAAC,2IAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;gBACrB,6BACC,8OAAC;;wBAAK;wBAAG,YAAY,cAAc;wBAAG;;;;;;;;;;;;mBAI1C,0GAAA,CAAA,aAAU,CAAC,QAAQ;;;;;;AAI3B", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/product-card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductCard() from the server but ProductCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/components/product-card.tsx <module evaluation>\",\n    \"ProductCard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,oEACA", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/product-card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductCard() from the server but ProductCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/components/product-card.tsx\",\n    \"ProductCard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gDACA", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/testimonials/testimonials-with-marquee.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TestimonialsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call TestimonialsSection() from the server but TestimonialsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/components/testimonials/testimonials-with-marquee.tsx <module evaluation>\",\n    \"TestimonialsSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8FACA", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/testimonials/testimonials-with-marquee.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TestimonialsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call TestimonialsSection() from the server but TestimonialsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/ui/components/testimonials/testimonials-with-marquee.tsx\",\n    \"TestimonialsSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0EACA", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/card.tsx"], "sourcesContent": ["import type * as React from \"react\";\n\nimport { cn } from \"~/lib/cn\";\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\n        `\n          flex flex-col gap-6 rounded-xl border bg-card py-6\n          text-card-foreground shadow-sm\n        `,\n        className,\n      )}\n      data-slot=\"card\"\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className,\n      )}\n      data-slot=\"card-action\"\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\"px-6\", className)}\n      data-slot=\"card-content\"\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      data-slot=\"card-description\"\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\n        `\n          flex items-center px-6\n          [.border-t]:pt-6\n        `,\n        className,\n      )}\n      data-slot=\"card-footer\"\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\n        `\n          @container/card-header grid auto-rows-min grid-rows-[auto_auto]\n          items-start gap-1.5 px-6\n          has-data-[slot=card-action]:grid-cols-[1fr_auto]\n          [.border-b]:pb-6\n        `,\n        className,\n      )}\n      data-slot=\"card-header\"\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\"leading-none font-semibold\", className)}\n      data-slot=\"card-title\"\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardAction,\n  CardContent,\n  CardDescription,\n  CardFooter,\n  CardHeader,\n  CardTitle,\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;QAGD,CAAC,EACD;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACtB,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;QAGD,CAAC,EACD;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;QAKD,CAAC,EACD;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC5C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/app/mocks.ts"], "sourcesContent": ["export const featuredProductsHomepage = [\n  {\n    category: \"Audio\",\n    id: \"1\",\n    image:\n      \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3\",\n    inStock: true,\n    name: \"Premium Wireless Headphones\",\n    originalPrice: 249.99,\n    price: 199.99,\n    rating: 4.5,\n  },\n  {\n    category: \"Wearables\",\n    id: \"2\",\n    image:\n      \"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3\",\n    inStock: true,\n    name: \"Smart Watch Series 5\",\n    originalPrice: 349.99,\n    price: 299.99,\n    rating: 4.2,\n  },\n  {\n    category: \"Smartphones\",\n    id: \"5\",\n    image:\n      \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3\",\n    inStock: true,\n    name: \"Smartphone Pro Max\",\n    originalPrice: 1099.99,\n    price: 999.99,\n    rating: 4.8,\n  },\n  {\n    category: \"Audio\",\n    id: \"6\",\n    image:\n      \"https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3\",\n    inStock: true,\n    name: \"Bluetooth Earbuds Pro\",\n    originalPrice: 179.99,\n    price: 149.99,\n    rating: 4.4,\n  },\n];\n\n// Categories for the shop by category section\nexport const categories = [\n  {\n    image:\n      \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3\",\n    name: \"Audio\",\n    productCount: 12,\n  },\n  {\n    image:\n      \"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3\",\n    name: \"Wearables\",\n    productCount: 8,\n  },\n  {\n    image:\n      \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3\",\n    name: \"Smartphones\",\n    productCount: 15,\n  },\n  {\n    image:\n      \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3\",\n    name: \"Laptops\",\n    productCount: 10,\n  },\n];\n\n// Testimonials for the testimonials section\nexport const testimonials = [\n  {\n    author: {\n      avatar:\n        \"https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face\",\n      handle: \"@sarahtechie\",\n      name: \"Sarah Johnson\",\n    },\n    text: \"Honestly, I don't even remember how many times I've ordered from here. Never once had a bad experience. Stuff just works, and when I had a question, support was on it in like, five minutes.\",\n  },\n  {\n    author: {\n      avatar:\n        \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n      handle: \"@mikedev\",\n      name: \"Michael Chen\",\n    },\n    text: \"Wasn't expecting much tbh, but ended up being super impressed. Got a keyboard that feels chef's kiss and arrived faster than other stores. No complaints.\",\n  },\n  {\n    author: {\n      avatar:\n        \"https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face\",\n      handle: \"@emdigital\",\n      name: \"Emily Rodriguez\",\n    },\n    text: \"I was totally lost picking a laptop for freelance work. Dropped them a message and got a thoughtful reply within the hour. They didn't push the priciest option either, which I respect. Love what I got.\",\n  },\n  {\n    author: {\n      avatar:\n        \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\n      handle: \"@davidtech\",\n      name: \"David Park\",\n    },\n    text: \"Site's clean, checkout was quick, and my order showed up two days early. Which never happens. Already eyeing my next upgrade 👀\",\n  },\n  {\n    author: {\n      avatar:\n        \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\",\n      handle: \"@sophiareviews\",\n      name: \"Sophia Martinez\",\n    },\n    text: \"Finally. A tech store that doesn't make me feel dumb. Everything's explained in plain English, and their chat team didn't treat me like I was annoying them. 10/10.\",\n  },\n];\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,2BAA2B;IACtC;QACE,UAAU;QACV,IAAI;QACJ,OACE;QACF,SAAS;QACT,MAAM;QACN,eAAe;QACf,OAAO;QACP,QAAQ;IACV;IACA;QACE,UAAU;QACV,IAAI;QACJ,OACE;QACF,SAAS;QACT,MAAM;QACN,eAAe;QACf,OAAO;QACP,QAAQ;IACV;IACA;QACE,UAAU;QACV,IAAI;QACJ,OACE;QACF,SAAS;QACT,MAAM;QACN,eAAe;QACf,OAAO;QACP,QAAQ;IACV;IACA;QACE,UAAU;QACV,IAAI;QACJ,OACE;QACF,SAAS;QACT,MAAM;QACN,eAAe;QACf,OAAO;QACP,QAAQ;IACV;CACD;AAGM,MAAM,aAAa;IACxB;QACE,OACE;QACF,MAAM;QACN,cAAc;IAChB;IACA;QACE,OACE;QACF,MAAM;QACN,cAAc;IAChB;IACA;QACE,OACE;QACF,MAAM;QACN,cAAc;IAChB;IACA;QACE,OACE;QACF,MAAM;QACN,cAAc;IAChB;CACD;AAGM,MAAM,eAAe;IAC1B;QACE,QAAQ;YACN,QACE;YACF,QAAQ;YACR,MAAM;QACR;QACA,MAAM;IACR;IACA;QACE,QAAQ;YACN,QACE;YACF,QAAQ;YACR,MAAM;QACR;QACA,MAAM;IACR;IACA;QACE,QAAQ;YACN,QACE;YACF,QAAQ;YACR,MAAM;QACR;QACA,MAAM;IACR;IACA;QACE,QAAQ;YACN,QACE;YACF,QAAQ;YACR,MAAM;QACR;QACA,MAAM;IACR;IACA;QACE,QAAQ;YACN,QACE;YACF,QAAQ;YACR,MAAM;QACR;QACA,MAAM;IACR;CACD", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/app/page.tsx"], "sourcesContent": ["import { <PERSON>R<PERSON>, Clock, ShoppingBag, Star, Truck } from \"lucide-react\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\n\nimport United24Banner from \"~/ui/components/banners/u24\";\nimport { HeroBadge } from \"~/ui/components/hero-badge\";\nimport { ProductCard } from \"~/ui/components/product-card\";\nimport { TestimonialsSection } from \"~/ui/components/testimonials/testimonials-with-marquee\";\nimport { Button } from \"~/ui/primitives/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"~/ui/primitives/card\";\n\nimport { categories, featuredProductsHomepage, testimonials } from \"./mocks\";\n\nconst featuresWhyChooseUs = [\n  {\n    description:\n      \"Free shipping on all orders over $50. Fast and reliable delivery to your doorstep.\",\n    icon: <Truck className=\"h-6 w-6 text-primary\" />,\n    title: \"Free Shipping\",\n  },\n  {\n    description:\n      \"Your payment information is always safe and secure with us. We use industry-leading encryption.\",\n    icon: <ShoppingBag className=\"h-6 w-6 text-primary\" />,\n    title: \"Secure Checkout\",\n  },\n  {\n    description:\n      \"Our customer support team is always available to help with any questions or concerns.\",\n    icon: <Clock className=\"h-6 w-6 text-primary\" />,\n    title: \"24/7 Support\",\n  },\n  {\n    description:\n      \"We stand behind the quality of every product we sell. 30-day money-back guarantee.\",\n    icon: <Star className=\"h-6 w-6 text-primary\" />,\n    title: \"Quality Guarantee\",\n  },\n];\n\nexport default function HomePage() {\n  return (\n    <>\n      <main\n        className={`\n          flex min-h-screen flex-col gap-y-16 bg-gradient-to-b from-muted/50\n          via-muted/25 to-background\n        `}\n      >\n        {/* Hero Section */}\n        <section\n          className={`\n            relative overflow-hidden py-24\n            md:py-32\n          `}\n        >\n          <div\n            className={`\n              bg-grid-black/[0.02] absolute inset-0\n              bg-[length:20px_20px]\n            `}\n          />\n          <div\n            className={`\n              relative z-10 container mx-auto max-w-7xl px-4\n              sm:px-6\n              lg:px-8\n            `}\n          >\n            <div\n              className={`\n                grid items-center gap-10\n                lg:grid-cols-2 lg:gap-12\n              `}\n            >\n              <div className=\"flex flex-col justify-center space-y-6\">\n                <div className=\"space-y-4\">\n                  <HeroBadge />\n\n                  <h1\n                    className={`\n                      font-display text-4xl leading-tight font-bold\n                      tracking-tight text-foreground\n                      sm:text-5xl\n                      md:text-6xl\n                      lg:leading-[1.1]\n                    `}\n                  >\n                    Your One-Stop Shop for{\" \"}\n                    <span\n                      className={`\n                        bg-gradient-to-r from-primary to-primary/70 bg-clip-text\n                        text-transparent\n                      `}\n                    >\n                      Everything Tech\n                    </span>\n                  </h1>\n                  <p\n                    className={`\n                      max-w-[700px] text-lg text-muted-foreground\n                      md:text-xl\n                    `}\n                  >\n                    Discover premium products at competitive prices, with fast\n                    shipping and exceptional customer service.\n                  </p>\n                </div>\n                <div\n                  className={`\n                    flex flex-col gap-3\n                    sm:flex-row\n                  `}\n                >\n                  <Link href=\"/products\">\n                    <Button\n                      className={`\n                        h-12 gap-1.5 px-8 transition-colors duration-200\n                      `}\n                      size=\"lg\"\n                    >\n                      Shop Now <ArrowRight className=\"h-4 w-4\" />\n                    </Button>\n                  </Link>\n                  <Link href=\"/showcase\">\n                    <Button\n                      className=\"h-12 px-8 transition-colors duration-200\"\n                      size=\"lg\"\n                      variant=\"outline\"\n                    >\n                      View Showcase\n                    </Button>\n                  </Link>\n                </div>\n                <div\n                  className={`\n                    flex flex-wrap gap-5 text-sm text-muted-foreground\n                  `}\n                >\n                  <div className=\"flex items-center gap-1.5\">\n                    <Truck className=\"h-5 w-5 text-primary/70\" />\n                    <span>Free shipping over $50</span>\n                  </div>\n                  <div className=\"flex items-center gap-1.5\">\n                    <Clock className=\"h-5 w-5 text-primary/70\" />\n                    <span>24/7 Customer Support</span>\n                  </div>\n                </div>\n              </div>\n              <div\n                className={`\n                  relative mx-auto hidden aspect-square w-full max-w-md\n                  overflow-hidden rounded-xl border shadow-lg\n                  lg:block\n                `}\n              >\n                <div\n                  className={`\n                    absolute inset-0 z-10 bg-gradient-to-tr from-primary/20\n                    via-transparent to-transparent\n                  `}\n                />\n                <Image\n                  alt=\"Shopping experience\"\n                  className=\"object-cover\"\n                  fill\n                  priority\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                  src=\"https://images.unsplash.com/photo-1624767735494-1929dc24ad43?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3\"\n                />\n              </div>\n            </div>\n          </div>\n          <div\n            className={`\n              absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent\n              via-primary/20 to-transparent\n            `}\n          />\n        </section>\n\n        {/* Featured Categories */}\n        <section\n          className={`\n            py-12\n            md:py-16\n          `}\n        >\n          <div\n            className={`\n              container mx-auto max-w-7xl px-4\n              sm:px-6\n              lg:px-8\n            `}\n          >\n            <div className=\"mb-8 flex flex-col items-center text-center\">\n              <h2\n                className={`\n                  font-display text-3xl leading-tight font-bold tracking-tight\n                  md:text-4xl\n                `}\n              >\n                Shop by Category\n              </h2>\n              <div className=\"mt-2 h-1 w-12 rounded-full bg-primary\" />\n              <p className=\"mt-4 max-w-2xl text-center text-muted-foreground\">\n                Find the perfect device for your needs from our curated\n                collections\n              </p>\n            </div>\n            <div\n              className={`\n                grid grid-cols-2 gap-4\n                md:grid-cols-4 md:gap-6\n              `}\n            >\n              {categories.map((category) => (\n                <Link\n                  aria-label={`Browse ${category.name} products`}\n                  className={`\n                    group relative flex flex-col space-y-4 overflow-hidden\n                    rounded-2xl border bg-card shadow transition-all\n                    duration-300\n                    hover:shadow-lg\n                  `}\n                  href={`/products?category=${category.name.toLowerCase()}`}\n                  key={category.name}\n                >\n                  <div className=\"relative aspect-[4/3] overflow-hidden\">\n                    <div\n                      className={`\n                        absolute inset-0 z-10 bg-gradient-to-t\n                        from-background/80 to-transparent\n                      `}\n                    />\n                    <Image\n                      alt={category.name}\n                      className={`\n                        object-cover transition duration-300\n                        group-hover:scale-105\n                      `}\n                      fill\n                      sizes=\"(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 20vw\"\n                      src={category.image}\n                    />\n                  </div>\n                  <div className=\"relative z-20 -mt-6 p-4\">\n                    <div className=\"mb-1 text-lg font-medium\">\n                      {category.name}\n                    </div>\n                    <p className=\"text-sm text-muted-foreground\">\n                      {category.productCount} products\n                    </p>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Featured Products */}\n        <section\n          className={`\n            bg-muted/50 py-12\n            md:py-16\n          `}\n        >\n          <div\n            className={`\n              container mx-auto max-w-7xl px-4\n              sm:px-6\n              lg:px-8\n            `}\n          >\n            <div className=\"mb-8 flex flex-col items-center text-center\">\n              <h2\n                className={`\n                  font-display text-3xl leading-tight font-bold tracking-tight\n                  md:text-4xl\n                `}\n              >\n                Featured Products\n              </h2>\n              <div className=\"mt-2 h-1 w-12 rounded-full bg-primary\" />\n              <p className=\"mt-4 max-w-2xl text-center text-muted-foreground\">\n                Check out our latest and most popular tech items\n              </p>\n            </div>\n            <div\n              className={`\n                grid grid-cols-1 gap-6\n                sm:grid-cols-2\n                lg:grid-cols-3\n                xl:grid-cols-4\n              `}\n            >\n              {featuredProductsHomepage.map((product) => (\n                <ProductCard key={product.id} product={product} />\n              ))}\n            </div>\n            <div className=\"mt-10 flex justify-center\">\n              <Link href=\"/products\">\n                <Button className=\"group h-12 px-8\" size=\"lg\" variant=\"outline\">\n                  View All Products\n                  <ArrowRight\n                    className={`\n                      ml-2 h-4 w-4 transition-transform duration-300\n                      group-hover:translate-x-1\n                    `}\n                  />\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* Features Section */}\n        <section\n          className={`\n            py-12\n            md:py-16\n          `}\n          id=\"features\"\n        >\n          <div\n            className={`\n              container mx-auto max-w-7xl px-4\n              sm:px-6\n              lg:px-8\n            `}\n          >\n            <div className=\"mb-8 flex flex-col items-center text-center\">\n              <h2\n                className={`\n                  font-display text-3xl leading-tight font-bold tracking-tight\n                  md:text-4xl\n                `}\n              >\n                Why Choose Us\n              </h2>\n              <div className=\"mt-2 h-1 w-12 rounded-full bg-primary\" />\n              <p\n                className={`\n                  mt-4 max-w-2xl text-center text-muted-foreground\n                  md:text-lg\n                `}\n              >\n                We offer the best shopping experience with premium features\n              </p>\n            </div>\n            <div\n              className={`\n                grid gap-8\n                md:grid-cols-2\n                lg:grid-cols-4\n              `}\n            >\n              {featuresWhyChooseUs.map((feature) => (\n                <Card\n                  className={`\n                    rounded-2xl border-none bg-background shadow transition-all\n                    duration-300\n                    hover:shadow-lg\n                  `}\n                  key={feature.title}\n                >\n                  <CardHeader className=\"pb-2\">\n                    <div\n                      className={`\n                        mb-3 flex h-12 w-12 items-center justify-center\n                        rounded-full bg-primary/10\n                      `}\n                    >\n                      {feature.icon}\n                    </div>\n                    <CardTitle>{feature.title}</CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-base\">\n                      {feature.description}\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Testimonials */}\n        <section\n          className={`\n            bg-muted/50 py-12\n            md:py-16\n          `}\n        >\n          <div\n            className={`\n              container mx-auto max-w-7xl px-4\n              sm:px-6\n              lg:px-8\n            `}\n          >\n            <TestimonialsSection\n              className=\"py-0\"\n              description=\"Don't just take our word for it - hear from our satisfied customers\"\n              testimonials={testimonials}\n              title=\"What Our Customers Say\"\n            />\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section\n          className={`\n            py-12\n            md:py-16\n          `}\n        >\n          <div\n            className={`\n              container mx-auto max-w-7xl px-4\n              sm:px-6\n              lg:px-8\n            `}\n          >\n            <div\n              className={`\n                relative overflow-hidden rounded-xl bg-primary/10 p-8 shadow-lg\n                md:p-12\n              `}\n            >\n              <div\n                className={`\n                  bg-grid-white/[0.05] absolute inset-0\n                  bg-[length:16px_16px]\n                `}\n              />\n              <div className=\"relative z-10 mx-auto max-w-2xl text-center\">\n                <h2\n                  className={`\n                    font-display text-3xl leading-tight font-bold tracking-tight\n                    md:text-4xl\n                  `}\n                >\n                  Ready to Upgrade Your Tech?\n                </h2>\n                <p\n                  className={`\n                    mt-4 text-lg text-muted-foreground\n                    md:text-xl\n                  `}\n                >\n                  Join thousands of satisfied customers and experience the best\n                  tech products on the market. Sign up today for exclusive deals\n                  and offers.\n                </p>\n                <div\n                  className={`\n                    mt-6 flex flex-col items-center justify-center gap-3\n                    sm:flex-row\n                  `}\n                >\n                  <Link href=\"/auth/sign-up\">\n                    <Button\n                      className=\"h-12 px-8 transition-colors duration-200\"\n                      size=\"lg\"\n                    >\n                      Sign Up Now\n                    </Button>\n                  </Link>\n                  <Link href=\"/products\">\n                    <Button\n                      className=\"h-12 px-8 transition-colors duration-200\"\n                      size=\"lg\"\n                      variant=\"outline\"\n                    >\n                      Browse Products\n                    </Button>\n                  </Link>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Sample banner */}\n        <United24Banner animateGradient={false} />\n      </main>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAQA;;;;;;;;;;;;AAEA,MAAM,sBAAsB;IAC1B;QACE,aACE;QACF,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvB,OAAO;IACT;IACA;QACE,aACE;QACF,oBAAM,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC7B,OAAO;IACT;IACA;QACE,aACE;QACF,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvB,OAAO;IACT;IACA;QACE,aACE;QACF,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE;kBACE,cAAA,8OAAC;YACC,WAAW,CAAC;;;QAGZ,CAAC;;8BAGD,8OAAC;oBACC,WAAW,CAAC;;;UAGZ,CAAC;;sCAED,8OAAC;4BACC,WAAW,CAAC;;;YAGZ,CAAC;;;;;;sCAEH,8OAAC;4BACC,WAAW,CAAC;;;;YAIZ,CAAC;sCAED,cAAA,8OAAC;gCACC,WAAW,CAAC;;;cAGZ,CAAC;;kDAED,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,yIAAA,CAAA,YAAS;;;;;kEAEV,8OAAC;wDACC,WAAW,CAAC;;;;;;oBAMZ,CAAC;;4DACF;4DACwB;0EACvB,8OAAC;gEACC,WAAW,CAAC;;;sBAGZ,CAAC;0EACF;;;;;;;;;;;;kEAIH,8OAAC;wDACC,WAAW,CAAC;;;oBAGZ,CAAC;kEACF;;;;;;;;;;;;0DAKH,8OAAC;gDACC,WAAW,CAAC;;;kBAGZ,CAAC;;kEAED,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,WAAW,CAAC;;sBAEZ,CAAC;4DACD,MAAK;;gEACN;8EACU,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAGnC,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,WAAU;4DACV,MAAK;4DACL,SAAQ;sEACT;;;;;;;;;;;;;;;;;0DAKL,8OAAC;gDACC,WAAW,CAAC;;kBAEZ,CAAC;;kEAED,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAIZ,8OAAC;wCACC,WAAW,CAAC;;;;gBAIZ,CAAC;;0DAED,8OAAC;gDACC,WAAW,CAAC;;;kBAGZ,CAAC;;;;;;0DAEH,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,WAAU;gDACV,IAAI;gDACJ,QAAQ;gDACR,OAAM;gDACN,KAAI;;;;;;;;;;;;;;;;;;;;;;;sCAKZ,8OAAC;4BACC,WAAW,CAAC;;;YAGZ,CAAC;;;;;;;;;;;;8BAKL,8OAAC;oBACC,WAAW,CAAC;;;UAGZ,CAAC;8BAED,cAAA,8OAAC;wBACC,WAAW,CAAC;;;;YAIZ,CAAC;;0CAED,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAW,CAAC;;;gBAGZ,CAAC;kDACF;;;;;;kDAGD,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAmD;;;;;;;;;;;;0CAKlE,8OAAC;gCACC,WAAW,CAAC;;;cAGZ,CAAC;0CAEA,mHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,yBACf,8OAAC,4JAAA,CAAA,UAAI;wCACH,cAAY,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,SAAS,CAAC;wCAC9C,WAAW,CAAC;;;;;kBAKZ,CAAC;wCACD,MAAM,CAAC,mBAAmB,EAAE,SAAS,IAAI,CAAC,WAAW,IAAI;;0DAGzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAW,CAAC;;;sBAGZ,CAAC;;;;;;kEAEH,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,SAAS,IAAI;wDAClB,WAAW,CAAC;;;sBAGZ,CAAC;wDACD,IAAI;wDACJ,OAAM;wDACN,KAAK,SAAS,KAAK;;;;;;;;;;;;0DAGvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,SAAS,IAAI;;;;;;kEAEhB,8OAAC;wDAAE,WAAU;;4DACV,SAAS,YAAY;4DAAC;;;;;;;;;;;;;;uCAzBtB,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;8BAmC5B,8OAAC;oBACC,WAAW,CAAC;;;UAGZ,CAAC;8BAED,cAAA,8OAAC;wBACC,WAAW,CAAC;;;;YAIZ,CAAC;;0CAED,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAW,CAAC;;;gBAGZ,CAAC;kDACF;;;;;;kDAGD,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAmD;;;;;;;;;;;;0CAIlE,8OAAC;gCACC,WAAW,CAAC;;;;;cAKZ,CAAC;0CAEA,mHAAA,CAAA,2BAAwB,CAAC,GAAG,CAAC,CAAC,wBAC7B,8OAAC,2IAAA,CAAA,cAAW;wCAAkB,SAAS;uCAArB,QAAQ,EAAE;;;;;;;;;;0CAGhC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;wCAAkB,MAAK;wCAAK,SAAQ;;4CAAU;0DAE9D,8OAAC,kNAAA,CAAA,aAAU;gDACT,WAAW,CAAC;;;oBAGZ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASb,8OAAC;oBACC,WAAW,CAAC;;;UAGZ,CAAC;oBACD,IAAG;8BAEH,cAAA,8OAAC;wBACC,WAAW,CAAC;;;;YAIZ,CAAC;;0CAED,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAW,CAAC;;;gBAGZ,CAAC;kDACF;;;;;;kDAGD,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCACC,WAAW,CAAC;;;gBAGZ,CAAC;kDACF;;;;;;;;;;;;0CAIH,8OAAC;gCACC,WAAW,CAAC;;;;cAIZ,CAAC;0CAEA,oBAAoB,GAAG,CAAC,CAAC,wBACxB,8OAAC,gIAAA,CAAA,OAAI;wCACH,WAAW,CAAC;;;;kBAIZ,CAAC;;0DAGD,8OAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,8OAAC;wDACC,WAAW,CAAC;;;sBAGZ,CAAC;kEAEA,QAAQ,IAAI;;;;;;kEAEf,8OAAC,gIAAA,CAAA,YAAS;kEAAE,QAAQ,KAAK;;;;;;;;;;;;0DAE3B,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,QAAQ,WAAW;;;;;;;;;;;;uCAfnB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;8BAyB5B,8OAAC;oBACC,WAAW,CAAC;;;UAGZ,CAAC;8BAED,cAAA,8OAAC;wBACC,WAAW,CAAC;;;;YAIZ,CAAC;kCAED,cAAA,8OAAC,2KAAA,CAAA,sBAAmB;4BAClB,WAAU;4BACV,aAAY;4BACZ,cAAc,mHAAA,CAAA,eAAY;4BAC1B,OAAM;;;;;;;;;;;;;;;;8BAMZ,8OAAC;oBACC,WAAW,CAAC;;;UAGZ,CAAC;8BAED,cAAA,8OAAC;wBACC,WAAW,CAAC;;;;YAIZ,CAAC;kCAED,cAAA,8OAAC;4BACC,WAAW,CAAC;;;cAGZ,CAAC;;8CAED,8OAAC;oCACC,WAAW,CAAC;;;gBAGZ,CAAC;;;;;;8CAEH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAW,CAAC;;;kBAGZ,CAAC;sDACF;;;;;;sDAGD,8OAAC;4CACC,WAAW,CAAC;;;kBAGZ,CAAC;sDACF;;;;;;sDAKD,8OAAC;4CACC,WAAW,CAAC;;;kBAGZ,CAAC;;8DAED,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,WAAU;wDACV,MAAK;kEACN;;;;;;;;;;;8DAIH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,WAAU;wDACV,MAAK;wDACL,SAAQ;kEACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWb,8OAAC,0IAAA,CAAA,UAAc;oBAAC,iBAAiB;;;;;;;;;;;;;AAIzC", "debugId": null}}]}