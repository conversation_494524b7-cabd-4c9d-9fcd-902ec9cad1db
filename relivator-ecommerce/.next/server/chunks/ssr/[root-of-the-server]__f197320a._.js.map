{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/lib/hooks/use-cart.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\n\nimport type { CartItem } from \"~/ui/components/cart\";\n\n/* -------------------------------------------------------------------------- */\n/*                                   Types                                    */\n/* -------------------------------------------------------------------------- */\n\nexport interface CartContextType {\n  addItem: (item: Omit<CartItem, \"quantity\">, quantity?: number) => void;\n  clearCart: () => void;\n  itemCount: number;\n  items: CartItem[];\n  removeItem: (id: string) => void;\n  subtotal: number;\n  updateQuantity: (id: string, quantity: number) => void;\n}\n\n/* -------------------------------------------------------------------------- */\n/*                                Context                                     */\n/* -------------------------------------------------------------------------- */\n\nconst CartContext = React.createContext<CartContextType | undefined>(undefined);\n\n/* -------------------------------------------------------------------------- */\n/*                         Local-storage helpers                              */\n/* -------------------------------------------------------------------------- */\n\nconst STORAGE_KEY = \"cart\";\nconst DEBOUNCE_MS = 500;\n\nconst loadCartFromStorage = (): CartItem[] => {\n  if (typeof window === \"undefined\") return [];\n  try {\n    const raw = localStorage.getItem(STORAGE_KEY);\n    if (!raw) return [];\n    const parsed = JSON.parse(raw) as unknown;\n    if (Array.isArray(parsed)) {\n      return parsed as CartItem[];\n    }\n  } catch (err) {\n    console.error(\"Failed to load cart:\", err);\n  }\n  return [];\n};\n\n/* -------------------------------------------------------------------------- */\n/*                               Provider                                     */\n/* -------------------------------------------------------------------------- */\n\nexport function CartProvider({ children }: React.PropsWithChildren) {\n  const [items, setItems] = React.useState<CartItem[]>(loadCartFromStorage);\n\n  /* -------------------- Persist to localStorage (debounced) ------------- */\n  const saveTimeout = React.useRef<null | ReturnType<typeof setTimeout>>(null);\n\n  React.useEffect(() => {\n    if (saveTimeout.current) clearTimeout(saveTimeout.current);\n    saveTimeout.current = setTimeout(() => {\n      try {\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(items));\n      } catch (err) {\n        console.error(\"Failed to save cart:\", err);\n      }\n    }, DEBOUNCE_MS);\n\n    return () => {\n      if (saveTimeout.current) clearTimeout(saveTimeout.current);\n    };\n  }, [items]);\n\n  /* ----------------------------- Actions -------------------------------- */\n  const addItem = React.useCallback(\n    (newItem: Omit<CartItem, \"quantity\">, qty = 1) => {\n      if (qty <= 0) return;\n      setItems((prev) => {\n        const existing = prev.find((i) => i.id === newItem.id);\n        if (existing) {\n          return prev.map((i) =>\n            i.id === newItem.id ? { ...i, quantity: i.quantity + qty } : i,\n          );\n        }\n        return [...prev, { ...newItem, quantity: qty }];\n      });\n    },\n    [],\n  );\n\n  const removeItem = React.useCallback((id: string) => {\n    setItems((prev) => prev.filter((i) => i.id !== id));\n  }, []);\n\n  const updateQuantity = React.useCallback((id: string, qty: number) => {\n    setItems((prev) =>\n      prev.flatMap((i) => {\n        if (i.id !== id) return i;\n        if (qty <= 0) return []; // treat zero/negative as remove\n        if (qty === i.quantity) return i;\n        return { ...i, quantity: qty };\n      }),\n    );\n  }, []);\n\n  const clearCart = React.useCallback(() => setItems([]), []);\n\n  /* --------------------------- Derived data ----------------------------- */\n  const itemCount = React.useMemo(\n    () => items.reduce((t, i) => t + i.quantity, 0),\n    [items],\n  );\n\n  const subtotal = React.useMemo(\n    () => items.reduce((t, i) => t + i.price * i.quantity, 0),\n    [items],\n  );\n\n  /* ----------------------------- Context value -------------------------- */\n  const value = React.useMemo<CartContextType>(\n    () => ({\n      addItem,\n      clearCart,\n      itemCount,\n      items,\n      removeItem,\n      subtotal,\n      updateQuantity,\n    }),\n    [\n      items,\n      addItem,\n      removeItem,\n      updateQuantity,\n      clearCart,\n      itemCount,\n      subtotal,\n    ],\n  );\n\n  return <CartContext value={value}>{children}</CartContext>;\n}\n\n/* -------------------------------------------------------------------------- */\n/*                                 Hook                                      */\n/* -------------------------------------------------------------------------- */\n\nexport function useCart(): CartContextType {\n  const ctx = React.use(CartContext);\n  if (!ctx) throw new Error(\"useCart must be used within a CartProvider\");\n  return ctx;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAoBA,8EAA8E,GAC9E,8EAA8E,GAC9E,8EAA8E,GAE9E,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA+B;AAErE,8EAA8E,GAC9E,8EAA8E,GAC9E,8EAA8E,GAE9E,MAAM,cAAc;AACpB,MAAM,cAAc;AAEpB,MAAM,sBAAsB;IAC1B,wCAAmC,OAAO,EAAE;;AAY9C;AAMO,SAAS,aAAa,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAc;IAErD,0EAA0E,GAC1E,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAwC;IAEvE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,YAAY,OAAO,EAAE,aAAa,YAAY,OAAO;QACzD,YAAY,OAAO,GAAG,WAAW;YAC/B,IAAI;gBACF,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACnD,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF,GAAG;QAEH,OAAO;YACL,IAAI,YAAY,OAAO,EAAE,aAAa,YAAY,OAAO;QAC3D;IACF,GAAG;QAAC;KAAM;IAEV,0EAA0E,GAC1E,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC,SAAqC,MAAM,CAAC;QAC3C,IAAI,OAAO,GAAG;QACd,SAAS,CAAC;YACR,MAAM,WAAW,KAAK,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,EAAE;YACrD,IAAI,UAAU;gBACZ,OAAO,KAAK,GAAG,CAAC,CAAC,IACf,EAAE,EAAE,KAAK,QAAQ,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,UAAU,EAAE,QAAQ,GAAG;oBAAI,IAAI;YAEjE;YACA,OAAO;mBAAI;gBAAM;oBAAE,GAAG,OAAO;oBAAE,UAAU;gBAAI;aAAE;QACjD;IACF,GACA,EAAE;IAGJ,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC;QACpC,SAAS,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACjD,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC,IAAY;QACpD,SAAS,CAAC,OACR,KAAK,OAAO,CAAC,CAAC;gBACZ,IAAI,EAAE,EAAE,KAAK,IAAI,OAAO;gBACxB,IAAI,OAAO,GAAG,OAAO,EAAE,EAAE,gCAAgC;gBACzD,IAAI,QAAQ,EAAE,QAAQ,EAAE,OAAO;gBAC/B,OAAO;oBAAE,GAAG,CAAC;oBAAE,UAAU;gBAAI;YAC/B;IAEJ,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,IAAM,SAAS,EAAE,GAAG,EAAE;IAE1D,0EAA0E,GAC1E,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC5B,IAAM,MAAM,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,EAAE,QAAQ,EAAE,IAC7C;QAAC;KAAM;IAGT,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC3B,IAAM,MAAM,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,EAAE,KAAK,GAAG,EAAE,QAAQ,EAAE,IACvD;QAAC;KAAM;IAGT,0EAA0E,GAC1E,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EACxB,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QACE;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,qBAAO,8OAAC;QAAY,OAAO;kBAAQ;;;;;;AACrC;AAMO,SAAS;IACd,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,MAAS,AAAD,EAAE;IACtB,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM;IAC1B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/app.ts"], "sourcesContent": ["export const SEO_CONFIG = {\n  description:\n    \"Relivator is a robust ecommerce template built with next.js and other modern technologies. It's designed for developers who want a fast, modern, and scalable foundation without reinventing the backend.\",\n  fullName: \"Relivator Next.js Template\",\n  name: \"Relivator\",\n  slogan: \"Store which makes you happy.\",\n};\n\nexport const SYSTEM_CONFIG = {\n  redirectAfterSignIn: \"/dashboard/uploads\",\n  redirectAfterSignUp: \"/dashboard/uploads\",\n  repoName: \"relivator\",\n  repoOwner: \"blefnk\",\n  repoStars: true,\n};\n\nexport const ADMIN_CONFIG = {\n  displayEmails: false,\n};\n\nexport const DB_DEV_LOGGER = false;\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,aAAa;IACxB,aACE;IACF,UAAU;IACV,MAAM;IACN,QAAQ;AACV;AAEO,MAAM,gBAAgB;IAC3B,qBAAqB;IACrB,qBAAqB;IACrB,UAAU;IACV,WAAW;IACX,WAAW;AACb;AAEO,MAAM,eAAe;IAC1B,eAAe;AACjB;AAEO,MAAM,gBAAgB", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/lib/auth-client.ts"], "sourcesContent": ["import { twoFactorClient } from \"better-auth/client/plugins\";\nimport { createAuth<PERSON>lient } from \"better-auth/react\";\nimport { useRouter } from \"next/navigation\";\nimport { useEffect } from \"react\";\n\n// Create and export the auth client\nexport const authClient = createAuthClient({\n  baseURL: process.env.NEXT_PUBLIC_APP_URL,\n  plugins: [\n    twoFactorClient({\n      onTwoFactorRedirect: () => {\n        // Redirect to the two-factor page\n        window.location.href = \"/auth/two-factor\";\n      },\n    }),\n  ],\n});\n\n// Auth methods\nexport const { signIn, signOut, signUp, useSession } = authClient;\n\n// Two-factor methods\nexport const twoFactor = authClient.twoFactor;\n\n// Hook to get current user data and loading state\n// !! Returns only raw (static) data, use getCurrentUserOrRedirect for data from db\nexport const useCurrentUser = () => {\n  const { data, isPending } = useSession();\n  return {\n    isPending,\n    session: data?.session,\n    user: data?.user,\n  };\n};\n\n// Hook similar to getCurrentUserOrRedirect for client-side use\n// !! Returns only raw (static) data, use getCurrentUserOrRedirect for data from db\nexport const useCurrentUserOrRedirect = (\n  forbiddenUrl = \"/auth/sign-in\",\n  okUrl = \"\",\n  ignoreForbidden = false,\n) => {\n  const { data, isPending } = useSession();\n  const router = useRouter();\n\n  useEffect(() => {\n    // only perform redirects after loading is complete and router is ready\n    if (!isPending && router) {\n      // if no user is found\n      if (!data?.user) {\n        // redirect to forbidden url unless explicitly ignored\n        if (!ignoreForbidden) {\n          router.push(forbiddenUrl);\n        }\n        // if ignoreforbidden is true, we do nothing and let the hook return the null user\n      } else if (okUrl) {\n        // if user is found and an okurl is provided, redirect there\n        router.push(okUrl);\n      }\n    }\n    // depend on loading state, user data, router instance, and redirect urls\n  }, [isPending, data?.user, router, forbiddenUrl, okUrl, ignoreForbidden]);\n\n  return {\n    isPending,\n    session: data?.session,\n    user: data?.user,\n  };\n};\n\n// !! currently not used in the app\n/**\n * returns the raw session object from better-auth client.\n * this is a direct wrapper around authclient.getsession and returns the same shape.\n *\n * use this when you require advanced session access patterns, e.g.:\n * - you need to fetch the session manually (e.g., with swr, react query, or custom logic).\n * - you need to access the session data directly without using the usesession hook.\n * - you want more control than the usesession hook provides.\n *\n * @example\n * const { data, error } = await useRawSession();\n */\n// export const useRawSession = authClient.getSession;\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;AACA;AACA;AACA;;;;;AAGO,MAAM,aAAa,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,OAAO;IACP,SAAS;QACP,CAAA,GAAA,uNAAA,CAAA,kBAAe,AAAD,EAAE;YACd,qBAAqB;gBACnB,kCAAkC;gBAClC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;KACD;AACH;AAGO,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG;AAGhD,MAAM,YAAY,WAAW,SAAS;AAItC,MAAM,iBAAiB;IAC5B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG;IAC5B,OAAO;QACL;QACA,SAAS,MAAM;QACf,MAAM,MAAM;IACd;AACF;AAIO,MAAM,2BAA2B,CACtC,eAAe,eAAe,EAC9B,QAAQ,EAAE,EACV,kBAAkB,KAAK;IAEvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG;IAC5B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uEAAuE;QACvE,IAAI,CAAC,aAAa,QAAQ;YACxB,sBAAsB;YACtB,IAAI,CAAC,MAAM,MAAM;gBACf,sDAAsD;gBACtD,IAAI,CAAC,iBAAiB;oBACpB,OAAO,IAAI,CAAC;gBACd;YACA,kFAAkF;YACpF,OAAO,IAAI,OAAO;gBAChB,4DAA4D;gBAC5D,OAAO,IAAI,CAAC;YACd;QACF;IACA,yEAAyE;IAC3E,GAAG;QAAC;QAAW,MAAM;QAAM;QAAQ;QAAc;QAAO;KAAgB;IAExE,OAAO;QACL;QACA,SAAS,MAAM;QACf,MAAM,MAAM;IACd;AACF,GAEA,mCAAmC;CACnC;;;;;;;;;;;CAWC,IACD,sDAAsD", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/lib/cn.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/lib/hooks/use-media-query.ts"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\n\nexport function useMediaQuery(query: string): boolean {\n  // Start with false during SSR\n  const [matches, setMatches] = useState(false);\n\n  useEffect(() => {\n    // Update the state on client-side\n    setMatches(window.matchMedia(query).matches);\n\n    const media = window.matchMedia(query);\n    const listener = () => setMatches(media.matches);\n\n    media.addEventListener(\"change\", listener);\n    return () => media.removeEventListener(\"change\", listener);\n  }, [query]);\n\n  return matches;\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIO,SAAS,cAAc,KAAa;IACzC,8BAA8B;IAC9B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,WAAW,OAAO,UAAU,CAAC,OAAO,OAAO;QAE3C,MAAM,QAAQ,OAAO,UAAU,CAAC;QAChC,MAAM,WAAW,IAAM,WAAW,MAAM,OAAO;QAE/C,MAAM,gBAAgB,CAAC,UAAU;QACjC,OAAO,IAAM,MAAM,mBAAmB,CAAC,UAAU;IACnD,GAAG;QAAC;KAAM;IAEV,OAAO;AACT", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/badge.tsx"], "sourcesContent": ["import type * as React from \"react\";\n\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"~/lib/cn\";\n\nconst badgeVariants = cva(\n  `\n    inline-flex w-fit shrink-0 items-center justify-center gap-1 overflow-hidden\n    rounded-md border px-2 py-0.5 text-xs font-medium whitespace-nowrap\n    transition-[color,box-shadow]\n    focus-visible:border-ring focus-visible:ring-[3px]\n    focus-visible:ring-ring/50\n    aria-invalid:border-destructive aria-invalid:ring-destructive/20\n    dark:aria-invalid:ring-destructive/40\n    [&>svg]:pointer-events-none [&>svg]:size-3\n  `,\n  {\n    defaultVariants: {\n      variant: \"default\",\n    },\n    variants: {\n      variant: {\n        default: `\n          border-transparent bg-primary text-primary-foreground\n          [a&]:hover:bg-primary/90\n        `,\n        destructive: `\n          border-transparent bg-destructive text-white\n          focus-visible:ring-destructive/20\n          dark:bg-destructive/60 dark:focus-visible:ring-destructive/40\n          [a&]:hover:bg-destructive/90\n        `,\n        outline: `\n          text-foreground\n          [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\n        `,\n        secondary: `\n          border-transparent bg-secondary text-secondary-foreground\n          [a&]:hover:bg-secondary/90\n        `,\n      },\n    },\n  },\n);\n\nfunction Badge({\n  asChild = false,\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\";\n\n  return (\n    <Comp\n      className={cn(badgeVariants({ variant }), className)}\n      data-slot=\"badge\"\n      {...props}\n    />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,CAAC;;;;;;;;;EASD,CAAC,EACD;IACE,iBAAiB;QACf,SAAS;IACX;IACA,UAAU;QACR,SAAS;YACP,SAAS,CAAC;;;QAGV,CAAC;YACD,aAAa,CAAC;;;;;QAKd,CAAC;YACD,SAAS,CAAC;;;QAGV,CAAC;YACD,WAAW,CAAC;;;QAGZ,CAAC;QACH;IACF;AACF;AAGF,SAAS,MAAM,EACb,UAAU,KAAK,EACf,SAAS,EACT,OAAO,EACP,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAC1C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/button.tsx"], "sourcesContent": ["import type * as React from \"react\";\n\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"~/lib/cn\";\n\nconst buttonVariants = cva(\n  `\n    inline-flex shrink-0 items-center justify-center gap-2 rounded-md text-sm\n    font-medium whitespace-nowrap shadow-sm transition-all duration-200\n    ease-in-out outline-none\n    hover:shadow-md\n    focus:shadow-lg\n    focus-visible:border-ring focus-visible:ring-2 focus-visible:ring-ring/60\n    active:shadow\n    disabled:pointer-events-none disabled:opacity-50\n    aria-invalid:border-destructive aria-invalid:ring-destructive/20\n    dark:aria-invalid:ring-destructive/40\n    [&_svg]:pointer-events-none [&_svg]:shrink-0\n    [&_svg:not([class*='size-'])]:size-4\n  `,\n  {\n    defaultVariants: {\n      size: \"default\",\n      variant: \"default\",\n    },\n    variants: {\n      size: {\n        default: `\n          h-9 px-4 py-2\n          has-[>svg]:px-3\n        `,\n        icon: \"size-9\",\n        lg: `\n          h-10 rounded-md px-6\n          has-[>svg]:px-4\n        `,\n        sm: `\n          h-8 gap-1.5 rounded-md px-3\n          has-[>svg]:px-2.5\n        `,\n      },\n      variant: {\n        default: `\n          bg-primary text-primary-foreground shadow-xs\n          hover:bg-primary/90 hover:shadow-md\n          focus-visible:ring-2 focus-visible:ring-primary/60\n        `,\n        destructive: `\n          bg-destructive text-white shadow-xs\n          hover:bg-destructive/90 hover:shadow-md\n          focus-visible:ring-2 focus-visible:ring-destructive/40\n          dark:bg-destructive/60 dark:focus-visible:ring-destructive/40\n        `,\n        ghost: `\n          hover:bg-accent hover:text-accent-foreground\n          focus-visible:ring-2 focus-visible:ring-accent/40\n          dark:hover:bg-accent/50\n        `,\n        link: `\n          text-primary underline-offset-4\n          hover:underline\n          focus-visible:ring-2 focus-visible:ring-primary/40\n        `,\n        outline: `\n          border bg-background shadow-xs\n          hover:bg-accent hover:text-accent-foreground hover:shadow-md\n          focus-visible:ring-2 focus-visible:ring-accent/40\n          dark:border-input dark:bg-input/30 dark:hover:bg-input/50\n        `,\n        secondary: `\n          bg-secondary text-secondary-foreground shadow-xs\n          hover:bg-secondary/80 hover:shadow-md\n          focus-visible:ring-2 focus-visible:ring-secondary/40\n        `,\n      },\n    },\n  },\n);\n\nfunction Button({\n  asChild = false,\n  className,\n  size,\n  variant,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      className={cn(buttonVariants({ className, size, variant }))}\n      data-slot=\"button\"\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,CAAC;;;;;;;;;;;;;EAaD,CAAC,EACD;IACE,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,UAAU;QACR,MAAM;YACJ,SAAS,CAAC;;;QAGV,CAAC;YACD,MAAM;YACN,IAAI,CAAC;;;QAGL,CAAC;YACD,IAAI,CAAC;;;QAGL,CAAC;QACH;QACA,SAAS;YACP,SAAS,CAAC;;;;QAIV,CAAC;YACD,aAAa,CAAC;;;;;QAKd,CAAC;YACD,OAAO,CAAC;;;;QAIR,CAAC;YACD,MAAM,CAAC;;;;QAIP,CAAC;YACD,SAAS,CAAC;;;;;QAKV,CAAC;YACD,WAAW,CAAC;;;;QAIZ,CAAC;QACH;IACF;AACF;AAGF,SAAS,OAAO,EACd,UAAU,KAAK,EACf,SAAS,EACT,IAAI,EACJ,OAAO,EACP,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAW;YAAM;QAAQ;QACxD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/drawer.tsx"], "sourcesContent": ["\"use client\";\n\nimport type * as React from \"react\";\n\nimport { Drawer as DrawerPrimitive } from \"vaul\";\n\nimport { cn } from \"~/lib/cn\";\n\nfunction Drawer({\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Root>) {\n  return <DrawerPrimitive.Root data-slot=\"drawer\" {...props} />;\n}\n\nfunction DrawerClose({\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Close>) {\n  return <DrawerPrimitive.Close data-slot=\"drawer-close\" {...props} />;\n}\n\nfunction DrawerContent({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Content>) {\n  return (\n    <DrawerPortal data-slot=\"drawer-portal\">\n      <DrawerOverlay />\n      <DrawerPrimitive.Content\n        className={cn(\n          \"group/drawer-content fixed z-50 flex h-auto flex-col bg-background\",\n          `\n            data-[vaul-drawer-direction=top]:inset-x-0\n            data-[vaul-drawer-direction=top]:top-0\n            data-[vaul-drawer-direction=top]:mb-24\n            data-[vaul-drawer-direction=top]:max-h-[80vh]\n            data-[vaul-drawer-direction=top]:rounded-b-lg\n            data-[vaul-drawer-direction=top]:border-b\n          `,\n          `\n            data-[vaul-drawer-direction=bottom]:inset-x-0\n            data-[vaul-drawer-direction=bottom]:bottom-0\n            data-[vaul-drawer-direction=bottom]:mt-24\n            data-[vaul-drawer-direction=bottom]:max-h-[80vh]\n            data-[vaul-drawer-direction=bottom]:rounded-t-lg\n            data-[vaul-drawer-direction=bottom]:border-t\n          `,\n          `\n            data-[vaul-drawer-direction=right]:inset-y-0\n            data-[vaul-drawer-direction=right]:right-0\n            data-[vaul-drawer-direction=right]:w-3/4\n            data-[vaul-drawer-direction=right]:border-l\n            data-[vaul-drawer-direction=right]:sm:max-w-sm\n          `,\n          `\n            data-[vaul-drawer-direction=left]:inset-y-0\n            data-[vaul-drawer-direction=left]:left-0\n            data-[vaul-drawer-direction=left]:w-3/4\n            data-[vaul-drawer-direction=left]:border-r\n            data-[vaul-drawer-direction=left]:sm:max-w-sm\n          `,\n          className,\n        )}\n        data-slot=\"drawer-content\"\n        {...props}\n      >\n        <div\n          className={`\n            mx-auto mt-4 hidden h-2 w-[100px] shrink-0 rounded-full bg-muted\n            group-data-[vaul-drawer-direction=bottom]/drawer-content:block\n          `}\n        />\n        {children}\n      </DrawerPrimitive.Content>\n    </DrawerPortal>\n  );\n}\n\nfunction DrawerDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Description>) {\n  return (\n    <DrawerPrimitive.Description\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      data-slot=\"drawer-description\"\n      {...props}\n    />\n  );\n}\n\nfunction DrawerFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      data-slot=\"drawer-footer\"\n      {...props}\n    />\n  );\n}\n\nfunction DrawerHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      data-slot=\"drawer-header\"\n      {...props}\n    />\n  );\n}\n\nfunction DrawerOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Overlay>) {\n  return (\n    <DrawerPrimitive.Overlay\n      className={cn(\n        `\n          fixed inset-0 z-50 bg-black/50\n          data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n          data-[state=open]:animate-in data-[state=open]:fade-in-0\n        `,\n        className,\n      )}\n      data-slot=\"drawer-overlay\"\n      {...props}\n    />\n  );\n}\n\nfunction DrawerPortal({\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Portal>) {\n  return <DrawerPrimitive.Portal data-slot=\"drawer-portal\" {...props} />;\n}\n\nfunction DrawerTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Title>) {\n  return (\n    <DrawerPrimitive.Title\n      className={cn(\"font-semibold text-foreground\", className)}\n      data-slot=\"drawer-title\"\n      {...props}\n    />\n  );\n}\n\nfunction DrawerTrigger({\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Trigger>) {\n  return <DrawerPrimitive.Trigger data-slot=\"drawer-trigger\" {...props} />;\n}\n\nexport {\n  Drawer,\n  DrawerClose,\n  DrawerContent,\n  DrawerDescription,\n  DrawerFooter,\n  DrawerHeader,\n  DrawerOverlay,\n  DrawerPortal,\n  DrawerTitle,\n  DrawerTrigger,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAIA;AAEA;AANA;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,sIAAA,CAAA,SAAe,CAAC,IAAI;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,sIAAA,CAAA,SAAe,CAAC,KAAK;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,QAAQ,EACR,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,sIAAA,CAAA,SAAe,CAAC,OAAO;gBACtB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,sEACA,CAAC;;;;;;;UAOD,CAAC,EACD,CAAC;;;;;;;UAOD,CAAC,EACD,CAAC;;;;;;UAMD,CAAC,EACD,CAAC;;;;;;UAMD,CAAC,EACD;gBAEF,aAAU;gBACT,GAAG,KAAK;;kCAET,8OAAC;wBACC,WAAW,CAAC;;;UAGZ,CAAC;;;;;;oBAEF;;;;;;;;;;;;;AAIT;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,sIAAA,CAAA,SAAe,CAAC,WAAW;QAC1B,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QACjD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC3C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,sIAAA,CAAA,SAAe,CAAC,OAAO;QACtB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;QAID,CAAC,EACD;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,sIAAA,CAAA,SAAe,CAAC,MAAM;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,sIAAA,CAAA,SAAe,CAAC,KAAK;QACpB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,sIAAA,CAAA,SAAe,CAAC,OAAO;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/separator.tsx"], "sourcesContent": ["\"use client\";\n\nimport type * as React from \"react\";\n\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\";\n\nimport { cn } from \"~/lib/cn\";\n\nfunction Separator({\n  className,\n  decorative = true,\n  orientation = \"horizontal\",\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      className={cn(\n        `\n          shrink-0 bg-border\n          data-[orientation=horizontal]:h-px\n          data-[orientation=horizontal]:w-full\n          data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\n        `,\n        className,\n      )}\n      data-slot=\"separator-root\"\n      decorative={decorative}\n      orientation={orientation}\n      {...props}\n    />\n  );\n}\n\nexport { Separator };\n"], "names": [], "mappings": ";;;;AAIA;AAEA;AANA;;;;AAQA,SAAS,UAAU,EACjB,SAAS,EACT,aAAa,IAAI,EACjB,cAAc,YAAY,EAC1B,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;QAKD,CAAC,EACD;QAEF,aAAU;QACV,YAAY;QACZ,aAAa;QACZ,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/sheet.tsx"], "sourcesContent": ["\"use client\";\n\nimport type * as React from \"react\";\n\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\";\nimport { XIcon } from \"lucide-react\";\n\nimport { cn } from \"~/lib/cn\";\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />;\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />;\n}\n\nfunction SheetContent({\n  children,\n  className,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"bottom\" | \"left\" | \"right\" | \"top\";\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        className={cn(\n          `\n            fixed z-50 flex flex-col gap-4 bg-background shadow-lg transition\n            ease-in-out\n            data-[state=closed]:duration-300 data-[state=closed]:animate-out\n            data-[state=open]:duration-500 data-[state=open]:animate-in\n          `,\n          side === \"right\" &&\n            `\n              inset-y-0 right-0 h-full w-3/4 border-l\n              data-[state=closed]:slide-out-to-right\n              data-[state=open]:slide-in-from-right\n              sm:max-w-sm\n            `,\n          side === \"left\" &&\n            `\n              inset-y-0 left-0 h-full w-3/4 border-r\n              data-[state=closed]:slide-out-to-left\n              data-[state=open]:slide-in-from-left\n              sm:max-w-sm\n            `,\n          side === \"top\" &&\n            `\n              inset-x-0 top-0 h-auto border-b\n              data-[state=closed]:slide-out-to-top\n              data-[state=open]:slide-in-from-top\n            `,\n          side === \"bottom\" &&\n            `\n              inset-x-0 bottom-0 h-auto border-t\n              data-[state=closed]:slide-out-to-bottom\n              data-[state=open]:slide-in-from-bottom\n            `,\n          className,\n        )}\n        data-slot=\"sheet-content\"\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close\n          className={`\n            absolute top-4 right-4 rounded-xs opacity-70 ring-offset-background\n            transition-opacity\n            hover:opacity-100\n            focus:ring-2 focus:ring-ring focus:ring-offset-2\n            focus:outline-hidden\n            disabled:pointer-events-none\n            data-[state=open]:bg-secondary\n          `}\n        >\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  );\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      data-slot=\"sheet-description\"\n      {...props}\n    />\n  );\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      data-slot=\"sheet-footer\"\n      {...props}\n    />\n  );\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      data-slot=\"sheet-header\"\n      {...props}\n    />\n  );\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      className={cn(\n        `\n          fixed inset-0 z-50 bg-black/50\n          data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n          data-[state=open]:animate-in data-[state=open]:fade-in-0\n        `,\n        className,\n      )}\n      data-slot=\"sheet-overlay\"\n      {...props}\n    />\n  );\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />;\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      className={cn(\"font-semibold text-foreground\", className)}\n      data-slot=\"sheet-title\"\n      {...props}\n    />\n  );\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />;\n}\n\nexport {\n  Sheet,\n  SheetClose,\n  SheetContent,\n  SheetDescription,\n  SheetFooter,\n  SheetHeader,\n  SheetTitle,\n  SheetTrigger,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AACA;AAEA;AAPA;;;;;AASA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,aAAa,EACpB,QAAQ,EACR,SAAS,EACT,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;UAKD,CAAC,EACD,SAAS,WACP,CAAC;;;;;YAKD,CAAC,EACH,SAAS,UACP,CAAC;;;;;YAKD,CAAC,EACH,SAAS,SACP,CAAC;;;;YAID,CAAC,EACH,SAAS,YACP,CAAC;;;;YAID,CAAC,EACH;gBAEF,aAAU;gBACT,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBACnB,WAAW,CAAC;;;;;;;;UAQZ,CAAC;;0CAED,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QACjD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC3C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;QAID,CAAC,EACD;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/cart-client.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { Minus, Plus, ShoppingCart, X } from \"lucide-react\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport * as React from \"react\";\n\nimport { cn } from \"~/lib/cn\";\nimport { useMediaQuery } from \"~/lib/hooks/use-media-query\";\nimport { Badge } from \"~/ui/primitives/badge\";\nimport { Button } from \"~/ui/primitives/button\";\nimport {\n  Drawer,\n  DrawerClose,\n  DrawerContent,\n  DrawerTrigger,\n} from \"~/ui/primitives/drawer\";\nimport { Separator } from \"~/ui/primitives/separator\";\nimport {\n  Sheet,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetTitle,\n  SheetTrigger,\n} from \"~/ui/primitives/sheet\";\n\nexport interface CartItem {\n  category: string;\n  id: string;\n  image: string;\n  name: string;\n  price: number;\n  quantity: number;\n}\n\ninterface CartProps {\n  className?: string;\n  mockCart: CartItem[];\n}\n\nexport function CartClient({ className, mockCart }: CartProps) {\n  const [isOpen, setIsOpen] = React.useState(false);\n  const [cartItems, setCartItems] = React.useState<CartItem[]>(mockCart);\n  const [isMounted, setIsMounted] = React.useState(false);\n  const isDesktop = useMediaQuery(\"(min-width: 768px)\");\n\n  React.useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  const totalItems = cartItems.reduce((acc, item) => acc + item.quantity, 0);\n  const subtotal = cartItems.reduce(\n    (acc, item) => acc + item.price * item.quantity,\n    0,\n  );\n\n  const handleUpdateQuantity = (id: string, newQuantity: number) => {\n    if (newQuantity < 1) return;\n    setCartItems((prev) =>\n      prev.map((item) =>\n        item.id === id ? { ...item, quantity: newQuantity } : item,\n      ),\n    );\n  };\n\n  const handleRemoveItem = (id: string) => {\n    setCartItems((prev) => prev.filter((item) => item.id !== id));\n  };\n\n  const handleClearCart = () => {\n    setCartItems([]);\n  };\n\n  const CartTrigger = (\n    <Button\n      aria-label=\"Open cart\"\n      className=\"relative h-9 w-9 rounded-full\"\n      size=\"icon\"\n      variant=\"outline\"\n    >\n      <ShoppingCart className=\"h-4 w-4\" />\n      {totalItems > 0 && (\n        <Badge\n          className={`\n            absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-[10px]\n          `}\n          variant=\"default\"\n        >\n          {totalItems}\n        </Badge>\n      )}\n    </Button>\n  );\n\n  const CartContent = (\n    <>\n      <div className=\"flex flex-col\">\n        <div className=\"flex items-center justify-between border-b px-6 py-4\">\n          <div>\n            <div className=\"text-xl font-semibold\">Your Cart</div>\n            <div className=\"text-sm text-muted-foreground\">\n              {totalItems === 0\n                ? \"Your cart is empty\"\n                : `You have ${totalItems} item${totalItems !== 1 ? \"s\" : \"\"} in your cart`}\n            </div>\n          </div>\n          {isDesktop && (\n            <SheetClose asChild>\n              <Button size=\"icon\" variant=\"ghost\">\n                <X className=\"h-5 w-5\" />\n              </Button>\n            </SheetClose>\n          )}\n        </div>\n\n        <div className=\"flex-1 overflow-y-auto px-6\">\n          <AnimatePresence>\n            {cartItems.length === 0 ? (\n              <motion.div\n                animate={{ opacity: 1 }}\n                className=\"flex flex-col items-center justify-center py-12\"\n                exit={{ opacity: 0 }}\n                initial={{ opacity: 0 }}\n              >\n                <div\n                  className={`\n                    mb-4 flex h-20 w-20 items-center justify-center rounded-full\n                    bg-muted\n                  `}\n                >\n                  <ShoppingCart className=\"h-10 w-10 text-muted-foreground\" />\n                </div>\n                <h3 className=\"mb-2 text-lg font-medium\">Your cart is empty</h3>\n                <p className=\"mb-6 text-center text-sm text-muted-foreground\">\n                  Looks like you haven't added anything to your cart yet.\n                </p>\n                {isDesktop ? (\n                  <SheetClose asChild>\n                    <Link href=\"/products\">\n                      <Button>Browse Products</Button>\n                    </Link>\n                  </SheetClose>\n                ) : (\n                  <DrawerClose asChild>\n                    <Link href=\"/products\">\n                      <Button>Browse Products</Button>\n                    </Link>\n                  </DrawerClose>\n                )}\n              </motion.div>\n            ) : (\n              <div className=\"space-y-4 py-4\">\n                {cartItems.map((item) => (\n                  <motion.div\n                    animate={{ opacity: 1, y: 0 }}\n                    className={`\n                      group relative flex rounded-lg border bg-card p-2\n                      shadow-sm transition-colors\n                      hover:bg-accent/50\n                    `}\n                    exit={{ opacity: 0, y: -10 }}\n                    initial={{ opacity: 0, y: 10 }}\n                    key={item.id}\n                    layout\n                    transition={{ duration: 0.15 }}\n                  >\n                    <div className=\"relative h-20 w-20 overflow-hidden rounded\">\n                      <Image\n                        alt={item.name}\n                        className=\"object-cover\"\n                        fill\n                        src={item.image}\n                      />\n                    </div>\n                    <div className=\"ml-4 flex flex-1 flex-col justify-between\">\n                      <div>\n                        <div className=\"flex items-start justify-between\">\n                          <Link\n                            className={`\n                              line-clamp-2 text-sm font-medium\n                              group-hover:text-primary\n                            `}\n                            href={`/products/${item.id}`}\n                            onClick={() => setIsOpen(false)}\n                          >\n                            {item.name}\n                          </Link>\n                          <button\n                            className={`\n                              -mt-1 -mr-1 ml-2 rounded-full p-1\n                              text-muted-foreground transition-colors\n                              hover:bg-muted hover:text-destructive\n                            `}\n                            onClick={() => handleRemoveItem(item.id)}\n                            type=\"button\"\n                          >\n                            <X className=\"h-4 w-4\" />\n                            <span className=\"sr-only\">Remove item</span>\n                          </button>\n                        </div>\n                        <p className=\"text-xs text-muted-foreground\">\n                          {item.category}\n                        </p>\n                      </div>\n                      <div className=\"mt-2 flex items-center justify-between\">\n                        <div className=\"flex items-center rounded-md border\">\n                          <button\n                            className={`\n                              flex h-7 w-7 items-center justify-center\n                              rounded-l-md border-r text-muted-foreground\n                              transition-colors\n                              hover:bg-muted hover:text-foreground\n                            `}\n                            disabled={item.quantity <= 1}\n                            onClick={() =>\n                              handleUpdateQuantity(item.id, item.quantity - 1)\n                            }\n                            type=\"button\"\n                          >\n                            <Minus className=\"h-3 w-3\" />\n                            <span className=\"sr-only\">Decrease quantity</span>\n                          </button>\n                          <span\n                            className={`\n                              flex h-7 w-7 items-center justify-center text-xs\n                              font-medium\n                            `}\n                          >\n                            {item.quantity}\n                          </span>\n                          <button\n                            className={`\n                              flex h-7 w-7 items-center justify-center\n                              rounded-r-md border-l text-muted-foreground\n                              transition-colors\n                              hover:bg-muted hover:text-foreground\n                            `}\n                            onClick={() =>\n                              handleUpdateQuantity(item.id, item.quantity + 1)\n                            }\n                            type=\"button\"\n                          >\n                            <Plus className=\"h-3 w-3\" />\n                            <span className=\"sr-only\">Increase quantity</span>\n                          </button>\n                        </div>\n                        <div className=\"text-sm font-medium\">\n                          ${(item.price * item.quantity).toFixed(2)}\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            )}\n          </AnimatePresence>\n        </div>\n\n        {cartItems.length > 0 && (\n          <div className=\"border-t px-6 py-4\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-muted-foreground\">Subtotal</span>\n                <span className=\"font-medium\">${subtotal.toFixed(2)}</span>\n              </div>\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-muted-foreground\">Shipping</span>\n                <span className=\"font-medium\">Calculated at checkout</span>\n              </div>\n              <Separator />\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-base font-semibold\">Total</span>\n                <span className=\"text-base font-semibold\">\n                  ${subtotal.toFixed(2)}\n                </span>\n              </div>\n              <Button className=\"w-full\" size=\"lg\">\n                Checkout\n              </Button>\n              <div className=\"flex items-center justify-between\">\n                {isDesktop ? (\n                  <SheetClose asChild>\n                    <Button variant=\"outline\">Continue Shopping</Button>\n                  </SheetClose>\n                ) : (\n                  <DrawerClose asChild>\n                    <Button variant=\"outline\">Continue Shopping</Button>\n                  </DrawerClose>\n                )}\n                <Button\n                  className=\"ml-2\"\n                  onClick={handleClearCart}\n                  variant=\"outline\"\n                >\n                  Clear Cart\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </>\n  );\n\n  if (!isMounted) {\n    return (\n      <div className={cn(\"relative\", className)}>\n        <Button\n          aria-label=\"Open cart\"\n          className=\"relative h-9 w-9 rounded-full\"\n          size=\"icon\"\n          variant=\"outline\"\n        >\n          <ShoppingCart className=\"h-4 w-4\" />\n          {totalItems > 0 && (\n            <Badge\n              className={`\n                absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-[10px]\n              `}\n              variant=\"default\"\n            >\n              {totalItems}\n            </Badge>\n          )}\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className={cn(\"relative\", className)}>\n      {isDesktop ? (\n        <Sheet onOpenChange={setIsOpen} open={isOpen}>\n          <SheetTrigger asChild>{CartTrigger}</SheetTrigger>\n          <SheetContent className=\"flex w-[400px] flex-col p-0\">\n            <SheetHeader>\n              <SheetTitle>Shopping Cart</SheetTitle>\n            </SheetHeader>\n            {CartContent}\n          </SheetContent>\n        </Sheet>\n      ) : (\n        <Drawer onOpenChange={setIsOpen} open={isOpen}>\n          <DrawerTrigger asChild>{CartTrigger}</DrawerTrigger>\n          <DrawerContent>{CartContent}</DrawerContent>\n        </Drawer>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAMA;AACA;AAnBA;;;;;;;;;;;;;;AA0CO,SAAS,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAa;IAC3D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAc;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,YAAY,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD,EAAE;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,aAAa,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;IACxE,MAAM,WAAW,UAAU,MAAM,CAC/B,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,GAAG,KAAK,QAAQ,EAC/C;IAGF,MAAM,uBAAuB,CAAC,IAAY;QACxC,IAAI,cAAc,GAAG;QACrB,aAAa,CAAC,OACZ,KAAK,GAAG,CAAC,CAAC,OACR,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE,UAAU;gBAAY,IAAI;IAG5D;IAEA,MAAM,mBAAmB,CAAC;QACxB,aAAa,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;IAC3D;IAEA,MAAM,kBAAkB;QACtB,aAAa,EAAE;IACjB;IAEA,MAAM,4BACJ,8OAAC,kIAAA,CAAA,SAAM;QACL,cAAW;QACX,WAAU;QACV,MAAK;QACL,SAAQ;;0BAER,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YACvB,aAAa,mBACZ,8OAAC,iIAAA,CAAA,QAAK;gBACJ,WAAW,CAAC;;UAEZ,CAAC;gBACD,SAAQ;0BAEP;;;;;;;;;;;;IAMT,MAAM,4BACJ;kBACE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;8CACZ,eAAe,IACZ,uBACA,CAAC,SAAS,EAAE,WAAW,KAAK,EAAE,eAAe,IAAI,MAAM,GAAG,aAAa,CAAC;;;;;;;;;;;;wBAG/E,2BACC,8OAAC,iIAAA,CAAA,aAAU;4BAAC,OAAO;sCACjB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAO,SAAQ;0CAC1B,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAMrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;kCACb,UAAU,MAAM,KAAK,kBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,WAAU;4BACV,MAAM;gCAAE,SAAS;4BAAE;4BACnB,SAAS;gCAAE,SAAS;4BAAE;;8CAEtB,8OAAC;oCACC,WAAW,CAAC;;;kBAGZ,CAAC;8CAED,cAAA,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,8OAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;8CAAiD;;;;;;gCAG7D,0BACC,8OAAC,iIAAA,CAAA,aAAU;oCAAC,OAAO;8CACjB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;yDAIZ,8OAAC,kIAAA,CAAA,cAAW;oCAAC,OAAO;8CAClB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;;;;iDAMhB,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,WAAW,CAAC;;;;oBAIZ,CAAC;oCACD,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC3B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAE7B,MAAM;oCACN,YAAY;wCAAE,UAAU;oCAAK;;sDAE7B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,IAAI;gDACd,WAAU;gDACV,IAAI;gDACJ,KAAK,KAAK,KAAK;;;;;;;;;;;sDAGnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,4JAAA,CAAA,UAAI;oEACH,WAAW,CAAC;;;4BAGZ,CAAC;oEACD,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;oEAC5B,SAAS,IAAM,UAAU;8EAExB,KAAK,IAAI;;;;;;8EAEZ,8OAAC;oEACC,WAAW,CAAC;;;;4BAIZ,CAAC;oEACD,SAAS,IAAM,iBAAiB,KAAK,EAAE;oEACvC,MAAK;;sFAEL,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;sFACb,8OAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;;sEAG9B,8OAAC;4DAAE,WAAU;sEACV,KAAK,QAAQ;;;;;;;;;;;;8DAGlB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,WAAW,CAAC;;;;;4BAKZ,CAAC;oEACD,UAAU,KAAK,QAAQ,IAAI;oEAC3B,SAAS,IACP,qBAAqB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;oEAEhD,MAAK;;sFAEL,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;8EAE5B,8OAAC;oEACC,WAAW,CAAC;;;4BAGZ,CAAC;8EAEA,KAAK,QAAQ;;;;;;8EAEhB,8OAAC;oEACC,WAAW,CAAC;;;;;4BAKZ,CAAC;oEACD,SAAS,IACP,qBAAqB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG;oEAEhD,MAAK;;sFAEL,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;;sEAG9B,8OAAC;4DAAI,WAAU;;gEAAsB;gEACjC,CAAC,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;mCArFxC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;gBAgGvB,UAAU,MAAM,GAAG,mBAClB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;;4CAAc;4CAAE,SAAS,OAAO,CAAC;;;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAEhC,8OAAC,qIAAA,CAAA,YAAS;;;;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;kDAC1C,8OAAC;wCAAK,WAAU;;4CAA0B;4CACtC,SAAS,OAAO,CAAC;;;;;;;;;;;;;0CAGvB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;gCAAS,MAAK;0CAAK;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;oCACZ,0BACC,8OAAC,iIAAA,CAAA,aAAU;wCAAC,OAAO;kDACjB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;6DAG5B,8OAAC,kIAAA,CAAA,cAAW;wCAAC,OAAO;kDAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;kDAG9B,8OAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS;wCACT,SAAQ;kDACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWf,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;sBAC7B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gBACL,cAAW;gBACX,WAAU;gBACV,MAAK;gBACL,SAAQ;;kCAER,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;oBACvB,aAAa,mBACZ,8OAAC,iIAAA,CAAA,QAAK;wBACJ,WAAW,CAAC;;cAEZ,CAAC;wBACD,SAAQ;kCAEP;;;;;;;;;;;;;;;;;IAMb;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBAC5B,0BACC,8OAAC,iIAAA,CAAA,QAAK;YAAC,cAAc;YAAW,MAAM;;8BACpC,8OAAC,iIAAA,CAAA,eAAY;oBAAC,OAAO;8BAAE;;;;;;8BACvB,8OAAC,iIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;;;;;;wBAEb;;;;;;;;;;;;iCAIL,8OAAC,kIAAA,CAAA,SAAM;YAAC,cAAc;YAAW,MAAM;;8BACrC,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,OAAO;8BAAE;;;;;;8BACxB,8OAAC,kIAAA,CAAA,gBAAa;8BAAE;;;;;;;;;;;;;;;;;AAK1B", "debugId": null}}, {"offset": {"line": 1685, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/cart.tsx"], "sourcesContent": ["import { cn } from \"~/lib/cn\";\n\nimport { CartClient } from \"./cart-client\";\n\nexport interface CartItem {\n  category: string;\n  id: string;\n  image: string;\n  name: string;\n  price: number;\n  quantity: number;\n}\n\ninterface CartProps {\n  className?: string;\n}\n\nconst mockCart: CartItem[] = [\n  {\n    category: \"Audio\",\n    id: \"1\",\n    image:\n      \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3\",\n    name: \"Premium Wireless Headphones\",\n    price: 199.99,\n    quantity: 1,\n  },\n  {\n    category: \"Wearables\",\n    id: \"2\",\n    image:\n      \"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3\",\n    name: \"Smart Watch Series 5\",\n    price: 299.99,\n    quantity: 2,\n  },\n];\n\nexport function Cart({ className }: CartProps) {\n  return (\n    <div className={cn(\"relative\", className)}>\n      {/* // TODO: Fetch cart from e.g. LocalStorage and/or database */}\n      <CartClient className={cn(\"\", className)} mockCart={mockCart} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAeA,MAAM,WAAuB;IAC3B;QACE,UAAU;QACV,IAAI;QACJ,OACE;QACF,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA;QACE,UAAU;QACV,IAAI;QACJ,OACE;QACF,MAAM;QACN,OAAO;QACP,UAAU;IACZ;CACD;AAEM,SAAS,KAAK,EAAE,SAAS,EAAa;IAC3C,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBAE7B,cAAA,8OAAC,0IAAA,CAAA,aAAU;YAAC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;YAAY,UAAU;;;;;;;;;;;AAG1D", "debugId": null}}, {"offset": {"line": 1735, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/skeleton.tsx"], "sourcesContent": ["import { cn } from \"~/lib/cn\";\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-accent\", className)}\n      data-slot=\"skeleton\"\n      {...props}\n    />\n  );\n}\n\nexport { Skeleton };\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1760, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/card.tsx"], "sourcesContent": ["import type * as React from \"react\";\n\nimport { cn } from \"~/lib/cn\";\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\n        `\n          flex flex-col gap-6 rounded-xl border bg-card py-6\n          text-card-foreground shadow-sm\n        `,\n        className,\n      )}\n      data-slot=\"card\"\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className,\n      )}\n      data-slot=\"card-action\"\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\"px-6\", className)}\n      data-slot=\"card-content\"\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      data-slot=\"card-description\"\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\n        `\n          flex items-center px-6\n          [.border-t]:pt-6\n        `,\n        className,\n      )}\n      data-slot=\"card-footer\"\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\n        `\n          @container/card-header grid auto-rows-min grid-rows-[auto_auto]\n          items-start gap-1.5 px-6\n          has-data-[slot=card-action]:grid-cols-[1fr_auto]\n          [.border-b]:pb-6\n        `,\n        className,\n      )}\n      data-slot=\"card-header\"\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      className={cn(\"leading-none font-semibold\", className)}\n      data-slot=\"card-title\"\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardAction,\n  CardContent,\n  CardDescription,\n  CardFooter,\n  CardHeader,\n  CardTitle,\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;QAGD,CAAC,EACD;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACtB,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;QAGD,CAAC,EACD;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;QAKD,CAAC,EACD;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC5C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1868, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/dropdown-menu.tsx"], "sourcesContent": ["\"use client\";\n\nimport type * as React from \"react\";\n\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\";\n\nimport { cn } from \"~/lib/cn\";\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />;\n}\n\nfunction DropdownMenuCheckboxItem({\n  checked,\n  children,\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      checked={checked}\n      className={cn(\n        `\n          relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2\n          pl-8 text-sm outline-hidden select-none\n          focus:bg-accent focus:text-accent-foreground\n          data-[disabled]:pointer-events-none data-[disabled]:opacity-50\n          [&_svg]:pointer-events-none [&_svg]:shrink-0\n          [&_svg:not([class*='size-'])]:size-4\n        `,\n        className,\n      )}\n      data-slot=\"dropdown-menu-checkbox-item\"\n      {...props}\n    >\n      <span\n        className={`\n          pointer-events-none absolute left-2 flex size-3.5 items-center\n          justify-center\n        `}\n      >\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  );\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        className={cn(\n          `\n            z-50 max-h-(--radix-dropdown-menu-content-available-height)\n            min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin)\n            overflow-x-hidden overflow-y-auto rounded-md border bg-popover p-1\n            text-popover-foreground shadow-md\n            data-[side=bottom]:slide-in-from-top-2\n            data-[side=left]:slide-in-from-right-2\n            data-[side=right]:slide-in-from-left-2\n            data-[side=top]:slide-in-from-bottom-2\n            data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n            data-[state=closed]:zoom-out-95\n            data-[state=open]:animate-in data-[state=open]:fade-in-0\n            data-[state=open]:zoom-in-95\n          `,\n          className,\n        )}\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  );\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  );\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean;\n  variant?: \"default\" | \"destructive\";\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      className={cn(\n        `\n          relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5\n          text-sm outline-hidden select-none\n          focus:bg-accent focus:text-accent-foreground\n          data-[disabled]:pointer-events-none data-[disabled]:opacity-50\n          data-[inset]:pl-8\n          data-[variant=destructive]:text-destructive\n          data-[variant=destructive]:focus:bg-destructive/10\n          data-[variant=destructive]:focus:text-destructive\n          data-[variant=destructive]:*:[svg]:!text-destructive\n          dark:data-[variant=destructive]:focus:bg-destructive/20\n          [&_svg]:pointer-events-none [&_svg]:shrink-0\n          [&_svg:not([class*='size-'])]:size-4\n          [&_svg:not([class*='text-'])]:text-muted-foreground\n        `,\n        className,\n      )}\n      data-inset={inset}\n      data-slot=\"dropdown-menu-item\"\n      data-variant={variant}\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean;\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      className={cn(\n        `\n          px-2 py-1.5 text-sm font-medium\n          data-[inset]:pl-8\n        `,\n        className,\n      )}\n      data-inset={inset}\n      data-slot=\"dropdown-menu-label\"\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  );\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuRadioItem({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      className={cn(\n        `\n          relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2\n          pl-8 text-sm outline-hidden select-none\n          focus:bg-accent focus:text-accent-foreground\n          data-[disabled]:pointer-events-none data-[disabled]:opacity-50\n          [&_svg]:pointer-events-none [&_svg]:shrink-0\n          [&_svg:not([class*='size-'])]:size-4\n        `,\n        className,\n      )}\n      data-slot=\"dropdown-menu-radio-item\"\n      {...props}\n    >\n      <span\n        className={`\n          pointer-events-none absolute left-2 flex size-3.5 items-center\n          justify-center\n        `}\n      >\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  );\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      className={cn(\"-mx-1 my-1 h-px bg-border\", className)}\n      data-slot=\"dropdown-menu-separator\"\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      className={cn(\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\n        className,\n      )}\n      data-slot=\"dropdown-menu-shortcut\"\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />;\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      className={cn(\n        `\n          z-50 min-w-[8rem]\n          origin-(--radix-dropdown-menu-content-transform-origin)\n          overflow-hidden rounded-md border bg-popover p-1\n          text-popover-foreground shadow-lg\n          data-[side=bottom]:slide-in-from-top-2\n          data-[side=left]:slide-in-from-right-2\n          data-[side=right]:slide-in-from-left-2\n          data-[side=top]:slide-in-from-bottom-2\n          data-[state=closed]:animate-out data-[state=closed]:fade-out-0\n          data-[state=closed]:zoom-out-95\n          data-[state=open]:animate-in data-[state=open]:fade-in-0\n          data-[state=open]:zoom-in-95\n        `,\n        className,\n      )}\n      data-slot=\"dropdown-menu-sub-content\"\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuSubTrigger({\n  children,\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean;\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      className={cn(\n        `\n          flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm\n          outline-hidden select-none\n          focus:bg-accent focus:text-accent-foreground\n          data-[inset]:pl-8\n          data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\n        `,\n        className,\n      )}\n      data-inset={inset}\n      data-slot=\"dropdown-menu-sub-trigger\"\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  );\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  );\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuCheckboxItem,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuPortal,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuTrigger,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAIA;AACA;AAAA;AAAA;AAEA;AAPA;;;;;AASA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,yBAAyB,EAChC,OAAO,EACP,QAAQ,EACR,SAAS,EACT,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,SAAS;QACT,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;QAOD,CAAC,EACD;QAEF,aAAU;QACT,GAAG,KAAK;;0BAET,8OAAC;gBACC,WAAW,CAAC;;;QAGZ,CAAC;0BAED,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;;;;;;;UAaD,CAAC,EACD;YAEF,aAAU;YACV,YAAY;YACX,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;;;;;;;;QAcD,CAAC,EACD;QAEF,cAAY;QACZ,aAAU;QACV,gBAAc;QACb,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;QAGD,CAAC,EACD;QAEF,cAAY;QACZ,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,QAAQ,EACR,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;QAOD,CAAC,EACD;QAEF,aAAU;QACT,GAAG,KAAK;;0BAET,8OAAC;gBACC,WAAW,CAAC;;;QAGZ,CAAC;0BAED,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC3C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;;;;;;;QAaD,CAAC,EACD;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,QAAQ,EACR,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;QAMD,CAAC,EACD;QAEF,cAAY;QACZ,aAAU;QACT,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2199, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/notifications/notifications.tsx"], "sourcesContent": ["import type * as React from \"react\";\n\nimport { <PERSON>, <PERSON>, <PERSON> } from \"lucide-react\";\n\nimport { cn } from \"~/lib/cn\";\nimport { Button } from \"~/ui/primitives/button\";\nimport {\n  DropdownMenuGroup,\n  DropdownMenuItem,\n} from \"~/ui/primitives/dropdown-menu\";\n\nimport type { Notification } from \"./notification-center\";\n\ninterface NotificationsProps {\n  notifications: Notification[];\n  onDismiss?: (id: string) => void;\n  onMarkAsRead?: (id: string) => void;\n}\n\nfunction formatTimestamp(date: Date) {\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n  if (diffInSeconds < 60) return \"Just now\";\n  if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60);\n    return minutes === 1 ? \"1 minute ago\" : `${minutes} minutes ago`;\n  }\n  if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600);\n    return hours === 1 ? \"1 hour ago\" : `${hours} hours ago`;\n  }\n  const days = Math.floor(diffInSeconds / 86400);\n  return days === 1 ? \"1 day ago\" : `${days} days ago`;\n}\n\nfunction getNotificationIcon(type: Notification[\"type\"]) {\n  switch (type) {\n    case \"error\":\n      return <div className=\"h-2 w-2 rounded-full bg-red-500\" />;\n    case \"info\":\n      return <div className=\"h-2 w-2 rounded-full bg-blue-500\" />;\n    case \"success\":\n      return <div className=\"h-2 w-2 rounded-full bg-green-500\" />;\n    case \"warning\":\n      return <div className=\"h-2 w-2 rounded-full bg-yellow-500\" />;\n    default:\n      return null;\n  }\n}\n\nexport const Notifications: React.FC<NotificationsProps> = ({\n  notifications,\n  onDismiss,\n  onMarkAsRead,\n}) => {\n  if (notifications.length === 0) {\n    return (\n      <div\n        className={\"flex flex-col items-center justify-center py-6 text-center\"}\n      >\n        <Bell className=\"mb-2 h-10 w-10 text-muted-foreground/50\" />\n        <p className=\"text-sm font-medium\">No notifications yet</p>\n        <p className=\"text-xs text-muted-foreground\">\n          When you get notifications, they'll show up here\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <DropdownMenuGroup className=\"max-h-[300px] overflow-y-auto\">\n      {notifications.map((notification) => (\n        <DropdownMenuItem\n          className={cn(\n            \"flex cursor-default flex-col items-start p-0\",\n            !notification.read && \"bg-muted/50\",\n          )}\n          key={notification.id}\n          onSelect={(e) => e.preventDefault()}\n        >\n          <div className=\"flex w-full items-start gap-2 p-2\">\n            <div className=\"mt-1 flex-shrink-0\">\n              {getNotificationIcon(notification.type)}\n            </div>\n            <div className=\"flex-1 space-y-1\">\n              <div className=\"flex items-center justify-between\">\n                <p className=\"text-sm leading-none font-medium\">\n                  {notification.title}\n                </p>\n                <span className=\"text-xs text-muted-foreground\">\n                  {formatTimestamp(notification.timestamp)}\n                </span>\n              </div>\n              <p className=\"text-xs text-muted-foreground\">\n                {notification.description}\n              </p>\n            </div>\n            <div className=\"flex flex-shrink-0 gap-1\">\n              {!notification.read && (\n                <Button\n                  className=\"h-6 w-6\"\n                  onClick={() => onMarkAsRead?.(notification.id)}\n                  size=\"icon\"\n                  variant=\"ghost\"\n                >\n                  <Check className=\"h-3 w-3\" />\n                  <span className=\"sr-only\">Mark as read</span>\n                </Button>\n              )}\n              <Button\n                className=\"h-6 w-6\"\n                onClick={() => onDismiss?.(notification.id)}\n                size=\"icon\"\n                variant=\"ghost\"\n              >\n                <X className=\"h-3 w-3\" />\n                <span className=\"sr-only\">Dismiss</span>\n              </Button>\n            </div>\n          </div>\n        </DropdownMenuItem>\n      ))}\n    </DropdownMenuGroup>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AACA;;;;;;AAaA,SAAS,gBAAgB,IAAU;IACjC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IACpE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM;QACxB,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,YAAY,IAAI,iBAAiB,GAAG,QAAQ,YAAY,CAAC;IAClE;IACA,IAAI,gBAAgB,OAAO;QACzB,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,UAAU,IAAI,eAAe,GAAG,MAAM,UAAU,CAAC;IAC1D;IACA,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;IACxC,OAAO,SAAS,IAAI,cAAc,GAAG,KAAK,SAAS,CAAC;AACtD;AAEA,SAAS,oBAAoB,IAA0B;IACrD,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC;gBAAI,WAAU;;;;;;QACxB,KAAK;YACH,qBAAO,8OAAC;gBAAI,WAAU;;;;;;QACxB,KAAK;YACH,qBAAO,8OAAC;gBAAI,WAAU;;;;;;QACxB,KAAK;YACH,qBAAO,8OAAC;gBAAI,WAAU;;;;;;QACxB;YACE,OAAO;IACX;AACF;AAEO,MAAM,gBAA8C,CAAC,EAC1D,aAAa,EACb,SAAS,EACT,YAAY,EACb;IACC,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC;YACC,WAAW;;8BAEX,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;8BAChB,8OAAC;oBAAE,WAAU;8BAAsB;;;;;;8BACnC,8OAAC;oBAAE,WAAU;8BAAgC;;;;;;;;;;;;IAKnD;IAEA,qBACE,8OAAC,4IAAA,CAAA,oBAAiB;QAAC,WAAU;kBAC1B,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC,4IAAA,CAAA,mBAAgB;gBACf,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,gDACA,CAAC,aAAa,IAAI,IAAI;gBAGxB,UAAU,CAAC,IAAM,EAAE,cAAc;0BAEjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,oBAAoB,aAAa,IAAI;;;;;;sCAExC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,aAAa,KAAK;;;;;;sDAErB,8OAAC;4CAAK,WAAU;sDACb,gBAAgB,aAAa,SAAS;;;;;;;;;;;;8CAG3C,8OAAC;oCAAE,WAAU;8CACV,aAAa,WAAW;;;;;;;;;;;;sCAG7B,8OAAC;4BAAI,WAAU;;gCACZ,CAAC,aAAa,IAAI,kBACjB,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe,aAAa,EAAE;oCAC7C,MAAK;oCACL,SAAQ;;sDAER,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAG9B,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS,IAAM,YAAY,aAAa,EAAE;oCAC1C,MAAK;oCACL,SAAQ;;sDAER,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;eAvC3B,aAAa,EAAE;;;;;;;;;;AA+C9B", "debugId": null}}, {"offset": {"line": 2446, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/notifications/notification-center.tsx"], "sourcesContent": ["\"use client\";\n\nimport type React from \"react\";\n\nimport { Bell } from \"lucide-react\";\nimport { useCallback, useState } from \"react\";\n\nimport { cn } from \"~/lib/cn\";\nimport { Button } from \"~/ui/primitives/button\";\nimport { CardFooter } from \"~/ui/primitives/card\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"~/ui/primitives/dropdown-menu\";\n\nimport { Notifications } from \"./notifications\";\n\nexport interface Notification {\n  description: string;\n  id: string;\n  read: boolean;\n  timestamp: Date;\n  title: string;\n  type: \"error\" | \"info\" | \"success\" | \"warning\";\n}\n\ntype NotificationCenterProps = React.HTMLAttributes<HTMLDivElement> & {\n  notifications: Notification[];\n  onClearAll?: () => void;\n  onDismiss?: (id: string) => void;\n  onMarkAllAsRead?: () => void;\n  onMarkAsRead?: (id: string) => void;\n};\n\nexport function NotificationCenter({\n  className,\n  notifications,\n  onClearAll,\n  onDismiss,\n  onMarkAllAsRead,\n  onMarkAsRead,\n  ...props\n}: NotificationCenterProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const unreadCount = notifications.filter((n) => !n.read).length;\n\n  const handleMarkAsRead = useCallback(\n    (id: string) => onMarkAsRead?.(id),\n    [onMarkAsRead],\n  );\n\n  const handleMarkAllAsRead = useCallback(\n    () => onMarkAllAsRead?.(),\n    [onMarkAllAsRead],\n  );\n\n  const handleDismiss = useCallback(\n    (id: string) => onDismiss?.(id),\n    [onDismiss],\n  );\n\n  const handleClearAll = useCallback(() => onClearAll?.(), [onClearAll]);\n\n  return (\n    <div className={cn(\"relative\", className)} {...props}>\n      <DropdownMenu onOpenChange={setIsOpen} open={isOpen}>\n        <DropdownMenuTrigger asChild>\n          <Button className=\"relative\" size=\"icon\" variant=\"ghost\">\n            <Bell className=\"h-5 w-5\" />\n            {unreadCount > 0 && (\n              <span\n                className={`\n                  absolute top-1 right-1 flex h-4 min-w-4 items-center\n                  justify-center rounded-full bg-red-500 px-1 text-[10px]\n                  font-medium text-white\n                `}\n              >\n                {unreadCount > 99 ? \"99+\" : String(unreadCount)}\n              </span>\n            )}\n          </Button>\n        </DropdownMenuTrigger>\n\n        <DropdownMenuContent align=\"end\" className=\"w-80\">\n          <DropdownMenuLabel className=\"flex items-center justify-between\">\n            <span>Notifications</span>\n            {unreadCount > 0 && (\n              <Button\n                className=\"h-auto p-0 text-xs font-normal text-primary\"\n                onClick={handleMarkAllAsRead}\n                size=\"sm\"\n                variant=\"ghost\"\n              >\n                Mark all as read\n              </Button>\n            )}\n          </DropdownMenuLabel>\n\n          <DropdownMenuSeparator />\n\n          <Notifications\n            notifications={notifications}\n            onDismiss={handleDismiss}\n            onMarkAsRead={handleMarkAsRead}\n          />\n\n          {notifications.length > 0 && (\n            <>\n              <DropdownMenuSeparator />\n              <CardFooter className=\"p-2\">\n                <Button\n                  className=\"w-full\"\n                  onClick={handleClearAll}\n                  size=\"sm\"\n                  variant=\"outline\"\n                >\n                  Clear all notifications\n                </Button>\n              </CardFooter>\n            </>\n          )}\n        </DropdownMenuContent>\n      </DropdownMenu>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAEA;AACA;AACA;AACA;AAQA;AAlBA;;;;;;;;;AAqCO,SAAS,mBAAmB,EACjC,SAAS,EACT,aAAa,EACb,UAAU,EACV,SAAS,EACT,eAAe,EACf,YAAY,EACZ,GAAG,OACqB;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,IAAI,EAAE,MAAM;IAE/D,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,CAAC,KAAe,eAAe,KAC/B;QAAC;KAAa;IAGhB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,IAAM,qBACN;QAAC;KAAgB;IAGnB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,KAAe,YAAY,KAC5B;QAAC;KAAU;IAGb,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,gBAAgB;QAAC;KAAW;IAErE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAClD,cAAA,8OAAC,4IAAA,CAAA,eAAY;YAAC,cAAc;YAAW,MAAM;;8BAC3C,8OAAC,4IAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;wBAAW,MAAK;wBAAO,SAAQ;;0CAC/C,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,cAAc,mBACb,8OAAC;gCACC,WAAW,CAAC;;;;gBAIZ,CAAC;0CAEA,cAAc,KAAK,QAAQ,OAAO;;;;;;;;;;;;;;;;;8BAM3C,8OAAC,4IAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAM,WAAU;;sCACzC,8OAAC,4IAAA,CAAA,oBAAiB;4BAAC,WAAU;;8CAC3B,8OAAC;8CAAK;;;;;;gCACL,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS;oCACT,MAAK;oCACL,SAAQ;8CACT;;;;;;;;;;;;sCAML,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sCAEtB,8OAAC,0JAAA,CAAA,gBAAa;4BACZ,eAAe;4BACf,WAAW;4BACX,cAAc;;;;;;wBAGf,cAAc,MAAM,GAAG,mBACtB;;8CACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8CACtB,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS;wCACT,MAAK;wCACL,SAAQ;kDACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 2621, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/notifications/notifications.mock.ts"], "sourcesContent": ["import { SEO_CONFIG } from \"~/app\";\n\nimport type { Notification } from \"./notification-center\";\n\nexport const mockNotifications: Notification[] = [\n  {\n    description: \"Thank you for signing up. Explore our features.\",\n    id: \"1\",\n    read: false,\n    timestamp: new Date(Date.now() - 60 * 1000), // 1 minute ago\n    title: `Welcome to ${SEO_CONFIG.name}!`,\n    type: \"success\",\n  },\n  {\n    description: \"There was an issue with your payment method.\",\n    id: \"2\",\n    read: false,\n    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago\n    title: \"Payment Failed\",\n    type: \"error\",\n  },\n  {\n    description: \"Your order #1234 has shipped.\",\n    id: \"3\",\n    read: false,\n    timestamp: new Date(Date.now() - 3600 * 1000), // 1 hour ago\n    title: \"Order Shipped\",\n    type: \"info\",\n  },\n  {\n    description: \"The item on your wishlist is now back in stock!\",\n    id: \"4\",\n    read: true,\n    timestamp: new Date(Date.now() - 3600 * 1000), // 1 hour ago\n    title: \"New Product Available\",\n    type: \"info\",\n  },\n  {\n    description: \"Get 10% off your next purchase.\",\n    id: \"5\",\n    read: true,\n    timestamp: new Date(Date.now() - 2 * 86400 * 1000), // 2 days ago\n    title: \"Discount Available!\",\n    type: \"warning\",\n  },\n];\n"], "names": [], "mappings": ";;;AAAA;;AAIO,MAAM,oBAAoC;IAC/C;QACE,aAAa;QACb,IAAI;QACJ,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK;QACtC,OAAO,CAAC,WAAW,EAAE,0GAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QACvC,MAAM;IACR;IACA;QACE,aAAa;QACb,IAAI;QACJ,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK;QAC1C,OAAO;QACP,MAAM;IACR;IACA;QACE,aAAa;QACb,IAAI;QACJ,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO;QACxC,OAAO;QACP,MAAM;IACR;IACA;QACE,aAAa;QACb,IAAI;QACJ,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO;QACxC,OAAO;QACP,MAAM;IACR;IACA;QACE,aAAa;QACb,IAAI;QACJ,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,QAAQ;QAC7C,OAAO;QACP,MAAM;IACR;CACD", "debugId": null}}, {"offset": {"line": 2674, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/notifications/notifications-widget.tsx"], "sourcesContent": ["import React from \"react\";\n\nimport type { Notification } from \"./notification-center\";\n\nimport { NotificationCenter } from \"./notification-center\";\nimport { mockNotifications } from \"./notifications.mock\";\n\nexport function NotificationsWidget() {\n  const [notifications, setNotifications] = React.useState<Notification[]>(\n    () => mockNotifications,\n  );\n\n  const handleMarkAsRead = (id: string) => {\n    setNotifications((prev) =>\n      prev.map((n) => (n.id === id ? { ...n, read: true } : n)),\n    );\n  };\n\n  const handleMarkAllAsRead = () => {\n    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));\n  };\n\n  const handleDismiss = (id: string) => {\n    setNotifications((prev) => prev.filter((n) => n.id !== id));\n  };\n\n  const handleClearAll = () => {\n    setNotifications([]);\n  };\n\n  return (\n    <NotificationCenter\n      notifications={notifications}\n      onClearAll={handleClearAll}\n      onDismiss={handleDismiss}\n      onMarkAllAsRead={handleMarkAllAsRead}\n      onMarkAsRead={handleMarkAsRead}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAIA;AACA;;;;;AAEO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CACtD,IAAM,iKAAA,CAAA,oBAAiB;IAGzB,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,CAAC,OAChB,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,KAAK;oBAAE,GAAG,CAAC;oBAAE,MAAM;gBAAK,IAAI;IAE1D;IAEA,MAAM,sBAAsB;QAC1B,iBAAiB,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAM,CAAC;oBAAE,GAAG,CAAC;oBAAE,MAAM;gBAAK,CAAC;IAClE;IAEA,MAAM,gBAAgB,CAAC;QACrB,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACzD;IAEA,MAAM,iBAAiB;QACrB,iBAAiB,EAAE;IACrB;IAEA,qBACE,8OAAC,mKAAA,CAAA,qBAAkB;QACjB,eAAe;QACf,YAAY;QACZ,WAAW;QACX,iBAAiB;QACjB,cAAc;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 2723, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON>, Sun } from \"lucide-react\";\nimport { useTheme } from \"next-themes\";\nimport * as React from \"react\";\n\nimport { cn } from \"~/lib/cn\";\nimport { Button } from \"~/ui/primitives/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"~/ui/primitives/dropdown-menu\";\n\nexport function ThemeToggle({ className }: { className?: string }) {\n  const { setTheme, theme } = useTheme();\n  const [mounted, setMounted] = React.useState(false);\n\n  // Avoid hydration mismatch by rendering only on client-side\n  React.useEffect(() => {\n    requestAnimationFrame(() => setMounted(true));\n  }, []);\n\n  if (!mounted) {\n    return (\n      <Button\n        className={cn(\"h-9 w-9 rounded-full\", className)}\n        disabled\n        size=\"icon\"\n        variant=\"ghost\"\n      >\n        <Sun className=\"h-[1.2rem] w-[1.2rem] opacity-70\" />\n        <span className=\"sr-only\">Loading theme toggle</span>\n      </Button>\n    );\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          className={cn(\n            `\n              h-9 w-9 rounded-full bg-background transition-colors\n              hover:bg-muted\n            `,\n            className,\n          )}\n          size=\"icon\"\n          variant=\"ghost\"\n        >\n          <Sun\n            className={`\n              h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all\n              duration-300\n              dark:scale-0 dark:-rotate-90\n            `}\n          />\n          <Moon\n            className={`\n              absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all\n              duration-300\n              dark:scale-100 dark:rotate-0\n            `}\n          />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem\n          className={cn(\n            \"flex cursor-pointer items-center gap-2\",\n            theme === \"light\" && \"font-medium text-primary\",\n          )}\n          onClick={() => setTheme(\"light\")}\n        >\n          <Sun className=\"h-4 w-4\" />\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem\n          className={cn(\n            \"flex cursor-pointer items-center gap-2\",\n            theme === \"dark\" && \"font-medium text-primary\",\n          )}\n          onClick={() => setTheme(\"dark\")}\n        >\n          <Moon className=\"h-4 w-4\" />\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem\n          className={cn(\n            \"flex cursor-pointer items-center gap-2\",\n            (theme === \"system\" || !theme) && \"font-medium text-primary\",\n          )}\n          onClick={() => setTheme(\"system\")}\n        >\n          <svg\n            aria-hidden=\"true\"\n            className=\"h-4 w-4\"\n            fill=\"currentColor\"\n            role=\"img\"\n            viewBox=\"0 0 20 20\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <title>System Theme Icon</title>\n            <path\n              clipRule=\"evenodd\"\n              d=\"M3.5 2A1.5 1.5 0 002 3.5V15a3 3 0 003 3h12a1.5 1.5 0 001.5-1.5V3.5A1.5 1.5 0 0017 2H3.5zM5 5.75c0-.41.334-.75.75-.75h8.5a.75.75 0 010 1.5h-8.5a.75.75 0 01-.75-.75zM5.75 8.25a.75.75 0 00-.75.75v3.25c0 .414.336.75.75.75h8.5a.75.75 0 00.75-.75V9a.75.75 0 00-.75-.75h-8.5z\"\n              fillRule=\"evenodd\"\n            />\n          </svg>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAEA;AACA;AACA;AARA;;;;;;;;AAeO,SAAS,YAAY,EAAE,SAAS,EAA0B;IAC/D,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,4DAA4D;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,sBAAsB,IAAM,WAAW;IACzC,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;YACtC,QAAQ;YACR,MAAK;YACL,SAAQ;;8BAER,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;YAGD,CAAC,EACD;oBAEF,MAAK;oBACL,SAAQ;;sCAER,8OAAC,gMAAA,CAAA,MAAG;4BACF,WAAW,CAAC;;;;YAIZ,CAAC;;;;;;sCAEH,8OAAC,kMAAA,CAAA,OAAI;4BACH,WAAW,CAAC;;;;YAIZ,CAAC;;;;;;sCAEH,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,0CACA,UAAU,WAAW;wBAEvB,SAAS,IAAM,SAAS;;0CAExB,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAG7B,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,0CACA,UAAU,UAAU;wBAEtB,SAAS,IAAM,SAAS;;0CAExB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAG9B,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,0CACA,CAAC,UAAU,YAAY,CAAC,KAAK,KAAK;wBAEpC,SAAS,IAAM,SAAS;;0CAExB,8OAAC;gCACC,eAAY;gCACZ,WAAU;gCACV,MAAK;gCACL,MAAK;gCACL,SAAQ;gCACR,OAAM;;kDAEN,8OAAC;kDAAM;;;;;;kDACP,8OAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;;4BAEP;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 2930, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/avatar.tsx"], "sourcesContent": ["\"use client\";\n\nimport type * as React from \"react\";\n\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\n\nimport { cn } from \"~/lib/cn\";\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className,\n      )}\n      data-slot=\"avatar\"\n      {...props}\n    />\n  );\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      className={cn(\n        \"flex size-full items-center justify-center rounded-full bg-muted\",\n        className,\n      )}\n      data-slot=\"avatar-fallback\"\n      {...props}\n    />\n  );\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      className={cn(\"aspect-square size-full\", className)}\n      data-slot=\"avatar-image\"\n      {...props}\n    />\n  );\n}\n\nexport { Avatar, AvatarFallback, AvatarImage };\n"], "names": [], "mappings": ";;;;;;AAIA;AAEA;AANA;;;;AAQA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACzC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2982, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/header/header-user.tsx"], "sourcesContent": ["import {\n  <PERSON><PERSON><PERSON>,\n  LogOut,\n  <PERSON>ting<PERSON>,\n  Shield,\n  Upload,\n  UserIcon,\n} from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { cn } from \"~/lib/cn\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"~/ui/primitives/avatar\";\nimport { Button } from \"~/ui/primitives/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"~/ui/primitives/dropdown-menu\";\n\ninterface HeaderUserDropdownProps {\n  isDashboard: boolean;\n  userEmail: string;\n  userImage?: null | string;\n  userName: string;\n}\n\nexport function HeaderUserDropdown({\n  isDashboard = false,\n  userEmail,\n  userImage,\n  userName,\n}: HeaderUserDropdownProps) {\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          className=\"relative overflow-hidden rounded-full\"\n          size=\"icon\"\n          variant=\"ghost\"\n        >\n          <Avatar className=\"h-9 w-9\">\n            <AvatarImage\n              alt={userName || \"User\"}\n              src={userImage || undefined}\n            />\n            <AvatarFallback>\n              {userName ? (\n                userName\n                  .split(\" \")\n                  .map((n: string) => n[0])\n                  .join(\"\")\n                  .slice(0, 2)\n              ) : (\n                <UserIcon className=\"h-4 w-4\" />\n              )}\n            </AvatarFallback>\n          </Avatar>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\" className=\"w-56\">\n        <div className=\"flex items-center justify-start gap-2 p-2\">\n          <Avatar className=\"h-8 w-8 bg-primary/10\">\n            <AvatarImage\n              alt={userName || \"User\"}\n              src={userImage || undefined}\n            />\n            <AvatarFallback>\n              {userName ? (\n                userName\n                  .split(\" \")\n                  .map((n: string) => n[0])\n                  .join(\"\")\n                  .slice(0, 2)\n              ) : (\n                <UserIcon className=\"h-4 w-4 text-primary\" />\n              )}\n            </AvatarFallback>\n          </Avatar>\n          <div className=\"flex flex-col space-y-0.5\">\n            <p className=\"text-sm font-medium\">{userName || \"User\"}</p>\n            <p\n              className={\"max-w-[160px] truncate text-xs text-muted-foreground\"}\n            >\n              {userEmail}\n            </p>\n          </div>\n        </div>\n        <DropdownMenuSeparator />\n        <DropdownMenuItem asChild>\n          <Link className=\"cursor-pointer\" href=\"/dashboard/stats\">\n            <BarChart className=\"mr-2 h-4 w-4\" />\n            Stats\n          </Link>\n        </DropdownMenuItem>\n        <DropdownMenuItem asChild>\n          <Link className=\"cursor-pointer\" href=\"/dashboard/profile\">\n            <UserIcon className=\"mr-2 h-4 w-4\" />\n            Profile\n          </Link>\n        </DropdownMenuItem>\n        <DropdownMenuItem asChild>\n          <Link className=\"cursor-pointer\" href=\"/dashboard/settings\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            Settings\n          </Link>\n        </DropdownMenuItem>\n        <DropdownMenuItem asChild>\n          <Link className=\"cursor-pointer\" href=\"/dashboard/uploads\">\n            <Upload className=\"mr-2 h-4 w-4\" />\n            Uploads\n          </Link>\n        </DropdownMenuItem>\n        <DropdownMenuItem asChild>\n          <Link className=\"cursor-pointer\" href=\"/admin/summary\">\n            <Shield className=\"mr-2 h-4 w-4\" />\n            Admin\n          </Link>\n        </DropdownMenuItem>\n        <DropdownMenuSeparator />\n        <DropdownMenuItem\n          asChild\n          className={cn(\n            \"cursor-pointer\",\n            isDashboard\n              ? \"text-red-600\"\n              : `\n                txt-destructive\n                focus:text-destrctive\n              `,\n          )}\n        >\n          <Link href=\"/auth/sign-out\">\n            <LogOut className=\"mr-2 h-4 w-4\" />\n            Log out\n          </Link>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAEA;AACA;AACA;AACA;;;;;;;;AAeO,SAAS,mBAAmB,EACjC,cAAc,KAAK,EACnB,SAAS,EACT,SAAS,EACT,QAAQ,EACgB;IACxB,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,WAAU;oBACV,MAAK;oBACL,SAAQ;8BAER,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,8OAAC,kIAAA,CAAA,cAAW;gCACV,KAAK,YAAY;gCACjB,KAAK,aAAa;;;;;;0CAEpB,8OAAC,kIAAA,CAAA,iBAAc;0CACZ,WACC,SACG,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EACvB,IAAI,CAAC,IACL,KAAK,CAAC,GAAG,mBAEZ,8OAAC,sMAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,8OAAC,kIAAA,CAAA,cAAW;wCACV,KAAK,YAAY;wCACjB,KAAK,aAAa;;;;;;kDAEpB,8OAAC,kIAAA,CAAA,iBAAc;kDACZ,WACC,SACG,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EACvB,IAAI,CAAC,IACL,KAAK,CAAC,GAAG,mBAEZ,8OAAC,sMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAuB,YAAY;;;;;;kDAChD,8OAAC;wCACC,WAAW;kDAEV;;;;;;;;;;;;;;;;;;kCAIP,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCACtB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAiB,MAAK;;8CACpC,8OAAC,6OAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAiB,MAAK;;8CACpC,8OAAC,sMAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAiB,MAAK;;8CACpC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAiB,MAAK;;8CACpC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIvC,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAiB,MAAK;;8CACpC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIvC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCACtB,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,OAAO;wBACP,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,kBACA,cACI,iBACA,CAAC;;;cAGH,CAAC;kCAGL,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 3303, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/header/header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON> } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { useState } from \"react\";\n\nimport { SEO_CONFIG } from \"~/app\";\nimport { useCurrentUser } from \"~/lib/auth-client\";\nimport { cn } from \"~/lib/cn\";\nimport { Cart } from \"~/ui/components/cart\";\nimport { Button } from \"~/ui/primitives/button\";\nimport { Skeleton } from \"~/ui/primitives/skeleton\";\n\nimport { NotificationsWidget } from \"../notifications/notifications-widget\";\nimport { ThemeToggle } from \"../theme-toggle\";\nimport { HeaderUserDropdown } from \"./header-user\";\n\ninterface HeaderProps {\n  children?: React.ReactNode;\n  showAuth?: boolean;\n}\n\nexport function Header({ showAuth = true }: HeaderProps) {\n  const pathname = usePathname();\n  const { isPending, user } = useCurrentUser();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  const mainNavigation = [\n    { href: \"/\", name: \"Home\" },\n    { href: \"/products\", name: \"Products\" },\n  ];\n\n  const dashboardNavigation = [\n    { href: \"/dashboard/stats\", name: \"Stats\" },\n    { href: \"/dashboard/profile\", name: \"Profile\" },\n    { href: \"/dashboard/settings\", name: \"Settings\" },\n    { href: \"/dashboard/uploads\", name: \"Uploads\" },\n    { href: \"/admin/summary\", name: \"Admin\" },\n  ];\n\n  const isDashboard =\n    user &&\n    (pathname.startsWith(\"/dashboard\") || pathname.startsWith(\"/admin\")); // todo: remove /admin when admin role is implemented\n  const navigation = isDashboard ? dashboardNavigation : mainNavigation;\n\n  const renderContent = () => (\n    <header\n      className={`\n        sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur\n        supports-[backdrop-filter]:bg-background/60\n      `}\n    >\n      <div\n        className={`\n          container mx-auto max-w-7xl px-4\n          sm:px-6\n          lg:px-8\n        `}\n      >\n        <div className=\"flex h-16 items-center justify-between\">\n          <div className=\"flex items-center gap-6\">\n            <Link className=\"flex items-center gap-2\" href=\"/\">\n              <span\n                className={cn(\n                  \"text-xl font-bold\",\n                  !isDashboard &&\n                    `\n                      bg-gradient-to-r from-primary to-primary/70 bg-clip-text\n                      tracking-tight text-transparent\n                    `,\n                )}\n              >\n                {SEO_CONFIG.name}\n              </span>\n            </Link>\n            <nav\n              className={`\n                hidden\n                md:flex\n              `}\n            >\n              <ul className=\"flex items-center gap-6\">\n                {isPending\n                  ? Array.from({ length: navigation.length }).map((_, i) => (\n                      <li key={i}>\n                        <Skeleton className=\"h-6 w-20\" />\n                      </li>\n                    ))\n                  : navigation.map((item) => {\n                      const isActive =\n                        pathname === item.href ||\n                        (item.href !== \"/\" && pathname?.startsWith(item.href));\n\n                      return (\n                        <li key={item.name}>\n                          <Link\n                            className={cn(\n                              `\n                                text-sm font-medium transition-colors\n                                hover:text-primary\n                              `,\n                              isActive\n                                ? \"font-semibold text-primary\"\n                                : \"text-muted-foreground\",\n                            )}\n                            href={item.href}\n                          >\n                            {item.name}\n                          </Link>\n                        </li>\n                      );\n                    })}\n              </ul>\n            </nav>\n          </div>\n\n          <div className=\"flex items-center gap-4\">\n            {!isDashboard &&\n              (isPending ? (\n                <Skeleton className={`h-9 w-9 rounded-full`} />\n              ) : (\n                <Cart />\n              ))}\n\n            {isPending ? (\n              <Skeleton className=\"h-9 w-9 rounded-full\" />\n            ) : (\n              <NotificationsWidget />\n            )}\n\n            {showAuth && (\n              <div\n                className={`\n                  hidden\n                  md:block\n                `}\n              >\n                {user ? (\n                  <HeaderUserDropdown\n                    isDashboard={!!isDashboard}\n                    userEmail={user.email}\n                    userImage={user.image}\n                    userName={user.name}\n                  />\n                ) : isPending ? (\n                  <Skeleton className=\"h-10 w-32\" />\n                ) : (\n                  <div className=\"flex items-center gap-2\">\n                    <Link href=\"/auth/sign-in\">\n                      <Button size=\"sm\" variant=\"ghost\">\n                        Log in\n                      </Button>\n                    </Link>\n                    <Link href=\"/auth/sign-up\">\n                      <Button size=\"sm\">Sign up</Button>\n                    </Link>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {!isDashboard &&\n              (isPending ? (\n                <Skeleton className={`h-9 w-9 rounded-full`} />\n              ) : (\n                <ThemeToggle />\n              ))}\n\n            {/* Mobile menu button */}\n            <Button\n              className=\"md:hidden\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n              size=\"icon\"\n              variant=\"ghost\"\n            >\n              {mobileMenuOpen ? (\n                <X className=\"h-5 w-5\" />\n              ) : (\n                <Menu className=\"h-5 w-5\" />\n              )}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {mobileMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"space-y-1 border-b px-4 py-3\">\n            {isPending\n              ? Array.from({ length: navigation.length }).map((_, i) => (\n                  <div className=\"py-2\" key={i}>\n                    <Skeleton className=\"h-6 w-32\" />\n                  </div>\n                ))\n              : navigation.map((item) => {\n                  const isActive =\n                    pathname === item.href ||\n                    (item.href !== \"/\" && pathname?.startsWith(item.href));\n\n                  return (\n                    <Link\n                      className={cn(\n                        \"block rounded-md px-3 py-2 text-base font-medium\",\n                        isActive\n                          ? \"bg-primary/10 text-primary\"\n                          : `\n                            text-foreground\n                            hover:bg-muted/50 hover:text-primary\n                          `,\n                      )}\n                      href={item.href}\n                      key={item.name}\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  );\n                })}\n          </div>\n\n          {showAuth && !user && (\n            <div className=\"space-y-1 border-b px-4 py-3\">\n              <Link\n                className={`\n                  block rounded-md px-3 py-2 text-base font-medium\n                  hover:bg-muted/50\n                `}\n                href=\"/auth/sign-in\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                Log in\n              </Link>\n              <Link\n                className={`\n                  block rounded-md bg-primary px-3 py-2 text-base font-medium\n                  text-primary-foreground\n                  hover:bg-primary/90\n                `}\n                href=\"/auth/sign-up\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                Sign up\n              </Link>\n            </div>\n          )}\n        </div>\n      )}\n    </header>\n  );\n\n  return renderContent();\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;AAuBO,SAAS,OAAO,EAAE,WAAW,IAAI,EAAe;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAK,MAAM;QAAO;QAC1B;YAAE,MAAM;YAAa,MAAM;QAAW;KACvC;IAED,MAAM,sBAAsB;QAC1B;YAAE,MAAM;YAAoB,MAAM;QAAQ;QAC1C;YAAE,MAAM;YAAsB,MAAM;QAAU;QAC9C;YAAE,MAAM;YAAuB,MAAM;QAAW;QAChD;YAAE,MAAM;YAAsB,MAAM;QAAU;QAC9C;YAAE,MAAM;YAAkB,MAAM;QAAQ;KACzC;IAED,MAAM,cACJ,QACA,CAAC,SAAS,UAAU,CAAC,iBAAiB,SAAS,UAAU,CAAC,SAAS,GAAG,qDAAqD;IAC7H,MAAM,aAAa,cAAc,sBAAsB;IAEvD,MAAM,gBAAgB,kBACpB,8OAAC;YACC,WAAW,CAAC;;;MAGZ,CAAC;;8BAED,8OAAC;oBACC,WAAW,CAAC;;;;QAIZ,CAAC;8BAED,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,WAAU;wCAA0B,MAAK;kDAC7C,cAAA,8OAAC;4CACC,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,qBACA,CAAC,eACC,CAAC;;;oBAGD,CAAC;sDAGJ,0GAAA,CAAA,aAAU,CAAC,IAAI;;;;;;;;;;;kDAGpB,8OAAC;wCACC,WAAW,CAAC;;;cAGZ,CAAC;kDAED,cAAA,8OAAC;4CAAG,WAAU;sDACX,YACG,MAAM,IAAI,CAAC;gDAAE,QAAQ,WAAW,MAAM;4CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChD,8OAAC;8DACC,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;mDADb;;;;4DAIX,WAAW,GAAG,CAAC,CAAC;gDACd,MAAM,WACJ,aAAa,KAAK,IAAI,IACrB,KAAK,IAAI,KAAK,OAAO,UAAU,WAAW,KAAK,IAAI;gDAEtD,qBACE,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;8BAGD,CAAC,EACD,WACI,+BACA;wDAEN,MAAM,KAAK,IAAI;kEAEd,KAAK,IAAI;;;;;;mDAbL,KAAK,IAAI;;;;;4CAiBtB;;;;;;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;oCACZ,CAAC,eACA,CAAC,0BACC,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAW,CAAC,oBAAoB,CAAC;;;;;6DAE3C,8OAAC,gIAAA,CAAA,OAAI;;;;4CACN;oCAEF,0BACC,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;6DAEpB,8OAAC,oKAAA,CAAA,sBAAmB;;;;;oCAGrB,0BACC,8OAAC;wCACC,WAAW,CAAC;;;gBAGZ,CAAC;kDAEA,qBACC,8OAAC,oJAAA,CAAA,qBAAkB;4CACjB,aAAa,CAAC,CAAC;4CACf,WAAW,KAAK,KAAK;4CACrB,WAAW,KAAK,KAAK;4CACrB,UAAU,KAAK,IAAI;;;;;mDAEnB,0BACF,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;iEAEpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,SAAQ;kEAAQ;;;;;;;;;;;8DAIpC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;kEAAK;;;;;;;;;;;;;;;;;;;;;;oCAO3B,CAAC,eACA,CAAC,0BACC,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAW,CAAC,oBAAoB,CAAC;;;;;6DAE3C,8OAAC,2IAAA,CAAA,cAAW;;;;4CACb;kDAGH,8OAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB,CAAC;wCAClC,MAAK;wCACL,SAAQ;kDAEP,+BACC,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAEb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQzB,gCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,YACG,MAAM,IAAI,CAAC;gCAAE,QAAQ,WAAW,MAAM;4BAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;mCADK;;;;4CAI7B,WAAW,GAAG,CAAC,CAAC;gCACd,MAAM,WACJ,aAAa,KAAK,IAAI,IACrB,KAAK,IAAI,KAAK,OAAO,UAAU,WAAW,KAAK,IAAI;gCAEtD,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCACH,WAAW,CAAA,GAAA,gHAAA,CAAA,KAAE,AAAD,EACV,oDACA,WACI,+BACA,CAAC;;;0BAGH,CAAC;oCAEL,MAAM,KAAK,IAAI;oCAEf,SAAS,IAAM,kBAAkB;8CAEhC,KAAK,IAAI;mCAHL,KAAK,IAAI;;;;;4BAMpB;;;;;;wBAGL,YAAY,CAAC,sBACZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,WAAW,CAAC;;;gBAGZ,CAAC;oCACD,MAAK;oCACL,SAAS,IAAM,kBAAkB;8CAClC;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,WAAW,CAAC;;;;gBAIZ,CAAC;oCACD,MAAK;oCACL,SAAS,IAAM,kBAAkB;8CAClC;;;;;;;;;;;;;;;;;;;;;;;;IAUb,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3707, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/components/theme-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ThemeProviderProps } from \"next-themes\";\n\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\";\nimport * as React from \"react\";\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAOO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 3731, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/ui/primitives/sonner.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useTheme } from \"next-themes\";\nimport { Toaster as Son<PERSON>, type ToasterProps } from \"sonner\";\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme();\n\n  return (\n    <Sonner\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-border\": \"var(--border)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n        } as React.CSSProperties\n      }\n      theme={theme as ToasterProps[\"theme\"]}\n      {...props}\n    />\n  );\n};\n\nexport { Toaster };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,WAAU;QACV,OACE;YACE,eAAe;YACf,mBAAmB;YACnB,iBAAiB;QACnB;QAEF,OAAO;QACN,GAAG,KAAK;;;;;;AAGf", "debugId": null}}]}