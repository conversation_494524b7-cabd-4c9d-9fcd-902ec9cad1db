module.exports = {

"[project]/node_modules/effect/dist/esm/internal/stm/versioned.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/** @internal */ __turbopack_context__.s({
    "Versioned": (()=>Versioned)
});
class Versioned {
    value;
    constructor(value){
        this.value = value;
    }
} //# sourceMappingURL=versioned.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/entry.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "commit": (()=>commit),
    "copy": (()=>copy),
    "isChanged": (()=>isChanged),
    "isInvalid": (()=>isInvalid),
    "isValid": (()=>isValid),
    "make": (()=>make),
    "unsafeGet": (()=>unsafeGet),
    "unsafeSet": (()=>unsafeSet)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$versioned$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/versioned.js [app-rsc] (ecmascript)");
;
const make = (ref, isNew)=>({
        ref,
        isNew,
        isChanged: false,
        expected: ref.versioned,
        newValue: ref.versioned.value
    });
const unsafeGet = (self)=>{
    return self.newValue;
};
const unsafeSet = (self, value)=>{
    self.isChanged = true;
    self.newValue = value;
};
const commit = (self)=>{
    self.ref.versioned = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$versioned$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Versioned"](self.newValue);
};
const copy = (self)=>({
        ref: self.ref,
        isNew: self.isNew,
        isChanged: self.isChanged,
        expected: self.expected,
        newValue: self.newValue
    });
const isValid = (self)=>{
    return self.ref.versioned === self.expected;
};
const isInvalid = (self)=>{
    return self.ref.versioned !== self.expected;
};
const isChanged = (self)=>{
    return self.isChanged;
}; //# sourceMappingURL=entry.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/journal.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "JournalAnalysisInvalid": (()=>JournalAnalysisInvalid),
    "JournalAnalysisReadOnly": (()=>JournalAnalysisReadOnly),
    "JournalAnalysisReadWrite": (()=>JournalAnalysisReadWrite),
    "addTodo": (()=>addTodo),
    "analyzeJournal": (()=>analyzeJournal),
    "collectTodos": (()=>collectTodos),
    "commitJournal": (()=>commitJournal),
    "execTodos": (()=>execTodos),
    "isInvalid": (()=>isInvalid),
    "isValid": (()=>isValid),
    "prepareResetJournal": (()=>prepareResetJournal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/entry.js [app-rsc] (ecmascript)");
;
const JournalAnalysisInvalid = "Invalid";
const JournalAnalysisReadWrite = "ReadWrite";
const JournalAnalysisReadOnly = "ReadOnly";
const commitJournal = (journal)=>{
    for (const entry of journal){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["commit"])(entry[1]);
    }
};
const analyzeJournal = (journal)=>{
    let val = JournalAnalysisReadOnly;
    for (const [, entry] of journal){
        val = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isInvalid"])(entry) ? JournalAnalysisInvalid : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isChanged"])(entry) ? JournalAnalysisReadWrite : val;
        if (val === JournalAnalysisInvalid) {
            return val;
        }
    }
    return val;
};
const prepareResetJournal = (journal)=>{
    const saved = new Map();
    for (const entry of journal){
        saved.set(entry[0], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["copy"])(entry[1]));
    }
    return ()=>{
        journal.clear();
        for (const entry of saved){
            journal.set(entry[0], entry[1]);
        }
    };
};
const collectTodos = (journal)=>{
    const allTodos = new Map();
    for (const [, entry] of journal){
        for (const todo of entry.ref.todos){
            allTodos.set(todo[0], todo[1]);
        }
        entry.ref.todos = new Map();
    }
    return allTodos;
};
const execTodos = (todos)=>{
    const todosSorted = Array.from(todos.entries()).sort((x, y)=>x[0] - y[0]);
    for (const [_, todo] of todosSorted){
        todo();
    }
};
const addTodo = (txnId, journal, todoEffect)=>{
    let added = false;
    for (const [, entry] of journal){
        if (!entry.ref.todos.has(txnId)) {
            entry.ref.todos.set(txnId, todoEffect);
            added = true;
        }
    }
    return added;
};
const isValid = (journal)=>{
    let valid = true;
    for (const [, entry] of journal){
        valid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isValid"])(entry);
        if (!valid) {
            return valid;
        }
    }
    return valid;
};
const isInvalid = (journal)=>{
    return !isValid(journal);
}; //# sourceMappingURL=journal.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/opCodes/stm.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/** @internal */ __turbopack_context__.s({
    "OP_DIE": (()=>OP_DIE),
    "OP_FAIL": (()=>OP_FAIL),
    "OP_INTERRUPT": (()=>OP_INTERRUPT),
    "OP_ON_FAILURE": (()=>OP_ON_FAILURE),
    "OP_ON_RETRY": (()=>OP_ON_RETRY),
    "OP_ON_SUCCESS": (()=>OP_ON_SUCCESS),
    "OP_PROVIDE": (()=>OP_PROVIDE),
    "OP_RETRY": (()=>OP_RETRY),
    "OP_SUCCEED": (()=>OP_SUCCEED),
    "OP_SYNC": (()=>OP_SYNC),
    "OP_TRACED": (()=>OP_TRACED),
    "OP_WITH_STM_RUNTIME": (()=>OP_WITH_STM_RUNTIME)
});
const OP_WITH_STM_RUNTIME = "WithSTMRuntime";
const OP_ON_FAILURE = "OnFailure";
const OP_ON_RETRY = "OnRetry";
const OP_ON_SUCCESS = "OnSuccess";
const OP_PROVIDE = "Provide";
const OP_SYNC = "Sync";
const OP_SUCCEED = "Succeed";
const OP_RETRY = "Retry";
const OP_FAIL = "Fail";
const OP_DIE = "Die";
const OP_INTERRUPT = "Interrupt";
const OP_TRACED = "Traced"; //# sourceMappingURL=stm.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/opCodes/tExit.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/** @internal */ __turbopack_context__.s({
    "OP_DIE": (()=>OP_DIE),
    "OP_FAIL": (()=>OP_FAIL),
    "OP_INTERRUPT": (()=>OP_INTERRUPT),
    "OP_RETRY": (()=>OP_RETRY),
    "OP_SUCCEED": (()=>OP_SUCCEED)
});
const OP_FAIL = "Fail";
const OP_DIE = "Die";
const OP_INTERRUPT = "Interrupt";
const OP_SUCCEED = "Succeed";
const OP_RETRY = "Retry"; //# sourceMappingURL=tExit.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/opCodes/tryCommit.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/** @internal */ __turbopack_context__.s({
    "OP_DONE": (()=>OP_DONE),
    "OP_SUSPEND": (()=>OP_SUSPEND)
});
const OP_DONE = "Done";
const OP_SUSPEND = "Suspend"; //# sourceMappingURL=tryCommit.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/opCodes/stmState.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/** @internal */ __turbopack_context__.s({
    "OP_DONE": (()=>OP_DONE),
    "OP_INTERRUPTED": (()=>OP_INTERRUPTED),
    "OP_RUNNING": (()=>OP_RUNNING)
});
const OP_DONE = "Done";
const OP_INTERRUPTED = "Interrupted";
const OP_RUNNING = "Running"; //# sourceMappingURL=stmState.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/stmState.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "STMStateTypeId": (()=>STMStateTypeId),
    "done": (()=>done),
    "fromTExit": (()=>fromTExit),
    "interrupted": (()=>interrupted),
    "isDone": (()=>isDone),
    "isInterrupted": (()=>isInterrupted),
    "isRunning": (()=>isRunning),
    "isSTMState": (()=>isSTMState),
    "running": (()=>running)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Equal.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Exit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Hash.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/opCodes/stmState.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/opCodes/tExit.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
/** @internal */ const STMStateSymbolKey = "effect/STM/State";
const STMStateTypeId = /*#__PURE__*/ Symbol.for(STMStateSymbolKey);
const isSTMState = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, STMStateTypeId);
const isRunning = (self)=>{
    return self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RUNNING"];
};
const isDone = (self)=>{
    return self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"];
};
const isInterrupted = (self)=>{
    return self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPTED"];
};
const done = (exit)=>{
    return {
        [STMStateTypeId]: STMStateTypeId,
        _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"],
        exit,
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] () {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(STMStateSymbolKey), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"])), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(exit)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cached"])(this));
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] (that) {
            return isSTMState(that) && that._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"] && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"])(exit, that.exit);
        }
    };
};
const interruptedHash = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(STMStateSymbolKey), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPTED"])), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])("interrupted")));
const interrupted = {
    [STMStateTypeId]: STMStateTypeId,
    _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPTED"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] () {
        return interruptedHash;
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] (that) {
        return isSTMState(that) && that._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPTED"];
    }
};
const runningHash = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(STMStateSymbolKey), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RUNNING"])), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])("running")));
const running = {
    [STMStateTypeId]: STMStateTypeId,
    _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RUNNING"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] () {
        return runningHash;
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] (that) {
        return isSTMState(that) && that._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RUNNING"];
    }
};
const fromTExit = (tExit)=>{
    switch(tExit._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAIL"]:
            {
                return done((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(tExit.error));
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DIE"]:
            {
                return done((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(tExit.defect));
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT"]:
            {
                return done((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(tExit.fiberId));
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCEED"]:
            {
                return done((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(tExit.value));
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RETRY"]:
            {
                throw new Error("BUG: STM.STMState.fromTExit - please report an issue at https://github.com/Effect-TS/effect/issues");
            }
    }
}; //# sourceMappingURL=stmState.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/tExit.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TExitTypeId": (()=>TExitTypeId),
    "die": (()=>die),
    "fail": (()=>fail),
    "interrupt": (()=>interrupt),
    "isDie": (()=>isDie),
    "isExit": (()=>isExit),
    "isFail": (()=>isFail),
    "isInterrupt": (()=>isInterrupt),
    "isRetry": (()=>isRetry),
    "isSuccess": (()=>isSuccess),
    "retry": (()=>retry),
    "succeed": (()=>succeed),
    "void": (()=>void_)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Equal.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Hash.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/opCodes/tExit.js [app-rsc] (ecmascript)");
;
;
;
;
;
/** @internal */ const TExitSymbolKey = "effect/TExit";
const TExitTypeId = /*#__PURE__*/ Symbol.for(TExitSymbolKey);
const variance = {
    /* c8 ignore next */ _A: (_)=>_,
    /* c8 ignore next */ _E: (_)=>_
};
const isExit = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, TExitTypeId);
const isFail = (self)=>{
    return self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAIL"];
};
const isDie = (self)=>{
    return self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DIE"];
};
const isInterrupt = (self)=>{
    return self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT"];
};
const isSuccess = (self)=>{
    return self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCEED"];
};
const isRetry = (self)=>{
    return self._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RETRY"];
};
const fail = (error)=>({
        [TExitTypeId]: variance,
        _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAIL"],
        error,
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] () {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(TExitSymbolKey), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAIL"])), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(error)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cached"])(this));
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] (that) {
            return isExit(that) && that._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAIL"] && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"])(error, that.error);
        }
    });
const die = (defect)=>({
        [TExitTypeId]: variance,
        _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DIE"],
        defect,
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] () {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(TExitSymbolKey), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DIE"])), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(defect)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cached"])(this));
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] (that) {
            return isExit(that) && that._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DIE"] && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"])(defect, that.defect);
        }
    });
const interrupt = (fiberId)=>({
        [TExitTypeId]: variance,
        _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT"],
        fiberId,
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] () {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(TExitSymbolKey), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT"])), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(fiberId)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cached"])(this));
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] (that) {
            return isExit(that) && that._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT"] && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"])(fiberId, that.fiberId);
        }
    });
const succeed = (value)=>({
        [TExitTypeId]: variance,
        _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCEED"],
        value,
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] () {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(TExitSymbolKey), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCEED"])), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(value)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cached"])(this));
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] (that) {
            return isExit(that) && that._tag === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCEED"] && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["equals"])(value, that.value);
        }
    });
const retryHash = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(TExitSymbolKey), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RETRY"])), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["combine"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hash"])("retry")));
const retry = {
    [TExitTypeId]: variance,
    _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RETRY"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] () {
        return retryHash;
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]] (that) {
        return isExit(that) && isRetry(that);
    }
};
const void_ = /*#__PURE__*/ succeed(undefined);
;
 //# sourceMappingURL=tExit.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/tryCommit.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "done": (()=>done),
    "suspend": (()=>suspend)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/opCodes/tryCommit.js [app-rsc] (ecmascript)");
;
const done = (exit)=>{
    return {
        _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"],
        exit
    };
};
const suspend = (journal)=>{
    return {
        _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUSPEND"],
        journal
    };
}; //# sourceMappingURL=tryCommit.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/txnId.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/** @internal */ __turbopack_context__.s({
    "make": (()=>make)
});
const txnCounter = {
    ref: 0
};
const make = ()=>{
    const newId = txnCounter.ref + 1;
    txnCounter.ref = newId;
    return newId;
}; //# sourceMappingURL=txnId.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/core.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "STMDriver": (()=>STMDriver),
    "STMTypeId": (()=>STMTypeId),
    "catchAll": (()=>catchAll),
    "commit": (()=>commit),
    "context": (()=>context),
    "contextWith": (()=>contextWith),
    "contextWithSTM": (()=>contextWithSTM),
    "die": (()=>die),
    "dieMessage": (()=>dieMessage),
    "dieSync": (()=>dieSync),
    "effect": (()=>effect),
    "ensuring": (()=>ensuring),
    "fail": (()=>fail),
    "failSync": (()=>failSync),
    "flatMap": (()=>flatMap),
    "interrupt": (()=>interrupt),
    "interruptAs": (()=>interruptAs),
    "isSTM": (()=>isSTM),
    "map": (()=>map),
    "mapInputContext": (()=>mapInputContext),
    "matchSTM": (()=>matchSTM),
    "orTry": (()=>orTry),
    "retry": (()=>retry),
    "succeed": (()=>succeed),
    "sync": (()=>sync),
    "unsafeAtomically": (()=>unsafeAtomically),
    "withSTMRuntime": (()=>withSTMRuntime),
    "zip": (()=>zip),
    "zipLeft": (()=>zipLeft),
    "zipRight": (()=>zipRight),
    "zipWith": (()=>zipWith)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Cause.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Context.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Either.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Equal.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Exit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/FiberRef.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Hash.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Pipeable.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/core-stream.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/core.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/effectable.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/opCodes/effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$singleShotGen$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/singleShotGen.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$sink$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/sink.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/journal.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/opCodes/stm.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/opCodes/tExit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/opCodes/tryCommit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/stmState.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/tExit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/tryCommit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$txnId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/txnId.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
/** @internal */ const STMSymbolKey = "effect/STM";
const STMTypeId = /*#__PURE__*/ Symbol.for(STMSymbolKey);
const stmVariance = {
    /* c8 ignore next */ _R: (_)=>_,
    /* c8 ignore next */ _E: (_)=>_,
    /* c8 ignore next */ _A: (_)=>_
};
/** @internal */ class STMPrimitive {
    effect_instruction_i0;
    _op = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$opCodes$2f$effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_COMMIT"];
    effect_instruction_i1 = undefined;
    effect_instruction_i2 = undefined;
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EffectTypeId"]];
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["StreamTypeId"]];
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$sink$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SinkTypeId"]];
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ChannelTypeId"]];
    get [STMTypeId]() {
        return stmVariance;
    }
    constructor(effect_instruction_i0){
        this.effect_instruction_i0 = effect_instruction_i0;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["EffectTypeId"]] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effectVariance"];
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$effectable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["StreamTypeId"]] = stmVariance;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$sink$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SinkTypeId"]] = stmVariance;
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2d$stream$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ChannelTypeId"]] = stmVariance;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Equal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]](that) {
        return this === that;
    }
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["symbol"]]() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cached"])(this, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Hash$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["random"])(this));
    }
    [Symbol.iterator]() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$singleShotGen$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SingleShotGen"](new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["YieldWrap"](this));
    }
    commit() {
        return unsafeAtomically(this, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constVoid"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constVoid"]);
    }
    pipe() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
}
const isSTM = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, STMTypeId);
const commit = (self)=>unsafeAtomically(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constVoid"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constVoid"]);
const unsafeAtomically = (self, onDone, onInterrupt)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withFiberRuntime"])((state)=>{
        const fiberId = state.id();
        const env = state.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentContext"]);
        const scheduler = state.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentScheduler"]);
        const priority = state.getFiberRef(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$FiberRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["currentSchedulingPriority"]);
        const commitResult = tryCommitSync(fiberId, self, env, scheduler, priority);
        switch(commitResult._tag){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"]:
                {
                    onDone(commitResult.exit);
                    return commitResult.exit;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUSPEND"]:
                {
                    const txnId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$txnId$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])();
                    const state = {
                        value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["running"]
                    };
                    const effect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["async"])((k)=>tryCommitAsync(fiberId, self, txnId, state, env, scheduler, priority, k));
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptibleMask"])((restore)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(restore(effect), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAllCause"])((cause)=>{
                            let currentState = state.value;
                            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isRunning"])(currentState)) {
                                state.value = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupted"];
                            }
                            currentState = state.value;
                            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isDone"])(currentState)) {
                                onDone(currentState.exit);
                                return currentState.exit;
                            }
                            onInterrupt();
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause);
                        })));
                }
        }
    });
/** @internal */ const tryCommit = (fiberId, stm, state, env, scheduler, priority)=>{
    const journal = new Map();
    const tExit = new STMDriver(stm, journal, fiberId, env).run();
    const analysis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["analyzeJournal"])(journal);
    if (analysis === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JournalAnalysisReadWrite"]) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["commitJournal"])(journal);
    } else if (analysis === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JournalAnalysisInvalid"]) {
        throw new Error("BUG: STM.TryCommit.tryCommit - please report an issue at https://github.com/Effect-TS/effect/issues");
    }
    switch(tExit._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCEED"]:
            {
                state.value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromTExit"])(tExit);
                return completeTodos((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(tExit.value), journal, scheduler, priority);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAIL"]:
            {
                state.value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromTExit"])(tExit);
                const cause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(tExit.error);
                return completeTodos((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause), journal, scheduler, priority);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DIE"]:
            {
                state.value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromTExit"])(tExit);
                const cause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(tExit.defect);
                return completeTodos((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause), journal, scheduler, priority);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT"]:
            {
                state.value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromTExit"])(tExit);
                const cause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(fiberId);
                return completeTodos((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause), journal, scheduler, priority);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RETRY"]:
            {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(journal);
            }
    }
};
/** @internal */ const tryCommitSync = (fiberId, stm, env, scheduler, priority)=>{
    const journal = new Map();
    const tExit = new STMDriver(stm, journal, fiberId, env).run();
    const analysis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["analyzeJournal"])(journal);
    if (analysis === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JournalAnalysisReadWrite"] && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSuccess"])(tExit)) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["commitJournal"])(journal);
    } else if (analysis === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JournalAnalysisInvalid"]) {
        throw new Error("BUG: STM.TryCommit.tryCommitSync - please report an issue at https://github.com/Effect-TS/effect/issues");
    }
    switch(tExit._tag){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCEED"]:
            {
                return completeTodos((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(tExit.value), journal, scheduler, priority);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAIL"]:
            {
                const cause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(tExit.error);
                return completeTodos((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause), journal, scheduler, priority);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DIE"]:
            {
                const cause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(tExit.defect);
                return completeTodos((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause), journal, scheduler, priority);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT"]:
            {
                const cause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(fiberId);
                return completeTodos((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause), journal, scheduler, priority);
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RETRY"]:
            {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(journal);
            }
    }
};
/** @internal */ const tryCommitAsync = (fiberId, self, txnId, state, context, scheduler, priority, k)=>{
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isRunning"])(state.value)) {
        const result = tryCommit(fiberId, self, state, context, scheduler, priority);
        switch(result._tag){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DONE"]:
                {
                    completeTryCommit(result.exit, k);
                    break;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUSPEND"]:
                {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["addTodo"])(txnId, result.journal, ()=>tryCommitAsync(fiberId, self, txnId, state, context, scheduler, priority, k));
                    break;
                }
        }
    }
};
/** @internal */ const completeTodos = (exit, journal, scheduler, priority)=>{
    const todos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["collectTodos"])(journal);
    if (todos.size > 0) {
        scheduler.scheduleTask(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["execTodos"])(todos), priority);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tryCommit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["done"])(exit);
};
/** @internal */ const completeTryCommit = (exit, k)=>{
    k(exit);
};
const context = ()=>effect((_, __, env)=>env);
const contextWith = (f)=>map(context(), f);
const contextWithSTM = (f)=>flatMap(context(), f);
class STMDriver {
    self;
    journal;
    fiberId;
    contStack = [];
    env;
    constructor(self, journal, fiberId, r0){
        this.self = self;
        this.journal = journal;
        this.fiberId = fiberId;
        this.env = r0;
    }
    getEnv() {
        return this.env;
    }
    pushStack(cont) {
        this.contStack.push(cont);
    }
    popStack() {
        return this.contStack.pop();
    }
    nextSuccess() {
        let current = this.popStack();
        while(current !== undefined && current.effect_instruction_i0 !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_SUCCESS"]){
            current = this.popStack();
        }
        return current;
    }
    nextFailure() {
        let current = this.popStack();
        while(current !== undefined && current.effect_instruction_i0 !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_FAILURE"]){
            current = this.popStack();
        }
        return current;
    }
    nextRetry() {
        let current = this.popStack();
        while(current !== undefined && current.effect_instruction_i0 !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_RETRY"]){
            current = this.popStack();
        }
        return current;
    }
    run() {
        let curr = this.self;
        let exit = undefined;
        while(exit === undefined && curr !== undefined){
            try {
                const current = curr;
                if (current) {
                    switch(current._op){
                        case "Tag":
                            {
                                curr = effect((_, __, env)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(env, current));
                                break;
                            }
                        case "Left":
                            {
                                curr = fail(current.left);
                                break;
                            }
                        case "None":
                            {
                                curr = fail(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NoSuchElementException"]());
                                break;
                            }
                        case "Right":
                            {
                                curr = succeed(current.right);
                                break;
                            }
                        case "Some":
                            {
                                curr = succeed(current.value);
                                break;
                            }
                        case "Commit":
                            {
                                switch(current.effect_instruction_i0){
                                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DIE"]:
                                        {
                                            exit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>current.effect_instruction_i1()));
                                            break;
                                        }
                                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAIL"]:
                                        {
                                            const cont = this.nextFailure();
                                            if (cont === undefined) {
                                                exit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>current.effect_instruction_i1()));
                                            } else {
                                                curr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>cont.effect_instruction_i2((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>current.effect_instruction_i1())));
                                            }
                                            break;
                                        }
                                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RETRY"]:
                                        {
                                            const cont = this.nextRetry();
                                            if (cont === undefined) {
                                                exit = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"];
                                            } else {
                                                curr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>cont.effect_instruction_i2());
                                            }
                                            break;
                                        }
                                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT"]:
                                        {
                                            exit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupt"])(this.fiberId);
                                            break;
                                        }
                                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_WITH_STM_RUNTIME"]:
                                        {
                                            curr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>current.effect_instruction_i1(this));
                                            break;
                                        }
                                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_SUCCESS"]:
                                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_FAILURE"]:
                                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_RETRY"]:
                                        {
                                            this.pushStack(current);
                                            curr = current.effect_instruction_i1;
                                            break;
                                        }
                                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PROVIDE"]:
                                        {
                                            const env = this.env;
                                            this.env = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>current.effect_instruction_i2(env));
                                            curr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(current.effect_instruction_i1, ensuring(sync(()=>this.env = env)));
                                            break;
                                        }
                                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCEED"]:
                                        {
                                            const value = current.effect_instruction_i1;
                                            const cont = this.nextSuccess();
                                            if (cont === undefined) {
                                                exit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(value);
                                            } else {
                                                curr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>cont.effect_instruction_i2(value));
                                            }
                                            break;
                                        }
                                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SYNC"]:
                                        {
                                            const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>current.effect_instruction_i1());
                                            const cont = this.nextSuccess();
                                            if (cont === undefined) {
                                                exit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tExit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(value);
                                            } else {
                                                curr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["internalCall"])(()=>cont.effect_instruction_i2(value));
                                            }
                                            break;
                                        }
                                }
                                break;
                            }
                    }
                }
            } catch (e) {
                curr = die(e);
            }
        }
        return exit;
    }
}
const catchAll = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const stm = new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_FAILURE"]);
    stm.effect_instruction_i1 = self;
    stm.effect_instruction_i2 = f;
    return stm;
});
const mapInputContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const stm = new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_PROVIDE"]);
    stm.effect_instruction_i1 = self;
    stm.effect_instruction_i2 = f;
    return stm;
});
const die = (defect)=>dieSync(()=>defect);
const dieMessage = (message)=>dieSync(()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RuntimeException"](message));
const dieSync = (evaluate)=>{
    const stm = new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DIE"]);
    stm.effect_instruction_i1 = evaluate;
    return stm;
};
const effect = (f)=>withSTMRuntime((_)=>succeed(f(_.journal, _.fiberId, _.getEnv())));
const ensuring = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, finalizer)=>matchSTM(self, {
        onFailure: (e)=>zipRight(finalizer, fail(e)),
        onSuccess: (a)=>zipRight(finalizer, succeed(a))
    }));
const fail = (error)=>failSync(()=>error);
const failSync = (evaluate)=>{
    const stm = new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_FAIL"]);
    stm.effect_instruction_i1 = evaluate;
    return stm;
};
const flatMap = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>{
    const stm = new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_SUCCESS"]);
    stm.effect_instruction_i1 = self;
    stm.effect_instruction_i2 = f;
    return stm;
});
const matchSTM = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onFailure, onSuccess })=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"]), catchAll((e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(onFailure(e), map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"]))), flatMap((either)=>{
        switch(either._tag){
            case "Left":
                {
                    return succeed(either.left);
                }
            case "Right":
                {
                    return onSuccess(either.right);
                }
        }
    })));
const withSTMRuntime = (f)=>{
    const stm = new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_WITH_STM_RUNTIME"]);
    stm.effect_instruction_i1 = f;
    return stm;
};
const interrupt = /*#__PURE__*/ withSTMRuntime((_)=>{
    const stm = new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT"]);
    stm.effect_instruction_i1 = _.fiberId;
    return stm;
});
const interruptAs = (fiberId)=>{
    const stm = new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_INTERRUPT"]);
    stm.effect_instruction_i1 = fiberId;
    return stm;
};
const map = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, flatMap((a)=>sync(()=>f(a)))));
const orTry = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>{
    const stm = new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_ON_RETRY"]);
    stm.effect_instruction_i1 = self;
    stm.effect_instruction_i2 = that;
    return stm;
});
const retry = /*#__PURE__*/ new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_RETRY"]);
const succeed = (value)=>{
    const stm = new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SUCCEED"]);
    stm.effect_instruction_i1 = value;
    return stm;
};
const sync = (evaluate)=>{
    const stm = new STMPrimitive(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SYNC"]);
    stm.effect_instruction_i1 = evaluate;
    return stm;
};
const zip = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, zipWith(that, (a, a1)=>[
            a,
            a1
        ])));
const zipLeft = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, flatMap((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(that, map(()=>a)))));
const zipRight = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, flatMap(()=>that)));
const zipWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, that, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, flatMap((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(that, map((b)=>f(a, b)))))); //# sourceMappingURL=core.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/opCodes/strategy.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/** @internal */ __turbopack_context__.s({
    "OP_BACKPRESSURE_STRATEGY": (()=>OP_BACKPRESSURE_STRATEGY),
    "OP_DROPPING_STRATEGY": (()=>OP_DROPPING_STRATEGY),
    "OP_SLIDING_STRATEGY": (()=>OP_SLIDING_STRATEGY)
});
const OP_BACKPRESSURE_STRATEGY = "BackPressure";
const OP_DROPPING_STRATEGY = "Dropping";
const OP_SLIDING_STRATEGY = "Sliding"; //# sourceMappingURL=strategy.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/stm.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "acquireUseRelease": (()=>acquireUseRelease),
    "all": (()=>all),
    "as": (()=>as),
    "asSome": (()=>asSome),
    "asSomeError": (()=>asSomeError),
    "asVoid": (()=>asVoid),
    "attempt": (()=>attempt),
    "bind": (()=>bind),
    "bindTo": (()=>bindTo),
    "catchSome": (()=>catchSome),
    "catchTag": (()=>catchTag),
    "catchTags": (()=>catchTags),
    "check": (()=>check),
    "collect": (()=>collect),
    "collectSTM": (()=>collectSTM),
    "commitEither": (()=>commitEither),
    "cond": (()=>cond),
    "either": (()=>either),
    "eventually": (()=>eventually),
    "every": (()=>every),
    "exists": (()=>exists),
    "fiberId": (()=>fiberId),
    "filter": (()=>filter),
    "filterNot": (()=>filterNot),
    "filterOrDie": (()=>filterOrDie),
    "filterOrDieMessage": (()=>filterOrDieMessage),
    "filterOrElse": (()=>filterOrElse),
    "filterOrFail": (()=>filterOrFail),
    "flatten": (()=>flatten),
    "flip": (()=>flip),
    "flipWith": (()=>flipWith),
    "forEach": (()=>forEach),
    "fromEither": (()=>fromEither),
    "fromOption": (()=>fromOption),
    "gen": (()=>gen),
    "head": (()=>head),
    "if_": (()=>if_),
    "ignore": (()=>ignore),
    "isFailure": (()=>isFailure),
    "isSuccess": (()=>isSuccess),
    "iterate": (()=>iterate),
    "let_": (()=>let_),
    "loop": (()=>loop),
    "mapAttempt": (()=>mapAttempt),
    "mapBoth": (()=>mapBoth),
    "mapError": (()=>mapError),
    "match": (()=>match),
    "merge": (()=>merge),
    "mergeAll": (()=>mergeAll),
    "negate": (()=>negate),
    "none": (()=>none),
    "option": (()=>option),
    "orDie": (()=>orDie),
    "orDieWith": (()=>orDieWith),
    "orElse": (()=>orElse),
    "orElseEither": (()=>orElseEither),
    "orElseFail": (()=>orElseFail),
    "orElseOptional": (()=>orElseOptional),
    "orElseSucceed": (()=>orElseSucceed),
    "partition": (()=>partition),
    "provideContext": (()=>provideContext),
    "provideService": (()=>provideService),
    "provideServiceSTM": (()=>provideServiceSTM),
    "provideSomeContext": (()=>provideSomeContext),
    "reduce": (()=>reduce),
    "reduceAll": (()=>reduceAll),
    "reduceRight": (()=>reduceRight),
    "refineOrDie": (()=>refineOrDie),
    "refineOrDieWith": (()=>refineOrDieWith),
    "reject": (()=>reject),
    "rejectSTM": (()=>rejectSTM),
    "repeatUntil": (()=>repeatUntil),
    "repeatWhile": (()=>repeatWhile),
    "replicate": (()=>replicate),
    "replicateSTM": (()=>replicateSTM),
    "replicateSTMDiscard": (()=>replicateSTMDiscard),
    "retryUntil": (()=>retryUntil),
    "retryWhile": (()=>retryWhile),
    "some": (()=>some),
    "succeedNone": (()=>succeedNone),
    "succeedSome": (()=>succeedSome),
    "summarized": (()=>summarized),
    "suspend": (()=>suspend),
    "tap": (()=>tap),
    "tapBoth": (()=>tapBoth),
    "tapError": (()=>tapError),
    "try_": (()=>try_),
    "unless": (()=>unless),
    "unlessSTM": (()=>unlessSTM),
    "unsome": (()=>unsome),
    "validateAll": (()=>validateAll),
    "validateFirst": (()=>validateFirst),
    "void": (()=>void_),
    "when": (()=>when),
    "whenSTM": (()=>whenSTM)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Array.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Cause.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Chunk.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Context.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Either.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Exit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Option.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/core.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/core.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/journal.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/stmState.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const acquireUseRelease = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (acquire, use, release)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uninterruptibleMask"])((restore)=>{
        let state = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["running"];
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(restore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeAtomically"])(acquire, (exit)=>{
            state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["done"])(exit);
        }, ()=>{
            state = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interrupted"];
        })), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])({
            onFailure: (cause)=>{
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stmState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isDone"])(state) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Exit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSuccess"])(state.exit)) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(release(state.exit.value), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])({
                        onFailure: (cause2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"])(cause, cause2)),
                        onSuccess: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause)
                    }));
                }
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause);
            },
            onSuccess: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(restore(use(a)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])({
                    onFailure: (cause)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(release(a), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchCauseEffect"])({
                            onFailure: (cause2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Cause$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parallel"])(cause, cause2)),
                            onSuccess: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failCause"])(cause)
                        })),
                    onSuccess: (a2)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(release(a), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["as"])(a2))
                }))
        }));
    }));
const as = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(()=>value)));
const asSome = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"]));
const asSomeError = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, mapError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"]));
const asVoid = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constVoid"]));
const attempt = (evaluate)=>suspend(()=>{
        try {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(evaluate());
        } catch (defect) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(defect);
        }
    });
const bind = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, tag, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (k)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(f(k), (a)=>({
                ...k,
                [tag]: a
            }))));
const bindTo = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, tag)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(self, (a)=>({
            [tag]: a
        })));
const let_ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, tag, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(self, (k)=>({
            ...k,
            [tag]: f(k)
        })));
const catchSome = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAll"])(self, (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getOrElse"])(pf(e), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(e))));
const catchTag = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, k, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAll"])(self, (e)=>{
        if ("_tag" in e && e["_tag"] === k) {
            return f(e);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(e);
    }));
const catchTags = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, cases)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAll"])(self, (e)=>{
        const keys = Object.keys(cases);
        if ("_tag" in e && keys.includes(e["_tag"])) {
            return cases[e["_tag"]](e);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(e);
    }));
const check = (predicate)=>suspend(()=>predicate() ? void_ : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"]);
const collect = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>collectSTM(self, (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(pf(a), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])));
const collectSTM = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onSuccess: (a)=>{
            const option = pf(a);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSome"])(option) ? option.value : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"];
        }
    }));
const commitEither = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatten"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["commit"])(either(self)));
const cond = (predicate, error, result)=>{
    return suspend(()=>predicate() ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(result) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failSync"])(error));
};
const either = (self)=>match(self, {
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"],
        onSuccess: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"]
    });
const eventually = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: ()=>eventually(self),
        onSuccess: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
    });
const every = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (iterable, predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>iterable[Symbol.iterator]()), (iterator)=>{
        const loop = suspend(()=>{
            const next = iterator.next();
            if (next.done) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(true);
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(predicate(next.value), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((bool)=>bool ? loop : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(bool)));
        });
        return loop;
    })));
const exists = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (iterable, predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>iterable[Symbol.iterator]()), (iterator)=>{
        const loop = suspend(()=>{
            const next = iterator.next();
            if (next.done) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(false);
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(predicate(next.value), (bool)=>bool ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(bool) : loop);
        });
        return loop;
    }));
const fiberId = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effect"])((_, fiberId)=>fiberId);
const filter = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (iterable, predicate)=>Array.from(iterable).reduce((acc, curr)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipWith"])(predicate(curr), (as, p)=>{
            if (p) {
                as.push(curr);
                return as;
            }
            return as;
        })), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])([])));
const filterNot = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (iterable, predicate)=>filter(iterable, (a)=>negate(predicate(a))));
const filterOrDie = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, predicate, defect)=>filterOrElse(self, predicate, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dieSync"])(defect)));
const filterOrDieMessage = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, predicate, message)=>filterOrElse(self, predicate, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dieMessage"])(message)));
const filterOrElse = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, predicate, orElse)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (a)=>predicate(a) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(a) : orElse(a)));
const filterOrFail = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, predicate, orFailWith)=>filterOrElse(self, predicate, (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failSync"])(()=>orFailWith(a))));
const flatten = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]);
const flip = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"],
        onSuccess: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"]
    });
const flipWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>flip(f(flip(self))));
const match = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onFailure, onSuccess })=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(onFailure(e)),
        onSuccess: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(onSuccess(a))
    }));
const forEach = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isIterable"])(args[0]), (iterable, f, options)=>{
    if (options?.discard) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(()=>iterable[Symbol.iterator]()), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((iterator)=>{
            const loop = suspend(()=>{
                const next = iterator.next();
                if (next.done) {
                    return void_;
                }
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(f(next.value), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>loop));
            });
            return loop;
        }));
    }
    return suspend(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromIterable"])(iterable).reduce((acc, curr)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipWith"])(acc, f(curr), (array, elem)=>{
                array.push(elem);
                return array;
            }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])([])));
});
const fromEither = (either)=>{
    switch(either._tag){
        case "Left":
            {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(either.left);
            }
        case "Right":
            {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(either.right);
            }
    }
};
const fromOption = (option)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(option, {
        onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])()),
        onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
    });
const gen = (...args)=>suspend(()=>{
        const f = args.length === 1 ? args[0] : args[1].bind(args[0]);
        const iterator = f(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"]);
        const state = iterator.next();
        const run = (state)=>state.done ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(state.value) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["yieldWrapGet"])(state.value), (val)=>run(iterator.next(val)));
        return run(state);
    });
const head = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])({
        onFailure: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(e)),
        onSuccess: (a)=>{
            const i = a[Symbol.iterator]();
            const res = i.next();
            if (res.done) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])());
            } else {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(res.value);
            }
        }
    }));
const if_ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])((args)=>typeof args[0] === "boolean" || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSTM"])(args[0]), (self, { onFalse, onTrue })=>{
    if (typeof self === "boolean") {
        return self ? onTrue : onFalse;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (bool)=>bool ? onTrue : onFalse);
});
const ignore = (self)=>match(self, {
        onFailure: ()=>void_,
        onSuccess: ()=>void_
    });
const isFailure = (self)=>match(self, {
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constTrue"],
        onSuccess: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constFalse"]
    });
const isSuccess = (self)=>match(self, {
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constFalse"],
        onSuccess: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["constTrue"]
    });
const iterate = (initial, options)=>iterateLoop(initial, options.while, options.body);
const iterateLoop = (initial, cont, body)=>{
    if (cont(initial)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(body(initial), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((z)=>iterateLoop(z, cont, body)));
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(initial);
};
const loop = (initial, options)=>options.discard ? loopDiscardLoop(initial, options.while, options.step, options.body) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(loopLoop(initial, options.while, options.step, options.body), (a)=>Array.from(a));
const loopLoop = (initial, cont, inc, body)=>{
    if (cont(initial)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(body(initial), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(loopLoop(inc(initial), cont, inc, body), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["append"])(a)))));
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])());
};
const loopDiscardLoop = (initial, cont, inc, body)=>{
    if (cont(initial)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(body(initial), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(()=>loopDiscardLoop(inc(initial), cont, inc, body)));
    }
    return void_;
};
const mapAttempt = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(e),
        onSuccess: (a)=>attempt(()=>f(a))
    }));
const mapBoth = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onFailure, onSuccess })=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(onFailure(e)),
        onSuccess: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(onSuccess(a))
    }));
const mapError = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(f(e)),
        onSuccess: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
    }));
const merge = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(e),
        onSuccess: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
    });
const mergeAll = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (iterable, zero, f)=>suspend(()=>Array.from(iterable).reduce((acc, curr)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipWith"])(curr, f)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(zero))));
const negate = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((b)=>!b));
const none = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(e)),
        onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
            onNone: ()=>void_,
            onSome: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])())
        })
    });
const option = (self)=>match(self, {
        onFailure: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])(),
        onSuccess: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"]
    });
const orDie = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, orDieWith(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]));
const orDieWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, mapError(f), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAll"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])));
const orElse = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effect"])((journal)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$journal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["prepareResetJournal"])(journal)), (reset)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["orTry"])(self, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(reset), that)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAll"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(reset), that)))));
const orElseEither = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>orElse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(self, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["left"]), ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(that(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["right"])));
const orElseFail = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, error)=>orElse(self, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["failSync"])(error)));
const orElseOptional = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, that)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAll"])(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
        onNone: that,
        onSome: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(e))
    })));
const orElseSucceed = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>orElse(self, ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(value)));
const provideContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, env)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapInputContext"])(self, (_)=>env));
const provideSomeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, context)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["mapInputContext"])(self, (parent)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["merge"])(parent, context)));
const provideService = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, tag, resource)=>provideServiceSTM(self, tag, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(resource)));
const provideServiceSTM = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, tag, stm)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["contextWithSTM"])((env)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(stm, (service)=>provideContext(self, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(env, tag, service)))));
const reduce = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (iterable, zero, f)=>suspend(()=>Array.from(iterable).reduce((acc, curr)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((s)=>f(s, curr))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(zero))));
const reduceAll = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (iterable, initial, f)=>suspend(()=>Array.from(iterable).reduce((acc, curr)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipWith"])(curr, f)), initial)));
const reduceRight = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (iterable, zero, f)=>suspend(()=>Array.from(iterable).reduceRight((acc, curr)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((s)=>f(s, curr))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(zero))));
const refineOrDie = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>refineOrDieWith(self, pf, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]));
const refineOrDieWith = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, pf, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["catchAll"])(self, (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(pf(e), {
            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["die"])(f(e)),
            onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"]
        })));
const reject = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>rejectSTM(self, (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(pf(a), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])));
const rejectSTM = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, pf)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(pf(a), {
            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(a),
            onSome: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])
        })));
const repeatUntil = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, predicate)=>repeatUntilLoop(self, predicate));
const repeatUntilLoop = (self, predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (a)=>predicate(a) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(a) : repeatUntilLoop(self, predicate));
const repeatWhile = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, predicate)=>repeatWhileLoop(self, predicate));
const repeatWhileLoop = (self, predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (a)=>predicate(a) ? repeatWhileLoop(self, predicate) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(a)));
const replicate = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, n)=>Array.from({
        length: n
    }, ()=>self));
const replicateSTM = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, n)=>all(replicate(self, n)));
const replicateSTMDiscard = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, n)=>all(replicate(self, n), {
        discard: true
    }));
const retryUntil = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onSuccess: (a)=>predicate(a) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(a) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"]
    }));
const retryWhile = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"],
        onSuccess: (a)=>!predicate(a) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(a) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"]
    }));
const partition = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (elements, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(forEach(elements, (a)=>either(f(a))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((as)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["partitionMap"])(as, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]))));
const some = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(e)),
        onSuccess: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])()),
            onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
        })
    });
const all = (input, options)=>{
    if (Symbol.iterator in input) {
        return forEach(input, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"], options);
    } else if (options?.discard) {
        return forEach(Object.values(input), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"], options);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(forEach(Object.entries(input), ([_, e])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(e, (a)=>[
                _,
                a
            ])), (values)=>{
        const res = {};
        for (const [k, v] of values){
            ;
            res[k] = v;
        }
        return res;
    });
};
const succeedNone = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])());
const succeedSome = (value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(value));
const summarized = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, summary, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(summary, (start)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(summary, (end)=>[
                    f(start, end),
                    value
                ]))));
const suspend = (evaluate)=>flatten((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sync"])(evaluate));
const tap = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self, (a)=>as(f(a), a)));
const tapBoth = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, { onFailure, onSuccess })=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(onFailure(e), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(e))),
        onSuccess: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(onSuccess(a), as(a))
    }));
const tapError = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["zipRight"])(f(e), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(e)),
        onSuccess: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"]
    }));
const try_ = (arg)=>{
    const evaluate = typeof arg === "function" ? arg : arg.try;
    return suspend(()=>{
        try {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(evaluate());
        } catch (error) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])("catch" in arg ? arg.catch(error) : error);
        }
    });
};
/** @internal */ const void_ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(void 0);
;
const unless = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, predicate)=>suspend(()=>predicate() ? succeedNone : asSome(self)));
const unlessSTM = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(predicate, (bool)=>bool ? succeedNone : asSome(self)));
const unsome = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["matchSTM"])(self, {
        onFailure: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])({
            onNone: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])()),
            onSome: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"]
        }),
        onSuccess: (a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(a))
    });
const validateAll = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (elements, f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(partition(elements, f), ([errors, values])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isNonEmptyArray"])(errors) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fail"])(errors) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(values)));
const validateFirst = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (elements, f)=>flip(forEach(elements, (a)=>flip(f(a)))));
const when = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, predicate)=>suspend(()=>predicate() ? asSome(self) : succeedNone));
const whenSTM = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(predicate, (bool)=>bool ? asSome(self) : succeedNone)); //# sourceMappingURL=stm.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/tRef.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TRefImpl": (()=>TRefImpl),
    "TRefTypeId": (()=>TRefTypeId),
    "get": (()=>get),
    "getAndSet": (()=>getAndSet),
    "getAndUpdate": (()=>getAndUpdate),
    "getAndUpdateSome": (()=>getAndUpdateSome),
    "make": (()=>make),
    "modify": (()=>modify),
    "modifySome": (()=>modifySome),
    "set": (()=>set),
    "setAndGet": (()=>setAndGet),
    "tRefVariance": (()=>tRefVariance),
    "unsafeGet": (()=>unsafeGet),
    "unsafeSet": (()=>unsafeSet),
    "update": (()=>update),
    "updateAndGet": (()=>updateAndGet),
    "updateSome": (()=>updateSome),
    "updateSomeAndGet": (()=>updateSomeAndGet)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Option.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Pipeable.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/core.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/entry.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$versioned$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/versioned.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
/** @internal */ const TRefSymbolKey = "effect/TRef";
const TRefTypeId = /*#__PURE__*/ Symbol.for(TRefSymbolKey);
const tRefVariance = {
    /* c8 ignore next */ _A: (_)=>_
};
class TRefImpl {
    [TRefTypeId] = tRefVariance;
    /** @internal */ todos;
    /** @internal */ versioned;
    constructor(value){
        this.versioned = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$versioned$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Versioned"](value);
        this.todos = new Map();
    }
    modify(f) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effect"])((journal)=>{
            const entry = getOrMakeEntry(this, journal);
            const [retValue, newValue] = f((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(entry));
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(entry, newValue);
            return retValue;
        });
    }
    pipe() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Pipeable$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipeArguments"])(this, arguments);
    }
}
const make = (value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effect"])((journal)=>{
        const ref = new TRefImpl(value);
        journal.set(ref, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(ref, true));
        return ref;
    });
const get = (self)=>self.modify((a)=>[
            a,
            a
        ]);
const set = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>self.modify(()=>[
            void 0,
            value
        ]));
const getAndSet = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>self.modify((a)=>[
            a,
            value
        ]));
const getAndUpdate = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>self.modify((a)=>[
            a,
            f(a)
        ]));
const getAndUpdateSome = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>self.modify((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(f(a), {
            onNone: ()=>[
                    a,
                    a
                ],
            onSome: (b)=>[
                    a,
                    b
                ]
        })));
const setAndGet = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>self.modify(()=>[
            value,
            value
        ]));
const modify = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>self.modify(f));
const modifySome = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, fallback, f)=>self.modify((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(f(a), {
            onNone: ()=>[
                    fallback,
                    a
                ],
            onSome: (b)=>b
        })));
const update = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>self.modify((a)=>[
            void 0,
            f(a)
        ]));
const updateAndGet = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>self.modify((a)=>{
        const b = f(a);
        return [
            b,
            b
        ];
    }));
const updateSome = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>self.modify((a)=>[
            void 0,
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(f(a), {
                onNone: ()=>a,
                onSome: (b)=>b
            })
        ]));
const updateSomeAndGet = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, f)=>self.modify((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"])(f(a), {
            onNone: ()=>[
                    a,
                    a
                ],
            onSome: (b)=>[
                    b,
                    b
                ]
        })));
/** @internal */ const getOrMakeEntry = (self, journal)=>{
    if (journal.has(self)) {
        return journal.get(self);
    }
    const entry = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(self, false);
    journal.set(self, entry);
    return entry;
};
const unsafeGet = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, journal)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(getOrMakeEntry(self, journal)));
const unsafeSet = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, value, journal)=>{
    const entry = getOrMakeEntry(self, journal);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$entry$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(entry, value);
    return undefined;
}); //# sourceMappingURL=tRef.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/tQueue.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BackPressure": (()=>BackPressure),
    "Dropping": (()=>Dropping),
    "Sliding": (()=>Sliding),
    "TDequeueTypeId": (()=>TDequeueTypeId),
    "TEnqueueTypeId": (()=>TEnqueueTypeId),
    "awaitShutdown": (()=>awaitShutdown),
    "bounded": (()=>bounded),
    "capacity": (()=>capacity),
    "dropping": (()=>dropping),
    "isEmpty": (()=>isEmpty),
    "isFull": (()=>isFull),
    "isShutdown": (()=>isShutdown),
    "isTDequeue": (()=>isTDequeue),
    "isTEnqueue": (()=>isTEnqueue),
    "isTQueue": (()=>isTQueue),
    "offer": (()=>offer),
    "offerAll": (()=>offerAll),
    "peek": (()=>peek),
    "peekOption": (()=>peekOption),
    "poll": (()=>poll),
    "seek": (()=>seek),
    "shutdown": (()=>shutdown),
    "size": (()=>size),
    "sliding": (()=>sliding),
    "tDequeueVariance": (()=>tDequeueVariance),
    "tEnqueueVariance": (()=>tEnqueueVariance),
    "take": (()=>take),
    "takeAll": (()=>takeAll),
    "takeBetween": (()=>takeBetween),
    "takeN": (()=>takeN),
    "takeUpTo": (()=>takeUpTo),
    "unbounded": (()=>unbounded)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Array.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Chunk.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Option.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Predicate.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/core.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/opCodes/strategy.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/stm.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/tRef.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
const TEnqueueSymbolKey = "effect/TQueue/TEnqueue";
const TEnqueueTypeId = /*#__PURE__*/ Symbol.for(TEnqueueSymbolKey);
const TDequeueSymbolKey = "effect/TQueue/TDequeue";
const TDequeueTypeId = /*#__PURE__*/ Symbol.for(TDequeueSymbolKey);
const BackPressure = {
    _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BACKPRESSURE_STRATEGY"]
};
const Dropping = {
    _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DROPPING_STRATEGY"]
};
const Sliding = {
    _tag: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SLIDING_STRATEGY"]
};
const tDequeueVariance = {
    /* c8 ignore next */ _Out: (_)=>_
};
const tEnqueueVariance = {
    /* c8 ignore next */ _In: (_)=>_
};
class TQueueImpl {
    ref;
    requestedCapacity;
    strategy;
    [TDequeueTypeId] = tDequeueVariance;
    [TEnqueueTypeId] = tEnqueueVariance;
    constructor(ref, requestedCapacity, strategy){
        this.ref = ref;
        this.requestedCapacity = requestedCapacity;
        this.strategy = strategy;
    }
    capacity() {
        return this.requestedCapacity;
    }
    size = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
        const queue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.ref, runtime.journal);
        if (queue === undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(queue.length);
    });
    isFull = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(this.size, (size)=>size === this.requestedCapacity);
    isEmpty = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(this.size, (size)=>size === 0);
    shutdown = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.ref, void 0, runtime.journal);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"];
    });
    isShutdown = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effect"])((journal)=>{
        const queue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.ref, journal);
        return queue === undefined;
    });
    awaitShutdown = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(this.isShutdown, (isShutdown)=>isShutdown ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"]);
    offer(value) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
            const queue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(this.ref, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(runtime.journal));
            if (queue === undefined) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
            }
            if (queue.length < this.requestedCapacity) {
                queue.push(value);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.ref, queue, runtime.journal);
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(true);
            }
            switch(this.strategy._tag){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BACKPRESSURE_STRATEGY"]:
                    {
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"];
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DROPPING_STRATEGY"]:
                    {
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(false);
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SLIDING_STRATEGY"]:
                    {
                        if (queue.length === 0) {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(true);
                        }
                        queue.shift();
                        queue.push(value);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.ref, queue, runtime.journal);
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(true);
                    }
            }
        });
    }
    offerAll(iterable) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
            const as = Array.from(iterable);
            const queue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.ref, runtime.journal);
            if (queue === undefined) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
            }
            if (queue.length + as.length <= this.requestedCapacity) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.ref, [
                    ...queue,
                    ...as
                ], runtime.journal);
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(true);
            }
            switch(this.strategy._tag){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BACKPRESSURE_STRATEGY"]:
                    {
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"];
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DROPPING_STRATEGY"]:
                    {
                        const forQueue = as.slice(0, this.requestedCapacity - queue.length);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.ref, [
                            ...queue,
                            ...forQueue
                        ], runtime.journal);
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(false);
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SLIDING_STRATEGY"]:
                    {
                        const forQueue = as.slice(0, this.requestedCapacity - queue.length);
                        const toDrop = queue.length + forQueue.length - this.requestedCapacity;
                        const newQueue = queue.slice(toDrop);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.ref, [
                            ...newQueue,
                            ...forQueue
                        ], runtime.journal);
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(true);
                    }
            }
        });
    }
    peek = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
        const queue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.ref, runtime.journal);
        if (queue === undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
        }
        if (queue.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"];
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(queue[0]);
    });
    peekOption = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
        const queue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.ref, runtime.journal);
        if (queue === undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fromNullable"])(queue[0]));
    });
    take = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
        const queue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.ref, runtime.journal);
        if (queue === undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
        }
        if (queue.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"];
        }
        const dequeued = queue.shift();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.ref, queue, runtime.journal);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(dequeued);
    });
    takeAll = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
        const queue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.ref, runtime.journal);
        if (queue === undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.ref, [], runtime.journal);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(queue);
    });
    takeUpTo(max) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
            const queue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.ref, runtime.journal);
            if (queue === undefined) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
            }
            const [toTake, remaining] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["splitAt"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(queue), max);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.ref, Array.from(remaining), runtime.journal);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(Array.from(toTake));
        });
    }
}
const isTQueue = (u)=>{
    return isTEnqueue(u) && isTDequeue(u);
};
const isTEnqueue = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, TEnqueueTypeId);
const isTDequeue = (u)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Predicate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hasProperty"])(u, TDequeueTypeId);
const awaitShutdown = (self)=>self.awaitShutdown;
const bounded = (requestedCapacity)=>makeQueue(requestedCapacity, BackPressure);
const capacity = (self)=>{
    return self.capacity();
};
const dropping = (requestedCapacity)=>makeQueue(requestedCapacity, Dropping);
const isEmpty = (self)=>self.isEmpty;
const isFull = (self)=>self.isFull;
const isShutdown = (self)=>self.isShutdown;
const offer = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>self.offer(value));
const offerAll = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, iterable)=>self.offerAll(iterable));
const peek = (self)=>self.peek;
const peekOption = (self)=>self.peekOption;
const poll = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self.takeUpTo(1), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["head"]));
const seek = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, predicate)=>seekLoop(self, predicate));
const seekLoop = (self, predicate)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(self.take, (a)=>predicate(a) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(a) : seekLoop(self, predicate));
const shutdown = (self)=>self.shutdown;
const size = (self)=>self.size;
const sliding = (requestedCapacity)=>makeQueue(requestedCapacity, Sliding);
const take = (self)=>self.take;
const takeAll = (self)=>self.takeAll;
const takeBetween = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(3, (self, min, max)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["suspend"])(()=>{
        const takeRemainder = (min, max, acc)=>{
            if (max < min) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(acc);
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self.takeUpTo(max), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((taken)=>{
                const remaining = min - taken.length;
                if (remaining === 1) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self.take, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(taken)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["append"])(a))));
                }
                if (remaining > 1) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self.take, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((a)=>takeRemainder(remaining - 1, max - taken.length - 1, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(taken)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["append"])(a)))));
                }
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(acc, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendAll"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeFromArray"])(taken))));
            }));
        };
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(takeRemainder(min, max, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Chunk$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])()), (c)=>Array.from(c));
    }));
const takeN = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, n)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(self, takeBetween(n, n)));
const takeUpTo = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, max)=>self.takeUpTo(max));
const unbounded = ()=>makeQueue(Number.MAX_SAFE_INTEGER, Dropping);
const makeQueue = (requestedCapacity, strategy)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])([]), (ref)=>new TQueueImpl(ref, requestedCapacity, strategy)); //# sourceMappingURL=tQueue.js.map
}}),
"[project]/node_modules/effect/dist/esm/internal/stm/tPubSub.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TPubSubTypeId": (()=>TPubSubTypeId),
    "awaitShutdown": (()=>awaitShutdown),
    "bounded": (()=>bounded),
    "capacity": (()=>capacity),
    "dropping": (()=>dropping),
    "isEmpty": (()=>isEmpty),
    "isFull": (()=>isFull),
    "isShutdown": (()=>isShutdown),
    "makeNode": (()=>makeNode),
    "publish": (()=>publish),
    "publishAll": (()=>publishAll),
    "shutdown": (()=>shutdown),
    "size": (()=>size),
    "sliding": (()=>sliding),
    "subscribe": (()=>subscribe),
    "subscribeScoped": (()=>subscribeScoped),
    "unbounded": (()=>unbounded)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Array.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Effect.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/HashSet.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/Option.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/core.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/opCodes/strategy.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/stm.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tQueue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/tQueue.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/effect/dist/esm/internal/stm/tRef.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
/** @internal */ const TPubSubSymbolKey = "effect/TPubSub";
const TPubSubTypeId = /*#__PURE__*/ Symbol.for(TPubSubSymbolKey);
const AbsentValue = /*#__PURE__*/ Symbol.for("effect/TPubSub/AbsentValue");
const makeNode = (head, subscribers, tail)=>({
        head,
        subscribers,
        tail
    });
/** @internal */ class TPubSubImpl {
    pubsubSize;
    publisherHead;
    publisherTail;
    requestedCapacity;
    strategy;
    subscriberCount;
    subscribers;
    [TPubSubTypeId] = {
        _A: (_)=>_
    };
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tQueue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TEnqueueTypeId"]] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tQueue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tEnqueueVariance"];
    constructor(pubsubSize, publisherHead, publisherTail, requestedCapacity, strategy, subscriberCount, subscribers){
        this.pubsubSize = pubsubSize;
        this.publisherHead = publisherHead;
        this.publisherTail = publisherTail;
        this.requestedCapacity = requestedCapacity;
        this.strategy = strategy;
        this.subscriberCount = subscriberCount;
        this.subscribers = subscribers;
    }
    isShutdown = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effect"])((journal)=>{
        const currentPublisherTail = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.publisherTail, journal);
        return currentPublisherTail === undefined;
    });
    awaitShutdown = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(this.isShutdown, (isShutdown)=>isShutdown ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"]);
    capacity() {
        return this.requestedCapacity;
    }
    size = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
        const currentPublisherTail = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.publisherTail, runtime.journal);
        if (currentPublisherTail === undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.pubsubSize, runtime.journal));
    });
    isEmpty = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(this.size, (size)=>size === 0);
    isFull = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(this.size, (size)=>size === this.capacity());
    offer(value) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
            const currentPublisherTail = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.publisherTail, runtime.journal);
            if (currentPublisherTail === undefined) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
            }
            const currentSubscriberCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.subscriberCount, runtime.journal);
            if (currentSubscriberCount === 0) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(true);
            }
            const currentPubSubSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.pubsubSize, runtime.journal);
            if (currentPubSubSize < this.requestedCapacity) {
                const updatedPublisherTail = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TRefImpl"](void 0);
                const updatedNode = makeNode(value, currentSubscriberCount, updatedPublisherTail);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(currentPublisherTail, updatedNode, runtime.journal);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.publisherTail, updatedPublisherTail, runtime.journal);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.pubsubSize, currentPubSubSize + 1, runtime.journal);
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(true);
            }
            switch(this.strategy._tag){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_BACKPRESSURE_STRATEGY"]:
                    {
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"];
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_DROPPING_STRATEGY"]:
                    {
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(false);
                    }
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$opCodes$2f$strategy$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OP_SLIDING_STRATEGY"]:
                    {
                        if (this.requestedCapacity > 0) {
                            let currentPublisherHead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.publisherHead, runtime.journal);
                            let loop = true;
                            while(loop){
                                const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(currentPublisherHead, runtime.journal);
                                if (node === undefined) {
                                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"];
                                }
                                const head = node.head;
                                const tail = node.tail;
                                if (head !== AbsentValue) {
                                    const updatedNode = makeNode(AbsentValue, node.subscribers, node.tail);
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(currentPublisherHead, updatedNode, runtime.journal);
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.publisherHead, tail, runtime.journal);
                                    loop = false;
                                } else {
                                    currentPublisherHead = tail;
                                }
                            }
                        }
                        const updatedPublisherTail = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TRefImpl"](void 0);
                        const updatedNode = makeNode(value, currentSubscriberCount, updatedPublisherTail);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(currentPublisherTail, updatedNode, runtime.journal);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.publisherTail, updatedPublisherTail, runtime.journal);
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(true);
                    }
            }
        });
    }
    offerAll(iterable) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEach"])(iterable, (a)=>this.offer(a)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["every"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["identity"]));
    }
    shutdown = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effect"])((journal)=>{
        const currentPublisherTail = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.publisherTail, journal);
        if (currentPublisherTail !== undefined) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.publisherTail, void 0, journal);
            const currentSubscribers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.subscribers, journal);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["forEach"])(currentSubscribers, (subscriber)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(subscriber, void 0, journal);
            });
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.subscribers, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])(), journal);
        }
    });
}
/** @internal */ class TPubSubSubscriptionImpl {
    pubsubSize;
    publisherHead;
    requestedCapacity;
    subscriberHead;
    subscriberCount;
    subscribers;
    [TPubSubTypeId] = TPubSubTypeId;
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tQueue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TDequeueTypeId"]] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tQueue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tDequeueVariance"];
    constructor(pubsubSize, publisherHead, requestedCapacity, subscriberHead, subscriberCount, subscribers){
        this.pubsubSize = pubsubSize;
        this.publisherHead = publisherHead;
        this.requestedCapacity = requestedCapacity;
        this.subscriberHead = subscriberHead;
        this.subscriberCount = subscriberCount;
        this.subscribers = subscribers;
    }
    isShutdown = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effect"])((journal)=>{
        const currentSubscriberHead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.subscriberHead, journal);
        return currentSubscriberHead === undefined;
    });
    awaitShutdown = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(this.isShutdown, (isShutdown)=>isShutdown ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["void"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"]);
    capacity() {
        return this.requestedCapacity;
    }
    size = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
        let currentSubscriberHead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.subscriberHead, runtime.journal);
        if (currentSubscriberHead === undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
        }
        let loop = true;
        let size = 0;
        while(loop){
            const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(currentSubscriberHead, runtime.journal);
            if (node === undefined) {
                loop = false;
            } else {
                const head = node.head;
                const tail = node.tail;
                if (head !== AbsentValue) {
                    size = size + 1;
                    if (size >= Number.MAX_SAFE_INTEGER) {
                        loop = false;
                    }
                }
                currentSubscriberHead = tail;
            }
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(size);
    });
    isEmpty = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(this.size, (size)=>size === 0);
    isFull = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(this.size, (size)=>size === this.capacity());
    peek = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
        let currentSubscriberHead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.subscriberHead, runtime.journal);
        if (currentSubscriberHead === undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
        }
        let value = AbsentValue;
        let loop = true;
        while(loop){
            const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(currentSubscriberHead, runtime.journal);
            if (node === undefined) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"];
            }
            const head = node.head;
            const tail = node.tail;
            if (head !== AbsentValue) {
                value = head;
                loop = false;
            } else {
                currentSubscriberHead = tail;
            }
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(value);
    });
    peekOption = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
        let currentSubscriberHead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.subscriberHead, runtime.journal);
        if (currentSubscriberHead === undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
        }
        let value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
        let loop = true;
        while(loop){
            const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(currentSubscriberHead, runtime.journal);
            if (node === undefined) {
                value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["none"])();
                loop = false;
            } else {
                const head = node.head;
                const tail = node.tail;
                if (head !== AbsentValue) {
                    value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Option$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["some"])(head);
                    loop = false;
                } else {
                    currentSubscriberHead = tail;
                }
            }
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(value);
    });
    shutdown = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["effect"])((journal)=>{
        let currentSubscriberHead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.subscriberHead, journal);
        if (currentSubscriberHead !== undefined) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.subscriberHead, void 0, journal);
            let loop = true;
            while(loop){
                const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(currentSubscriberHead, journal);
                if (node === undefined) {
                    loop = false;
                } else {
                    const head = node.head;
                    const tail = node.tail;
                    if (head !== AbsentValue) {
                        const subscribers = node.subscribers;
                        if (subscribers === 1) {
                            const size = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.pubsubSize, journal);
                            const updatedNode = makeNode(AbsentValue, 0, tail);
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(currentSubscriberHead, updatedNode, journal);
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.publisherHead, tail, journal);
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.pubsubSize, size - 1, journal);
                        } else {
                            const updatedNode = makeNode(head, subscribers - 1, tail);
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(currentSubscriberHead, updatedNode, journal);
                        }
                    }
                    currentSubscriberHead = tail;
                }
            }
            const currentSubscriberCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.subscriberCount, journal);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.subscriberCount, currentSubscriberCount - 1, journal);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.subscribers, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["remove"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.subscribers, journal), this.subscriberHead), journal);
        }
    });
    take = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
        let currentSubscriberHead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.subscriberHead, runtime.journal);
        if (currentSubscriberHead === undefined) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
        }
        let value = AbsentValue;
        let loop = true;
        while(loop){
            const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(currentSubscriberHead, runtime.journal);
            if (node === undefined) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["retry"];
            }
            const head = node.head;
            const tail = node.tail;
            if (head !== AbsentValue) {
                const subscribers = node.subscribers;
                if (subscribers === 1) {
                    const size = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.pubsubSize, runtime.journal);
                    const updatedNode = makeNode(AbsentValue, 0, tail);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(currentSubscriberHead, updatedNode, runtime.journal);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.publisherHead, tail, runtime.journal);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.pubsubSize, size - 1, runtime.journal);
                } else {
                    const updatedNode = makeNode(head, subscribers - 1, tail);
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(currentSubscriberHead, updatedNode, runtime.journal);
                }
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.subscriberHead, tail, runtime.journal);
                value = head;
                loop = false;
            } else {
                currentSubscriberHead = tail;
            }
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(value);
    });
    takeAll = /*#__PURE__*/ this.takeUpTo(Number.POSITIVE_INFINITY);
    takeUpTo(max) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["withSTMRuntime"])((runtime)=>{
            let currentSubscriberHead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.subscriberHead, runtime.journal);
            if (currentSubscriberHead === undefined) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["interruptAs"])(runtime.fiberId);
            }
            const builder = [];
            let n = 0;
            while(n !== max){
                const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(currentSubscriberHead, runtime.journal);
                if (node === undefined) {
                    n = max;
                } else {
                    const head = node.head;
                    const tail = node.tail;
                    if (head !== AbsentValue) {
                        const subscribers = node.subscribers;
                        if (subscribers === 1) {
                            const size = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeGet"])(this.pubsubSize, runtime.journal);
                            const updatedNode = makeNode(AbsentValue, 0, tail);
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(currentSubscriberHead, updatedNode, runtime.journal);
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.publisherHead, tail, runtime.journal);
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.pubsubSize, size - 1, runtime.journal);
                        } else {
                            const updatedNode = makeNode(head, subscribers - 1, tail);
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(currentSubscriberHead, updatedNode, runtime.journal);
                        }
                        builder.push(head);
                        n = n + 1;
                    }
                    currentSubscriberHead = tail;
                }
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsafeSet"])(this.subscriberHead, currentSubscriberHead, runtime.journal);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["succeed"])(builder);
        });
    }
}
/** @internal */ const makeTPubSub = (requestedCapacity, strategy)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["all"])([
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(void 0),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(0)
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])(([empty, pubsubSize])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["all"])([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(empty),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(empty),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(0),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["empty"])())
        ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(([publisherHead, publisherTail, subscriberCount, subscribers])=>new TPubSubImpl(pubsubSize, publisherHead, publisherTail, requestedCapacity, strategy, subscriberCount, subscribers)))));
const makeSubscription = (pubsubSize, publisherHead, publisherTail, requestedCapacity, subscriberCount, subscribers)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(publisherTail), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flatMap"])((currentPublisherTail)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["all"])([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["make"])(currentPublisherTail),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(subscriberCount),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["get"])(subscribers)
        ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tap"])(([_, currentSubscriberCount])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(subscriberCount, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])(currentSubscriberCount + 1))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$stm$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tap"])(([subscriberHead, _, currentSubscribers])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(subscribers, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tRef$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["set"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(currentSubscribers, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$HashSet$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(subscriberHead))))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$core$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(([subscriberHead])=>new TPubSubSubscriptionImpl(pubsubSize, publisherHead, requestedCapacity, subscriberHead, subscriberCount, subscribers)))));
const awaitShutdown = (self)=>self.awaitShutdown;
const bounded = (requestedCapacity)=>makeTPubSub(requestedCapacity, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tQueue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BackPressure"]);
const capacity = (self)=>self.capacity();
const dropping = (requestedCapacity)=>makeTPubSub(requestedCapacity, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tQueue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Dropping"]);
const isEmpty = (self)=>self.isEmpty;
const isFull = (self)=>self.isFull;
const isShutdown = (self)=>self.isShutdown;
const publish = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, value)=>self.offer(value));
const publishAll = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dual"])(2, (self, iterable)=>self.offerAll(iterable));
const size = (self)=>self.size;
const shutdown = (self)=>self.shutdown;
const sliding = (requestedCapacity)=>makeTPubSub(requestedCapacity, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tQueue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Sliding"]);
const subscribe = (self)=>makeSubscription(self.pubsubSize, self.publisherHead, self.publisherTail, self.requestedCapacity, self.subscriberCount, self.subscribers);
const subscribeScoped = (self)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$Effect$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["acquireRelease"])(subscribe(self), (dequeue)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tQueue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["shutdown"])(dequeue));
const unbounded = ()=>makeTPubSub(Number.MAX_SAFE_INTEGER, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$effect$2f$dist$2f$esm$2f$internal$2f$stm$2f$tQueue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Dropping"]); //# sourceMappingURL=tPubSub.js.map
}}),

};

//# sourceMappingURL=node_modules_effect_dist_esm_internal_stm_b7d759f2._.js.map