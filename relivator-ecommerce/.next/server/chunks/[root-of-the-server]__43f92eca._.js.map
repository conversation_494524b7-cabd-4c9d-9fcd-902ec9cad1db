{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/app.ts"], "sourcesContent": ["export const SEO_CONFIG = {\n  description:\n    \"Relivator is a robust ecommerce template built with next.js and other modern technologies. It's designed for developers who want a fast, modern, and scalable foundation without reinventing the backend.\",\n  fullName: \"Relivator Next.js Template\",\n  name: \"Relivator\",\n  slogan: \"Store which makes you happy.\",\n};\n\nexport const SYSTEM_CONFIG = {\n  redirectAfterSignIn: \"/dashboard/uploads\",\n  redirectAfterSignUp: \"/dashboard/uploads\",\n  repoName: \"relivator\",\n  repoOwner: \"blefnk\",\n  repoStars: true,\n};\n\nexport const ADMIN_CONFIG = {\n  displayEmails: false,\n};\n\nexport const DB_DEV_LOGGER = false;\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,aAAa;IACxB,aACE;IACF,UAAU;IACV,MAAM;IACN,QAAQ;AACV;AAEO,MAAM,gBAAgB;IAC3B,qBAAqB;IACrB,qBAAqB;IACrB,UAAU;IACV,WAAW;IACX,WAAW;AACb;AAEO,MAAM,eAAe;IAC1B,eAAe;AACjB;AAEO,MAAM,gBAAgB", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/users/tables.ts"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED - DO NOT EDIT DIRECTLY\n *\n * To modify the schema, edit src/lib/auth.ts instead,\n * then run 'bun db:auth' to regenerate this file.\n *\n * Any direct changes to this file will be overwritten.\n */\n\nimport {\n  boolean,\n  integer,\n  pgTable,\n  text,\n  timestamp,\n} from \"drizzle-orm/pg-core\";\n\nexport const userTable = pgTable(\"user\", {\n  age: integer(\"age\"),\n  createdAt: timestamp(\"created_at\").notNull(),\n  email: text(\"email\").notNull().unique(),\n  emailVerified: boolean(\"email_verified\").notNull(),\n  firstName: text(\"first_name\"),\n  id: text(\"id\").primaryKey(),\n  image: text(\"image\"),\n  lastName: text(\"last_name\"),\n  name: text(\"name\").notNull(),\n  twoFactorEnabled: boolean(\"two_factor_enabled\"),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n});\n\nexport const sessionTable = pgTable(\"session\", {\n  createdAt: timestamp(\"created_at\").notNull(),\n  expiresAt: timestamp(\"expires_at\").notNull(),\n  id: text(\"id\").primaryKey(),\n  ipAddress: text(\"ip_address\"),\n  token: text(\"token\").notNull().unique(),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n  userAgent: text(\"user_agent\"),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n\nexport const accountTable = pgTable(\"account\", {\n  accessToken: text(\"access_token\"),\n  accessTokenExpiresAt: timestamp(\"access_token_expires_at\"),\n  accountId: text(\"account_id\").notNull(),\n  createdAt: timestamp(\"created_at\").notNull(),\n  id: text(\"id\").primaryKey(),\n  idToken: text(\"id_token\"),\n  password: text(\"password\"),\n  providerId: text(\"provider_id\").notNull(),\n  refreshToken: text(\"refresh_token\"),\n  refreshTokenExpiresAt: timestamp(\"refresh_token_expires_at\"),\n  scope: text(\"scope\"),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n\nexport const verificationTable = pgTable(\"verification\", {\n  createdAt: timestamp(\"created_at\"),\n  expiresAt: timestamp(\"expires_at\").notNull(),\n  id: text(\"id\").primaryKey(),\n  identifier: text(\"identifier\").notNull(),\n  updatedAt: timestamp(\"updated_at\"),\n  value: text(\"value\").notNull(),\n});\n\nexport const twoFactorTable = pgTable(\"two_factor\", {\n  backupCodes: text(\"backup_codes\").notNull(),\n  id: text(\"id\").primaryKey(),\n  secret: text(\"secret\").notNull(),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;AAED;AAAA;AAAA;AAAA;AAAA;;AAQO,MAAM,YAAY,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IACvC,KAAK,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE;IACb,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO;IAChD,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACZ,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACf,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE;IAC1B,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;AAC5C;AAEO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IAC7C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,UAAU,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D;AAEO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IAC7C,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,sBAAsB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IAChC,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACrC,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACd,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACf,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACvC,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,uBAAuB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACjC,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACZ,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,UAAU,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D;AAEO,MAAM,oBAAoB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACrB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACrB,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;AAC9B;AAEO,MAAM,iBAAiB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IAClD,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,OAAO;IACzC,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,UAAU,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/uploads/tables.ts"], "sourcesContent": ["import { pgEnum, pgTable, text, timestamp } from \"drizzle-orm/pg-core\";\n\nimport { userTable } from \"../users/tables\";\n\nexport const mediaTypeEnum = pgEnum(\"type\", [\"image\", \"video\"]);\n\nexport const uploadsTable = pgTable(\"uploads\", {\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n  id: text(\"id\").primaryKey(),\n  key: text(\"key\").notNull(), // UploadThing file key\n  type: mediaTypeEnum(\"type\").notNull(),\n  updatedAt: timestamp(\"updated_at\").defaultNow().notNull(),\n  url: text(\"url\").notNull(), // UploadThing file URL\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAEA;;;AAEO,MAAM,gBAAgB,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IAAC;IAAS;CAAQ;AAEvD,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IAC7C,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,KAAK,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,OAAO,OAAO;IACxB,MAAM,cAAc,QAAQ,OAAO;IACnC,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,KAAK,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,OAAO,OAAO;IACxB,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,wIAAA,CAAA,YAAS,CAAC,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/uploads/relations.ts"], "sourcesContent": ["import { relations } from \"drizzle-orm\";\n\nimport { userTable } from \"../users/tables\";\nimport { uploadsTable } from \"./tables\";\n\nexport const uploadsRelations = relations(uploadsTable, ({ one }) => ({\n  user: one(userTable, {\n    fields: [uploadsTable.userId],\n    references: [userTable.id],\n  }),\n}));\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;AAEO,MAAM,mBAAmB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,0IAAA,CAAA,eAAY,EAAE,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACpE,MAAM,IAAI,wIAAA,CAAA,YAAS,EAAE;YACnB,QAAQ;gBAAC,0IAAA,CAAA,eAAY,CAAC,MAAM;aAAC;YAC7B,YAAY;gBAAC,wIAAA,CAAA,YAAS,CAAC,EAAE;aAAC;QAC5B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/uploads/types.ts"], "sourcesContent": ["import type { uploadsTable } from \"./tables\";\n\nexport type MediaUpload = typeof uploadsTable.$inferSelect;\n// export type NewMediaUpload = typeof uploadsTable.$inferInsert;\n"], "names": [], "mappings": ";;CAGA,iEAAiE", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/payments/tables.ts"], "sourcesContent": ["import { pgTable, text, timestamp } from \"drizzle-orm/pg-core\";\n\nimport { userTable } from \"../users/tables\";\n\nexport const polarCustomerTable = pgTable(\"polar_customer\", {\n  createdAt: timestamp(\"created_at\").notNull(),\n  customerId: text(\"customer_id\").notNull().unique(),\n  id: text(\"id\").primaryKey(),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n\nexport const polarSubscriptionTable = pgTable(\"polar_subscription\", {\n  createdAt: timestamp(\"created_at\").notNull(),\n  customerId: text(\"customer_id\").notNull(),\n  id: text(\"id\").primaryKey(),\n  productId: text(\"product_id\").notNull(),\n  status: text(\"status\").notNull(),\n  subscriptionId: text(\"subscription_id\").notNull().unique(),\n  updatedAt: timestamp(\"updated_at\").notNull(),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => userTable.id, { onDelete: \"cascade\" }),\n});\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAEA;;;AAEO,MAAM,qBAAqB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;IAC1D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO,GAAG,MAAM;IAChD,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,wIAAA,CAAA,YAAS,CAAC,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D;AAEO,MAAM,yBAAyB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB;IAClE,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACvC,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACrC,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,gBAAgB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,mBAAmB,OAAO,GAAG,MAAM;IACxD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,wIAAA,CAAA,YAAS,CAAC,EAAE,EAAE;QAAE,UAAU;IAAU;AAC1D", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/payments/relations.ts"], "sourcesContent": ["import { relations } from \"drizzle-orm\";\n\nimport { userTable } from \"../users/tables\";\nimport { polarCustomerTable, polarSubscriptionTable } from \"./tables\";\n\nexport const polarCustomerRelations = relations(polarCustomerTable, ({ one }) => ({\n  user: one(userTable, {\n    fields: [polarCustomerTable.userId],\n    references: [userTable.id],\n  }),\n}));\n\nexport const polarSubscriptionRelations = relations(polarSubscriptionTable, ({ one }) => ({\n  user: one(userTable, {\n    fields: [polarSubscriptionTable.userId],\n    references: [userTable.id],\n  }),\n}));\n\nexport const extendUserRelations = relations(userTable, ({ many }) => ({\n  polarCustomers: many(polarCustomerTable),\n  polarSubscriptions: many(polarSubscriptionTable),\n}));\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;;;;AAEO,MAAM,yBAAyB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,2IAAA,CAAA,qBAAkB,EAAE,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QAChF,MAAM,IAAI,wIAAA,CAAA,YAAS,EAAE;YACnB,QAAQ;gBAAC,2IAAA,CAAA,qBAAkB,CAAC,MAAM;aAAC;YACnC,YAAY;gBAAC,wIAAA,CAAA,YAAS,CAAC,EAAE;aAAC;QAC5B;IACF,CAAC;AAEM,MAAM,6BAA6B,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,2IAAA,CAAA,yBAAsB,EAAE,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACxF,MAAM,IAAI,wIAAA,CAAA,YAAS,EAAE;YACnB,QAAQ;gBAAC,2IAAA,CAAA,yBAAsB,CAAC,MAAM;aAAC;YACvC,YAAY;gBAAC,wIAAA,CAAA,YAAS,CAAC,EAAE;aAAC;QAC5B;IACF,CAAC;AAEM,MAAM,sBAAsB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,wIAAA,CAAA,YAAS,EAAE,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QACrE,gBAAgB,KAAK,2IAAA,CAAA,qBAAkB;QACvC,oBAAoB,KAAK,2IAAA,CAAA,yBAAsB;IACjD,CAAC", "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/users/relations.ts"], "sourcesContent": ["import { relations } from \"drizzle-orm\";\n\nimport { uploadsTable } from \"../uploads/tables\";\nimport { accountTable, sessionTable, userTable } from \"./tables\";\n\nexport const userRelations = relations(userTable, ({ many }) => ({\n  accounts: many(accountTable),\n  sessions: many(sessionTable),\n  uploads: many(uploadsTable),\n}));\n\nexport const sessionRelations = relations(sessionTable, ({ one }) => ({\n  user: one(userTable, {\n    fields: [sessionTable.userId],\n    references: [userTable.id],\n  }),\n}));\n\nexport const accountRelations = relations(accountTable, ({ one }) => ({\n  user: one(userTable, {\n    fields: [accountTable.userId],\n    references: [userTable.id],\n  }),\n}));\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;;;;AAEO,MAAM,gBAAgB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,wIAAA,CAAA,YAAS,EAAE,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC;QAC/D,UAAU,KAAK,wIAAA,CAAA,eAAY;QAC3B,UAAU,KAAK,wIAAA,CAAA,eAAY;QAC3B,SAAS,KAAK,0IAAA,CAAA,eAAY;IAC5B,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,wIAAA,CAAA,eAAY,EAAE,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACpE,MAAM,IAAI,wIAAA,CAAA,YAAS,EAAE;YACnB,QAAQ;gBAAC,wIAAA,CAAA,eAAY,CAAC,MAAM;aAAC;YAC7B,YAAY;gBAAC,wIAAA,CAAA,YAAS,CAAC,EAAE;aAAC;QAC5B;IACF,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,wIAAA,CAAA,eAAY,EAAE,CAAC,EAAE,GAAG,EAAE,GAAK,CAAC;QACpE,MAAM,IAAI,wIAAA,CAAA,YAAS,EAAE;YACnB,QAAQ;gBAAC,wIAAA,CAAA,eAAY,CAAC,MAAM;aAAC;YAC7B,YAAY;gBAAC,wIAAA,CAAA,YAAS,CAAC,EAAE;aAAC;QAC5B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 499, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/schema/index.ts"], "sourcesContent": ["export * from \"./uploads/relations\";\nexport * from \"./uploads/tables\";\nexport * from \"./uploads/types\";\n\nexport * from \"./payments/relations\";\nexport * from \"./payments/tables\";\nexport * from \"./payments/types\";\n\n// relations\nexport * from \"./users/relations\";\n\n// schema\nexport * from \"./users/tables\";\n// types\nexport * from \"./users/types\";\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AAEA;AACA;AACA;AAEA,YAAY;AACZ;AAEA,SAAS;AACT;AACA,QAAQ;AACR", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/db/index.ts"], "sourcesContent": ["import \"dotenv/config\";\nimport { drizzle } from \"drizzle-orm/postgres-js\";\nimport postgres from \"postgres\";\n\nimport { DB_DEV_LOGGER } from \"~/app\";\n\nimport * as schema from \"./schema\";\n\n// Ensure the database URL is set\nif (!process.env.DATABASE_URL) {\n  throw new Error(\"🔴 DATABASE_URL environment variable is not set\");\n}\n\n/**\n * Caches the database connection in development to\n * prevent creating a new connection on every HMR update.\n */\ntype DbConnection = ReturnType<typeof postgres>;\nconst globalForDb = globalThis as unknown as {\n  conn?: DbConnection;\n};\nexport const conn: DbConnection =\n  globalForDb.conn ?? postgres(process.env.DATABASE_URL);\nif (process.env.NODE_ENV !== \"production\") {\n  globalForDb.conn = conn;\n}\n\n// Database connection instance\nexport const db = drizzle(conn, {\n  logger: DB_DEV_LOGGER && process.env.NODE_ENV !== \"production\",\n  schema,\n});\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AAEA;AAAA;;;;;;AAEA,iCAAiC;AACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,YAAY,EAAE;IAC7B,MAAM,IAAI,MAAM;AAClB;AAOA,MAAM,cAAc;AAGb,MAAM,OACX,YAAY,IAAI,IAAI,CAAA,GAAA,0IAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,GAAG,CAAC,YAAY;AACvD,wCAA2C;IACzC,YAAY,IAAI,GAAG;AACrB;AAGO,MAAM,KAAK,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD,EAAE,MAAM;IAC9B,QAAQ,4GAAA,CAAA,gBAAa,IAAI,oDAAyB;IAClD,QAAA;AACF", "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/lib/auth.ts"], "sourcesContent": ["// note: run `bun db:auth` to generate the `users.ts`\n// schema after making breaking changes to this file\n\nimport { betterAuth } from \"better-auth\";\nimport { drizzleAdapter } from \"better-auth/adapters/drizzle\";\nimport { twoFactor } from \"better-auth/plugins\";\nimport { polar } from \"@polar-sh/better-auth\";\nimport { Polar } from \"@polar-sh/sdk\";\nimport { headers } from \"next/headers\";\nimport { redirect } from \"next/navigation\";\n\nimport type { UserDbType } from \"~/lib/auth-types\";\n\nimport { SYSTEM_CONFIG } from \"~/app\";\nimport { db } from \"~/db\";\nimport {\n  accountTable,\n  sessionTable,\n  twoFactorTable,\n  userTable,\n  verificationTable,\n} from \"~/db/schema\";\n\ninterface GitHubProfile {\n  [key: string]: unknown;\n  email?: string;\n  name?: string;\n}\n\ninterface GoogleProfile {\n  [key: string]: unknown;\n  email?: string;\n  family_name?: string;\n  given_name?: string;\n}\n\ninterface SocialProviderConfig {\n  [key: string]: unknown;\n  clientId: string;\n  clientSecret: string;\n  mapProfileToUser: (\n    profile: GitHubProfile | GoogleProfile,\n  ) => Record<string, unknown>;\n  redirectURI?: string;\n  scope: string[];\n}\n\nconst hasGithubCredentials =\n  process.env.AUTH_GITHUB_ID &&\n  process.env.AUTH_GITHUB_SECRET &&\n  process.env.AUTH_GITHUB_ID.length > 0 &&\n  process.env.AUTH_GITHUB_SECRET.length > 0;\n\nconst hasGoogleCredentials =\n  process.env.AUTH_GOOGLE_ID &&\n  process.env.AUTH_GOOGLE_SECRET &&\n  process.env.AUTH_GOOGLE_ID.length > 0 &&\n  process.env.AUTH_GOOGLE_SECRET.length > 0;\n\n// Build social providers configuration\nconst socialProviders: Record<string, SocialProviderConfig> = {};\n\nif (hasGithubCredentials) {\n  socialProviders.github = {\n    clientId: process.env.AUTH_GITHUB_ID ?? \"\",\n    clientSecret: process.env.AUTH_GITHUB_SECRET ?? \"\",\n    mapProfileToUser: (profile: GitHubProfile) => {\n      let firstName = \"\";\n      let lastName = \"\";\n      if (profile.name) {\n        const nameParts = profile.name.split(\" \");\n        firstName = nameParts[0];\n        lastName = nameParts.length > 1 ? nameParts.slice(1).join(\" \") : \"\";\n      }\n      return {\n        age: null,\n        firstName,\n        lastName,\n      };\n    },\n    scope: [\"user:email\", \"read:user\"],\n  };\n}\n\nif (hasGoogleCredentials) {\n  socialProviders.google = {\n    clientId: process.env.AUTH_GOOGLE_ID ?? \"\",\n    clientSecret: process.env.AUTH_GOOGLE_SECRET ?? \"\",\n    mapProfileToUser: (profile: GoogleProfile) => {\n      return {\n        age: null,\n        firstName: profile.given_name ?? \"\",\n        lastName: profile.family_name ?? \"\",\n      };\n    },\n    scope: [\"openid\", \"email\", \"profile\"],\n  };\n}\n\nconst polarClient = new Polar({\n  accessToken: process.env.POLAR_ACCESS_TOKEN,\n  server: (process.env.POLAR_ENVIRONMENT as \"production\" | \"sandbox\") || \"production\",\n});\n\nexport const auth = betterAuth({\n  account: {\n    accountLinking: {\n      allowDifferentEmails: false,\n      enabled: true,\n      trustedProviders: Object.keys(socialProviders),\n    },\n  },\n  baseURL: process.env.NEXT_SERVER_APP_URL,\n\n  database: drizzleAdapter(db, {\n    provider: \"pg\",\n    schema: {\n      account: accountTable,\n      session: sessionTable,\n      twoFactor: twoFactorTable,\n      user: userTable,\n      verification: verificationTable,\n    },\n  }),\n\n  emailAndPassword: {\n    enabled: true,\n  },\n\n  // Configure OAuth behavior\n  oauth: {\n    // Default redirect URL after successful login\n    defaultCallbackUrl: SYSTEM_CONFIG.redirectAfterSignIn,\n    // URL to redirect to on error\n    errorCallbackUrl: \"/auth/error\",\n    // Whether to link accounts with the same email\n    linkAccountsByEmail: true,\n  },\n\n  plugins: [\n    twoFactor(),\n    polar({\n      client: polarClient,\n      createCustomerOnSignUp: true,\n      enableCustomerPortal: true,\n      // Configure checkout\n      checkout: {\n        enabled: true,\n        products: [\n          {\n            productId: \"pro-plan\", // Replace with actual product ID from Polar Dashboard\n            slug: \"pro\" // Custom slug for easy reference in Checkout URL\n          },\n          {\n            productId: \"premium-plan\", // Replace with actual product ID from Polar Dashboard\n            slug: \"premium\" // Custom slug for easy reference in Checkout URL\n          }\n        ],\n        successUrl: \"/dashboard/billing?checkout_success=true&checkout_id={CHECKOUT_ID}\",\n      },\n      // Configure webhooks\n      webhooks: {\n        secret: process.env.POLAR_WEBHOOK_SECRET || \"\",\n        onPayload: async (payload) => {\n          console.log(\"Received webhook payload:\", payload.type);\n        },\n      },\n    }),\n  ],\n\n  secret: process.env.AUTH_SECRET,\n\n  // Only include social providers if credentials are available\n  socialProviders,\n\n  user: {\n    additionalFields: {\n      age: {\n        input: true,\n        required: false,\n        type: \"number\",\n      },\n      firstName: {\n        input: true,\n        required: false,\n        type: \"string\",\n      },\n      lastName: {\n        input: true,\n        required: false,\n        type: \"string\",\n      },\n    },\n  },\n});\n\nexport const getCurrentUser = async (): Promise<null | UserDbType> => {\n  const session = await auth.api.getSession({\n    headers: await headers(),\n  });\n  if (!session) {\n    return null;\n  }\n  return session.user as UserDbType;\n};\n\nexport const getCurrentUserOrRedirect = async (\n  forbiddenUrl = \"/auth/sign-in\",\n  okUrl = \"\",\n  ignoreForbidden = false,\n): Promise<null | UserDbType> => {\n  const user = await getCurrentUser();\n\n  // if no user is found\n  if (!user) {\n    // redirect to forbidden url unless explicitly ignored\n    if (!ignoreForbidden) {\n      redirect(forbiddenUrl);\n    }\n    // if ignoring forbidden, return the null user immediately\n    // (don't proceed to okUrl check)\n    return user; // user is null here\n  }\n\n  // if user is found and an okUrl is provided, redirect there\n  if (okUrl) {\n    redirect(okUrl);\n  }\n\n  // if user is found and no okUrl is provided, return the user\n  return user; // user is UserDbType here\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD,oDAAoD;;;;;;AAEpD;AAAA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAIA;AACA;AACA;AAAA;;;;;;;;;;;AAgCA,MAAM,uBACJ,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,kBAAkB,IAC9B,QAAQ,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,KACpC,QAAQ,GAAG,CAAC,kBAAkB,CAAC,MAAM,GAAG;AAE1C,MAAM,uBACJ,QAAQ,GAAG,CAAC,cAAc,IAC1B,QAAQ,GAAG,CAAC,kBAAkB,IAC9B,QAAQ,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,KACpC,QAAQ,GAAG,CAAC,kBAAkB,CAAC,MAAM,GAAG;AAE1C,uCAAuC;AACvC,MAAM,kBAAwD,CAAC;AAE/D,IAAI,sBAAsB;IACxB,gBAAgB,MAAM,GAAG;QACvB,UAAU,QAAQ,GAAG,CAAC,cAAc,IAAI;QACxC,cAAc,QAAQ,GAAG,CAAC,kBAAkB,IAAI;QAChD,kBAAkB,CAAC;YACjB,IAAI,YAAY;YAChB,IAAI,WAAW;YACf,IAAI,QAAQ,IAAI,EAAE;gBAChB,MAAM,YAAY,QAAQ,IAAI,CAAC,KAAK,CAAC;gBACrC,YAAY,SAAS,CAAC,EAAE;gBACxB,WAAW,UAAU,MAAM,GAAG,IAAI,UAAU,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO;YACnE;YACA,OAAO;gBACL,KAAK;gBACL;gBACA;YACF;QACF;QACA,OAAO;YAAC;YAAc;SAAY;IACpC;AACF;AAEA,IAAI,sBAAsB;IACxB,gBAAgB,MAAM,GAAG;QACvB,UAAU,QAAQ,GAAG,CAAC,cAAc,IAAI;QACxC,cAAc,QAAQ,GAAG,CAAC,kBAAkB,IAAI;QAChD,kBAAkB,CAAC;YACjB,OAAO;gBACL,KAAK;gBACL,WAAW,QAAQ,UAAU,IAAI;gBACjC,UAAU,QAAQ,WAAW,IAAI;YACnC;QACF;QACA,OAAO;YAAC;YAAU;YAAS;SAAU;IACvC;AACF;AAEA,MAAM,cAAc,IAAI,mKAAA,CAAA,QAAK,CAAC;IAC5B,aAAa,QAAQ,GAAG,CAAC,kBAAkB;IAC3C,QAAQ,AAAC,QAAQ,GAAG,CAAC,iBAAiB,IAAiC;AACzE;AAEO,MAAM,OAAO,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE;IAC7B,SAAS;QACP,gBAAgB;YACd,sBAAsB;YACtB,SAAS;YACT,kBAAkB,OAAO,IAAI,CAAC;QAChC;IACF;IACA,SAAS,QAAQ,GAAG,CAAC,mBAAmB;IAExC,UAAU,CAAA,GAAA,oLAAA,CAAA,iBAAc,AAAD,EAAE,oHAAA,CAAA,KAAE,EAAE;QAC3B,UAAU;QACV,QAAQ;YACN,SAAS,wIAAA,CAAA,eAAY;YACrB,SAAS,wIAAA,CAAA,eAAY;YACrB,WAAW,wIAAA,CAAA,iBAAc;YACzB,MAAM,wIAAA,CAAA,YAAS;YACf,cAAc,wIAAA,CAAA,oBAAiB;QACjC;IACF;IAEA,kBAAkB;QAChB,SAAS;IACX;IAEA,2BAA2B;IAC3B,OAAO;QACL,8CAA8C;QAC9C,oBAAoB,4GAAA,CAAA,gBAAa,CAAC,mBAAmB;QACrD,8BAA8B;QAC9B,kBAAkB;QAClB,+CAA+C;QAC/C,qBAAqB;IACvB;IAEA,SAAS;QACP,CAAA,GAAA,8LAAA,CAAA,YAAS,AAAD;QACR,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE;YACJ,QAAQ;YACR,wBAAwB;YACxB,sBAAsB;YACtB,qBAAqB;YACrB,UAAU;gBACR,SAAS;gBACT,UAAU;oBACR;wBACE,WAAW;wBACX,MAAM,MAAM,iDAAiD;oBAC/D;oBACA;wBACE,WAAW;wBACX,MAAM,UAAU,iDAAiD;oBACnE;iBACD;gBACD,YAAY;YACd;YACA,qBAAqB;YACrB,UAAU;gBACR,QAAQ,QAAQ,GAAG,CAAC,oBAAoB,IAAI;gBAC5C,WAAW,OAAO;oBAChB,QAAQ,GAAG,CAAC,6BAA6B,QAAQ,IAAI;gBACvD;YACF;QACF;KACD;IAED,QAAQ,QAAQ,GAAG,CAAC,WAAW;IAE/B,6DAA6D;IAC7D;IAEA,MAAM;QACJ,kBAAkB;YAChB,KAAK;gBACH,OAAO;gBACP,UAAU;gBACV,MAAM;YACR;YACA,WAAW;gBACT,OAAO;gBACP,UAAU;gBACV,MAAM;YACR;YACA,UAAU;gBACR,OAAO;gBACP,UAAU;gBACV,MAAM;YACR;QACF;IACF;AACF;AAEO,MAAM,iBAAiB;IAC5B,MAAM,UAAU,MAAM,KAAK,GAAG,CAAC,UAAU,CAAC;QACxC,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACvB;IACA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IACA,OAAO,QAAQ,IAAI;AACrB;AAEO,MAAM,2BAA2B,OACtC,eAAe,eAAe,EAC9B,QAAQ,EAAE,EACV,kBAAkB,KAAK;IAEvB,MAAM,OAAO,MAAM;IAEnB,sBAAsB;IACtB,IAAI,CAAC,MAAM;QACT,sDAAsD;QACtD,IAAI,CAAC,iBAAiB;YACpB,CAAA,GAAA,uLAAA,CAAA,WAAQ,AAAD,EAAE;QACX;QACA,0DAA0D;QAC1D,iCAAiC;QACjC,OAAO,MAAM,oBAAoB;IACnC;IAEA,4DAA4D;IAC5D,IAAI,OAAO;QACT,CAAA,GAAA,uLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,6DAA6D;IAC7D,OAAO,MAAM,0BAA0B;AACzC", "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/tutex/relivator-ecommerce/src/app/api/auth/%5B...all%5D/route.ts"], "sourcesContent": ["import { toNextJsHandler } from \"better-auth/next-js\";\n\nimport { auth } from \"~/lib/auth\";\n\nexport const { GET, POST } = toNextJsHandler(auth);\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;AAEO,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uKAAA,CAAA,kBAAe,AAAD,EAAE,oHAAA,CAAA,OAAI", "debugId": null}}]}