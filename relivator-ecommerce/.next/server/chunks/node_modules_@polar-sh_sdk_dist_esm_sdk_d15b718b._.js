module.exports = {

"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/benefits.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Benefits": (()=>Benefits)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/benefitsCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/benefitsDelete.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/benefitsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsGrants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/benefitsGrants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/benefitsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/benefitsUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
class Benefits extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Benefits
     *
     * @remarks
     * List benefits.
     *
     * **Scopes**: `benefits:read` `benefits:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["benefitsList"])(this, request, options));
    }
    /**
     * Create Benefit
     *
     * @remarks
     * Create a benefit.
     *
     * **Scopes**: `benefits:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["benefitsCreate"])(this, request, options));
    }
    /**
     * Get Benefit
     *
     * @remarks
     * Get a benefit by ID.
     *
     * **Scopes**: `benefits:read` `benefits:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["benefitsGet"])(this, request, options));
    }
    /**
     * Update Benefit
     *
     * @remarks
     * Update a benefit.
     *
     * **Scopes**: `benefits:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["benefitsUpdate"])(this, request, options));
    }
    /**
     * Delete Benefit
     *
     * @remarks
     * Delete a benefit.
     *
     * > [!WARNING]
     * > Every grants associated with the benefit will be revoked.
     * > Users will lose access to the benefit.
     *
     * **Scopes**: `benefits:write`
     */ async delete(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["benefitsDelete"])(this, request, options));
    }
    /**
     * List Benefit Grants
     *
     * @remarks
     * List the individual grants for a benefit.
     *
     * It's especially useful to check if a user has been granted a benefit.
     *
     * **Scopes**: `benefits:read` `benefits:write`
     */ async grants(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$benefitsGrants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["benefitsGrants"])(this, request, options));
    }
} //# sourceMappingURL=benefits.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/checkoutlinks.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "CheckoutLinks": (()=>CheckoutLinks)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutLinksCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutLinksCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutLinksDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutLinksDelete.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutLinksGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutLinksGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutLinksList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutLinksList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutLinksUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutLinksUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class CheckoutLinks extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Checkout Links
     *
     * @remarks
     * List checkout links.
     *
     * **Scopes**: `checkout_links:read` `checkout_links:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutLinksList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutLinksList"])(this, request, options));
    }
    /**
     * Create Checkout Link
     *
     * @remarks
     * Create a checkout link.
     *
     * **Scopes**: `checkout_links:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutLinksCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutLinksCreate"])(this, request, options));
    }
    /**
     * Get Checkout Link
     *
     * @remarks
     * Get a checkout link by ID.
     *
     * **Scopes**: `checkout_links:read` `checkout_links:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutLinksGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutLinksGet"])(this, request, options));
    }
    /**
     * Update Checkout Link
     *
     * @remarks
     * Update a checkout link.
     *
     * **Scopes**: `checkout_links:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutLinksUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutLinksUpdate"])(this, request, options));
    }
    /**
     * Delete Checkout Link
     *
     * @remarks
     * Delete a checkout link.
     *
     * **Scopes**: `checkout_links:write`
     */ async delete(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutLinksDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutLinksDelete"])(this, request, options));
    }
} //# sourceMappingURL=checkoutlinks.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/checkouts.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Checkouts": (()=>Checkouts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsClientConfirm$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutsClientConfirm.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsClientGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutsClientGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsClientUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutsClientUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutsCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/checkoutsUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
class Checkouts extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Checkout Sessions
     *
     * @remarks
     * List checkout sessions.
     *
     * **Scopes**: `checkouts:read` `checkouts:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutsList"])(this, request, options));
    }
    /**
     * Create Checkout Session
     *
     * @remarks
     * Create a checkout session.
     *
     * **Scopes**: `checkouts:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutsCreate"])(this, request, options));
    }
    /**
     * Get Checkout Session
     *
     * @remarks
     * Get a checkout session by ID.
     *
     * **Scopes**: `checkouts:read` `checkouts:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutsGet"])(this, request, options));
    }
    /**
     * Update Checkout Session
     *
     * @remarks
     * Update a checkout session.
     *
     * **Scopes**: `checkouts:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutsUpdate"])(this, request, options));
    }
    /**
     * Get Checkout Session from Client
     *
     * @remarks
     * Get a checkout session by client secret.
     */ async clientGet(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsClientGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutsClientGet"])(this, request, options));
    }
    /**
     * Update Checkout Session from Client
     *
     * @remarks
     * Update a checkout session by client secret.
     */ async clientUpdate(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsClientUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutsClientUpdate"])(this, request, options));
    }
    /**
     * Confirm Checkout Session from Client
     *
     * @remarks
     * Confirm a checkout session by client secret.
     *
     * Orders and subscriptions will be processed.
     */ async clientConfirm(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$checkoutsClientConfirm$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkoutsClientConfirm"])(this, request, options));
    }
} //# sourceMappingURL=checkouts.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/customermeters.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "CustomerMeters": (()=>CustomerMeters)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerMetersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerMetersGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerMetersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerMetersList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
class CustomerMeters extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Customer Meters
     *
     * @remarks
     * List customer meters.
     *
     * **Scopes**: `customer_meters:read`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerMetersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerMetersList"])(this, request, options));
    }
    /**
     * Get Customer Meter
     *
     * @remarks
     * Get a customer meter by ID.
     *
     * **Scopes**: `customer_meters:read`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerMetersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerMetersGet"])(this, request, options));
    }
} //# sourceMappingURL=customermeters.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/benefitgrants.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "BenefitGrants": (()=>BenefitGrants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalBenefitGrantsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalBenefitGrantsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalBenefitGrantsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalBenefitGrantsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalBenefitGrantsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalBenefitGrantsUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
class BenefitGrants extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Benefit Grants
     *
     * @remarks
     * List benefits grants of the authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async list(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalBenefitGrantsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalBenefitGrantsList"])(this, security, request, options));
    }
    /**
     * Get Benefit Grant
     *
     * @remarks
     * Get a benefit grant by ID for the authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async get(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalBenefitGrantsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalBenefitGrantsGet"])(this, security, request, options));
    }
    /**
     * Update Benefit Grant
     *
     * @remarks
     * Update a benefit grant for the authenticated customer.
     *
     * **Scopes**: `customer_portal:write`
     */ async update(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalBenefitGrantsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalBenefitGrantsUpdate"])(this, security, request, options));
    }
} //# sourceMappingURL=benefitgrants.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/downloadables.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Downloadables": (()=>Downloadables)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalDownloadablesGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalDownloadablesGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalDownloadablesList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalDownloadablesList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
class Downloadables extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Downloadables
     *
     * @remarks
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async list(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalDownloadablesList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalDownloadablesList"])(this, security, request, options));
    }
    /**
     * Get Downloadable
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalDownloadablesGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalDownloadablesGet"])(this, request, options));
    }
} //# sourceMappingURL=downloadables.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarcustomermeters.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "PolarCustomerMeters": (()=>PolarCustomerMeters)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomerMetersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalCustomerMetersGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomerMetersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalCustomerMetersList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
class PolarCustomerMeters extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Meters
     *
     * @remarks
     * List meters of the authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async list(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomerMetersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalCustomerMetersList"])(this, security, request, options));
    }
    /**
     * Get Customer Meter
     *
     * @remarks
     * Get a meter by ID for the authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async get(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomerMetersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalCustomerMetersGet"])(this, security, request, options));
    }
} //# sourceMappingURL=polarcustomermeters.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarcustomers.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "PolarCustomers": (()=>PolarCustomers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomersAddPaymentMethod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalCustomersAddPaymentMethod.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomersDeletePaymentMethod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalCustomersDeletePaymentMethod.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalCustomersGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomersGetPaymentMethods$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalCustomersGetPaymentMethods.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomersUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalCustomersUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class PolarCustomers extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * Get Customer
     *
     * @remarks
     * Get authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async get(security, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalCustomersGet"])(this, security, options));
    }
    /**
     * Update Customer
     *
     * @remarks
     * Update authenticated customer.
     *
     * **Scopes**: `customer_portal:write`
     */ async update(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomersUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalCustomersUpdate"])(this, security, request, options));
    }
    /**
     * Get Customer Payment Methods
     *
     * @remarks
     * Get saved payment methods of the authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async getPaymentMethods(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomersGetPaymentMethods$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalCustomersGetPaymentMethods"])(this, security, request, options));
    }
    /**
     * Add Customer Payment Method
     *
     * @remarks
     * Add a payment method to the authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async addPaymentMethod(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomersAddPaymentMethod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalCustomersAddPaymentMethod"])(this, security, request, options));
    }
    /**
     * Delete Customer Payment Method
     *
     * @remarks
     * Delete a payment method from the authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async deletePaymentMethod(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalCustomersDeletePaymentMethod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalCustomersDeletePaymentMethod"])(this, security, request, options));
    }
} //# sourceMappingURL=polarcustomers.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarlicensekeys.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "PolarLicenseKeys": (()=>PolarLicenseKeys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalLicenseKeysActivate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalLicenseKeysActivate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalLicenseKeysDeactivate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalLicenseKeysDeactivate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalLicenseKeysGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalLicenseKeysGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalLicenseKeysList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalLicenseKeysList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalLicenseKeysValidate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalLicenseKeysValidate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class PolarLicenseKeys extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List License Keys
     *
     * @remarks
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async list(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalLicenseKeysList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalLicenseKeysList"])(this, security, request, options));
    }
    /**
     * Get License Key
     *
     * @remarks
     * Get a license key.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async get(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalLicenseKeysGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalLicenseKeysGet"])(this, security, request, options));
    }
    /**
     * Validate License Key
     *
     * @remarks
     * Validate a license key.
     */ async validate(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalLicenseKeysValidate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalLicenseKeysValidate"])(this, request, options));
    }
    /**
     * Activate License Key
     *
     * @remarks
     * Activate a license key instance.
     */ async activate(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalLicenseKeysActivate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalLicenseKeysActivate"])(this, request, options));
    }
    /**
     * Deactivate License Key
     *
     * @remarks
     * Deactivate a license key instance.
     */ async deactivate(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalLicenseKeysDeactivate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalLicenseKeysDeactivate"])(this, request, options));
    }
} //# sourceMappingURL=polarlicensekeys.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarorders.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "PolarOrders": (()=>PolarOrders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalOrdersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalOrdersGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalOrdersInvoice$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalOrdersInvoice.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalOrdersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalOrdersList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
class PolarOrders extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Orders
     *
     * @remarks
     * List orders of the authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async list(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalOrdersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalOrdersList"])(this, security, request, options));
    }
    /**
     * Get Order
     *
     * @remarks
     * Get an order by ID for the authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async get(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalOrdersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalOrdersGet"])(this, security, request, options));
    }
    /**
     * Get Order Invoice
     *
     * @remarks
     * Get an order's invoice data.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async invoice(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalOrdersInvoice$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalOrdersInvoice"])(this, security, request, options));
    }
} //# sourceMappingURL=polarorders.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarorganizations.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "PolarOrganizations": (()=>PolarOrganizations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalOrganizationsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalOrganizationsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
;
;
;
class PolarOrganizations extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * Get Organization
     *
     * @remarks
     * Get a customer portal's organization by slug.
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalOrganizationsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalOrganizationsGet"])(this, request, options));
    }
} //# sourceMappingURL=polarorganizations.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarsubscriptions.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "PolarSubscriptions": (()=>PolarSubscriptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalSubscriptionsCancel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalSubscriptionsCancel.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalSubscriptionsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalSubscriptionsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalSubscriptionsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalSubscriptionsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalSubscriptionsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerPortalSubscriptionsUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
class PolarSubscriptions extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Subscriptions
     *
     * @remarks
     * List subscriptions of the authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async list(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalSubscriptionsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalSubscriptionsList"])(this, security, request, options));
    }
    /**
     * Get Subscription
     *
     * @remarks
     * Get a subscription for the authenticated customer.
     *
     * **Scopes**: `customer_portal:read` `customer_portal:write`
     */ async get(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalSubscriptionsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalSubscriptionsGet"])(this, security, request, options));
    }
    /**
     * Update Subscription
     *
     * @remarks
     * Update a subscription of the authenticated customer.
     *
     * **Scopes**: `customer_portal:write`
     */ async update(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalSubscriptionsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalSubscriptionsUpdate"])(this, security, request, options));
    }
    /**
     * Cancel Subscription
     *
     * @remarks
     * Cancel a subscription of the authenticated customer.
     *
     * **Scopes**: `customer_portal:write`
     */ async cancel(security, request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerPortalSubscriptionsCancel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerPortalSubscriptionsCancel"])(this, security, request, options));
    }
} //# sourceMappingURL=polarsubscriptions.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/customerportal.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "CustomerPortal": (()=>CustomerPortal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$benefitgrants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/benefitgrants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$downloadables$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/downloadables.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarcustomermeters$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarcustomermeters.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarcustomers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarcustomers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarlicensekeys$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarlicensekeys.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarorders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarorders.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarorganizations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarorganizations.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarsubscriptions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/polarsubscriptions.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
class CustomerPortal extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    get benefitGrants() {
        return this._benefitGrants ?? (this._benefitGrants = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$benefitgrants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BenefitGrants"](this._options));
    }
    get customers() {
        return this._customers ?? (this._customers = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarcustomers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolarCustomers"](this._options));
    }
    get customerMeters() {
        return this._customerMeters ?? (this._customerMeters = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarcustomermeters$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolarCustomerMeters"](this._options));
    }
    get downloadables() {
        return this._downloadables ?? (this._downloadables = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$downloadables$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Downloadables"](this._options));
    }
    get licenseKeys() {
        return this._licenseKeys ?? (this._licenseKeys = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarlicensekeys$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolarLicenseKeys"](this._options));
    }
    get orders() {
        return this._orders ?? (this._orders = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarorders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolarOrders"](this._options));
    }
    get organizations() {
        return this._organizations ?? (this._organizations = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarorganizations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolarOrganizations"](this._options));
    }
    get subscriptions() {
        return this._subscriptions ?? (this._subscriptions = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$polarsubscriptions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PolarSubscriptions"](this._options));
    }
} //# sourceMappingURL=customerportal.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/customers.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Customers": (()=>Customers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customersCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customersDelete.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersDeleteExternal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customersDeleteExternal.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customersGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersGetExternal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customersGetExternal.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersGetState$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customersGetState.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersGetStateExternal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customersGetStateExternal.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customersList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customersUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersUpdateExternal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customersUpdateExternal.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
class Customers extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Customers
     *
     * @remarks
     * List customers.
     *
     * **Scopes**: `customers:read` `customers:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customersList"])(this, request, options));
    }
    /**
     * Create Customer
     *
     * @remarks
     * Create a customer.
     *
     * **Scopes**: `customers:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customersCreate"])(this, request, options));
    }
    /**
     * Get Customer
     *
     * @remarks
     * Get a customer by ID.
     *
     * **Scopes**: `customers:read` `customers:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customersGet"])(this, request, options));
    }
    /**
     * Update Customer
     *
     * @remarks
     * Update a customer.
     *
     * **Scopes**: `customers:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customersUpdate"])(this, request, options));
    }
    /**
     * Delete Customer
     *
     * @remarks
     * Delete a customer.
     *
     * This action cannot be undone and will immediately:
     * - Cancel any active subscriptions for the customer
     * - Revoke all their benefits
     * - Clear any `external_id`
     *
     * Use it only in the context of deleting a user within your
     * own service. Otherwise, use more granular API endpoints to cancel
     * a specific subscription or revoke certain benefits.
     *
     * Note: The customers information will nonetheless be retained for historic
     * orders and subscriptions.
     *
     * **Scopes**: `customers:write`
     */ async delete(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customersDelete"])(this, request, options));
    }
    /**
     * Get Customer by External ID
     *
     * @remarks
     * Get a customer by external ID.
     *
     * **Scopes**: `customers:read` `customers:write`
     */ async getExternal(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersGetExternal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customersGetExternal"])(this, request, options));
    }
    /**
     * Update Customer by External ID
     *
     * @remarks
     * Update a customer by external ID.
     *
     * **Scopes**: `customers:write`
     */ async updateExternal(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersUpdateExternal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customersUpdateExternal"])(this, request, options));
    }
    /**
     * Delete Customer by External ID
     *
     * @remarks
     * Delete a customer by external ID.
     *
     * Immediately cancels any active subscriptions and revokes any active benefits.
     *
     * **Scopes**: `customers:write`
     */ async deleteExternal(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersDeleteExternal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customersDeleteExternal"])(this, request, options));
    }
    /**
     * Get Customer State
     *
     * @remarks
     * Get a customer state by ID.
     *
     * The customer state includes information about
     * the customer's active subscriptions and benefits.
     *
     * It's the ideal endpoint to use when you need to get a full overview
     * of a customer's status.
     *
     * **Scopes**: `customers:read` `customers:write`
     */ async getState(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersGetState$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customersGetState"])(this, request, options));
    }
    /**
     * Get Customer State by External ID
     *
     * @remarks
     * Get a customer state by external ID.
     *
     * The customer state includes information about
     * the customer's active subscriptions and benefits.
     *
     * It's the ideal endpoint to use when you need to get a full overview
     * of a customer's status.
     *
     * **Scopes**: `customers:read` `customers:write`
     */ async getStateExternal(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customersGetStateExternal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customersGetStateExternal"])(this, request, options));
    }
} //# sourceMappingURL=customers.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/customersessions.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "CustomerSessions": (()=>CustomerSessions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerSessionsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customerSessionsCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
;
;
;
class CustomerSessions extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * Create Customer Session
     *
     * @remarks
     * Create a customer session.
     *
     * **Scopes**: `customer_sessions:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customerSessionsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customerSessionsCreate"])(this, request, options));
    }
} //# sourceMappingURL=customersessions.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/customfields.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "CustomFields": (()=>CustomFields)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customFieldsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customFieldsCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customFieldsDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customFieldsDelete.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customFieldsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customFieldsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customFieldsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customFieldsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customFieldsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/customFieldsUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class CustomFields extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Custom Fields
     *
     * @remarks
     * List custom fields.
     *
     * **Scopes**: `custom_fields:read` `custom_fields:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customFieldsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customFieldsList"])(this, request, options));
    }
    /**
     * Create Custom Field
     *
     * @remarks
     * Create a custom field.
     *
     * **Scopes**: `custom_fields:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customFieldsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customFieldsCreate"])(this, request, options));
    }
    /**
     * Get Custom Field
     *
     * @remarks
     * Get a custom field by ID.
     *
     * **Scopes**: `custom_fields:read` `custom_fields:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customFieldsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customFieldsGet"])(this, request, options));
    }
    /**
     * Update Custom Field
     *
     * @remarks
     * Update a custom field.
     *
     * **Scopes**: `custom_fields:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customFieldsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customFieldsUpdate"])(this, request, options));
    }
    /**
     * Delete Custom Field
     *
     * @remarks
     * Delete a custom field.
     *
     * **Scopes**: `custom_fields:write`
     */ async delete(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$customFieldsDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["customFieldsDelete"])(this, request, options));
    }
} //# sourceMappingURL=customfields.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/discounts.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Discounts": (()=>Discounts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$discountsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/discountsCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$discountsDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/discountsDelete.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$discountsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/discountsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$discountsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/discountsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$discountsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/discountsUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class Discounts extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Discounts
     *
     * @remarks
     * List discounts.
     *
     * **Scopes**: `discounts:read` `discounts:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$discountsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["discountsList"])(this, request, options));
    }
    /**
     * Create Discount
     *
     * @remarks
     * Create a discount.
     *
     * **Scopes**: `discounts:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$discountsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["discountsCreate"])(this, request, options));
    }
    /**
     * Get Discount
     *
     * @remarks
     * Get a discount by ID.
     *
     * **Scopes**: `discounts:read` `discounts:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$discountsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["discountsGet"])(this, request, options));
    }
    /**
     * Update Discount
     *
     * @remarks
     * Update a discount.
     *
     * **Scopes**: `discounts:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$discountsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["discountsUpdate"])(this, request, options));
    }
    /**
     * Delete Discount
     *
     * @remarks
     * Delete a discount.
     *
     * **Scopes**: `discounts:write`
     */ async delete(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$discountsDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["discountsDelete"])(this, request, options));
    }
} //# sourceMappingURL=discounts.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/events.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Events": (()=>Events)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$eventsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/eventsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$eventsIngest$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/eventsIngest.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$eventsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/eventsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$eventsListNames$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/eventsListNames.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
class Events extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Events
     *
     * @remarks
     * List events.
     *
     * **Scopes**: `events:read` `events:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$eventsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eventsList"])(this, request, options));
    }
    /**
     * List Event Names
     *
     * @remarks
     * List event names.
     *
     * **Scopes**: `events:read` `events:write`
     */ async listNames(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$eventsListNames$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eventsListNames"])(this, request, options));
    }
    /**
     * Get Event
     *
     * @remarks
     * Get an event by ID.
     *
     * **Scopes**: `events:read` `events:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$eventsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eventsGet"])(this, request, options));
    }
    /**
     * Ingest Events
     *
     * @remarks
     * Ingest batch of events.
     *
     * **Scopes**: `events:write`
     */ async ingest(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$eventsIngest$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["eventsIngest"])(this, request, options));
    }
} //# sourceMappingURL=events.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/files.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Files": (()=>Files)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$filesCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/filesCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$filesDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/filesDelete.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$filesList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/filesList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$filesUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/filesUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$filesUploaded$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/filesUploaded.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class Files extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Files
     *
     * @remarks
     * List files.
     *
     * **Scopes**: `files:read` `files:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$filesList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["filesList"])(this, request, options));
    }
    /**
     * Create File
     *
     * @remarks
     * Create a file.
     *
     * **Scopes**: `files:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$filesCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["filesCreate"])(this, request, options));
    }
    /**
     * Complete File Upload
     *
     * @remarks
     * Complete a file upload.
     *
     * **Scopes**: `files:write`
     */ async uploaded(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$filesUploaded$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["filesUploaded"])(this, request, options));
    }
    /**
     * Update File
     *
     * @remarks
     * Update a file.
     *
     * **Scopes**: `files:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$filesUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["filesUpdate"])(this, request, options));
    }
    /**
     * Delete File
     *
     * @remarks
     * Delete a file.
     *
     * **Scopes**: `files:write`
     */ async delete(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$filesDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["filesDelete"])(this, request, options));
    }
} //# sourceMappingURL=files.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/licensekeys.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "LicenseKeys": (()=>LicenseKeys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$licenseKeysGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/licenseKeysGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$licenseKeysGetActivation$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/licenseKeysGetActivation.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$licenseKeysList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/licenseKeysList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$licenseKeysUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/licenseKeysUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
class LicenseKeys extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List License Keys
     *
     * @remarks
     * Get license keys connected to the given organization & filters.
     *
     * **Scopes**: `license_keys:read` `license_keys:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$licenseKeysList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["licenseKeysList"])(this, request, options));
    }
    /**
     * Get License Key
     *
     * @remarks
     * Get a license key.
     *
     * **Scopes**: `license_keys:read` `license_keys:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$licenseKeysGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["licenseKeysGet"])(this, request, options));
    }
    /**
     * Update License Key
     *
     * @remarks
     * Update a license key.
     *
     * **Scopes**: `license_keys:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$licenseKeysUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["licenseKeysUpdate"])(this, request, options));
    }
    /**
     * Get Activation
     *
     * @remarks
     * Get a license key activation.
     *
     * **Scopes**: `license_keys:read` `license_keys:write`
     */ async getActivation(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$licenseKeysGetActivation$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["licenseKeysGetActivation"])(this, request, options));
    }
} //# sourceMappingURL=licensekeys.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/meters.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Meters": (()=>Meters)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metersCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/metersCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/metersGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/metersList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metersQuantities$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/metersQuantities.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metersUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/metersUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class Meters extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Meters
     *
     * @remarks
     * List meters.
     *
     * **Scopes**: `meters:read` `meters:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["metersList"])(this, request, options));
    }
    /**
     * Create Meter
     *
     * @remarks
     * Create a meter.
     *
     * **Scopes**: `meters:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metersCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["metersCreate"])(this, request, options));
    }
    /**
     * Get Meter
     *
     * @remarks
     * Get a meter by ID.
     *
     * **Scopes**: `meters:read` `meters:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["metersGet"])(this, request, options));
    }
    /**
     * Update Meter
     *
     * @remarks
     * Update a meter.
     *
     * **Scopes**: `meters:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metersUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["metersUpdate"])(this, request, options));
    }
    /**
     * Get Meter Quantities
     *
     * @remarks
     * Get quantities of a meter over a time period.
     *
     * **Scopes**: `meters:read` `meters:write`
     */ async quantities(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metersQuantities$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["metersQuantities"])(this, request, options));
    }
} //# sourceMappingURL=meters.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/metrics.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Metrics": (()=>Metrics)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metricsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/metricsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metricsLimits$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/metricsLimits.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
;
;
;
;
class Metrics extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * Get Metrics
     *
     * @remarks
     * Get metrics about your orders and subscriptions.
     *
     * Currency values are output in cents.
     *
     * **Scopes**: `metrics:read`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metricsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["metricsGet"])(this, request, options));
    }
    /**
     * Get Metrics Limits
     *
     * @remarks
     * Get the interval limits for the metrics endpoint.
     *
     * **Scopes**: `metrics:read`
     */ async limits(options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$metricsLimits$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["metricsLimits"])(this, options));
    }
} //# sourceMappingURL=metrics.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/clients.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Clients": (()=>Clients)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2ClientsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/oauth2ClientsCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2ClientsDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/oauth2ClientsDelete.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2ClientsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/oauth2ClientsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2ClientsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/oauth2ClientsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2ClientsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/oauth2ClientsUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class Clients extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Clients
     *
     * @remarks
     * List OAuth2 clients.
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2ClientsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["oauth2ClientsList"])(this, request, options));
    }
    /**
     * Create Client
     *
     * @remarks
     * Create an OAuth2 client.
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2ClientsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["oauth2ClientsCreate"])(this, request, options));
    }
    /**
     * Get Client
     *
     * @remarks
     * Get an OAuth2 client by Client ID.
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2ClientsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["oauth2ClientsGet"])(this, request, options));
    }
    /**
     * Update Client
     *
     * @remarks
     * Update an OAuth2 client.
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2ClientsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["oauth2ClientsUpdate"])(this, request, options));
    }
    /**
     * Delete Client
     *
     * @remarks
     * Delete an OAuth2 client.
     */ async delete(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2ClientsDelete$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["oauth2ClientsDelete"])(this, request, options));
    }
} //# sourceMappingURL=clients.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/oauth2.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Oauth2": (()=>Oauth2)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2Authorize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/oauth2Authorize.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2Introspect$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/oauth2Introspect.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2Revoke$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/oauth2Revoke.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2Token$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/oauth2Token.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2Userinfo$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/oauth2Userinfo.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$clients$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/clients.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class Oauth2 extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    get clients() {
        return this._clients ?? (this._clients = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$clients$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Clients"](this._options));
    }
    /**
     * Authorize
     */ async authorize(options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2Authorize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["oauth2Authorize"])(this, options));
    }
    /**
     * Request Token
     *
     * @remarks
     * Request an access token using a valid grant.
     */ async token(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2Token$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["oauth2Token"])(this, request, options));
    }
    /**
     * Revoke Token
     *
     * @remarks
     * Revoke an access token or a refresh token.
     */ async revoke(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2Revoke$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["oauth2Revoke"])(this, request, options));
    }
    /**
     * Introspect Token
     *
     * @remarks
     * Get information about an access token.
     */ async introspect(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2Introspect$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["oauth2Introspect"])(this, request, options));
    }
    /**
     * Get User Info
     *
     * @remarks
     * Get information about the authenticated user.
     */ async userinfo(options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$oauth2Userinfo$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["oauth2Userinfo"])(this, options));
    }
} //# sourceMappingURL=oauth2.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/orders.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Orders": (()=>Orders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$ordersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/ordersGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$ordersInvoice$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/ordersInvoice.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$ordersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/ordersList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
class Orders extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Orders
     *
     * @remarks
     * List orders.
     *
     * **Scopes**: `orders:read`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$ordersList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ordersList"])(this, request, options));
    }
    /**
     * Get Order
     *
     * @remarks
     * Get an order by ID.
     *
     * **Scopes**: `orders:read`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$ordersGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ordersGet"])(this, request, options));
    }
    /**
     * Get Order Invoice
     *
     * @remarks
     * Get an order's invoice data.
     *
     * **Scopes**: `orders:read`
     */ async invoice(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$ordersInvoice$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ordersInvoice"])(this, request, options));
    }
} //# sourceMappingURL=orders.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/organizations.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Organizations": (()=>Organizations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$organizationsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/organizationsCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$organizationsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/organizationsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$organizationsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/organizationsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$organizationsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/organizationsUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
class Organizations extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Organizations
     *
     * @remarks
     * List organizations.
     *
     * **Scopes**: `organizations:read` `organizations:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$organizationsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["organizationsList"])(this, request, options));
    }
    /**
     * Create Organization
     *
     * @remarks
     * Create an organization.
     *
     * **Scopes**: `organizations:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$organizationsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["organizationsCreate"])(this, request, options));
    }
    /**
     * Get Organization
     *
     * @remarks
     * Get an organization by ID.
     *
     * **Scopes**: `organizations:read` `organizations:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$organizationsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["organizationsGet"])(this, request, options));
    }
    /**
     * Update Organization
     *
     * @remarks
     * Update an organization.
     *
     * **Scopes**: `organizations:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$organizationsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["organizationsUpdate"])(this, request, options));
    }
} //# sourceMappingURL=organizations.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/payments.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Payments": (()=>Payments)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$paymentsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/paymentsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$paymentsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/paymentsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
class Payments extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Payments
     *
     * @remarks
     * List payments.
     *
     * **Scopes**: `payments:read`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$paymentsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["paymentsList"])(this, request, options));
    }
    /**
     * Get Payment
     *
     * @remarks
     * Get a payment by ID.
     *
     * **Scopes**: `payments:read`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$paymentsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["paymentsGet"])(this, request, options));
    }
} //# sourceMappingURL=payments.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/products.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Products": (()=>Products)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$productsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/productsCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$productsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/productsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$productsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/productsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$productsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/productsUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$productsUpdateBenefits$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/productsUpdateBenefits.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class Products extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Products
     *
     * @remarks
     * List products.
     *
     * **Scopes**: `products:read` `products:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$productsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["productsList"])(this, request, options));
    }
    /**
     * Create Product
     *
     * @remarks
     * Create a product.
     *
     * **Scopes**: `products:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$productsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["productsCreate"])(this, request, options));
    }
    /**
     * Get Product
     *
     * @remarks
     * Get a product by ID.
     *
     * **Scopes**: `products:read` `products:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$productsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["productsGet"])(this, request, options));
    }
    /**
     * Update Product
     *
     * @remarks
     * Update a product.
     *
     * **Scopes**: `products:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$productsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["productsUpdate"])(this, request, options));
    }
    /**
     * Update Product Benefits
     *
     * @remarks
     * Update benefits granted by a product.
     *
     * **Scopes**: `products:write`
     */ async updateBenefits(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$productsUpdateBenefits$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["productsUpdateBenefits"])(this, request, options));
    }
} //# sourceMappingURL=products.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/refunds.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Refunds": (()=>Refunds)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$refundsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/refundsCreate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$refundsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/refundsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
class Refunds extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Refunds
     *
     * @remarks
     * List products.
     *
     * **Scopes**: `refunds:read` `refunds:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$refundsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["refundsList"])(this, request, options));
    }
    /**
     * Create Refund
     *
     * @remarks
     * Create a refund.
     *
     * **Scopes**: `refunds:write`
     */ async create(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$refundsCreate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["refundsCreate"])(this, request, options));
    }
} //# sourceMappingURL=refunds.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/subscriptions.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Subscriptions": (()=>Subscriptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$subscriptionsExport$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/subscriptionsExport.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$subscriptionsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/subscriptionsGet.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$subscriptionsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/subscriptionsList.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$subscriptionsRevoke$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/subscriptionsRevoke.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$subscriptionsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/funcs/subscriptionsUpdate.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/fp.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/types/operations.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
class Subscriptions extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    /**
     * List Subscriptions
     *
     * @remarks
     * List subscriptions.
     *
     * **Scopes**: `subscriptions:read` `subscriptions:write`
     */ async list(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$operations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapResultIterator"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$subscriptionsList$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptionsList"])(this, request, options));
    }
    /**
     * Export Subscriptions
     *
     * @remarks
     * Export subscriptions as a CSV file.
     *
     * **Scopes**: `subscriptions:read` `subscriptions:write`
     */ async export(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$subscriptionsExport$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptionsExport"])(this, request, options));
    }
    /**
     * Get Subscription
     *
     * @remarks
     * Get a subscription by ID.
     *
     * **Scopes**: `subscriptions:write`
     */ async get(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$subscriptionsGet$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptionsGet"])(this, request, options));
    }
    /**
     * Update Subscription
     *
     * @remarks
     * Update a subscription.
     *
     * **Scopes**: `subscriptions:write`
     */ async update(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$subscriptionsUpdate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptionsUpdate"])(this, request, options));
    }
    /**
     * Revoke Subscription
     *
     * @remarks
     * Revoke a subscription, i.e cancel immediately.
     *
     * **Scopes**: `subscriptions:write`
     */ async revoke(request, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$types$2f$fp$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["unwrapAsync"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$funcs$2f$subscriptionsRevoke$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscriptionsRevoke"])(this, request, options));
    }
} //# sourceMappingURL=subscriptions.js.map
}}),
"[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/sdk.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */ __turbopack_context__.s({
    "Polar": (()=>Polar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/lib/sdks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$benefits$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/benefits.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$checkoutlinks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/checkoutlinks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$checkouts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/checkouts.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$customermeters$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/customermeters.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$customerportal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/customerportal.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$customers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/customers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$customersessions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/customersessions.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$customfields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/customfields.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$discounts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/discounts.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$events$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/events.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$files$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/files.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$licensekeys$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/licensekeys.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$meters$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/meters.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$metrics$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/metrics.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$oauth2$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/oauth2.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$orders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/orders.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$organizations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/organizations.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$payments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/payments.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$products$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/products.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$refunds$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/refunds.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$subscriptions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@polar-sh/sdk/dist/esm/sdk/subscriptions.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
class Polar extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$lib$2f$sdks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ClientSDK"] {
    get organizations() {
        return this._organizations ?? (this._organizations = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$organizations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Organizations"](this._options));
    }
    get subscriptions() {
        return this._subscriptions ?? (this._subscriptions = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$subscriptions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Subscriptions"](this._options));
    }
    get oauth2() {
        return this._oauth2 ?? (this._oauth2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$oauth2$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Oauth2"](this._options));
    }
    get benefits() {
        return this._benefits ?? (this._benefits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$benefits$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Benefits"](this._options));
    }
    get products() {
        return this._products ?? (this._products = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$products$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Products"](this._options));
    }
    get orders() {
        return this._orders ?? (this._orders = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$orders$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Orders"](this._options));
    }
    get refunds() {
        return this._refunds ?? (this._refunds = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$refunds$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Refunds"](this._options));
    }
    get checkouts() {
        return this._checkouts ?? (this._checkouts = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$checkouts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Checkouts"](this._options));
    }
    get files() {
        return this._files ?? (this._files = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$files$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Files"](this._options));
    }
    get metrics() {
        return this._metrics ?? (this._metrics = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$metrics$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Metrics"](this._options));
    }
    get licenseKeys() {
        return this._licenseKeys ?? (this._licenseKeys = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$licensekeys$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LicenseKeys"](this._options));
    }
    get checkoutLinks() {
        return this._checkoutLinks ?? (this._checkoutLinks = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$checkoutlinks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CheckoutLinks"](this._options));
    }
    get customFields() {
        return this._customFields ?? (this._customFields = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$customfields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CustomFields"](this._options));
    }
    get discounts() {
        return this._discounts ?? (this._discounts = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$discounts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Discounts"](this._options));
    }
    get customers() {
        return this._customers ?? (this._customers = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$customers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Customers"](this._options));
    }
    get customerPortal() {
        return this._customerPortal ?? (this._customerPortal = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$customerportal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CustomerPortal"](this._options));
    }
    get customerSessions() {
        return this._customerSessions ?? (this._customerSessions = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$customersessions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CustomerSessions"](this._options));
    }
    get events() {
        return this._events ?? (this._events = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$events$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Events"](this._options));
    }
    get meters() {
        return this._meters ?? (this._meters = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$meters$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Meters"](this._options));
    }
    get customerMeters() {
        return this._customerMeters ?? (this._customerMeters = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$customermeters$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CustomerMeters"](this._options));
    }
    get payments() {
        return this._payments ?? (this._payments = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$polar$2d$sh$2f$sdk$2f$dist$2f$esm$2f$sdk$2f$payments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Payments"](this._options));
    }
} //# sourceMappingURL=sdk.js.map
}}),

};

//# sourceMappingURL=node_modules_%40polar-sh_sdk_dist_esm_sdk_d15b718b._.js.map