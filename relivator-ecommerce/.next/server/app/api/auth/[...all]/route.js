const CHUNK_PUBLIC_PATH = "server/app/api/auth/[...all]/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_96f3ebed._.js");
runtime.loadChunk("server/chunks/node_modules_next_4eee130a._.js");
runtime.loadChunk("server/chunks/node_modules_better-auth_dist_shared_3cb9ab3d._.js");
runtime.loadChunk("server/chunks/node_modules_better-auth_dist_plugins_3a3d7382._.js");
runtime.loadChunk("server/chunks/node_modules_better-auth_dist_bf608b6a._.js");
runtime.loadChunk("server/chunks/node_modules_better-call_dist_index_5d513ab4.js");
runtime.loadChunk("server/chunks/node_modules_zod_dist_esm_54374b3b._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_esm_aa3d1626._.js");
runtime.loadChunk("server/chunks/node_modules_kysely_dist_esm_37ebfac0._.js");
runtime.loadChunk("server/chunks/node_modules_drizzle-orm_a10b2c49._.js");
runtime.loadChunk("server/chunks/node_modules_@polar-sh_sdk_dist_esm_models_components_661959f6._.js");
runtime.loadChunk("server/chunks/node_modules_@polar-sh_sdk_dist_esm_models_operations_3ecb5372._.js");
runtime.loadChunk("server/chunks/node_modules_@polar-sh_sdk_dist_esm_models_errors_e20ee4cf._.js");
runtime.loadChunk("server/chunks/node_modules_@polar-sh_sdk_dist_esm_funcs_9abaadf7._.js");
runtime.loadChunk("server/chunks/node_modules_@polar-sh_sdk_dist_esm_sdk_d15b718b._.js");
runtime.loadChunk("server/chunks/node_modules_@polar-sh_sdk_dist_esm_20e5ff1a._.js");
runtime.loadChunk("server/chunks/node_modules_209dd8e0._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__43f92eca._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/auth/[...all]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/auth/[...all]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/auth/[...all]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
