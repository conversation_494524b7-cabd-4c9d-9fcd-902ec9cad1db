/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { PolarCore } from "../core.js";
import { SDKOptions } from "../lib/config.js";
import type { ConsoleLogger } from "./console-logger.js";
import { createRegisterPrompt } from "./prompts.js";
import {
  createRegisterResource,
  createRegisterResourceTemplate,
} from "./resources.js";
import { MCPScope } from "./scopes.js";
import { createRegisterTool } from "./tools.js";
import { tool$customerMetersGet } from "./tools/customerMetersGet.js";
import { tool$customerMetersList } from "./tools/customerMetersList.js";
import { tool$customersCreate } from "./tools/customersCreate.js";
import { tool$customersDelete } from "./tools/customersDelete.js";
import { tool$customersDeleteExternal } from "./tools/customersDeleteExternal.js";
import { tool$customersGet } from "./tools/customersGet.js";
import { tool$customersGetExternal } from "./tools/customersGetExternal.js";
import { tool$customersGetState } from "./tools/customersGetState.js";
import { tool$customersGetStateExternal } from "./tools/customersGetStateExternal.js";
import { tool$customersList } from "./tools/customersList.js";
import { tool$customersUpdate } from "./tools/customersUpdate.js";
import { tool$customersUpdateExternal } from "./tools/customersUpdateExternal.js";
import { tool$metricsGet } from "./tools/metricsGet.js";
import { tool$metricsLimits } from "./tools/metricsLimits.js";
import { tool$ordersGet } from "./tools/ordersGet.js";
import { tool$ordersInvoice } from "./tools/ordersInvoice.js";
import { tool$ordersList } from "./tools/ordersList.js";
import { tool$paymentsGet } from "./tools/paymentsGet.js";
import { tool$paymentsList } from "./tools/paymentsList.js";
import { tool$productsCreate } from "./tools/productsCreate.js";
import { tool$productsGet } from "./tools/productsGet.js";
import { tool$productsList } from "./tools/productsList.js";
import { tool$productsUpdate } from "./tools/productsUpdate.js";
import { tool$productsUpdateBenefits } from "./tools/productsUpdateBenefits.js";
import { tool$subscriptionsExport } from "./tools/subscriptionsExport.js";
import { tool$subscriptionsGet } from "./tools/subscriptionsGet.js";
import { tool$subscriptionsList } from "./tools/subscriptionsList.js";
import { tool$subscriptionsRevoke } from "./tools/subscriptionsRevoke.js";
import { tool$subscriptionsUpdate } from "./tools/subscriptionsUpdate.js";

export function createMCPServer(deps: {
  logger: ConsoleLogger;
  allowedTools?: string[] | undefined;
  scopes?: MCPScope[] | undefined;
  serverURL?: string | undefined;
  accessToken?: SDKOptions["accessToken"] | undefined;
  server?: SDKOptions["server"] | undefined;
}) {
  const server = new McpServer({
    name: "Polar",
    version: "0.32.16",
  });

  const client = new PolarCore({
    accessToken: deps.accessToken,
    serverURL: deps.serverURL,
    server: deps.server,
  });

  const scopes = new Set(deps.scopes);

  const allowedTools = deps.allowedTools && new Set(deps.allowedTools);
  const tool = createRegisterTool(
    deps.logger,
    server,
    client,
    scopes,
    allowedTools,
  );
  const resource = createRegisterResource(deps.logger, server, client, scopes);
  const resourceTemplate = createRegisterResourceTemplate(
    deps.logger,
    server,
    client,
    scopes,
  );
  const prompt = createRegisterPrompt(deps.logger, server, client, scopes);
  const register = { tool, resource, resourceTemplate, prompt };
  void register; // suppress unused warnings

  tool(tool$subscriptionsList);
  tool(tool$subscriptionsExport);
  tool(tool$subscriptionsGet);
  tool(tool$subscriptionsUpdate);
  tool(tool$subscriptionsRevoke);
  tool(tool$productsList);
  tool(tool$productsCreate);
  tool(tool$productsGet);
  tool(tool$productsUpdate);
  tool(tool$productsUpdateBenefits);
  tool(tool$ordersList);
  tool(tool$ordersGet);
  tool(tool$ordersInvoice);
  tool(tool$metricsGet);
  tool(tool$metricsLimits);
  tool(tool$customersList);
  tool(tool$customersCreate);
  tool(tool$customersGet);
  tool(tool$customersUpdate);
  tool(tool$customersDelete);
  tool(tool$customersGetExternal);
  tool(tool$customersUpdateExternal);
  tool(tool$customersDeleteExternal);
  tool(tool$customersGetState);
  tool(tool$customersGetStateExternal);
  tool(tool$customerMetersList);
  tool(tool$customerMetersGet);
  tool(tool$paymentsList);
  tool(tool$paymentsGet);

  return server;
}
