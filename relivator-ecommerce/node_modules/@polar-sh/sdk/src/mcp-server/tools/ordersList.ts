/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { ordersList } from "../../funcs/ordersList.js";
import { OrdersListRequest$inboundSchema } from "../../models/operations/orderslist.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: OrdersListRequest$inboundSchema,
};

export const tool$ordersList: ToolDefinition<typeof args> = {
  name: "orders-list",
  description: `List Orders

List orders.

**Scopes**: \`orders:read\``,
  scopes: ["read", "orders"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await ordersList(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value.result;

    return formatResult(value, apiCall);
  },
};
