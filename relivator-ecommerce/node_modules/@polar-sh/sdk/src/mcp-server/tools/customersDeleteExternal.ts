/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { customersDeleteExternal } from "../../funcs/customersDeleteExternal.js";
import { CustomersDeleteExternalRequest$inboundSchema } from "../../models/operations/customersdeleteexternal.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: CustomersDeleteExternalRequest$inboundSchema,
};

export const tool$customersDeleteExternal: ToolDefinition<typeof args> = {
  name: "customers-delete-external",
  description: `Delete Customer by External ID

Delete a customer by external ID.

Immediately cancels any active subscriptions and revokes any active benefits.

**Scopes**: \`customers:write\``,
  scopes: ["write", "customers"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await customersDeleteExternal(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    return formatResult(void 0, apiCall);
  },
};
