/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { productsUpdateBenefits } from "../../funcs/productsUpdateBenefits.js";
import { ProductsUpdateBenefitsRequest$inboundSchema } from "../../models/operations/productsupdatebenefits.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: ProductsUpdateBenefitsRequest$inboundSchema,
};

export const tool$productsUpdateBenefits: ToolDefinition<typeof args> = {
  name: "products-update-benefits",
  description: `Update Product Benefits

Update benefits granted by a product.

**Scopes**: \`products:write\``,
  scopes: ["write", "products"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await productsUpdateBenefits(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
