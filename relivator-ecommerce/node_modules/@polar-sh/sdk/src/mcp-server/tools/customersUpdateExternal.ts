/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { customersUpdateExternal } from "../../funcs/customersUpdateExternal.js";
import { CustomersUpdateExternalRequest$inboundSchema } from "../../models/operations/customersupdateexternal.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: CustomersUpdateExternalRequest$inboundSchema,
};

export const tool$customersUpdateExternal: ToolDefinition<typeof args> = {
  name: "customers-update-external",
  description: `Update Customer by External ID

Update a customer by external ID.

**Scopes**: \`customers:write\``,
  scopes: ["write", "customers"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await customersUpdateExternal(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
