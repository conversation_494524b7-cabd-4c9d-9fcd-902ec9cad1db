/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { subscriptionsUpdate } from "../../funcs/subscriptionsUpdate.js";
import { SubscriptionsUpdateRequest$inboundSchema } from "../../models/operations/subscriptionsupdate.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: SubscriptionsUpdateRequest$inboundSchema,
};

export const tool$subscriptionsUpdate: ToolDefinition<typeof args> = {
  name: "subscriptions-update",
  description: `Update Subscription

Update a subscription.

**Scopes**: \`subscriptions:write\``,
  scopes: ["write", "subscriptions"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await subscriptionsUpdate(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
