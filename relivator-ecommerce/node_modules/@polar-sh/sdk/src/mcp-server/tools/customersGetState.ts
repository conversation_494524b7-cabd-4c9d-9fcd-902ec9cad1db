/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { customersGetState } from "../../funcs/customersGetState.js";
import { CustomersGetStateRequest$inboundSchema } from "../../models/operations/customersgetstate.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: CustomersGetStateRequest$inboundSchema,
};

export const tool$customersGetState: ToolDefinition<typeof args> = {
  name: "customers-get-state",
  description: `Get Customer State

Get a customer state by ID.

The customer state includes information about
the customer's active subscriptions and benefits.

It's the ideal endpoint to use when you need to get a full overview
of a customer's status.

**Scopes**: \`customers:read\` \`customers:write\``,
  scopes: ["read", "customers"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await customersGetState(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
