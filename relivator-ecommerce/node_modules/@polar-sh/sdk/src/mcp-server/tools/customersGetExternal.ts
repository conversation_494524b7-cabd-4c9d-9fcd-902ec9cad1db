/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { customersGetExternal } from "../../funcs/customersGetExternal.js";
import { CustomersGetExternalRequest$inboundSchema } from "../../models/operations/customersgetexternal.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: CustomersGetExternalRequest$inboundSchema,
};

export const tool$customersGetExternal: ToolDefinition<typeof args> = {
  name: "customers-get-external",
  description: `Get Customer by External ID

Get a customer by external ID.

**Scopes**: \`customers:read\` \`customers:write\``,
  scopes: ["read", "customers"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await customersGetExternal(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
