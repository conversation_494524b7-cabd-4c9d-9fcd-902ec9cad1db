/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { subscriptionsGet } from "../../funcs/subscriptionsGet.js";
import { SubscriptionsGetRequest$inboundSchema } from "../../models/operations/subscriptionsget.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: SubscriptionsGetRequest$inboundSchema,
};

export const tool$subscriptionsGet: ToolDefinition<typeof args> = {
  name: "subscriptions-get",
  description: `Get Subscription

Get a subscription by ID.

**Scopes**: \`subscriptions:write\``,
  scopes: ["read", "subscriptions"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await subscriptionsGet(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
