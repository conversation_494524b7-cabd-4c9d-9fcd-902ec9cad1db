/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { customersGet } from "../../funcs/customersGet.js";
import { CustomersGetRequest$inboundSchema } from "../../models/operations/customersget.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: CustomersGetRequest$inboundSchema,
};

export const tool$customersGet: ToolDefinition<typeof args> = {
  name: "customers-get",
  description: `Get Customer

Get a customer by ID.

**Scopes**: \`customers:read\` \`customers:write\``,
  scopes: ["read", "customers"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await customersGet(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
