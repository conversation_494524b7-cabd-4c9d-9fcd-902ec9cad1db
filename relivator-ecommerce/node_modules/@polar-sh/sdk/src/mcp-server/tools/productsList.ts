/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { productsList } from "../../funcs/productsList.js";
import { ProductsListRequest$inboundSchema } from "../../models/operations/productslist.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: ProductsListRequest$inboundSchema,
};

export const tool$productsList: ToolDefinition<typeof args> = {
  name: "products-list",
  description: `List Products

List products.

**Scopes**: \`products:read\` \`products:write\``,
  scopes: ["read", "products"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await productsList(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value.result;

    return formatResult(value, apiCall);
  },
};
