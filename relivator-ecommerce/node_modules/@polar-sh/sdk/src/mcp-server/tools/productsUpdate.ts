/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { productsUpdate } from "../../funcs/productsUpdate.js";
import { ProductsUpdateRequest$inboundSchema } from "../../models/operations/productsupdate.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: ProductsUpdateRequest$inboundSchema,
};

export const tool$productsUpdate: ToolDefinition<typeof args> = {
  name: "products-update",
  description: `Update Product

Update a product.

**Scopes**: \`products:write\``,
  scopes: ["write", "products"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await productsUpdate(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
