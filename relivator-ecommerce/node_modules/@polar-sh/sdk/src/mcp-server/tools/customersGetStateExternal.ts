/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { customersGetStateExternal } from "../../funcs/customersGetStateExternal.js";
import { CustomersGetStateExternalRequest$inboundSchema } from "../../models/operations/customersgetstateexternal.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: CustomersGetStateExternalRequest$inboundSchema,
};

export const tool$customersGetStateExternal: ToolDefinition<typeof args> = {
  name: "customers-get-state-external",
  description: `Get Customer State by External ID

Get a customer state by external ID.

The customer state includes information about
the customer's active subscriptions and benefits.

It's the ideal endpoint to use when you need to get a full overview
of a customer's status.

**Scopes**: \`customers:read\` \`customers:write\``,
  scopes: ["read", "customers"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await customersGetStateExternal(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
