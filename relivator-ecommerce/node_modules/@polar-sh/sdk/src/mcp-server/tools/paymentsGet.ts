/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { paymentsGet } from "../../funcs/paymentsGet.js";
import { PaymentsGetRequest$inboundSchema } from "../../models/operations/paymentsget.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: PaymentsGetRequest$inboundSchema,
};

export const tool$paymentsGet: ToolDefinition<typeof args> = {
  name: "payments-get",
  description: `Get Payment

Get a payment by ID.

**Scopes**: \`payments:read\``,
  scopes: ["read", "payments"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await paymentsGet(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
