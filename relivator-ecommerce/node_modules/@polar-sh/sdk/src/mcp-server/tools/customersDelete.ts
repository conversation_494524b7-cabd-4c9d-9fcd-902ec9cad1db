/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { customersDelete } from "../../funcs/customersDelete.js";
import { CustomersDeleteRequest$inboundSchema } from "../../models/operations/customersdelete.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: CustomersDeleteRequest$inboundSchema,
};

export const tool$customersDelete: ToolDefinition<typeof args> = {
  name: "customers-delete",
  description: `Delete Customer

Delete a customer.

This action cannot be undone and will immediately:
- Cancel any active subscriptions for the customer
- Revoke all their benefits
- Clear any \`external_id\`

Use it only in the context of deleting a user within your
own service. Otherwise, use more granular API endpoints to cancel
a specific subscription or revoke certain benefits.

Note: The customers information will nonetheless be retained for historic
orders and subscriptions.

**Scopes**: \`customers:write\``,
  scopes: ["write", "customers"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await customersDelete(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    return formatResult(void 0, apiCall);
  },
};
