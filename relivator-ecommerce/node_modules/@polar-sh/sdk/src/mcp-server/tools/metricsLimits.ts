/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { metricsLimits } from "../../funcs/metricsLimits.js";
import { formatResult, ToolDefinition } from "../tools.js";

export const tool$metricsLimits: ToolDefinition = {
  name: "metrics-limits",
  description: `Get Metrics Limits

Get the interval limits for the metrics endpoint.

**Scopes**: \`metrics:read\``,
  scopes: ["read", "metrics"],
  tool: async (client, ctx) => {
    const [result, apiCall] = await metricsLimits(
      client,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
