/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { customersList } from "../../funcs/customersList.js";
import { CustomersListRequest$inboundSchema } from "../../models/operations/customerslist.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: CustomersListRequest$inboundSchema,
};

export const tool$customersList: ToolDefinition<typeof args> = {
  name: "customers-list",
  description: `List Customers

List customers.

**Scopes**: \`customers:read\` \`customers:write\``,
  scopes: ["read", "customers"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await customersList(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value.result;

    return formatResult(value, apiCall);
  },
};
