/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { customersCreate } from "../../funcs/customersCreate.js";
import { CustomerCreate$inboundSchema } from "../../models/components/customercreate.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: CustomerCreate$inboundSchema,
};

export const tool$customersCreate: ToolDefinition<typeof args> = {
  name: "customers-create",
  description: `Create Customer

Create a customer.

**Scopes**: \`customers:write\``,
  scopes: ["write", "customers"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await customersCreate(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
