/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { metricsGet } from "../../funcs/metricsGet.js";
import { MetricsGetRequest$inboundSchema } from "../../models/operations/metricsget.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: MetricsGetRequest$inboundSchema,
};

export const tool$metricsGet: ToolDefinition<typeof args> = {
  name: "metrics-get",
  description: `Get Metrics

Get metrics about your orders and subscriptions.

Currency values are output in cents.

**Scopes**: \`metrics:read\``,
  scopes: ["read", "metrics"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await metricsGet(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value;

    return formatResult(value, apiCall);
  },
};
