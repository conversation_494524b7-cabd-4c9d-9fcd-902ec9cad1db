/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { subscriptionsList } from "../../funcs/subscriptionsList.js";
import { SubscriptionsListRequest$inboundSchema } from "../../models/operations/subscriptionslist.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: SubscriptionsListRequest$inboundSchema,
};

export const tool$subscriptionsList: ToolDefinition<typeof args> = {
  name: "subscriptions-list",
  description: `List Subscriptions

List subscriptions.

**Scopes**: \`subscriptions:read\` \`subscriptions:write\``,
  scopes: ["read", "subscriptions"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await subscriptionsList(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value.result;

    return formatResult(value, apiCall);
  },
};
