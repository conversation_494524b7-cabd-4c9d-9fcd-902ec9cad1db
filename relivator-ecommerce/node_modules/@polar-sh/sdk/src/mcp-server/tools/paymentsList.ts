/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { paymentsList } from "../../funcs/paymentsList.js";
import { PaymentsListRequest$inboundSchema } from "../../models/operations/paymentslist.js";
import { formatResult, ToolDefinition } from "../tools.js";

const args = {
  request: PaymentsListRequest$inboundSchema,
};

export const tool$paymentsList: ToolDefinition<typeof args> = {
  name: "payments-list",
  description: `List Payments

List payments.

**Scopes**: \`payments:read\``,
  scopes: ["read", "payments"],
  args,
  tool: async (client, args, ctx) => {
    const [result, apiCall] = await paymentsList(
      client,
      args.request,
      { fetchOptions: { signal: ctx.signal } },
    ).$inspect();

    if (!result.ok) {
      return {
        content: [{ type: "text", text: result.error.message }],
        isError: true,
      };
    }

    const value = result.value.result;

    return formatResult(value, apiCall);
  },
};
