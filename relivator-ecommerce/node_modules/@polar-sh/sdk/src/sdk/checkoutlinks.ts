/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { checkoutLinksCreate } from "../funcs/checkoutLinksCreate.js";
import { checkoutLinksDelete } from "../funcs/checkoutLinksDelete.js";
import { checkoutLinksGet } from "../funcs/checkoutLinksGet.js";
import { checkoutLinksList } from "../funcs/checkoutLinksList.js";
import { checkoutLinksUpdate } from "../funcs/checkoutLinksUpdate.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import { CheckoutLink } from "../models/components/checkoutlink.js";
import { CheckoutLinksCreateCheckoutLinkCreate } from "../models/operations/checkoutlinkscreate.js";
import { CheckoutLinksDeleteRequest } from "../models/operations/checkoutlinksdelete.js";
import { CheckoutLinksGetRequest } from "../models/operations/checkoutlinksget.js";
import {
  CheckoutLinksListRequest,
  CheckoutLinksListResponse,
} from "../models/operations/checkoutlinkslist.js";
import { CheckoutLinksUpdateRequest } from "../models/operations/checkoutlinksupdate.js";
import { unwrapAsync } from "../types/fp.js";
import { PageIterator, unwrapResultIterator } from "../types/operations.js";

export class CheckoutLinks extends ClientSDK {
  /**
   * List Checkout Links
   *
   * @remarks
   * List checkout links.
   *
   * **Scopes**: `checkout_links:read` `checkout_links:write`
   */
  async list(
    request: CheckoutLinksListRequest,
    options?: RequestOptions,
  ): Promise<PageIterator<CheckoutLinksListResponse, { page: number }>> {
    return unwrapResultIterator(checkoutLinksList(
      this,
      request,
      options,
    ));
  }

  /**
   * Create Checkout Link
   *
   * @remarks
   * Create a checkout link.
   *
   * **Scopes**: `checkout_links:write`
   */
  async create(
    request: CheckoutLinksCreateCheckoutLinkCreate,
    options?: RequestOptions,
  ): Promise<CheckoutLink> {
    return unwrapAsync(checkoutLinksCreate(
      this,
      request,
      options,
    ));
  }

  /**
   * Get Checkout Link
   *
   * @remarks
   * Get a checkout link by ID.
   *
   * **Scopes**: `checkout_links:read` `checkout_links:write`
   */
  async get(
    request: CheckoutLinksGetRequest,
    options?: RequestOptions,
  ): Promise<CheckoutLink> {
    return unwrapAsync(checkoutLinksGet(
      this,
      request,
      options,
    ));
  }

  /**
   * Update Checkout Link
   *
   * @remarks
   * Update a checkout link.
   *
   * **Scopes**: `checkout_links:write`
   */
  async update(
    request: CheckoutLinksUpdateRequest,
    options?: RequestOptions,
  ): Promise<CheckoutLink> {
    return unwrapAsync(checkoutLinksUpdate(
      this,
      request,
      options,
    ));
  }

  /**
   * Delete Checkout Link
   *
   * @remarks
   * Delete a checkout link.
   *
   * **Scopes**: `checkout_links:write`
   */
  async delete(
    request: CheckoutLinksDeleteRequest,
    options?: RequestOptions,
  ): Promise<void> {
    return unwrapAsync(checkoutLinksDelete(
      this,
      request,
      options,
    ));
  }
}
