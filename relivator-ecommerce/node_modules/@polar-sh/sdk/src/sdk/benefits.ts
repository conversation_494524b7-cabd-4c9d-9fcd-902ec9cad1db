/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { benefitsCreate } from "../funcs/benefitsCreate.js";
import { benefitsDelete } from "../funcs/benefitsDelete.js";
import { benefitsGet } from "../funcs/benefitsGet.js";
import { benefitsGrants } from "../funcs/benefitsGrants.js";
import { benefitsList } from "../funcs/benefitsList.js";
import { benefitsUpdate } from "../funcs/benefitsUpdate.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import { Benefit } from "../models/components/benefit.js";
import { BenefitCreate } from "../models/components/benefitcreate.js";
import { BenefitsDeleteRequest } from "../models/operations/benefitsdelete.js";
import { BenefitsGetRequest } from "../models/operations/benefitsget.js";
import {
  BenefitsGrantsRequest,
  BenefitsGrantsResponse,
} from "../models/operations/benefitsgrants.js";
import {
  BenefitsListRequest,
  BenefitsListResponse,
} from "../models/operations/benefitslist.js";
import { BenefitsUpdateRequest } from "../models/operations/benefitsupdate.js";
import { unwrapAsync } from "../types/fp.js";
import { PageIterator, unwrapResultIterator } from "../types/operations.js";

export class Benefits extends ClientSDK {
  /**
   * List Benefits
   *
   * @remarks
   * List benefits.
   *
   * **Scopes**: `benefits:read` `benefits:write`
   */
  async list(
    request: BenefitsListRequest,
    options?: RequestOptions,
  ): Promise<PageIterator<BenefitsListResponse, { page: number }>> {
    return unwrapResultIterator(benefitsList(
      this,
      request,
      options,
    ));
  }

  /**
   * Create Benefit
   *
   * @remarks
   * Create a benefit.
   *
   * **Scopes**: `benefits:write`
   */
  async create(
    request: BenefitCreate,
    options?: RequestOptions,
  ): Promise<Benefit> {
    return unwrapAsync(benefitsCreate(
      this,
      request,
      options,
    ));
  }

  /**
   * Get Benefit
   *
   * @remarks
   * Get a benefit by ID.
   *
   * **Scopes**: `benefits:read` `benefits:write`
   */
  async get(
    request: BenefitsGetRequest,
    options?: RequestOptions,
  ): Promise<Benefit> {
    return unwrapAsync(benefitsGet(
      this,
      request,
      options,
    ));
  }

  /**
   * Update Benefit
   *
   * @remarks
   * Update a benefit.
   *
   * **Scopes**: `benefits:write`
   */
  async update(
    request: BenefitsUpdateRequest,
    options?: RequestOptions,
  ): Promise<Benefit> {
    return unwrapAsync(benefitsUpdate(
      this,
      request,
      options,
    ));
  }

  /**
   * Delete Benefit
   *
   * @remarks
   * Delete a benefit.
   *
   * > [!WARNING]
   * > Every grants associated with the benefit will be revoked.
   * > Users will lose access to the benefit.
   *
   * **Scopes**: `benefits:write`
   */
  async delete(
    request: BenefitsDeleteRequest,
    options?: RequestOptions,
  ): Promise<void> {
    return unwrapAsync(benefitsDelete(
      this,
      request,
      options,
    ));
  }

  /**
   * List Benefit Grants
   *
   * @remarks
   * List the individual grants for a benefit.
   *
   * It's especially useful to check if a user has been granted a benefit.
   *
   * **Scopes**: `benefits:read` `benefits:write`
   */
  async grants(
    request: BenefitsGrantsRequest,
    options?: RequestOptions,
  ): Promise<PageIterator<BenefitsGrantsResponse, { page: number }>> {
    return unwrapResultIterator(benefitsGrants(
      this,
      request,
      options,
    ));
  }
}
