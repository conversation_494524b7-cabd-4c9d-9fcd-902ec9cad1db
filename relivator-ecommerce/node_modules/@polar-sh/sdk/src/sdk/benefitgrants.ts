/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { customerPortalBenefitGrantsGet } from "../funcs/customerPortalBenefitGrantsGet.js";
import { customerPortalBenefitGrantsList } from "../funcs/customerPortalBenefitGrantsList.js";
import { customerPortalBenefitGrantsUpdate } from "../funcs/customerPortalBenefitGrantsUpdate.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import { CustomerBenefitGrant } from "../models/components/customerbenefitgrant.js";
import {
  CustomerPortalBenefitGrantsGetRequest,
  CustomerPortalBenefitGrantsGetSecurity,
} from "../models/operations/customerportalbenefitgrantsget.js";
import {
  CustomerPortalBenefitGrantsListRequest,
  CustomerPortalBenefitGrantsListResponse,
  CustomerPortalBenefitGrantsListSecurity,
} from "../models/operations/customerportalbenefitgrantslist.js";
import {
  CustomerPortalBenefitGrantsUpdateRequest,
  CustomerPortalBenefitGrantsUpdateSecurity,
} from "../models/operations/customerportalbenefitgrantsupdate.js";
import { unwrapAsync } from "../types/fp.js";
import { PageIterator, unwrapResultIterator } from "../types/operations.js";

export class BenefitGrants extends ClientSDK {
  /**
   * List Benefit Grants
   *
   * @remarks
   * List benefits grants of the authenticated customer.
   *
   * **Scopes**: `customer_portal:read` `customer_portal:write`
   */
  async list(
    security: CustomerPortalBenefitGrantsListSecurity,
    request: CustomerPortalBenefitGrantsListRequest,
    options?: RequestOptions,
  ): Promise<
    PageIterator<CustomerPortalBenefitGrantsListResponse, { page: number }>
  > {
    return unwrapResultIterator(customerPortalBenefitGrantsList(
      this,
      security,
      request,
      options,
    ));
  }

  /**
   * Get Benefit Grant
   *
   * @remarks
   * Get a benefit grant by ID for the authenticated customer.
   *
   * **Scopes**: `customer_portal:read` `customer_portal:write`
   */
  async get(
    security: CustomerPortalBenefitGrantsGetSecurity,
    request: CustomerPortalBenefitGrantsGetRequest,
    options?: RequestOptions,
  ): Promise<CustomerBenefitGrant> {
    return unwrapAsync(customerPortalBenefitGrantsGet(
      this,
      security,
      request,
      options,
    ));
  }

  /**
   * Update Benefit Grant
   *
   * @remarks
   * Update a benefit grant for the authenticated customer.
   *
   * **Scopes**: `customer_portal:write`
   */
  async update(
    security: CustomerPortalBenefitGrantsUpdateSecurity,
    request: CustomerPortalBenefitGrantsUpdateRequest,
    options?: RequestOptions,
  ): Promise<CustomerBenefitGrant> {
    return unwrapAsync(customerPortalBenefitGrantsUpdate(
      this,
      security,
      request,
      options,
    ));
  }
}
