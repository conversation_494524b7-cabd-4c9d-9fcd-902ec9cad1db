/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import {
  AlreadyActiveSubscriptionError,
  AlreadyActiveSubscriptionError$inboundSchema,
  AlreadyActiveSubscriptionError$Outbound,
  AlreadyActiveSubscriptionError$outboundSchema,
} from "./alreadyactivesubscriptionerror.js";
import {
  NotOpenCheckout,
  NotOpenCheckout$inboundSchema,
  NotOpenCheckout$Outbound,
  NotOpenCheckout$outboundSchema,
} from "./notopencheckout.js";
import { SDKValidationError } from "./sdkvalidationerror.js";

export type CheckoutForbiddenError =
  | AlreadyActiveSubscriptionError
  | NotOpenCheckout;

/** @internal */
export const CheckoutForbiddenError$inboundSchema: z.ZodType<
  CheckoutForbiddenError,
  z.ZodTypeDef,
  unknown
> = z.union([
  AlreadyActiveSubscriptionError$inboundSchema,
  NotOpenCheckout$inboundSchema,
]);

/** @internal */
export type CheckoutForbiddenError$Outbound =
  | AlreadyActiveSubscriptionError$Outbound
  | NotOpenCheckout$Outbound;

/** @internal */
export const CheckoutForbiddenError$outboundSchema: z.ZodType<
  CheckoutForbiddenError$Outbound,
  z.ZodTypeDef,
  CheckoutForbiddenError
> = z.union([
  AlreadyActiveSubscriptionError$outboundSchema,
  NotOpenCheckout$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutForbiddenError$ {
  /** @deprecated use `CheckoutForbiddenError$inboundSchema` instead. */
  export const inboundSchema = CheckoutForbiddenError$inboundSchema;
  /** @deprecated use `CheckoutForbiddenError$outboundSchema` instead. */
  export const outboundSchema = CheckoutForbiddenError$outboundSchema;
  /** @deprecated use `CheckoutForbiddenError$Outbound` instead. */
  export type Outbound = CheckoutForbiddenError$Outbound;
}

export function checkoutForbiddenErrorToJSON(
  checkoutForbiddenError: CheckoutForbiddenError,
): string {
  return JSON.stringify(
    CheckoutForbiddenError$outboundSchema.parse(checkoutForbiddenError),
  );
}

export function checkoutForbiddenErrorFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutForbiddenError, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutForbiddenError$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutForbiddenError' from JSON`,
  );
}
