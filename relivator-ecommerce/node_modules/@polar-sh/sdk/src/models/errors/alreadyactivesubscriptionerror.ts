/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";

export type AlreadyActiveSubscriptionErrorData = {
  error: "AlreadyActiveSubscriptionError";
  detail: string;
};

export class AlreadyActiveSubscriptionError extends Error {
  error: "AlreadyActiveSubscriptionError";
  detail: string;

  /** The original data that was passed to this error instance. */
  data$: AlreadyActiveSubscriptionErrorData;

  constructor(err: AlreadyActiveSubscriptionErrorData) {
    const message = "message" in err && typeof err.message === "string"
      ? err.message
      : `API error occurred: ${JSON.stringify(err)}`;
    super(message);
    this.data$ = err;

    this.error = err.error;
    this.detail = err.detail;

    this.name = "AlreadyActiveSubscriptionError";
  }
}

/** @internal */
export const AlreadyActiveSubscriptionError$inboundSchema: z.ZodType<
  AlreadyActiveSubscriptionError,
  z.ZodTypeDef,
  unknown
> = z.object({
  error: z.literal("AlreadyActiveSubscriptionError"),
  detail: z.string(),
})
  .transform((v) => {
    return new AlreadyActiveSubscriptionError(v);
  });

/** @internal */
export type AlreadyActiveSubscriptionError$Outbound = {
  error: "AlreadyActiveSubscriptionError";
  detail: string;
};

/** @internal */
export const AlreadyActiveSubscriptionError$outboundSchema: z.ZodType<
  AlreadyActiveSubscriptionError$Outbound,
  z.ZodTypeDef,
  AlreadyActiveSubscriptionError
> = z.instanceof(AlreadyActiveSubscriptionError)
  .transform(v => v.data$)
  .pipe(z.object({
    error: z.literal("AlreadyActiveSubscriptionError").default(
      "AlreadyActiveSubscriptionError" as const,
    ),
    detail: z.string(),
  }));

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace AlreadyActiveSubscriptionError$ {
  /** @deprecated use `AlreadyActiveSubscriptionError$inboundSchema` instead. */
  export const inboundSchema = AlreadyActiveSubscriptionError$inboundSchema;
  /** @deprecated use `AlreadyActiveSubscriptionError$outboundSchema` instead. */
  export const outboundSchema = AlreadyActiveSubscriptionError$outboundSchema;
  /** @deprecated use `AlreadyActiveSubscriptionError$Outbound` instead. */
  export type Outbound = AlreadyActiveSubscriptionError$Outbound;
}
