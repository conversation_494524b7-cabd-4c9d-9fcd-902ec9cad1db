/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import {
  ListResourceBenefitGrant,
  ListResourceBenefitGrant$inboundSchema,
  ListResourceBenefitGrant$Outbound,
  ListResourceBenefitGrant$outboundSchema,
} from "../components/listresourcebenefitgrant.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Filter by customer.
 */
export type QueryParamCustomerIDFilter = string | Array<string>;

export type BenefitsGrantsRequest = {
  id: string;
  /**
   * Filter by granted status. If `true`, only granted benefits will be returned. If `false`, only revoked benefits will be returned.
   */
  isGranted?: boolean | null | undefined;
  /**
   * Filter by customer.
   */
  customerId?: string | Array<string> | null | undefined;
  /**
   * Page number, defaults to 1.
   */
  page?: number | undefined;
  /**
   * Size of a page, defaults to 10. Maximum is 100.
   */
  limit?: number | undefined;
};

export type BenefitsGrantsResponse = {
  result: ListResourceBenefitGrant;
};

/** @internal */
export const QueryParamCustomerIDFilter$inboundSchema: z.ZodType<
  QueryParamCustomerIDFilter,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.array(z.string())]);

/** @internal */
export type QueryParamCustomerIDFilter$Outbound = string | Array<string>;

/** @internal */
export const QueryParamCustomerIDFilter$outboundSchema: z.ZodType<
  QueryParamCustomerIDFilter$Outbound,
  z.ZodTypeDef,
  QueryParamCustomerIDFilter
> = z.union([z.string(), z.array(z.string())]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace QueryParamCustomerIDFilter$ {
  /** @deprecated use `QueryParamCustomerIDFilter$inboundSchema` instead. */
  export const inboundSchema = QueryParamCustomerIDFilter$inboundSchema;
  /** @deprecated use `QueryParamCustomerIDFilter$outboundSchema` instead. */
  export const outboundSchema = QueryParamCustomerIDFilter$outboundSchema;
  /** @deprecated use `QueryParamCustomerIDFilter$Outbound` instead. */
  export type Outbound = QueryParamCustomerIDFilter$Outbound;
}

export function queryParamCustomerIDFilterToJSON(
  queryParamCustomerIDFilter: QueryParamCustomerIDFilter,
): string {
  return JSON.stringify(
    QueryParamCustomerIDFilter$outboundSchema.parse(queryParamCustomerIDFilter),
  );
}

export function queryParamCustomerIDFilterFromJSON(
  jsonString: string,
): SafeParseResult<QueryParamCustomerIDFilter, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => QueryParamCustomerIDFilter$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'QueryParamCustomerIDFilter' from JSON`,
  );
}

/** @internal */
export const BenefitsGrantsRequest$inboundSchema: z.ZodType<
  BenefitsGrantsRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  is_granted: z.nullable(z.boolean()).optional(),
  customer_id: z.nullable(z.union([z.string(), z.array(z.string())]))
    .optional(),
  page: z.number().int().default(1),
  limit: z.number().int().default(10),
}).transform((v) => {
  return remap$(v, {
    "is_granted": "isGranted",
    "customer_id": "customerId",
  });
});

/** @internal */
export type BenefitsGrantsRequest$Outbound = {
  id: string;
  is_granted?: boolean | null | undefined;
  customer_id?: string | Array<string> | null | undefined;
  page: number;
  limit: number;
};

/** @internal */
export const BenefitsGrantsRequest$outboundSchema: z.ZodType<
  BenefitsGrantsRequest$Outbound,
  z.ZodTypeDef,
  BenefitsGrantsRequest
> = z.object({
  id: z.string(),
  isGranted: z.nullable(z.boolean()).optional(),
  customerId: z.nullable(z.union([z.string(), z.array(z.string())])).optional(),
  page: z.number().int().default(1),
  limit: z.number().int().default(10),
}).transform((v) => {
  return remap$(v, {
    isGranted: "is_granted",
    customerId: "customer_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitsGrantsRequest$ {
  /** @deprecated use `BenefitsGrantsRequest$inboundSchema` instead. */
  export const inboundSchema = BenefitsGrantsRequest$inboundSchema;
  /** @deprecated use `BenefitsGrantsRequest$outboundSchema` instead. */
  export const outboundSchema = BenefitsGrantsRequest$outboundSchema;
  /** @deprecated use `BenefitsGrantsRequest$Outbound` instead. */
  export type Outbound = BenefitsGrantsRequest$Outbound;
}

export function benefitsGrantsRequestToJSON(
  benefitsGrantsRequest: BenefitsGrantsRequest,
): string {
  return JSON.stringify(
    BenefitsGrantsRequest$outboundSchema.parse(benefitsGrantsRequest),
  );
}

export function benefitsGrantsRequestFromJSON(
  jsonString: string,
): SafeParseResult<BenefitsGrantsRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitsGrantsRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitsGrantsRequest' from JSON`,
  );
}

/** @internal */
export const BenefitsGrantsResponse$inboundSchema: z.ZodType<
  BenefitsGrantsResponse,
  z.ZodTypeDef,
  unknown
> = z.object({
  Result: ListResourceBenefitGrant$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "Result": "result",
  });
});

/** @internal */
export type BenefitsGrantsResponse$Outbound = {
  Result: ListResourceBenefitGrant$Outbound;
};

/** @internal */
export const BenefitsGrantsResponse$outboundSchema: z.ZodType<
  BenefitsGrantsResponse$Outbound,
  z.ZodTypeDef,
  BenefitsGrantsResponse
> = z.object({
  result: ListResourceBenefitGrant$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    result: "Result",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitsGrantsResponse$ {
  /** @deprecated use `BenefitsGrantsResponse$inboundSchema` instead. */
  export const inboundSchema = BenefitsGrantsResponse$inboundSchema;
  /** @deprecated use `BenefitsGrantsResponse$outboundSchema` instead. */
  export const outboundSchema = BenefitsGrantsResponse$outboundSchema;
  /** @deprecated use `BenefitsGrantsResponse$Outbound` instead. */
  export type Outbound = BenefitsGrantsResponse$Outbound;
}

export function benefitsGrantsResponseToJSON(
  benefitsGrantsResponse: BenefitsGrantsResponse,
): string {
  return JSON.stringify(
    BenefitsGrantsResponse$outboundSchema.parse(benefitsGrantsResponse),
  );
}

export function benefitsGrantsResponseFromJSON(
  jsonString: string,
): SafeParseResult<BenefitsGrantsResponse, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitsGrantsResponse$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitsGrantsResponse' from JSON`,
  );
}
