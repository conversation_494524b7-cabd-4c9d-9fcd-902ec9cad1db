/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import {
  CheckoutLinkCreateProduct,
  CheckoutLinkCreateProduct$inboundSchema,
  CheckoutLinkCreateProduct$Outbound,
  CheckoutLinkCreateProduct$outboundSchema,
} from "../components/checkoutlinkcreateproduct.js";
import {
  CheckoutLinkCreateProductPrice,
  CheckoutLinkCreateProductPrice$inboundSchema,
  CheckoutLinkCreateProductPrice$Outbound,
  CheckoutLinkCreateProductPrice$outboundSchema,
} from "../components/checkoutlinkcreateproductprice.js";
import {
  CheckoutLinkCreateProducts,
  CheckoutLinkCreateProducts$inboundSchema,
  CheckoutLinkCreateProducts$Outbound,
  CheckoutLinkCreateProducts$outboundSchema,
} from "../components/checkoutlinkcreateproducts.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type CheckoutLinksCreateCheckoutLinkCreate =
  | CheckoutLinkCreateProductPrice
  | CheckoutLinkCreateProduct
  | CheckoutLinkCreateProducts;

/** @internal */
export const CheckoutLinksCreateCheckoutLinkCreate$inboundSchema: z.ZodType<
  CheckoutLinksCreateCheckoutLinkCreate,
  z.ZodTypeDef,
  unknown
> = z.union([
  CheckoutLinkCreateProductPrice$inboundSchema,
  CheckoutLinkCreateProduct$inboundSchema,
  CheckoutLinkCreateProducts$inboundSchema,
]);

/** @internal */
export type CheckoutLinksCreateCheckoutLinkCreate$Outbound =
  | CheckoutLinkCreateProductPrice$Outbound
  | CheckoutLinkCreateProduct$Outbound
  | CheckoutLinkCreateProducts$Outbound;

/** @internal */
export const CheckoutLinksCreateCheckoutLinkCreate$outboundSchema: z.ZodType<
  CheckoutLinksCreateCheckoutLinkCreate$Outbound,
  z.ZodTypeDef,
  CheckoutLinksCreateCheckoutLinkCreate
> = z.union([
  CheckoutLinkCreateProductPrice$outboundSchema,
  CheckoutLinkCreateProduct$outboundSchema,
  CheckoutLinkCreateProducts$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutLinksCreateCheckoutLinkCreate$ {
  /** @deprecated use `CheckoutLinksCreateCheckoutLinkCreate$inboundSchema` instead. */
  export const inboundSchema =
    CheckoutLinksCreateCheckoutLinkCreate$inboundSchema;
  /** @deprecated use `CheckoutLinksCreateCheckoutLinkCreate$outboundSchema` instead. */
  export const outboundSchema =
    CheckoutLinksCreateCheckoutLinkCreate$outboundSchema;
  /** @deprecated use `CheckoutLinksCreateCheckoutLinkCreate$Outbound` instead. */
  export type Outbound = CheckoutLinksCreateCheckoutLinkCreate$Outbound;
}

export function checkoutLinksCreateCheckoutLinkCreateToJSON(
  checkoutLinksCreateCheckoutLinkCreate: CheckoutLinksCreateCheckoutLinkCreate,
): string {
  return JSON.stringify(
    CheckoutLinksCreateCheckoutLinkCreate$outboundSchema.parse(
      checkoutLinksCreateCheckoutLinkCreate,
    ),
  );
}

export function checkoutLinksCreateCheckoutLinkCreateFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutLinksCreateCheckoutLinkCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      CheckoutLinksCreateCheckoutLinkCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutLinksCreateCheckoutLinkCreate' from JSON`,
  );
}
