/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import {
  CheckoutLinkUpdate,
  CheckoutLinkUpdate$inboundSchema,
  CheckoutLinkUpdate$Outbound,
  CheckoutLinkUpdate$outboundSchema,
} from "../components/checkoutlinkupdate.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type CheckoutLinksUpdateRequest = {
  /**
   * The checkout link ID.
   */
  id: string;
  checkoutLinkUpdate: CheckoutLinkUpdate;
};

/** @internal */
export const CheckoutLinksUpdateRequest$inboundSchema: z.ZodType<
  CheckoutLinksUpdateRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  CheckoutLinkUpdate: CheckoutLinkUpdate$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "CheckoutLinkUpdate": "checkoutLinkUpdate",
  });
});

/** @internal */
export type CheckoutLinksUpdateRequest$Outbound = {
  id: string;
  CheckoutLinkUpdate: CheckoutLinkUpdate$Outbound;
};

/** @internal */
export const CheckoutLinksUpdateRequest$outboundSchema: z.ZodType<
  CheckoutLinksUpdateRequest$Outbound,
  z.ZodTypeDef,
  CheckoutLinksUpdateRequest
> = z.object({
  id: z.string(),
  checkoutLinkUpdate: CheckoutLinkUpdate$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    checkoutLinkUpdate: "CheckoutLinkUpdate",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutLinksUpdateRequest$ {
  /** @deprecated use `CheckoutLinksUpdateRequest$inboundSchema` instead. */
  export const inboundSchema = CheckoutLinksUpdateRequest$inboundSchema;
  /** @deprecated use `CheckoutLinksUpdateRequest$outboundSchema` instead. */
  export const outboundSchema = CheckoutLinksUpdateRequest$outboundSchema;
  /** @deprecated use `CheckoutLinksUpdateRequest$Outbound` instead. */
  export type Outbound = CheckoutLinksUpdateRequest$Outbound;
}

export function checkoutLinksUpdateRequestToJSON(
  checkoutLinksUpdateRequest: CheckoutLinksUpdateRequest,
): string {
  return JSON.stringify(
    CheckoutLinksUpdateRequest$outboundSchema.parse(checkoutLinksUpdateRequest),
  );
}

export function checkoutLinksUpdateRequestFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutLinksUpdateRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutLinksUpdateRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutLinksUpdateRequest' from JSON`,
  );
}
