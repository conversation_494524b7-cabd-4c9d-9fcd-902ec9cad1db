/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const OrganizationSocialPlatforms = {
  X: "x",
  Github: "github",
  Facebook: "facebook",
  Instagram: "instagram",
  Youtube: "youtube",
  Tiktok: "tiktok",
  Linkedin: "linkedin",
  Other: "other",
} as const;
export type OrganizationSocialPlatforms = ClosedEnum<
  typeof OrganizationSocialPlatforms
>;

/** @internal */
export const OrganizationSocialPlatforms$inboundSchema: z.ZodNativeEnum<
  typeof OrganizationSocialPlatforms
> = z.nativeEnum(OrganizationSocialPlatforms);

/** @internal */
export const OrganizationSocialPlatforms$outboundSchema: z.ZodNativeEnum<
  typeof OrganizationSocialPlatforms
> = OrganizationSocialPlatforms$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrganizationSocialPlatforms$ {
  /** @deprecated use `OrganizationSocialPlatforms$inboundSchema` instead. */
  export const inboundSchema = OrganizationSocialPlatforms$inboundSchema;
  /** @deprecated use `OrganizationSocialPlatforms$outboundSchema` instead. */
  export const outboundSchema = OrganizationSocialPlatforms$outboundSchema;
}
