/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Address,
  Address$inboundSchema,
  Address$Outbound,
  Address$outboundSchema,
} from "./address.js";
import {
  TaxIDFormat,
  TaxIDFormat$inboundSchema,
  TaxIDFormat$outboundSchema,
} from "./taxidformat.js";

export type CustomerUpdateMetadata = string | number | number | boolean;

export type CustomerUpdateTaxId = string | TaxIDFormat;

export type CustomerUpdate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  /**
   * The email address of the customer. This must be unique within the organization.
   */
  email?: string | null | undefined;
  /**
   * The name of the customer.
   */
  name?: string | null | undefined;
  billingAddress?: Address | null | undefined;
  taxId?: Array<string | TaxIDFormat | null> | null | undefined;
  /**
   * The ID of the customer in your system. This must be unique within the organization. Once set, it can't be updated.
   */
  externalId?: string | null | undefined;
};

/** @internal */
export const CustomerUpdateMetadata$inboundSchema: z.ZodType<
  CustomerUpdateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomerUpdateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomerUpdateMetadata$outboundSchema: z.ZodType<
  CustomerUpdateMetadata$Outbound,
  z.ZodTypeDef,
  CustomerUpdateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerUpdateMetadata$ {
  /** @deprecated use `CustomerUpdateMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomerUpdateMetadata$inboundSchema;
  /** @deprecated use `CustomerUpdateMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomerUpdateMetadata$outboundSchema;
  /** @deprecated use `CustomerUpdateMetadata$Outbound` instead. */
  export type Outbound = CustomerUpdateMetadata$Outbound;
}

export function customerUpdateMetadataToJSON(
  customerUpdateMetadata: CustomerUpdateMetadata,
): string {
  return JSON.stringify(
    CustomerUpdateMetadata$outboundSchema.parse(customerUpdateMetadata),
  );
}

export function customerUpdateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomerUpdateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerUpdateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerUpdateMetadata' from JSON`,
  );
}

/** @internal */
export const CustomerUpdateTaxId$inboundSchema: z.ZodType<
  CustomerUpdateTaxId,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), TaxIDFormat$inboundSchema]);

/** @internal */
export type CustomerUpdateTaxId$Outbound = string | string;

/** @internal */
export const CustomerUpdateTaxId$outboundSchema: z.ZodType<
  CustomerUpdateTaxId$Outbound,
  z.ZodTypeDef,
  CustomerUpdateTaxId
> = z.union([z.string(), TaxIDFormat$outboundSchema]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerUpdateTaxId$ {
  /** @deprecated use `CustomerUpdateTaxId$inboundSchema` instead. */
  export const inboundSchema = CustomerUpdateTaxId$inboundSchema;
  /** @deprecated use `CustomerUpdateTaxId$outboundSchema` instead. */
  export const outboundSchema = CustomerUpdateTaxId$outboundSchema;
  /** @deprecated use `CustomerUpdateTaxId$Outbound` instead. */
  export type Outbound = CustomerUpdateTaxId$Outbound;
}

export function customerUpdateTaxIdToJSON(
  customerUpdateTaxId: CustomerUpdateTaxId,
): string {
  return JSON.stringify(
    CustomerUpdateTaxId$outboundSchema.parse(customerUpdateTaxId),
  );
}

export function customerUpdateTaxIdFromJSON(
  jsonString: string,
): SafeParseResult<CustomerUpdateTaxId, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerUpdateTaxId$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerUpdateTaxId' from JSON`,
  );
}

/** @internal */
export const CustomerUpdate$inboundSchema: z.ZodType<
  CustomerUpdate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  email: z.nullable(z.string()).optional(),
  name: z.nullable(z.string()).optional(),
  billing_address: z.nullable(Address$inboundSchema).optional(),
  tax_id: z.nullable(
    z.array(z.nullable(z.union([z.string(), TaxIDFormat$inboundSchema]))),
  ).optional(),
  external_id: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    "billing_address": "billingAddress",
    "tax_id": "taxId",
    "external_id": "externalId",
  });
});

/** @internal */
export type CustomerUpdate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  email?: string | null | undefined;
  name?: string | null | undefined;
  billing_address?: Address$Outbound | null | undefined;
  tax_id?: Array<string | string | null> | null | undefined;
  external_id?: string | null | undefined;
};

/** @internal */
export const CustomerUpdate$outboundSchema: z.ZodType<
  CustomerUpdate$Outbound,
  z.ZodTypeDef,
  CustomerUpdate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  email: z.nullable(z.string()).optional(),
  name: z.nullable(z.string()).optional(),
  billingAddress: z.nullable(Address$outboundSchema).optional(),
  taxId: z.nullable(
    z.array(z.nullable(z.union([z.string(), TaxIDFormat$outboundSchema]))),
  ).optional(),
  externalId: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    billingAddress: "billing_address",
    taxId: "tax_id",
    externalId: "external_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerUpdate$ {
  /** @deprecated use `CustomerUpdate$inboundSchema` instead. */
  export const inboundSchema = CustomerUpdate$inboundSchema;
  /** @deprecated use `CustomerUpdate$outboundSchema` instead. */
  export const outboundSchema = CustomerUpdate$outboundSchema;
  /** @deprecated use `CustomerUpdate$Outbound` instead. */
  export type Outbound = CustomerUpdate$Outbound;
}

export function customerUpdateToJSON(customerUpdate: CustomerUpdate): string {
  return JSON.stringify(CustomerUpdate$outboundSchema.parse(customerUpdate));
}

export function customerUpdateFromJSON(
  jsonString: string,
): SafeParseResult<CustomerUpdate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerUpdate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerUpdate' from JSON`,
  );
}
