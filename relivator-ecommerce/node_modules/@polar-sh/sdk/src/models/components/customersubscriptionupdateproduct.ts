/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type CustomerSubscriptionUpdateProduct = {
  /**
   * Update subscription to another product.
   */
  productId: string;
};

/** @internal */
export const CustomerSubscriptionUpdateProduct$inboundSchema: z.ZodType<
  CustomerSubscriptionUpdateProduct,
  z.ZodTypeDef,
  unknown
> = z.object({
  product_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "product_id": "productId",
  });
});

/** @internal */
export type CustomerSubscriptionUpdateProduct$Outbound = {
  product_id: string;
};

/** @internal */
export const CustomerSubscriptionUpdateProduct$outboundSchema: z.ZodType<
  CustomerSubscriptionUpdateProduct$Outbound,
  z.ZodTypeDef,
  CustomerSubscriptionUpdateProduct
> = z.object({
  productId: z.string(),
}).transform((v) => {
  return remap$(v, {
    productId: "product_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerSubscriptionUpdateProduct$ {
  /** @deprecated use `CustomerSubscriptionUpdateProduct$inboundSchema` instead. */
  export const inboundSchema = CustomerSubscriptionUpdateProduct$inboundSchema;
  /** @deprecated use `CustomerSubscriptionUpdateProduct$outboundSchema` instead. */
  export const outboundSchema =
    CustomerSubscriptionUpdateProduct$outboundSchema;
  /** @deprecated use `CustomerSubscriptionUpdateProduct$Outbound` instead. */
  export type Outbound = CustomerSubscriptionUpdateProduct$Outbound;
}

export function customerSubscriptionUpdateProductToJSON(
  customerSubscriptionUpdateProduct: CustomerSubscriptionUpdateProduct,
): string {
  return JSON.stringify(
    CustomerSubscriptionUpdateProduct$outboundSchema.parse(
      customerSubscriptionUpdateProduct,
    ),
  );
}

export function customerSubscriptionUpdateProductFromJSON(
  jsonString: string,
): SafeParseResult<CustomerSubscriptionUpdateProduct, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerSubscriptionUpdateProduct$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerSubscriptionUpdateProduct' from JSON`,
  );
}
