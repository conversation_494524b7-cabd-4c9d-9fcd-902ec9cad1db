/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type OrganizationFeatureSettings = {
  /**
   * If this organization has issue funding enabled
   */
  issueFundingEnabled?: boolean | undefined;
  /**
   * If this organization has usage-based billing enabled
   */
  usageBasedBillingEnabled?: boolean | undefined;
};

/** @internal */
export const OrganizationFeatureSettings$inboundSchema: z.ZodType<
  OrganizationFeatureSettings,
  z.ZodTypeDef,
  unknown
> = z.object({
  issue_funding_enabled: z.boolean().default(false),
  usage_based_billing_enabled: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    "issue_funding_enabled": "issueFundingEnabled",
    "usage_based_billing_enabled": "usageBasedBillingEnabled",
  });
});

/** @internal */
export type OrganizationFeatureSettings$Outbound = {
  issue_funding_enabled: boolean;
  usage_based_billing_enabled: boolean;
};

/** @internal */
export const OrganizationFeatureSettings$outboundSchema: z.ZodType<
  OrganizationFeatureSettings$Outbound,
  z.ZodTypeDef,
  OrganizationFeatureSettings
> = z.object({
  issueFundingEnabled: z.boolean().default(false),
  usageBasedBillingEnabled: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    issueFundingEnabled: "issue_funding_enabled",
    usageBasedBillingEnabled: "usage_based_billing_enabled",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrganizationFeatureSettings$ {
  /** @deprecated use `OrganizationFeatureSettings$inboundSchema` instead. */
  export const inboundSchema = OrganizationFeatureSettings$inboundSchema;
  /** @deprecated use `OrganizationFeatureSettings$outboundSchema` instead. */
  export const outboundSchema = OrganizationFeatureSettings$outboundSchema;
  /** @deprecated use `OrganizationFeatureSettings$Outbound` instead. */
  export type Outbound = OrganizationFeatureSettings$Outbound;
}

export function organizationFeatureSettingsToJSON(
  organizationFeatureSettings: OrganizationFeatureSettings,
): string {
  return JSON.stringify(
    OrganizationFeatureSettings$outboundSchema.parse(
      organizationFeatureSettings,
    ),
  );
}

export function organizationFeatureSettingsFromJSON(
  jsonString: string,
): SafeParseResult<OrganizationFeatureSettings, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrganizationFeatureSettings$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrganizationFeatureSettings' from JSON`,
  );
}
