/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  OAuth2Client,
  OAuth2Client$inboundSchema,
  OAuth2Client$Outbound,
  OAuth2Client$outboundSchema,
} from "./oauth2client.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";

export type ListResourceOAuth2Client = {
  items: Array<OAuth2Client>;
  pagination: Pagination;
};

/** @internal */
export const ListResourceOAuth2Client$inboundSchema: z.ZodType<
  ListResourceOAuth2Client,
  z.ZodTypeDef,
  unknown
> = z.object({
  items: z.array(OAuth2Client$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ListResourceOAuth2Client$Outbound = {
  items: Array<OAuth2Client$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResourceOAuth2Client$outboundSchema: z.ZodType<
  ListResourceOAuth2Client$Outbound,
  z.ZodTypeDef,
  ListResourceOAuth2Client
> = z.object({
  items: z.array(OAuth2Client$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResourceOAuth2Client$ {
  /** @deprecated use `ListResourceOAuth2Client$inboundSchema` instead. */
  export const inboundSchema = ListResourceOAuth2Client$inboundSchema;
  /** @deprecated use `ListResourceOAuth2Client$outboundSchema` instead. */
  export const outboundSchema = ListResourceOAuth2Client$outboundSchema;
  /** @deprecated use `ListResourceOAuth2Client$Outbound` instead. */
  export type Outbound = ListResourceOAuth2Client$Outbound;
}

export function listResourceOAuth2ClientToJSON(
  listResourceOAuth2Client: ListResourceOAuth2Client,
): string {
  return JSON.stringify(
    ListResourceOAuth2Client$outboundSchema.parse(listResourceOAuth2Client),
  );
}

export function listResourceOAuth2ClientFromJSON(
  jsonString: string,
): SafeParseResult<ListResourceOAuth2Client, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListResourceOAuth2Client$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListResourceOAuth2Client' from JSON`,
  );
}
