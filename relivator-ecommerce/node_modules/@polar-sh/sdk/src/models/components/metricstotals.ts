/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type MetricsTotalsOrders = number | number;

export type MetricsTotalsRevenue = number | number;

export type MetricsTotalsCumulativeRevenue = number | number;

export type MetricsTotalsAverageOrderValue = number | number;

export type MetricsTotalsOneTimeProducts = number | number;

export type MetricsTotalsOneTimeProductsRevenue = number | number;

export type MetricsTotalsNewSubscriptions = number | number;

export type MetricsTotalsNewSubscriptionsRevenue = number | number;

export type MetricsTotalsRenewedSubscriptions = number | number;

export type MetricsTotalsRenewedSubscriptionsRevenue = number | number;

export type MetricsTotalsActiveSubscriptions = number | number;

export type MetricsTotalsMonthlyRecurringRevenue = number | number;

export type MetricsTotalsCheckouts = number | number;

export type MetricsTotalsSucceededCheckouts = number | number;

export type MetricsTotalsCheckoutsConversion = number | number;

export type MetricsTotals = {
  orders: number | number;
  revenue: number | number;
  cumulativeRevenue: number | number;
  averageOrderValue: number | number;
  oneTimeProducts: number | number;
  oneTimeProductsRevenue: number | number;
  newSubscriptions: number | number;
  newSubscriptionsRevenue: number | number;
  renewedSubscriptions: number | number;
  renewedSubscriptionsRevenue: number | number;
  activeSubscriptions: number | number;
  monthlyRecurringRevenue: number | number;
  checkouts: number | number;
  succeededCheckouts: number | number;
  checkoutsConversion: number | number;
};

/** @internal */
export const MetricsTotalsOrders$inboundSchema: z.ZodType<
  MetricsTotalsOrders,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsOrders$Outbound = number | number;

/** @internal */
export const MetricsTotalsOrders$outboundSchema: z.ZodType<
  MetricsTotalsOrders$Outbound,
  z.ZodTypeDef,
  MetricsTotalsOrders
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsOrders$ {
  /** @deprecated use `MetricsTotalsOrders$inboundSchema` instead. */
  export const inboundSchema = MetricsTotalsOrders$inboundSchema;
  /** @deprecated use `MetricsTotalsOrders$outboundSchema` instead. */
  export const outboundSchema = MetricsTotalsOrders$outboundSchema;
  /** @deprecated use `MetricsTotalsOrders$Outbound` instead. */
  export type Outbound = MetricsTotalsOrders$Outbound;
}

export function metricsTotalsOrdersToJSON(
  metricsTotalsOrders: MetricsTotalsOrders,
): string {
  return JSON.stringify(
    MetricsTotalsOrders$outboundSchema.parse(metricsTotalsOrders),
  );
}

export function metricsTotalsOrdersFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsOrders, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotalsOrders$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsOrders' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsRevenue$inboundSchema: z.ZodType<
  MetricsTotalsRevenue,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsRevenue$Outbound = number | number;

/** @internal */
export const MetricsTotalsRevenue$outboundSchema: z.ZodType<
  MetricsTotalsRevenue$Outbound,
  z.ZodTypeDef,
  MetricsTotalsRevenue
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsRevenue$ {
  /** @deprecated use `MetricsTotalsRevenue$inboundSchema` instead. */
  export const inboundSchema = MetricsTotalsRevenue$inboundSchema;
  /** @deprecated use `MetricsTotalsRevenue$outboundSchema` instead. */
  export const outboundSchema = MetricsTotalsRevenue$outboundSchema;
  /** @deprecated use `MetricsTotalsRevenue$Outbound` instead. */
  export type Outbound = MetricsTotalsRevenue$Outbound;
}

export function metricsTotalsRevenueToJSON(
  metricsTotalsRevenue: MetricsTotalsRevenue,
): string {
  return JSON.stringify(
    MetricsTotalsRevenue$outboundSchema.parse(metricsTotalsRevenue),
  );
}

export function metricsTotalsRevenueFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsRevenue, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotalsRevenue$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsRevenue' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsCumulativeRevenue$inboundSchema: z.ZodType<
  MetricsTotalsCumulativeRevenue,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsCumulativeRevenue$Outbound = number | number;

/** @internal */
export const MetricsTotalsCumulativeRevenue$outboundSchema: z.ZodType<
  MetricsTotalsCumulativeRevenue$Outbound,
  z.ZodTypeDef,
  MetricsTotalsCumulativeRevenue
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsCumulativeRevenue$ {
  /** @deprecated use `MetricsTotalsCumulativeRevenue$inboundSchema` instead. */
  export const inboundSchema = MetricsTotalsCumulativeRevenue$inboundSchema;
  /** @deprecated use `MetricsTotalsCumulativeRevenue$outboundSchema` instead. */
  export const outboundSchema = MetricsTotalsCumulativeRevenue$outboundSchema;
  /** @deprecated use `MetricsTotalsCumulativeRevenue$Outbound` instead. */
  export type Outbound = MetricsTotalsCumulativeRevenue$Outbound;
}

export function metricsTotalsCumulativeRevenueToJSON(
  metricsTotalsCumulativeRevenue: MetricsTotalsCumulativeRevenue,
): string {
  return JSON.stringify(
    MetricsTotalsCumulativeRevenue$outboundSchema.parse(
      metricsTotalsCumulativeRevenue,
    ),
  );
}

export function metricsTotalsCumulativeRevenueFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsCumulativeRevenue, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotalsCumulativeRevenue$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsCumulativeRevenue' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsAverageOrderValue$inboundSchema: z.ZodType<
  MetricsTotalsAverageOrderValue,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsAverageOrderValue$Outbound = number | number;

/** @internal */
export const MetricsTotalsAverageOrderValue$outboundSchema: z.ZodType<
  MetricsTotalsAverageOrderValue$Outbound,
  z.ZodTypeDef,
  MetricsTotalsAverageOrderValue
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsAverageOrderValue$ {
  /** @deprecated use `MetricsTotalsAverageOrderValue$inboundSchema` instead. */
  export const inboundSchema = MetricsTotalsAverageOrderValue$inboundSchema;
  /** @deprecated use `MetricsTotalsAverageOrderValue$outboundSchema` instead. */
  export const outboundSchema = MetricsTotalsAverageOrderValue$outboundSchema;
  /** @deprecated use `MetricsTotalsAverageOrderValue$Outbound` instead. */
  export type Outbound = MetricsTotalsAverageOrderValue$Outbound;
}

export function metricsTotalsAverageOrderValueToJSON(
  metricsTotalsAverageOrderValue: MetricsTotalsAverageOrderValue,
): string {
  return JSON.stringify(
    MetricsTotalsAverageOrderValue$outboundSchema.parse(
      metricsTotalsAverageOrderValue,
    ),
  );
}

export function metricsTotalsAverageOrderValueFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsAverageOrderValue, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotalsAverageOrderValue$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsAverageOrderValue' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsOneTimeProducts$inboundSchema: z.ZodType<
  MetricsTotalsOneTimeProducts,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsOneTimeProducts$Outbound = number | number;

/** @internal */
export const MetricsTotalsOneTimeProducts$outboundSchema: z.ZodType<
  MetricsTotalsOneTimeProducts$Outbound,
  z.ZodTypeDef,
  MetricsTotalsOneTimeProducts
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsOneTimeProducts$ {
  /** @deprecated use `MetricsTotalsOneTimeProducts$inboundSchema` instead. */
  export const inboundSchema = MetricsTotalsOneTimeProducts$inboundSchema;
  /** @deprecated use `MetricsTotalsOneTimeProducts$outboundSchema` instead. */
  export const outboundSchema = MetricsTotalsOneTimeProducts$outboundSchema;
  /** @deprecated use `MetricsTotalsOneTimeProducts$Outbound` instead. */
  export type Outbound = MetricsTotalsOneTimeProducts$Outbound;
}

export function metricsTotalsOneTimeProductsToJSON(
  metricsTotalsOneTimeProducts: MetricsTotalsOneTimeProducts,
): string {
  return JSON.stringify(
    MetricsTotalsOneTimeProducts$outboundSchema.parse(
      metricsTotalsOneTimeProducts,
    ),
  );
}

export function metricsTotalsOneTimeProductsFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsOneTimeProducts, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotalsOneTimeProducts$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsOneTimeProducts' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsOneTimeProductsRevenue$inboundSchema: z.ZodType<
  MetricsTotalsOneTimeProductsRevenue,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsOneTimeProductsRevenue$Outbound = number | number;

/** @internal */
export const MetricsTotalsOneTimeProductsRevenue$outboundSchema: z.ZodType<
  MetricsTotalsOneTimeProductsRevenue$Outbound,
  z.ZodTypeDef,
  MetricsTotalsOneTimeProductsRevenue
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsOneTimeProductsRevenue$ {
  /** @deprecated use `MetricsTotalsOneTimeProductsRevenue$inboundSchema` instead. */
  export const inboundSchema =
    MetricsTotalsOneTimeProductsRevenue$inboundSchema;
  /** @deprecated use `MetricsTotalsOneTimeProductsRevenue$outboundSchema` instead. */
  export const outboundSchema =
    MetricsTotalsOneTimeProductsRevenue$outboundSchema;
  /** @deprecated use `MetricsTotalsOneTimeProductsRevenue$Outbound` instead. */
  export type Outbound = MetricsTotalsOneTimeProductsRevenue$Outbound;
}

export function metricsTotalsOneTimeProductsRevenueToJSON(
  metricsTotalsOneTimeProductsRevenue: MetricsTotalsOneTimeProductsRevenue,
): string {
  return JSON.stringify(
    MetricsTotalsOneTimeProductsRevenue$outboundSchema.parse(
      metricsTotalsOneTimeProductsRevenue,
    ),
  );
}

export function metricsTotalsOneTimeProductsRevenueFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsOneTimeProductsRevenue, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      MetricsTotalsOneTimeProductsRevenue$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsOneTimeProductsRevenue' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsNewSubscriptions$inboundSchema: z.ZodType<
  MetricsTotalsNewSubscriptions,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsNewSubscriptions$Outbound = number | number;

/** @internal */
export const MetricsTotalsNewSubscriptions$outboundSchema: z.ZodType<
  MetricsTotalsNewSubscriptions$Outbound,
  z.ZodTypeDef,
  MetricsTotalsNewSubscriptions
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsNewSubscriptions$ {
  /** @deprecated use `MetricsTotalsNewSubscriptions$inboundSchema` instead. */
  export const inboundSchema = MetricsTotalsNewSubscriptions$inboundSchema;
  /** @deprecated use `MetricsTotalsNewSubscriptions$outboundSchema` instead. */
  export const outboundSchema = MetricsTotalsNewSubscriptions$outboundSchema;
  /** @deprecated use `MetricsTotalsNewSubscriptions$Outbound` instead. */
  export type Outbound = MetricsTotalsNewSubscriptions$Outbound;
}

export function metricsTotalsNewSubscriptionsToJSON(
  metricsTotalsNewSubscriptions: MetricsTotalsNewSubscriptions,
): string {
  return JSON.stringify(
    MetricsTotalsNewSubscriptions$outboundSchema.parse(
      metricsTotalsNewSubscriptions,
    ),
  );
}

export function metricsTotalsNewSubscriptionsFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsNewSubscriptions, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotalsNewSubscriptions$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsNewSubscriptions' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsNewSubscriptionsRevenue$inboundSchema: z.ZodType<
  MetricsTotalsNewSubscriptionsRevenue,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsNewSubscriptionsRevenue$Outbound = number | number;

/** @internal */
export const MetricsTotalsNewSubscriptionsRevenue$outboundSchema: z.ZodType<
  MetricsTotalsNewSubscriptionsRevenue$Outbound,
  z.ZodTypeDef,
  MetricsTotalsNewSubscriptionsRevenue
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsNewSubscriptionsRevenue$ {
  /** @deprecated use `MetricsTotalsNewSubscriptionsRevenue$inboundSchema` instead. */
  export const inboundSchema =
    MetricsTotalsNewSubscriptionsRevenue$inboundSchema;
  /** @deprecated use `MetricsTotalsNewSubscriptionsRevenue$outboundSchema` instead. */
  export const outboundSchema =
    MetricsTotalsNewSubscriptionsRevenue$outboundSchema;
  /** @deprecated use `MetricsTotalsNewSubscriptionsRevenue$Outbound` instead. */
  export type Outbound = MetricsTotalsNewSubscriptionsRevenue$Outbound;
}

export function metricsTotalsNewSubscriptionsRevenueToJSON(
  metricsTotalsNewSubscriptionsRevenue: MetricsTotalsNewSubscriptionsRevenue,
): string {
  return JSON.stringify(
    MetricsTotalsNewSubscriptionsRevenue$outboundSchema.parse(
      metricsTotalsNewSubscriptionsRevenue,
    ),
  );
}

export function metricsTotalsNewSubscriptionsRevenueFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsNewSubscriptionsRevenue, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      MetricsTotalsNewSubscriptionsRevenue$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsNewSubscriptionsRevenue' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsRenewedSubscriptions$inboundSchema: z.ZodType<
  MetricsTotalsRenewedSubscriptions,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsRenewedSubscriptions$Outbound = number | number;

/** @internal */
export const MetricsTotalsRenewedSubscriptions$outboundSchema: z.ZodType<
  MetricsTotalsRenewedSubscriptions$Outbound,
  z.ZodTypeDef,
  MetricsTotalsRenewedSubscriptions
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsRenewedSubscriptions$ {
  /** @deprecated use `MetricsTotalsRenewedSubscriptions$inboundSchema` instead. */
  export const inboundSchema = MetricsTotalsRenewedSubscriptions$inboundSchema;
  /** @deprecated use `MetricsTotalsRenewedSubscriptions$outboundSchema` instead. */
  export const outboundSchema =
    MetricsTotalsRenewedSubscriptions$outboundSchema;
  /** @deprecated use `MetricsTotalsRenewedSubscriptions$Outbound` instead. */
  export type Outbound = MetricsTotalsRenewedSubscriptions$Outbound;
}

export function metricsTotalsRenewedSubscriptionsToJSON(
  metricsTotalsRenewedSubscriptions: MetricsTotalsRenewedSubscriptions,
): string {
  return JSON.stringify(
    MetricsTotalsRenewedSubscriptions$outboundSchema.parse(
      metricsTotalsRenewedSubscriptions,
    ),
  );
}

export function metricsTotalsRenewedSubscriptionsFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsRenewedSubscriptions, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotalsRenewedSubscriptions$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsRenewedSubscriptions' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsRenewedSubscriptionsRevenue$inboundSchema: z.ZodType<
  MetricsTotalsRenewedSubscriptionsRevenue,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsRenewedSubscriptionsRevenue$Outbound = number | number;

/** @internal */
export const MetricsTotalsRenewedSubscriptionsRevenue$outboundSchema: z.ZodType<
  MetricsTotalsRenewedSubscriptionsRevenue$Outbound,
  z.ZodTypeDef,
  MetricsTotalsRenewedSubscriptionsRevenue
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsRenewedSubscriptionsRevenue$ {
  /** @deprecated use `MetricsTotalsRenewedSubscriptionsRevenue$inboundSchema` instead. */
  export const inboundSchema =
    MetricsTotalsRenewedSubscriptionsRevenue$inboundSchema;
  /** @deprecated use `MetricsTotalsRenewedSubscriptionsRevenue$outboundSchema` instead. */
  export const outboundSchema =
    MetricsTotalsRenewedSubscriptionsRevenue$outboundSchema;
  /** @deprecated use `MetricsTotalsRenewedSubscriptionsRevenue$Outbound` instead. */
  export type Outbound = MetricsTotalsRenewedSubscriptionsRevenue$Outbound;
}

export function metricsTotalsRenewedSubscriptionsRevenueToJSON(
  metricsTotalsRenewedSubscriptionsRevenue:
    MetricsTotalsRenewedSubscriptionsRevenue,
): string {
  return JSON.stringify(
    MetricsTotalsRenewedSubscriptionsRevenue$outboundSchema.parse(
      metricsTotalsRenewedSubscriptionsRevenue,
    ),
  );
}

export function metricsTotalsRenewedSubscriptionsRevenueFromJSON(
  jsonString: string,
): SafeParseResult<
  MetricsTotalsRenewedSubscriptionsRevenue,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      MetricsTotalsRenewedSubscriptionsRevenue$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'MetricsTotalsRenewedSubscriptionsRevenue' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsActiveSubscriptions$inboundSchema: z.ZodType<
  MetricsTotalsActiveSubscriptions,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsActiveSubscriptions$Outbound = number | number;

/** @internal */
export const MetricsTotalsActiveSubscriptions$outboundSchema: z.ZodType<
  MetricsTotalsActiveSubscriptions$Outbound,
  z.ZodTypeDef,
  MetricsTotalsActiveSubscriptions
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsActiveSubscriptions$ {
  /** @deprecated use `MetricsTotalsActiveSubscriptions$inboundSchema` instead. */
  export const inboundSchema = MetricsTotalsActiveSubscriptions$inboundSchema;
  /** @deprecated use `MetricsTotalsActiveSubscriptions$outboundSchema` instead. */
  export const outboundSchema = MetricsTotalsActiveSubscriptions$outboundSchema;
  /** @deprecated use `MetricsTotalsActiveSubscriptions$Outbound` instead. */
  export type Outbound = MetricsTotalsActiveSubscriptions$Outbound;
}

export function metricsTotalsActiveSubscriptionsToJSON(
  metricsTotalsActiveSubscriptions: MetricsTotalsActiveSubscriptions,
): string {
  return JSON.stringify(
    MetricsTotalsActiveSubscriptions$outboundSchema.parse(
      metricsTotalsActiveSubscriptions,
    ),
  );
}

export function metricsTotalsActiveSubscriptionsFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsActiveSubscriptions, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotalsActiveSubscriptions$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsActiveSubscriptions' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsMonthlyRecurringRevenue$inboundSchema: z.ZodType<
  MetricsTotalsMonthlyRecurringRevenue,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsMonthlyRecurringRevenue$Outbound = number | number;

/** @internal */
export const MetricsTotalsMonthlyRecurringRevenue$outboundSchema: z.ZodType<
  MetricsTotalsMonthlyRecurringRevenue$Outbound,
  z.ZodTypeDef,
  MetricsTotalsMonthlyRecurringRevenue
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsMonthlyRecurringRevenue$ {
  /** @deprecated use `MetricsTotalsMonthlyRecurringRevenue$inboundSchema` instead. */
  export const inboundSchema =
    MetricsTotalsMonthlyRecurringRevenue$inboundSchema;
  /** @deprecated use `MetricsTotalsMonthlyRecurringRevenue$outboundSchema` instead. */
  export const outboundSchema =
    MetricsTotalsMonthlyRecurringRevenue$outboundSchema;
  /** @deprecated use `MetricsTotalsMonthlyRecurringRevenue$Outbound` instead. */
  export type Outbound = MetricsTotalsMonthlyRecurringRevenue$Outbound;
}

export function metricsTotalsMonthlyRecurringRevenueToJSON(
  metricsTotalsMonthlyRecurringRevenue: MetricsTotalsMonthlyRecurringRevenue,
): string {
  return JSON.stringify(
    MetricsTotalsMonthlyRecurringRevenue$outboundSchema.parse(
      metricsTotalsMonthlyRecurringRevenue,
    ),
  );
}

export function metricsTotalsMonthlyRecurringRevenueFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsMonthlyRecurringRevenue, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      MetricsTotalsMonthlyRecurringRevenue$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsMonthlyRecurringRevenue' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsCheckouts$inboundSchema: z.ZodType<
  MetricsTotalsCheckouts,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsCheckouts$Outbound = number | number;

/** @internal */
export const MetricsTotalsCheckouts$outboundSchema: z.ZodType<
  MetricsTotalsCheckouts$Outbound,
  z.ZodTypeDef,
  MetricsTotalsCheckouts
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsCheckouts$ {
  /** @deprecated use `MetricsTotalsCheckouts$inboundSchema` instead. */
  export const inboundSchema = MetricsTotalsCheckouts$inboundSchema;
  /** @deprecated use `MetricsTotalsCheckouts$outboundSchema` instead. */
  export const outboundSchema = MetricsTotalsCheckouts$outboundSchema;
  /** @deprecated use `MetricsTotalsCheckouts$Outbound` instead. */
  export type Outbound = MetricsTotalsCheckouts$Outbound;
}

export function metricsTotalsCheckoutsToJSON(
  metricsTotalsCheckouts: MetricsTotalsCheckouts,
): string {
  return JSON.stringify(
    MetricsTotalsCheckouts$outboundSchema.parse(metricsTotalsCheckouts),
  );
}

export function metricsTotalsCheckoutsFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsCheckouts, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotalsCheckouts$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsCheckouts' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsSucceededCheckouts$inboundSchema: z.ZodType<
  MetricsTotalsSucceededCheckouts,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsSucceededCheckouts$Outbound = number | number;

/** @internal */
export const MetricsTotalsSucceededCheckouts$outboundSchema: z.ZodType<
  MetricsTotalsSucceededCheckouts$Outbound,
  z.ZodTypeDef,
  MetricsTotalsSucceededCheckouts
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsSucceededCheckouts$ {
  /** @deprecated use `MetricsTotalsSucceededCheckouts$inboundSchema` instead. */
  export const inboundSchema = MetricsTotalsSucceededCheckouts$inboundSchema;
  /** @deprecated use `MetricsTotalsSucceededCheckouts$outboundSchema` instead. */
  export const outboundSchema = MetricsTotalsSucceededCheckouts$outboundSchema;
  /** @deprecated use `MetricsTotalsSucceededCheckouts$Outbound` instead. */
  export type Outbound = MetricsTotalsSucceededCheckouts$Outbound;
}

export function metricsTotalsSucceededCheckoutsToJSON(
  metricsTotalsSucceededCheckouts: MetricsTotalsSucceededCheckouts,
): string {
  return JSON.stringify(
    MetricsTotalsSucceededCheckouts$outboundSchema.parse(
      metricsTotalsSucceededCheckouts,
    ),
  );
}

export function metricsTotalsSucceededCheckoutsFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsSucceededCheckouts, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotalsSucceededCheckouts$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsSucceededCheckouts' from JSON`,
  );
}

/** @internal */
export const MetricsTotalsCheckoutsConversion$inboundSchema: z.ZodType<
  MetricsTotalsCheckoutsConversion,
  z.ZodTypeDef,
  unknown
> = z.union([z.number().int(), z.number()]);

/** @internal */
export type MetricsTotalsCheckoutsConversion$Outbound = number | number;

/** @internal */
export const MetricsTotalsCheckoutsConversion$outboundSchema: z.ZodType<
  MetricsTotalsCheckoutsConversion$Outbound,
  z.ZodTypeDef,
  MetricsTotalsCheckoutsConversion
> = z.union([z.number().int(), z.number()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotalsCheckoutsConversion$ {
  /** @deprecated use `MetricsTotalsCheckoutsConversion$inboundSchema` instead. */
  export const inboundSchema = MetricsTotalsCheckoutsConversion$inboundSchema;
  /** @deprecated use `MetricsTotalsCheckoutsConversion$outboundSchema` instead. */
  export const outboundSchema = MetricsTotalsCheckoutsConversion$outboundSchema;
  /** @deprecated use `MetricsTotalsCheckoutsConversion$Outbound` instead. */
  export type Outbound = MetricsTotalsCheckoutsConversion$Outbound;
}

export function metricsTotalsCheckoutsConversionToJSON(
  metricsTotalsCheckoutsConversion: MetricsTotalsCheckoutsConversion,
): string {
  return JSON.stringify(
    MetricsTotalsCheckoutsConversion$outboundSchema.parse(
      metricsTotalsCheckoutsConversion,
    ),
  );
}

export function metricsTotalsCheckoutsConversionFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotalsCheckoutsConversion, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotalsCheckoutsConversion$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotalsCheckoutsConversion' from JSON`,
  );
}

/** @internal */
export const MetricsTotals$inboundSchema: z.ZodType<
  MetricsTotals,
  z.ZodTypeDef,
  unknown
> = z.object({
  orders: z.union([z.number().int(), z.number()]),
  revenue: z.union([z.number().int(), z.number()]),
  cumulative_revenue: z.union([z.number().int(), z.number()]),
  average_order_value: z.union([z.number().int(), z.number()]),
  one_time_products: z.union([z.number().int(), z.number()]),
  one_time_products_revenue: z.union([z.number().int(), z.number()]),
  new_subscriptions: z.union([z.number().int(), z.number()]),
  new_subscriptions_revenue: z.union([z.number().int(), z.number()]),
  renewed_subscriptions: z.union([z.number().int(), z.number()]),
  renewed_subscriptions_revenue: z.union([z.number().int(), z.number()]),
  active_subscriptions: z.union([z.number().int(), z.number()]),
  monthly_recurring_revenue: z.union([z.number().int(), z.number()]),
  checkouts: z.union([z.number().int(), z.number()]),
  succeeded_checkouts: z.union([z.number().int(), z.number()]),
  checkouts_conversion: z.union([z.number().int(), z.number()]),
}).transform((v) => {
  return remap$(v, {
    "cumulative_revenue": "cumulativeRevenue",
    "average_order_value": "averageOrderValue",
    "one_time_products": "oneTimeProducts",
    "one_time_products_revenue": "oneTimeProductsRevenue",
    "new_subscriptions": "newSubscriptions",
    "new_subscriptions_revenue": "newSubscriptionsRevenue",
    "renewed_subscriptions": "renewedSubscriptions",
    "renewed_subscriptions_revenue": "renewedSubscriptionsRevenue",
    "active_subscriptions": "activeSubscriptions",
    "monthly_recurring_revenue": "monthlyRecurringRevenue",
    "succeeded_checkouts": "succeededCheckouts",
    "checkouts_conversion": "checkoutsConversion",
  });
});

/** @internal */
export type MetricsTotals$Outbound = {
  orders: number | number;
  revenue: number | number;
  cumulative_revenue: number | number;
  average_order_value: number | number;
  one_time_products: number | number;
  one_time_products_revenue: number | number;
  new_subscriptions: number | number;
  new_subscriptions_revenue: number | number;
  renewed_subscriptions: number | number;
  renewed_subscriptions_revenue: number | number;
  active_subscriptions: number | number;
  monthly_recurring_revenue: number | number;
  checkouts: number | number;
  succeeded_checkouts: number | number;
  checkouts_conversion: number | number;
};

/** @internal */
export const MetricsTotals$outboundSchema: z.ZodType<
  MetricsTotals$Outbound,
  z.ZodTypeDef,
  MetricsTotals
> = z.object({
  orders: z.union([z.number().int(), z.number()]),
  revenue: z.union([z.number().int(), z.number()]),
  cumulativeRevenue: z.union([z.number().int(), z.number()]),
  averageOrderValue: z.union([z.number().int(), z.number()]),
  oneTimeProducts: z.union([z.number().int(), z.number()]),
  oneTimeProductsRevenue: z.union([z.number().int(), z.number()]),
  newSubscriptions: z.union([z.number().int(), z.number()]),
  newSubscriptionsRevenue: z.union([z.number().int(), z.number()]),
  renewedSubscriptions: z.union([z.number().int(), z.number()]),
  renewedSubscriptionsRevenue: z.union([z.number().int(), z.number()]),
  activeSubscriptions: z.union([z.number().int(), z.number()]),
  monthlyRecurringRevenue: z.union([z.number().int(), z.number()]),
  checkouts: z.union([z.number().int(), z.number()]),
  succeededCheckouts: z.union([z.number().int(), z.number()]),
  checkoutsConversion: z.union([z.number().int(), z.number()]),
}).transform((v) => {
  return remap$(v, {
    cumulativeRevenue: "cumulative_revenue",
    averageOrderValue: "average_order_value",
    oneTimeProducts: "one_time_products",
    oneTimeProductsRevenue: "one_time_products_revenue",
    newSubscriptions: "new_subscriptions",
    newSubscriptionsRevenue: "new_subscriptions_revenue",
    renewedSubscriptions: "renewed_subscriptions",
    renewedSubscriptionsRevenue: "renewed_subscriptions_revenue",
    activeSubscriptions: "active_subscriptions",
    monthlyRecurringRevenue: "monthly_recurring_revenue",
    succeededCheckouts: "succeeded_checkouts",
    checkoutsConversion: "checkouts_conversion",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsTotals$ {
  /** @deprecated use `MetricsTotals$inboundSchema` instead. */
  export const inboundSchema = MetricsTotals$inboundSchema;
  /** @deprecated use `MetricsTotals$outboundSchema` instead. */
  export const outboundSchema = MetricsTotals$outboundSchema;
  /** @deprecated use `MetricsTotals$Outbound` instead. */
  export type Outbound = MetricsTotals$Outbound;
}

export function metricsTotalsToJSON(metricsTotals: MetricsTotals): string {
  return JSON.stringify(MetricsTotals$outboundSchema.parse(metricsTotals));
}

export function metricsTotalsFromJSON(
  jsonString: string,
): SafeParseResult<MetricsTotals, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsTotals$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsTotals' from JSON`,
  );
}
