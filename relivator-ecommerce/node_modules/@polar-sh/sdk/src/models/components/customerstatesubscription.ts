/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomerStateSubscriptionMeter,
  CustomerStateSubscriptionMeter$inboundSchema,
  CustomerStateSubscriptionMeter$Outbound,
  CustomerStateSubscriptionMeter$outboundSchema,
} from "./customerstatesubscriptionmeter.js";
import {
  SubscriptionRecurringInterval,
  SubscriptionRecurringInterval$inboundSchema,
  SubscriptionRecurringInterval$outboundSchema,
} from "./subscriptionrecurringinterval.js";

export type CustomerStateSubscriptionCustomFieldData =
  | string
  | number
  | boolean
  | Date;

export type CustomerStateSubscriptionMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * An active customer subscription.
 */
export type CustomerStateSubscription = {
  /**
   * The ID of the subscription.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * Key-value object storing custom field values.
   */
  customFieldData?:
    | { [k: string]: string | number | boolean | Date | null }
    | undefined;
  metadata: { [k: string]: string | number | number | boolean };
  status?: "active" | undefined;
  /**
   * The amount of the subscription.
   */
  amount: number;
  /**
   * The currency of the subscription.
   */
  currency: string;
  recurringInterval: SubscriptionRecurringInterval;
  /**
   * The start timestamp of the current billing period.
   */
  currentPeriodStart: Date;
  /**
   * The end timestamp of the current billing period.
   */
  currentPeriodEnd: Date | null;
  /**
   * Whether the subscription will be canceled at the end of the current period.
   */
  cancelAtPeriodEnd: boolean;
  /**
   * The timestamp when the subscription was canceled. The subscription might still be active if `cancel_at_period_end` is `true`.
   */
  canceledAt: Date | null;
  /**
   * The timestamp when the subscription started.
   */
  startedAt: Date | null;
  /**
   * The timestamp when the subscription will end.
   */
  endsAt: Date | null;
  /**
   * The ID of the subscribed product.
   */
  productId: string;
  /**
   * The ID of the applied discount, if any.
   */
  discountId: string | null;
  /**
   * List of meters associated with the subscription.
   */
  meters: Array<CustomerStateSubscriptionMeter>;
};

/** @internal */
export const CustomerStateSubscriptionCustomFieldData$inboundSchema: z.ZodType<
  CustomerStateSubscriptionCustomFieldData,
  z.ZodTypeDef,
  unknown
> = z.union([
  z.string(),
  z.number().int(),
  z.boolean(),
  z.string().datetime({ offset: true }).transform(v => new Date(v)),
]);

/** @internal */
export type CustomerStateSubscriptionCustomFieldData$Outbound =
  | string
  | number
  | boolean
  | string;

/** @internal */
export const CustomerStateSubscriptionCustomFieldData$outboundSchema: z.ZodType<
  CustomerStateSubscriptionCustomFieldData$Outbound,
  z.ZodTypeDef,
  CustomerStateSubscriptionCustomFieldData
> = z.union([
  z.string(),
  z.number().int(),
  z.boolean(),
  z.date().transform(v => v.toISOString()),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerStateSubscriptionCustomFieldData$ {
  /** @deprecated use `CustomerStateSubscriptionCustomFieldData$inboundSchema` instead. */
  export const inboundSchema =
    CustomerStateSubscriptionCustomFieldData$inboundSchema;
  /** @deprecated use `CustomerStateSubscriptionCustomFieldData$outboundSchema` instead. */
  export const outboundSchema =
    CustomerStateSubscriptionCustomFieldData$outboundSchema;
  /** @deprecated use `CustomerStateSubscriptionCustomFieldData$Outbound` instead. */
  export type Outbound = CustomerStateSubscriptionCustomFieldData$Outbound;
}

export function customerStateSubscriptionCustomFieldDataToJSON(
  customerStateSubscriptionCustomFieldData:
    CustomerStateSubscriptionCustomFieldData,
): string {
  return JSON.stringify(
    CustomerStateSubscriptionCustomFieldData$outboundSchema.parse(
      customerStateSubscriptionCustomFieldData,
    ),
  );
}

export function customerStateSubscriptionCustomFieldDataFromJSON(
  jsonString: string,
): SafeParseResult<
  CustomerStateSubscriptionCustomFieldData,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      CustomerStateSubscriptionCustomFieldData$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'CustomerStateSubscriptionCustomFieldData' from JSON`,
  );
}

/** @internal */
export const CustomerStateSubscriptionMetadata$inboundSchema: z.ZodType<
  CustomerStateSubscriptionMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomerStateSubscriptionMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomerStateSubscriptionMetadata$outboundSchema: z.ZodType<
  CustomerStateSubscriptionMetadata$Outbound,
  z.ZodTypeDef,
  CustomerStateSubscriptionMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerStateSubscriptionMetadata$ {
  /** @deprecated use `CustomerStateSubscriptionMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomerStateSubscriptionMetadata$inboundSchema;
  /** @deprecated use `CustomerStateSubscriptionMetadata$outboundSchema` instead. */
  export const outboundSchema =
    CustomerStateSubscriptionMetadata$outboundSchema;
  /** @deprecated use `CustomerStateSubscriptionMetadata$Outbound` instead. */
  export type Outbound = CustomerStateSubscriptionMetadata$Outbound;
}

export function customerStateSubscriptionMetadataToJSON(
  customerStateSubscriptionMetadata: CustomerStateSubscriptionMetadata,
): string {
  return JSON.stringify(
    CustomerStateSubscriptionMetadata$outboundSchema.parse(
      customerStateSubscriptionMetadata,
    ),
  );
}

export function customerStateSubscriptionMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomerStateSubscriptionMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerStateSubscriptionMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerStateSubscriptionMetadata' from JSON`,
  );
}

/** @internal */
export const CustomerStateSubscription$inboundSchema: z.ZodType<
  CustomerStateSubscription,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  custom_field_data: z.record(
    z.nullable(
      z.union([
        z.string(),
        z.number().int(),
        z.boolean(),
        z.string().datetime({ offset: true }).transform(v => new Date(v)),
      ]),
    ),
  ).optional(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  status: z.literal("active").optional(),
  amount: z.number().int(),
  currency: z.string(),
  recurring_interval: SubscriptionRecurringInterval$inboundSchema,
  current_period_start: z.string().datetime({ offset: true }).transform(v =>
    new Date(v)
  ),
  current_period_end: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  cancel_at_period_end: z.boolean(),
  canceled_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  started_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  ends_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  product_id: z.string(),
  discount_id: z.nullable(z.string()),
  meters: z.array(CustomerStateSubscriptionMeter$inboundSchema),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "custom_field_data": "customFieldData",
    "recurring_interval": "recurringInterval",
    "current_period_start": "currentPeriodStart",
    "current_period_end": "currentPeriodEnd",
    "cancel_at_period_end": "cancelAtPeriodEnd",
    "canceled_at": "canceledAt",
    "started_at": "startedAt",
    "ends_at": "endsAt",
    "product_id": "productId",
    "discount_id": "discountId",
  });
});

/** @internal */
export type CustomerStateSubscription$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  custom_field_data?:
    | { [k: string]: string | number | boolean | string | null }
    | undefined;
  metadata: { [k: string]: string | number | number | boolean };
  status: "active";
  amount: number;
  currency: string;
  recurring_interval: string;
  current_period_start: string;
  current_period_end: string | null;
  cancel_at_period_end: boolean;
  canceled_at: string | null;
  started_at: string | null;
  ends_at: string | null;
  product_id: string;
  discount_id: string | null;
  meters: Array<CustomerStateSubscriptionMeter$Outbound>;
};

/** @internal */
export const CustomerStateSubscription$outboundSchema: z.ZodType<
  CustomerStateSubscription$Outbound,
  z.ZodTypeDef,
  CustomerStateSubscription
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  customFieldData: z.record(
    z.nullable(
      z.union([
        z.string(),
        z.number().int(),
        z.boolean(),
        z.date().transform(v => v.toISOString()),
      ]),
    ),
  ).optional(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  status: z.literal("active").default("active" as const),
  amount: z.number().int(),
  currency: z.string(),
  recurringInterval: SubscriptionRecurringInterval$outboundSchema,
  currentPeriodStart: z.date().transform(v => v.toISOString()),
  currentPeriodEnd: z.nullable(z.date().transform(v => v.toISOString())),
  cancelAtPeriodEnd: z.boolean(),
  canceledAt: z.nullable(z.date().transform(v => v.toISOString())),
  startedAt: z.nullable(z.date().transform(v => v.toISOString())),
  endsAt: z.nullable(z.date().transform(v => v.toISOString())),
  productId: z.string(),
  discountId: z.nullable(z.string()),
  meters: z.array(CustomerStateSubscriptionMeter$outboundSchema),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    customFieldData: "custom_field_data",
    recurringInterval: "recurring_interval",
    currentPeriodStart: "current_period_start",
    currentPeriodEnd: "current_period_end",
    cancelAtPeriodEnd: "cancel_at_period_end",
    canceledAt: "canceled_at",
    startedAt: "started_at",
    endsAt: "ends_at",
    productId: "product_id",
    discountId: "discount_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerStateSubscription$ {
  /** @deprecated use `CustomerStateSubscription$inboundSchema` instead. */
  export const inboundSchema = CustomerStateSubscription$inboundSchema;
  /** @deprecated use `CustomerStateSubscription$outboundSchema` instead. */
  export const outboundSchema = CustomerStateSubscription$outboundSchema;
  /** @deprecated use `CustomerStateSubscription$Outbound` instead. */
  export type Outbound = CustomerStateSubscription$Outbound;
}

export function customerStateSubscriptionToJSON(
  customerStateSubscription: CustomerStateSubscription,
): string {
  return JSON.stringify(
    CustomerStateSubscription$outboundSchema.parse(customerStateSubscription),
  );
}

export function customerStateSubscriptionFromJSON(
  jsonString: string,
): SafeParseResult<CustomerStateSubscription, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerStateSubscription$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerStateSubscription' from JSON`,
  );
}
