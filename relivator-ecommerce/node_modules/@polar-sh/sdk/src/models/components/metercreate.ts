/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CountAggregation,
  CountAggregation$inboundSchema,
  CountAggregation$Outbound,
  CountAggregation$outboundSchema,
} from "./countaggregation.js";
import {
  Filter,
  Filter$inboundSchema,
  Filter$Outbound,
  Filter$outboundSchema,
} from "./filter.js";
import {
  PropertyAggregation,
  PropertyAggregation$inboundSchema,
  PropertyAggregation$Outbound,
  PropertyAggregation$outboundSchema,
} from "./propertyaggregation.js";

export type MeterCreateMetadata = string | number | number | boolean;

/**
 * The aggregation to apply on the filtered events to calculate the meter.
 */
export type MeterCreateAggregation =
  | (CountAggregation & { func: "count" })
  | (PropertyAggregation & { func: "avg" })
  | (PropertyAggregation & { func: "max" })
  | (PropertyAggregation & { func: "min" })
  | (PropertyAggregation & { func: "sum" });

export type MeterCreate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  /**
   * The name of the meter. Will be shown on customer's invoices and usage.
   */
  name: string;
  filter: Filter;
  /**
   * The aggregation to apply on the filtered events to calculate the meter.
   */
  aggregation:
    | (CountAggregation & { func: "count" })
    | (PropertyAggregation & { func: "avg" })
    | (PropertyAggregation & { func: "max" })
    | (PropertyAggregation & { func: "min" })
    | (PropertyAggregation & { func: "sum" });
  /**
   * The ID of the organization owning the meter. **Required unless you use an organization token.**
   */
  organizationId?: string | null | undefined;
};

/** @internal */
export const MeterCreateMetadata$inboundSchema: z.ZodType<
  MeterCreateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type MeterCreateMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const MeterCreateMetadata$outboundSchema: z.ZodType<
  MeterCreateMetadata$Outbound,
  z.ZodTypeDef,
  MeterCreateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterCreateMetadata$ {
  /** @deprecated use `MeterCreateMetadata$inboundSchema` instead. */
  export const inboundSchema = MeterCreateMetadata$inboundSchema;
  /** @deprecated use `MeterCreateMetadata$outboundSchema` instead. */
  export const outboundSchema = MeterCreateMetadata$outboundSchema;
  /** @deprecated use `MeterCreateMetadata$Outbound` instead. */
  export type Outbound = MeterCreateMetadata$Outbound;
}

export function meterCreateMetadataToJSON(
  meterCreateMetadata: MeterCreateMetadata,
): string {
  return JSON.stringify(
    MeterCreateMetadata$outboundSchema.parse(meterCreateMetadata),
  );
}

export function meterCreateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<MeterCreateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterCreateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterCreateMetadata' from JSON`,
  );
}

/** @internal */
export const MeterCreateAggregation$inboundSchema: z.ZodType<
  MeterCreateAggregation,
  z.ZodTypeDef,
  unknown
> = z.union([
  CountAggregation$inboundSchema.and(
    z.object({ func: z.literal("count") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("avg") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("max") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("min") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("sum") }).transform((v) => ({ func: v.func })),
  ),
]);

/** @internal */
export type MeterCreateAggregation$Outbound =
  | (CountAggregation$Outbound & { func: "count" })
  | (PropertyAggregation$Outbound & { func: "avg" })
  | (PropertyAggregation$Outbound & { func: "max" })
  | (PropertyAggregation$Outbound & { func: "min" })
  | (PropertyAggregation$Outbound & { func: "sum" });

/** @internal */
export const MeterCreateAggregation$outboundSchema: z.ZodType<
  MeterCreateAggregation$Outbound,
  z.ZodTypeDef,
  MeterCreateAggregation
> = z.union([
  CountAggregation$outboundSchema.and(
    z.object({ func: z.literal("count") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("avg") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("max") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("min") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("sum") }).transform((v) => ({ func: v.func })),
  ),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterCreateAggregation$ {
  /** @deprecated use `MeterCreateAggregation$inboundSchema` instead. */
  export const inboundSchema = MeterCreateAggregation$inboundSchema;
  /** @deprecated use `MeterCreateAggregation$outboundSchema` instead. */
  export const outboundSchema = MeterCreateAggregation$outboundSchema;
  /** @deprecated use `MeterCreateAggregation$Outbound` instead. */
  export type Outbound = MeterCreateAggregation$Outbound;
}

export function meterCreateAggregationToJSON(
  meterCreateAggregation: MeterCreateAggregation,
): string {
  return JSON.stringify(
    MeterCreateAggregation$outboundSchema.parse(meterCreateAggregation),
  );
}

export function meterCreateAggregationFromJSON(
  jsonString: string,
): SafeParseResult<MeterCreateAggregation, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterCreateAggregation$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterCreateAggregation' from JSON`,
  );
}

/** @internal */
export const MeterCreate$inboundSchema: z.ZodType<
  MeterCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.string(),
  filter: Filter$inboundSchema,
  aggregation: z.union([
    CountAggregation$inboundSchema.and(
      z.object({ func: z.literal("count") }).transform((v) => ({
        func: v.func,
      })),
    ),
    PropertyAggregation$inboundSchema.and(
      z.object({ func: z.literal("avg") }).transform((v) => ({ func: v.func })),
    ),
    PropertyAggregation$inboundSchema.and(
      z.object({ func: z.literal("max") }).transform((v) => ({ func: v.func })),
    ),
    PropertyAggregation$inboundSchema.and(
      z.object({ func: z.literal("min") }).transform((v) => ({ func: v.func })),
    ),
    PropertyAggregation$inboundSchema.and(
      z.object({ func: z.literal("sum") }).transform((v) => ({ func: v.func })),
    ),
  ]),
  organization_id: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
  });
});

/** @internal */
export type MeterCreate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  name: string;
  filter: Filter$Outbound;
  aggregation:
    | (CountAggregation$Outbound & { func: "count" })
    | (PropertyAggregation$Outbound & { func: "avg" })
    | (PropertyAggregation$Outbound & { func: "max" })
    | (PropertyAggregation$Outbound & { func: "min" })
    | (PropertyAggregation$Outbound & { func: "sum" });
  organization_id?: string | null | undefined;
};

/** @internal */
export const MeterCreate$outboundSchema: z.ZodType<
  MeterCreate$Outbound,
  z.ZodTypeDef,
  MeterCreate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.string(),
  filter: Filter$outboundSchema,
  aggregation: z.union([
    CountAggregation$outboundSchema.and(
      z.object({ func: z.literal("count") }).transform((v) => ({
        func: v.func,
      })),
    ),
    PropertyAggregation$outboundSchema.and(
      z.object({ func: z.literal("avg") }).transform((v) => ({ func: v.func })),
    ),
    PropertyAggregation$outboundSchema.and(
      z.object({ func: z.literal("max") }).transform((v) => ({ func: v.func })),
    ),
    PropertyAggregation$outboundSchema.and(
      z.object({ func: z.literal("min") }).transform((v) => ({ func: v.func })),
    ),
    PropertyAggregation$outboundSchema.and(
      z.object({ func: z.literal("sum") }).transform((v) => ({ func: v.func })),
    ),
  ]),
  organizationId: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterCreate$ {
  /** @deprecated use `MeterCreate$inboundSchema` instead. */
  export const inboundSchema = MeterCreate$inboundSchema;
  /** @deprecated use `MeterCreate$outboundSchema` instead. */
  export const outboundSchema = MeterCreate$outboundSchema;
  /** @deprecated use `MeterCreate$Outbound` instead. */
  export type Outbound = MeterCreate$Outbound;
}

export function meterCreateToJSON(meterCreate: MeterCreate): string {
  return JSON.stringify(MeterCreate$outboundSchema.parse(meterCreate));
}

export function meterCreateFromJSON(
  jsonString: string,
): SafeParseResult<MeterCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterCreate' from JSON`,
  );
}
