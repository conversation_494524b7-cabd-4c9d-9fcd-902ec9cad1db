/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const OrganizationSortProperty = {
  CreatedAt: "created_at",
  MinusCreatedAt: "-created_at",
  Slug: "slug",
  MinusSlug: "-slug",
  Name: "name",
  MinusName: "-name",
} as const;
export type OrganizationSortProperty = ClosedEnum<
  typeof OrganizationSortProperty
>;

/** @internal */
export const OrganizationSortProperty$inboundSchema: z.ZodNativeEnum<
  typeof OrganizationSortProperty
> = z.nativeEnum(OrganizationSortProperty);

/** @internal */
export const OrganizationSortProperty$outboundSchema: z.ZodNativeEnum<
  typeof OrganizationSortProperty
> = OrganizationSortProperty$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrganizationSortProperty$ {
  /** @deprecated use `OrganizationSortProperty$inboundSchema` instead. */
  export const inboundSchema = OrganizationSortProperty$inboundSchema;
  /** @deprecated use `OrganizationSortProperty$outboundSchema` instead. */
  export const outboundSchema = OrganizationSortProperty$outboundSchema;
}
