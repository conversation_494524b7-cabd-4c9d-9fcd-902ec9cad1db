/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomerCancellationReason,
  CustomerCancellationReason$inboundSchema,
  CustomerCancellationReason$outboundSchema,
} from "./customercancellationreason.js";

export type CustomerSubscriptionCancel = {
  /**
   * Cancel an active subscription once the current period ends.
   *
   * @remarks
   *
   * Or uncancel a subscription currently set to be revoked at period end.
   */
  cancelAtPeriodEnd?: boolean | null | undefined;
  /**
   * Customers reason for cancellation.
   *
   * @remarks
   *
   * * `too_expensive`: Too expensive for the customer.
   * * `missing_features`: Customer is missing certain features.
   * * `switched_service`: Customer switched to another service.
   * * `unused`: Customer is not using it enough.
   * * `customer_service`: Customer is not satisfied with the customer service.
   * * `low_quality`: Customer is unhappy with the quality.
   * * `too_complex`: Customer considers the service too complicated.
   * * `other`: Other reason(s).
   */
  cancellationReason?: CustomerCancellationReason | null | undefined;
  /**
   * Customer feedback and why they decided to cancel.
   */
  cancellationComment?: string | null | undefined;
};

/** @internal */
export const CustomerSubscriptionCancel$inboundSchema: z.ZodType<
  CustomerSubscriptionCancel,
  z.ZodTypeDef,
  unknown
> = z.object({
  cancel_at_period_end: z.nullable(z.boolean()).optional(),
  cancellation_reason: z.nullable(CustomerCancellationReason$inboundSchema)
    .optional(),
  cancellation_comment: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    "cancel_at_period_end": "cancelAtPeriodEnd",
    "cancellation_reason": "cancellationReason",
    "cancellation_comment": "cancellationComment",
  });
});

/** @internal */
export type CustomerSubscriptionCancel$Outbound = {
  cancel_at_period_end?: boolean | null | undefined;
  cancellation_reason?: string | null | undefined;
  cancellation_comment?: string | null | undefined;
};

/** @internal */
export const CustomerSubscriptionCancel$outboundSchema: z.ZodType<
  CustomerSubscriptionCancel$Outbound,
  z.ZodTypeDef,
  CustomerSubscriptionCancel
> = z.object({
  cancelAtPeriodEnd: z.nullable(z.boolean()).optional(),
  cancellationReason: z.nullable(CustomerCancellationReason$outboundSchema)
    .optional(),
  cancellationComment: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    cancelAtPeriodEnd: "cancel_at_period_end",
    cancellationReason: "cancellation_reason",
    cancellationComment: "cancellation_comment",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerSubscriptionCancel$ {
  /** @deprecated use `CustomerSubscriptionCancel$inboundSchema` instead. */
  export const inboundSchema = CustomerSubscriptionCancel$inboundSchema;
  /** @deprecated use `CustomerSubscriptionCancel$outboundSchema` instead. */
  export const outboundSchema = CustomerSubscriptionCancel$outboundSchema;
  /** @deprecated use `CustomerSubscriptionCancel$Outbound` instead. */
  export type Outbound = CustomerSubscriptionCancel$Outbound;
}

export function customerSubscriptionCancelToJSON(
  customerSubscriptionCancel: CustomerSubscriptionCancel,
): string {
  return JSON.stringify(
    CustomerSubscriptionCancel$outboundSchema.parse(customerSubscriptionCancel),
  );
}

export function customerSubscriptionCancelFromJSON(
  jsonString: string,
): SafeParseResult<CustomerSubscriptionCancel, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerSubscriptionCancel$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerSubscriptionCancel' from JSON`,
  );
}
