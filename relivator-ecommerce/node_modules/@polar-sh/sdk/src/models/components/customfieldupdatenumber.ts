/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomFieldNumberProperties,
  CustomFieldNumberProperties$inboundSchema,
  CustomFieldNumberProperties$Outbound,
  CustomFieldNumberProperties$outboundSchema,
} from "./customfieldnumberproperties.js";

export type CustomFieldUpdateNumberMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * Schema to update a custom field of type number.
 */
export type CustomFieldUpdateNumber = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  name?: string | null | undefined;
  slug?: string | null | undefined;
  type?: "number" | undefined;
  properties?: CustomFieldNumberProperties | null | undefined;
};

/** @internal */
export const CustomFieldUpdateNumberMetadata$inboundSchema: z.ZodType<
  CustomFieldUpdateNumberMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomFieldUpdateNumberMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomFieldUpdateNumberMetadata$outboundSchema: z.ZodType<
  CustomFieldUpdateNumberMetadata$Outbound,
  z.ZodTypeDef,
  CustomFieldUpdateNumberMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldUpdateNumberMetadata$ {
  /** @deprecated use `CustomFieldUpdateNumberMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomFieldUpdateNumberMetadata$inboundSchema;
  /** @deprecated use `CustomFieldUpdateNumberMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomFieldUpdateNumberMetadata$outboundSchema;
  /** @deprecated use `CustomFieldUpdateNumberMetadata$Outbound` instead. */
  export type Outbound = CustomFieldUpdateNumberMetadata$Outbound;
}

export function customFieldUpdateNumberMetadataToJSON(
  customFieldUpdateNumberMetadata: CustomFieldUpdateNumberMetadata,
): string {
  return JSON.stringify(
    CustomFieldUpdateNumberMetadata$outboundSchema.parse(
      customFieldUpdateNumberMetadata,
    ),
  );
}

export function customFieldUpdateNumberMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldUpdateNumberMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldUpdateNumberMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldUpdateNumberMetadata' from JSON`,
  );
}

/** @internal */
export const CustomFieldUpdateNumber$inboundSchema: z.ZodType<
  CustomFieldUpdateNumber,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.nullable(z.string()).optional(),
  slug: z.nullable(z.string()).optional(),
  type: z.literal("number").optional(),
  properties: z.nullable(CustomFieldNumberProperties$inboundSchema).optional(),
});

/** @internal */
export type CustomFieldUpdateNumber$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  name?: string | null | undefined;
  slug?: string | null | undefined;
  type: "number";
  properties?: CustomFieldNumberProperties$Outbound | null | undefined;
};

/** @internal */
export const CustomFieldUpdateNumber$outboundSchema: z.ZodType<
  CustomFieldUpdateNumber$Outbound,
  z.ZodTypeDef,
  CustomFieldUpdateNumber
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.nullable(z.string()).optional(),
  slug: z.nullable(z.string()).optional(),
  type: z.literal("number").default("number" as const),
  properties: z.nullable(CustomFieldNumberProperties$outboundSchema).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldUpdateNumber$ {
  /** @deprecated use `CustomFieldUpdateNumber$inboundSchema` instead. */
  export const inboundSchema = CustomFieldUpdateNumber$inboundSchema;
  /** @deprecated use `CustomFieldUpdateNumber$outboundSchema` instead. */
  export const outboundSchema = CustomFieldUpdateNumber$outboundSchema;
  /** @deprecated use `CustomFieldUpdateNumber$Outbound` instead. */
  export type Outbound = CustomFieldUpdateNumber$Outbound;
}

export function customFieldUpdateNumberToJSON(
  customFieldUpdateNumber: CustomFieldUpdateNumber,
): string {
  return JSON.stringify(
    CustomFieldUpdateNumber$outboundSchema.parse(customFieldUpdateNumber),
  );
}

export function customFieldUpdateNumberFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldUpdateNumber, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldUpdateNumber$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldUpdateNumber' from JSON`,
  );
}
