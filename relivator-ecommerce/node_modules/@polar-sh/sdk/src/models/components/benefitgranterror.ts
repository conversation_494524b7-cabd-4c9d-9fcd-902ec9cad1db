/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type BenefitGrantError = {
  message: string;
  type: string;
  timestamp: string;
};

/** @internal */
export const BenefitGrantError$inboundSchema: z.ZodType<
  BenefitGrantError,
  z.ZodTypeDef,
  unknown
> = z.object({
  message: z.string(),
  type: z.string(),
  timestamp: z.string(),
});

/** @internal */
export type BenefitGrantError$Outbound = {
  message: string;
  type: string;
  timestamp: string;
};

/** @internal */
export const BenefitGrantError$outboundSchema: z.ZodType<
  BenefitGrantError$Outbound,
  z.ZodTypeDef,
  BenefitGrantError
> = z.object({
  message: z.string(),
  type: z.string(),
  timestamp: z.string(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGrantError$ {
  /** @deprecated use `BenefitGrantError$inboundSchema` instead. */
  export const inboundSchema = BenefitGrantError$inboundSchema;
  /** @deprecated use `BenefitGrantError$outboundSchema` instead. */
  export const outboundSchema = BenefitGrantError$outboundSchema;
  /** @deprecated use `BenefitGrantError$Outbound` instead. */
  export type Outbound = BenefitGrantError$Outbound;
}

export function benefitGrantErrorToJSON(
  benefitGrantError: BenefitGrantError,
): string {
  return JSON.stringify(
    BenefitGrantError$outboundSchema.parse(benefitGrantError),
  );
}

export function benefitGrantErrorFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGrantError, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitGrantError$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGrantError' from JSON`,
  );
}
