/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Organization,
  Organization$inboundSchema,
  Organization$Outbound,
  Organization$outboundSchema,
} from "./organization.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";

export type ListResourceOrganization = {
  items: Array<Organization>;
  pagination: Pagination;
};

/** @internal */
export const ListResourceOrganization$inboundSchema: z.ZodType<
  ListResourceOrganization,
  z.ZodTypeDef,
  unknown
> = z.object({
  items: z.array(Organization$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ListResourceOrganization$Outbound = {
  items: Array<Organization$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResourceOrganization$outboundSchema: z.ZodType<
  ListResourceOrganization$Outbound,
  z.ZodTypeDef,
  ListResourceOrganization
> = z.object({
  items: z.array(Organization$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResourceOrganization$ {
  /** @deprecated use `ListResourceOrganization$inboundSchema` instead. */
  export const inboundSchema = ListResourceOrganization$inboundSchema;
  /** @deprecated use `ListResourceOrganization$outboundSchema` instead. */
  export const outboundSchema = ListResourceOrganization$outboundSchema;
  /** @deprecated use `ListResourceOrganization$Outbound` instead. */
  export type Outbound = ListResourceOrganization$Outbound;
}

export function listResourceOrganizationToJSON(
  listResourceOrganization: ListResourceOrganization,
): string {
  return JSON.stringify(
    ListResourceOrganization$outboundSchema.parse(listResourceOrganization),
  );
}

export function listResourceOrganizationFromJSON(
  jsonString: string,
): SafeParseResult<ListResourceOrganization, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListResourceOrganization$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListResourceOrganization' from JSON`,
  );
}
