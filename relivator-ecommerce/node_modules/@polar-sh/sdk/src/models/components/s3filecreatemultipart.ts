/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  S3FileCreatePart,
  S3FileCreatePart$inboundSchema,
  S3FileCreatePart$Outbound,
  S3FileCreatePart$outboundSchema,
} from "./s3filecreatepart.js";

export type S3FileCreateMultipart = {
  parts: Array<S3FileCreatePart>;
};

/** @internal */
export const S3FileCreateMultipart$inboundSchema: z.ZodType<
  S3FileCreateMultipart,
  z.ZodTypeDef,
  unknown
> = z.object({
  parts: z.array(S3FileCreatePart$inboundSchema),
});

/** @internal */
export type S3FileCreateMultipart$Outbound = {
  parts: Array<S3FileCreatePart$Outbound>;
};

/** @internal */
export const S3FileCreateMultipart$outboundSchema: z.ZodType<
  S3FileCreateMultipart$Outbound,
  z.ZodTypeDef,
  S3FileCreateMultipart
> = z.object({
  parts: z.array(S3FileCreatePart$outboundSchema),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace S3FileCreateMultipart$ {
  /** @deprecated use `S3FileCreateMultipart$inboundSchema` instead. */
  export const inboundSchema = S3FileCreateMultipart$inboundSchema;
  /** @deprecated use `S3FileCreateMultipart$outboundSchema` instead. */
  export const outboundSchema = S3FileCreateMultipart$outboundSchema;
  /** @deprecated use `S3FileCreateMultipart$Outbound` instead. */
  export type Outbound = S3FileCreateMultipart$Outbound;
}

export function s3FileCreateMultipartToJSON(
  s3FileCreateMultipart: S3FileCreateMultipart,
): string {
  return JSON.stringify(
    S3FileCreateMultipart$outboundSchema.parse(s3FileCreateMultipart),
  );
}

export function s3FileCreateMultipartFromJSON(
  jsonString: string,
): SafeParseResult<S3FileCreateMultipart, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => S3FileCreateMultipart$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'S3FileCreateMultipart' from JSON`,
  );
}
