/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { RFCDate } from "../../types/rfcdate.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  MetricsIntervalsLimits,
  MetricsIntervalsLimits$inboundSchema,
  MetricsIntervalsLimits$Outbound,
  MetricsIntervalsLimits$outboundSchema,
} from "./metricsintervalslimits.js";

/**
 * Date limits to get metrics.
 */
export type MetricsLimits = {
  /**
   * Minimum date to get metrics.
   */
  minDate: RFCDate;
  /**
   * Date interval limits to get metrics for each interval.
   */
  intervals: MetricsIntervalsLimits;
};

/** @internal */
export const MetricsLimits$inboundSchema: z.ZodType<
  MetricsLimits,
  z.ZodTypeDef,
  unknown
> = z.object({
  min_date: z.string().transform(v => new RFCDate(v)),
  intervals: MetricsIntervalsLimits$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "min_date": "minDate",
  });
});

/** @internal */
export type MetricsLimits$Outbound = {
  min_date: string;
  intervals: MetricsIntervalsLimits$Outbound;
};

/** @internal */
export const MetricsLimits$outboundSchema: z.ZodType<
  MetricsLimits$Outbound,
  z.ZodTypeDef,
  MetricsLimits
> = z.object({
  minDate: z.instanceof(RFCDate).transform(v => v.toString()),
  intervals: MetricsIntervalsLimits$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    minDate: "min_date",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsLimits$ {
  /** @deprecated use `MetricsLimits$inboundSchema` instead. */
  export const inboundSchema = MetricsLimits$inboundSchema;
  /** @deprecated use `MetricsLimits$outboundSchema` instead. */
  export const outboundSchema = MetricsLimits$outboundSchema;
  /** @deprecated use `MetricsLimits$Outbound` instead. */
  export type Outbound = MetricsLimits$Outbound;
}

export function metricsLimitsToJSON(metricsLimits: MetricsLimits): string {
  return JSON.stringify(MetricsLimits$outboundSchema.parse(metricsLimits));
}

export function metricsLimitsFromJSON(
  jsonString: string,
): SafeParseResult<MetricsLimits, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsLimits$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsLimits' from JSON`,
  );
}
