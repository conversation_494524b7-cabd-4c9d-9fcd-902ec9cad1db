/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const CustomerSubscriptionSortProperty = {
  StartedAt: "started_at",
  MinusStartedAt: "-started_at",
  Amount: "amount",
  MinusAmount: "-amount",
  Status: "status",
  MinusStatus: "-status",
  Organization: "organization",
  MinusOrganization: "-organization",
  Product: "product",
  MinusProduct: "-product",
} as const;
export type CustomerSubscriptionSortProperty = ClosedEnum<
  typeof CustomerSubscriptionSortProperty
>;

/** @internal */
export const CustomerSubscriptionSortProperty$inboundSchema: z.ZodNativeEnum<
  typeof CustomerSubscriptionSortProperty
> = z.nativeEnum(CustomerSubscriptionSortProperty);

/** @internal */
export const CustomerSubscriptionSortProperty$outboundSchema: z.ZodNativeEnum<
  typeof CustomerSubscriptionSortProperty
> = CustomerSubscriptionSortProperty$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerSubscriptionSortProperty$ {
  /** @deprecated use `CustomerSubscriptionSortProperty$inboundSchema` instead. */
  export const inboundSchema = CustomerSubscriptionSortProperty$inboundSchema;
  /** @deprecated use `CustomerSubscriptionSortProperty$outboundSchema` instead. */
  export const outboundSchema = CustomerSubscriptionSortProperty$outboundSchema;
}
