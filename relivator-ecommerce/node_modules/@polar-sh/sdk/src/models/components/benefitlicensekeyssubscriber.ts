/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitLicenseKeysSubscriberProperties,
  BenefitLicenseKeysSubscriberProperties$inboundSchema,
  BenefitLicenseKeysSubscriberProperties$Outbound,
  BenefitLicenseKeysSubscriberProperties$outboundSchema,
} from "./benefitlicensekeyssubscriberproperties.js";
import {
  Organization,
  Organization$inboundSchema,
  Organization$Outbound,
  Organization$outboundSchema,
} from "./organization.js";

export type BenefitLicenseKeysSubscriberMetadata =
  | string
  | number
  | number
  | boolean;

export type BenefitLicenseKeysSubscriber = {
  /**
   * The ID of the benefit.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  type?: "license_keys" | undefined;
  /**
   * The description of the benefit.
   */
  description: string;
  /**
   * Whether the benefit is selectable when creating a product.
   */
  selectable: boolean;
  /**
   * Whether the benefit is deletable.
   */
  deletable: boolean;
  /**
   * The ID of the organization owning the benefit.
   */
  organizationId: string;
  metadata: { [k: string]: string | number | number | boolean };
  organization: Organization;
  properties: BenefitLicenseKeysSubscriberProperties;
};

/** @internal */
export const BenefitLicenseKeysSubscriberMetadata$inboundSchema: z.ZodType<
  BenefitLicenseKeysSubscriberMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitLicenseKeysSubscriberMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitLicenseKeysSubscriberMetadata$outboundSchema: z.ZodType<
  BenefitLicenseKeysSubscriberMetadata$Outbound,
  z.ZodTypeDef,
  BenefitLicenseKeysSubscriberMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitLicenseKeysSubscriberMetadata$ {
  /** @deprecated use `BenefitLicenseKeysSubscriberMetadata$inboundSchema` instead. */
  export const inboundSchema =
    BenefitLicenseKeysSubscriberMetadata$inboundSchema;
  /** @deprecated use `BenefitLicenseKeysSubscriberMetadata$outboundSchema` instead. */
  export const outboundSchema =
    BenefitLicenseKeysSubscriberMetadata$outboundSchema;
  /** @deprecated use `BenefitLicenseKeysSubscriberMetadata$Outbound` instead. */
  export type Outbound = BenefitLicenseKeysSubscriberMetadata$Outbound;
}

export function benefitLicenseKeysSubscriberMetadataToJSON(
  benefitLicenseKeysSubscriberMetadata: BenefitLicenseKeysSubscriberMetadata,
): string {
  return JSON.stringify(
    BenefitLicenseKeysSubscriberMetadata$outboundSchema.parse(
      benefitLicenseKeysSubscriberMetadata,
    ),
  );
}

export function benefitLicenseKeysSubscriberMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitLicenseKeysSubscriberMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitLicenseKeysSubscriberMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitLicenseKeysSubscriberMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitLicenseKeysSubscriber$inboundSchema: z.ZodType<
  BenefitLicenseKeysSubscriber,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  type: z.literal("license_keys").optional(),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organization_id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  organization: Organization$inboundSchema,
  properties: BenefitLicenseKeysSubscriberProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitLicenseKeysSubscriber$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  type: "license_keys";
  description: string;
  selectable: boolean;
  deletable: boolean;
  organization_id: string;
  metadata: { [k: string]: string | number | number | boolean };
  organization: Organization$Outbound;
  properties: BenefitLicenseKeysSubscriberProperties$Outbound;
};

/** @internal */
export const BenefitLicenseKeysSubscriber$outboundSchema: z.ZodType<
  BenefitLicenseKeysSubscriber$Outbound,
  z.ZodTypeDef,
  BenefitLicenseKeysSubscriber
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  type: z.literal("license_keys").default("license_keys" as const),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organizationId: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  organization: Organization$outboundSchema,
  properties: BenefitLicenseKeysSubscriberProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitLicenseKeysSubscriber$ {
  /** @deprecated use `BenefitLicenseKeysSubscriber$inboundSchema` instead. */
  export const inboundSchema = BenefitLicenseKeysSubscriber$inboundSchema;
  /** @deprecated use `BenefitLicenseKeysSubscriber$outboundSchema` instead. */
  export const outboundSchema = BenefitLicenseKeysSubscriber$outboundSchema;
  /** @deprecated use `BenefitLicenseKeysSubscriber$Outbound` instead. */
  export type Outbound = BenefitLicenseKeysSubscriber$Outbound;
}

export function benefitLicenseKeysSubscriberToJSON(
  benefitLicenseKeysSubscriber: BenefitLicenseKeysSubscriber,
): string {
  return JSON.stringify(
    BenefitLicenseKeysSubscriber$outboundSchema.parse(
      benefitLicenseKeysSubscriber,
    ),
  );
}

export function benefitLicenseKeysSubscriberFromJSON(
  jsonString: string,
): SafeParseResult<BenefitLicenseKeysSubscriber, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitLicenseKeysSubscriber$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitLicenseKeysSubscriber' from JSON`,
  );
}
