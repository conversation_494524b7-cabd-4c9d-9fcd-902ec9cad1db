/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitGrantCustomProperties,
  BenefitGrantCustomProperties$inboundSchema,
  BenefitGrantCustomProperties$Outbound,
  BenefitGrantCustomProperties$outboundSchema,
} from "./benefitgrantcustomproperties.js";
import {
  BenefitGrantDiscordProperties,
  BenefitGrantDiscordProperties$inboundSchema,
  BenefitGrantDiscordProperties$Outbound,
  BenefitGrantDiscordProperties$outboundSchema,
} from "./benefitgrantdiscordproperties.js";
import {
  BenefitGrantDownloadablesProperties,
  BenefitGrantDownloadablesProperties$inboundSchema,
  BenefitGrantDownloadablesProperties$Outbound,
  BenefitGrantDownloadablesProperties$outboundSchema,
} from "./benefitgrantdownloadablesproperties.js";
import {
  BenefitGrantGitHubRepositoryProperties,
  BenefitGrantGitHubRepositoryProperties$inboundSchema,
  BenefitGrantGitHubRepositoryProperties$Outbound,
  BenefitGrantGitHubRepositoryProperties$outboundSchema,
} from "./benefitgrantgithubrepositoryproperties.js";
import {
  BenefitGrantLicenseKeysProperties,
  BenefitGrantLicenseKeysProperties$inboundSchema,
  BenefitGrantLicenseKeysProperties$Outbound,
  BenefitGrantLicenseKeysProperties$outboundSchema,
} from "./benefitgrantlicensekeysproperties.js";
import {
  BenefitType,
  BenefitType$inboundSchema,
  BenefitType$outboundSchema,
} from "./benefittype.js";

export type CustomerStateBenefitGrantProperties =
  | BenefitGrantCustomProperties
  | BenefitGrantDownloadablesProperties
  | BenefitGrantLicenseKeysProperties
  | BenefitGrantDiscordProperties
  | BenefitGrantGitHubRepositoryProperties;

/**
 * An active benefit grant for a customer.
 */
export type CustomerStateBenefitGrant = {
  /**
   * The ID of the grant.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The timestamp when the benefit was granted.
   */
  grantedAt: Date;
  /**
   * The ID of the benefit concerned by this grant.
   */
  benefitId: string;
  benefitType: BenefitType;
  properties:
    | BenefitGrantCustomProperties
    | BenefitGrantDownloadablesProperties
    | BenefitGrantLicenseKeysProperties
    | BenefitGrantDiscordProperties
    | BenefitGrantGitHubRepositoryProperties;
};

/** @internal */
export const CustomerStateBenefitGrantProperties$inboundSchema: z.ZodType<
  CustomerStateBenefitGrantProperties,
  z.ZodTypeDef,
  unknown
> = z.union([
  BenefitGrantCustomProperties$inboundSchema,
  BenefitGrantDownloadablesProperties$inboundSchema,
  BenefitGrantLicenseKeysProperties$inboundSchema,
  BenefitGrantDiscordProperties$inboundSchema,
  BenefitGrantGitHubRepositoryProperties$inboundSchema,
]);

/** @internal */
export type CustomerStateBenefitGrantProperties$Outbound =
  | BenefitGrantCustomProperties$Outbound
  | BenefitGrantDownloadablesProperties$Outbound
  | BenefitGrantLicenseKeysProperties$Outbound
  | BenefitGrantDiscordProperties$Outbound
  | BenefitGrantGitHubRepositoryProperties$Outbound;

/** @internal */
export const CustomerStateBenefitGrantProperties$outboundSchema: z.ZodType<
  CustomerStateBenefitGrantProperties$Outbound,
  z.ZodTypeDef,
  CustomerStateBenefitGrantProperties
> = z.union([
  BenefitGrantCustomProperties$outboundSchema,
  BenefitGrantDownloadablesProperties$outboundSchema,
  BenefitGrantLicenseKeysProperties$outboundSchema,
  BenefitGrantDiscordProperties$outboundSchema,
  BenefitGrantGitHubRepositoryProperties$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerStateBenefitGrantProperties$ {
  /** @deprecated use `CustomerStateBenefitGrantProperties$inboundSchema` instead. */
  export const inboundSchema =
    CustomerStateBenefitGrantProperties$inboundSchema;
  /** @deprecated use `CustomerStateBenefitGrantProperties$outboundSchema` instead. */
  export const outboundSchema =
    CustomerStateBenefitGrantProperties$outboundSchema;
  /** @deprecated use `CustomerStateBenefitGrantProperties$Outbound` instead. */
  export type Outbound = CustomerStateBenefitGrantProperties$Outbound;
}

export function customerStateBenefitGrantPropertiesToJSON(
  customerStateBenefitGrantProperties: CustomerStateBenefitGrantProperties,
): string {
  return JSON.stringify(
    CustomerStateBenefitGrantProperties$outboundSchema.parse(
      customerStateBenefitGrantProperties,
    ),
  );
}

export function customerStateBenefitGrantPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<CustomerStateBenefitGrantProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      CustomerStateBenefitGrantProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerStateBenefitGrantProperties' from JSON`,
  );
}

/** @internal */
export const CustomerStateBenefitGrant$inboundSchema: z.ZodType<
  CustomerStateBenefitGrant,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  granted_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  benefit_id: z.string(),
  benefit_type: BenefitType$inboundSchema,
  properties: z.union([
    BenefitGrantCustomProperties$inboundSchema,
    BenefitGrantDownloadablesProperties$inboundSchema,
    BenefitGrantLicenseKeysProperties$inboundSchema,
    BenefitGrantDiscordProperties$inboundSchema,
    BenefitGrantGitHubRepositoryProperties$inboundSchema,
  ]),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "granted_at": "grantedAt",
    "benefit_id": "benefitId",
    "benefit_type": "benefitType",
  });
});

/** @internal */
export type CustomerStateBenefitGrant$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  granted_at: string;
  benefit_id: string;
  benefit_type: string;
  properties:
    | BenefitGrantCustomProperties$Outbound
    | BenefitGrantDownloadablesProperties$Outbound
    | BenefitGrantLicenseKeysProperties$Outbound
    | BenefitGrantDiscordProperties$Outbound
    | BenefitGrantGitHubRepositoryProperties$Outbound;
};

/** @internal */
export const CustomerStateBenefitGrant$outboundSchema: z.ZodType<
  CustomerStateBenefitGrant$Outbound,
  z.ZodTypeDef,
  CustomerStateBenefitGrant
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  grantedAt: z.date().transform(v => v.toISOString()),
  benefitId: z.string(),
  benefitType: BenefitType$outboundSchema,
  properties: z.union([
    BenefitGrantCustomProperties$outboundSchema,
    BenefitGrantDownloadablesProperties$outboundSchema,
    BenefitGrantLicenseKeysProperties$outboundSchema,
    BenefitGrantDiscordProperties$outboundSchema,
    BenefitGrantGitHubRepositoryProperties$outboundSchema,
  ]),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    grantedAt: "granted_at",
    benefitId: "benefit_id",
    benefitType: "benefit_type",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerStateBenefitGrant$ {
  /** @deprecated use `CustomerStateBenefitGrant$inboundSchema` instead. */
  export const inboundSchema = CustomerStateBenefitGrant$inboundSchema;
  /** @deprecated use `CustomerStateBenefitGrant$outboundSchema` instead. */
  export const outboundSchema = CustomerStateBenefitGrant$outboundSchema;
  /** @deprecated use `CustomerStateBenefitGrant$Outbound` instead. */
  export type Outbound = CustomerStateBenefitGrant$Outbound;
}

export function customerStateBenefitGrantToJSON(
  customerStateBenefitGrant: CustomerStateBenefitGrant,
): string {
  return JSON.stringify(
    CustomerStateBenefitGrant$outboundSchema.parse(customerStateBenefitGrant),
  );
}

export function customerStateBenefitGrantFromJSON(
  jsonString: string,
): SafeParseResult<CustomerStateBenefitGrant, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerStateBenefitGrant$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerStateBenefitGrant' from JSON`,
  );
}
