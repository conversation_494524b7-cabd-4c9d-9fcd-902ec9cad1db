/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  S3FileCreateMultipart,
  S3FileCreateMultipart$inboundSchema,
  S3FileCreateMultipart$Outbound,
  S3FileCreateMultipart$outboundSchema,
} from "./s3filecreatemultipart.js";

/**
 * Schema to create a file to be used as a product media file.
 */
export type ProductMediaFileCreate = {
  organizationId?: string | null | undefined;
  name: string;
  /**
   * MIME type of the file. Only images are supported for this type of file.
   */
  mimeType: string;
  /**
   * Size of the file. A maximum of 10 MB is allowed for this type of file.
   */
  size: number;
  checksumSha256Base64?: string | null | undefined;
  upload: S3FileCreateMultipart;
  service?: "product_media" | undefined;
  version?: string | null | undefined;
};

/** @internal */
export const ProductMediaFileCreate$inboundSchema: z.ZodType<
  ProductMediaFileCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  organization_id: z.nullable(z.string()).optional(),
  name: z.string(),
  mime_type: z.string(),
  size: z.number().int(),
  checksum_sha256_base64: z.nullable(z.string()).optional(),
  upload: S3FileCreateMultipart$inboundSchema,
  service: z.literal("product_media").optional(),
  version: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
    "mime_type": "mimeType",
    "checksum_sha256_base64": "checksumSha256Base64",
  });
});

/** @internal */
export type ProductMediaFileCreate$Outbound = {
  organization_id?: string | null | undefined;
  name: string;
  mime_type: string;
  size: number;
  checksum_sha256_base64?: string | null | undefined;
  upload: S3FileCreateMultipart$Outbound;
  service: "product_media";
  version?: string | null | undefined;
};

/** @internal */
export const ProductMediaFileCreate$outboundSchema: z.ZodType<
  ProductMediaFileCreate$Outbound,
  z.ZodTypeDef,
  ProductMediaFileCreate
> = z.object({
  organizationId: z.nullable(z.string()).optional(),
  name: z.string(),
  mimeType: z.string(),
  size: z.number().int(),
  checksumSha256Base64: z.nullable(z.string()).optional(),
  upload: S3FileCreateMultipart$outboundSchema,
  service: z.literal("product_media").default("product_media" as const),
  version: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
    mimeType: "mime_type",
    checksumSha256Base64: "checksum_sha256_base64",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ProductMediaFileCreate$ {
  /** @deprecated use `ProductMediaFileCreate$inboundSchema` instead. */
  export const inboundSchema = ProductMediaFileCreate$inboundSchema;
  /** @deprecated use `ProductMediaFileCreate$outboundSchema` instead. */
  export const outboundSchema = ProductMediaFileCreate$outboundSchema;
  /** @deprecated use `ProductMediaFileCreate$Outbound` instead. */
  export type Outbound = ProductMediaFileCreate$Outbound;
}

export function productMediaFileCreateToJSON(
  productMediaFileCreate: ProductMediaFileCreate,
): string {
  return JSON.stringify(
    ProductMediaFileCreate$outboundSchema.parse(productMediaFileCreate),
  );
}

export function productMediaFileCreateFromJSON(
  jsonString: string,
): SafeParseResult<ProductMediaFileCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ProductMediaFileCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ProductMediaFileCreate' from JSON`,
  );
}
