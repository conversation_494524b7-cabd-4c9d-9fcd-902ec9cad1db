/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitGitHubRepositoryCreateProperties,
  BenefitGitHubRepositoryCreateProperties$inboundSchema,
  BenefitGitHubRepositoryCreateProperties$Outbound,
  BenefitGitHubRepositoryCreateProperties$outboundSchema,
} from "./benefitgithubrepositorycreateproperties.js";

export type BenefitGitHubRepositoryCreateMetadata =
  | string
  | number
  | number
  | boolean;

export type BenefitGitHubRepositoryCreate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type?: "github_repository" | undefined;
  /**
   * The description of the benefit. Will be displayed on products having this benefit.
   */
  description: string;
  /**
   * The ID of the organization owning the benefit. **Required unless you use an organization token.**
   */
  organizationId?: string | null | undefined;
  /**
   * Properties to create a benefit of type `github_repository`.
   */
  properties: BenefitGitHubRepositoryCreateProperties;
};

/** @internal */
export const BenefitGitHubRepositoryCreateMetadata$inboundSchema: z.ZodType<
  BenefitGitHubRepositoryCreateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitGitHubRepositoryCreateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitGitHubRepositoryCreateMetadata$outboundSchema: z.ZodType<
  BenefitGitHubRepositoryCreateMetadata$Outbound,
  z.ZodTypeDef,
  BenefitGitHubRepositoryCreateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGitHubRepositoryCreateMetadata$ {
  /** @deprecated use `BenefitGitHubRepositoryCreateMetadata$inboundSchema` instead. */
  export const inboundSchema =
    BenefitGitHubRepositoryCreateMetadata$inboundSchema;
  /** @deprecated use `BenefitGitHubRepositoryCreateMetadata$outboundSchema` instead. */
  export const outboundSchema =
    BenefitGitHubRepositoryCreateMetadata$outboundSchema;
  /** @deprecated use `BenefitGitHubRepositoryCreateMetadata$Outbound` instead. */
  export type Outbound = BenefitGitHubRepositoryCreateMetadata$Outbound;
}

export function benefitGitHubRepositoryCreateMetadataToJSON(
  benefitGitHubRepositoryCreateMetadata: BenefitGitHubRepositoryCreateMetadata,
): string {
  return JSON.stringify(
    BenefitGitHubRepositoryCreateMetadata$outboundSchema.parse(
      benefitGitHubRepositoryCreateMetadata,
    ),
  );
}

export function benefitGitHubRepositoryCreateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGitHubRepositoryCreateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitGitHubRepositoryCreateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGitHubRepositoryCreateMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitGitHubRepositoryCreate$inboundSchema: z.ZodType<
  BenefitGitHubRepositoryCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("github_repository").optional(),
  description: z.string(),
  organization_id: z.nullable(z.string()).optional(),
  properties: BenefitGitHubRepositoryCreateProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitGitHubRepositoryCreate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type: "github_repository";
  description: string;
  organization_id?: string | null | undefined;
  properties: BenefitGitHubRepositoryCreateProperties$Outbound;
};

/** @internal */
export const BenefitGitHubRepositoryCreate$outboundSchema: z.ZodType<
  BenefitGitHubRepositoryCreate$Outbound,
  z.ZodTypeDef,
  BenefitGitHubRepositoryCreate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("github_repository").default("github_repository" as const),
  description: z.string(),
  organizationId: z.nullable(z.string()).optional(),
  properties: BenefitGitHubRepositoryCreateProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGitHubRepositoryCreate$ {
  /** @deprecated use `BenefitGitHubRepositoryCreate$inboundSchema` instead. */
  export const inboundSchema = BenefitGitHubRepositoryCreate$inboundSchema;
  /** @deprecated use `BenefitGitHubRepositoryCreate$outboundSchema` instead. */
  export const outboundSchema = BenefitGitHubRepositoryCreate$outboundSchema;
  /** @deprecated use `BenefitGitHubRepositoryCreate$Outbound` instead. */
  export type Outbound = BenefitGitHubRepositoryCreate$Outbound;
}

export function benefitGitHubRepositoryCreateToJSON(
  benefitGitHubRepositoryCreate: BenefitGitHubRepositoryCreate,
): string {
  return JSON.stringify(
    BenefitGitHubRepositoryCreate$outboundSchema.parse(
      benefitGitHubRepositoryCreate,
    ),
  );
}

export function benefitGitHubRepositoryCreateFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGitHubRepositoryCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitGitHubRepositoryCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGitHubRepositoryCreate' from JSON`,
  );
}
