/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Properties available to subscribers for a benefit of type `meter_unit`.
 */
export type BenefitMeterCreditSubscriberProperties = {
  units: number;
  rollover: boolean;
  meterId: string;
};

/** @internal */
export const BenefitMeterCreditSubscriberProperties$inboundSchema: z.ZodType<
  BenefitMeterCreditSubscriberProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  units: z.number().int(),
  rollover: z.boolean(),
  meter_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "meter_id": "meterId",
  });
});

/** @internal */
export type BenefitMeterCreditSubscriberProperties$Outbound = {
  units: number;
  rollover: boolean;
  meter_id: string;
};

/** @internal */
export const BenefitMeterCreditSubscriberProperties$outboundSchema: z.ZodType<
  BenefitMeterCreditSubscriberProperties$Outbound,
  z.ZodTypeDef,
  BenefitMeterCreditSubscriberProperties
> = z.object({
  units: z.number().int(),
  rollover: z.boolean(),
  meterId: z.string(),
}).transform((v) => {
  return remap$(v, {
    meterId: "meter_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitMeterCreditSubscriberProperties$ {
  /** @deprecated use `BenefitMeterCreditSubscriberProperties$inboundSchema` instead. */
  export const inboundSchema =
    BenefitMeterCreditSubscriberProperties$inboundSchema;
  /** @deprecated use `BenefitMeterCreditSubscriberProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitMeterCreditSubscriberProperties$outboundSchema;
  /** @deprecated use `BenefitMeterCreditSubscriberProperties$Outbound` instead. */
  export type Outbound = BenefitMeterCreditSubscriberProperties$Outbound;
}

export function benefitMeterCreditSubscriberPropertiesToJSON(
  benefitMeterCreditSubscriberProperties:
    BenefitMeterCreditSubscriberProperties,
): string {
  return JSON.stringify(
    BenefitMeterCreditSubscriberProperties$outboundSchema.parse(
      benefitMeterCreditSubscriberProperties,
    ),
  );
}

export function benefitMeterCreditSubscriberPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<BenefitMeterCreditSubscriberProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitMeterCreditSubscriberProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitMeterCreditSubscriberProperties' from JSON`,
  );
}
