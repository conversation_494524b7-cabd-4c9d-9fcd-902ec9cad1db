/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomFieldDateProperties,
  CustomFieldDateProperties$inboundSchema,
  CustomFieldDateProperties$Outbound,
  CustomFieldDateProperties$outboundSchema,
} from "./customfielddateproperties.js";

export type CustomFieldDateMetadata = string | number | number | boolean;

/**
 * Schema for a custom field of type date.
 */
export type CustomFieldDate = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  type?: "date" | undefined;
  /**
   * Identifier of the custom field. It'll be used as key when storing the value.
   */
  slug: string;
  /**
   * Name of the custom field.
   */
  name: string;
  /**
   * The ID of the organization owning the custom field.
   */
  organizationId: string;
  properties: CustomFieldDateProperties;
};

/** @internal */
export const CustomFieldDateMetadata$inboundSchema: z.ZodType<
  CustomFieldDateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomFieldDateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomFieldDateMetadata$outboundSchema: z.ZodType<
  CustomFieldDateMetadata$Outbound,
  z.ZodTypeDef,
  CustomFieldDateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldDateMetadata$ {
  /** @deprecated use `CustomFieldDateMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomFieldDateMetadata$inboundSchema;
  /** @deprecated use `CustomFieldDateMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomFieldDateMetadata$outboundSchema;
  /** @deprecated use `CustomFieldDateMetadata$Outbound` instead. */
  export type Outbound = CustomFieldDateMetadata$Outbound;
}

export function customFieldDateMetadataToJSON(
  customFieldDateMetadata: CustomFieldDateMetadata,
): string {
  return JSON.stringify(
    CustomFieldDateMetadata$outboundSchema.parse(customFieldDateMetadata),
  );
}

export function customFieldDateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldDateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldDateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldDateMetadata' from JSON`,
  );
}

/** @internal */
export const CustomFieldDate$inboundSchema: z.ZodType<
  CustomFieldDate,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  type: z.literal("date").optional(),
  slug: z.string(),
  name: z.string(),
  organization_id: z.string(),
  properties: CustomFieldDateProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type CustomFieldDate$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  type: "date";
  slug: string;
  name: string;
  organization_id: string;
  properties: CustomFieldDateProperties$Outbound;
};

/** @internal */
export const CustomFieldDate$outboundSchema: z.ZodType<
  CustomFieldDate$Outbound,
  z.ZodTypeDef,
  CustomFieldDate
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  type: z.literal("date").default("date" as const),
  slug: z.string(),
  name: z.string(),
  organizationId: z.string(),
  properties: CustomFieldDateProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldDate$ {
  /** @deprecated use `CustomFieldDate$inboundSchema` instead. */
  export const inboundSchema = CustomFieldDate$inboundSchema;
  /** @deprecated use `CustomFieldDate$outboundSchema` instead. */
  export const outboundSchema = CustomFieldDate$outboundSchema;
  /** @deprecated use `CustomFieldDate$Outbound` instead. */
  export type Outbound = CustomFieldDate$Outbound;
}

export function customFieldDateToJSON(
  customFieldDate: CustomFieldDate,
): string {
  return JSON.stringify(CustomFieldDate$outboundSchema.parse(customFieldDate));
}

export function customFieldDateFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldDate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldDate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldDate' from JSON`,
  );
}
