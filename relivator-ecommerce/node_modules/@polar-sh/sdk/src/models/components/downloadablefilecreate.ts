/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  S3FileCreateMultipart,
  S3FileCreateMultipart$inboundSchema,
  S3FileCreateMultipart$Outbound,
  S3FileCreateMultipart$outboundSchema,
} from "./s3filecreatemultipart.js";

/**
 * Schema to create a file to be associated with the downloadables benefit.
 */
export type DownloadableFileCreate = {
  organizationId?: string | null | undefined;
  name: string;
  mimeType: string;
  size: number;
  checksumSha256Base64?: string | null | undefined;
  upload: S3FileCreateMultipart;
  service?: "downloadable" | undefined;
  version?: string | null | undefined;
};

/** @internal */
export const DownloadableFileCreate$inboundSchema: z.ZodType<
  DownloadableFileCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  organization_id: z.nullable(z.string()).optional(),
  name: z.string(),
  mime_type: z.string(),
  size: z.number().int(),
  checksum_sha256_base64: z.nullable(z.string()).optional(),
  upload: S3FileCreateMultipart$inboundSchema,
  service: z.literal("downloadable").optional(),
  version: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
    "mime_type": "mimeType",
    "checksum_sha256_base64": "checksumSha256Base64",
  });
});

/** @internal */
export type DownloadableFileCreate$Outbound = {
  organization_id?: string | null | undefined;
  name: string;
  mime_type: string;
  size: number;
  checksum_sha256_base64?: string | null | undefined;
  upload: S3FileCreateMultipart$Outbound;
  service: "downloadable";
  version?: string | null | undefined;
};

/** @internal */
export const DownloadableFileCreate$outboundSchema: z.ZodType<
  DownloadableFileCreate$Outbound,
  z.ZodTypeDef,
  DownloadableFileCreate
> = z.object({
  organizationId: z.nullable(z.string()).optional(),
  name: z.string(),
  mimeType: z.string(),
  size: z.number().int(),
  checksumSha256Base64: z.nullable(z.string()).optional(),
  upload: S3FileCreateMultipart$outboundSchema,
  service: z.literal("downloadable").default("downloadable" as const),
  version: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
    mimeType: "mime_type",
    checksumSha256Base64: "checksum_sha256_base64",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DownloadableFileCreate$ {
  /** @deprecated use `DownloadableFileCreate$inboundSchema` instead. */
  export const inboundSchema = DownloadableFileCreate$inboundSchema;
  /** @deprecated use `DownloadableFileCreate$outboundSchema` instead. */
  export const outboundSchema = DownloadableFileCreate$outboundSchema;
  /** @deprecated use `DownloadableFileCreate$Outbound` instead. */
  export type Outbound = DownloadableFileCreate$Outbound;
}

export function downloadableFileCreateToJSON(
  downloadableFileCreate: DownloadableFileCreate,
): string {
  return JSON.stringify(
    DownloadableFileCreate$outboundSchema.parse(downloadableFileCreate),
  );
}

export function downloadableFileCreateFromJSON(
  jsonString: string,
): SafeParseResult<DownloadableFileCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DownloadableFileCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DownloadableFileCreate' from JSON`,
  );
}
