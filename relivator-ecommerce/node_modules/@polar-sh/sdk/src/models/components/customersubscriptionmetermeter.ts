/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type CustomerSubscriptionMeterMeter = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * The name of the meter. Will be shown on customer's invoices and usage.
   */
  name: string;
};

/** @internal */
export const CustomerSubscriptionMeterMeter$inboundSchema: z.ZodType<
  CustomerSubscriptionMeterMeter,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  name: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
  });
});

/** @internal */
export type CustomerSubscriptionMeterMeter$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  name: string;
};

/** @internal */
export const CustomerSubscriptionMeterMeter$outboundSchema: z.ZodType<
  CustomerSubscriptionMeterMeter$Outbound,
  z.ZodTypeDef,
  CustomerSubscriptionMeterMeter
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  name: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerSubscriptionMeterMeter$ {
  /** @deprecated use `CustomerSubscriptionMeterMeter$inboundSchema` instead. */
  export const inboundSchema = CustomerSubscriptionMeterMeter$inboundSchema;
  /** @deprecated use `CustomerSubscriptionMeterMeter$outboundSchema` instead. */
  export const outboundSchema = CustomerSubscriptionMeterMeter$outboundSchema;
  /** @deprecated use `CustomerSubscriptionMeterMeter$Outbound` instead. */
  export type Outbound = CustomerSubscriptionMeterMeter$Outbound;
}

export function customerSubscriptionMeterMeterToJSON(
  customerSubscriptionMeterMeter: CustomerSubscriptionMeterMeter,
): string {
  return JSON.stringify(
    CustomerSubscriptionMeterMeter$outboundSchema.parse(
      customerSubscriptionMeterMeter,
    ),
  );
}

export function customerSubscriptionMeterMeterFromJSON(
  jsonString: string,
): SafeParseResult<CustomerSubscriptionMeterMeter, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerSubscriptionMeterMeter$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerSubscriptionMeterMeter' from JSON`,
  );
}
