/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type CheckoutLinkCreateProductsMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * Schema to create a new checkout link.
 */
export type CheckoutLinkCreateProducts = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  /**
   * Payment processor to use. Currently only Stripe is supported.
   */
  paymentProcessor?: "stripe" | undefined;
  /**
   * Optional label to distinguish links internally
   */
  label?: string | null | undefined;
  /**
   * Whether to allow the customer to apply discount codes. If you apply a discount through `discount_id`, it'll still be applied, but the customer won't be able to change it.
   */
  allowDiscountCodes?: boolean | undefined;
  /**
   * Whether to require the customer to fill their full billing address, instead of just the country. Customers in the US will always be required to fill their full address, regardless of this setting.
   */
  requireBillingAddress?: boolean | undefined;
  /**
   * ID of the discount to apply to the checkout. If the discount is not applicable anymore when opening the checkout link, it'll be ignored.
   */
  discountId?: string | null | undefined;
  /**
   * URL where the customer will be redirected after a successful payment.You can add the `checkout_id={CHECKOUT_ID}` query parameter to retrieve the checkout session id.
   */
  successUrl?: string | null | undefined;
  /**
   * List of products that will be available to select at checkout.
   */
  products: Array<string>;
};

/** @internal */
export const CheckoutLinkCreateProductsMetadata$inboundSchema: z.ZodType<
  CheckoutLinkCreateProductsMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CheckoutLinkCreateProductsMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CheckoutLinkCreateProductsMetadata$outboundSchema: z.ZodType<
  CheckoutLinkCreateProductsMetadata$Outbound,
  z.ZodTypeDef,
  CheckoutLinkCreateProductsMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutLinkCreateProductsMetadata$ {
  /** @deprecated use `CheckoutLinkCreateProductsMetadata$inboundSchema` instead. */
  export const inboundSchema = CheckoutLinkCreateProductsMetadata$inboundSchema;
  /** @deprecated use `CheckoutLinkCreateProductsMetadata$outboundSchema` instead. */
  export const outboundSchema =
    CheckoutLinkCreateProductsMetadata$outboundSchema;
  /** @deprecated use `CheckoutLinkCreateProductsMetadata$Outbound` instead. */
  export type Outbound = CheckoutLinkCreateProductsMetadata$Outbound;
}

export function checkoutLinkCreateProductsMetadataToJSON(
  checkoutLinkCreateProductsMetadata: CheckoutLinkCreateProductsMetadata,
): string {
  return JSON.stringify(
    CheckoutLinkCreateProductsMetadata$outboundSchema.parse(
      checkoutLinkCreateProductsMetadata,
    ),
  );
}

export function checkoutLinkCreateProductsMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutLinkCreateProductsMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      CheckoutLinkCreateProductsMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutLinkCreateProductsMetadata' from JSON`,
  );
}

/** @internal */
export const CheckoutLinkCreateProducts$inboundSchema: z.ZodType<
  CheckoutLinkCreateProducts,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  payment_processor: z.literal("stripe").optional(),
  label: z.nullable(z.string()).optional(),
  allow_discount_codes: z.boolean().default(true),
  require_billing_address: z.boolean().default(false),
  discount_id: z.nullable(z.string()).optional(),
  success_url: z.nullable(z.string()).optional(),
  products: z.array(z.string()),
}).transform((v) => {
  return remap$(v, {
    "payment_processor": "paymentProcessor",
    "allow_discount_codes": "allowDiscountCodes",
    "require_billing_address": "requireBillingAddress",
    "discount_id": "discountId",
    "success_url": "successUrl",
  });
});

/** @internal */
export type CheckoutLinkCreateProducts$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  payment_processor: "stripe";
  label?: string | null | undefined;
  allow_discount_codes: boolean;
  require_billing_address: boolean;
  discount_id?: string | null | undefined;
  success_url?: string | null | undefined;
  products: Array<string>;
};

/** @internal */
export const CheckoutLinkCreateProducts$outboundSchema: z.ZodType<
  CheckoutLinkCreateProducts$Outbound,
  z.ZodTypeDef,
  CheckoutLinkCreateProducts
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  paymentProcessor: z.literal("stripe").default("stripe" as const),
  label: z.nullable(z.string()).optional(),
  allowDiscountCodes: z.boolean().default(true),
  requireBillingAddress: z.boolean().default(false),
  discountId: z.nullable(z.string()).optional(),
  successUrl: z.nullable(z.string()).optional(),
  products: z.array(z.string()),
}).transform((v) => {
  return remap$(v, {
    paymentProcessor: "payment_processor",
    allowDiscountCodes: "allow_discount_codes",
    requireBillingAddress: "require_billing_address",
    discountId: "discount_id",
    successUrl: "success_url",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutLinkCreateProducts$ {
  /** @deprecated use `CheckoutLinkCreateProducts$inboundSchema` instead. */
  export const inboundSchema = CheckoutLinkCreateProducts$inboundSchema;
  /** @deprecated use `CheckoutLinkCreateProducts$outboundSchema` instead. */
  export const outboundSchema = CheckoutLinkCreateProducts$outboundSchema;
  /** @deprecated use `CheckoutLinkCreateProducts$Outbound` instead. */
  export type Outbound = CheckoutLinkCreateProducts$Outbound;
}

export function checkoutLinkCreateProductsToJSON(
  checkoutLinkCreateProducts: CheckoutLinkCreateProducts,
): string {
  return JSON.stringify(
    CheckoutLinkCreateProducts$outboundSchema.parse(checkoutLinkCreateProducts),
  );
}

export function checkoutLinkCreateProductsFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutLinkCreateProducts, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutLinkCreateProducts$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutLinkCreateProducts' from JSON`,
  );
}
