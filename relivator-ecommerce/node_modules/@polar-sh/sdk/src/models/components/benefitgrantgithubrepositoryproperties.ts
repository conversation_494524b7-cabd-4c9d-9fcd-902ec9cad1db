/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const BenefitGrantGitHubRepositoryPropertiesPermission = {
  Pull: "pull",
  Triage: "triage",
  Push: "push",
  Maintain: "maintain",
  Admin: "admin",
} as const;
export type BenefitGrantGitHubRepositoryPropertiesPermission = ClosedEnum<
  typeof BenefitGrantGitHubRepositoryPropertiesPermission
>;

export type BenefitGrantGitHubRepositoryProperties = {
  accountId?: string | undefined;
  repositoryOwner?: string | undefined;
  repositoryName?: string | undefined;
  permission?: BenefitGrantGitHubRepositoryPropertiesPermission | undefined;
};

/** @internal */
export const BenefitGrantGitHubRepositoryPropertiesPermission$inboundSchema:
  z.ZodNativeEnum<typeof BenefitGrantGitHubRepositoryPropertiesPermission> = z
    .nativeEnum(BenefitGrantGitHubRepositoryPropertiesPermission);

/** @internal */
export const BenefitGrantGitHubRepositoryPropertiesPermission$outboundSchema:
  z.ZodNativeEnum<typeof BenefitGrantGitHubRepositoryPropertiesPermission> =
    BenefitGrantGitHubRepositoryPropertiesPermission$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGrantGitHubRepositoryPropertiesPermission$ {
  /** @deprecated use `BenefitGrantGitHubRepositoryPropertiesPermission$inboundSchema` instead. */
  export const inboundSchema =
    BenefitGrantGitHubRepositoryPropertiesPermission$inboundSchema;
  /** @deprecated use `BenefitGrantGitHubRepositoryPropertiesPermission$outboundSchema` instead. */
  export const outboundSchema =
    BenefitGrantGitHubRepositoryPropertiesPermission$outboundSchema;
}

/** @internal */
export const BenefitGrantGitHubRepositoryProperties$inboundSchema: z.ZodType<
  BenefitGrantGitHubRepositoryProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  account_id: z.string().optional(),
  repository_owner: z.string().optional(),
  repository_name: z.string().optional(),
  permission: BenefitGrantGitHubRepositoryPropertiesPermission$inboundSchema
    .optional(),
}).transform((v) => {
  return remap$(v, {
    "account_id": "accountId",
    "repository_owner": "repositoryOwner",
    "repository_name": "repositoryName",
  });
});

/** @internal */
export type BenefitGrantGitHubRepositoryProperties$Outbound = {
  account_id?: string | undefined;
  repository_owner?: string | undefined;
  repository_name?: string | undefined;
  permission?: string | undefined;
};

/** @internal */
export const BenefitGrantGitHubRepositoryProperties$outboundSchema: z.ZodType<
  BenefitGrantGitHubRepositoryProperties$Outbound,
  z.ZodTypeDef,
  BenefitGrantGitHubRepositoryProperties
> = z.object({
  accountId: z.string().optional(),
  repositoryOwner: z.string().optional(),
  repositoryName: z.string().optional(),
  permission: BenefitGrantGitHubRepositoryPropertiesPermission$outboundSchema
    .optional(),
}).transform((v) => {
  return remap$(v, {
    accountId: "account_id",
    repositoryOwner: "repository_owner",
    repositoryName: "repository_name",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGrantGitHubRepositoryProperties$ {
  /** @deprecated use `BenefitGrantGitHubRepositoryProperties$inboundSchema` instead. */
  export const inboundSchema =
    BenefitGrantGitHubRepositoryProperties$inboundSchema;
  /** @deprecated use `BenefitGrantGitHubRepositoryProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitGrantGitHubRepositoryProperties$outboundSchema;
  /** @deprecated use `BenefitGrantGitHubRepositoryProperties$Outbound` instead. */
  export type Outbound = BenefitGrantGitHubRepositoryProperties$Outbound;
}

export function benefitGrantGitHubRepositoryPropertiesToJSON(
  benefitGrantGitHubRepositoryProperties:
    BenefitGrantGitHubRepositoryProperties,
): string {
  return JSON.stringify(
    BenefitGrantGitHubRepositoryProperties$outboundSchema.parse(
      benefitGrantGitHubRepositoryProperties,
    ),
  );
}

export function benefitGrantGitHubRepositoryPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGrantGitHubRepositoryProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitGrantGitHubRepositoryProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGrantGitHubRepositoryProperties' from JSON`,
  );
}
