/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  SubscriptionRecurringInterval,
  SubscriptionRecurringInterval$inboundSchema,
  SubscriptionRecurringInterval$outboundSchema,
} from "./subscriptionrecurringinterval.js";

export type DiscountProductMetadata = string | number | number | boolean;

/**
 * A product that a discount can be applied to.
 */
export type DiscountProduct = {
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the product.
   */
  id: string;
  /**
   * The name of the product.
   */
  name: string;
  /**
   * The description of the product.
   */
  description: string | null;
  /**
   * The recurring interval of the product. If `None`, the product is a one-time purchase.
   */
  recurringInterval: SubscriptionRecurringInterval | null;
  /**
   * Whether the product is a subscription.
   */
  isRecurring: boolean;
  /**
   * Whether the product is archived and no longer available.
   */
  isArchived: boolean;
  /**
   * The ID of the organization owning the product.
   */
  organizationId: string;
};

/** @internal */
export const DiscountProductMetadata$inboundSchema: z.ZodType<
  DiscountProductMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type DiscountProductMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const DiscountProductMetadata$outboundSchema: z.ZodType<
  DiscountProductMetadata$Outbound,
  z.ZodTypeDef,
  DiscountProductMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountProductMetadata$ {
  /** @deprecated use `DiscountProductMetadata$inboundSchema` instead. */
  export const inboundSchema = DiscountProductMetadata$inboundSchema;
  /** @deprecated use `DiscountProductMetadata$outboundSchema` instead. */
  export const outboundSchema = DiscountProductMetadata$outboundSchema;
  /** @deprecated use `DiscountProductMetadata$Outbound` instead. */
  export type Outbound = DiscountProductMetadata$Outbound;
}

export function discountProductMetadataToJSON(
  discountProductMetadata: DiscountProductMetadata,
): string {
  return JSON.stringify(
    DiscountProductMetadata$outboundSchema.parse(discountProductMetadata),
  );
}

export function discountProductMetadataFromJSON(
  jsonString: string,
): SafeParseResult<DiscountProductMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DiscountProductMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DiscountProductMetadata' from JSON`,
  );
}

/** @internal */
export const DiscountProduct$inboundSchema: z.ZodType<
  DiscountProduct,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  name: z.string(),
  description: z.nullable(z.string()),
  recurring_interval: z.nullable(SubscriptionRecurringInterval$inboundSchema),
  is_recurring: z.boolean(),
  is_archived: z.boolean(),
  organization_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "recurring_interval": "recurringInterval",
    "is_recurring": "isRecurring",
    "is_archived": "isArchived",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type DiscountProduct$Outbound = {
  metadata: { [k: string]: string | number | number | boolean };
  created_at: string;
  modified_at: string | null;
  id: string;
  name: string;
  description: string | null;
  recurring_interval: string | null;
  is_recurring: boolean;
  is_archived: boolean;
  organization_id: string;
};

/** @internal */
export const DiscountProduct$outboundSchema: z.ZodType<
  DiscountProduct$Outbound,
  z.ZodTypeDef,
  DiscountProduct
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  name: z.string(),
  description: z.nullable(z.string()),
  recurringInterval: z.nullable(SubscriptionRecurringInterval$outboundSchema),
  isRecurring: z.boolean(),
  isArchived: z.boolean(),
  organizationId: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    recurringInterval: "recurring_interval",
    isRecurring: "is_recurring",
    isArchived: "is_archived",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountProduct$ {
  /** @deprecated use `DiscountProduct$inboundSchema` instead. */
  export const inboundSchema = DiscountProduct$inboundSchema;
  /** @deprecated use `DiscountProduct$outboundSchema` instead. */
  export const outboundSchema = DiscountProduct$outboundSchema;
  /** @deprecated use `DiscountProduct$Outbound` instead. */
  export type Outbound = DiscountProduct$Outbound;
}

export function discountProductToJSON(
  discountProduct: DiscountProduct,
): string {
  return JSON.stringify(DiscountProduct$outboundSchema.parse(discountProduct));
}

export function discountProductFromJSON(
  jsonString: string,
): SafeParseResult<DiscountProduct, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DiscountProduct$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DiscountProduct' from JSON`,
  );
}
