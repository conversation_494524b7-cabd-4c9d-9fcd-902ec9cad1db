/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  AttachedCustomField,
  AttachedCustomField$inboundSchema,
  AttachedCustomField$Outbound,
  AttachedCustomField$outboundSchema,
} from "./attachedcustomfield.js";
import {
  Benefit,
  Benefit$inboundSchema,
  Benefit$Outbound,
  Benefit$outboundSchema,
} from "./benefit.js";
import {
  LegacyRecurringProductPrice,
  LegacyRecurringProductPrice$inboundSchema,
  LegacyRecurringProductPrice$Outbound,
  LegacyRecurringProductPrice$outboundSchema,
} from "./legacyrecurringproductprice.js";
import {
  ProductMediaFileRead,
  ProductMediaFileRead$inboundSchema,
  ProductMediaFileRead$Outbound,
  ProductMediaFileRead$outboundSchema,
} from "./productmediafileread.js";
import {
  ProductPrice,
  ProductPrice$inboundSchema,
  ProductPrice$Outbound,
  ProductPrice$outboundSchema,
} from "./productprice.js";
import {
  SubscriptionRecurringInterval,
  SubscriptionRecurringInterval$inboundSchema,
  SubscriptionRecurringInterval$outboundSchema,
} from "./subscriptionrecurringinterval.js";

export type ProductMetadata = string | number | number | boolean;

export type Prices = LegacyRecurringProductPrice | ProductPrice;

/**
 * A product.
 */
export type Product = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the product.
   */
  id: string;
  /**
   * The name of the product.
   */
  name: string;
  /**
   * The description of the product.
   */
  description: string | null;
  /**
   * The recurring interval of the product. If `None`, the product is a one-time purchase.
   */
  recurringInterval: SubscriptionRecurringInterval | null;
  /**
   * Whether the product is a subscription.
   */
  isRecurring: boolean;
  /**
   * Whether the product is archived and no longer available.
   */
  isArchived: boolean;
  /**
   * The ID of the organization owning the product.
   */
  organizationId: string;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * List of prices for this product.
   */
  prices: Array<LegacyRecurringProductPrice | ProductPrice>;
  /**
   * List of benefits granted by the product.
   */
  benefits: Array<Benefit>;
  /**
   * List of medias associated to the product.
   */
  medias: Array<ProductMediaFileRead>;
  /**
   * List of custom fields attached to the product.
   */
  attachedCustomFields: Array<AttachedCustomField>;
};

/** @internal */
export const ProductMetadata$inboundSchema: z.ZodType<
  ProductMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type ProductMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const ProductMetadata$outboundSchema: z.ZodType<
  ProductMetadata$Outbound,
  z.ZodTypeDef,
  ProductMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ProductMetadata$ {
  /** @deprecated use `ProductMetadata$inboundSchema` instead. */
  export const inboundSchema = ProductMetadata$inboundSchema;
  /** @deprecated use `ProductMetadata$outboundSchema` instead. */
  export const outboundSchema = ProductMetadata$outboundSchema;
  /** @deprecated use `ProductMetadata$Outbound` instead. */
  export type Outbound = ProductMetadata$Outbound;
}

export function productMetadataToJSON(
  productMetadata: ProductMetadata,
): string {
  return JSON.stringify(ProductMetadata$outboundSchema.parse(productMetadata));
}

export function productMetadataFromJSON(
  jsonString: string,
): SafeParseResult<ProductMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ProductMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ProductMetadata' from JSON`,
  );
}

/** @internal */
export const Prices$inboundSchema: z.ZodType<Prices, z.ZodTypeDef, unknown> = z
  .union([
    LegacyRecurringProductPrice$inboundSchema,
    ProductPrice$inboundSchema,
  ]);

/** @internal */
export type Prices$Outbound =
  | LegacyRecurringProductPrice$Outbound
  | ProductPrice$Outbound;

/** @internal */
export const Prices$outboundSchema: z.ZodType<
  Prices$Outbound,
  z.ZodTypeDef,
  Prices
> = z.union([
  LegacyRecurringProductPrice$outboundSchema,
  ProductPrice$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Prices$ {
  /** @deprecated use `Prices$inboundSchema` instead. */
  export const inboundSchema = Prices$inboundSchema;
  /** @deprecated use `Prices$outboundSchema` instead. */
  export const outboundSchema = Prices$outboundSchema;
  /** @deprecated use `Prices$Outbound` instead. */
  export type Outbound = Prices$Outbound;
}

export function pricesToJSON(prices: Prices): string {
  return JSON.stringify(Prices$outboundSchema.parse(prices));
}

export function pricesFromJSON(
  jsonString: string,
): SafeParseResult<Prices, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Prices$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Prices' from JSON`,
  );
}

/** @internal */
export const Product$inboundSchema: z.ZodType<Product, z.ZodTypeDef, unknown> =
  z.object({
    created_at: z.string().datetime({ offset: true }).transform(v =>
      new Date(v)
    ),
    modified_at: z.nullable(
      z.string().datetime({ offset: true }).transform(v => new Date(v)),
    ),
    id: z.string(),
    name: z.string(),
    description: z.nullable(z.string()),
    recurring_interval: z.nullable(SubscriptionRecurringInterval$inboundSchema),
    is_recurring: z.boolean(),
    is_archived: z.boolean(),
    organization_id: z.string(),
    metadata: z.record(
      z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
    ),
    prices: z.array(
      z.union([
        LegacyRecurringProductPrice$inboundSchema,
        ProductPrice$inboundSchema,
      ]),
    ),
    benefits: z.array(Benefit$inboundSchema),
    medias: z.array(ProductMediaFileRead$inboundSchema),
    attached_custom_fields: z.array(AttachedCustomField$inboundSchema),
  }).transform((v) => {
    return remap$(v, {
      "created_at": "createdAt",
      "modified_at": "modifiedAt",
      "recurring_interval": "recurringInterval",
      "is_recurring": "isRecurring",
      "is_archived": "isArchived",
      "organization_id": "organizationId",
      "attached_custom_fields": "attachedCustomFields",
    });
  });

/** @internal */
export type Product$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  name: string;
  description: string | null;
  recurring_interval: string | null;
  is_recurring: boolean;
  is_archived: boolean;
  organization_id: string;
  metadata: { [k: string]: string | number | number | boolean };
  prices: Array<LegacyRecurringProductPrice$Outbound | ProductPrice$Outbound>;
  benefits: Array<Benefit$Outbound>;
  medias: Array<ProductMediaFileRead$Outbound>;
  attached_custom_fields: Array<AttachedCustomField$Outbound>;
};

/** @internal */
export const Product$outboundSchema: z.ZodType<
  Product$Outbound,
  z.ZodTypeDef,
  Product
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  name: z.string(),
  description: z.nullable(z.string()),
  recurringInterval: z.nullable(SubscriptionRecurringInterval$outboundSchema),
  isRecurring: z.boolean(),
  isArchived: z.boolean(),
  organizationId: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  prices: z.array(
    z.union([
      LegacyRecurringProductPrice$outboundSchema,
      ProductPrice$outboundSchema,
    ]),
  ),
  benefits: z.array(Benefit$outboundSchema),
  medias: z.array(ProductMediaFileRead$outboundSchema),
  attachedCustomFields: z.array(AttachedCustomField$outboundSchema),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    recurringInterval: "recurring_interval",
    isRecurring: "is_recurring",
    isArchived: "is_archived",
    organizationId: "organization_id",
    attachedCustomFields: "attached_custom_fields",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Product$ {
  /** @deprecated use `Product$inboundSchema` instead. */
  export const inboundSchema = Product$inboundSchema;
  /** @deprecated use `Product$outboundSchema` instead. */
  export const outboundSchema = Product$outboundSchema;
  /** @deprecated use `Product$Outbound` instead. */
  export type Outbound = Product$Outbound;
}

export function productToJSON(product: Product): string {
  return JSON.stringify(Product$outboundSchema.parse(product));
}

export function productFromJSON(
  jsonString: string,
): SafeParseResult<Product, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Product$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Product' from JSON`,
  );
}
