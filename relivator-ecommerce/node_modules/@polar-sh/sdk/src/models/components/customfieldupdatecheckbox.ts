/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomFieldCheckboxProperties,
  CustomFieldCheckboxProperties$inboundSchema,
  CustomFieldCheckboxProperties$Outbound,
  CustomFieldCheckboxProperties$outboundSchema,
} from "./customfieldcheckboxproperties.js";

export type CustomFieldUpdateCheckboxMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * Schema to update a custom field of type checkbox.
 */
export type CustomFieldUpdateCheckbox = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  name?: string | null | undefined;
  slug?: string | null | undefined;
  type?: "checkbox" | undefined;
  properties?: CustomFieldCheckboxProperties | null | undefined;
};

/** @internal */
export const CustomFieldUpdateCheckboxMetadata$inboundSchema: z.ZodType<
  CustomFieldUpdateCheckboxMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomFieldUpdateCheckboxMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomFieldUpdateCheckboxMetadata$outboundSchema: z.ZodType<
  CustomFieldUpdateCheckboxMetadata$Outbound,
  z.ZodTypeDef,
  CustomFieldUpdateCheckboxMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldUpdateCheckboxMetadata$ {
  /** @deprecated use `CustomFieldUpdateCheckboxMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomFieldUpdateCheckboxMetadata$inboundSchema;
  /** @deprecated use `CustomFieldUpdateCheckboxMetadata$outboundSchema` instead. */
  export const outboundSchema =
    CustomFieldUpdateCheckboxMetadata$outboundSchema;
  /** @deprecated use `CustomFieldUpdateCheckboxMetadata$Outbound` instead. */
  export type Outbound = CustomFieldUpdateCheckboxMetadata$Outbound;
}

export function customFieldUpdateCheckboxMetadataToJSON(
  customFieldUpdateCheckboxMetadata: CustomFieldUpdateCheckboxMetadata,
): string {
  return JSON.stringify(
    CustomFieldUpdateCheckboxMetadata$outboundSchema.parse(
      customFieldUpdateCheckboxMetadata,
    ),
  );
}

export function customFieldUpdateCheckboxMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldUpdateCheckboxMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldUpdateCheckboxMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldUpdateCheckboxMetadata' from JSON`,
  );
}

/** @internal */
export const CustomFieldUpdateCheckbox$inboundSchema: z.ZodType<
  CustomFieldUpdateCheckbox,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.nullable(z.string()).optional(),
  slug: z.nullable(z.string()).optional(),
  type: z.literal("checkbox").optional(),
  properties: z.nullable(CustomFieldCheckboxProperties$inboundSchema)
    .optional(),
});

/** @internal */
export type CustomFieldUpdateCheckbox$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  name?: string | null | undefined;
  slug?: string | null | undefined;
  type: "checkbox";
  properties?: CustomFieldCheckboxProperties$Outbound | null | undefined;
};

/** @internal */
export const CustomFieldUpdateCheckbox$outboundSchema: z.ZodType<
  CustomFieldUpdateCheckbox$Outbound,
  z.ZodTypeDef,
  CustomFieldUpdateCheckbox
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.nullable(z.string()).optional(),
  slug: z.nullable(z.string()).optional(),
  type: z.literal("checkbox").default("checkbox" as const),
  properties: z.nullable(CustomFieldCheckboxProperties$outboundSchema)
    .optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldUpdateCheckbox$ {
  /** @deprecated use `CustomFieldUpdateCheckbox$inboundSchema` instead. */
  export const inboundSchema = CustomFieldUpdateCheckbox$inboundSchema;
  /** @deprecated use `CustomFieldUpdateCheckbox$outboundSchema` instead. */
  export const outboundSchema = CustomFieldUpdateCheckbox$outboundSchema;
  /** @deprecated use `CustomFieldUpdateCheckbox$Outbound` instead. */
  export type Outbound = CustomFieldUpdateCheckbox$Outbound;
}

export function customFieldUpdateCheckboxToJSON(
  customFieldUpdateCheckbox: CustomFieldUpdateCheckbox,
): string {
  return JSON.stringify(
    CustomFieldUpdateCheckbox$outboundSchema.parse(customFieldUpdateCheckbox),
  );
}

export function customFieldUpdateCheckboxFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldUpdateCheckbox, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldUpdateCheckbox$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldUpdateCheckbox' from JSON`,
  );
}
