/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomerOrder,
  CustomerOrder$inboundSchema,
  CustomerOrder$Outbound,
  CustomerOrder$outboundSchema,
} from "./customerorder.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";

export type ListResourceCustomerOrder = {
  items: Array<CustomerOrder>;
  pagination: Pagination;
};

/** @internal */
export const ListResourceCustomerOrder$inboundSchema: z.ZodType<
  ListResourceCustomerOrder,
  z.ZodTypeDef,
  unknown
> = z.object({
  items: z.array(CustomerOrder$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ListResourceCustomerOrder$Outbound = {
  items: Array<CustomerOrder$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResourceCustomerOrder$outboundSchema: z.ZodType<
  ListResourceCustomerOrder$Outbound,
  z.ZodTypeDef,
  ListResourceCustomerOrder
> = z.object({
  items: z.array(CustomerOrder$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResourceCustomerOrder$ {
  /** @deprecated use `ListResourceCustomerOrder$inboundSchema` instead. */
  export const inboundSchema = ListResourceCustomerOrder$inboundSchema;
  /** @deprecated use `ListResourceCustomerOrder$outboundSchema` instead. */
  export const outboundSchema = ListResourceCustomerOrder$outboundSchema;
  /** @deprecated use `ListResourceCustomerOrder$Outbound` instead. */
  export type Outbound = ListResourceCustomerOrder$Outbound;
}

export function listResourceCustomerOrderToJSON(
  listResourceCustomerOrder: ListResourceCustomerOrder,
): string {
  return JSON.stringify(
    ListResourceCustomerOrder$outboundSchema.parse(listResourceCustomerOrder),
  );
}

export function listResourceCustomerOrderFromJSON(
  jsonString: string,
): SafeParseResult<ListResourceCustomerOrder, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListResourceCustomerOrder$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListResourceCustomerOrder' from JSON`,
  );
}
