/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type MeterCreditedMetadata = {
  meterId: string;
  units: number;
  rollover: boolean;
};

/** @internal */
export const MeterCreditedMetadata$inboundSchema: z.ZodType<
  MeterCreditedMetadata,
  z.ZodTypeDef,
  unknown
> = z.object({
  meter_id: z.string(),
  units: z.number().int(),
  rollover: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    "meter_id": "meterId",
  });
});

/** @internal */
export type MeterCreditedMetadata$Outbound = {
  meter_id: string;
  units: number;
  rollover: boolean;
};

/** @internal */
export const MeterCreditedMetadata$outboundSchema: z.ZodType<
  MeterCreditedMetadata$Outbound,
  z.ZodTypeDef,
  MeterCreditedMetadata
> = z.object({
  meterId: z.string(),
  units: z.number().int(),
  rollover: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    meterId: "meter_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterCreditedMetadata$ {
  /** @deprecated use `MeterCreditedMetadata$inboundSchema` instead. */
  export const inboundSchema = MeterCreditedMetadata$inboundSchema;
  /** @deprecated use `MeterCreditedMetadata$outboundSchema` instead. */
  export const outboundSchema = MeterCreditedMetadata$outboundSchema;
  /** @deprecated use `MeterCreditedMetadata$Outbound` instead. */
  export type Outbound = MeterCreditedMetadata$Outbound;
}

export function meterCreditedMetadataToJSON(
  meterCreditedMetadata: MeterCreditedMetadata,
): string {
  return JSON.stringify(
    MeterCreditedMetadata$outboundSchema.parse(meterCreditedMetadata),
  );
}

export function meterCreditedMetadataFromJSON(
  jsonString: string,
): SafeParseResult<MeterCreditedMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterCreditedMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterCreditedMetadata' from JSON`,
  );
}
