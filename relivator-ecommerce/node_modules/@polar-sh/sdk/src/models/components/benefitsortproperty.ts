/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const BenefitSortProperty = {
  CreatedAt: "created_at",
  MinusCreatedAt: "-created_at",
  Description: "description",
  MinusDescription: "-description",
} as const;
export type BenefitSortProperty = ClosedEnum<typeof BenefitSortProperty>;

/** @internal */
export const BenefitSortProperty$inboundSchema: z.ZodNativeEnum<
  typeof BenefitSortProperty
> = z.nativeEnum(BenefitSortProperty);

/** @internal */
export const BenefitSortProperty$outboundSchema: z.ZodNativeEnum<
  typeof BenefitSortProperty
> = BenefitSortProperty$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitSortProperty$ {
  /** @deprecated use `BenefitSortProperty$inboundSchema` instead. */
  export const inboundSchema = BenefitSortProperty$inboundSchema;
  /** @deprecated use `BenefitSortProperty$outboundSchema` instead. */
  export const outboundSchema = BenefitSortProperty$outboundSchema;
}
