/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomFieldSelectOption,
  CustomFieldSelectOption$inboundSchema,
  CustomFieldSelectOption$Outbound,
  CustomFieldSelectOption$outboundSchema,
} from "./customfieldselectoption.js";

export type CustomFieldSelectProperties = {
  formLabel?: string | undefined;
  formHelpText?: string | undefined;
  formPlaceholder?: string | undefined;
  options: Array<CustomFieldSelectOption>;
};

/** @internal */
export const CustomFieldSelectProperties$inboundSchema: z.ZodType<
  CustomFieldSelectProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  form_label: z.string().optional(),
  form_help_text: z.string().optional(),
  form_placeholder: z.string().optional(),
  options: z.array(CustomFieldSelectOption$inboundSchema),
}).transform((v) => {
  return remap$(v, {
    "form_label": "formLabel",
    "form_help_text": "formHelpText",
    "form_placeholder": "formPlaceholder",
  });
});

/** @internal */
export type CustomFieldSelectProperties$Outbound = {
  form_label?: string | undefined;
  form_help_text?: string | undefined;
  form_placeholder?: string | undefined;
  options: Array<CustomFieldSelectOption$Outbound>;
};

/** @internal */
export const CustomFieldSelectProperties$outboundSchema: z.ZodType<
  CustomFieldSelectProperties$Outbound,
  z.ZodTypeDef,
  CustomFieldSelectProperties
> = z.object({
  formLabel: z.string().optional(),
  formHelpText: z.string().optional(),
  formPlaceholder: z.string().optional(),
  options: z.array(CustomFieldSelectOption$outboundSchema),
}).transform((v) => {
  return remap$(v, {
    formLabel: "form_label",
    formHelpText: "form_help_text",
    formPlaceholder: "form_placeholder",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldSelectProperties$ {
  /** @deprecated use `CustomFieldSelectProperties$inboundSchema` instead. */
  export const inboundSchema = CustomFieldSelectProperties$inboundSchema;
  /** @deprecated use `CustomFieldSelectProperties$outboundSchema` instead. */
  export const outboundSchema = CustomFieldSelectProperties$outboundSchema;
  /** @deprecated use `CustomFieldSelectProperties$Outbound` instead. */
  export type Outbound = CustomFieldSelectProperties$Outbound;
}

export function customFieldSelectPropertiesToJSON(
  customFieldSelectProperties: CustomFieldSelectProperties,
): string {
  return JSON.stringify(
    CustomFieldSelectProperties$outboundSchema.parse(
      customFieldSelectProperties,
    ),
  );
}

export function customFieldSelectPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldSelectProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldSelectProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldSelectProperties' from JSON`,
  );
}
