/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomerCustomerMeter,
  CustomerCustomerMeter$inboundSchema,
  CustomerCustomerMeter$Outbound,
  CustomerCustomerMeter$outboundSchema,
} from "./customercustomermeter.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";

export type ListResourceCustomerCustomerMeter = {
  items: Array<CustomerCustomerMeter>;
  pagination: Pagination;
};

/** @internal */
export const ListResourceCustomerCustomerMeter$inboundSchema: z.ZodType<
  ListResourceCustomerCustomerMeter,
  z.ZodTypeDef,
  unknown
> = z.object({
  items: z.array(CustomerCustomerMeter$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ListResourceCustomerCustomerMeter$Outbound = {
  items: Array<CustomerCustomerMeter$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResourceCustomerCustomerMeter$outboundSchema: z.ZodType<
  ListResourceCustomerCustomerMeter$Outbound,
  z.ZodTypeDef,
  ListResourceCustomerCustomerMeter
> = z.object({
  items: z.array(CustomerCustomerMeter$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResourceCustomerCustomerMeter$ {
  /** @deprecated use `ListResourceCustomerCustomerMeter$inboundSchema` instead. */
  export const inboundSchema = ListResourceCustomerCustomerMeter$inboundSchema;
  /** @deprecated use `ListResourceCustomerCustomerMeter$outboundSchema` instead. */
  export const outboundSchema =
    ListResourceCustomerCustomerMeter$outboundSchema;
  /** @deprecated use `ListResourceCustomerCustomerMeter$Outbound` instead. */
  export type Outbound = ListResourceCustomerCustomerMeter$Outbound;
}

export function listResourceCustomerCustomerMeterToJSON(
  listResourceCustomerCustomerMeter: ListResourceCustomerCustomerMeter,
): string {
  return JSON.stringify(
    ListResourceCustomerCustomerMeter$outboundSchema.parse(
      listResourceCustomerCustomerMeter,
    ),
  );
}

export function listResourceCustomerCustomerMeterFromJSON(
  jsonString: string,
): SafeParseResult<ListResourceCustomerCustomerMeter, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListResourceCustomerCustomerMeter$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListResourceCustomerCustomerMeter' from JSON`,
  );
}
