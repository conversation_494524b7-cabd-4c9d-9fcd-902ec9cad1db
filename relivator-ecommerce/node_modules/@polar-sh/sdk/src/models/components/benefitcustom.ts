/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitCustomProperties,
  BenefitCustomProperties$inboundSchema,
  BenefitCustomProperties$Outbound,
  BenefitCustomProperties$outboundSchema,
} from "./benefitcustomproperties.js";

export type BenefitCustomMetadata = string | number | number | boolean;

/**
 * A benefit of type `custom`.
 *
 * @remarks
 *
 * Use it to grant any kind of benefit that doesn't fit in the other types.
 */
export type BenefitCustom = {
  /**
   * The ID of the benefit.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  type?: "custom" | undefined;
  /**
   * The description of the benefit.
   */
  description: string;
  /**
   * Whether the benefit is selectable when creating a product.
   */
  selectable: boolean;
  /**
   * Whether the benefit is deletable.
   */
  deletable: boolean;
  /**
   * The ID of the organization owning the benefit.
   */
  organizationId: string;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Properties for a benefit of type `custom`.
   */
  properties: BenefitCustomProperties;
};

/** @internal */
export const BenefitCustomMetadata$inboundSchema: z.ZodType<
  BenefitCustomMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitCustomMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const BenefitCustomMetadata$outboundSchema: z.ZodType<
  BenefitCustomMetadata$Outbound,
  z.ZodTypeDef,
  BenefitCustomMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitCustomMetadata$ {
  /** @deprecated use `BenefitCustomMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitCustomMetadata$inboundSchema;
  /** @deprecated use `BenefitCustomMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitCustomMetadata$outboundSchema;
  /** @deprecated use `BenefitCustomMetadata$Outbound` instead. */
  export type Outbound = BenefitCustomMetadata$Outbound;
}

export function benefitCustomMetadataToJSON(
  benefitCustomMetadata: BenefitCustomMetadata,
): string {
  return JSON.stringify(
    BenefitCustomMetadata$outboundSchema.parse(benefitCustomMetadata),
  );
}

export function benefitCustomMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitCustomMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitCustomMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitCustomMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitCustom$inboundSchema: z.ZodType<
  BenefitCustom,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  type: z.literal("custom").optional(),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organization_id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  properties: BenefitCustomProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitCustom$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  type: "custom";
  description: string;
  selectable: boolean;
  deletable: boolean;
  organization_id: string;
  metadata: { [k: string]: string | number | number | boolean };
  properties: BenefitCustomProperties$Outbound;
};

/** @internal */
export const BenefitCustom$outboundSchema: z.ZodType<
  BenefitCustom$Outbound,
  z.ZodTypeDef,
  BenefitCustom
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  type: z.literal("custom").default("custom" as const),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organizationId: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  properties: BenefitCustomProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitCustom$ {
  /** @deprecated use `BenefitCustom$inboundSchema` instead. */
  export const inboundSchema = BenefitCustom$inboundSchema;
  /** @deprecated use `BenefitCustom$outboundSchema` instead. */
  export const outboundSchema = BenefitCustom$outboundSchema;
  /** @deprecated use `BenefitCustom$Outbound` instead. */
  export type Outbound = BenefitCustom$Outbound;
}

export function benefitCustomToJSON(benefitCustom: BenefitCustom): string {
  return JSON.stringify(BenefitCustom$outboundSchema.parse(benefitCustom));
}

export function benefitCustomFromJSON(
  jsonString: string,
): SafeParseResult<BenefitCustom, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitCustom$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitCustom' from JSON`,
  );
}
