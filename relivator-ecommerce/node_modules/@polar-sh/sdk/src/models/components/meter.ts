/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CountAggregation,
  CountAggregation$inboundSchema,
  CountAggregation$Outbound,
  CountAggregation$outboundSchema,
} from "./countaggregation.js";
import {
  Filter,
  Filter$inboundSchema,
  Filter$Outbound,
  Filter$outboundSchema,
} from "./filter.js";
import {
  PropertyAggregation,
  PropertyAggregation$inboundSchema,
  PropertyAggregation$Outbound,
  PropertyAggregation$outboundSchema,
} from "./propertyaggregation.js";

export type MeterMetadata = string | number | number | boolean;

/**
 * The aggregation to apply on the filtered events to calculate the meter.
 */
export type MeterAggregation =
  | (CountAggregation & { func: "count" })
  | (PropertyAggregation & { func: "avg" })
  | (PropertyAggregation & { func: "max" })
  | (PropertyAggregation & { func: "min" })
  | (PropertyAggregation & { func: "sum" });

export type Meter = {
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * The name of the meter. Will be shown on customer's invoices and usage.
   */
  name: string;
  filter: Filter;
  /**
   * The aggregation to apply on the filtered events to calculate the meter.
   */
  aggregation:
    | (CountAggregation & { func: "count" })
    | (PropertyAggregation & { func: "avg" })
    | (PropertyAggregation & { func: "max" })
    | (PropertyAggregation & { func: "min" })
    | (PropertyAggregation & { func: "sum" });
  /**
   * The ID of the organization owning the meter.
   */
  organizationId: string;
};

/** @internal */
export const MeterMetadata$inboundSchema: z.ZodType<
  MeterMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type MeterMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const MeterMetadata$outboundSchema: z.ZodType<
  MeterMetadata$Outbound,
  z.ZodTypeDef,
  MeterMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterMetadata$ {
  /** @deprecated use `MeterMetadata$inboundSchema` instead. */
  export const inboundSchema = MeterMetadata$inboundSchema;
  /** @deprecated use `MeterMetadata$outboundSchema` instead. */
  export const outboundSchema = MeterMetadata$outboundSchema;
  /** @deprecated use `MeterMetadata$Outbound` instead. */
  export type Outbound = MeterMetadata$Outbound;
}

export function meterMetadataToJSON(meterMetadata: MeterMetadata): string {
  return JSON.stringify(MeterMetadata$outboundSchema.parse(meterMetadata));
}

export function meterMetadataFromJSON(
  jsonString: string,
): SafeParseResult<MeterMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterMetadata' from JSON`,
  );
}

/** @internal */
export const MeterAggregation$inboundSchema: z.ZodType<
  MeterAggregation,
  z.ZodTypeDef,
  unknown
> = z.union([
  CountAggregation$inboundSchema.and(
    z.object({ func: z.literal("count") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("avg") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("max") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("min") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("sum") }).transform((v) => ({ func: v.func })),
  ),
]);

/** @internal */
export type MeterAggregation$Outbound =
  | (CountAggregation$Outbound & { func: "count" })
  | (PropertyAggregation$Outbound & { func: "avg" })
  | (PropertyAggregation$Outbound & { func: "max" })
  | (PropertyAggregation$Outbound & { func: "min" })
  | (PropertyAggregation$Outbound & { func: "sum" });

/** @internal */
export const MeterAggregation$outboundSchema: z.ZodType<
  MeterAggregation$Outbound,
  z.ZodTypeDef,
  MeterAggregation
> = z.union([
  CountAggregation$outboundSchema.and(
    z.object({ func: z.literal("count") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("avg") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("max") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("min") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("sum") }).transform((v) => ({ func: v.func })),
  ),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterAggregation$ {
  /** @deprecated use `MeterAggregation$inboundSchema` instead. */
  export const inboundSchema = MeterAggregation$inboundSchema;
  /** @deprecated use `MeterAggregation$outboundSchema` instead. */
  export const outboundSchema = MeterAggregation$outboundSchema;
  /** @deprecated use `MeterAggregation$Outbound` instead. */
  export type Outbound = MeterAggregation$Outbound;
}

export function meterAggregationToJSON(
  meterAggregation: MeterAggregation,
): string {
  return JSON.stringify(
    MeterAggregation$outboundSchema.parse(meterAggregation),
  );
}

export function meterAggregationFromJSON(
  jsonString: string,
): SafeParseResult<MeterAggregation, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterAggregation$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterAggregation' from JSON`,
  );
}

/** @internal */
export const Meter$inboundSchema: z.ZodType<Meter, z.ZodTypeDef, unknown> = z
  .object({
    metadata: z.record(
      z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
    ),
    created_at: z.string().datetime({ offset: true }).transform(v =>
      new Date(v)
    ),
    modified_at: z.nullable(
      z.string().datetime({ offset: true }).transform(v => new Date(v)),
    ),
    id: z.string(),
    name: z.string(),
    filter: Filter$inboundSchema,
    aggregation: z.union([
      CountAggregation$inboundSchema.and(
        z.object({ func: z.literal("count") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$inboundSchema.and(
        z.object({ func: z.literal("avg") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$inboundSchema.and(
        z.object({ func: z.literal("max") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$inboundSchema.and(
        z.object({ func: z.literal("min") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$inboundSchema.and(
        z.object({ func: z.literal("sum") }).transform((v) => ({
          func: v.func,
        })),
      ),
    ]),
    organization_id: z.string(),
  }).transform((v) => {
    return remap$(v, {
      "created_at": "createdAt",
      "modified_at": "modifiedAt",
      "organization_id": "organizationId",
    });
  });

/** @internal */
export type Meter$Outbound = {
  metadata: { [k: string]: string | number | number | boolean };
  created_at: string;
  modified_at: string | null;
  id: string;
  name: string;
  filter: Filter$Outbound;
  aggregation:
    | (CountAggregation$Outbound & { func: "count" })
    | (PropertyAggregation$Outbound & { func: "avg" })
    | (PropertyAggregation$Outbound & { func: "max" })
    | (PropertyAggregation$Outbound & { func: "min" })
    | (PropertyAggregation$Outbound & { func: "sum" });
  organization_id: string;
};

/** @internal */
export const Meter$outboundSchema: z.ZodType<
  Meter$Outbound,
  z.ZodTypeDef,
  Meter
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  name: z.string(),
  filter: Filter$outboundSchema,
  aggregation: z.union([
    CountAggregation$outboundSchema.and(
      z.object({ func: z.literal("count") }).transform((v) => ({
        func: v.func,
      })),
    ),
    PropertyAggregation$outboundSchema.and(
      z.object({ func: z.literal("avg") }).transform((v) => ({ func: v.func })),
    ),
    PropertyAggregation$outboundSchema.and(
      z.object({ func: z.literal("max") }).transform((v) => ({ func: v.func })),
    ),
    PropertyAggregation$outboundSchema.and(
      z.object({ func: z.literal("min") }).transform((v) => ({ func: v.func })),
    ),
    PropertyAggregation$outboundSchema.and(
      z.object({ func: z.literal("sum") }).transform((v) => ({ func: v.func })),
    ),
  ]),
  organizationId: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Meter$ {
  /** @deprecated use `Meter$inboundSchema` instead. */
  export const inboundSchema = Meter$inboundSchema;
  /** @deprecated use `Meter$outboundSchema` instead. */
  export const outboundSchema = Meter$outboundSchema;
  /** @deprecated use `Meter$Outbound` instead. */
  export type Outbound = Meter$Outbound;
}

export function meterToJSON(meter: Meter): string {
  return JSON.stringify(Meter$outboundSchema.parse(meter));
}

export function meterFromJSON(
  jsonString: string,
): SafeParseResult<Meter, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Meter$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Meter' from JSON`,
  );
}
