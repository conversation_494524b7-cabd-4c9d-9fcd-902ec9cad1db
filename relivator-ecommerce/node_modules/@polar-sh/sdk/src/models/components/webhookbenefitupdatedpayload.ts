/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Benefit,
  Benefit$inboundSchema,
  Benefit$Outbound,
  Benefit$outboundSchema,
} from "./benefit.js";

/**
 * Sent when a benefit is updated.
 *
 * @remarks
 *
 * **Discord & Slack support:** Basic
 */
export type WebhookBenefitUpdatedPayload = {
  type?: "benefit.updated" | undefined;
  data: Benefit;
};

/** @internal */
export const WebhookBenefitUpdatedPayload$inboundSchema: z.ZodType<
  WebhookBenefitUpdatedPayload,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: z.literal("benefit.updated").optional(),
  data: Benefit$inboundSchema,
});

/** @internal */
export type WebhookBenefitUpdatedPayload$Outbound = {
  type: "benefit.updated";
  data: Benefit$Outbound;
};

/** @internal */
export const WebhookBenefitUpdatedPayload$outboundSchema: z.ZodType<
  WebhookBenefitUpdatedPayload$Outbound,
  z.ZodTypeDef,
  WebhookBenefitUpdatedPayload
> = z.object({
  type: z.literal("benefit.updated").default("benefit.updated" as const),
  data: Benefit$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace WebhookBenefitUpdatedPayload$ {
  /** @deprecated use `WebhookBenefitUpdatedPayload$inboundSchema` instead. */
  export const inboundSchema = WebhookBenefitUpdatedPayload$inboundSchema;
  /** @deprecated use `WebhookBenefitUpdatedPayload$outboundSchema` instead. */
  export const outboundSchema = WebhookBenefitUpdatedPayload$outboundSchema;
  /** @deprecated use `WebhookBenefitUpdatedPayload$Outbound` instead. */
  export type Outbound = WebhookBenefitUpdatedPayload$Outbound;
}

export function webhookBenefitUpdatedPayloadToJSON(
  webhookBenefitUpdatedPayload: WebhookBenefitUpdatedPayload,
): string {
  return JSON.stringify(
    WebhookBenefitUpdatedPayload$outboundSchema.parse(
      webhookBenefitUpdatedPayload,
    ),
  );
}

export function webhookBenefitUpdatedPayloadFromJSON(
  jsonString: string,
): SafeParseResult<WebhookBenefitUpdatedPayload, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => WebhookBenefitUpdatedPayload$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'WebhookBenefitUpdatedPayload' from JSON`,
  );
}
