/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Current consumption and spending for a subscription meter.
 */
export type CustomerStateSubscriptionMeter = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * The number of consumed units so far in this billing period.
   */
  consumedUnits: number;
  /**
   * The number of credited units so far in this billing period.
   */
  creditedUnits: number;
  /**
   * The amount due in cents so far in this billing period.
   */
  amount: number;
  /**
   * The ID of the meter.
   */
  meterId: string;
};

/** @internal */
export const CustomerStateSubscriptionMeter$inboundSchema: z.ZodType<
  CustomerStateSubscriptionMeter,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  consumed_units: z.number(),
  credited_units: z.number().int(),
  amount: z.number().int(),
  meter_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "consumed_units": "consumedUnits",
    "credited_units": "creditedUnits",
    "meter_id": "meterId",
  });
});

/** @internal */
export type CustomerStateSubscriptionMeter$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  consumed_units: number;
  credited_units: number;
  amount: number;
  meter_id: string;
};

/** @internal */
export const CustomerStateSubscriptionMeter$outboundSchema: z.ZodType<
  CustomerStateSubscriptionMeter$Outbound,
  z.ZodTypeDef,
  CustomerStateSubscriptionMeter
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  consumedUnits: z.number(),
  creditedUnits: z.number().int(),
  amount: z.number().int(),
  meterId: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    consumedUnits: "consumed_units",
    creditedUnits: "credited_units",
    meterId: "meter_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerStateSubscriptionMeter$ {
  /** @deprecated use `CustomerStateSubscriptionMeter$inboundSchema` instead. */
  export const inboundSchema = CustomerStateSubscriptionMeter$inboundSchema;
  /** @deprecated use `CustomerStateSubscriptionMeter$outboundSchema` instead. */
  export const outboundSchema = CustomerStateSubscriptionMeter$outboundSchema;
  /** @deprecated use `CustomerStateSubscriptionMeter$Outbound` instead. */
  export type Outbound = CustomerStateSubscriptionMeter$Outbound;
}

export function customerStateSubscriptionMeterToJSON(
  customerStateSubscriptionMeter: CustomerStateSubscriptionMeter,
): string {
  return JSON.stringify(
    CustomerStateSubscriptionMeter$outboundSchema.parse(
      customerStateSubscriptionMeter,
    ),
  );
}

export function customerStateSubscriptionMeterFromJSON(
  jsonString: string,
): SafeParseResult<CustomerStateSubscriptionMeter, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerStateSubscriptionMeter$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerStateSubscriptionMeter' from JSON`,
  );
}
