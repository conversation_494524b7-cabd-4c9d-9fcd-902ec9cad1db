/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Benefit,
  Benefit$inboundSchema,
  Benefit$Outbound,
  Benefit$outboundSchema,
} from "./benefit.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";

export type ListResourceBenefit = {
  items: Array<Benefit>;
  pagination: Pagination;
};

/** @internal */
export const ListResourceBenefit$inboundSchema: z.ZodType<
  ListResourceBenefit,
  z.ZodTypeDef,
  unknown
> = z.object({
  items: z.array(Benefit$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ListResourceBenefit$Outbound = {
  items: Array<Benefit$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResourceBenefit$outboundSchema: z.ZodType<
  ListResourceBenefit$Outbound,
  z.ZodTypeDef,
  ListResourceBenefit
> = z.object({
  items: z.array(Benefit$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResourceBenefit$ {
  /** @deprecated use `ListResourceBenefit$inboundSchema` instead. */
  export const inboundSchema = ListResourceBenefit$inboundSchema;
  /** @deprecated use `ListResourceBenefit$outboundSchema` instead. */
  export const outboundSchema = ListResourceBenefit$outboundSchema;
  /** @deprecated use `ListResourceBenefit$Outbound` instead. */
  export type Outbound = ListResourceBenefit$Outbound;
}

export function listResourceBenefitToJSON(
  listResourceBenefit: ListResourceBenefit,
): string {
  return JSON.stringify(
    ListResourceBenefit$outboundSchema.parse(listResourceBenefit),
  );
}

export function listResourceBenefitFromJSON(
  jsonString: string,
): SafeParseResult<ListResourceBenefit, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListResourceBenefit$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListResourceBenefit' from JSON`,
  );
}
