/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const FilterConjunction = {
  And: "and",
  Or: "or",
} as const;
export type FilterConjunction = ClosedEnum<typeof FilterConjunction>;

/** @internal */
export const FilterConjunction$inboundSchema: z.ZodNativeEnum<
  typeof FilterConjunction
> = z.nativeEnum(FilterConjunction);

/** @internal */
export const FilterConjunction$outboundSchema: z.ZodNativeEnum<
  typeof FilterConjunction
> = FilterConjunction$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FilterConjunction$ {
  /** @deprecated use `FilterConjunction$inboundSchema` instead. */
  export const inboundSchema = FilterConjunction$inboundSchema;
  /** @deprecated use `FilterConjunction$outboundSchema` instead. */
  export const outboundSchema = FilterConjunction$outboundSchema;
}
