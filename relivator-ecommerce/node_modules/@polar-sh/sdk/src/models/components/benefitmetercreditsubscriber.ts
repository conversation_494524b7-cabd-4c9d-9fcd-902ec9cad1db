/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitMeterCreditSubscriberProperties,
  BenefitMeterCreditSubscriberProperties$inboundSchema,
  BenefitMeterCreditSubscriberProperties$Outbound,
  BenefitMeterCreditSubscriberProperties$outboundSchema,
} from "./benefitmetercreditsubscriberproperties.js";
import {
  Organization,
  Organization$inboundSchema,
  Organization$Outbound,
  Organization$outboundSchema,
} from "./organization.js";

export type BenefitMeterCreditSubscriberMetadata =
  | string
  | number
  | number
  | boolean;

export type BenefitMeterCreditSubscriber = {
  /**
   * The ID of the benefit.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  type?: "meter_credit" | undefined;
  /**
   * The description of the benefit.
   */
  description: string;
  /**
   * Whether the benefit is selectable when creating a product.
   */
  selectable: boolean;
  /**
   * Whether the benefit is deletable.
   */
  deletable: boolean;
  /**
   * The ID of the organization owning the benefit.
   */
  organizationId: string;
  metadata: { [k: string]: string | number | number | boolean };
  organization: Organization;
  /**
   * Properties available to subscribers for a benefit of type `meter_unit`.
   */
  properties: BenefitMeterCreditSubscriberProperties;
};

/** @internal */
export const BenefitMeterCreditSubscriberMetadata$inboundSchema: z.ZodType<
  BenefitMeterCreditSubscriberMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitMeterCreditSubscriberMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitMeterCreditSubscriberMetadata$outboundSchema: z.ZodType<
  BenefitMeterCreditSubscriberMetadata$Outbound,
  z.ZodTypeDef,
  BenefitMeterCreditSubscriberMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitMeterCreditSubscriberMetadata$ {
  /** @deprecated use `BenefitMeterCreditSubscriberMetadata$inboundSchema` instead. */
  export const inboundSchema =
    BenefitMeterCreditSubscriberMetadata$inboundSchema;
  /** @deprecated use `BenefitMeterCreditSubscriberMetadata$outboundSchema` instead. */
  export const outboundSchema =
    BenefitMeterCreditSubscriberMetadata$outboundSchema;
  /** @deprecated use `BenefitMeterCreditSubscriberMetadata$Outbound` instead. */
  export type Outbound = BenefitMeterCreditSubscriberMetadata$Outbound;
}

export function benefitMeterCreditSubscriberMetadataToJSON(
  benefitMeterCreditSubscriberMetadata: BenefitMeterCreditSubscriberMetadata,
): string {
  return JSON.stringify(
    BenefitMeterCreditSubscriberMetadata$outboundSchema.parse(
      benefitMeterCreditSubscriberMetadata,
    ),
  );
}

export function benefitMeterCreditSubscriberMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitMeterCreditSubscriberMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitMeterCreditSubscriberMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitMeterCreditSubscriberMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitMeterCreditSubscriber$inboundSchema: z.ZodType<
  BenefitMeterCreditSubscriber,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  type: z.literal("meter_credit").optional(),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organization_id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  organization: Organization$inboundSchema,
  properties: BenefitMeterCreditSubscriberProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitMeterCreditSubscriber$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  type: "meter_credit";
  description: string;
  selectable: boolean;
  deletable: boolean;
  organization_id: string;
  metadata: { [k: string]: string | number | number | boolean };
  organization: Organization$Outbound;
  properties: BenefitMeterCreditSubscriberProperties$Outbound;
};

/** @internal */
export const BenefitMeterCreditSubscriber$outboundSchema: z.ZodType<
  BenefitMeterCreditSubscriber$Outbound,
  z.ZodTypeDef,
  BenefitMeterCreditSubscriber
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  type: z.literal("meter_credit").default("meter_credit" as const),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organizationId: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  organization: Organization$outboundSchema,
  properties: BenefitMeterCreditSubscriberProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitMeterCreditSubscriber$ {
  /** @deprecated use `BenefitMeterCreditSubscriber$inboundSchema` instead. */
  export const inboundSchema = BenefitMeterCreditSubscriber$inboundSchema;
  /** @deprecated use `BenefitMeterCreditSubscriber$outboundSchema` instead. */
  export const outboundSchema = BenefitMeterCreditSubscriber$outboundSchema;
  /** @deprecated use `BenefitMeterCreditSubscriber$Outbound` instead. */
  export type Outbound = BenefitMeterCreditSubscriber$Outbound;
}

export function benefitMeterCreditSubscriberToJSON(
  benefitMeterCreditSubscriber: BenefitMeterCreditSubscriber,
): string {
  return JSON.stringify(
    BenefitMeterCreditSubscriber$outboundSchema.parse(
      benefitMeterCreditSubscriber,
    ),
  );
}

export function benefitMeterCreditSubscriberFromJSON(
  jsonString: string,
): SafeParseResult<BenefitMeterCreditSubscriber, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitMeterCreditSubscriber$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitMeterCreditSubscriber' from JSON`,
  );
}
