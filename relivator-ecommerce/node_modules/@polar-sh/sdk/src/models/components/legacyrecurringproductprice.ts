/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  LegacyRecurringProductPriceCustom,
  LegacyRecurringProductPriceCustom$inboundSchema,
  LegacyRecurringProductPriceCustom$Outbound,
  LegacyRecurringProductPriceCustom$outboundSchema,
} from "./legacyrecurringproductpricecustom.js";
import {
  LegacyRecurringProductPriceFixed,
  LegacyRecurringProductPriceFixed$inboundSchema,
  LegacyRecurringProductPriceFixed$Outbound,
  LegacyRecurringProductPriceFixed$outboundSchema,
} from "./legacyrecurringproductpricefixed.js";
import {
  LegacyRecurringProductPriceFree,
  LegacyRecurringProductPriceFree$inboundSchema,
  LegacyRecurringProductPriceFree$Outbound,
  LegacyRecurringProductPriceFree$outboundSchema,
} from "./legacyrecurringproductpricefree.js";

export type LegacyRecurringProductPrice =
  | (LegacyRecurringProductPriceFree & { amountType: "free" })
  | (LegacyRecurringProductPriceFixed & { amountType: "fixed" })
  | (LegacyRecurringProductPriceCustom & { amountType: "custom" });

/** @internal */
export const LegacyRecurringProductPrice$inboundSchema: z.ZodType<
  LegacyRecurringProductPrice,
  z.ZodTypeDef,
  unknown
> = z.union([
  LegacyRecurringProductPriceFree$inboundSchema.and(
    z.object({ amount_type: z.literal("free") }).transform((v) => ({
      amountType: v.amount_type,
    })),
  ),
  LegacyRecurringProductPriceFixed$inboundSchema.and(
    z.object({ amount_type: z.literal("fixed") }).transform((v) => ({
      amountType: v.amount_type,
    })),
  ),
  LegacyRecurringProductPriceCustom$inboundSchema.and(
    z.object({ amount_type: z.literal("custom") }).transform((v) => ({
      amountType: v.amount_type,
    })),
  ),
]);

/** @internal */
export type LegacyRecurringProductPrice$Outbound =
  | (LegacyRecurringProductPriceFree$Outbound & { amount_type: "free" })
  | (LegacyRecurringProductPriceFixed$Outbound & { amount_type: "fixed" })
  | (LegacyRecurringProductPriceCustom$Outbound & { amount_type: "custom" });

/** @internal */
export const LegacyRecurringProductPrice$outboundSchema: z.ZodType<
  LegacyRecurringProductPrice$Outbound,
  z.ZodTypeDef,
  LegacyRecurringProductPrice
> = z.union([
  LegacyRecurringProductPriceFree$outboundSchema.and(
    z.object({ amountType: z.literal("free") }).transform((v) => ({
      amount_type: v.amountType,
    })),
  ),
  LegacyRecurringProductPriceFixed$outboundSchema.and(
    z.object({ amountType: z.literal("fixed") }).transform((v) => ({
      amount_type: v.amountType,
    })),
  ),
  LegacyRecurringProductPriceCustom$outboundSchema.and(
    z.object({ amountType: z.literal("custom") }).transform((v) => ({
      amount_type: v.amountType,
    })),
  ),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LegacyRecurringProductPrice$ {
  /** @deprecated use `LegacyRecurringProductPrice$inboundSchema` instead. */
  export const inboundSchema = LegacyRecurringProductPrice$inboundSchema;
  /** @deprecated use `LegacyRecurringProductPrice$outboundSchema` instead. */
  export const outboundSchema = LegacyRecurringProductPrice$outboundSchema;
  /** @deprecated use `LegacyRecurringProductPrice$Outbound` instead. */
  export type Outbound = LegacyRecurringProductPrice$Outbound;
}

export function legacyRecurringProductPriceToJSON(
  legacyRecurringProductPrice: LegacyRecurringProductPrice,
): string {
  return JSON.stringify(
    LegacyRecurringProductPrice$outboundSchema.parse(
      legacyRecurringProductPrice,
    ),
  );
}

export function legacyRecurringProductPriceFromJSON(
  jsonString: string,
): SafeParseResult<LegacyRecurringProductPrice, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LegacyRecurringProductPrice$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LegacyRecurringProductPrice' from JSON`,
  );
}
