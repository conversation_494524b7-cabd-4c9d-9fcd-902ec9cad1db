/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitPublic,
  BenefitPublic$inboundSchema,
  BenefitPublic$Outbound,
  BenefitPublic$outboundSchema,
} from "./benefitpublic.js";
import {
  LegacyRecurringProductPrice,
  LegacyRecurringProductPrice$inboundSchema,
  LegacyRecurringProductPrice$Outbound,
  LegacyRecurringProductPrice$outboundSchema,
} from "./legacyrecurringproductprice.js";
import {
  ProductMediaFileRead,
  ProductMediaFileRead$inboundSchema,
  ProductMediaFileRead$Outbound,
  ProductMediaFileRead$outboundSchema,
} from "./productmediafileread.js";
import {
  ProductPrice,
  ProductPrice$inboundSchema,
  ProductPrice$Outbound,
  ProductPrice$outboundSchema,
} from "./productprice.js";
import {
  SubscriptionRecurringInterval,
  SubscriptionRecurringInterval$inboundSchema,
  SubscriptionRecurringInterval$outboundSchema,
} from "./subscriptionrecurringinterval.js";

export type CustomerProductPrices = LegacyRecurringProductPrice | ProductPrice;

/**
 * Schema of a product for customer portal.
 */
export type CustomerProduct = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the product.
   */
  id: string;
  /**
   * The name of the product.
   */
  name: string;
  /**
   * The description of the product.
   */
  description: string | null;
  /**
   * The recurring interval of the product. If `None`, the product is a one-time purchase.
   */
  recurringInterval: SubscriptionRecurringInterval | null;
  /**
   * Whether the product is a subscription.
   */
  isRecurring: boolean;
  /**
   * Whether the product is archived and no longer available.
   */
  isArchived: boolean;
  /**
   * The ID of the organization owning the product.
   */
  organizationId: string;
  /**
   * List of available prices for this product.
   */
  prices: Array<LegacyRecurringProductPrice | ProductPrice>;
  /**
   * The benefits granted by the product.
   */
  benefits: Array<BenefitPublic>;
  /**
   * The medias associated to the product.
   */
  medias: Array<ProductMediaFileRead>;
};

/** @internal */
export const CustomerProductPrices$inboundSchema: z.ZodType<
  CustomerProductPrices,
  z.ZodTypeDef,
  unknown
> = z.union([
  LegacyRecurringProductPrice$inboundSchema,
  ProductPrice$inboundSchema,
]);

/** @internal */
export type CustomerProductPrices$Outbound =
  | LegacyRecurringProductPrice$Outbound
  | ProductPrice$Outbound;

/** @internal */
export const CustomerProductPrices$outboundSchema: z.ZodType<
  CustomerProductPrices$Outbound,
  z.ZodTypeDef,
  CustomerProductPrices
> = z.union([
  LegacyRecurringProductPrice$outboundSchema,
  ProductPrice$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerProductPrices$ {
  /** @deprecated use `CustomerProductPrices$inboundSchema` instead. */
  export const inboundSchema = CustomerProductPrices$inboundSchema;
  /** @deprecated use `CustomerProductPrices$outboundSchema` instead. */
  export const outboundSchema = CustomerProductPrices$outboundSchema;
  /** @deprecated use `CustomerProductPrices$Outbound` instead. */
  export type Outbound = CustomerProductPrices$Outbound;
}

export function customerProductPricesToJSON(
  customerProductPrices: CustomerProductPrices,
): string {
  return JSON.stringify(
    CustomerProductPrices$outboundSchema.parse(customerProductPrices),
  );
}

export function customerProductPricesFromJSON(
  jsonString: string,
): SafeParseResult<CustomerProductPrices, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerProductPrices$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerProductPrices' from JSON`,
  );
}

/** @internal */
export const CustomerProduct$inboundSchema: z.ZodType<
  CustomerProduct,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  name: z.string(),
  description: z.nullable(z.string()),
  recurring_interval: z.nullable(SubscriptionRecurringInterval$inboundSchema),
  is_recurring: z.boolean(),
  is_archived: z.boolean(),
  organization_id: z.string(),
  prices: z.array(
    z.union([
      LegacyRecurringProductPrice$inboundSchema,
      ProductPrice$inboundSchema,
    ]),
  ),
  benefits: z.array(BenefitPublic$inboundSchema),
  medias: z.array(ProductMediaFileRead$inboundSchema),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "recurring_interval": "recurringInterval",
    "is_recurring": "isRecurring",
    "is_archived": "isArchived",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type CustomerProduct$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  name: string;
  description: string | null;
  recurring_interval: string | null;
  is_recurring: boolean;
  is_archived: boolean;
  organization_id: string;
  prices: Array<LegacyRecurringProductPrice$Outbound | ProductPrice$Outbound>;
  benefits: Array<BenefitPublic$Outbound>;
  medias: Array<ProductMediaFileRead$Outbound>;
};

/** @internal */
export const CustomerProduct$outboundSchema: z.ZodType<
  CustomerProduct$Outbound,
  z.ZodTypeDef,
  CustomerProduct
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  name: z.string(),
  description: z.nullable(z.string()),
  recurringInterval: z.nullable(SubscriptionRecurringInterval$outboundSchema),
  isRecurring: z.boolean(),
  isArchived: z.boolean(),
  organizationId: z.string(),
  prices: z.array(
    z.union([
      LegacyRecurringProductPrice$outboundSchema,
      ProductPrice$outboundSchema,
    ]),
  ),
  benefits: z.array(BenefitPublic$outboundSchema),
  medias: z.array(ProductMediaFileRead$outboundSchema),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    recurringInterval: "recurring_interval",
    isRecurring: "is_recurring",
    isArchived: "is_archived",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerProduct$ {
  /** @deprecated use `CustomerProduct$inboundSchema` instead. */
  export const inboundSchema = CustomerProduct$inboundSchema;
  /** @deprecated use `CustomerProduct$outboundSchema` instead. */
  export const outboundSchema = CustomerProduct$outboundSchema;
  /** @deprecated use `CustomerProduct$Outbound` instead. */
  export type Outbound = CustomerProduct$Outbound;
}

export function customerProductToJSON(
  customerProduct: CustomerProduct,
): string {
  return JSON.stringify(CustomerProduct$outboundSchema.parse(customerProduct));
}

export function customerProductFromJSON(
  jsonString: string,
): SafeParseResult<CustomerProduct, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerProduct$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerProduct' from JSON`,
  );
}
