/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  OrganizationDetails,
  OrganizationDetails$inboundSchema,
  OrganizationDetails$Outbound,
  OrganizationDetails$outboundSchema,
} from "./organizationdetails.js";
import {
  OrganizationFeatureSettings,
  OrganizationFeatureSettings$inboundSchema,
  OrganizationFeatureSettings$Outbound,
  OrganizationFeatureSettings$outboundSchema,
} from "./organizationfeaturesettings.js";
import {
  OrganizationSocialLink,
  OrganizationSocialLink$inboundSchema,
  OrganizationSocialLink$Outbound,
  OrganizationSocialLink$outboundSchema,
} from "./organizationsociallink.js";
import {
  OrganizationSubscriptionSettings,
  OrganizationSubscriptionSettings$inboundSchema,
  OrganizationSubscriptionSettings$Outbound,
  OrganizationSubscriptionSettings$outboundSchema,
} from "./organizationsubscriptionsettings.js";

export type OrganizationCreate = {
  name: string;
  slug: string;
  avatarUrl?: string | null | undefined;
  /**
   * Public support email.
   */
  email?: string | null | undefined;
  /**
   * Official website of the organization.
   */
  website?: string | null | undefined;
  /**
   * Link to social profiles.
   */
  socials?: Array<OrganizationSocialLink> | null | undefined;
  /**
   * Additional, private, business details Polar needs about active organizations for compliance (KYC).
   */
  details?: OrganizationDetails | null | undefined;
  featureSettings?: OrganizationFeatureSettings | null | undefined;
  subscriptionSettings?: OrganizationSubscriptionSettings | null | undefined;
};

/** @internal */
export const OrganizationCreate$inboundSchema: z.ZodType<
  OrganizationCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  name: z.string(),
  slug: z.string(),
  avatar_url: z.nullable(z.string()).optional(),
  email: z.nullable(z.string()).optional(),
  website: z.nullable(z.string()).optional(),
  socials: z.nullable(z.array(OrganizationSocialLink$inboundSchema)).optional(),
  details: z.nullable(OrganizationDetails$inboundSchema).optional(),
  feature_settings: z.nullable(OrganizationFeatureSettings$inboundSchema)
    .optional(),
  subscription_settings: z.nullable(
    OrganizationSubscriptionSettings$inboundSchema,
  ).optional(),
}).transform((v) => {
  return remap$(v, {
    "avatar_url": "avatarUrl",
    "feature_settings": "featureSettings",
    "subscription_settings": "subscriptionSettings",
  });
});

/** @internal */
export type OrganizationCreate$Outbound = {
  name: string;
  slug: string;
  avatar_url?: string | null | undefined;
  email?: string | null | undefined;
  website?: string | null | undefined;
  socials?: Array<OrganizationSocialLink$Outbound> | null | undefined;
  details?: OrganizationDetails$Outbound | null | undefined;
  feature_settings?: OrganizationFeatureSettings$Outbound | null | undefined;
  subscription_settings?:
    | OrganizationSubscriptionSettings$Outbound
    | null
    | undefined;
};

/** @internal */
export const OrganizationCreate$outboundSchema: z.ZodType<
  OrganizationCreate$Outbound,
  z.ZodTypeDef,
  OrganizationCreate
> = z.object({
  name: z.string(),
  slug: z.string(),
  avatarUrl: z.nullable(z.string()).optional(),
  email: z.nullable(z.string()).optional(),
  website: z.nullable(z.string()).optional(),
  socials: z.nullable(z.array(OrganizationSocialLink$outboundSchema))
    .optional(),
  details: z.nullable(OrganizationDetails$outboundSchema).optional(),
  featureSettings: z.nullable(OrganizationFeatureSettings$outboundSchema)
    .optional(),
  subscriptionSettings: z.nullable(
    OrganizationSubscriptionSettings$outboundSchema,
  ).optional(),
}).transform((v) => {
  return remap$(v, {
    avatarUrl: "avatar_url",
    featureSettings: "feature_settings",
    subscriptionSettings: "subscription_settings",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrganizationCreate$ {
  /** @deprecated use `OrganizationCreate$inboundSchema` instead. */
  export const inboundSchema = OrganizationCreate$inboundSchema;
  /** @deprecated use `OrganizationCreate$outboundSchema` instead. */
  export const outboundSchema = OrganizationCreate$outboundSchema;
  /** @deprecated use `OrganizationCreate$Outbound` instead. */
  export type Outbound = OrganizationCreate$Outbound;
}

export function organizationCreateToJSON(
  organizationCreate: OrganizationCreate,
): string {
  return JSON.stringify(
    OrganizationCreate$outboundSchema.parse(organizationCreate),
  );
}

export function organizationCreateFromJSON(
  jsonString: string,
): SafeParseResult<OrganizationCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrganizationCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrganizationCreate' from JSON`,
  );
}
