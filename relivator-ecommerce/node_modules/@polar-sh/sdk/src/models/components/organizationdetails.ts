/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const SwitchingFrom = {
  Paddle: "paddle",
  LemonSqueezy: "lemon_squeezy",
  Gumroad: "gumroad",
  Stripe: "stripe",
  Other: "other",
} as const;
export type SwitchingFrom = ClosedEnum<typeof SwitchingFrom>;

export type OrganizationDetails = {
  /**
   * Brief information about you and your business.
   */
  about: string;
  /**
   * Description of digital products being sold.
   */
  productDescription: string;
  /**
   * How the organization will integrate and use Polar.
   */
  intendedUse: string;
  /**
   * Main customer acquisition channels.
   */
  customerAcquisition: Array<string>;
  /**
   * Estimated revenue in the next 12 months
   */
  futureAnnualRevenue: number;
  /**
   * Switching from another platform?
   */
  switching?: boolean | undefined;
  /**
   * Which platform the organization is migrating from.
   */
  switchingFrom?: SwitchingFrom | null | undefined;
  /**
   * Revenue from last year if applicable.
   */
  previousAnnualRevenue?: number | undefined;
};

/** @internal */
export const SwitchingFrom$inboundSchema: z.ZodNativeEnum<
  typeof SwitchingFrom
> = z.nativeEnum(SwitchingFrom);

/** @internal */
export const SwitchingFrom$outboundSchema: z.ZodNativeEnum<
  typeof SwitchingFrom
> = SwitchingFrom$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace SwitchingFrom$ {
  /** @deprecated use `SwitchingFrom$inboundSchema` instead. */
  export const inboundSchema = SwitchingFrom$inboundSchema;
  /** @deprecated use `SwitchingFrom$outboundSchema` instead. */
  export const outboundSchema = SwitchingFrom$outboundSchema;
}

/** @internal */
export const OrganizationDetails$inboundSchema: z.ZodType<
  OrganizationDetails,
  z.ZodTypeDef,
  unknown
> = z.object({
  about: z.string(),
  product_description: z.string(),
  intended_use: z.string(),
  customer_acquisition: z.array(z.string()),
  future_annual_revenue: z.number().int(),
  switching: z.boolean().default(true),
  switching_from: z.nullable(SwitchingFrom$inboundSchema).optional(),
  previous_annual_revenue: z.number().int().default(0),
}).transform((v) => {
  return remap$(v, {
    "product_description": "productDescription",
    "intended_use": "intendedUse",
    "customer_acquisition": "customerAcquisition",
    "future_annual_revenue": "futureAnnualRevenue",
    "switching_from": "switchingFrom",
    "previous_annual_revenue": "previousAnnualRevenue",
  });
});

/** @internal */
export type OrganizationDetails$Outbound = {
  about: string;
  product_description: string;
  intended_use: string;
  customer_acquisition: Array<string>;
  future_annual_revenue: number;
  switching: boolean;
  switching_from?: string | null | undefined;
  previous_annual_revenue: number;
};

/** @internal */
export const OrganizationDetails$outboundSchema: z.ZodType<
  OrganizationDetails$Outbound,
  z.ZodTypeDef,
  OrganizationDetails
> = z.object({
  about: z.string(),
  productDescription: z.string(),
  intendedUse: z.string(),
  customerAcquisition: z.array(z.string()),
  futureAnnualRevenue: z.number().int(),
  switching: z.boolean().default(true),
  switchingFrom: z.nullable(SwitchingFrom$outboundSchema).optional(),
  previousAnnualRevenue: z.number().int().default(0),
}).transform((v) => {
  return remap$(v, {
    productDescription: "product_description",
    intendedUse: "intended_use",
    customerAcquisition: "customer_acquisition",
    futureAnnualRevenue: "future_annual_revenue",
    switchingFrom: "switching_from",
    previousAnnualRevenue: "previous_annual_revenue",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrganizationDetails$ {
  /** @deprecated use `OrganizationDetails$inboundSchema` instead. */
  export const inboundSchema = OrganizationDetails$inboundSchema;
  /** @deprecated use `OrganizationDetails$outboundSchema` instead. */
  export const outboundSchema = OrganizationDetails$outboundSchema;
  /** @deprecated use `OrganizationDetails$Outbound` instead. */
  export type Outbound = OrganizationDetails$Outbound;
}

export function organizationDetailsToJSON(
  organizationDetails: OrganizationDetails,
): string {
  return JSON.stringify(
    OrganizationDetails$outboundSchema.parse(organizationDetails),
  );
}

export function organizationDetailsFromJSON(
  jsonString: string,
): SafeParseResult<OrganizationDetails, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrganizationDetails$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrganizationDetails' from JSON`,
  );
}
