/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomFieldDateProperties,
  CustomFieldDateProperties$inboundSchema,
  CustomFieldDateProperties$Outbound,
  CustomFieldDateProperties$outboundSchema,
} from "./customfielddateproperties.js";

export type CustomFieldCreateDateMetadata = string | number | number | boolean;

/**
 * Schema to create a custom field of type date.
 */
export type CustomFieldCreateDate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type?: "date" | undefined;
  /**
   * Identifier of the custom field. It'll be used as key when storing the value. Must be unique across the organization.It can only contain ASCII letters, numbers and hyphens.
   */
  slug: string;
  /**
   * Name of the custom field.
   */
  name: string;
  /**
   * The ID of the organization owning the custom field. **Required unless you use an organization token.**
   */
  organizationId?: string | null | undefined;
  properties: CustomFieldDateProperties;
};

/** @internal */
export const CustomFieldCreateDateMetadata$inboundSchema: z.ZodType<
  CustomFieldCreateDateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomFieldCreateDateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomFieldCreateDateMetadata$outboundSchema: z.ZodType<
  CustomFieldCreateDateMetadata$Outbound,
  z.ZodTypeDef,
  CustomFieldCreateDateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldCreateDateMetadata$ {
  /** @deprecated use `CustomFieldCreateDateMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomFieldCreateDateMetadata$inboundSchema;
  /** @deprecated use `CustomFieldCreateDateMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomFieldCreateDateMetadata$outboundSchema;
  /** @deprecated use `CustomFieldCreateDateMetadata$Outbound` instead. */
  export type Outbound = CustomFieldCreateDateMetadata$Outbound;
}

export function customFieldCreateDateMetadataToJSON(
  customFieldCreateDateMetadata: CustomFieldCreateDateMetadata,
): string {
  return JSON.stringify(
    CustomFieldCreateDateMetadata$outboundSchema.parse(
      customFieldCreateDateMetadata,
    ),
  );
}

export function customFieldCreateDateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldCreateDateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldCreateDateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldCreateDateMetadata' from JSON`,
  );
}

/** @internal */
export const CustomFieldCreateDate$inboundSchema: z.ZodType<
  CustomFieldCreateDate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("date").optional(),
  slug: z.string(),
  name: z.string(),
  organization_id: z.nullable(z.string()).optional(),
  properties: CustomFieldDateProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
  });
});

/** @internal */
export type CustomFieldCreateDate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type: "date";
  slug: string;
  name: string;
  organization_id?: string | null | undefined;
  properties: CustomFieldDateProperties$Outbound;
};

/** @internal */
export const CustomFieldCreateDate$outboundSchema: z.ZodType<
  CustomFieldCreateDate$Outbound,
  z.ZodTypeDef,
  CustomFieldCreateDate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("date").default("date" as const),
  slug: z.string(),
  name: z.string(),
  organizationId: z.nullable(z.string()).optional(),
  properties: CustomFieldDateProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldCreateDate$ {
  /** @deprecated use `CustomFieldCreateDate$inboundSchema` instead. */
  export const inboundSchema = CustomFieldCreateDate$inboundSchema;
  /** @deprecated use `CustomFieldCreateDate$outboundSchema` instead. */
  export const outboundSchema = CustomFieldCreateDate$outboundSchema;
  /** @deprecated use `CustomFieldCreateDate$Outbound` instead. */
  export type Outbound = CustomFieldCreateDate$Outbound;
}

export function customFieldCreateDateToJSON(
  customFieldCreateDate: CustomFieldCreateDate,
): string {
  return JSON.stringify(
    CustomFieldCreateDate$outboundSchema.parse(customFieldCreateDate),
  );
}

export function customFieldCreateDateFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldCreateDate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldCreateDate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldCreateDate' from JSON`,
  );
}
