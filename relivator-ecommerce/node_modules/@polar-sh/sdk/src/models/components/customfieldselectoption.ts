/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type CustomFieldSelectOption = {
  value: string;
  label: string;
};

/** @internal */
export const CustomFieldSelectOption$inboundSchema: z.ZodType<
  CustomFieldSelectOption,
  z.ZodTypeDef,
  unknown
> = z.object({
  value: z.string(),
  label: z.string(),
});

/** @internal */
export type CustomFieldSelectOption$Outbound = {
  value: string;
  label: string;
};

/** @internal */
export const CustomFieldSelectOption$outboundSchema: z.ZodType<
  CustomFieldSelectOption$Outbound,
  z.ZodTypeDef,
  CustomFieldSelectOption
> = z.object({
  value: z.string(),
  label: z.string(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldSelectOption$ {
  /** @deprecated use `CustomFieldSelectOption$inboundSchema` instead. */
  export const inboundSchema = CustomFieldSelectOption$inboundSchema;
  /** @deprecated use `CustomFieldSelectOption$outboundSchema` instead. */
  export const outboundSchema = CustomFieldSelectOption$outboundSchema;
  /** @deprecated use `CustomFieldSelectOption$Outbound` instead. */
  export type Outbound = CustomFieldSelectOption$Outbound;
}

export function customFieldSelectOptionToJSON(
  customFieldSelectOption: CustomFieldSelectOption,
): string {
  return JSON.stringify(
    CustomFieldSelectOption$outboundSchema.parse(customFieldSelectOption),
  );
}

export function customFieldSelectOptionFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldSelectOption, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldSelectOption$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldSelectOption' from JSON`,
  );
}
