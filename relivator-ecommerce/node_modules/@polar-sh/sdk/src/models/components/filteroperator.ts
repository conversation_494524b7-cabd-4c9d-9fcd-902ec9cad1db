/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const FilterOperator = {
  Eq: "eq",
  Ne: "ne",
  Gt: "gt",
  Gte: "gte",
  Lt: "lt",
  Lte: "lte",
  Like: "like",
  NotL<PERSON>: "not_like",
} as const;
export type FilterOperator = ClosedEnum<typeof FilterOperator>;

/** @internal */
export const FilterOperator$inboundSchema: z.ZodNativeEnum<
  typeof FilterOperator
> = z.nativeEnum(FilterOperator);

/** @internal */
export const FilterOperator$outboundSchema: z.ZodNativeEnum<
  typeof FilterOperator
> = FilterOperator$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FilterOperator$ {
  /** @deprecated use `FilterOperator$inboundSchema` instead. */
  export const inboundSchema = FilterOperator$inboundSchema;
  /** @deprecated use `FilterOperator$outboundSchema` instead. */
  export const outboundSchema = FilterOperator$outboundSchema;
}
