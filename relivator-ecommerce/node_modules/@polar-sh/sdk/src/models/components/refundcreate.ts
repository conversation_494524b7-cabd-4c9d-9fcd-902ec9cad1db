/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  RefundReason,
  RefundReason$inboundSchema,
  RefundReason$outboundSchema,
} from "./refundreason.js";

export type RefundCreateMetadata = string | number | number | boolean;

export type RefundCreate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  orderId: string;
  reason: RefundReason;
  /**
   * Amount to refund in cents. Minimum is 1.
   */
  amount: number;
  /**
   * An internal comment about the refund.
   */
  comment?: string | null | undefined;
  /**
   * Should this refund trigger the associated customer benefits to be revoked?
   *
   * @remarks
   *
   * **Note:**
   * Only allowed in case the `order` is a one-time purchase.
   * Subscriptions automatically revoke customer benefits once the
   * subscription itself is revoked, i.e fully canceled.
   */
  revokeBenefits?: boolean | undefined;
};

/** @internal */
export const RefundCreateMetadata$inboundSchema: z.ZodType<
  RefundCreateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type RefundCreateMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const RefundCreateMetadata$outboundSchema: z.ZodType<
  RefundCreateMetadata$Outbound,
  z.ZodTypeDef,
  RefundCreateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace RefundCreateMetadata$ {
  /** @deprecated use `RefundCreateMetadata$inboundSchema` instead. */
  export const inboundSchema = RefundCreateMetadata$inboundSchema;
  /** @deprecated use `RefundCreateMetadata$outboundSchema` instead. */
  export const outboundSchema = RefundCreateMetadata$outboundSchema;
  /** @deprecated use `RefundCreateMetadata$Outbound` instead. */
  export type Outbound = RefundCreateMetadata$Outbound;
}

export function refundCreateMetadataToJSON(
  refundCreateMetadata: RefundCreateMetadata,
): string {
  return JSON.stringify(
    RefundCreateMetadata$outboundSchema.parse(refundCreateMetadata),
  );
}

export function refundCreateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<RefundCreateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => RefundCreateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'RefundCreateMetadata' from JSON`,
  );
}

/** @internal */
export const RefundCreate$inboundSchema: z.ZodType<
  RefundCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  order_id: z.string(),
  reason: RefundReason$inboundSchema,
  amount: z.number().int(),
  comment: z.nullable(z.string()).optional(),
  revoke_benefits: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    "order_id": "orderId",
    "revoke_benefits": "revokeBenefits",
  });
});

/** @internal */
export type RefundCreate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  order_id: string;
  reason: string;
  amount: number;
  comment?: string | null | undefined;
  revoke_benefits: boolean;
};

/** @internal */
export const RefundCreate$outboundSchema: z.ZodType<
  RefundCreate$Outbound,
  z.ZodTypeDef,
  RefundCreate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  orderId: z.string(),
  reason: RefundReason$outboundSchema,
  amount: z.number().int(),
  comment: z.nullable(z.string()).optional(),
  revokeBenefits: z.boolean().default(false),
}).transform((v) => {
  return remap$(v, {
    orderId: "order_id",
    revokeBenefits: "revoke_benefits",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace RefundCreate$ {
  /** @deprecated use `RefundCreate$inboundSchema` instead. */
  export const inboundSchema = RefundCreate$inboundSchema;
  /** @deprecated use `RefundCreate$outboundSchema` instead. */
  export const outboundSchema = RefundCreate$outboundSchema;
  /** @deprecated use `RefundCreate$Outbound` instead. */
  export type Outbound = RefundCreate$Outbound;
}

export function refundCreateToJSON(refundCreate: RefundCreate): string {
  return JSON.stringify(RefundCreate$outboundSchema.parse(refundCreate));
}

export function refundCreateFromJSON(
  jsonString: string,
): SafeParseResult<RefundCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => RefundCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'RefundCreate' from JSON`,
  );
}
