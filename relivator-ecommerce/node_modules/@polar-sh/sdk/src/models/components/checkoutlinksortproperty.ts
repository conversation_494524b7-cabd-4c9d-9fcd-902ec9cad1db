/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const CheckoutLinkSortProperty = {
  CreatedAt: "created_at",
  MinusCreatedAt: "-created_at",
  Label: "label",
  MinusLabel: "-label",
  SuccessUrl: "success_url",
  MinusSuccessUrl: "-success_url",
  AllowDiscountCodes: "allow_discount_codes",
  MinusAllowDiscountCodes: "-allow_discount_codes",
} as const;
export type CheckoutLinkSortProperty = ClosedEnum<
  typeof CheckoutLinkSortProperty
>;

/** @internal */
export const CheckoutLinkSortProperty$inboundSchema: z.ZodNativeEnum<
  typeof CheckoutLinkSortProperty
> = z.nativeEnum(CheckoutLinkSortProperty);

/** @internal */
export const CheckoutLinkSortProperty$outboundSchema: z.ZodNativeEnum<
  typeof CheckoutLinkSortProperty
> = CheckoutLinkSortProperty$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutLinkSortProperty$ {
  /** @deprecated use `CheckoutLinkSortProperty$inboundSchema` instead. */
  export const inboundSchema = CheckoutLinkSortProperty$inboundSchema;
  /** @deprecated use `CheckoutLinkSortProperty$outboundSchema` instead. */
  export const outboundSchema = CheckoutLinkSortProperty$outboundSchema;
}
