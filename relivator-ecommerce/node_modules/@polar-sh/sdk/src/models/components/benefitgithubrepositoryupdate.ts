/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitGitHubRepositoryCreateProperties,
  BenefitGitHubRepositoryCreateProperties$inboundSchema,
  BenefitGitHubRepositoryCreateProperties$Outbound,
  BenefitGitHubRepositoryCreateProperties$outboundSchema,
} from "./benefitgithubrepositorycreateproperties.js";

export type BenefitGitHubRepositoryUpdateMetadata =
  | string
  | number
  | number
  | boolean;

export type BenefitGitHubRepositoryUpdate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  /**
   * The description of the benefit. Will be displayed on products having this benefit.
   */
  description?: string | null | undefined;
  type?: "github_repository" | undefined;
  properties?: BenefitGitHubRepositoryCreateProperties | null | undefined;
};

/** @internal */
export const BenefitGitHubRepositoryUpdateMetadata$inboundSchema: z.ZodType<
  BenefitGitHubRepositoryUpdateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitGitHubRepositoryUpdateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitGitHubRepositoryUpdateMetadata$outboundSchema: z.ZodType<
  BenefitGitHubRepositoryUpdateMetadata$Outbound,
  z.ZodTypeDef,
  BenefitGitHubRepositoryUpdateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGitHubRepositoryUpdateMetadata$ {
  /** @deprecated use `BenefitGitHubRepositoryUpdateMetadata$inboundSchema` instead. */
  export const inboundSchema =
    BenefitGitHubRepositoryUpdateMetadata$inboundSchema;
  /** @deprecated use `BenefitGitHubRepositoryUpdateMetadata$outboundSchema` instead. */
  export const outboundSchema =
    BenefitGitHubRepositoryUpdateMetadata$outboundSchema;
  /** @deprecated use `BenefitGitHubRepositoryUpdateMetadata$Outbound` instead. */
  export type Outbound = BenefitGitHubRepositoryUpdateMetadata$Outbound;
}

export function benefitGitHubRepositoryUpdateMetadataToJSON(
  benefitGitHubRepositoryUpdateMetadata: BenefitGitHubRepositoryUpdateMetadata,
): string {
  return JSON.stringify(
    BenefitGitHubRepositoryUpdateMetadata$outboundSchema.parse(
      benefitGitHubRepositoryUpdateMetadata,
    ),
  );
}

export function benefitGitHubRepositoryUpdateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGitHubRepositoryUpdateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitGitHubRepositoryUpdateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGitHubRepositoryUpdateMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitGitHubRepositoryUpdate$inboundSchema: z.ZodType<
  BenefitGitHubRepositoryUpdate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  description: z.nullable(z.string()).optional(),
  type: z.literal("github_repository").optional(),
  properties: z.nullable(BenefitGitHubRepositoryCreateProperties$inboundSchema)
    .optional(),
});

/** @internal */
export type BenefitGitHubRepositoryUpdate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  description?: string | null | undefined;
  type: "github_repository";
  properties?:
    | BenefitGitHubRepositoryCreateProperties$Outbound
    | null
    | undefined;
};

/** @internal */
export const BenefitGitHubRepositoryUpdate$outboundSchema: z.ZodType<
  BenefitGitHubRepositoryUpdate$Outbound,
  z.ZodTypeDef,
  BenefitGitHubRepositoryUpdate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  description: z.nullable(z.string()).optional(),
  type: z.literal("github_repository").default("github_repository" as const),
  properties: z.nullable(BenefitGitHubRepositoryCreateProperties$outboundSchema)
    .optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGitHubRepositoryUpdate$ {
  /** @deprecated use `BenefitGitHubRepositoryUpdate$inboundSchema` instead. */
  export const inboundSchema = BenefitGitHubRepositoryUpdate$inboundSchema;
  /** @deprecated use `BenefitGitHubRepositoryUpdate$outboundSchema` instead. */
  export const outboundSchema = BenefitGitHubRepositoryUpdate$outboundSchema;
  /** @deprecated use `BenefitGitHubRepositoryUpdate$Outbound` instead. */
  export type Outbound = BenefitGitHubRepositoryUpdate$Outbound;
}

export function benefitGitHubRepositoryUpdateToJSON(
  benefitGitHubRepositoryUpdate: BenefitGitHubRepositoryUpdate,
): string {
  return JSON.stringify(
    BenefitGitHubRepositoryUpdate$outboundSchema.parse(
      benefitGitHubRepositoryUpdate,
    ),
  );
}

export function benefitGitHubRepositoryUpdateFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGitHubRepositoryUpdate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitGitHubRepositoryUpdate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGitHubRepositoryUpdate' from JSON`,
  );
}
