/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Customer,
  Customer$inboundSchema,
  Customer$Outbound,
  Customer$outboundSchema,
} from "./customer.js";
import {
  MeterResetMetadata,
  MeterResetMetadata$inboundSchema,
  MeterResetMetadata$Outbound,
  MeterResetMetadata$outboundSchema,
} from "./meterresetmetadata.js";

/**
 * An event created by <PERSON> when a customer meter is reset.
 */
export type MeterResetEvent = {
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * The timestamp of the event.
   */
  timestamp: Date;
  /**
   * The ID of the organization owning the event.
   */
  organizationId: string;
  /**
   * ID of the customer in your Polar organization associated with the event.
   */
  customerId: string | null;
  /**
   * The customer associated with the event.
   */
  customer: Customer | null;
  /**
   * ID of the customer in your system associated with the event.
   */
  externalCustomerId: string | null;
  /**
   * The source of the event. `system` events are created by Polar. `user` events are the one you create through our ingestion API.
   */
  source?: "system" | undefined;
  /**
   * The name of the event.
   */
  name?: "meter.reset" | undefined;
  metadata: MeterResetMetadata;
};

/** @internal */
export const MeterResetEvent$inboundSchema: z.ZodType<
  MeterResetEvent,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  timestamp: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  organization_id: z.string(),
  customer_id: z.nullable(z.string()),
  customer: z.nullable(Customer$inboundSchema),
  external_customer_id: z.nullable(z.string()),
  source: z.literal("system").optional(),
  name: z.literal("meter.reset").optional(),
  metadata: MeterResetMetadata$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
    "customer_id": "customerId",
    "external_customer_id": "externalCustomerId",
  });
});

/** @internal */
export type MeterResetEvent$Outbound = {
  id: string;
  timestamp: string;
  organization_id: string;
  customer_id: string | null;
  customer: Customer$Outbound | null;
  external_customer_id: string | null;
  source: "system";
  name: "meter.reset";
  metadata: MeterResetMetadata$Outbound;
};

/** @internal */
export const MeterResetEvent$outboundSchema: z.ZodType<
  MeterResetEvent$Outbound,
  z.ZodTypeDef,
  MeterResetEvent
> = z.object({
  id: z.string(),
  timestamp: z.date().transform(v => v.toISOString()),
  organizationId: z.string(),
  customerId: z.nullable(z.string()),
  customer: z.nullable(Customer$outboundSchema),
  externalCustomerId: z.nullable(z.string()),
  source: z.literal("system").default("system" as const),
  name: z.literal("meter.reset").default("meter.reset" as const),
  metadata: MeterResetMetadata$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
    customerId: "customer_id",
    externalCustomerId: "external_customer_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterResetEvent$ {
  /** @deprecated use `MeterResetEvent$inboundSchema` instead. */
  export const inboundSchema = MeterResetEvent$inboundSchema;
  /** @deprecated use `MeterResetEvent$outboundSchema` instead. */
  export const outboundSchema = MeterResetEvent$outboundSchema;
  /** @deprecated use `MeterResetEvent$Outbound` instead. */
  export type Outbound = MeterResetEvent$Outbound;
}

export function meterResetEventToJSON(
  meterResetEvent: MeterResetEvent,
): string {
  return JSON.stringify(MeterResetEvent$outboundSchema.parse(meterResetEvent));
}

export function meterResetEventFromJSON(
  jsonString: string,
): SafeParseResult<MeterResetEvent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterResetEvent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterResetEvent' from JSON`,
  );
}
