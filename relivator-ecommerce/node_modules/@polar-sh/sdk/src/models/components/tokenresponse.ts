/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type TokenResponse = {
  accessToken: string;
  tokenType?: "Bearer" | undefined;
  expiresIn: number;
  refreshToken: string | null;
  scope: string;
  idToken: string;
};

/** @internal */
export const TokenResponse$inboundSchema: z.ZodType<
  TokenResponse,
  z.ZodTypeDef,
  unknown
> = z.object({
  access_token: z.string(),
  token_type: z.literal("Bearer").optional(),
  expires_in: z.number().int(),
  refresh_token: z.nullable(z.string()),
  scope: z.string(),
  id_token: z.string(),
}).transform((v) => {
  return remap$(v, {
    "access_token": "accessToken",
    "token_type": "tokenType",
    "expires_in": "expiresIn",
    "refresh_token": "refreshToken",
    "id_token": "idToken",
  });
});

/** @internal */
export type TokenResponse$Outbound = {
  access_token: string;
  token_type: "Bearer";
  expires_in: number;
  refresh_token: string | null;
  scope: string;
  id_token: string;
};

/** @internal */
export const TokenResponse$outboundSchema: z.ZodType<
  TokenResponse$Outbound,
  z.ZodTypeDef,
  TokenResponse
> = z.object({
  accessToken: z.string(),
  tokenType: z.literal("Bearer").default("Bearer" as const),
  expiresIn: z.number().int(),
  refreshToken: z.nullable(z.string()),
  scope: z.string(),
  idToken: z.string(),
}).transform((v) => {
  return remap$(v, {
    accessToken: "access_token",
    tokenType: "token_type",
    expiresIn: "expires_in",
    refreshToken: "refresh_token",
    idToken: "id_token",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TokenResponse$ {
  /** @deprecated use `TokenResponse$inboundSchema` instead. */
  export const inboundSchema = TokenResponse$inboundSchema;
  /** @deprecated use `TokenResponse$outboundSchema` instead. */
  export const outboundSchema = TokenResponse$outboundSchema;
  /** @deprecated use `TokenResponse$Outbound` instead. */
  export type Outbound = TokenResponse$Outbound;
}

export function tokenResponseToJSON(tokenResponse: TokenResponse): string {
  return JSON.stringify(TokenResponse$outboundSchema.parse(tokenResponse));
}

export function tokenResponseFromJSON(
  jsonString: string,
): SafeParseResult<TokenResponse, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => TokenResponse$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'TokenResponse' from JSON`,
  );
}
