/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  SubscriptionRecurringInterval,
  SubscriptionRecurringInterval$inboundSchema,
  SubscriptionRecurringInterval$outboundSchema,
} from "./subscriptionrecurringinterval.js";

/**
 * A pay-what-you-want recurring price for a product, i.e. a subscription.
 *
 * @remarks
 *
 * **Deprecated**: The recurring interval should be set on the product itself.
 */
export type LegacyRecurringProductPriceCustom = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the price.
   */
  id: string;
  amountType?: "custom" | undefined;
  /**
   * Whether the price is archived and no longer available.
   */
  isArchived: boolean;
  /**
   * The ID of the product owning the price.
   */
  productId: string;
  /**
   * The type of the price.
   */
  type?: "recurring" | undefined;
  recurringInterval: SubscriptionRecurringInterval;
  /**
   * The currency.
   */
  priceCurrency: string;
  /**
   * The minimum amount the customer can pay.
   */
  minimumAmount: number | null;
  /**
   * The maximum amount the customer can pay.
   */
  maximumAmount: number | null;
  /**
   * The initial amount shown to the customer.
   */
  presetAmount: number | null;
  legacy?: true | undefined;
};

/** @internal */
export const LegacyRecurringProductPriceCustom$inboundSchema: z.ZodType<
  LegacyRecurringProductPriceCustom,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  amount_type: z.literal("custom").optional(),
  is_archived: z.boolean(),
  product_id: z.string(),
  type: z.literal("recurring").optional(),
  recurring_interval: SubscriptionRecurringInterval$inboundSchema,
  price_currency: z.string(),
  minimum_amount: z.nullable(z.number().int()),
  maximum_amount: z.nullable(z.number().int()),
  preset_amount: z.nullable(z.number().int()),
  legacy: z.literal(true).optional(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "amount_type": "amountType",
    "is_archived": "isArchived",
    "product_id": "productId",
    "recurring_interval": "recurringInterval",
    "price_currency": "priceCurrency",
    "minimum_amount": "minimumAmount",
    "maximum_amount": "maximumAmount",
    "preset_amount": "presetAmount",
  });
});

/** @internal */
export type LegacyRecurringProductPriceCustom$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  amount_type: "custom";
  is_archived: boolean;
  product_id: string;
  type: "recurring";
  recurring_interval: string;
  price_currency: string;
  minimum_amount: number | null;
  maximum_amount: number | null;
  preset_amount: number | null;
  legacy: true;
};

/** @internal */
export const LegacyRecurringProductPriceCustom$outboundSchema: z.ZodType<
  LegacyRecurringProductPriceCustom$Outbound,
  z.ZodTypeDef,
  LegacyRecurringProductPriceCustom
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  amountType: z.literal("custom").default("custom" as const),
  isArchived: z.boolean(),
  productId: z.string(),
  type: z.literal("recurring").default("recurring" as const),
  recurringInterval: SubscriptionRecurringInterval$outboundSchema,
  priceCurrency: z.string(),
  minimumAmount: z.nullable(z.number().int()),
  maximumAmount: z.nullable(z.number().int()),
  presetAmount: z.nullable(z.number().int()),
  legacy: z.literal(true).default(true as const),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    amountType: "amount_type",
    isArchived: "is_archived",
    productId: "product_id",
    recurringInterval: "recurring_interval",
    priceCurrency: "price_currency",
    minimumAmount: "minimum_amount",
    maximumAmount: "maximum_amount",
    presetAmount: "preset_amount",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LegacyRecurringProductPriceCustom$ {
  /** @deprecated use `LegacyRecurringProductPriceCustom$inboundSchema` instead. */
  export const inboundSchema = LegacyRecurringProductPriceCustom$inboundSchema;
  /** @deprecated use `LegacyRecurringProductPriceCustom$outboundSchema` instead. */
  export const outboundSchema =
    LegacyRecurringProductPriceCustom$outboundSchema;
  /** @deprecated use `LegacyRecurringProductPriceCustom$Outbound` instead. */
  export type Outbound = LegacyRecurringProductPriceCustom$Outbound;
}

export function legacyRecurringProductPriceCustomToJSON(
  legacyRecurringProductPriceCustom: LegacyRecurringProductPriceCustom,
): string {
  return JSON.stringify(
    LegacyRecurringProductPriceCustom$outboundSchema.parse(
      legacyRecurringProductPriceCustom,
    ),
  );
}

export function legacyRecurringProductPriceCustomFromJSON(
  jsonString: string,
): SafeParseResult<LegacyRecurringProductPriceCustom, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LegacyRecurringProductPriceCustom$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LegacyRecurringProductPriceCustom' from JSON`,
  );
}
