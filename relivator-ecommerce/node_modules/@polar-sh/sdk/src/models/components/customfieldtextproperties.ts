/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type CustomFieldTextProperties = {
  formLabel?: string | undefined;
  formHelpText?: string | undefined;
  formPlaceholder?: string | undefined;
  textarea?: boolean | undefined;
  minLength?: number | undefined;
  maxLength?: number | undefined;
};

/** @internal */
export const CustomFieldTextProperties$inboundSchema: z.ZodType<
  CustomFieldTextProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  form_label: z.string().optional(),
  form_help_text: z.string().optional(),
  form_placeholder: z.string().optional(),
  textarea: z.boolean().optional(),
  min_length: z.number().int().optional(),
  max_length: z.number().int().optional(),
}).transform((v) => {
  return remap$(v, {
    "form_label": "formLabel",
    "form_help_text": "formHelpText",
    "form_placeholder": "formPlaceholder",
    "min_length": "minLength",
    "max_length": "maxLength",
  });
});

/** @internal */
export type CustomFieldTextProperties$Outbound = {
  form_label?: string | undefined;
  form_help_text?: string | undefined;
  form_placeholder?: string | undefined;
  textarea?: boolean | undefined;
  min_length?: number | undefined;
  max_length?: number | undefined;
};

/** @internal */
export const CustomFieldTextProperties$outboundSchema: z.ZodType<
  CustomFieldTextProperties$Outbound,
  z.ZodTypeDef,
  CustomFieldTextProperties
> = z.object({
  formLabel: z.string().optional(),
  formHelpText: z.string().optional(),
  formPlaceholder: z.string().optional(),
  textarea: z.boolean().optional(),
  minLength: z.number().int().optional(),
  maxLength: z.number().int().optional(),
}).transform((v) => {
  return remap$(v, {
    formLabel: "form_label",
    formHelpText: "form_help_text",
    formPlaceholder: "form_placeholder",
    minLength: "min_length",
    maxLength: "max_length",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldTextProperties$ {
  /** @deprecated use `CustomFieldTextProperties$inboundSchema` instead. */
  export const inboundSchema = CustomFieldTextProperties$inboundSchema;
  /** @deprecated use `CustomFieldTextProperties$outboundSchema` instead. */
  export const outboundSchema = CustomFieldTextProperties$outboundSchema;
  /** @deprecated use `CustomFieldTextProperties$Outbound` instead. */
  export type Outbound = CustomFieldTextProperties$Outbound;
}

export function customFieldTextPropertiesToJSON(
  customFieldTextProperties: CustomFieldTextProperties,
): string {
  return JSON.stringify(
    CustomFieldTextProperties$outboundSchema.parse(customFieldTextProperties),
  );
}

export function customFieldTextPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldTextProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldTextProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldTextProperties' from JSON`,
  );
}
