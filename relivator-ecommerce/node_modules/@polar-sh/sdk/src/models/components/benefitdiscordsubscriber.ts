/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitDiscordSubscriberProperties,
  BenefitDiscordSubscriberProperties$inboundSchema,
  BenefitDiscordSubscriberProperties$Outbound,
  BenefitDiscordSubscriberProperties$outboundSchema,
} from "./benefitdiscordsubscriberproperties.js";
import {
  Organization,
  Organization$inboundSchema,
  Organization$Outbound,
  Organization$outboundSchema,
} from "./organization.js";

export type BenefitDiscordSubscriberMetadata =
  | string
  | number
  | number
  | boolean;

export type BenefitDiscordSubscriber = {
  /**
   * The ID of the benefit.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  type?: "discord" | undefined;
  /**
   * The description of the benefit.
   */
  description: string;
  /**
   * Whether the benefit is selectable when creating a product.
   */
  selectable: boolean;
  /**
   * Whether the benefit is deletable.
   */
  deletable: boolean;
  /**
   * The ID of the organization owning the benefit.
   */
  organizationId: string;
  metadata: { [k: string]: string | number | number | boolean };
  organization: Organization;
  /**
   * Properties available to subscribers for a benefit of type `discord`.
   */
  properties: BenefitDiscordSubscriberProperties;
};

/** @internal */
export const BenefitDiscordSubscriberMetadata$inboundSchema: z.ZodType<
  BenefitDiscordSubscriberMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitDiscordSubscriberMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitDiscordSubscriberMetadata$outboundSchema: z.ZodType<
  BenefitDiscordSubscriberMetadata$Outbound,
  z.ZodTypeDef,
  BenefitDiscordSubscriberMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDiscordSubscriberMetadata$ {
  /** @deprecated use `BenefitDiscordSubscriberMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitDiscordSubscriberMetadata$inboundSchema;
  /** @deprecated use `BenefitDiscordSubscriberMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitDiscordSubscriberMetadata$outboundSchema;
  /** @deprecated use `BenefitDiscordSubscriberMetadata$Outbound` instead. */
  export type Outbound = BenefitDiscordSubscriberMetadata$Outbound;
}

export function benefitDiscordSubscriberMetadataToJSON(
  benefitDiscordSubscriberMetadata: BenefitDiscordSubscriberMetadata,
): string {
  return JSON.stringify(
    BenefitDiscordSubscriberMetadata$outboundSchema.parse(
      benefitDiscordSubscriberMetadata,
    ),
  );
}

export function benefitDiscordSubscriberMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitDiscordSubscriberMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitDiscordSubscriberMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitDiscordSubscriberMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitDiscordSubscriber$inboundSchema: z.ZodType<
  BenefitDiscordSubscriber,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  type: z.literal("discord").optional(),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organization_id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  organization: Organization$inboundSchema,
  properties: BenefitDiscordSubscriberProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitDiscordSubscriber$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  type: "discord";
  description: string;
  selectable: boolean;
  deletable: boolean;
  organization_id: string;
  metadata: { [k: string]: string | number | number | boolean };
  organization: Organization$Outbound;
  properties: BenefitDiscordSubscriberProperties$Outbound;
};

/** @internal */
export const BenefitDiscordSubscriber$outboundSchema: z.ZodType<
  BenefitDiscordSubscriber$Outbound,
  z.ZodTypeDef,
  BenefitDiscordSubscriber
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  type: z.literal("discord").default("discord" as const),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organizationId: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  organization: Organization$outboundSchema,
  properties: BenefitDiscordSubscriberProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDiscordSubscriber$ {
  /** @deprecated use `BenefitDiscordSubscriber$inboundSchema` instead. */
  export const inboundSchema = BenefitDiscordSubscriber$inboundSchema;
  /** @deprecated use `BenefitDiscordSubscriber$outboundSchema` instead. */
  export const outboundSchema = BenefitDiscordSubscriber$outboundSchema;
  /** @deprecated use `BenefitDiscordSubscriber$Outbound` instead. */
  export type Outbound = BenefitDiscordSubscriber$Outbound;
}

export function benefitDiscordSubscriberToJSON(
  benefitDiscordSubscriber: BenefitDiscordSubscriber,
): string {
  return JSON.stringify(
    BenefitDiscordSubscriber$outboundSchema.parse(benefitDiscordSubscriber),
  );
}

export function benefitDiscordSubscriberFromJSON(
  jsonString: string,
): SafeParseResult<BenefitDiscordSubscriber, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitDiscordSubscriber$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitDiscordSubscriber' from JSON`,
  );
}
