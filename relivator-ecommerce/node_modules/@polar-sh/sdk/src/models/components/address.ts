/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type Address = {
  line1?: string | null | undefined;
  line2?: string | null | undefined;
  postalCode?: string | null | undefined;
  city?: string | null | undefined;
  state?: string | null | undefined;
  country: string;
};

/** @internal */
export const Address$inboundSchema: z.ZodType<Address, z.ZodTypeDef, unknown> =
  z.object({
    line1: z.nullable(z.string()).optional(),
    line2: z.nullable(z.string()).optional(),
    postal_code: z.nullable(z.string()).optional(),
    city: z.nullable(z.string()).optional(),
    state: z.nullable(z.string()).optional(),
    country: z.string(),
  }).transform((v) => {
    return remap$(v, {
      "postal_code": "postalCode",
    });
  });

/** @internal */
export type Address$Outbound = {
  line1?: string | null | undefined;
  line2?: string | null | undefined;
  postal_code?: string | null | undefined;
  city?: string | null | undefined;
  state?: string | null | undefined;
  country: string;
};

/** @internal */
export const Address$outboundSchema: z.ZodType<
  Address$Outbound,
  z.ZodTypeDef,
  Address
> = z.object({
  line1: z.nullable(z.string()).optional(),
  line2: z.nullable(z.string()).optional(),
  postalCode: z.nullable(z.string()).optional(),
  city: z.nullable(z.string()).optional(),
  state: z.nullable(z.string()).optional(),
  country: z.string(),
}).transform((v) => {
  return remap$(v, {
    postalCode: "postal_code",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Address$ {
  /** @deprecated use `Address$inboundSchema` instead. */
  export const inboundSchema = Address$inboundSchema;
  /** @deprecated use `Address$outboundSchema` instead. */
  export const outboundSchema = Address$outboundSchema;
  /** @deprecated use `Address$Outbound` instead. */
  export type Outbound = Address$Outbound;
}

export function addressToJSON(address: Address): string {
  return JSON.stringify(Address$outboundSchema.parse(address));
}

export function addressFromJSON(
  jsonString: string,
): SafeParseResult<Address, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Address$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Address' from JSON`,
  );
}
