/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type Conditions = string | number | number | boolean;

export type LicenseKeyValidate = {
  key: string;
  organizationId: string;
  activationId?: string | null | undefined;
  benefitId?: string | null | undefined;
  customerId?: string | null | undefined;
  incrementUsage?: number | null | undefined;
  /**
   * Key-value object allowing you to set conditions that must match when validating the license key.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  conditions?: { [k: string]: string | number | number | boolean } | undefined;
};

/** @internal */
export const Conditions$inboundSchema: z.ZodType<
  Conditions,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type Conditions$Outbound = string | number | number | boolean;

/** @internal */
export const Conditions$outboundSchema: z.ZodType<
  Conditions$Outbound,
  z.ZodTypeDef,
  Conditions
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Conditions$ {
  /** @deprecated use `Conditions$inboundSchema` instead. */
  export const inboundSchema = Conditions$inboundSchema;
  /** @deprecated use `Conditions$outboundSchema` instead. */
  export const outboundSchema = Conditions$outboundSchema;
  /** @deprecated use `Conditions$Outbound` instead. */
  export type Outbound = Conditions$Outbound;
}

export function conditionsToJSON(conditions: Conditions): string {
  return JSON.stringify(Conditions$outboundSchema.parse(conditions));
}

export function conditionsFromJSON(
  jsonString: string,
): SafeParseResult<Conditions, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Conditions$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Conditions' from JSON`,
  );
}

/** @internal */
export const LicenseKeyValidate$inboundSchema: z.ZodType<
  LicenseKeyValidate,
  z.ZodTypeDef,
  unknown
> = z.object({
  key: z.string(),
  organization_id: z.string(),
  activation_id: z.nullable(z.string()).optional(),
  benefit_id: z.nullable(z.string()).optional(),
  customer_id: z.nullable(z.string()).optional(),
  increment_usage: z.nullable(z.number().int()).optional(),
  conditions: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
    "activation_id": "activationId",
    "benefit_id": "benefitId",
    "customer_id": "customerId",
    "increment_usage": "incrementUsage",
  });
});

/** @internal */
export type LicenseKeyValidate$Outbound = {
  key: string;
  organization_id: string;
  activation_id?: string | null | undefined;
  benefit_id?: string | null | undefined;
  customer_id?: string | null | undefined;
  increment_usage?: number | null | undefined;
  conditions?: { [k: string]: string | number | number | boolean } | undefined;
};

/** @internal */
export const LicenseKeyValidate$outboundSchema: z.ZodType<
  LicenseKeyValidate$Outbound,
  z.ZodTypeDef,
  LicenseKeyValidate
> = z.object({
  key: z.string(),
  organizationId: z.string(),
  activationId: z.nullable(z.string()).optional(),
  benefitId: z.nullable(z.string()).optional(),
  customerId: z.nullable(z.string()).optional(),
  incrementUsage: z.nullable(z.number().int()).optional(),
  conditions: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
    activationId: "activation_id",
    benefitId: "benefit_id",
    customerId: "customer_id",
    incrementUsage: "increment_usage",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LicenseKeyValidate$ {
  /** @deprecated use `LicenseKeyValidate$inboundSchema` instead. */
  export const inboundSchema = LicenseKeyValidate$inboundSchema;
  /** @deprecated use `LicenseKeyValidate$outboundSchema` instead. */
  export const outboundSchema = LicenseKeyValidate$outboundSchema;
  /** @deprecated use `LicenseKeyValidate$Outbound` instead. */
  export type Outbound = LicenseKeyValidate$Outbound;
}

export function licenseKeyValidateToJSON(
  licenseKeyValidate: LicenseKeyValidate,
): string {
  return JSON.stringify(
    LicenseKeyValidate$outboundSchema.parse(licenseKeyValidate),
  );
}

export function licenseKeyValidateFromJSON(
  jsonString: string,
): SafeParseResult<LicenseKeyValidate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LicenseKeyValidate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LicenseKeyValidate' from JSON`,
  );
}
