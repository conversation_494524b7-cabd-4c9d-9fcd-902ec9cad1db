/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type AuthorizeOrganization = {
  id: string;
  slug: string;
  avatarUrl: string | null;
};

/** @internal */
export const AuthorizeOrganization$inboundSchema: z.ZodType<
  AuthorizeOrganization,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  slug: z.string(),
  avatar_url: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    "avatar_url": "avatarUrl",
  });
});

/** @internal */
export type AuthorizeOrganization$Outbound = {
  id: string;
  slug: string;
  avatar_url: string | null;
};

/** @internal */
export const AuthorizeOrganization$outboundSchema: z.ZodType<
  AuthorizeOrganization$Outbound,
  z.ZodTypeDef,
  AuthorizeOrganization
> = z.object({
  id: z.string(),
  slug: z.string(),
  avatarUrl: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    avatarUrl: "avatar_url",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace AuthorizeOrganization$ {
  /** @deprecated use `AuthorizeOrganization$inboundSchema` instead. */
  export const inboundSchema = AuthorizeOrganization$inboundSchema;
  /** @deprecated use `AuthorizeOrganization$outboundSchema` instead. */
  export const outboundSchema = AuthorizeOrganization$outboundSchema;
  /** @deprecated use `AuthorizeOrganization$Outbound` instead. */
  export type Outbound = AuthorizeOrganization$Outbound;
}

export function authorizeOrganizationToJSON(
  authorizeOrganization: AuthorizeOrganization,
): string {
  return JSON.stringify(
    AuthorizeOrganization$outboundSchema.parse(authorizeOrganization),
  );
}

export function authorizeOrganizationFromJSON(
  jsonString: string,
): SafeParseResult<AuthorizeOrganization, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => AuthorizeOrganization$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'AuthorizeOrganization' from JSON`,
  );
}
