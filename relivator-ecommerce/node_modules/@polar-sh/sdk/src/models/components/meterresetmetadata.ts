/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type MeterResetMetadata = {
  meterId: string;
};

/** @internal */
export const MeterResetMetadata$inboundSchema: z.ZodType<
  MeterResetMetadata,
  z.ZodTypeDef,
  unknown
> = z.object({
  meter_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "meter_id": "meterId",
  });
});

/** @internal */
export type MeterResetMetadata$Outbound = {
  meter_id: string;
};

/** @internal */
export const MeterResetMetadata$outboundSchema: z.ZodType<
  MeterResetMetadata$Outbound,
  z.ZodTypeDef,
  MeterResetMetadata
> = z.object({
  meterId: z.string(),
}).transform((v) => {
  return remap$(v, {
    meterId: "meter_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterResetMetadata$ {
  /** @deprecated use `MeterResetMetadata$inboundSchema` instead. */
  export const inboundSchema = MeterResetMetadata$inboundSchema;
  /** @deprecated use `MeterResetMetadata$outboundSchema` instead. */
  export const outboundSchema = MeterResetMetadata$outboundSchema;
  /** @deprecated use `MeterResetMetadata$Outbound` instead. */
  export type Outbound = MeterResetMetadata$Outbound;
}

export function meterResetMetadataToJSON(
  meterResetMetadata: MeterResetMetadata,
): string {
  return JSON.stringify(
    MeterResetMetadata$outboundSchema.parse(meterResetMetadata),
  );
}

export function meterResetMetadataFromJSON(
  jsonString: string,
): SafeParseResult<MeterResetMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterResetMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterResetMetadata' from JSON`,
  );
}
