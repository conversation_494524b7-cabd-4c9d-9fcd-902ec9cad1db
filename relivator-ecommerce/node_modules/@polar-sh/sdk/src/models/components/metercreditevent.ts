/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Customer,
  Customer$inboundSchema,
  Customer$Outbound,
  Customer$outboundSchema,
} from "./customer.js";
import {
  MeterCreditedMetadata,
  MeterCreditedMetadata$inboundSchema,
  MeterCreditedMetadata$Outbound,
  MeterCreditedMetadata$outboundSchema,
} from "./metercreditedmetadata.js";

/**
 * An event created by <PERSON> when credits are added to a customer meter.
 */
export type MeterCreditEvent = {
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * The timestamp of the event.
   */
  timestamp: Date;
  /**
   * The ID of the organization owning the event.
   */
  organizationId: string;
  /**
   * ID of the customer in your Polar organization associated with the event.
   */
  customerId: string | null;
  /**
   * The customer associated with the event.
   */
  customer: Customer | null;
  /**
   * ID of the customer in your system associated with the event.
   */
  externalCustomerId: string | null;
  /**
   * The source of the event. `system` events are created by Polar. `user` events are the one you create through our ingestion API.
   */
  source?: "system" | undefined;
  /**
   * The name of the event.
   */
  name?: "meter.credited" | undefined;
  metadata: MeterCreditedMetadata;
};

/** @internal */
export const MeterCreditEvent$inboundSchema: z.ZodType<
  MeterCreditEvent,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  timestamp: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  organization_id: z.string(),
  customer_id: z.nullable(z.string()),
  customer: z.nullable(Customer$inboundSchema),
  external_customer_id: z.nullable(z.string()),
  source: z.literal("system").optional(),
  name: z.literal("meter.credited").optional(),
  metadata: MeterCreditedMetadata$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
    "customer_id": "customerId",
    "external_customer_id": "externalCustomerId",
  });
});

/** @internal */
export type MeterCreditEvent$Outbound = {
  id: string;
  timestamp: string;
  organization_id: string;
  customer_id: string | null;
  customer: Customer$Outbound | null;
  external_customer_id: string | null;
  source: "system";
  name: "meter.credited";
  metadata: MeterCreditedMetadata$Outbound;
};

/** @internal */
export const MeterCreditEvent$outboundSchema: z.ZodType<
  MeterCreditEvent$Outbound,
  z.ZodTypeDef,
  MeterCreditEvent
> = z.object({
  id: z.string(),
  timestamp: z.date().transform(v => v.toISOString()),
  organizationId: z.string(),
  customerId: z.nullable(z.string()),
  customer: z.nullable(Customer$outboundSchema),
  externalCustomerId: z.nullable(z.string()),
  source: z.literal("system").default("system" as const),
  name: z.literal("meter.credited").default("meter.credited" as const),
  metadata: MeterCreditedMetadata$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
    customerId: "customer_id",
    externalCustomerId: "external_customer_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterCreditEvent$ {
  /** @deprecated use `MeterCreditEvent$inboundSchema` instead. */
  export const inboundSchema = MeterCreditEvent$inboundSchema;
  /** @deprecated use `MeterCreditEvent$outboundSchema` instead. */
  export const outboundSchema = MeterCreditEvent$outboundSchema;
  /** @deprecated use `MeterCreditEvent$Outbound` instead. */
  export type Outbound = MeterCreditEvent$Outbound;
}

export function meterCreditEventToJSON(
  meterCreditEvent: MeterCreditEvent,
): string {
  return JSON.stringify(
    MeterCreditEvent$outboundSchema.parse(meterCreditEvent),
  );
}

export function meterCreditEventFromJSON(
  jsonString: string,
): SafeParseResult<MeterCreditEvent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterCreditEvent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterCreditEvent' from JSON`,
  );
}
