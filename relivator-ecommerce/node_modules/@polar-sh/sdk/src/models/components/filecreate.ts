/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DownloadableFileCreate,
  DownloadableFileCreate$inboundSchema,
  DownloadableFileCreate$Outbound,
  DownloadableFileCreate$outboundSchema,
} from "./downloadablefilecreate.js";
import {
  OrganizationAvatarFileCreate,
  OrganizationAvatarFileCreate$inboundSchema,
  OrganizationAvatarFileCreate$Outbound,
  OrganizationAvatarFileCreate$outboundSchema,
} from "./organizationavatarfilecreate.js";
import {
  ProductMediaFileCreate,
  ProductMediaFileCreate$inboundSchema,
  ProductMediaFileCreate$Outbound,
  ProductMediaFileCreate$outboundSchema,
} from "./productmediafilecreate.js";

export type FileCreate =
  | (DownloadableFileCreate & { service: "downloadable" })
  | (OrganizationAvatarFileCreate & { service: "organization_avatar" })
  | (ProductMediaFileCreate & { service: "product_media" });

/** @internal */
export const FileCreate$inboundSchema: z.ZodType<
  FileCreate,
  z.ZodTypeDef,
  unknown
> = z.union([
  DownloadableFileCreate$inboundSchema.and(
    z.object({ service: z.literal("downloadable") }).transform((v) => ({
      service: v.service,
    })),
  ),
  OrganizationAvatarFileCreate$inboundSchema.and(
    z.object({ service: z.literal("organization_avatar") }).transform((v) => ({
      service: v.service,
    })),
  ),
  ProductMediaFileCreate$inboundSchema.and(
    z.object({ service: z.literal("product_media") }).transform((v) => ({
      service: v.service,
    })),
  ),
]);

/** @internal */
export type FileCreate$Outbound =
  | (DownloadableFileCreate$Outbound & { service: "downloadable" })
  | (OrganizationAvatarFileCreate$Outbound & { service: "organization_avatar" })
  | (ProductMediaFileCreate$Outbound & { service: "product_media" });

/** @internal */
export const FileCreate$outboundSchema: z.ZodType<
  FileCreate$Outbound,
  z.ZodTypeDef,
  FileCreate
> = z.union([
  DownloadableFileCreate$outboundSchema.and(
    z.object({ service: z.literal("downloadable") }).transform((v) => ({
      service: v.service,
    })),
  ),
  OrganizationAvatarFileCreate$outboundSchema.and(
    z.object({ service: z.literal("organization_avatar") }).transform((v) => ({
      service: v.service,
    })),
  ),
  ProductMediaFileCreate$outboundSchema.and(
    z.object({ service: z.literal("product_media") }).transform((v) => ({
      service: v.service,
    })),
  ),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FileCreate$ {
  /** @deprecated use `FileCreate$inboundSchema` instead. */
  export const inboundSchema = FileCreate$inboundSchema;
  /** @deprecated use `FileCreate$outboundSchema` instead. */
  export const outboundSchema = FileCreate$outboundSchema;
  /** @deprecated use `FileCreate$Outbound` instead. */
  export type Outbound = FileCreate$Outbound;
}

export function fileCreateToJSON(fileCreate: FileCreate): string {
  return JSON.stringify(FileCreate$outboundSchema.parse(fileCreate));
}

export function fileCreateFromJSON(
  jsonString: string,
): SafeParseResult<FileCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FileCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FileCreate' from JSON`,
  );
}
