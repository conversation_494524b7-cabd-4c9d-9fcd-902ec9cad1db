/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Schema to create a pay-what-you-want price.
 */
export type ProductPriceCustomCreate = {
  amountType?: "custom" | undefined;
  /**
   * The currency. Currently, only `usd` is supported.
   */
  priceCurrency?: string | undefined;
  /**
   * The minimum amount the customer can pay.
   */
  minimumAmount?: number | null | undefined;
  /**
   * The maximum amount the customer can pay.
   */
  maximumAmount?: number | null | undefined;
  /**
   * The initial amount shown to the customer.
   */
  presetAmount?: number | null | undefined;
};

/** @internal */
export const ProductPriceCustomCreate$inboundSchema: z.ZodType<
  ProductPriceCustomCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  amount_type: z.literal("custom").optional(),
  price_currency: z.string().default("usd"),
  minimum_amount: z.nullable(z.number().int()).optional(),
  maximum_amount: z.nullable(z.number().int()).optional(),
  preset_amount: z.nullable(z.number().int()).optional(),
}).transform((v) => {
  return remap$(v, {
    "amount_type": "amountType",
    "price_currency": "priceCurrency",
    "minimum_amount": "minimumAmount",
    "maximum_amount": "maximumAmount",
    "preset_amount": "presetAmount",
  });
});

/** @internal */
export type ProductPriceCustomCreate$Outbound = {
  amount_type: "custom";
  price_currency: string;
  minimum_amount?: number | null | undefined;
  maximum_amount?: number | null | undefined;
  preset_amount?: number | null | undefined;
};

/** @internal */
export const ProductPriceCustomCreate$outboundSchema: z.ZodType<
  ProductPriceCustomCreate$Outbound,
  z.ZodTypeDef,
  ProductPriceCustomCreate
> = z.object({
  amountType: z.literal("custom").default("custom" as const),
  priceCurrency: z.string().default("usd"),
  minimumAmount: z.nullable(z.number().int()).optional(),
  maximumAmount: z.nullable(z.number().int()).optional(),
  presetAmount: z.nullable(z.number().int()).optional(),
}).transform((v) => {
  return remap$(v, {
    amountType: "amount_type",
    priceCurrency: "price_currency",
    minimumAmount: "minimum_amount",
    maximumAmount: "maximum_amount",
    presetAmount: "preset_amount",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ProductPriceCustomCreate$ {
  /** @deprecated use `ProductPriceCustomCreate$inboundSchema` instead. */
  export const inboundSchema = ProductPriceCustomCreate$inboundSchema;
  /** @deprecated use `ProductPriceCustomCreate$outboundSchema` instead. */
  export const outboundSchema = ProductPriceCustomCreate$outboundSchema;
  /** @deprecated use `ProductPriceCustomCreate$Outbound` instead. */
  export type Outbound = ProductPriceCustomCreate$Outbound;
}

export function productPriceCustomCreateToJSON(
  productPriceCustomCreate: ProductPriceCustomCreate,
): string {
  return JSON.stringify(
    ProductPriceCustomCreate$outboundSchema.parse(productPriceCustomCreate),
  );
}

export function productPriceCustomCreateFromJSON(
  jsonString: string,
): SafeParseResult<ProductPriceCustomCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ProductPriceCustomCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ProductPriceCustomCreate' from JSON`,
  );
}
