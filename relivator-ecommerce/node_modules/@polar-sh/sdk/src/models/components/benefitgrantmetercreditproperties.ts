/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type BenefitGrantMeterCreditProperties = {
  lastCreditedMeterId: string;
  lastCreditedUnits: number;
  lastCreditedAt: string;
};

/** @internal */
export const BenefitGrantMeterCreditProperties$inboundSchema: z.ZodType<
  BenefitGrantMeterCreditProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  last_credited_meter_id: z.string(),
  last_credited_units: z.number().int(),
  last_credited_at: z.string(),
}).transform((v) => {
  return remap$(v, {
    "last_credited_meter_id": "lastCreditedMeterId",
    "last_credited_units": "lastCreditedUnits",
    "last_credited_at": "lastCreditedAt",
  });
});

/** @internal */
export type BenefitGrantMeterCreditProperties$Outbound = {
  last_credited_meter_id: string;
  last_credited_units: number;
  last_credited_at: string;
};

/** @internal */
export const BenefitGrantMeterCreditProperties$outboundSchema: z.ZodType<
  BenefitGrantMeterCreditProperties$Outbound,
  z.ZodTypeDef,
  BenefitGrantMeterCreditProperties
> = z.object({
  lastCreditedMeterId: z.string(),
  lastCreditedUnits: z.number().int(),
  lastCreditedAt: z.string(),
}).transform((v) => {
  return remap$(v, {
    lastCreditedMeterId: "last_credited_meter_id",
    lastCreditedUnits: "last_credited_units",
    lastCreditedAt: "last_credited_at",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGrantMeterCreditProperties$ {
  /** @deprecated use `BenefitGrantMeterCreditProperties$inboundSchema` instead. */
  export const inboundSchema = BenefitGrantMeterCreditProperties$inboundSchema;
  /** @deprecated use `BenefitGrantMeterCreditProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitGrantMeterCreditProperties$outboundSchema;
  /** @deprecated use `BenefitGrantMeterCreditProperties$Outbound` instead. */
  export type Outbound = BenefitGrantMeterCreditProperties$Outbound;
}

export function benefitGrantMeterCreditPropertiesToJSON(
  benefitGrantMeterCreditProperties: BenefitGrantMeterCreditProperties,
): string {
  return JSON.stringify(
    BenefitGrantMeterCreditProperties$outboundSchema.parse(
      benefitGrantMeterCreditProperties,
    ),
  );
}

export function benefitGrantMeterCreditPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGrantMeterCreditProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitGrantMeterCreditProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGrantMeterCreditProperties' from JSON`,
  );
}
