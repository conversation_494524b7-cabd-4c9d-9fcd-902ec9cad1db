/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Address,
  Address$inboundSchema,
  Address$Outbound,
  Address$outboundSchema,
} from "./address.js";
import {
  DiscountFixedOnceForeverDurationBase,
  DiscountFixedOnceForeverDurationBase$inboundSchema,
  DiscountFixedOnceForeverDurationBase$Outbound,
  DiscountFixedOnceForeverDurationBase$outboundSchema,
} from "./discountfixedonceforeverdurationbase.js";
import {
  DiscountFixedRepeatDurationBase,
  DiscountFixedRepeatDurationBase$inboundSchema,
  DiscountFixedRepeatDurationBase$Outbound,
  DiscountFixedRepeatDurationBase$outboundSchema,
} from "./discountfixedrepeatdurationbase.js";
import {
  DiscountPercentageOnceForeverDurationBase,
  DiscountPercentageOnceForeverDurationBase$inboundSchema,
  DiscountPercentageOnceForeverDurationBase$Outbound,
  DiscountPercentageOnceForeverDurationBase$outboundSchema,
} from "./discountpercentageonceforeverdurationbase.js";
import {
  DiscountPercentageRepeatDurationBase,
  DiscountPercentageRepeatDurationBase$inboundSchema,
  DiscountPercentageRepeatDurationBase$Outbound,
  DiscountPercentageRepeatDurationBase$outboundSchema,
} from "./discountpercentagerepeatdurationbase.js";
import {
  OrderBillingReason,
  OrderBillingReason$inboundSchema,
  OrderBillingReason$outboundSchema,
} from "./orderbillingreason.js";
import {
  OrderCustomer,
  OrderCustomer$inboundSchema,
  OrderCustomer$Outbound,
  OrderCustomer$outboundSchema,
} from "./ordercustomer.js";
import {
  OrderItemSchema,
  OrderItemSchema$inboundSchema,
  OrderItemSchema$Outbound,
  OrderItemSchema$outboundSchema,
} from "./orderitemschema.js";
import {
  OrderProduct,
  OrderProduct$inboundSchema,
  OrderProduct$Outbound,
  OrderProduct$outboundSchema,
} from "./orderproduct.js";
import {
  OrderStatus,
  OrderStatus$inboundSchema,
  OrderStatus$outboundSchema,
} from "./orderstatus.js";
import {
  OrderSubscription,
  OrderSubscription$inboundSchema,
  OrderSubscription$Outbound,
  OrderSubscription$outboundSchema,
} from "./ordersubscription.js";

export type OrderMetadata = string | number | number | boolean;

export type OrderCustomFieldData = string | number | boolean | Date;

export type OrderDiscount =
  | DiscountPercentageOnceForeverDurationBase
  | DiscountFixedOnceForeverDurationBase
  | DiscountPercentageRepeatDurationBase
  | DiscountFixedRepeatDurationBase;

export type Order = {
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  status: OrderStatus;
  /**
   * Whether the order has been paid for.
   */
  paid: boolean;
  /**
   * Amount in cents, before discounts and taxes.
   */
  subtotalAmount: number;
  /**
   * Discount amount in cents.
   */
  discountAmount: number;
  /**
   * Amount in cents, after discounts but before taxes.
   */
  netAmount: number;
  /**
   * Amount in cents, after discounts but before taxes.
   *
   * @deprecated field: This will be removed in a future release, please migrate away from it as soon as possible.
   */
  amount: number;
  /**
   * Sales tax amount in cents.
   */
  taxAmount: number;
  /**
   * Amount in cents, after discounts and taxes.
   */
  totalAmount: number;
  /**
   * Amount refunded in cents.
   */
  refundedAmount: number;
  /**
   * Sales tax refunded in cents.
   */
  refundedTaxAmount: number;
  currency: string;
  billingReason: OrderBillingReason;
  billingAddress: Address | null;
  customerId: string;
  productId: string;
  discountId: string | null;
  subscriptionId: string | null;
  checkoutId: string | null;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Key-value object storing custom field values.
   */
  customFieldData?:
    | { [k: string]: string | number | boolean | Date | null }
    | undefined;
  customer: OrderCustomer;
  /**
   * @deprecated field: This will be removed in a future release, please migrate away from it as soon as possible.
   */
  userId: string;
  product: OrderProduct;
  discount:
    | DiscountPercentageOnceForeverDurationBase
    | DiscountFixedOnceForeverDurationBase
    | DiscountPercentageRepeatDurationBase
    | DiscountFixedRepeatDurationBase
    | null;
  subscription: OrderSubscription | null;
  /**
   * Line items composing the order.
   */
  items: Array<OrderItemSchema>;
};

/** @internal */
export const OrderMetadata$inboundSchema: z.ZodType<
  OrderMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type OrderMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const OrderMetadata$outboundSchema: z.ZodType<
  OrderMetadata$Outbound,
  z.ZodTypeDef,
  OrderMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrderMetadata$ {
  /** @deprecated use `OrderMetadata$inboundSchema` instead. */
  export const inboundSchema = OrderMetadata$inboundSchema;
  /** @deprecated use `OrderMetadata$outboundSchema` instead. */
  export const outboundSchema = OrderMetadata$outboundSchema;
  /** @deprecated use `OrderMetadata$Outbound` instead. */
  export type Outbound = OrderMetadata$Outbound;
}

export function orderMetadataToJSON(orderMetadata: OrderMetadata): string {
  return JSON.stringify(OrderMetadata$outboundSchema.parse(orderMetadata));
}

export function orderMetadataFromJSON(
  jsonString: string,
): SafeParseResult<OrderMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrderMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrderMetadata' from JSON`,
  );
}

/** @internal */
export const OrderCustomFieldData$inboundSchema: z.ZodType<
  OrderCustomFieldData,
  z.ZodTypeDef,
  unknown
> = z.union([
  z.string(),
  z.number().int(),
  z.boolean(),
  z.string().datetime({ offset: true }).transform(v => new Date(v)),
]);

/** @internal */
export type OrderCustomFieldData$Outbound = string | number | boolean | string;

/** @internal */
export const OrderCustomFieldData$outboundSchema: z.ZodType<
  OrderCustomFieldData$Outbound,
  z.ZodTypeDef,
  OrderCustomFieldData
> = z.union([
  z.string(),
  z.number().int(),
  z.boolean(),
  z.date().transform(v => v.toISOString()),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrderCustomFieldData$ {
  /** @deprecated use `OrderCustomFieldData$inboundSchema` instead. */
  export const inboundSchema = OrderCustomFieldData$inboundSchema;
  /** @deprecated use `OrderCustomFieldData$outboundSchema` instead. */
  export const outboundSchema = OrderCustomFieldData$outboundSchema;
  /** @deprecated use `OrderCustomFieldData$Outbound` instead. */
  export type Outbound = OrderCustomFieldData$Outbound;
}

export function orderCustomFieldDataToJSON(
  orderCustomFieldData: OrderCustomFieldData,
): string {
  return JSON.stringify(
    OrderCustomFieldData$outboundSchema.parse(orderCustomFieldData),
  );
}

export function orderCustomFieldDataFromJSON(
  jsonString: string,
): SafeParseResult<OrderCustomFieldData, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrderCustomFieldData$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrderCustomFieldData' from JSON`,
  );
}

/** @internal */
export const OrderDiscount$inboundSchema: z.ZodType<
  OrderDiscount,
  z.ZodTypeDef,
  unknown
> = z.union([
  DiscountPercentageOnceForeverDurationBase$inboundSchema,
  DiscountFixedOnceForeverDurationBase$inboundSchema,
  DiscountPercentageRepeatDurationBase$inboundSchema,
  DiscountFixedRepeatDurationBase$inboundSchema,
]);

/** @internal */
export type OrderDiscount$Outbound =
  | DiscountPercentageOnceForeverDurationBase$Outbound
  | DiscountFixedOnceForeverDurationBase$Outbound
  | DiscountPercentageRepeatDurationBase$Outbound
  | DiscountFixedRepeatDurationBase$Outbound;

/** @internal */
export const OrderDiscount$outboundSchema: z.ZodType<
  OrderDiscount$Outbound,
  z.ZodTypeDef,
  OrderDiscount
> = z.union([
  DiscountPercentageOnceForeverDurationBase$outboundSchema,
  DiscountFixedOnceForeverDurationBase$outboundSchema,
  DiscountPercentageRepeatDurationBase$outboundSchema,
  DiscountFixedRepeatDurationBase$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrderDiscount$ {
  /** @deprecated use `OrderDiscount$inboundSchema` instead. */
  export const inboundSchema = OrderDiscount$inboundSchema;
  /** @deprecated use `OrderDiscount$outboundSchema` instead. */
  export const outboundSchema = OrderDiscount$outboundSchema;
  /** @deprecated use `OrderDiscount$Outbound` instead. */
  export type Outbound = OrderDiscount$Outbound;
}

export function orderDiscountToJSON(orderDiscount: OrderDiscount): string {
  return JSON.stringify(OrderDiscount$outboundSchema.parse(orderDiscount));
}

export function orderDiscountFromJSON(
  jsonString: string,
): SafeParseResult<OrderDiscount, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrderDiscount$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrderDiscount' from JSON`,
  );
}

/** @internal */
export const Order$inboundSchema: z.ZodType<Order, z.ZodTypeDef, unknown> = z
  .object({
    id: z.string(),
    created_at: z.string().datetime({ offset: true }).transform(v =>
      new Date(v)
    ),
    modified_at: z.nullable(
      z.string().datetime({ offset: true }).transform(v => new Date(v)),
    ),
    status: OrderStatus$inboundSchema,
    paid: z.boolean(),
    subtotal_amount: z.number().int(),
    discount_amount: z.number().int(),
    net_amount: z.number().int(),
    amount: z.number().int(),
    tax_amount: z.number().int(),
    total_amount: z.number().int(),
    refunded_amount: z.number().int(),
    refunded_tax_amount: z.number().int(),
    currency: z.string(),
    billing_reason: OrderBillingReason$inboundSchema,
    billing_address: z.nullable(Address$inboundSchema),
    customer_id: z.string(),
    product_id: z.string(),
    discount_id: z.nullable(z.string()),
    subscription_id: z.nullable(z.string()),
    checkout_id: z.nullable(z.string()),
    metadata: z.record(
      z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
    ),
    custom_field_data: z.record(
      z.nullable(
        z.union([
          z.string(),
          z.number().int(),
          z.boolean(),
          z.string().datetime({ offset: true }).transform(v => new Date(v)),
        ]),
      ),
    ).optional(),
    customer: OrderCustomer$inboundSchema,
    user_id: z.string(),
    product: OrderProduct$inboundSchema,
    discount: z.nullable(
      z.union([
        DiscountPercentageOnceForeverDurationBase$inboundSchema,
        DiscountFixedOnceForeverDurationBase$inboundSchema,
        DiscountPercentageRepeatDurationBase$inboundSchema,
        DiscountFixedRepeatDurationBase$inboundSchema,
      ]),
    ),
    subscription: z.nullable(OrderSubscription$inboundSchema),
    items: z.array(OrderItemSchema$inboundSchema),
  }).transform((v) => {
    return remap$(v, {
      "created_at": "createdAt",
      "modified_at": "modifiedAt",
      "subtotal_amount": "subtotalAmount",
      "discount_amount": "discountAmount",
      "net_amount": "netAmount",
      "tax_amount": "taxAmount",
      "total_amount": "totalAmount",
      "refunded_amount": "refundedAmount",
      "refunded_tax_amount": "refundedTaxAmount",
      "billing_reason": "billingReason",
      "billing_address": "billingAddress",
      "customer_id": "customerId",
      "product_id": "productId",
      "discount_id": "discountId",
      "subscription_id": "subscriptionId",
      "checkout_id": "checkoutId",
      "custom_field_data": "customFieldData",
      "user_id": "userId",
    });
  });

/** @internal */
export type Order$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  status: string;
  paid: boolean;
  subtotal_amount: number;
  discount_amount: number;
  net_amount: number;
  amount: number;
  tax_amount: number;
  total_amount: number;
  refunded_amount: number;
  refunded_tax_amount: number;
  currency: string;
  billing_reason: string;
  billing_address: Address$Outbound | null;
  customer_id: string;
  product_id: string;
  discount_id: string | null;
  subscription_id: string | null;
  checkout_id: string | null;
  metadata: { [k: string]: string | number | number | boolean };
  custom_field_data?:
    | { [k: string]: string | number | boolean | string | null }
    | undefined;
  customer: OrderCustomer$Outbound;
  user_id: string;
  product: OrderProduct$Outbound;
  discount:
    | DiscountPercentageOnceForeverDurationBase$Outbound
    | DiscountFixedOnceForeverDurationBase$Outbound
    | DiscountPercentageRepeatDurationBase$Outbound
    | DiscountFixedRepeatDurationBase$Outbound
    | null;
  subscription: OrderSubscription$Outbound | null;
  items: Array<OrderItemSchema$Outbound>;
};

/** @internal */
export const Order$outboundSchema: z.ZodType<
  Order$Outbound,
  z.ZodTypeDef,
  Order
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  status: OrderStatus$outboundSchema,
  paid: z.boolean(),
  subtotalAmount: z.number().int(),
  discountAmount: z.number().int(),
  netAmount: z.number().int(),
  amount: z.number().int(),
  taxAmount: z.number().int(),
  totalAmount: z.number().int(),
  refundedAmount: z.number().int(),
  refundedTaxAmount: z.number().int(),
  currency: z.string(),
  billingReason: OrderBillingReason$outboundSchema,
  billingAddress: z.nullable(Address$outboundSchema),
  customerId: z.string(),
  productId: z.string(),
  discountId: z.nullable(z.string()),
  subscriptionId: z.nullable(z.string()),
  checkoutId: z.nullable(z.string()),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  customFieldData: z.record(
    z.nullable(
      z.union([
        z.string(),
        z.number().int(),
        z.boolean(),
        z.date().transform(v => v.toISOString()),
      ]),
    ),
  ).optional(),
  customer: OrderCustomer$outboundSchema,
  userId: z.string(),
  product: OrderProduct$outboundSchema,
  discount: z.nullable(
    z.union([
      DiscountPercentageOnceForeverDurationBase$outboundSchema,
      DiscountFixedOnceForeverDurationBase$outboundSchema,
      DiscountPercentageRepeatDurationBase$outboundSchema,
      DiscountFixedRepeatDurationBase$outboundSchema,
    ]),
  ),
  subscription: z.nullable(OrderSubscription$outboundSchema),
  items: z.array(OrderItemSchema$outboundSchema),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    subtotalAmount: "subtotal_amount",
    discountAmount: "discount_amount",
    netAmount: "net_amount",
    taxAmount: "tax_amount",
    totalAmount: "total_amount",
    refundedAmount: "refunded_amount",
    refundedTaxAmount: "refunded_tax_amount",
    billingReason: "billing_reason",
    billingAddress: "billing_address",
    customerId: "customer_id",
    productId: "product_id",
    discountId: "discount_id",
    subscriptionId: "subscription_id",
    checkoutId: "checkout_id",
    customFieldData: "custom_field_data",
    userId: "user_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Order$ {
  /** @deprecated use `Order$inboundSchema` instead. */
  export const inboundSchema = Order$inboundSchema;
  /** @deprecated use `Order$outboundSchema` instead. */
  export const outboundSchema = Order$outboundSchema;
  /** @deprecated use `Order$Outbound` instead. */
  export type Outbound = Order$Outbound;
}

export function orderToJSON(order: Order): string {
  return JSON.stringify(Order$outboundSchema.parse(order));
}

export function orderFromJSON(
  jsonString: string,
): SafeParseResult<Order, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Order$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Order' from JSON`,
  );
}
