/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitType,
  BenefitType$inboundSchema,
  BenefitType$outboundSchema,
} from "./benefittype.js";

export type BenefitGrantMetadata = {
  benefitId: string;
  benefitGrantId: string;
  benefitType: BenefitType;
};

/** @internal */
export const BenefitGrantMetadata$inboundSchema: z.ZodType<
  BenefitGrantMetadata,
  z.ZodTypeDef,
  unknown
> = z.object({
  benefit_id: z.string(),
  benefit_grant_id: z.string(),
  benefit_type: BenefitType$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "benefit_id": "benefitId",
    "benefit_grant_id": "benefitGrantId",
    "benefit_type": "benefitType",
  });
});

/** @internal */
export type BenefitGrantMetadata$Outbound = {
  benefit_id: string;
  benefit_grant_id: string;
  benefit_type: string;
};

/** @internal */
export const BenefitGrantMetadata$outboundSchema: z.ZodType<
  BenefitGrantMetadata$Outbound,
  z.ZodTypeDef,
  BenefitGrantMetadata
> = z.object({
  benefitId: z.string(),
  benefitGrantId: z.string(),
  benefitType: BenefitType$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    benefitId: "benefit_id",
    benefitGrantId: "benefit_grant_id",
    benefitType: "benefit_type",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGrantMetadata$ {
  /** @deprecated use `BenefitGrantMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitGrantMetadata$inboundSchema;
  /** @deprecated use `BenefitGrantMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitGrantMetadata$outboundSchema;
  /** @deprecated use `BenefitGrantMetadata$Outbound` instead. */
  export type Outbound = BenefitGrantMetadata$Outbound;
}

export function benefitGrantMetadataToJSON(
  benefitGrantMetadata: BenefitGrantMetadata,
): string {
  return JSON.stringify(
    BenefitGrantMetadata$outboundSchema.parse(benefitGrantMetadata),
  );
}

export function benefitGrantMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGrantMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitGrantMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGrantMetadata' from JSON`,
  );
}
