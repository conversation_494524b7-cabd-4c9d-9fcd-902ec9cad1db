/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Properties available to subscribers for a benefit of type `github_repository`.
 */
export type BenefitGitHubRepositorySubscriberProperties = {
  /**
   * The owner of the repository.
   */
  repositoryOwner: string;
  /**
   * The name of the repository.
   */
  repositoryName: string;
};

/** @internal */
export const BenefitGitHubRepositorySubscriberProperties$inboundSchema:
  z.ZodType<
    BenefitGitHubRepositorySubscriberProperties,
    z.ZodTypeDef,
    unknown
  > = z.object({
    repository_owner: z.string(),
    repository_name: z.string(),
  }).transform((v) => {
    return remap$(v, {
      "repository_owner": "repositoryOwner",
      "repository_name": "repositoryName",
    });
  });

/** @internal */
export type BenefitGitHubRepositorySubscriberProperties$Outbound = {
  repository_owner: string;
  repository_name: string;
};

/** @internal */
export const BenefitGitHubRepositorySubscriberProperties$outboundSchema:
  z.ZodType<
    BenefitGitHubRepositorySubscriberProperties$Outbound,
    z.ZodTypeDef,
    BenefitGitHubRepositorySubscriberProperties
  > = z.object({
    repositoryOwner: z.string(),
    repositoryName: z.string(),
  }).transform((v) => {
    return remap$(v, {
      repositoryOwner: "repository_owner",
      repositoryName: "repository_name",
    });
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGitHubRepositorySubscriberProperties$ {
  /** @deprecated use `BenefitGitHubRepositorySubscriberProperties$inboundSchema` instead. */
  export const inboundSchema =
    BenefitGitHubRepositorySubscriberProperties$inboundSchema;
  /** @deprecated use `BenefitGitHubRepositorySubscriberProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitGitHubRepositorySubscriberProperties$outboundSchema;
  /** @deprecated use `BenefitGitHubRepositorySubscriberProperties$Outbound` instead. */
  export type Outbound = BenefitGitHubRepositorySubscriberProperties$Outbound;
}

export function benefitGitHubRepositorySubscriberPropertiesToJSON(
  benefitGitHubRepositorySubscriberProperties:
    BenefitGitHubRepositorySubscriberProperties,
): string {
  return JSON.stringify(
    BenefitGitHubRepositorySubscriberProperties$outboundSchema.parse(
      benefitGitHubRepositorySubscriberProperties,
    ),
  );
}

export function benefitGitHubRepositorySubscriberPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<
  BenefitGitHubRepositorySubscriberProperties,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitGitHubRepositorySubscriberProperties$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'BenefitGitHubRepositorySubscriberProperties' from JSON`,
  );
}
