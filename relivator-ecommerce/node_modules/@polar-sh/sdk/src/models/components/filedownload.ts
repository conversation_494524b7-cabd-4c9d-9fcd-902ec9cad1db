/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  FileServiceTypes,
  FileServiceTypes$inboundSchema,
  FileServiceTypes$outboundSchema,
} from "./fileservicetypes.js";
import {
  S3DownloadURL,
  S3DownloadURL$inboundSchema,
  S3DownloadURL$Outbound,
  S3DownloadURL$outboundSchema,
} from "./s3downloadurl.js";

export type FileDownload = {
  /**
   * The ID of the object.
   */
  id: string;
  organizationId: string;
  name: string;
  path: string;
  mimeType: string;
  size: number;
  storageVersion: string | null;
  checksumEtag: string | null;
  checksumSha256Base64: string | null;
  checksumSha256Hex: string | null;
  lastModifiedAt: Date | null;
  download: S3DownloadURL;
  version: string | null;
  isUploaded: boolean;
  service: FileServiceTypes;
  sizeReadable: string;
};

/** @internal */
export const FileDownload$inboundSchema: z.ZodType<
  FileDownload,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  organization_id: z.string(),
  name: z.string(),
  path: z.string(),
  mime_type: z.string(),
  size: z.number().int(),
  storage_version: z.nullable(z.string()),
  checksum_etag: z.nullable(z.string()),
  checksum_sha256_base64: z.nullable(z.string()),
  checksum_sha256_hex: z.nullable(z.string()),
  last_modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  download: S3DownloadURL$inboundSchema,
  version: z.nullable(z.string()),
  is_uploaded: z.boolean(),
  service: FileServiceTypes$inboundSchema,
  size_readable: z.string(),
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
    "mime_type": "mimeType",
    "storage_version": "storageVersion",
    "checksum_etag": "checksumEtag",
    "checksum_sha256_base64": "checksumSha256Base64",
    "checksum_sha256_hex": "checksumSha256Hex",
    "last_modified_at": "lastModifiedAt",
    "is_uploaded": "isUploaded",
    "size_readable": "sizeReadable",
  });
});

/** @internal */
export type FileDownload$Outbound = {
  id: string;
  organization_id: string;
  name: string;
  path: string;
  mime_type: string;
  size: number;
  storage_version: string | null;
  checksum_etag: string | null;
  checksum_sha256_base64: string | null;
  checksum_sha256_hex: string | null;
  last_modified_at: string | null;
  download: S3DownloadURL$Outbound;
  version: string | null;
  is_uploaded: boolean;
  service: string;
  size_readable: string;
};

/** @internal */
export const FileDownload$outboundSchema: z.ZodType<
  FileDownload$Outbound,
  z.ZodTypeDef,
  FileDownload
> = z.object({
  id: z.string(),
  organizationId: z.string(),
  name: z.string(),
  path: z.string(),
  mimeType: z.string(),
  size: z.number().int(),
  storageVersion: z.nullable(z.string()),
  checksumEtag: z.nullable(z.string()),
  checksumSha256Base64: z.nullable(z.string()),
  checksumSha256Hex: z.nullable(z.string()),
  lastModifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  download: S3DownloadURL$outboundSchema,
  version: z.nullable(z.string()),
  isUploaded: z.boolean(),
  service: FileServiceTypes$outboundSchema,
  sizeReadable: z.string(),
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
    mimeType: "mime_type",
    storageVersion: "storage_version",
    checksumEtag: "checksum_etag",
    checksumSha256Base64: "checksum_sha256_base64",
    checksumSha256Hex: "checksum_sha256_hex",
    lastModifiedAt: "last_modified_at",
    isUploaded: "is_uploaded",
    sizeReadable: "size_readable",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FileDownload$ {
  /** @deprecated use `FileDownload$inboundSchema` instead. */
  export const inboundSchema = FileDownload$inboundSchema;
  /** @deprecated use `FileDownload$outboundSchema` instead. */
  export const outboundSchema = FileDownload$outboundSchema;
  /** @deprecated use `FileDownload$Outbound` instead. */
  export type Outbound = FileDownload$Outbound;
}

export function fileDownloadToJSON(fileDownload: FileDownload): string {
  return JSON.stringify(FileDownload$outboundSchema.parse(fileDownload));
}

export function fileDownloadFromJSON(
  jsonString: string,
): SafeParseResult<FileDownload, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FileDownload$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FileDownload' from JSON`,
  );
}
