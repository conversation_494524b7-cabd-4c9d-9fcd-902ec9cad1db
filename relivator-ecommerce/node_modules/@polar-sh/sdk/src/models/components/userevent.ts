/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Customer,
  Customer$inboundSchema,
  Customer$Outbound,
  Customer$outboundSchema,
} from "./customer.js";

export type UserEventMetadata = string | number | number | boolean;

/**
 * An event you created through the ingestion API.
 */
export type UserEvent = {
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * The timestamp of the event.
   */
  timestamp: Date;
  /**
   * The ID of the organization owning the event.
   */
  organizationId: string;
  /**
   * ID of the customer in your Polar organization associated with the event.
   */
  customerId: string | null;
  /**
   * The customer associated with the event.
   */
  customer: Customer | null;
  /**
   * ID of the customer in your system associated with the event.
   */
  externalCustomerId: string | null;
  /**
   * The name of the event.
   */
  name: string;
  /**
   * The source of the event. `system` events are created by Polar. `user` events are the one you create through our ingestion API.
   */
  source?: "user" | undefined;
};

/** @internal */
export const UserEventMetadata$inboundSchema: z.ZodType<
  UserEventMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type UserEventMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const UserEventMetadata$outboundSchema: z.ZodType<
  UserEventMetadata$Outbound,
  z.ZodTypeDef,
  UserEventMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace UserEventMetadata$ {
  /** @deprecated use `UserEventMetadata$inboundSchema` instead. */
  export const inboundSchema = UserEventMetadata$inboundSchema;
  /** @deprecated use `UserEventMetadata$outboundSchema` instead. */
  export const outboundSchema = UserEventMetadata$outboundSchema;
  /** @deprecated use `UserEventMetadata$Outbound` instead. */
  export type Outbound = UserEventMetadata$Outbound;
}

export function userEventMetadataToJSON(
  userEventMetadata: UserEventMetadata,
): string {
  return JSON.stringify(
    UserEventMetadata$outboundSchema.parse(userEventMetadata),
  );
}

export function userEventMetadataFromJSON(
  jsonString: string,
): SafeParseResult<UserEventMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => UserEventMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'UserEventMetadata' from JSON`,
  );
}

/** @internal */
export const UserEvent$inboundSchema: z.ZodType<
  UserEvent,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  id: z.string(),
  timestamp: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  organization_id: z.string(),
  customer_id: z.nullable(z.string()),
  customer: z.nullable(Customer$inboundSchema),
  external_customer_id: z.nullable(z.string()),
  name: z.string(),
  source: z.literal("user").optional(),
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
    "customer_id": "customerId",
    "external_customer_id": "externalCustomerId",
  });
});

/** @internal */
export type UserEvent$Outbound = {
  metadata: { [k: string]: string | number | number | boolean };
  id: string;
  timestamp: string;
  organization_id: string;
  customer_id: string | null;
  customer: Customer$Outbound | null;
  external_customer_id: string | null;
  name: string;
  source: "user";
};

/** @internal */
export const UserEvent$outboundSchema: z.ZodType<
  UserEvent$Outbound,
  z.ZodTypeDef,
  UserEvent
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  id: z.string(),
  timestamp: z.date().transform(v => v.toISOString()),
  organizationId: z.string(),
  customerId: z.nullable(z.string()),
  customer: z.nullable(Customer$outboundSchema),
  externalCustomerId: z.nullable(z.string()),
  name: z.string(),
  source: z.literal("user").default("user" as const),
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
    customerId: "customer_id",
    externalCustomerId: "external_customer_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace UserEvent$ {
  /** @deprecated use `UserEvent$inboundSchema` instead. */
  export const inboundSchema = UserEvent$inboundSchema;
  /** @deprecated use `UserEvent$outboundSchema` instead. */
  export const outboundSchema = UserEvent$outboundSchema;
  /** @deprecated use `UserEvent$Outbound` instead. */
  export type Outbound = UserEvent$Outbound;
}

export function userEventToJSON(userEvent: UserEvent): string {
  return JSON.stringify(UserEvent$outboundSchema.parse(userEvent));
}

export function userEventFromJSON(
  jsonString: string,
): SafeParseResult<UserEvent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => UserEvent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'UserEvent' from JSON`,
  );
}
