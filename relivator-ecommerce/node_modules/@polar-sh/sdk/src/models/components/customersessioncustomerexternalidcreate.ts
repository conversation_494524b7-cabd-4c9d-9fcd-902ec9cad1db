/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Schema for creating a customer session using an external customer ID.
 */
export type CustomerSessionCustomerExternalIDCreate = {
  /**
   * External ID of the customer to create a session for.
   */
  customerExternalId: string;
};

/** @internal */
export const CustomerSessionCustomerExternalIDCreate$inboundSchema: z.ZodType<
  CustomerSessionCustomerExternalIDCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  customer_external_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "customer_external_id": "customerExternalId",
  });
});

/** @internal */
export type CustomerSessionCustomerExternalIDCreate$Outbound = {
  customer_external_id: string;
};

/** @internal */
export const CustomerSessionCustomerExternalIDCreate$outboundSchema: z.ZodType<
  CustomerSessionCustomerExternalIDCreate$Outbound,
  z.ZodTypeDef,
  CustomerSessionCustomerExternalIDCreate
> = z.object({
  customerExternalId: z.string(),
}).transform((v) => {
  return remap$(v, {
    customerExternalId: "customer_external_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerSessionCustomerExternalIDCreate$ {
  /** @deprecated use `CustomerSessionCustomerExternalIDCreate$inboundSchema` instead. */
  export const inboundSchema =
    CustomerSessionCustomerExternalIDCreate$inboundSchema;
  /** @deprecated use `CustomerSessionCustomerExternalIDCreate$outboundSchema` instead. */
  export const outboundSchema =
    CustomerSessionCustomerExternalIDCreate$outboundSchema;
  /** @deprecated use `CustomerSessionCustomerExternalIDCreate$Outbound` instead. */
  export type Outbound = CustomerSessionCustomerExternalIDCreate$Outbound;
}

export function customerSessionCustomerExternalIDCreateToJSON(
  customerSessionCustomerExternalIDCreate:
    CustomerSessionCustomerExternalIDCreate,
): string {
  return JSON.stringify(
    CustomerSessionCustomerExternalIDCreate$outboundSchema.parse(
      customerSessionCustomerExternalIDCreate,
    ),
  );
}

export function customerSessionCustomerExternalIDCreateFromJSON(
  jsonString: string,
): SafeParseResult<
  CustomerSessionCustomerExternalIDCreate,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      CustomerSessionCustomerExternalIDCreate$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'CustomerSessionCustomerExternalIDCreate' from JSON`,
  );
}
