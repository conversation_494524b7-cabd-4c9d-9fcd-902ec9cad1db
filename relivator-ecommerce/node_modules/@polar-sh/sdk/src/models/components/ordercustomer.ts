/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Address,
  Address$inboundSchema,
  Address$Outbound,
  Address$outboundSchema,
} from "./address.js";
import {
  TaxIDFormat,
  TaxIDFormat$inboundSchema,
  TaxIDFormat$outboundSchema,
} from "./taxidformat.js";

export type OrderCustomerMetadata = string | number | number | boolean;

export type OrderCustomerTaxId = string | TaxIDFormat;

export type OrderCustomer = {
  /**
   * The ID of the customer.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * The ID of the customer in your system. This must be unique within the organization. Once set, it can't be updated.
   */
  externalId: string | null;
  /**
   * The email address of the customer. This must be unique within the organization.
   */
  email: string;
  /**
   * Whether the customer email address is verified. The address is automatically verified when the customer accesses the customer portal using their email address.
   */
  emailVerified: boolean;
  /**
   * The name of the customer.
   */
  name: string | null;
  billingAddress: Address | null;
  taxId: Array<string | TaxIDFormat | null> | null;
  /**
   * The ID of the organization owning the customer.
   */
  organizationId: string;
  /**
   * Timestamp for when the customer was soft deleted.
   */
  deletedAt: Date | null;
  avatarUrl: string;
};

/** @internal */
export const OrderCustomerMetadata$inboundSchema: z.ZodType<
  OrderCustomerMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type OrderCustomerMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const OrderCustomerMetadata$outboundSchema: z.ZodType<
  OrderCustomerMetadata$Outbound,
  z.ZodTypeDef,
  OrderCustomerMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrderCustomerMetadata$ {
  /** @deprecated use `OrderCustomerMetadata$inboundSchema` instead. */
  export const inboundSchema = OrderCustomerMetadata$inboundSchema;
  /** @deprecated use `OrderCustomerMetadata$outboundSchema` instead. */
  export const outboundSchema = OrderCustomerMetadata$outboundSchema;
  /** @deprecated use `OrderCustomerMetadata$Outbound` instead. */
  export type Outbound = OrderCustomerMetadata$Outbound;
}

export function orderCustomerMetadataToJSON(
  orderCustomerMetadata: OrderCustomerMetadata,
): string {
  return JSON.stringify(
    OrderCustomerMetadata$outboundSchema.parse(orderCustomerMetadata),
  );
}

export function orderCustomerMetadataFromJSON(
  jsonString: string,
): SafeParseResult<OrderCustomerMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrderCustomerMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrderCustomerMetadata' from JSON`,
  );
}

/** @internal */
export const OrderCustomerTaxId$inboundSchema: z.ZodType<
  OrderCustomerTaxId,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), TaxIDFormat$inboundSchema]);

/** @internal */
export type OrderCustomerTaxId$Outbound = string | string;

/** @internal */
export const OrderCustomerTaxId$outboundSchema: z.ZodType<
  OrderCustomerTaxId$Outbound,
  z.ZodTypeDef,
  OrderCustomerTaxId
> = z.union([z.string(), TaxIDFormat$outboundSchema]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrderCustomerTaxId$ {
  /** @deprecated use `OrderCustomerTaxId$inboundSchema` instead. */
  export const inboundSchema = OrderCustomerTaxId$inboundSchema;
  /** @deprecated use `OrderCustomerTaxId$outboundSchema` instead. */
  export const outboundSchema = OrderCustomerTaxId$outboundSchema;
  /** @deprecated use `OrderCustomerTaxId$Outbound` instead. */
  export type Outbound = OrderCustomerTaxId$Outbound;
}

export function orderCustomerTaxIdToJSON(
  orderCustomerTaxId: OrderCustomerTaxId,
): string {
  return JSON.stringify(
    OrderCustomerTaxId$outboundSchema.parse(orderCustomerTaxId),
  );
}

export function orderCustomerTaxIdFromJSON(
  jsonString: string,
): SafeParseResult<OrderCustomerTaxId, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrderCustomerTaxId$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrderCustomerTaxId' from JSON`,
  );
}

/** @internal */
export const OrderCustomer$inboundSchema: z.ZodType<
  OrderCustomer,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  external_id: z.nullable(z.string()),
  email: z.string(),
  email_verified: z.boolean(),
  name: z.nullable(z.string()),
  billing_address: z.nullable(Address$inboundSchema),
  tax_id: z.nullable(
    z.array(z.nullable(z.union([z.string(), TaxIDFormat$inboundSchema]))),
  ),
  organization_id: z.string(),
  deleted_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  avatar_url: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "external_id": "externalId",
    "email_verified": "emailVerified",
    "billing_address": "billingAddress",
    "tax_id": "taxId",
    "organization_id": "organizationId",
    "deleted_at": "deletedAt",
    "avatar_url": "avatarUrl",
  });
});

/** @internal */
export type OrderCustomer$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  metadata: { [k: string]: string | number | number | boolean };
  external_id: string | null;
  email: string;
  email_verified: boolean;
  name: string | null;
  billing_address: Address$Outbound | null;
  tax_id: Array<string | string | null> | null;
  organization_id: string;
  deleted_at: string | null;
  avatar_url: string;
};

/** @internal */
export const OrderCustomer$outboundSchema: z.ZodType<
  OrderCustomer$Outbound,
  z.ZodTypeDef,
  OrderCustomer
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  externalId: z.nullable(z.string()),
  email: z.string(),
  emailVerified: z.boolean(),
  name: z.nullable(z.string()),
  billingAddress: z.nullable(Address$outboundSchema),
  taxId: z.nullable(
    z.array(z.nullable(z.union([z.string(), TaxIDFormat$outboundSchema]))),
  ),
  organizationId: z.string(),
  deletedAt: z.nullable(z.date().transform(v => v.toISOString())),
  avatarUrl: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    externalId: "external_id",
    emailVerified: "email_verified",
    billingAddress: "billing_address",
    taxId: "tax_id",
    organizationId: "organization_id",
    deletedAt: "deleted_at",
    avatarUrl: "avatar_url",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrderCustomer$ {
  /** @deprecated use `OrderCustomer$inboundSchema` instead. */
  export const inboundSchema = OrderCustomer$inboundSchema;
  /** @deprecated use `OrderCustomer$outboundSchema` instead. */
  export const outboundSchema = OrderCustomer$outboundSchema;
  /** @deprecated use `OrderCustomer$Outbound` instead. */
  export type Outbound = OrderCustomer$Outbound;
}

export function orderCustomerToJSON(orderCustomer: OrderCustomer): string {
  return JSON.stringify(OrderCustomer$outboundSchema.parse(orderCustomer));
}

export function orderCustomerFromJSON(
  jsonString: string,
): SafeParseResult<OrderCustomer, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrderCustomer$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrderCustomer' from JSON`,
  );
}
