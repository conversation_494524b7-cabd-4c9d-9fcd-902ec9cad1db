/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Order,
  Order$inboundSchema,
  Order$Outbound,
  Order$outboundSchema,
} from "./order.js";

/**
 * Sent when an order is fully or partially refunded.
 *
 * @remarks
 *
 * **Discord & Slack support:** Full
 */
export type WebhookOrderRefundedPayload = {
  type?: "order.refunded" | undefined;
  data: Order;
};

/** @internal */
export const WebhookOrderRefundedPayload$inboundSchema: z.ZodType<
  WebhookOrderRefundedPayload,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: z.literal("order.refunded").optional(),
  data: Order$inboundSchema,
});

/** @internal */
export type WebhookOrderRefundedPayload$Outbound = {
  type: "order.refunded";
  data: Order$Outbound;
};

/** @internal */
export const WebhookOrderRefundedPayload$outboundSchema: z.ZodType<
  WebhookOrderRefundedPayload$Outbound,
  z.ZodTypeDef,
  WebhookOrderRefundedPayload
> = z.object({
  type: z.literal("order.refunded").default("order.refunded" as const),
  data: Order$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace WebhookOrderRefundedPayload$ {
  /** @deprecated use `WebhookOrderRefundedPayload$inboundSchema` instead. */
  export const inboundSchema = WebhookOrderRefundedPayload$inboundSchema;
  /** @deprecated use `WebhookOrderRefundedPayload$outboundSchema` instead. */
  export const outboundSchema = WebhookOrderRefundedPayload$outboundSchema;
  /** @deprecated use `WebhookOrderRefundedPayload$Outbound` instead. */
  export type Outbound = WebhookOrderRefundedPayload$Outbound;
}

export function webhookOrderRefundedPayloadToJSON(
  webhookOrderRefundedPayload: WebhookOrderRefundedPayload,
): string {
  return JSON.stringify(
    WebhookOrderRefundedPayload$outboundSchema.parse(
      webhookOrderRefundedPayload,
    ),
  );
}

export function webhookOrderRefundedPayloadFromJSON(
  jsonString: string,
): SafeParseResult<WebhookOrderRefundedPayload, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => WebhookOrderRefundedPayload$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'WebhookOrderRefundedPayload' from JSON`,
  );
}
