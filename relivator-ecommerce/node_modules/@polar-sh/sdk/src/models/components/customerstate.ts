/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Address,
  Address$inboundSchema,
  Address$Outbound,
  Address$outboundSchema,
} from "./address.js";
import {
  CustomerStateBenefitGrant,
  CustomerStateBenefitGrant$inboundSchema,
  CustomerStateBenefitGrant$Outbound,
  CustomerStateBenefitGrant$outboundSchema,
} from "./customerstatebenefitgrant.js";
import {
  CustomerStateMeter,
  CustomerStateMeter$inboundSchema,
  CustomerStateMeter$Outbound,
  CustomerStateMeter$outboundSchema,
} from "./customerstatemeter.js";
import {
  CustomerStateSubscription,
  CustomerStateSubscription$inboundSchema,
  CustomerStateSubscription$Outbound,
  CustomerStateSubscription$outboundSchema,
} from "./customerstatesubscription.js";
import {
  TaxIDFormat,
  TaxIDFormat$inboundSchema,
  TaxIDFormat$outboundSchema,
} from "./taxidformat.js";

export type CustomerStateMetadata = string | number | number | boolean;

export type CustomerStateTaxId = string | TaxIDFormat;

/**
 * A customer along with additional state information:
 *
 * @remarks
 *
 * * Active subscriptions
 * * Granted benefits
 * * Active meters
 */
export type CustomerState = {
  /**
   * The ID of the customer.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * The ID of the customer in your system. This must be unique within the organization. Once set, it can't be updated.
   */
  externalId: string | null;
  /**
   * The email address of the customer. This must be unique within the organization.
   */
  email: string;
  /**
   * Whether the customer email address is verified. The address is automatically verified when the customer accesses the customer portal using their email address.
   */
  emailVerified: boolean;
  /**
   * The name of the customer.
   */
  name: string | null;
  billingAddress: Address | null;
  taxId: Array<string | TaxIDFormat | null> | null;
  /**
   * The ID of the organization owning the customer.
   */
  organizationId: string;
  /**
   * Timestamp for when the customer was soft deleted.
   */
  deletedAt: Date | null;
  /**
   * The customer's active subscriptions.
   */
  activeSubscriptions: Array<CustomerStateSubscription>;
  /**
   * The customer's active benefit grants.
   */
  grantedBenefits: Array<CustomerStateBenefitGrant>;
  /**
   * The customer's active meters.
   */
  activeMeters: Array<CustomerStateMeter>;
  avatarUrl: string;
};

/** @internal */
export const CustomerStateMetadata$inboundSchema: z.ZodType<
  CustomerStateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomerStateMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const CustomerStateMetadata$outboundSchema: z.ZodType<
  CustomerStateMetadata$Outbound,
  z.ZodTypeDef,
  CustomerStateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerStateMetadata$ {
  /** @deprecated use `CustomerStateMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomerStateMetadata$inboundSchema;
  /** @deprecated use `CustomerStateMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomerStateMetadata$outboundSchema;
  /** @deprecated use `CustomerStateMetadata$Outbound` instead. */
  export type Outbound = CustomerStateMetadata$Outbound;
}

export function customerStateMetadataToJSON(
  customerStateMetadata: CustomerStateMetadata,
): string {
  return JSON.stringify(
    CustomerStateMetadata$outboundSchema.parse(customerStateMetadata),
  );
}

export function customerStateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomerStateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerStateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerStateMetadata' from JSON`,
  );
}

/** @internal */
export const CustomerStateTaxId$inboundSchema: z.ZodType<
  CustomerStateTaxId,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), TaxIDFormat$inboundSchema]);

/** @internal */
export type CustomerStateTaxId$Outbound = string | string;

/** @internal */
export const CustomerStateTaxId$outboundSchema: z.ZodType<
  CustomerStateTaxId$Outbound,
  z.ZodTypeDef,
  CustomerStateTaxId
> = z.union([z.string(), TaxIDFormat$outboundSchema]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerStateTaxId$ {
  /** @deprecated use `CustomerStateTaxId$inboundSchema` instead. */
  export const inboundSchema = CustomerStateTaxId$inboundSchema;
  /** @deprecated use `CustomerStateTaxId$outboundSchema` instead. */
  export const outboundSchema = CustomerStateTaxId$outboundSchema;
  /** @deprecated use `CustomerStateTaxId$Outbound` instead. */
  export type Outbound = CustomerStateTaxId$Outbound;
}

export function customerStateTaxIdToJSON(
  customerStateTaxId: CustomerStateTaxId,
): string {
  return JSON.stringify(
    CustomerStateTaxId$outboundSchema.parse(customerStateTaxId),
  );
}

export function customerStateTaxIdFromJSON(
  jsonString: string,
): SafeParseResult<CustomerStateTaxId, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerStateTaxId$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerStateTaxId' from JSON`,
  );
}

/** @internal */
export const CustomerState$inboundSchema: z.ZodType<
  CustomerState,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  external_id: z.nullable(z.string()),
  email: z.string(),
  email_verified: z.boolean(),
  name: z.nullable(z.string()),
  billing_address: z.nullable(Address$inboundSchema),
  tax_id: z.nullable(
    z.array(z.nullable(z.union([z.string(), TaxIDFormat$inboundSchema]))),
  ),
  organization_id: z.string(),
  deleted_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  active_subscriptions: z.array(CustomerStateSubscription$inboundSchema),
  granted_benefits: z.array(CustomerStateBenefitGrant$inboundSchema),
  active_meters: z.array(CustomerStateMeter$inboundSchema),
  avatar_url: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "external_id": "externalId",
    "email_verified": "emailVerified",
    "billing_address": "billingAddress",
    "tax_id": "taxId",
    "organization_id": "organizationId",
    "deleted_at": "deletedAt",
    "active_subscriptions": "activeSubscriptions",
    "granted_benefits": "grantedBenefits",
    "active_meters": "activeMeters",
    "avatar_url": "avatarUrl",
  });
});

/** @internal */
export type CustomerState$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  metadata: { [k: string]: string | number | number | boolean };
  external_id: string | null;
  email: string;
  email_verified: boolean;
  name: string | null;
  billing_address: Address$Outbound | null;
  tax_id: Array<string | string | null> | null;
  organization_id: string;
  deleted_at: string | null;
  active_subscriptions: Array<CustomerStateSubscription$Outbound>;
  granted_benefits: Array<CustomerStateBenefitGrant$Outbound>;
  active_meters: Array<CustomerStateMeter$Outbound>;
  avatar_url: string;
};

/** @internal */
export const CustomerState$outboundSchema: z.ZodType<
  CustomerState$Outbound,
  z.ZodTypeDef,
  CustomerState
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  externalId: z.nullable(z.string()),
  email: z.string(),
  emailVerified: z.boolean(),
  name: z.nullable(z.string()),
  billingAddress: z.nullable(Address$outboundSchema),
  taxId: z.nullable(
    z.array(z.nullable(z.union([z.string(), TaxIDFormat$outboundSchema]))),
  ),
  organizationId: z.string(),
  deletedAt: z.nullable(z.date().transform(v => v.toISOString())),
  activeSubscriptions: z.array(CustomerStateSubscription$outboundSchema),
  grantedBenefits: z.array(CustomerStateBenefitGrant$outboundSchema),
  activeMeters: z.array(CustomerStateMeter$outboundSchema),
  avatarUrl: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    externalId: "external_id",
    emailVerified: "email_verified",
    billingAddress: "billing_address",
    taxId: "tax_id",
    organizationId: "organization_id",
    deletedAt: "deleted_at",
    activeSubscriptions: "active_subscriptions",
    grantedBenefits: "granted_benefits",
    activeMeters: "active_meters",
    avatarUrl: "avatar_url",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerState$ {
  /** @deprecated use `CustomerState$inboundSchema` instead. */
  export const inboundSchema = CustomerState$inboundSchema;
  /** @deprecated use `CustomerState$outboundSchema` instead. */
  export const outboundSchema = CustomerState$outboundSchema;
  /** @deprecated use `CustomerState$Outbound` instead. */
  export type Outbound = CustomerState$Outbound;
}

export function customerStateToJSON(customerState: CustomerState): string {
  return JSON.stringify(CustomerState$outboundSchema.parse(customerState));
}

export function customerStateFromJSON(
  jsonString: string,
): SafeParseResult<CustomerState, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerState$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerState' from JSON`,
  );
}
