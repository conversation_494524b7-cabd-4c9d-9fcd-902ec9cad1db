/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Properties for a benefit of type `custom`.
 */
export type BenefitCustomProperties = {
  note: string | null;
};

/** @internal */
export const BenefitCustomProperties$inboundSchema: z.ZodType<
  BenefitCustomProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  note: z.nullable(z.string()),
});

/** @internal */
export type BenefitCustomProperties$Outbound = {
  note: string | null;
};

/** @internal */
export const BenefitCustomProperties$outboundSchema: z.ZodType<
  BenefitCustomProperties$Outbound,
  z.ZodTypeDef,
  BenefitCustomProperties
> = z.object({
  note: z.nullable(z.string()),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitCustomProperties$ {
  /** @deprecated use `BenefitCustomProperties$inboundSchema` instead. */
  export const inboundSchema = BenefitCustomProperties$inboundSchema;
  /** @deprecated use `BenefitCustomProperties$outboundSchema` instead. */
  export const outboundSchema = BenefitCustomProperties$outboundSchema;
  /** @deprecated use `BenefitCustomProperties$Outbound` instead. */
  export type Outbound = BenefitCustomProperties$Outbound;
}

export function benefitCustomPropertiesToJSON(
  benefitCustomProperties: BenefitCustomProperties,
): string {
  return JSON.stringify(
    BenefitCustomProperties$outboundSchema.parse(benefitCustomProperties),
  );
}

export function benefitCustomPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<BenefitCustomProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitCustomProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitCustomProperties' from JSON`,
  );
}
