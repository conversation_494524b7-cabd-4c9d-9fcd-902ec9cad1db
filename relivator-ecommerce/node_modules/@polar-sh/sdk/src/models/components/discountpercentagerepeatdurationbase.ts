/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountDuration,
  DiscountDuration$inboundSchema,
  DiscountDuration$outboundSchema,
} from "./discountduration.js";
import {
  DiscountType,
  DiscountType$inboundSchema,
  DiscountType$outboundSchema,
} from "./discounttype.js";

export type DiscountPercentageRepeatDurationBaseMetadata =
  | string
  | number
  | number
  | boolean;

export type DiscountPercentageRepeatDurationBase = {
  duration: DiscountDuration;
  durationInMonths: number;
  type: DiscountType;
  basisPoints: number;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Name of the discount. Will be displayed to the customer when the discount is applied.
   */
  name: string;
  /**
   * Code customers can use to apply the discount during checkout.
   */
  code: string | null;
  /**
   * Timestamp after which the discount is redeemable.
   */
  startsAt: Date | null;
  /**
   * Timestamp after which the discount is no longer redeemable.
   */
  endsAt: Date | null;
  /**
   * Maximum number of times the discount can be redeemed.
   */
  maxRedemptions: number | null;
  /**
   * Number of times the discount has been redeemed.
   */
  redemptionsCount: number;
  /**
   * The organization ID.
   */
  organizationId: string;
};

/** @internal */
export const DiscountPercentageRepeatDurationBaseMetadata$inboundSchema:
  z.ZodType<
    DiscountPercentageRepeatDurationBaseMetadata,
    z.ZodTypeDef,
    unknown
  > = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type DiscountPercentageRepeatDurationBaseMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const DiscountPercentageRepeatDurationBaseMetadata$outboundSchema:
  z.ZodType<
    DiscountPercentageRepeatDurationBaseMetadata$Outbound,
    z.ZodTypeDef,
    DiscountPercentageRepeatDurationBaseMetadata
  > = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountPercentageRepeatDurationBaseMetadata$ {
  /** @deprecated use `DiscountPercentageRepeatDurationBaseMetadata$inboundSchema` instead. */
  export const inboundSchema =
    DiscountPercentageRepeatDurationBaseMetadata$inboundSchema;
  /** @deprecated use `DiscountPercentageRepeatDurationBaseMetadata$outboundSchema` instead. */
  export const outboundSchema =
    DiscountPercentageRepeatDurationBaseMetadata$outboundSchema;
  /** @deprecated use `DiscountPercentageRepeatDurationBaseMetadata$Outbound` instead. */
  export type Outbound = DiscountPercentageRepeatDurationBaseMetadata$Outbound;
}

export function discountPercentageRepeatDurationBaseMetadataToJSON(
  discountPercentageRepeatDurationBaseMetadata:
    DiscountPercentageRepeatDurationBaseMetadata,
): string {
  return JSON.stringify(
    DiscountPercentageRepeatDurationBaseMetadata$outboundSchema.parse(
      discountPercentageRepeatDurationBaseMetadata,
    ),
  );
}

export function discountPercentageRepeatDurationBaseMetadataFromJSON(
  jsonString: string,
): SafeParseResult<
  DiscountPercentageRepeatDurationBaseMetadata,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      DiscountPercentageRepeatDurationBaseMetadata$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'DiscountPercentageRepeatDurationBaseMetadata' from JSON`,
  );
}

/** @internal */
export const DiscountPercentageRepeatDurationBase$inboundSchema: z.ZodType<
  DiscountPercentageRepeatDurationBase,
  z.ZodTypeDef,
  unknown
> = z.object({
  duration: DiscountDuration$inboundSchema,
  duration_in_months: z.number().int(),
  type: DiscountType$inboundSchema,
  basis_points: z.number().int(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  name: z.string(),
  code: z.nullable(z.string()),
  starts_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  ends_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  max_redemptions: z.nullable(z.number().int()),
  redemptions_count: z.number().int(),
  organization_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "duration_in_months": "durationInMonths",
    "basis_points": "basisPoints",
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "starts_at": "startsAt",
    "ends_at": "endsAt",
    "max_redemptions": "maxRedemptions",
    "redemptions_count": "redemptionsCount",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type DiscountPercentageRepeatDurationBase$Outbound = {
  duration: string;
  duration_in_months: number;
  type: string;
  basis_points: number;
  created_at: string;
  modified_at: string | null;
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  name: string;
  code: string | null;
  starts_at: string | null;
  ends_at: string | null;
  max_redemptions: number | null;
  redemptions_count: number;
  organization_id: string;
};

/** @internal */
export const DiscountPercentageRepeatDurationBase$outboundSchema: z.ZodType<
  DiscountPercentageRepeatDurationBase$Outbound,
  z.ZodTypeDef,
  DiscountPercentageRepeatDurationBase
> = z.object({
  duration: DiscountDuration$outboundSchema,
  durationInMonths: z.number().int(),
  type: DiscountType$outboundSchema,
  basisPoints: z.number().int(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  name: z.string(),
  code: z.nullable(z.string()),
  startsAt: z.nullable(z.date().transform(v => v.toISOString())),
  endsAt: z.nullable(z.date().transform(v => v.toISOString())),
  maxRedemptions: z.nullable(z.number().int()),
  redemptionsCount: z.number().int(),
  organizationId: z.string(),
}).transform((v) => {
  return remap$(v, {
    durationInMonths: "duration_in_months",
    basisPoints: "basis_points",
    createdAt: "created_at",
    modifiedAt: "modified_at",
    startsAt: "starts_at",
    endsAt: "ends_at",
    maxRedemptions: "max_redemptions",
    redemptionsCount: "redemptions_count",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountPercentageRepeatDurationBase$ {
  /** @deprecated use `DiscountPercentageRepeatDurationBase$inboundSchema` instead. */
  export const inboundSchema =
    DiscountPercentageRepeatDurationBase$inboundSchema;
  /** @deprecated use `DiscountPercentageRepeatDurationBase$outboundSchema` instead. */
  export const outboundSchema =
    DiscountPercentageRepeatDurationBase$outboundSchema;
  /** @deprecated use `DiscountPercentageRepeatDurationBase$Outbound` instead. */
  export type Outbound = DiscountPercentageRepeatDurationBase$Outbound;
}

export function discountPercentageRepeatDurationBaseToJSON(
  discountPercentageRepeatDurationBase: DiscountPercentageRepeatDurationBase,
): string {
  return JSON.stringify(
    DiscountPercentageRepeatDurationBase$outboundSchema.parse(
      discountPercentageRepeatDurationBase,
    ),
  );
}

export function discountPercentageRepeatDurationBaseFromJSON(
  jsonString: string,
): SafeParseResult<DiscountPercentageRepeatDurationBase, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      DiscountPercentageRepeatDurationBase$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DiscountPercentageRepeatDurationBase' from JSON`,
  );
}
