/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";
import {
  Payment,
  Payment$inboundSchema,
  Payment$Outbound,
  Payment$outboundSchema,
} from "./payment.js";

export type ListResource = {
  items: Array<Payment>;
  pagination: Pagination;
};

/** @internal */
export const ListResource$inboundSchema: z.ZodType<
  ListResource,
  z.ZodTypeDef,
  unknown
> = z.object({
  items: z.array(Payment$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ListResource$Outbound = {
  items: Array<Payment$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResource$outboundSchema: z.ZodType<
  ListResource$Outbound,
  z.ZodTypeDef,
  ListResource
> = z.object({
  items: z.array(Payment$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResource$ {
  /** @deprecated use `ListResource$inboundSchema` instead. */
  export const inboundSchema = ListResource$inboundSchema;
  /** @deprecated use `ListResource$outboundSchema` instead. */
  export const outboundSchema = ListResource$outboundSchema;
  /** @deprecated use `ListResource$Outbound` instead. */
  export type Outbound = ListResource$Outbound;
}

export function listResourceToJSON(listResource: ListResource): string {
  return JSON.stringify(ListResource$outboundSchema.parse(listResource));
}

export function listResourceFromJSON(
  jsonString: string,
): SafeParseResult<ListResource, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListResource$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListResource' from JSON`,
  );
}
