/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const LicenseKeyStatus = {
  Granted: "granted",
  Revoked: "revoked",
  Disabled: "disabled",
} as const;
export type LicenseKeyStatus = ClosedEnum<typeof LicenseKeyStatus>;

/** @internal */
export const LicenseKeyStatus$inboundSchema: z.ZodNativeEnum<
  typeof LicenseKeyStatus
> = z.nativeEnum(LicenseKeyStatus);

/** @internal */
export const LicenseKeyStatus$outboundSchema: z.ZodNativeEnum<
  typeof LicenseKeyStatus
> = LicenseKeyStatus$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LicenseKeyStatus$ {
  /** @deprecated use `LicenseKeyStatus$inboundSchema` instead. */
  export const inboundSchema = LicenseKeyStatus$inboundSchema;
  /** @deprecated use `LicenseKeyStatus$outboundSchema` instead. */
  export const outboundSchema = LicenseKeyStatus$outboundSchema;
}
