/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const ProductSortProperty = {
  CreatedAt: "created_at",
  MinusCreatedAt: "-created_at",
  Name: "name",
  MinusName: "-name",
  PriceAmountType: "price_amount_type",
  MinusPriceAmountType: "-price_amount_type",
  PriceAmount: "price_amount",
  MinusPriceAmount: "-price_amount",
} as const;
export type ProductSortProperty = ClosedEnum<typeof ProductSortProperty>;

/** @internal */
export const ProductSortProperty$inboundSchema: z.ZodNativeEnum<
  typeof ProductSortProperty
> = z.nativeEnum(ProductSortProperty);

/** @internal */
export const ProductSortProperty$outboundSchema: z.ZodNativeEnum<
  typeof ProductSortProperty
> = ProductSortProperty$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ProductSortProperty$ {
  /** @deprecated use `ProductSortProperty$inboundSchema` instead. */
  export const inboundSchema = ProductSortProperty$inboundSchema;
  /** @deprecated use `ProductSortProperty$outboundSchema` instead. */
  export const outboundSchema = ProductSortProperty$outboundSchema;
}
