/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomerMeter,
  CustomerMeter$inboundSchema,
  CustomerMeter$Outbound,
  CustomerMeter$outboundSchema,
} from "./customermeter.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";

export type ListResourceCustomerMeter = {
  items: Array<CustomerMeter>;
  pagination: Pagination;
};

/** @internal */
export const ListResourceCustomerMeter$inboundSchema: z.ZodType<
  ListResourceCustomerMeter,
  z.ZodTypeDef,
  unknown
> = z.object({
  items: z.array(CustomerMeter$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ListResourceCustomerMeter$Outbound = {
  items: Array<CustomerMeter$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResourceCustomerMeter$outboundSchema: z.ZodType<
  ListResourceCustomerMeter$Outbound,
  z.ZodTypeDef,
  ListResourceCustomerMeter
> = z.object({
  items: z.array(CustomerMeter$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResourceCustomerMeter$ {
  /** @deprecated use `ListResourceCustomerMeter$inboundSchema` instead. */
  export const inboundSchema = ListResourceCustomerMeter$inboundSchema;
  /** @deprecated use `ListResourceCustomerMeter$outboundSchema` instead. */
  export const outboundSchema = ListResourceCustomerMeter$outboundSchema;
  /** @deprecated use `ListResourceCustomerMeter$Outbound` instead. */
  export type Outbound = ListResourceCustomerMeter$Outbound;
}

export function listResourceCustomerMeterToJSON(
  listResourceCustomerMeter: ListResourceCustomerMeter,
): string {
  return JSON.stringify(
    ListResourceCustomerMeter$outboundSchema.parse(listResourceCustomerMeter),
  );
}

export function listResourceCustomerMeterFromJSON(
  jsonString: string,
): SafeParseResult<ListResourceCustomerMeter, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListResourceCustomerMeter$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListResourceCustomerMeter' from JSON`,
  );
}
