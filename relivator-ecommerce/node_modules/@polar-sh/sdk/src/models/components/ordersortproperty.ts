/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const OrderSortProperty = {
  CreatedAt: "created_at",
  MinusCreatedAt: "-created_at",
  Amount: "amount",
  MinusAmount: "-amount",
  NetAmount: "net_amount",
  MinusNetAmount: "-net_amount",
  Customer: "customer",
  MinusCustomer: "-customer",
  Product: "product",
  MinusProduct: "-product",
  Discount: "discount",
  MinusDiscount: "-discount",
  Subscription: "subscription",
  MinusSubscription: "-subscription",
} as const;
export type OrderSortProperty = ClosedEnum<typeof OrderSortProperty>;

/** @internal */
export const OrderSortProperty$inboundSchema: z.ZodNativeEnum<
  typeof OrderSortProperty
> = z.nativeEnum(OrderSortProperty);

/** @internal */
export const OrderSortProperty$outboundSchema: z.ZodNativeEnum<
  typeof OrderSortProperty
> = OrderSortProperty$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrderSortProperty$ {
  /** @deprecated use `OrderSortProperty$inboundSchema` instead. */
  export const inboundSchema = OrderSortProperty$inboundSchema;
  /** @deprecated use `OrderSortProperty$outboundSchema` instead. */
  export const outboundSchema = OrderSortProperty$outboundSchema;
}
