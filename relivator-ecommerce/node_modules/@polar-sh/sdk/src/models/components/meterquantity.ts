/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type MeterQuantity = {
  /**
   * The timestamp for the current period.
   */
  timestamp: Date;
  /**
   * The quantity for the current period.
   */
  quantity: number;
};

/** @internal */
export const MeterQuantity$inboundSchema: z.ZodType<
  MeterQuantity,
  z.ZodTypeDef,
  unknown
> = z.object({
  timestamp: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  quantity: z.number(),
});

/** @internal */
export type MeterQuantity$Outbound = {
  timestamp: string;
  quantity: number;
};

/** @internal */
export const MeterQuantity$outboundSchema: z.ZodType<
  MeterQuantity$Outbound,
  z.ZodTypeDef,
  MeterQuantity
> = z.object({
  timestamp: z.date().transform(v => v.toISOString()),
  quantity: z.number(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterQuantity$ {
  /** @deprecated use `MeterQuantity$inboundSchema` instead. */
  export const inboundSchema = MeterQuantity$inboundSchema;
  /** @deprecated use `MeterQuantity$outboundSchema` instead. */
  export const outboundSchema = MeterQuantity$outboundSchema;
  /** @deprecated use `MeterQuantity$Outbound` instead. */
  export type Outbound = MeterQuantity$Outbound;
}

export function meterQuantityToJSON(meterQuantity: MeterQuantity): string {
  return JSON.stringify(MeterQuantity$outboundSchema.parse(meterQuantity));
}

export function meterQuantityFromJSON(
  jsonString: string,
): SafeParseResult<MeterQuantity, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterQuantity$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterQuantity' from JSON`,
  );
}
