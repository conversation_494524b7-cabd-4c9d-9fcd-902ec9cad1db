/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Customer,
  Customer$inboundSchema,
  Customer$Outbound,
  Customer$outboundSchema,
} from "./customer.js";

/**
 * A customer session that can be used to authenticate as a customer.
 */
export type CustomerSession = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  token: string;
  expiresAt: Date;
  customerPortalUrl: string;
  customerId: string;
  /**
   * A customer in an organization.
   */
  customer: Customer;
};

/** @internal */
export const CustomerSession$inboundSchema: z.ZodType<
  CustomerSession,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  token: z.string(),
  expires_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  customer_portal_url: z.string(),
  customer_id: z.string(),
  customer: Customer$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "expires_at": "expiresAt",
    "customer_portal_url": "customerPortalUrl",
    "customer_id": "customerId",
  });
});

/** @internal */
export type CustomerSession$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  token: string;
  expires_at: string;
  customer_portal_url: string;
  customer_id: string;
  customer: Customer$Outbound;
};

/** @internal */
export const CustomerSession$outboundSchema: z.ZodType<
  CustomerSession$Outbound,
  z.ZodTypeDef,
  CustomerSession
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  token: z.string(),
  expiresAt: z.date().transform(v => v.toISOString()),
  customerPortalUrl: z.string(),
  customerId: z.string(),
  customer: Customer$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    expiresAt: "expires_at",
    customerPortalUrl: "customer_portal_url",
    customerId: "customer_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerSession$ {
  /** @deprecated use `CustomerSession$inboundSchema` instead. */
  export const inboundSchema = CustomerSession$inboundSchema;
  /** @deprecated use `CustomerSession$outboundSchema` instead. */
  export const outboundSchema = CustomerSession$outboundSchema;
  /** @deprecated use `CustomerSession$Outbound` instead. */
  export type Outbound = CustomerSession$Outbound;
}

export function customerSessionToJSON(
  customerSession: CustomerSession,
): string {
  return JSON.stringify(CustomerSession$outboundSchema.parse(customerSession));
}

export function customerSessionFromJSON(
  jsonString: string,
): SafeParseResult<CustomerSession, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerSession$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerSession' from JSON`,
  );
}
