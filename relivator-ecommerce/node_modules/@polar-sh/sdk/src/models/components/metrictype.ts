/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const MetricType = {
  Scalar: "scalar",
  Currency: "currency",
  Percentage: "percentage",
} as const;
export type MetricType = ClosedEnum<typeof MetricType>;

/** @internal */
export const MetricType$inboundSchema: z.ZodNativeEnum<typeof MetricType> = z
  .nativeEnum(MetricType);

/** @internal */
export const MetricType$outboundSchema: z.ZodNativeEnum<typeof MetricType> =
  MetricType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricType$ {
  /** @deprecated use `MetricType$inboundSchema` instead. */
  export const inboundSchema = MetricType$inboundSchema;
  /** @deprecated use `MetricType$outboundSchema` instead. */
  export const outboundSchema = MetricType$outboundSchema;
}
