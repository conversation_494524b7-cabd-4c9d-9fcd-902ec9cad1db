/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type S3DownloadURL = {
  url: string;
  headers?: { [k: string]: string } | undefined;
  expiresAt: Date;
};

/** @internal */
export const S3DownloadURL$inboundSchema: z.ZodType<
  S3DownloadURL,
  z.ZodTypeDef,
  unknown
> = z.object({
  url: z.string(),
  headers: z.record(z.string()).optional(),
  expires_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
}).transform((v) => {
  return remap$(v, {
    "expires_at": "expiresAt",
  });
});

/** @internal */
export type S3DownloadURL$Outbound = {
  url: string;
  headers?: { [k: string]: string } | undefined;
  expires_at: string;
};

/** @internal */
export const S3DownloadURL$outboundSchema: z.ZodType<
  S3DownloadURL$Outbound,
  z.ZodTypeDef,
  S3DownloadURL
> = z.object({
  url: z.string(),
  headers: z.record(z.string()).optional(),
  expiresAt: z.date().transform(v => v.toISOString()),
}).transform((v) => {
  return remap$(v, {
    expiresAt: "expires_at",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace S3DownloadURL$ {
  /** @deprecated use `S3DownloadURL$inboundSchema` instead. */
  export const inboundSchema = S3DownloadURL$inboundSchema;
  /** @deprecated use `S3DownloadURL$outboundSchema` instead. */
  export const outboundSchema = S3DownloadURL$outboundSchema;
  /** @deprecated use `S3DownloadURL$Outbound` instead. */
  export type Outbound = S3DownloadURL$Outbound;
}

export function s3DownloadURLToJSON(s3DownloadURL: S3DownloadURL): string {
  return JSON.stringify(S3DownloadURL$outboundSchema.parse(s3DownloadURL));
}

export function s3DownloadURLFromJSON(
  jsonString: string,
): SafeParseResult<S3DownloadURL, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => S3DownloadURL$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'S3DownloadURL' from JSON`,
  );
}
