/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CheckoutLink,
  CheckoutLink$inboundSchema,
  CheckoutLink$Outbound,
  CheckoutLink$outboundSchema,
} from "./checkoutlink.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";

export type ListResourceCheckoutLink = {
  items: Array<CheckoutLink>;
  pagination: Pagination;
};

/** @internal */
export const ListResourceCheckoutLink$inboundSchema: z.ZodType<
  ListResourceCheckoutLink,
  z.ZodTypeDef,
  unknown
> = z.object({
  items: z.array(CheckoutLink$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ListResourceCheckoutLink$Outbound = {
  items: Array<CheckoutLink$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResourceCheckoutLink$outboundSchema: z.ZodType<
  ListResourceCheckoutLink$Outbound,
  z.ZodTypeDef,
  ListResourceCheckoutLink
> = z.object({
  items: z.array(CheckoutLink$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResourceCheckoutLink$ {
  /** @deprecated use `ListResourceCheckoutLink$inboundSchema` instead. */
  export const inboundSchema = ListResourceCheckoutLink$inboundSchema;
  /** @deprecated use `ListResourceCheckoutLink$outboundSchema` instead. */
  export const outboundSchema = ListResourceCheckoutLink$outboundSchema;
  /** @deprecated use `ListResourceCheckoutLink$Outbound` instead. */
  export type Outbound = ListResourceCheckoutLink$Outbound;
}

export function listResourceCheckoutLinkToJSON(
  listResourceCheckoutLink: ListResourceCheckoutLink,
): string {
  return JSON.stringify(
    ListResourceCheckoutLink$outboundSchema.parse(listResourceCheckoutLink),
  );
}

export function listResourceCheckoutLinkFromJSON(
  jsonString: string,
): SafeParseResult<ListResourceCheckoutLink, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListResourceCheckoutLink$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListResourceCheckoutLink' from JSON`,
  );
}
