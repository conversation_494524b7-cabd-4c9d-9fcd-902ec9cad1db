/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  PaymentMethodCardData,
  PaymentMethodCardData$inboundSchema,
  PaymentMethodCardData$Outbound,
  PaymentMethodCardData$outboundSchema,
} from "./paymentmethodcarddata.js";

export type PaymentMethodCard = {
  id: string;
  type?: "card" | undefined;
  createdAt: Date;
  default: boolean;
  card: PaymentMethodCardData;
};

/** @internal */
export const PaymentMethodCard$inboundSchema: z.ZodType<
  PaymentMethodCard,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  type: z.literal("card").optional(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  default: z.boolean(),
  card: PaymentMethodCardData$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
  });
});

/** @internal */
export type PaymentMethodCard$Outbound = {
  id: string;
  type: "card";
  created_at: string;
  default: boolean;
  card: PaymentMethodCardData$Outbound;
};

/** @internal */
export const PaymentMethodCard$outboundSchema: z.ZodType<
  PaymentMethodCard$Outbound,
  z.ZodTypeDef,
  PaymentMethodCard
> = z.object({
  id: z.string(),
  type: z.literal("card").default("card" as const),
  createdAt: z.date().transform(v => v.toISOString()),
  default: z.boolean(),
  card: PaymentMethodCardData$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace PaymentMethodCard$ {
  /** @deprecated use `PaymentMethodCard$inboundSchema` instead. */
  export const inboundSchema = PaymentMethodCard$inboundSchema;
  /** @deprecated use `PaymentMethodCard$outboundSchema` instead. */
  export const outboundSchema = PaymentMethodCard$outboundSchema;
  /** @deprecated use `PaymentMethodCard$Outbound` instead. */
  export type Outbound = PaymentMethodCard$Outbound;
}

export function paymentMethodCardToJSON(
  paymentMethodCard: PaymentMethodCard,
): string {
  return JSON.stringify(
    PaymentMethodCard$outboundSchema.parse(paymentMethodCard),
  );
}

export function paymentMethodCardFromJSON(
  jsonString: string,
): SafeParseResult<PaymentMethodCard, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => PaymentMethodCard$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'PaymentMethodCard' from JSON`,
  );
}
