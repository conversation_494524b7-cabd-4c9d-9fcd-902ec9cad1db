/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomFieldNumberProperties,
  CustomFieldNumberProperties$inboundSchema,
  CustomFieldNumberProperties$Outbound,
  CustomFieldNumberProperties$outboundSchema,
} from "./customfieldnumberproperties.js";

export type CustomFieldNumberMetadata = string | number | number | boolean;

/**
 * Schema for a custom field of type number.
 */
export type CustomFieldNumber = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  type?: "number" | undefined;
  /**
   * Identifier of the custom field. It'll be used as key when storing the value.
   */
  slug: string;
  /**
   * Name of the custom field.
   */
  name: string;
  /**
   * The ID of the organization owning the custom field.
   */
  organizationId: string;
  properties: CustomFieldNumberProperties;
};

/** @internal */
export const CustomFieldNumberMetadata$inboundSchema: z.ZodType<
  CustomFieldNumberMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomFieldNumberMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomFieldNumberMetadata$outboundSchema: z.ZodType<
  CustomFieldNumberMetadata$Outbound,
  z.ZodTypeDef,
  CustomFieldNumberMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldNumberMetadata$ {
  /** @deprecated use `CustomFieldNumberMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomFieldNumberMetadata$inboundSchema;
  /** @deprecated use `CustomFieldNumberMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomFieldNumberMetadata$outboundSchema;
  /** @deprecated use `CustomFieldNumberMetadata$Outbound` instead. */
  export type Outbound = CustomFieldNumberMetadata$Outbound;
}

export function customFieldNumberMetadataToJSON(
  customFieldNumberMetadata: CustomFieldNumberMetadata,
): string {
  return JSON.stringify(
    CustomFieldNumberMetadata$outboundSchema.parse(customFieldNumberMetadata),
  );
}

export function customFieldNumberMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldNumberMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldNumberMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldNumberMetadata' from JSON`,
  );
}

/** @internal */
export const CustomFieldNumber$inboundSchema: z.ZodType<
  CustomFieldNumber,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  type: z.literal("number").optional(),
  slug: z.string(),
  name: z.string(),
  organization_id: z.string(),
  properties: CustomFieldNumberProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type CustomFieldNumber$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  type: "number";
  slug: string;
  name: string;
  organization_id: string;
  properties: CustomFieldNumberProperties$Outbound;
};

/** @internal */
export const CustomFieldNumber$outboundSchema: z.ZodType<
  CustomFieldNumber$Outbound,
  z.ZodTypeDef,
  CustomFieldNumber
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  type: z.literal("number").default("number" as const),
  slug: z.string(),
  name: z.string(),
  organizationId: z.string(),
  properties: CustomFieldNumberProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldNumber$ {
  /** @deprecated use `CustomFieldNumber$inboundSchema` instead. */
  export const inboundSchema = CustomFieldNumber$inboundSchema;
  /** @deprecated use `CustomFieldNumber$outboundSchema` instead. */
  export const outboundSchema = CustomFieldNumber$outboundSchema;
  /** @deprecated use `CustomFieldNumber$Outbound` instead. */
  export type Outbound = CustomFieldNumber$Outbound;
}

export function customFieldNumberToJSON(
  customFieldNumber: CustomFieldNumber,
): string {
  return JSON.stringify(
    CustomFieldNumber$outboundSchema.parse(customFieldNumber),
  );
}

export function customFieldNumberFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldNumber, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldNumber$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldNumber' from JSON`,
  );
}
