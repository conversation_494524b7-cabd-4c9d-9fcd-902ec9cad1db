/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Schema for creating a customer session using a customer ID.
 */
export type CustomerSessionCustomerIDCreate = {
  /**
   * ID of the customer to create a session for.
   */
  customerId: string;
};

/** @internal */
export const CustomerSessionCustomerIDCreate$inboundSchema: z.ZodType<
  CustomerSessionCustomerIDCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  customer_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "customer_id": "customerId",
  });
});

/** @internal */
export type CustomerSessionCustomerIDCreate$Outbound = {
  customer_id: string;
};

/** @internal */
export const CustomerSessionCustomerIDCreate$outboundSchema: z.ZodType<
  CustomerSessionCustomerIDCreate$Outbound,
  z.ZodTypeDef,
  CustomerSessionCustomerIDCreate
> = z.object({
  customerId: z.string(),
}).transform((v) => {
  return remap$(v, {
    customerId: "customer_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerSessionCustomerIDCreate$ {
  /** @deprecated use `CustomerSessionCustomerIDCreate$inboundSchema` instead. */
  export const inboundSchema = CustomerSessionCustomerIDCreate$inboundSchema;
  /** @deprecated use `CustomerSessionCustomerIDCreate$outboundSchema` instead. */
  export const outboundSchema = CustomerSessionCustomerIDCreate$outboundSchema;
  /** @deprecated use `CustomerSessionCustomerIDCreate$Outbound` instead. */
  export type Outbound = CustomerSessionCustomerIDCreate$Outbound;
}

export function customerSessionCustomerIDCreateToJSON(
  customerSessionCustomerIDCreate: CustomerSessionCustomerIDCreate,
): string {
  return JSON.stringify(
    CustomerSessionCustomerIDCreate$outboundSchema.parse(
      customerSessionCustomerIDCreate,
    ),
  );
}

export function customerSessionCustomerIDCreateFromJSON(
  jsonString: string,
): SafeParseResult<CustomerSessionCustomerIDCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerSessionCustomerIDCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerSessionCustomerIDCreate' from JSON`,
  );
}
