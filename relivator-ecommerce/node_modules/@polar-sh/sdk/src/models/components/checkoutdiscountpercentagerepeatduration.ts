/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountDuration,
  DiscountDuration$inboundSchema,
  DiscountDuration$outboundSchema,
} from "./discountduration.js";
import {
  DiscountType,
  DiscountType$inboundSchema,
  DiscountType$outboundSchema,
} from "./discounttype.js";

/**
 * Schema for a percentage discount that is applied on every invoice
 *
 * @remarks
 * for a certain number of months.
 */
export type CheckoutDiscountPercentageRepeatDuration = {
  duration: DiscountDuration;
  durationInMonths: number;
  type: DiscountType;
  basisPoints: number;
  /**
   * The ID of the object.
   */
  id: string;
  name: string;
  code: string | null;
};

/** @internal */
export const CheckoutDiscountPercentageRepeatDuration$inboundSchema: z.ZodType<
  CheckoutDiscountPercentageRepeatDuration,
  z.ZodTypeDef,
  unknown
> = z.object({
  duration: DiscountDuration$inboundSchema,
  duration_in_months: z.number().int(),
  type: DiscountType$inboundSchema,
  basis_points: z.number().int(),
  id: z.string(),
  name: z.string(),
  code: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    "duration_in_months": "durationInMonths",
    "basis_points": "basisPoints",
  });
});

/** @internal */
export type CheckoutDiscountPercentageRepeatDuration$Outbound = {
  duration: string;
  duration_in_months: number;
  type: string;
  basis_points: number;
  id: string;
  name: string;
  code: string | null;
};

/** @internal */
export const CheckoutDiscountPercentageRepeatDuration$outboundSchema: z.ZodType<
  CheckoutDiscountPercentageRepeatDuration$Outbound,
  z.ZodTypeDef,
  CheckoutDiscountPercentageRepeatDuration
> = z.object({
  duration: DiscountDuration$outboundSchema,
  durationInMonths: z.number().int(),
  type: DiscountType$outboundSchema,
  basisPoints: z.number().int(),
  id: z.string(),
  name: z.string(),
  code: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    durationInMonths: "duration_in_months",
    basisPoints: "basis_points",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutDiscountPercentageRepeatDuration$ {
  /** @deprecated use `CheckoutDiscountPercentageRepeatDuration$inboundSchema` instead. */
  export const inboundSchema =
    CheckoutDiscountPercentageRepeatDuration$inboundSchema;
  /** @deprecated use `CheckoutDiscountPercentageRepeatDuration$outboundSchema` instead. */
  export const outboundSchema =
    CheckoutDiscountPercentageRepeatDuration$outboundSchema;
  /** @deprecated use `CheckoutDiscountPercentageRepeatDuration$Outbound` instead. */
  export type Outbound = CheckoutDiscountPercentageRepeatDuration$Outbound;
}

export function checkoutDiscountPercentageRepeatDurationToJSON(
  checkoutDiscountPercentageRepeatDuration:
    CheckoutDiscountPercentageRepeatDuration,
): string {
  return JSON.stringify(
    CheckoutDiscountPercentageRepeatDuration$outboundSchema.parse(
      checkoutDiscountPercentageRepeatDuration,
    ),
  );
}

export function checkoutDiscountPercentageRepeatDurationFromJSON(
  jsonString: string,
): SafeParseResult<
  CheckoutDiscountPercentageRepeatDuration,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      CheckoutDiscountPercentageRepeatDuration$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'CheckoutDiscountPercentageRepeatDuration' from JSON`,
  );
}
