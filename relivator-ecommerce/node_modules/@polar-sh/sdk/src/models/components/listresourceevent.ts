/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Event,
  Event$inboundSchema,
  Event$Outbound,
  Event$outboundSchema,
} from "./event.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";

export type ListResourceEvent = {
  items: Array<Event>;
  pagination: Pagination;
};

/** @internal */
export const ListResourceEvent$inboundSchema: z.ZodType<
  ListResourceEvent,
  z.ZodTypeDef,
  unknown
> = z.object({
  items: z.array(Event$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ListResourceEvent$Outbound = {
  items: Array<Event$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResourceEvent$outboundSchema: z.ZodType<
  ListResourceEvent$Outbound,
  z.ZodTypeDef,
  ListResourceEvent
> = z.object({
  items: z.array(Event$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResourceEvent$ {
  /** @deprecated use `ListResourceEvent$inboundSchema` instead. */
  export const inboundSchema = ListResourceEvent$inboundSchema;
  /** @deprecated use `ListResourceEvent$outboundSchema` instead. */
  export const outboundSchema = ListResourceEvent$outboundSchema;
  /** @deprecated use `ListResourceEvent$Outbound` instead. */
  export type Outbound = ListResourceEvent$Outbound;
}

export function listResourceEventToJSON(
  listResourceEvent: ListResourceEvent,
): string {
  return JSON.stringify(
    ListResourceEvent$outboundSchema.parse(listResourceEvent),
  );
}

export function listResourceEventFromJSON(
  jsonString: string,
): SafeParseResult<ListResourceEvent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListResourceEvent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListResourceEvent' from JSON`,
  );
}
