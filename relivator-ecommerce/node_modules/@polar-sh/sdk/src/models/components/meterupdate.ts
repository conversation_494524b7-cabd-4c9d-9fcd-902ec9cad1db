/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CountAggregation,
  CountAggregation$inboundSchema,
  CountAggregation$Outbound,
  CountAggregation$outboundSchema,
} from "./countaggregation.js";
import {
  Filter,
  Filter$inboundSchema,
  Filter$Outbound,
  Filter$outboundSchema,
} from "./filter.js";
import {
  PropertyAggregation,
  PropertyAggregation$inboundSchema,
  PropertyAggregation$Outbound,
  PropertyAggregation$outboundSchema,
} from "./propertyaggregation.js";

export type MeterUpdateMetadata = string | number | number | boolean;

export type Aggregation =
  | (CountAggregation & { func: "count" })
  | (PropertyAggregation & { func: "avg" })
  | (PropertyAggregation & { func: "max" })
  | (PropertyAggregation & { func: "min" })
  | (PropertyAggregation & { func: "sum" });

export type MeterUpdate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  /**
   * The name of the meter. Will be shown on customer's invoices and usage.
   */
  name?: string | null | undefined;
  /**
   * The filter to apply on events that'll be used to calculate the meter.
   */
  filter?: Filter | null | undefined;
  /**
   * The aggregation to apply on the filtered events to calculate the meter.
   */
  aggregation?:
    | (CountAggregation & { func: "count" })
    | (PropertyAggregation & { func: "avg" })
    | (PropertyAggregation & { func: "max" })
    | (PropertyAggregation & { func: "min" })
    | (PropertyAggregation & { func: "sum" })
    | null
    | undefined;
};

/** @internal */
export const MeterUpdateMetadata$inboundSchema: z.ZodType<
  MeterUpdateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type MeterUpdateMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const MeterUpdateMetadata$outboundSchema: z.ZodType<
  MeterUpdateMetadata$Outbound,
  z.ZodTypeDef,
  MeterUpdateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterUpdateMetadata$ {
  /** @deprecated use `MeterUpdateMetadata$inboundSchema` instead. */
  export const inboundSchema = MeterUpdateMetadata$inboundSchema;
  /** @deprecated use `MeterUpdateMetadata$outboundSchema` instead. */
  export const outboundSchema = MeterUpdateMetadata$outboundSchema;
  /** @deprecated use `MeterUpdateMetadata$Outbound` instead. */
  export type Outbound = MeterUpdateMetadata$Outbound;
}

export function meterUpdateMetadataToJSON(
  meterUpdateMetadata: MeterUpdateMetadata,
): string {
  return JSON.stringify(
    MeterUpdateMetadata$outboundSchema.parse(meterUpdateMetadata),
  );
}

export function meterUpdateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<MeterUpdateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterUpdateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterUpdateMetadata' from JSON`,
  );
}

/** @internal */
export const Aggregation$inboundSchema: z.ZodType<
  Aggregation,
  z.ZodTypeDef,
  unknown
> = z.union([
  CountAggregation$inboundSchema.and(
    z.object({ func: z.literal("count") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("avg") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("max") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("min") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$inboundSchema.and(
    z.object({ func: z.literal("sum") }).transform((v) => ({ func: v.func })),
  ),
]);

/** @internal */
export type Aggregation$Outbound =
  | (CountAggregation$Outbound & { func: "count" })
  | (PropertyAggregation$Outbound & { func: "avg" })
  | (PropertyAggregation$Outbound & { func: "max" })
  | (PropertyAggregation$Outbound & { func: "min" })
  | (PropertyAggregation$Outbound & { func: "sum" });

/** @internal */
export const Aggregation$outboundSchema: z.ZodType<
  Aggregation$Outbound,
  z.ZodTypeDef,
  Aggregation
> = z.union([
  CountAggregation$outboundSchema.and(
    z.object({ func: z.literal("count") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("avg") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("max") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("min") }).transform((v) => ({ func: v.func })),
  ),
  PropertyAggregation$outboundSchema.and(
    z.object({ func: z.literal("sum") }).transform((v) => ({ func: v.func })),
  ),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Aggregation$ {
  /** @deprecated use `Aggregation$inboundSchema` instead. */
  export const inboundSchema = Aggregation$inboundSchema;
  /** @deprecated use `Aggregation$outboundSchema` instead. */
  export const outboundSchema = Aggregation$outboundSchema;
  /** @deprecated use `Aggregation$Outbound` instead. */
  export type Outbound = Aggregation$Outbound;
}

export function aggregationToJSON(aggregation: Aggregation): string {
  return JSON.stringify(Aggregation$outboundSchema.parse(aggregation));
}

export function aggregationFromJSON(
  jsonString: string,
): SafeParseResult<Aggregation, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Aggregation$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Aggregation' from JSON`,
  );
}

/** @internal */
export const MeterUpdate$inboundSchema: z.ZodType<
  MeterUpdate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.nullable(z.string()).optional(),
  filter: z.nullable(Filter$inboundSchema).optional(),
  aggregation: z.nullable(
    z.union([
      CountAggregation$inboundSchema.and(
        z.object({ func: z.literal("count") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$inboundSchema.and(
        z.object({ func: z.literal("avg") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$inboundSchema.and(
        z.object({ func: z.literal("max") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$inboundSchema.and(
        z.object({ func: z.literal("min") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$inboundSchema.and(
        z.object({ func: z.literal("sum") }).transform((v) => ({
          func: v.func,
        })),
      ),
    ]),
  ).optional(),
});

/** @internal */
export type MeterUpdate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  name?: string | null | undefined;
  filter?: Filter$Outbound | null | undefined;
  aggregation?:
    | (CountAggregation$Outbound & { func: "count" })
    | (PropertyAggregation$Outbound & { func: "avg" })
    | (PropertyAggregation$Outbound & { func: "max" })
    | (PropertyAggregation$Outbound & { func: "min" })
    | (PropertyAggregation$Outbound & { func: "sum" })
    | null
    | undefined;
};

/** @internal */
export const MeterUpdate$outboundSchema: z.ZodType<
  MeterUpdate$Outbound,
  z.ZodTypeDef,
  MeterUpdate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.nullable(z.string()).optional(),
  filter: z.nullable(Filter$outboundSchema).optional(),
  aggregation: z.nullable(
    z.union([
      CountAggregation$outboundSchema.and(
        z.object({ func: z.literal("count") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$outboundSchema.and(
        z.object({ func: z.literal("avg") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$outboundSchema.and(
        z.object({ func: z.literal("max") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$outboundSchema.and(
        z.object({ func: z.literal("min") }).transform((v) => ({
          func: v.func,
        })),
      ),
      PropertyAggregation$outboundSchema.and(
        z.object({ func: z.literal("sum") }).transform((v) => ({
          func: v.func,
        })),
      ),
    ]),
  ).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterUpdate$ {
  /** @deprecated use `MeterUpdate$inboundSchema` instead. */
  export const inboundSchema = MeterUpdate$inboundSchema;
  /** @deprecated use `MeterUpdate$outboundSchema` instead. */
  export const outboundSchema = MeterUpdate$outboundSchema;
  /** @deprecated use `MeterUpdate$Outbound` instead. */
  export type Outbound = MeterUpdate$Outbound;
}

export function meterUpdateToJSON(meterUpdate: MeterUpdate): string {
  return JSON.stringify(MeterUpdate$outboundSchema.parse(meterUpdate));
}

export function meterUpdateFromJSON(
  jsonString: string,
): SafeParseResult<MeterUpdate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MeterUpdate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MeterUpdate' from JSON`,
  );
}
