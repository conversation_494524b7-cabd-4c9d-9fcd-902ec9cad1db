/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountDuration,
  DiscountDuration$inboundSchema,
  DiscountDuration$outboundSchema,
} from "./discountduration.js";
import {
  DiscountType,
  DiscountType$inboundSchema,
  DiscountType$outboundSchema,
} from "./discounttype.js";

/**
 * Schema for a fixed amount discount that is applied on every invoice
 *
 * @remarks
 * for a certain number of months.
 */
export type CheckoutDiscountFixedRepeatDuration = {
  duration: DiscountDuration;
  durationInMonths: number;
  type: DiscountType;
  amount: number;
  currency: string;
  /**
   * The ID of the object.
   */
  id: string;
  name: string;
  code: string | null;
};

/** @internal */
export const CheckoutDiscountFixedRepeatDuration$inboundSchema: z.ZodType<
  CheckoutDiscountFixedRepeatDuration,
  z.ZodTypeDef,
  unknown
> = z.object({
  duration: DiscountDuration$inboundSchema,
  duration_in_months: z.number().int(),
  type: DiscountType$inboundSchema,
  amount: z.number().int(),
  currency: z.string(),
  id: z.string(),
  name: z.string(),
  code: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    "duration_in_months": "durationInMonths",
  });
});

/** @internal */
export type CheckoutDiscountFixedRepeatDuration$Outbound = {
  duration: string;
  duration_in_months: number;
  type: string;
  amount: number;
  currency: string;
  id: string;
  name: string;
  code: string | null;
};

/** @internal */
export const CheckoutDiscountFixedRepeatDuration$outboundSchema: z.ZodType<
  CheckoutDiscountFixedRepeatDuration$Outbound,
  z.ZodTypeDef,
  CheckoutDiscountFixedRepeatDuration
> = z.object({
  duration: DiscountDuration$outboundSchema,
  durationInMonths: z.number().int(),
  type: DiscountType$outboundSchema,
  amount: z.number().int(),
  currency: z.string(),
  id: z.string(),
  name: z.string(),
  code: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    durationInMonths: "duration_in_months",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutDiscountFixedRepeatDuration$ {
  /** @deprecated use `CheckoutDiscountFixedRepeatDuration$inboundSchema` instead. */
  export const inboundSchema =
    CheckoutDiscountFixedRepeatDuration$inboundSchema;
  /** @deprecated use `CheckoutDiscountFixedRepeatDuration$outboundSchema` instead. */
  export const outboundSchema =
    CheckoutDiscountFixedRepeatDuration$outboundSchema;
  /** @deprecated use `CheckoutDiscountFixedRepeatDuration$Outbound` instead. */
  export type Outbound = CheckoutDiscountFixedRepeatDuration$Outbound;
}

export function checkoutDiscountFixedRepeatDurationToJSON(
  checkoutDiscountFixedRepeatDuration: CheckoutDiscountFixedRepeatDuration,
): string {
  return JSON.stringify(
    CheckoutDiscountFixedRepeatDuration$outboundSchema.parse(
      checkoutDiscountFixedRepeatDuration,
    ),
  );
}

export function checkoutDiscountFixedRepeatDurationFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutDiscountFixedRepeatDuration, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      CheckoutDiscountFixedRepeatDuration$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutDiscountFixedRepeatDuration' from JSON`,
  );
}
