/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type CustomerPortalOAuthAccount = {
  accountId: string;
  accountUsername: string | null;
};

/** @internal */
export const CustomerPortalOAuthAccount$inboundSchema: z.ZodType<
  CustomerPortalOAuthAccount,
  z.ZodTypeDef,
  unknown
> = z.object({
  account_id: z.string(),
  account_username: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    "account_id": "accountId",
    "account_username": "accountUsername",
  });
});

/** @internal */
export type CustomerPortalOAuthAccount$Outbound = {
  account_id: string;
  account_username: string | null;
};

/** @internal */
export const CustomerPortalOAuthAccount$outboundSchema: z.ZodType<
  CustomerPortalOAuthAccount$Outbound,
  z.ZodTypeDef,
  CustomerPortalOAuthAccount
> = z.object({
  accountId: z.string(),
  accountUsername: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    accountId: "account_id",
    accountUsername: "account_username",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerPortalOAuthAccount$ {
  /** @deprecated use `CustomerPortalOAuthAccount$inboundSchema` instead. */
  export const inboundSchema = CustomerPortalOAuthAccount$inboundSchema;
  /** @deprecated use `CustomerPortalOAuthAccount$outboundSchema` instead. */
  export const outboundSchema = CustomerPortalOAuthAccount$outboundSchema;
  /** @deprecated use `CustomerPortalOAuthAccount$Outbound` instead. */
  export type Outbound = CustomerPortalOAuthAccount$Outbound;
}

export function customerPortalOAuthAccountToJSON(
  customerPortalOAuthAccount: CustomerPortalOAuthAccount,
): string {
  return JSON.stringify(
    CustomerPortalOAuthAccount$outboundSchema.parse(customerPortalOAuthAccount),
  );
}

export function customerPortalOAuthAccountFromJSON(
  jsonString: string,
): SafeParseResult<CustomerPortalOAuthAccount, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerPortalOAuthAccount$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerPortalOAuthAccount' from JSON`,
  );
}
