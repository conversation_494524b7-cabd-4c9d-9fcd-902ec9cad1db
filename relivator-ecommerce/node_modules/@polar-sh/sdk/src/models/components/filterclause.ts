/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  FilterOperator,
  FilterOperator$inboundSchema,
  FilterOperator$outboundSchema,
} from "./filteroperator.js";

export type Value = string | number | boolean;

export type FilterClause = {
  property: string;
  operator: FilterOperator;
  value: string | number | boolean;
};

/** @internal */
export const Value$inboundSchema: z.ZodType<Value, z.ZodTypeDef, unknown> = z
  .union([z.string(), z.number().int(), z.boolean()]);

/** @internal */
export type Value$Outbound = string | number | boolean;

/** @internal */
export const Value$outboundSchema: z.ZodType<
  Value$Outbound,
  z.ZodTypeDef,
  Value
> = z.union([z.string(), z.number().int(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Value$ {
  /** @deprecated use `Value$inboundSchema` instead. */
  export const inboundSchema = Value$inboundSchema;
  /** @deprecated use `Value$outboundSchema` instead. */
  export const outboundSchema = Value$outboundSchema;
  /** @deprecated use `Value$Outbound` instead. */
  export type Outbound = Value$Outbound;
}

export function valueToJSON(value: Value): string {
  return JSON.stringify(Value$outboundSchema.parse(value));
}

export function valueFromJSON(
  jsonString: string,
): SafeParseResult<Value, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Value$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Value' from JSON`,
  );
}

/** @internal */
export const FilterClause$inboundSchema: z.ZodType<
  FilterClause,
  z.ZodTypeDef,
  unknown
> = z.object({
  property: z.string(),
  operator: FilterOperator$inboundSchema,
  value: z.union([z.string(), z.number().int(), z.boolean()]),
});

/** @internal */
export type FilterClause$Outbound = {
  property: string;
  operator: string;
  value: string | number | boolean;
};

/** @internal */
export const FilterClause$outboundSchema: z.ZodType<
  FilterClause$Outbound,
  z.ZodTypeDef,
  FilterClause
> = z.object({
  property: z.string(),
  operator: FilterOperator$outboundSchema,
  value: z.union([z.string(), z.number().int(), z.boolean()]),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace FilterClause$ {
  /** @deprecated use `FilterClause$inboundSchema` instead. */
  export const inboundSchema = FilterClause$inboundSchema;
  /** @deprecated use `FilterClause$outboundSchema` instead. */
  export const outboundSchema = FilterClause$outboundSchema;
  /** @deprecated use `FilterClause$Outbound` instead. */
  export type Outbound = FilterClause$Outbound;
}

export function filterClauseToJSON(filterClause: FilterClause): string {
  return JSON.stringify(FilterClause$outboundSchema.parse(filterClause));
}

export function filterClauseFromJSON(
  jsonString: string,
): SafeParseResult<FilterClause, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => FilterClause$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'FilterClause' from JSON`,
  );
}
