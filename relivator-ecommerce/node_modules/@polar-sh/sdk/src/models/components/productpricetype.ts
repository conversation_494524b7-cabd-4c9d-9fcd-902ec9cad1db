/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const ProductPriceType = {
  OneTime: "one_time",
  Recurring: "recurring",
} as const;
export type ProductPriceType = ClosedEnum<typeof ProductPriceType>;

/** @internal */
export const ProductPriceType$inboundSchema: z.ZodNativeEnum<
  typeof ProductPriceType
> = z.nativeEnum(ProductPriceType);

/** @internal */
export const ProductPriceType$outboundSchema: z.ZodNativeEnum<
  typeof ProductPriceType
> = ProductPriceType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ProductPriceType$ {
  /** @deprecated use `ProductPriceType$inboundSchema` instead. */
  export const inboundSchema = ProductPriceType$inboundSchema;
  /** @deprecated use `ProductPriceType$outboundSchema` instead. */
  export const outboundSchema = ProductPriceType$outboundSchema;
}
