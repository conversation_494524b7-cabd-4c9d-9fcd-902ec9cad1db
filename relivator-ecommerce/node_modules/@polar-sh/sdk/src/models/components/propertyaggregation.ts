/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const Func = {
  Sum: "sum",
  Max: "max",
  Min: "min",
  Avg: "avg",
} as const;
export type Func = ClosedEnum<typeof Func>;

export type PropertyAggregation = {
  func: Func;
  property: string;
};

/** @internal */
export const Func$inboundSchema: z.ZodNativeEnum<typeof Func> = z.nativeEnum(
  Func,
);

/** @internal */
export const Func$outboundSchema: z.ZodNativeEnum<typeof Func> =
  Func$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Func$ {
  /** @deprecated use `Func$inboundSchema` instead. */
  export const inboundSchema = Func$inboundSchema;
  /** @deprecated use `Func$outboundSchema` instead. */
  export const outboundSchema = Func$outboundSchema;
}

/** @internal */
export const PropertyAggregation$inboundSchema: z.ZodType<
  PropertyAggregation,
  z.ZodTypeDef,
  unknown
> = z.object({
  func: Func$inboundSchema,
  property: z.string(),
});

/** @internal */
export type PropertyAggregation$Outbound = {
  func: string;
  property: string;
};

/** @internal */
export const PropertyAggregation$outboundSchema: z.ZodType<
  PropertyAggregation$Outbound,
  z.ZodTypeDef,
  PropertyAggregation
> = z.object({
  func: Func$outboundSchema,
  property: z.string(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace PropertyAggregation$ {
  /** @deprecated use `PropertyAggregation$inboundSchema` instead. */
  export const inboundSchema = PropertyAggregation$inboundSchema;
  /** @deprecated use `PropertyAggregation$outboundSchema` instead. */
  export const outboundSchema = PropertyAggregation$outboundSchema;
  /** @deprecated use `PropertyAggregation$Outbound` instead. */
  export type Outbound = PropertyAggregation$Outbound;
}

export function propertyAggregationToJSON(
  propertyAggregation: PropertyAggregation,
): string {
  return JSON.stringify(
    PropertyAggregation$outboundSchema.parse(propertyAggregation),
  );
}

export function propertyAggregationFromJSON(
  jsonString: string,
): SafeParseResult<PropertyAggregation, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => PropertyAggregation$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'PropertyAggregation' from JSON`,
  );
}
