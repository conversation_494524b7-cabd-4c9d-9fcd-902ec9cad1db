/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountDuration,
  DiscountDuration$inboundSchema,
  DiscountDuration$outboundSchema,
} from "./discountduration.js";
import {
  DiscountType,
  DiscountType$inboundSchema,
  DiscountType$outboundSchema,
} from "./discounttype.js";

export type DiscountFixedRepeatDurationCreateMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * Schema to create a fixed amount discount that is applied on every invoice
 *
 * @remarks
 * for a certain number of months.
 */
export type DiscountFixedRepeatDurationCreate = {
  duration: DiscountDuration;
  /**
   * Number of months the discount should be applied.
   *
   * @remarks
   *
   * For this to work on yearly pricing, you should multiply this by 12.
   * For example, to apply the discount for 2 years, set this to 24.
   */
  durationInMonths: number;
  type: DiscountType;
  /**
   * Fixed amount to discount from the invoice total.
   */
  amount: number;
  /**
   * The currency. Currently, only `usd` is supported.
   */
  currency?: string | undefined;
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  /**
   * Name of the discount. Will be displayed to the customer when the discount is applied.
   */
  name: string;
  /**
   * Code customers can use to apply the discount during checkout. Must be between 3 and 256 characters long and contain only alphanumeric characters.If not provided, the discount can only be applied via the API.
   */
  code?: string | null | undefined;
  /**
   * Optional timestamp after which the discount is redeemable.
   */
  startsAt?: Date | null | undefined;
  /**
   * Optional timestamp after which the discount is no longer redeemable.
   */
  endsAt?: Date | null | undefined;
  /**
   * Optional maximum number of times the discount can be redeemed.
   */
  maxRedemptions?: number | null | undefined;
  products?: Array<string> | null | undefined;
  /**
   * The ID of the organization owning the discount. **Required unless you use an organization token.**
   */
  organizationId?: string | null | undefined;
};

/** @internal */
export const DiscountFixedRepeatDurationCreateMetadata$inboundSchema: z.ZodType<
  DiscountFixedRepeatDurationCreateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type DiscountFixedRepeatDurationCreateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const DiscountFixedRepeatDurationCreateMetadata$outboundSchema:
  z.ZodType<
    DiscountFixedRepeatDurationCreateMetadata$Outbound,
    z.ZodTypeDef,
    DiscountFixedRepeatDurationCreateMetadata
  > = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountFixedRepeatDurationCreateMetadata$ {
  /** @deprecated use `DiscountFixedRepeatDurationCreateMetadata$inboundSchema` instead. */
  export const inboundSchema =
    DiscountFixedRepeatDurationCreateMetadata$inboundSchema;
  /** @deprecated use `DiscountFixedRepeatDurationCreateMetadata$outboundSchema` instead. */
  export const outboundSchema =
    DiscountFixedRepeatDurationCreateMetadata$outboundSchema;
  /** @deprecated use `DiscountFixedRepeatDurationCreateMetadata$Outbound` instead. */
  export type Outbound = DiscountFixedRepeatDurationCreateMetadata$Outbound;
}

export function discountFixedRepeatDurationCreateMetadataToJSON(
  discountFixedRepeatDurationCreateMetadata:
    DiscountFixedRepeatDurationCreateMetadata,
): string {
  return JSON.stringify(
    DiscountFixedRepeatDurationCreateMetadata$outboundSchema.parse(
      discountFixedRepeatDurationCreateMetadata,
    ),
  );
}

export function discountFixedRepeatDurationCreateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<
  DiscountFixedRepeatDurationCreateMetadata,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      DiscountFixedRepeatDurationCreateMetadata$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'DiscountFixedRepeatDurationCreateMetadata' from JSON`,
  );
}

/** @internal */
export const DiscountFixedRepeatDurationCreate$inboundSchema: z.ZodType<
  DiscountFixedRepeatDurationCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  duration: DiscountDuration$inboundSchema,
  duration_in_months: z.number().int(),
  type: DiscountType$inboundSchema,
  amount: z.number().int(),
  currency: z.string().default("usd"),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.string(),
  code: z.nullable(z.string()).optional(),
  starts_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  ends_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  max_redemptions: z.nullable(z.number().int()).optional(),
  products: z.nullable(z.array(z.string())).optional(),
  organization_id: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    "duration_in_months": "durationInMonths",
    "starts_at": "startsAt",
    "ends_at": "endsAt",
    "max_redemptions": "maxRedemptions",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type DiscountFixedRepeatDurationCreate$Outbound = {
  duration: string;
  duration_in_months: number;
  type: string;
  amount: number;
  currency: string;
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  name: string;
  code?: string | null | undefined;
  starts_at?: string | null | undefined;
  ends_at?: string | null | undefined;
  max_redemptions?: number | null | undefined;
  products?: Array<string> | null | undefined;
  organization_id?: string | null | undefined;
};

/** @internal */
export const DiscountFixedRepeatDurationCreate$outboundSchema: z.ZodType<
  DiscountFixedRepeatDurationCreate$Outbound,
  z.ZodTypeDef,
  DiscountFixedRepeatDurationCreate
> = z.object({
  duration: DiscountDuration$outboundSchema,
  durationInMonths: z.number().int(),
  type: DiscountType$outboundSchema,
  amount: z.number().int(),
  currency: z.string().default("usd"),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.string(),
  code: z.nullable(z.string()).optional(),
  startsAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
  endsAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
  maxRedemptions: z.nullable(z.number().int()).optional(),
  products: z.nullable(z.array(z.string())).optional(),
  organizationId: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    durationInMonths: "duration_in_months",
    startsAt: "starts_at",
    endsAt: "ends_at",
    maxRedemptions: "max_redemptions",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountFixedRepeatDurationCreate$ {
  /** @deprecated use `DiscountFixedRepeatDurationCreate$inboundSchema` instead. */
  export const inboundSchema = DiscountFixedRepeatDurationCreate$inboundSchema;
  /** @deprecated use `DiscountFixedRepeatDurationCreate$outboundSchema` instead. */
  export const outboundSchema =
    DiscountFixedRepeatDurationCreate$outboundSchema;
  /** @deprecated use `DiscountFixedRepeatDurationCreate$Outbound` instead. */
  export type Outbound = DiscountFixedRepeatDurationCreate$Outbound;
}

export function discountFixedRepeatDurationCreateToJSON(
  discountFixedRepeatDurationCreate: DiscountFixedRepeatDurationCreate,
): string {
  return JSON.stringify(
    DiscountFixedRepeatDurationCreate$outboundSchema.parse(
      discountFixedRepeatDurationCreate,
    ),
  );
}

export function discountFixedRepeatDurationCreateFromJSON(
  jsonString: string,
): SafeParseResult<DiscountFixedRepeatDurationCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DiscountFixedRepeatDurationCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DiscountFixedRepeatDurationCreate' from JSON`,
  );
}
