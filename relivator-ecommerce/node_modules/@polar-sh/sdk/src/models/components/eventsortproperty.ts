/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const EventSortProperty = {
  Timestamp: "timestamp",
  MinusTimestamp: "-timestamp",
} as const;
export type EventSortProperty = ClosedEnum<typeof EventSortProperty>;

/** @internal */
export const EventSortProperty$inboundSchema: z.ZodNativeEnum<
  typeof EventSortProperty
> = z.nativeEnum(EventSortProperty);

/** @internal */
export const EventSortProperty$outboundSchema: z.ZodNativeEnum<
  typeof EventSortProperty
> = EventSortProperty$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace EventSortProperty$ {
  /** @deprecated use `EventSortProperty$inboundSchema` instead. */
  export const inboundSchema = EventSortProperty$inboundSchema;
  /** @deprecated use `EventSortProperty$outboundSchema` instead. */
  export const outboundSchema = EventSortProperty$outboundSchema;
}
