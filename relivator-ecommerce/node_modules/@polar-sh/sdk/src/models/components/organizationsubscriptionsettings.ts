/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  SubscriptionProrationBehavior,
  SubscriptionProrationBehavior$inboundSchema,
  SubscriptionProrationBehavior$outboundSchema,
} from "./subscriptionprorationbehavior.js";

export type OrganizationSubscriptionSettings = {
  allowMultipleSubscriptions: boolean;
  allowCustomerUpdates: boolean;
  prorationBehavior: SubscriptionProrationBehavior;
};

/** @internal */
export const OrganizationSubscriptionSettings$inboundSchema: z.ZodType<
  OrganizationSubscriptionSettings,
  z.ZodTypeDef,
  unknown
> = z.object({
  allow_multiple_subscriptions: z.boolean(),
  allow_customer_updates: z.boolean(),
  proration_behavior: SubscriptionProrationBehavior$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "allow_multiple_subscriptions": "allowMultipleSubscriptions",
    "allow_customer_updates": "allowCustomerUpdates",
    "proration_behavior": "prorationBehavior",
  });
});

/** @internal */
export type OrganizationSubscriptionSettings$Outbound = {
  allow_multiple_subscriptions: boolean;
  allow_customer_updates: boolean;
  proration_behavior: string;
};

/** @internal */
export const OrganizationSubscriptionSettings$outboundSchema: z.ZodType<
  OrganizationSubscriptionSettings$Outbound,
  z.ZodTypeDef,
  OrganizationSubscriptionSettings
> = z.object({
  allowMultipleSubscriptions: z.boolean(),
  allowCustomerUpdates: z.boolean(),
  prorationBehavior: SubscriptionProrationBehavior$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    allowMultipleSubscriptions: "allow_multiple_subscriptions",
    allowCustomerUpdates: "allow_customer_updates",
    prorationBehavior: "proration_behavior",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrganizationSubscriptionSettings$ {
  /** @deprecated use `OrganizationSubscriptionSettings$inboundSchema` instead. */
  export const inboundSchema = OrganizationSubscriptionSettings$inboundSchema;
  /** @deprecated use `OrganizationSubscriptionSettings$outboundSchema` instead. */
  export const outboundSchema = OrganizationSubscriptionSettings$outboundSchema;
  /** @deprecated use `OrganizationSubscriptionSettings$Outbound` instead. */
  export type Outbound = OrganizationSubscriptionSettings$Outbound;
}

export function organizationSubscriptionSettingsToJSON(
  organizationSubscriptionSettings: OrganizationSubscriptionSettings,
): string {
  return JSON.stringify(
    OrganizationSubscriptionSettings$outboundSchema.parse(
      organizationSubscriptionSettings,
    ),
  );
}

export function organizationSubscriptionSettingsFromJSON(
  jsonString: string,
): SafeParseResult<OrganizationSubscriptionSettings, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrganizationSubscriptionSettings$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrganizationSubscriptionSettings' from JSON`,
  );
}
