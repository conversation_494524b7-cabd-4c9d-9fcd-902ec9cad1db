/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type EventsIngestResponse = {
  /**
   * Number of events inserted.
   */
  inserted: number;
};

/** @internal */
export const EventsIngestResponse$inboundSchema: z.ZodType<
  EventsIngestResponse,
  z.ZodTypeDef,
  unknown
> = z.object({
  inserted: z.number().int(),
});

/** @internal */
export type EventsIngestResponse$Outbound = {
  inserted: number;
};

/** @internal */
export const EventsIngestResponse$outboundSchema: z.ZodType<
  EventsIngestResponse$Outbound,
  z.ZodTypeDef,
  EventsIngestResponse
> = z.object({
  inserted: z.number().int(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace EventsIngestResponse$ {
  /** @deprecated use `EventsIngestResponse$inboundSchema` instead. */
  export const inboundSchema = EventsIngestResponse$inboundSchema;
  /** @deprecated use `EventsIngestResponse$outboundSchema` instead. */
  export const outboundSchema = EventsIngestResponse$outboundSchema;
  /** @deprecated use `EventsIngestResponse$Outbound` instead. */
  export type Outbound = EventsIngestResponse$Outbound;
}

export function eventsIngestResponseToJSON(
  eventsIngestResponse: EventsIngestResponse,
): string {
  return JSON.stringify(
    EventsIngestResponse$outboundSchema.parse(eventsIngestResponse),
  );
}

export function eventsIngestResponseFromJSON(
  jsonString: string,
): SafeParseResult<EventsIngestResponse, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => EventsIngestResponse$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'EventsIngestResponse' from JSON`,
  );
}
