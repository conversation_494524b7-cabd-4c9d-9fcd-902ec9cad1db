/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Properties for a benefit of type `meter_unit`.
 */
export type BenefitMeterCreditProperties = {
  units: number;
  rollover: boolean;
  meterId: string;
};

/** @internal */
export const BenefitMeterCreditProperties$inboundSchema: z.ZodType<
  BenefitMeterCreditProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  units: z.number().int(),
  rollover: z.boolean(),
  meter_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "meter_id": "meterId",
  });
});

/** @internal */
export type BenefitMeterCreditProperties$Outbound = {
  units: number;
  rollover: boolean;
  meter_id: string;
};

/** @internal */
export const BenefitMeterCreditProperties$outboundSchema: z.ZodType<
  BenefitMeterCreditProperties$Outbound,
  z.ZodTypeDef,
  BenefitMeterCreditProperties
> = z.object({
  units: z.number().int(),
  rollover: z.boolean(),
  meterId: z.string(),
}).transform((v) => {
  return remap$(v, {
    meterId: "meter_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitMeterCreditProperties$ {
  /** @deprecated use `BenefitMeterCreditProperties$inboundSchema` instead. */
  export const inboundSchema = BenefitMeterCreditProperties$inboundSchema;
  /** @deprecated use `BenefitMeterCreditProperties$outboundSchema` instead. */
  export const outboundSchema = BenefitMeterCreditProperties$outboundSchema;
  /** @deprecated use `BenefitMeterCreditProperties$Outbound` instead. */
  export type Outbound = BenefitMeterCreditProperties$Outbound;
}

export function benefitMeterCreditPropertiesToJSON(
  benefitMeterCreditProperties: BenefitMeterCreditProperties,
): string {
  return JSON.stringify(
    BenefitMeterCreditProperties$outboundSchema.parse(
      benefitMeterCreditProperties,
    ),
  );
}

export function benefitMeterCreditPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<BenefitMeterCreditProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitMeterCreditProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitMeterCreditProperties' from JSON`,
  );
}
