/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountDuration,
  DiscountDuration$inboundSchema,
  DiscountDuration$outboundSchema,
} from "./discountduration.js";
import {
  DiscountType,
  DiscountType$inboundSchema,
  DiscountType$outboundSchema,
} from "./discounttype.js";

export type DiscountFixedOnceForeverDurationBaseMetadata =
  | string
  | number
  | number
  | boolean;

export type DiscountFixedOnceForeverDurationBase = {
  duration: DiscountDuration;
  type: DiscountType;
  amount: number;
  currency: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Name of the discount. Will be displayed to the customer when the discount is applied.
   */
  name: string;
  /**
   * Code customers can use to apply the discount during checkout.
   */
  code: string | null;
  /**
   * Timestamp after which the discount is redeemable.
   */
  startsAt: Date | null;
  /**
   * Timestamp after which the discount is no longer redeemable.
   */
  endsAt: Date | null;
  /**
   * Maximum number of times the discount can be redeemed.
   */
  maxRedemptions: number | null;
  /**
   * Number of times the discount has been redeemed.
   */
  redemptionsCount: number;
  /**
   * The organization ID.
   */
  organizationId: string;
};

/** @internal */
export const DiscountFixedOnceForeverDurationBaseMetadata$inboundSchema:
  z.ZodType<
    DiscountFixedOnceForeverDurationBaseMetadata,
    z.ZodTypeDef,
    unknown
  > = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type DiscountFixedOnceForeverDurationBaseMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const DiscountFixedOnceForeverDurationBaseMetadata$outboundSchema:
  z.ZodType<
    DiscountFixedOnceForeverDurationBaseMetadata$Outbound,
    z.ZodTypeDef,
    DiscountFixedOnceForeverDurationBaseMetadata
  > = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountFixedOnceForeverDurationBaseMetadata$ {
  /** @deprecated use `DiscountFixedOnceForeverDurationBaseMetadata$inboundSchema` instead. */
  export const inboundSchema =
    DiscountFixedOnceForeverDurationBaseMetadata$inboundSchema;
  /** @deprecated use `DiscountFixedOnceForeverDurationBaseMetadata$outboundSchema` instead. */
  export const outboundSchema =
    DiscountFixedOnceForeverDurationBaseMetadata$outboundSchema;
  /** @deprecated use `DiscountFixedOnceForeverDurationBaseMetadata$Outbound` instead. */
  export type Outbound = DiscountFixedOnceForeverDurationBaseMetadata$Outbound;
}

export function discountFixedOnceForeverDurationBaseMetadataToJSON(
  discountFixedOnceForeverDurationBaseMetadata:
    DiscountFixedOnceForeverDurationBaseMetadata,
): string {
  return JSON.stringify(
    DiscountFixedOnceForeverDurationBaseMetadata$outboundSchema.parse(
      discountFixedOnceForeverDurationBaseMetadata,
    ),
  );
}

export function discountFixedOnceForeverDurationBaseMetadataFromJSON(
  jsonString: string,
): SafeParseResult<
  DiscountFixedOnceForeverDurationBaseMetadata,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      DiscountFixedOnceForeverDurationBaseMetadata$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'DiscountFixedOnceForeverDurationBaseMetadata' from JSON`,
  );
}

/** @internal */
export const DiscountFixedOnceForeverDurationBase$inboundSchema: z.ZodType<
  DiscountFixedOnceForeverDurationBase,
  z.ZodTypeDef,
  unknown
> = z.object({
  duration: DiscountDuration$inboundSchema,
  type: DiscountType$inboundSchema,
  amount: z.number().int(),
  currency: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  name: z.string(),
  code: z.nullable(z.string()),
  starts_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  ends_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  max_redemptions: z.nullable(z.number().int()),
  redemptions_count: z.number().int(),
  organization_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "starts_at": "startsAt",
    "ends_at": "endsAt",
    "max_redemptions": "maxRedemptions",
    "redemptions_count": "redemptionsCount",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type DiscountFixedOnceForeverDurationBase$Outbound = {
  duration: string;
  type: string;
  amount: number;
  currency: string;
  created_at: string;
  modified_at: string | null;
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  name: string;
  code: string | null;
  starts_at: string | null;
  ends_at: string | null;
  max_redemptions: number | null;
  redemptions_count: number;
  organization_id: string;
};

/** @internal */
export const DiscountFixedOnceForeverDurationBase$outboundSchema: z.ZodType<
  DiscountFixedOnceForeverDurationBase$Outbound,
  z.ZodTypeDef,
  DiscountFixedOnceForeverDurationBase
> = z.object({
  duration: DiscountDuration$outboundSchema,
  type: DiscountType$outboundSchema,
  amount: z.number().int(),
  currency: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  name: z.string(),
  code: z.nullable(z.string()),
  startsAt: z.nullable(z.date().transform(v => v.toISOString())),
  endsAt: z.nullable(z.date().transform(v => v.toISOString())),
  maxRedemptions: z.nullable(z.number().int()),
  redemptionsCount: z.number().int(),
  organizationId: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    startsAt: "starts_at",
    endsAt: "ends_at",
    maxRedemptions: "max_redemptions",
    redemptionsCount: "redemptions_count",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountFixedOnceForeverDurationBase$ {
  /** @deprecated use `DiscountFixedOnceForeverDurationBase$inboundSchema` instead. */
  export const inboundSchema =
    DiscountFixedOnceForeverDurationBase$inboundSchema;
  /** @deprecated use `DiscountFixedOnceForeverDurationBase$outboundSchema` instead. */
  export const outboundSchema =
    DiscountFixedOnceForeverDurationBase$outboundSchema;
  /** @deprecated use `DiscountFixedOnceForeverDurationBase$Outbound` instead. */
  export type Outbound = DiscountFixedOnceForeverDurationBase$Outbound;
}

export function discountFixedOnceForeverDurationBaseToJSON(
  discountFixedOnceForeverDurationBase: DiscountFixedOnceForeverDurationBase,
): string {
  return JSON.stringify(
    DiscountFixedOnceForeverDurationBase$outboundSchema.parse(
      discountFixedOnceForeverDurationBase,
    ),
  );
}

export function discountFixedOnceForeverDurationBaseFromJSON(
  jsonString: string,
): SafeParseResult<DiscountFixedOnceForeverDurationBase, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      DiscountFixedOnceForeverDurationBase$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DiscountFixedOnceForeverDurationBase' from JSON`,
  );
}
