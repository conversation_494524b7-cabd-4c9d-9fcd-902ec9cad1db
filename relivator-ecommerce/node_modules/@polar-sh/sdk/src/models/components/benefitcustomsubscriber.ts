/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitCustomSubscriberProperties,
  BenefitCustomSubscriberProperties$inboundSchema,
  BenefitCustomSubscriberProperties$Outbound,
  BenefitCustomSubscriberProperties$outboundSchema,
} from "./benefitcustomsubscriberproperties.js";
import {
  Organization,
  Organization$inboundSchema,
  Organization$Outbound,
  Organization$outboundSchema,
} from "./organization.js";

export type BenefitCustomSubscriberMetadata =
  | string
  | number
  | number
  | boolean;

export type BenefitCustomSubscriber = {
  /**
   * The ID of the benefit.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  type?: "custom" | undefined;
  /**
   * The description of the benefit.
   */
  description: string;
  /**
   * Whether the benefit is selectable when creating a product.
   */
  selectable: boolean;
  /**
   * Whether the benefit is deletable.
   */
  deletable: boolean;
  /**
   * The ID of the organization owning the benefit.
   */
  organizationId: string;
  metadata: { [k: string]: string | number | number | boolean };
  organization: Organization;
  /**
   * Properties available to subscribers for a benefit of type `custom`.
   */
  properties: BenefitCustomSubscriberProperties;
};

/** @internal */
export const BenefitCustomSubscriberMetadata$inboundSchema: z.ZodType<
  BenefitCustomSubscriberMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitCustomSubscriberMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitCustomSubscriberMetadata$outboundSchema: z.ZodType<
  BenefitCustomSubscriberMetadata$Outbound,
  z.ZodTypeDef,
  BenefitCustomSubscriberMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitCustomSubscriberMetadata$ {
  /** @deprecated use `BenefitCustomSubscriberMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitCustomSubscriberMetadata$inboundSchema;
  /** @deprecated use `BenefitCustomSubscriberMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitCustomSubscriberMetadata$outboundSchema;
  /** @deprecated use `BenefitCustomSubscriberMetadata$Outbound` instead. */
  export type Outbound = BenefitCustomSubscriberMetadata$Outbound;
}

export function benefitCustomSubscriberMetadataToJSON(
  benefitCustomSubscriberMetadata: BenefitCustomSubscriberMetadata,
): string {
  return JSON.stringify(
    BenefitCustomSubscriberMetadata$outboundSchema.parse(
      benefitCustomSubscriberMetadata,
    ),
  );
}

export function benefitCustomSubscriberMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitCustomSubscriberMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitCustomSubscriberMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitCustomSubscriberMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitCustomSubscriber$inboundSchema: z.ZodType<
  BenefitCustomSubscriber,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  type: z.literal("custom").optional(),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organization_id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  organization: Organization$inboundSchema,
  properties: BenefitCustomSubscriberProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitCustomSubscriber$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  type: "custom";
  description: string;
  selectable: boolean;
  deletable: boolean;
  organization_id: string;
  metadata: { [k: string]: string | number | number | boolean };
  organization: Organization$Outbound;
  properties: BenefitCustomSubscriberProperties$Outbound;
};

/** @internal */
export const BenefitCustomSubscriber$outboundSchema: z.ZodType<
  BenefitCustomSubscriber$Outbound,
  z.ZodTypeDef,
  BenefitCustomSubscriber
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  type: z.literal("custom").default("custom" as const),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organizationId: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  organization: Organization$outboundSchema,
  properties: BenefitCustomSubscriberProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitCustomSubscriber$ {
  /** @deprecated use `BenefitCustomSubscriber$inboundSchema` instead. */
  export const inboundSchema = BenefitCustomSubscriber$inboundSchema;
  /** @deprecated use `BenefitCustomSubscriber$outboundSchema` instead. */
  export const outboundSchema = BenefitCustomSubscriber$outboundSchema;
  /** @deprecated use `BenefitCustomSubscriber$Outbound` instead. */
  export type Outbound = BenefitCustomSubscriber$Outbound;
}

export function benefitCustomSubscriberToJSON(
  benefitCustomSubscriber: BenefitCustomSubscriber,
): string {
  return JSON.stringify(
    BenefitCustomSubscriber$outboundSchema.parse(benefitCustomSubscriber),
  );
}

export function benefitCustomSubscriberFromJSON(
  jsonString: string,
): SafeParseResult<BenefitCustomSubscriber, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitCustomSubscriber$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitCustomSubscriber' from JSON`,
  );
}
