/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Schema to attach a custom field to a resource.
 */
export type AttachedCustomFieldCreate = {
  /**
   * ID of the custom field to attach.
   */
  customFieldId: string;
  /**
   * Whether the value is required for this custom field.
   */
  required: boolean;
};

/** @internal */
export const AttachedCustomFieldCreate$inboundSchema: z.ZodType<
  AttachedCustomFieldCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  custom_field_id: z.string(),
  required: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    "custom_field_id": "customFieldId",
  });
});

/** @internal */
export type AttachedCustomFieldCreate$Outbound = {
  custom_field_id: string;
  required: boolean;
};

/** @internal */
export const AttachedCustomFieldCreate$outboundSchema: z.ZodType<
  AttachedCustomFieldCreate$Outbound,
  z.ZodTypeDef,
  AttachedCustomFieldCreate
> = z.object({
  customFieldId: z.string(),
  required: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    customFieldId: "custom_field_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace AttachedCustomFieldCreate$ {
  /** @deprecated use `AttachedCustomFieldCreate$inboundSchema` instead. */
  export const inboundSchema = AttachedCustomFieldCreate$inboundSchema;
  /** @deprecated use `AttachedCustomFieldCreate$outboundSchema` instead. */
  export const outboundSchema = AttachedCustomFieldCreate$outboundSchema;
  /** @deprecated use `AttachedCustomFieldCreate$Outbound` instead. */
  export type Outbound = AttachedCustomFieldCreate$Outbound;
}

export function attachedCustomFieldCreateToJSON(
  attachedCustomFieldCreate: AttachedCustomFieldCreate,
): string {
  return JSON.stringify(
    AttachedCustomFieldCreate$outboundSchema.parse(attachedCustomFieldCreate),
  );
}

export function attachedCustomFieldCreateFromJSON(
  jsonString: string,
): SafeParseResult<AttachedCustomFieldCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => AttachedCustomFieldCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'AttachedCustomFieldCreate' from JSON`,
  );
}
