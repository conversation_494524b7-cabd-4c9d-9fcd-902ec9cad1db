/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Additional metadata for a card payment method.
 */
export type CardPaymentMetadata = {
  /**
   * The brand of the card used for the payment.
   */
  brand: string;
  /**
   * The last 4 digits of the card number.
   */
  last4: string;
};

/** @internal */
export const CardPaymentMetadata$inboundSchema: z.ZodType<
  CardPaymentMetadata,
  z.ZodTypeDef,
  unknown
> = z.object({
  brand: z.string(),
  last4: z.string(),
});

/** @internal */
export type CardPaymentMetadata$Outbound = {
  brand: string;
  last4: string;
};

/** @internal */
export const CardPaymentMetadata$outboundSchema: z.ZodType<
  CardPaymentMetadata$Outbound,
  z.ZodTypeDef,
  CardPaymentMetadata
> = z.object({
  brand: z.string(),
  last4: z.string(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CardPaymentMetadata$ {
  /** @deprecated use `CardPaymentMetadata$inboundSchema` instead. */
  export const inboundSchema = CardPaymentMetadata$inboundSchema;
  /** @deprecated use `CardPaymentMetadata$outboundSchema` instead. */
  export const outboundSchema = CardPaymentMetadata$outboundSchema;
  /** @deprecated use `CardPaymentMetadata$Outbound` instead. */
  export type Outbound = CardPaymentMetadata$Outbound;
}

export function cardPaymentMetadataToJSON(
  cardPaymentMetadata: CardPaymentMetadata,
): string {
  return JSON.stringify(
    CardPaymentMetadata$outboundSchema.parse(cardPaymentMetadata),
  );
}

export function cardPaymentMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CardPaymentMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CardPaymentMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CardPaymentMetadata' from JSON`,
  );
}
