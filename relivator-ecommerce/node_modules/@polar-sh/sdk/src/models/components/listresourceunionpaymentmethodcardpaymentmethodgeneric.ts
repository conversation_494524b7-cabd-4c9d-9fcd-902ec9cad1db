/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";
import {
  PaymentMethodCard,
  PaymentMethodCard$inboundSchema,
  PaymentMethodCard$Outbound,
  PaymentMethodCard$outboundSchema,
} from "./paymentmethodcard.js";
import {
  PaymentMethodGeneric,
  PaymentMethodGeneric$inboundSchema,
  PaymentMethodGeneric$Outbound,
  PaymentMethodGeneric$outboundSchema,
} from "./paymentmethodgeneric.js";

export type Items = PaymentMethodGeneric | PaymentMethodCard;

export type ListResourceUnionPaymentMethodCardPaymentMethodGeneric = {
  items: Array<PaymentMethodGeneric | PaymentMethodCard>;
  pagination: Pagination;
};

/** @internal */
export const Items$inboundSchema: z.ZodType<Items, z.ZodTypeDef, unknown> = z
  .union([PaymentMethodGeneric$inboundSchema, PaymentMethodCard$inboundSchema]);

/** @internal */
export type Items$Outbound =
  | PaymentMethodGeneric$Outbound
  | PaymentMethodCard$Outbound;

/** @internal */
export const Items$outboundSchema: z.ZodType<
  Items$Outbound,
  z.ZodTypeDef,
  Items
> = z.union([
  PaymentMethodGeneric$outboundSchema,
  PaymentMethodCard$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Items$ {
  /** @deprecated use `Items$inboundSchema` instead. */
  export const inboundSchema = Items$inboundSchema;
  /** @deprecated use `Items$outboundSchema` instead. */
  export const outboundSchema = Items$outboundSchema;
  /** @deprecated use `Items$Outbound` instead. */
  export type Outbound = Items$Outbound;
}

export function itemsToJSON(items: Items): string {
  return JSON.stringify(Items$outboundSchema.parse(items));
}

export function itemsFromJSON(
  jsonString: string,
): SafeParseResult<Items, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Items$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Items' from JSON`,
  );
}

/** @internal */
export const ListResourceUnionPaymentMethodCardPaymentMethodGeneric$inboundSchema:
  z.ZodType<
    ListResourceUnionPaymentMethodCardPaymentMethodGeneric,
    z.ZodTypeDef,
    unknown
  > = z.object({
    items: z.array(
      z.union([
        PaymentMethodGeneric$inboundSchema,
        PaymentMethodCard$inboundSchema,
      ]),
    ),
    pagination: Pagination$inboundSchema,
  });

/** @internal */
export type ListResourceUnionPaymentMethodCardPaymentMethodGeneric$Outbound = {
  items: Array<PaymentMethodGeneric$Outbound | PaymentMethodCard$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResourceUnionPaymentMethodCardPaymentMethodGeneric$outboundSchema:
  z.ZodType<
    ListResourceUnionPaymentMethodCardPaymentMethodGeneric$Outbound,
    z.ZodTypeDef,
    ListResourceUnionPaymentMethodCardPaymentMethodGeneric
  > = z.object({
    items: z.array(
      z.union([
        PaymentMethodGeneric$outboundSchema,
        PaymentMethodCard$outboundSchema,
      ]),
    ),
    pagination: Pagination$outboundSchema,
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResourceUnionPaymentMethodCardPaymentMethodGeneric$ {
  /** @deprecated use `ListResourceUnionPaymentMethodCardPaymentMethodGeneric$inboundSchema` instead. */
  export const inboundSchema =
    ListResourceUnionPaymentMethodCardPaymentMethodGeneric$inboundSchema;
  /** @deprecated use `ListResourceUnionPaymentMethodCardPaymentMethodGeneric$outboundSchema` instead. */
  export const outboundSchema =
    ListResourceUnionPaymentMethodCardPaymentMethodGeneric$outboundSchema;
  /** @deprecated use `ListResourceUnionPaymentMethodCardPaymentMethodGeneric$Outbound` instead. */
  export type Outbound =
    ListResourceUnionPaymentMethodCardPaymentMethodGeneric$Outbound;
}

export function listResourceUnionPaymentMethodCardPaymentMethodGenericToJSON(
  listResourceUnionPaymentMethodCardPaymentMethodGeneric:
    ListResourceUnionPaymentMethodCardPaymentMethodGeneric,
): string {
  return JSON.stringify(
    ListResourceUnionPaymentMethodCardPaymentMethodGeneric$outboundSchema.parse(
      listResourceUnionPaymentMethodCardPaymentMethodGeneric,
    ),
  );
}

export function listResourceUnionPaymentMethodCardPaymentMethodGenericFromJSON(
  jsonString: string,
): SafeParseResult<
  ListResourceUnionPaymentMethodCardPaymentMethodGeneric,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      ListResourceUnionPaymentMethodCardPaymentMethodGeneric$inboundSchema
        .parse(JSON.parse(x)),
    `Failed to parse 'ListResourceUnionPaymentMethodCardPaymentMethodGeneric' from JSON`,
  );
}
