/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitDiscordCreateProperties,
  BenefitDiscordCreateProperties$inboundSchema,
  BenefitDiscordCreateProperties$Outbound,
  BenefitDiscordCreateProperties$outboundSchema,
} from "./benefitdiscordcreateproperties.js";

export type BenefitDiscordUpdateMetadata = string | number | number | boolean;

export type BenefitDiscordUpdate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  /**
   * The description of the benefit. Will be displayed on products having this benefit.
   */
  description?: string | null | undefined;
  type?: "discord" | undefined;
  properties?: BenefitDiscordCreateProperties | null | undefined;
};

/** @internal */
export const BenefitDiscordUpdateMetadata$inboundSchema: z.ZodType<
  BenefitDiscordUpdateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitDiscordUpdateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitDiscordUpdateMetadata$outboundSchema: z.ZodType<
  BenefitDiscordUpdateMetadata$Outbound,
  z.ZodTypeDef,
  BenefitDiscordUpdateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDiscordUpdateMetadata$ {
  /** @deprecated use `BenefitDiscordUpdateMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitDiscordUpdateMetadata$inboundSchema;
  /** @deprecated use `BenefitDiscordUpdateMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitDiscordUpdateMetadata$outboundSchema;
  /** @deprecated use `BenefitDiscordUpdateMetadata$Outbound` instead. */
  export type Outbound = BenefitDiscordUpdateMetadata$Outbound;
}

export function benefitDiscordUpdateMetadataToJSON(
  benefitDiscordUpdateMetadata: BenefitDiscordUpdateMetadata,
): string {
  return JSON.stringify(
    BenefitDiscordUpdateMetadata$outboundSchema.parse(
      benefitDiscordUpdateMetadata,
    ),
  );
}

export function benefitDiscordUpdateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitDiscordUpdateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitDiscordUpdateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitDiscordUpdateMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitDiscordUpdate$inboundSchema: z.ZodType<
  BenefitDiscordUpdate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  description: z.nullable(z.string()).optional(),
  type: z.literal("discord").optional(),
  properties: z.nullable(BenefitDiscordCreateProperties$inboundSchema)
    .optional(),
});

/** @internal */
export type BenefitDiscordUpdate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  description?: string | null | undefined;
  type: "discord";
  properties?: BenefitDiscordCreateProperties$Outbound | null | undefined;
};

/** @internal */
export const BenefitDiscordUpdate$outboundSchema: z.ZodType<
  BenefitDiscordUpdate$Outbound,
  z.ZodTypeDef,
  BenefitDiscordUpdate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  description: z.nullable(z.string()).optional(),
  type: z.literal("discord").default("discord" as const),
  properties: z.nullable(BenefitDiscordCreateProperties$outboundSchema)
    .optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDiscordUpdate$ {
  /** @deprecated use `BenefitDiscordUpdate$inboundSchema` instead. */
  export const inboundSchema = BenefitDiscordUpdate$inboundSchema;
  /** @deprecated use `BenefitDiscordUpdate$outboundSchema` instead. */
  export const outboundSchema = BenefitDiscordUpdate$outboundSchema;
  /** @deprecated use `BenefitDiscordUpdate$Outbound` instead. */
  export type Outbound = BenefitDiscordUpdate$Outbound;
}

export function benefitDiscordUpdateToJSON(
  benefitDiscordUpdate: BenefitDiscordUpdate,
): string {
  return JSON.stringify(
    BenefitDiscordUpdate$outboundSchema.parse(benefitDiscordUpdate),
  );
}

export function benefitDiscordUpdateFromJSON(
  jsonString: string,
): SafeParseResult<BenefitDiscordUpdate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitDiscordUpdate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitDiscordUpdate' from JSON`,
  );
}
