/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  SubscriptionRecurringInterval,
  SubscriptionRecurringInterval$inboundSchema,
  SubscriptionRecurringInterval$outboundSchema,
} from "./subscriptionrecurringinterval.js";

/**
 * A free recurring price for a product, i.e. a subscription.
 *
 * @remarks
 *
 * **Deprecated**: The recurring interval should be set on the product itself.
 */
export type LegacyRecurringProductPriceFree = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the price.
   */
  id: string;
  amountType?: "free" | undefined;
  /**
   * Whether the price is archived and no longer available.
   */
  isArchived: boolean;
  /**
   * The ID of the product owning the price.
   */
  productId: string;
  /**
   * The type of the price.
   */
  type?: "recurring" | undefined;
  recurringInterval: SubscriptionRecurringInterval;
  legacy?: true | undefined;
};

/** @internal */
export const LegacyRecurringProductPriceFree$inboundSchema: z.ZodType<
  LegacyRecurringProductPriceFree,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  amount_type: z.literal("free").optional(),
  is_archived: z.boolean(),
  product_id: z.string(),
  type: z.literal("recurring").optional(),
  recurring_interval: SubscriptionRecurringInterval$inboundSchema,
  legacy: z.literal(true).optional(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "amount_type": "amountType",
    "is_archived": "isArchived",
    "product_id": "productId",
    "recurring_interval": "recurringInterval",
  });
});

/** @internal */
export type LegacyRecurringProductPriceFree$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  amount_type: "free";
  is_archived: boolean;
  product_id: string;
  type: "recurring";
  recurring_interval: string;
  legacy: true;
};

/** @internal */
export const LegacyRecurringProductPriceFree$outboundSchema: z.ZodType<
  LegacyRecurringProductPriceFree$Outbound,
  z.ZodTypeDef,
  LegacyRecurringProductPriceFree
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  amountType: z.literal("free").default("free" as const),
  isArchived: z.boolean(),
  productId: z.string(),
  type: z.literal("recurring").default("recurring" as const),
  recurringInterval: SubscriptionRecurringInterval$outboundSchema,
  legacy: z.literal(true).default(true as const),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    amountType: "amount_type",
    isArchived: "is_archived",
    productId: "product_id",
    recurringInterval: "recurring_interval",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LegacyRecurringProductPriceFree$ {
  /** @deprecated use `LegacyRecurringProductPriceFree$inboundSchema` instead. */
  export const inboundSchema = LegacyRecurringProductPriceFree$inboundSchema;
  /** @deprecated use `LegacyRecurringProductPriceFree$outboundSchema` instead. */
  export const outboundSchema = LegacyRecurringProductPriceFree$outboundSchema;
  /** @deprecated use `LegacyRecurringProductPriceFree$Outbound` instead. */
  export type Outbound = LegacyRecurringProductPriceFree$Outbound;
}

export function legacyRecurringProductPriceFreeToJSON(
  legacyRecurringProductPriceFree: LegacyRecurringProductPriceFree,
): string {
  return JSON.stringify(
    LegacyRecurringProductPriceFree$outboundSchema.parse(
      legacyRecurringProductPriceFree,
    ),
  );
}

export function legacyRecurringProductPriceFreeFromJSON(
  jsonString: string,
): SafeParseResult<LegacyRecurringProductPriceFree, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LegacyRecurringProductPriceFree$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LegacyRecurringProductPriceFree' from JSON`,
  );
}
