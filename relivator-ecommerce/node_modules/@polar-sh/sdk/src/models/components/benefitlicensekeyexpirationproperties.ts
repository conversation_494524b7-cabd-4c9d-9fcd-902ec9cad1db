/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const Timeframe = {
  Year: "year",
  Month: "month",
  Day: "day",
} as const;
export type Timeframe = ClosedEnum<typeof Timeframe>;

export type BenefitLicenseKeyExpirationProperties = {
  ttl: number;
  timeframe: Timeframe;
};

/** @internal */
export const Timeframe$inboundSchema: z.ZodNativeEnum<typeof Timeframe> = z
  .nativeEnum(Timeframe);

/** @internal */
export const Timeframe$outboundSchema: z.ZodNativeEnum<typeof Timeframe> =
  Timeframe$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Timeframe$ {
  /** @deprecated use `Timeframe$inboundSchema` instead. */
  export const inboundSchema = Timeframe$inboundSchema;
  /** @deprecated use `Timeframe$outboundSchema` instead. */
  export const outboundSchema = Timeframe$outboundSchema;
}

/** @internal */
export const BenefitLicenseKeyExpirationProperties$inboundSchema: z.ZodType<
  BenefitLicenseKeyExpirationProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  ttl: z.number().int(),
  timeframe: Timeframe$inboundSchema,
});

/** @internal */
export type BenefitLicenseKeyExpirationProperties$Outbound = {
  ttl: number;
  timeframe: string;
};

/** @internal */
export const BenefitLicenseKeyExpirationProperties$outboundSchema: z.ZodType<
  BenefitLicenseKeyExpirationProperties$Outbound,
  z.ZodTypeDef,
  BenefitLicenseKeyExpirationProperties
> = z.object({
  ttl: z.number().int(),
  timeframe: Timeframe$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitLicenseKeyExpirationProperties$ {
  /** @deprecated use `BenefitLicenseKeyExpirationProperties$inboundSchema` instead. */
  export const inboundSchema =
    BenefitLicenseKeyExpirationProperties$inboundSchema;
  /** @deprecated use `BenefitLicenseKeyExpirationProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitLicenseKeyExpirationProperties$outboundSchema;
  /** @deprecated use `BenefitLicenseKeyExpirationProperties$Outbound` instead. */
  export type Outbound = BenefitLicenseKeyExpirationProperties$Outbound;
}

export function benefitLicenseKeyExpirationPropertiesToJSON(
  benefitLicenseKeyExpirationProperties: BenefitLicenseKeyExpirationProperties,
): string {
  return JSON.stringify(
    BenefitLicenseKeyExpirationProperties$outboundSchema.parse(
      benefitLicenseKeyExpirationProperties,
    ),
  );
}

export function benefitLicenseKeyExpirationPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<BenefitLicenseKeyExpirationProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitLicenseKeyExpirationProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitLicenseKeyExpirationProperties' from JSON`,
  );
}
