/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type BenefitLicenseKeyActivationCreateProperties = {
  limit: number;
  enableCustomerAdmin: boolean;
};

/** @internal */
export const BenefitLicenseKeyActivationCreateProperties$inboundSchema:
  z.ZodType<
    BenefitLicenseKeyActivationCreateProperties,
    z.ZodTypeDef,
    unknown
  > = z.object({
    limit: z.number().int(),
    enable_customer_admin: z.boolean(),
  }).transform((v) => {
    return remap$(v, {
      "enable_customer_admin": "enableCustomerAdmin",
    });
  });

/** @internal */
export type BenefitLicenseKeyActivationCreateProperties$Outbound = {
  limit: number;
  enable_customer_admin: boolean;
};

/** @internal */
export const BenefitLicenseKeyActivationCreateProperties$outboundSchema:
  z.ZodType<
    BenefitLicenseKeyActivationCreateProperties$Outbound,
    z.ZodTypeDef,
    BenefitLicenseKeyActivationCreateProperties
  > = z.object({
    limit: z.number().int(),
    enableCustomerAdmin: z.boolean(),
  }).transform((v) => {
    return remap$(v, {
      enableCustomerAdmin: "enable_customer_admin",
    });
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitLicenseKeyActivationCreateProperties$ {
  /** @deprecated use `BenefitLicenseKeyActivationCreateProperties$inboundSchema` instead. */
  export const inboundSchema =
    BenefitLicenseKeyActivationCreateProperties$inboundSchema;
  /** @deprecated use `BenefitLicenseKeyActivationCreateProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitLicenseKeyActivationCreateProperties$outboundSchema;
  /** @deprecated use `BenefitLicenseKeyActivationCreateProperties$Outbound` instead. */
  export type Outbound = BenefitLicenseKeyActivationCreateProperties$Outbound;
}

export function benefitLicenseKeyActivationCreatePropertiesToJSON(
  benefitLicenseKeyActivationCreateProperties:
    BenefitLicenseKeyActivationCreateProperties,
): string {
  return JSON.stringify(
    BenefitLicenseKeyActivationCreateProperties$outboundSchema.parse(
      benefitLicenseKeyActivationCreateProperties,
    ),
  );
}

export function benefitLicenseKeyActivationCreatePropertiesFromJSON(
  jsonString: string,
): SafeParseResult<
  BenefitLicenseKeyActivationCreateProperties,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitLicenseKeyActivationCreateProperties$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'BenefitLicenseKeyActivationCreateProperties' from JSON`,
  );
}
