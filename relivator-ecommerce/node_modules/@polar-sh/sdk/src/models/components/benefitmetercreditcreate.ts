/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitMeterCreditCreateProperties,
  BenefitMeterCreditCreateProperties$inboundSchema,
  BenefitMeterCreditCreateProperties$Outbound,
  BenefitMeterCreditCreateProperties$outboundSchema,
} from "./benefitmetercreditcreateproperties.js";

export type BenefitMeterCreditCreateMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * Schema to create a benefit of type `meter_unit`.
 */
export type BenefitMeterCreditCreate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type?: "meter_credit" | undefined;
  /**
   * The description of the benefit. Will be displayed on products having this benefit.
   */
  description: string;
  /**
   * The ID of the organization owning the benefit. **Required unless you use an organization token.**
   */
  organizationId?: string | null | undefined;
  /**
   * Properties for creating a benefit of type `meter_unit`.
   */
  properties: BenefitMeterCreditCreateProperties;
};

/** @internal */
export const BenefitMeterCreditCreateMetadata$inboundSchema: z.ZodType<
  BenefitMeterCreditCreateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitMeterCreditCreateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitMeterCreditCreateMetadata$outboundSchema: z.ZodType<
  BenefitMeterCreditCreateMetadata$Outbound,
  z.ZodTypeDef,
  BenefitMeterCreditCreateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitMeterCreditCreateMetadata$ {
  /** @deprecated use `BenefitMeterCreditCreateMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitMeterCreditCreateMetadata$inboundSchema;
  /** @deprecated use `BenefitMeterCreditCreateMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitMeterCreditCreateMetadata$outboundSchema;
  /** @deprecated use `BenefitMeterCreditCreateMetadata$Outbound` instead. */
  export type Outbound = BenefitMeterCreditCreateMetadata$Outbound;
}

export function benefitMeterCreditCreateMetadataToJSON(
  benefitMeterCreditCreateMetadata: BenefitMeterCreditCreateMetadata,
): string {
  return JSON.stringify(
    BenefitMeterCreditCreateMetadata$outboundSchema.parse(
      benefitMeterCreditCreateMetadata,
    ),
  );
}

export function benefitMeterCreditCreateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitMeterCreditCreateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitMeterCreditCreateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitMeterCreditCreateMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitMeterCreditCreate$inboundSchema: z.ZodType<
  BenefitMeterCreditCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("meter_credit").optional(),
  description: z.string(),
  organization_id: z.nullable(z.string()).optional(),
  properties: BenefitMeterCreditCreateProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitMeterCreditCreate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type: "meter_credit";
  description: string;
  organization_id?: string | null | undefined;
  properties: BenefitMeterCreditCreateProperties$Outbound;
};

/** @internal */
export const BenefitMeterCreditCreate$outboundSchema: z.ZodType<
  BenefitMeterCreditCreate$Outbound,
  z.ZodTypeDef,
  BenefitMeterCreditCreate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("meter_credit").default("meter_credit" as const),
  description: z.string(),
  organizationId: z.nullable(z.string()).optional(),
  properties: BenefitMeterCreditCreateProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitMeterCreditCreate$ {
  /** @deprecated use `BenefitMeterCreditCreate$inboundSchema` instead. */
  export const inboundSchema = BenefitMeterCreditCreate$inboundSchema;
  /** @deprecated use `BenefitMeterCreditCreate$outboundSchema` instead. */
  export const outboundSchema = BenefitMeterCreditCreate$outboundSchema;
  /** @deprecated use `BenefitMeterCreditCreate$Outbound` instead. */
  export type Outbound = BenefitMeterCreditCreate$Outbound;
}

export function benefitMeterCreditCreateToJSON(
  benefitMeterCreditCreate: BenefitMeterCreditCreate,
): string {
  return JSON.stringify(
    BenefitMeterCreditCreate$outboundSchema.parse(benefitMeterCreditCreate),
  );
}

export function benefitMeterCreditCreateFromJSON(
  jsonString: string,
): SafeParseResult<BenefitMeterCreditCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitMeterCreditCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitMeterCreditCreate' from JSON`,
  );
}
