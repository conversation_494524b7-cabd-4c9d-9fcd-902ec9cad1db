/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountDuration,
  DiscountDuration$inboundSchema,
  DiscountDuration$outboundSchema,
} from "./discountduration.js";
import {
  DiscountProduct,
  DiscountProduct$inboundSchema,
  DiscountProduct$Outbound,
  DiscountProduct$outboundSchema,
} from "./discountproduct.js";
import {
  DiscountType,
  DiscountType$inboundSchema,
  DiscountType$outboundSchema,
} from "./discounttype.js";

export type DiscountPercentageOnceForeverDurationMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * Schema for a percentage discount that is applied once or forever.
 */
export type DiscountPercentageOnceForeverDuration = {
  duration: DiscountDuration;
  type: DiscountType;
  basisPoints: number;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Name of the discount. Will be displayed to the customer when the discount is applied.
   */
  name: string;
  /**
   * Code customers can use to apply the discount during checkout.
   */
  code: string | null;
  /**
   * Timestamp after which the discount is redeemable.
   */
  startsAt: Date | null;
  /**
   * Timestamp after which the discount is no longer redeemable.
   */
  endsAt: Date | null;
  /**
   * Maximum number of times the discount can be redeemed.
   */
  maxRedemptions: number | null;
  /**
   * Number of times the discount has been redeemed.
   */
  redemptionsCount: number;
  /**
   * The organization ID.
   */
  organizationId: string;
  products: Array<DiscountProduct>;
};

/** @internal */
export const DiscountPercentageOnceForeverDurationMetadata$inboundSchema:
  z.ZodType<
    DiscountPercentageOnceForeverDurationMetadata,
    z.ZodTypeDef,
    unknown
  > = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type DiscountPercentageOnceForeverDurationMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const DiscountPercentageOnceForeverDurationMetadata$outboundSchema:
  z.ZodType<
    DiscountPercentageOnceForeverDurationMetadata$Outbound,
    z.ZodTypeDef,
    DiscountPercentageOnceForeverDurationMetadata
  > = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountPercentageOnceForeverDurationMetadata$ {
  /** @deprecated use `DiscountPercentageOnceForeverDurationMetadata$inboundSchema` instead. */
  export const inboundSchema =
    DiscountPercentageOnceForeverDurationMetadata$inboundSchema;
  /** @deprecated use `DiscountPercentageOnceForeverDurationMetadata$outboundSchema` instead. */
  export const outboundSchema =
    DiscountPercentageOnceForeverDurationMetadata$outboundSchema;
  /** @deprecated use `DiscountPercentageOnceForeverDurationMetadata$Outbound` instead. */
  export type Outbound = DiscountPercentageOnceForeverDurationMetadata$Outbound;
}

export function discountPercentageOnceForeverDurationMetadataToJSON(
  discountPercentageOnceForeverDurationMetadata:
    DiscountPercentageOnceForeverDurationMetadata,
): string {
  return JSON.stringify(
    DiscountPercentageOnceForeverDurationMetadata$outboundSchema.parse(
      discountPercentageOnceForeverDurationMetadata,
    ),
  );
}

export function discountPercentageOnceForeverDurationMetadataFromJSON(
  jsonString: string,
): SafeParseResult<
  DiscountPercentageOnceForeverDurationMetadata,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      DiscountPercentageOnceForeverDurationMetadata$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'DiscountPercentageOnceForeverDurationMetadata' from JSON`,
  );
}

/** @internal */
export const DiscountPercentageOnceForeverDuration$inboundSchema: z.ZodType<
  DiscountPercentageOnceForeverDuration,
  z.ZodTypeDef,
  unknown
> = z.object({
  duration: DiscountDuration$inboundSchema,
  type: DiscountType$inboundSchema,
  basis_points: z.number().int(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  name: z.string(),
  code: z.nullable(z.string()),
  starts_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  ends_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  max_redemptions: z.nullable(z.number().int()),
  redemptions_count: z.number().int(),
  organization_id: z.string(),
  products: z.array(DiscountProduct$inboundSchema),
}).transform((v) => {
  return remap$(v, {
    "basis_points": "basisPoints",
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "starts_at": "startsAt",
    "ends_at": "endsAt",
    "max_redemptions": "maxRedemptions",
    "redemptions_count": "redemptionsCount",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type DiscountPercentageOnceForeverDuration$Outbound = {
  duration: string;
  type: string;
  basis_points: number;
  created_at: string;
  modified_at: string | null;
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  name: string;
  code: string | null;
  starts_at: string | null;
  ends_at: string | null;
  max_redemptions: number | null;
  redemptions_count: number;
  organization_id: string;
  products: Array<DiscountProduct$Outbound>;
};

/** @internal */
export const DiscountPercentageOnceForeverDuration$outboundSchema: z.ZodType<
  DiscountPercentageOnceForeverDuration$Outbound,
  z.ZodTypeDef,
  DiscountPercentageOnceForeverDuration
> = z.object({
  duration: DiscountDuration$outboundSchema,
  type: DiscountType$outboundSchema,
  basisPoints: z.number().int(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  name: z.string(),
  code: z.nullable(z.string()),
  startsAt: z.nullable(z.date().transform(v => v.toISOString())),
  endsAt: z.nullable(z.date().transform(v => v.toISOString())),
  maxRedemptions: z.nullable(z.number().int()),
  redemptionsCount: z.number().int(),
  organizationId: z.string(),
  products: z.array(DiscountProduct$outboundSchema),
}).transform((v) => {
  return remap$(v, {
    basisPoints: "basis_points",
    createdAt: "created_at",
    modifiedAt: "modified_at",
    startsAt: "starts_at",
    endsAt: "ends_at",
    maxRedemptions: "max_redemptions",
    redemptionsCount: "redemptions_count",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountPercentageOnceForeverDuration$ {
  /** @deprecated use `DiscountPercentageOnceForeverDuration$inboundSchema` instead. */
  export const inboundSchema =
    DiscountPercentageOnceForeverDuration$inboundSchema;
  /** @deprecated use `DiscountPercentageOnceForeverDuration$outboundSchema` instead. */
  export const outboundSchema =
    DiscountPercentageOnceForeverDuration$outboundSchema;
  /** @deprecated use `DiscountPercentageOnceForeverDuration$Outbound` instead. */
  export type Outbound = DiscountPercentageOnceForeverDuration$Outbound;
}

export function discountPercentageOnceForeverDurationToJSON(
  discountPercentageOnceForeverDuration: DiscountPercentageOnceForeverDuration,
): string {
  return JSON.stringify(
    DiscountPercentageOnceForeverDuration$outboundSchema.parse(
      discountPercentageOnceForeverDuration,
    ),
  );
}

export function discountPercentageOnceForeverDurationFromJSON(
  jsonString: string,
): SafeParseResult<DiscountPercentageOnceForeverDuration, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      DiscountPercentageOnceForeverDuration$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DiscountPercentageOnceForeverDuration' from JSON`,
  );
}
