/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitGitHubRepositoryProperties,
  BenefitGitHubRepositoryProperties$inboundSchema,
  BenefitGitHubRepositoryProperties$Outbound,
  BenefitGitHubRepositoryProperties$outboundSchema,
} from "./benefitgithubrepositoryproperties.js";

export type BenefitGitHubRepositoryMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * A benefit of type `github_repository`.
 *
 * @remarks
 *
 * Use it to automatically invite your backers to a private GitHub repository.
 */
export type BenefitGitHubRepository = {
  /**
   * The ID of the benefit.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  type?: "github_repository" | undefined;
  /**
   * The description of the benefit.
   */
  description: string;
  /**
   * Whether the benefit is selectable when creating a product.
   */
  selectable: boolean;
  /**
   * Whether the benefit is deletable.
   */
  deletable: boolean;
  /**
   * The ID of the organization owning the benefit.
   */
  organizationId: string;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Properties for a benefit of type `github_repository`.
   */
  properties: BenefitGitHubRepositoryProperties;
};

/** @internal */
export const BenefitGitHubRepositoryMetadata$inboundSchema: z.ZodType<
  BenefitGitHubRepositoryMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitGitHubRepositoryMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitGitHubRepositoryMetadata$outboundSchema: z.ZodType<
  BenefitGitHubRepositoryMetadata$Outbound,
  z.ZodTypeDef,
  BenefitGitHubRepositoryMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGitHubRepositoryMetadata$ {
  /** @deprecated use `BenefitGitHubRepositoryMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitGitHubRepositoryMetadata$inboundSchema;
  /** @deprecated use `BenefitGitHubRepositoryMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitGitHubRepositoryMetadata$outboundSchema;
  /** @deprecated use `BenefitGitHubRepositoryMetadata$Outbound` instead. */
  export type Outbound = BenefitGitHubRepositoryMetadata$Outbound;
}

export function benefitGitHubRepositoryMetadataToJSON(
  benefitGitHubRepositoryMetadata: BenefitGitHubRepositoryMetadata,
): string {
  return JSON.stringify(
    BenefitGitHubRepositoryMetadata$outboundSchema.parse(
      benefitGitHubRepositoryMetadata,
    ),
  );
}

export function benefitGitHubRepositoryMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGitHubRepositoryMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitGitHubRepositoryMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGitHubRepositoryMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitGitHubRepository$inboundSchema: z.ZodType<
  BenefitGitHubRepository,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  type: z.literal("github_repository").optional(),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organization_id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  properties: BenefitGitHubRepositoryProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitGitHubRepository$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  type: "github_repository";
  description: string;
  selectable: boolean;
  deletable: boolean;
  organization_id: string;
  metadata: { [k: string]: string | number | number | boolean };
  properties: BenefitGitHubRepositoryProperties$Outbound;
};

/** @internal */
export const BenefitGitHubRepository$outboundSchema: z.ZodType<
  BenefitGitHubRepository$Outbound,
  z.ZodTypeDef,
  BenefitGitHubRepository
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  type: z.literal("github_repository").default("github_repository" as const),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organizationId: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  properties: BenefitGitHubRepositoryProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGitHubRepository$ {
  /** @deprecated use `BenefitGitHubRepository$inboundSchema` instead. */
  export const inboundSchema = BenefitGitHubRepository$inboundSchema;
  /** @deprecated use `BenefitGitHubRepository$outboundSchema` instead. */
  export const outboundSchema = BenefitGitHubRepository$outboundSchema;
  /** @deprecated use `BenefitGitHubRepository$Outbound` instead. */
  export type Outbound = BenefitGitHubRepository$Outbound;
}

export function benefitGitHubRepositoryToJSON(
  benefitGitHubRepository: BenefitGitHubRepository,
): string {
  return JSON.stringify(
    BenefitGitHubRepository$outboundSchema.parse(benefitGitHubRepository),
  );
}

export function benefitGitHubRepositoryFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGitHubRepository, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitGitHubRepository$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGitHubRepository' from JSON`,
  );
}
