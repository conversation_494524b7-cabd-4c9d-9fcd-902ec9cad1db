/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Address,
  Address$inboundSchema,
  Address$Outbound,
  Address$outboundSchema,
} from "./address.js";
import {
  AttachedCustomField,
  AttachedCustomField$inboundSchema,
  AttachedCustomField$Outbound,
  AttachedCustomField$outboundSchema,
} from "./attachedcustomfield.js";
import {
  CheckoutCustomerBillingAddressFields,
  CheckoutCustomerBillingAddressFields$inboundSchema,
  CheckoutCustomerBillingAddressFields$Outbound,
  CheckoutCustomerBillingAddressFields$outboundSchema,
} from "./checkoutcustomerbillingaddressfields.js";
import {
  CheckoutDiscountFixedOnceForeverDuration,
  CheckoutDiscountFixedOnceForeverDuration$inboundSchema,
  CheckoutDiscountFixedOnceForeverDuration$Outbound,
  CheckoutDiscountFixedOnceForeverDuration$outboundSchema,
} from "./checkoutdiscountfixedonceforeverduration.js";
import {
  CheckoutDiscountFixedRepeatDuration,
  CheckoutDiscountFixedRepeatDuration$inboundSchema,
  CheckoutDiscountFixedRepeatDuration$Outbound,
  CheckoutDiscountFixedRepeatDuration$outboundSchema,
} from "./checkoutdiscountfixedrepeatduration.js";
import {
  CheckoutDiscountPercentageOnceForeverDuration,
  CheckoutDiscountPercentageOnceForeverDuration$inboundSchema,
  CheckoutDiscountPercentageOnceForeverDuration$Outbound,
  CheckoutDiscountPercentageOnceForeverDuration$outboundSchema,
} from "./checkoutdiscountpercentageonceforeverduration.js";
import {
  CheckoutDiscountPercentageRepeatDuration,
  CheckoutDiscountPercentageRepeatDuration$inboundSchema,
  CheckoutDiscountPercentageRepeatDuration$Outbound,
  CheckoutDiscountPercentageRepeatDuration$outboundSchema,
} from "./checkoutdiscountpercentagerepeatduration.js";
import {
  CheckoutProduct,
  CheckoutProduct$inboundSchema,
  CheckoutProduct$Outbound,
  CheckoutProduct$outboundSchema,
} from "./checkoutproduct.js";
import {
  CheckoutStatus,
  CheckoutStatus$inboundSchema,
  CheckoutStatus$outboundSchema,
} from "./checkoutstatus.js";
import {
  LegacyRecurringProductPrice,
  LegacyRecurringProductPrice$inboundSchema,
  LegacyRecurringProductPrice$Outbound,
  LegacyRecurringProductPrice$outboundSchema,
} from "./legacyrecurringproductprice.js";
import {
  PaymentProcessor,
  PaymentProcessor$inboundSchema,
  PaymentProcessor$outboundSchema,
} from "./paymentprocessor.js";
import {
  ProductPrice,
  ProductPrice$inboundSchema,
  ProductPrice$Outbound,
  ProductPrice$outboundSchema,
} from "./productprice.js";

export type CheckoutCustomFieldData = string | number | boolean | Date;

export type CheckoutMetadata = string | number | number | boolean;

/**
 * Price of the selected product.
 */
export type CheckoutProductPrice = LegacyRecurringProductPrice | ProductPrice;

export type CheckoutDiscount =
  | CheckoutDiscountPercentageOnceForeverDuration
  | CheckoutDiscountFixedOnceForeverDuration
  | CheckoutDiscountPercentageRepeatDuration
  | CheckoutDiscountFixedRepeatDuration;

export type CustomerMetadata = string | number | boolean;

/**
 * Checkout session data retrieved using an access token.
 */
export type Checkout = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * Key-value object storing custom field values.
   */
  customFieldData?:
    | { [k: string]: string | number | boolean | Date | null }
    | undefined;
  paymentProcessor: PaymentProcessor;
  status: CheckoutStatus;
  /**
   * Client secret used to update and complete the checkout session from the client.
   */
  clientSecret: string;
  /**
   * URL where the customer can access the checkout session.
   */
  url: string;
  /**
   * Expiration date and time of the checkout session.
   */
  expiresAt: Date;
  /**
   * URL where the customer will be redirected after a successful payment.
   */
  successUrl: string;
  /**
   * When checkout is embedded, represents the Origin of the page embedding the checkout. Used as a security measure to send messages only to the embedding page.
   */
  embedOrigin: string | null;
  /**
   * Amount in cents, before discounts and taxes.
   */
  amount: number;
  /**
   * Discount amount in cents.
   */
  discountAmount: number;
  /**
   * Amount in cents, after discounts but before taxes.
   */
  netAmount: number;
  /**
   * Sales tax amount in cents. If `null`, it means there is no enough information yet to calculate it.
   */
  taxAmount: number | null;
  /**
   * Amount in cents, after discounts and taxes.
   */
  totalAmount: number;
  /**
   * Currency code of the checkout session.
   */
  currency: string;
  /**
   * ID of the product to checkout.
   */
  productId: string;
  /**
   * ID of the product price to checkout.
   */
  productPriceId: string;
  /**
   * ID of the discount applied to the checkout.
   */
  discountId: string | null;
  /**
   * Whether to allow the customer to apply discount codes. If you apply a discount through `discount_id`, it'll still be applied, but the customer won't be able to change it.
   */
  allowDiscountCodes: boolean;
  /**
   * Whether to require the customer to fill their full billing address, instead of just the country. Customers in the US will always be required to fill their full address, regardless of this setting. If you preset the billing address, this setting will be automatically set to `true`.
   */
  requireBillingAddress: boolean;
  /**
   * Whether the discount is applicable to the checkout. Typically, free and custom prices are not discountable.
   */
  isDiscountApplicable: boolean;
  /**
   * Whether the product price is free, regardless of discounts.
   */
  isFreeProductPrice: boolean;
  /**
   * Whether the checkout requires payment, e.g. in case of free products or discounts that cover the total amount.
   */
  isPaymentRequired: boolean;
  /**
   * Whether the checkout requires setting up a payment method, regardless of the amount, e.g. subscriptions that have first free cycles.
   */
  isPaymentSetupRequired: boolean;
  /**
   * Whether the checkout requires a payment form, whether because of a payment or payment method setup.
   */
  isPaymentFormRequired: boolean;
  customerId: string | null;
  /**
   * Whether the customer is a business or an individual. If `true`, the customer will be required to fill their full billing address and billing name.
   */
  isBusinessCustomer: boolean;
  /**
   * Name of the customer.
   */
  customerName: string | null;
  /**
   * Email address of the customer.
   */
  customerEmail: string | null;
  customerIpAddress: string | null;
  customerBillingName: string | null;
  customerBillingAddress: Address | null;
  customerTaxId: string | null;
  paymentProcessorMetadata: { [k: string]: string };
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * ID of the customer in your system. If a matching customer exists on Polar, the resulting order will be linked to this customer. Otherwise, a new customer will be created with this external ID set.
   */
  customerExternalId: string | null;
  /**
   * List of products available to select.
   */
  products: Array<CheckoutProduct>;
  /**
   * Product data for a checkout session.
   */
  product: CheckoutProduct;
  /**
   * Price of the selected product.
   */
  productPrice: LegacyRecurringProductPrice | ProductPrice;
  discount:
    | CheckoutDiscountPercentageOnceForeverDuration
    | CheckoutDiscountFixedOnceForeverDuration
    | CheckoutDiscountPercentageRepeatDuration
    | CheckoutDiscountFixedRepeatDuration
    | null;
  subscriptionId: string | null;
  attachedCustomFields: Array<AttachedCustomField>;
  customerMetadata: { [k: string]: string | number | boolean };
  customerBillingAddressFields: CheckoutCustomerBillingAddressFields;
};

/** @internal */
export const CheckoutCustomFieldData$inboundSchema: z.ZodType<
  CheckoutCustomFieldData,
  z.ZodTypeDef,
  unknown
> = z.union([
  z.string(),
  z.number().int(),
  z.boolean(),
  z.string().datetime({ offset: true }).transform(v => new Date(v)),
]);

/** @internal */
export type CheckoutCustomFieldData$Outbound =
  | string
  | number
  | boolean
  | string;

/** @internal */
export const CheckoutCustomFieldData$outboundSchema: z.ZodType<
  CheckoutCustomFieldData$Outbound,
  z.ZodTypeDef,
  CheckoutCustomFieldData
> = z.union([
  z.string(),
  z.number().int(),
  z.boolean(),
  z.date().transform(v => v.toISOString()),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutCustomFieldData$ {
  /** @deprecated use `CheckoutCustomFieldData$inboundSchema` instead. */
  export const inboundSchema = CheckoutCustomFieldData$inboundSchema;
  /** @deprecated use `CheckoutCustomFieldData$outboundSchema` instead. */
  export const outboundSchema = CheckoutCustomFieldData$outboundSchema;
  /** @deprecated use `CheckoutCustomFieldData$Outbound` instead. */
  export type Outbound = CheckoutCustomFieldData$Outbound;
}

export function checkoutCustomFieldDataToJSON(
  checkoutCustomFieldData: CheckoutCustomFieldData,
): string {
  return JSON.stringify(
    CheckoutCustomFieldData$outboundSchema.parse(checkoutCustomFieldData),
  );
}

export function checkoutCustomFieldDataFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutCustomFieldData, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutCustomFieldData$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutCustomFieldData' from JSON`,
  );
}

/** @internal */
export const CheckoutMetadata$inboundSchema: z.ZodType<
  CheckoutMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CheckoutMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const CheckoutMetadata$outboundSchema: z.ZodType<
  CheckoutMetadata$Outbound,
  z.ZodTypeDef,
  CheckoutMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutMetadata$ {
  /** @deprecated use `CheckoutMetadata$inboundSchema` instead. */
  export const inboundSchema = CheckoutMetadata$inboundSchema;
  /** @deprecated use `CheckoutMetadata$outboundSchema` instead. */
  export const outboundSchema = CheckoutMetadata$outboundSchema;
  /** @deprecated use `CheckoutMetadata$Outbound` instead. */
  export type Outbound = CheckoutMetadata$Outbound;
}

export function checkoutMetadataToJSON(
  checkoutMetadata: CheckoutMetadata,
): string {
  return JSON.stringify(
    CheckoutMetadata$outboundSchema.parse(checkoutMetadata),
  );
}

export function checkoutMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutMetadata' from JSON`,
  );
}

/** @internal */
export const CheckoutProductPrice$inboundSchema: z.ZodType<
  CheckoutProductPrice,
  z.ZodTypeDef,
  unknown
> = z.union([
  LegacyRecurringProductPrice$inboundSchema,
  ProductPrice$inboundSchema,
]);

/** @internal */
export type CheckoutProductPrice$Outbound =
  | LegacyRecurringProductPrice$Outbound
  | ProductPrice$Outbound;

/** @internal */
export const CheckoutProductPrice$outboundSchema: z.ZodType<
  CheckoutProductPrice$Outbound,
  z.ZodTypeDef,
  CheckoutProductPrice
> = z.union([
  LegacyRecurringProductPrice$outboundSchema,
  ProductPrice$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutProductPrice$ {
  /** @deprecated use `CheckoutProductPrice$inboundSchema` instead. */
  export const inboundSchema = CheckoutProductPrice$inboundSchema;
  /** @deprecated use `CheckoutProductPrice$outboundSchema` instead. */
  export const outboundSchema = CheckoutProductPrice$outboundSchema;
  /** @deprecated use `CheckoutProductPrice$Outbound` instead. */
  export type Outbound = CheckoutProductPrice$Outbound;
}

export function checkoutProductPriceToJSON(
  checkoutProductPrice: CheckoutProductPrice,
): string {
  return JSON.stringify(
    CheckoutProductPrice$outboundSchema.parse(checkoutProductPrice),
  );
}

export function checkoutProductPriceFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutProductPrice, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutProductPrice$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutProductPrice' from JSON`,
  );
}

/** @internal */
export const CheckoutDiscount$inboundSchema: z.ZodType<
  CheckoutDiscount,
  z.ZodTypeDef,
  unknown
> = z.union([
  CheckoutDiscountPercentageOnceForeverDuration$inboundSchema,
  CheckoutDiscountFixedOnceForeverDuration$inboundSchema,
  CheckoutDiscountPercentageRepeatDuration$inboundSchema,
  CheckoutDiscountFixedRepeatDuration$inboundSchema,
]);

/** @internal */
export type CheckoutDiscount$Outbound =
  | CheckoutDiscountPercentageOnceForeverDuration$Outbound
  | CheckoutDiscountFixedOnceForeverDuration$Outbound
  | CheckoutDiscountPercentageRepeatDuration$Outbound
  | CheckoutDiscountFixedRepeatDuration$Outbound;

/** @internal */
export const CheckoutDiscount$outboundSchema: z.ZodType<
  CheckoutDiscount$Outbound,
  z.ZodTypeDef,
  CheckoutDiscount
> = z.union([
  CheckoutDiscountPercentageOnceForeverDuration$outboundSchema,
  CheckoutDiscountFixedOnceForeverDuration$outboundSchema,
  CheckoutDiscountPercentageRepeatDuration$outboundSchema,
  CheckoutDiscountFixedRepeatDuration$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutDiscount$ {
  /** @deprecated use `CheckoutDiscount$inboundSchema` instead. */
  export const inboundSchema = CheckoutDiscount$inboundSchema;
  /** @deprecated use `CheckoutDiscount$outboundSchema` instead. */
  export const outboundSchema = CheckoutDiscount$outboundSchema;
  /** @deprecated use `CheckoutDiscount$Outbound` instead. */
  export type Outbound = CheckoutDiscount$Outbound;
}

export function checkoutDiscountToJSON(
  checkoutDiscount: CheckoutDiscount,
): string {
  return JSON.stringify(
    CheckoutDiscount$outboundSchema.parse(checkoutDiscount),
  );
}

export function checkoutDiscountFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutDiscount, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutDiscount$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutDiscount' from JSON`,
  );
}

/** @internal */
export const CustomerMetadata$inboundSchema: z.ZodType<
  CustomerMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.boolean()]);

/** @internal */
export type CustomerMetadata$Outbound = string | number | boolean;

/** @internal */
export const CustomerMetadata$outboundSchema: z.ZodType<
  CustomerMetadata$Outbound,
  z.ZodTypeDef,
  CustomerMetadata
> = z.union([z.string(), z.number().int(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerMetadata$ {
  /** @deprecated use `CustomerMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomerMetadata$inboundSchema;
  /** @deprecated use `CustomerMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomerMetadata$outboundSchema;
  /** @deprecated use `CustomerMetadata$Outbound` instead. */
  export type Outbound = CustomerMetadata$Outbound;
}

export function customerMetadataToJSON(
  customerMetadata: CustomerMetadata,
): string {
  return JSON.stringify(
    CustomerMetadata$outboundSchema.parse(customerMetadata),
  );
}

export function customerMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomerMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerMetadata' from JSON`,
  );
}

/** @internal */
export const Checkout$inboundSchema: z.ZodType<
  Checkout,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  custom_field_data: z.record(
    z.nullable(
      z.union([
        z.string(),
        z.number().int(),
        z.boolean(),
        z.string().datetime({ offset: true }).transform(v => new Date(v)),
      ]),
    ),
  ).optional(),
  payment_processor: PaymentProcessor$inboundSchema,
  status: CheckoutStatus$inboundSchema,
  client_secret: z.string(),
  url: z.string(),
  expires_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  success_url: z.string(),
  embed_origin: z.nullable(z.string()),
  amount: z.number().int(),
  discount_amount: z.number().int(),
  net_amount: z.number().int(),
  tax_amount: z.nullable(z.number().int()),
  total_amount: z.number().int(),
  currency: z.string(),
  product_id: z.string(),
  product_price_id: z.string(),
  discount_id: z.nullable(z.string()),
  allow_discount_codes: z.boolean(),
  require_billing_address: z.boolean(),
  is_discount_applicable: z.boolean(),
  is_free_product_price: z.boolean(),
  is_payment_required: z.boolean(),
  is_payment_setup_required: z.boolean(),
  is_payment_form_required: z.boolean(),
  customer_id: z.nullable(z.string()),
  is_business_customer: z.boolean(),
  customer_name: z.nullable(z.string()),
  customer_email: z.nullable(z.string()),
  customer_ip_address: z.nullable(z.string()),
  customer_billing_name: z.nullable(z.string()),
  customer_billing_address: z.nullable(Address$inboundSchema),
  customer_tax_id: z.nullable(z.string()),
  payment_processor_metadata: z.record(z.string()),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  customer_external_id: z.nullable(z.string()),
  products: z.array(CheckoutProduct$inboundSchema),
  product: CheckoutProduct$inboundSchema,
  product_price: z.union([
    LegacyRecurringProductPrice$inboundSchema,
    ProductPrice$inboundSchema,
  ]),
  discount: z.nullable(
    z.union([
      CheckoutDiscountPercentageOnceForeverDuration$inboundSchema,
      CheckoutDiscountFixedOnceForeverDuration$inboundSchema,
      CheckoutDiscountPercentageRepeatDuration$inboundSchema,
      CheckoutDiscountFixedRepeatDuration$inboundSchema,
    ]),
  ),
  subscription_id: z.nullable(z.string()),
  attached_custom_fields: z.array(AttachedCustomField$inboundSchema),
  customer_metadata: z.record(
    z.union([z.string(), z.number().int(), z.boolean()]),
  ),
  customer_billing_address_fields:
    CheckoutCustomerBillingAddressFields$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "custom_field_data": "customFieldData",
    "payment_processor": "paymentProcessor",
    "client_secret": "clientSecret",
    "expires_at": "expiresAt",
    "success_url": "successUrl",
    "embed_origin": "embedOrigin",
    "discount_amount": "discountAmount",
    "net_amount": "netAmount",
    "tax_amount": "taxAmount",
    "total_amount": "totalAmount",
    "product_id": "productId",
    "product_price_id": "productPriceId",
    "discount_id": "discountId",
    "allow_discount_codes": "allowDiscountCodes",
    "require_billing_address": "requireBillingAddress",
    "is_discount_applicable": "isDiscountApplicable",
    "is_free_product_price": "isFreeProductPrice",
    "is_payment_required": "isPaymentRequired",
    "is_payment_setup_required": "isPaymentSetupRequired",
    "is_payment_form_required": "isPaymentFormRequired",
    "customer_id": "customerId",
    "is_business_customer": "isBusinessCustomer",
    "customer_name": "customerName",
    "customer_email": "customerEmail",
    "customer_ip_address": "customerIpAddress",
    "customer_billing_name": "customerBillingName",
    "customer_billing_address": "customerBillingAddress",
    "customer_tax_id": "customerTaxId",
    "payment_processor_metadata": "paymentProcessorMetadata",
    "customer_external_id": "customerExternalId",
    "product_price": "productPrice",
    "subscription_id": "subscriptionId",
    "attached_custom_fields": "attachedCustomFields",
    "customer_metadata": "customerMetadata",
    "customer_billing_address_fields": "customerBillingAddressFields",
  });
});

/** @internal */
export type Checkout$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  custom_field_data?:
    | { [k: string]: string | number | boolean | string | null }
    | undefined;
  payment_processor: string;
  status: string;
  client_secret: string;
  url: string;
  expires_at: string;
  success_url: string;
  embed_origin: string | null;
  amount: number;
  discount_amount: number;
  net_amount: number;
  tax_amount: number | null;
  total_amount: number;
  currency: string;
  product_id: string;
  product_price_id: string;
  discount_id: string | null;
  allow_discount_codes: boolean;
  require_billing_address: boolean;
  is_discount_applicable: boolean;
  is_free_product_price: boolean;
  is_payment_required: boolean;
  is_payment_setup_required: boolean;
  is_payment_form_required: boolean;
  customer_id: string | null;
  is_business_customer: boolean;
  customer_name: string | null;
  customer_email: string | null;
  customer_ip_address: string | null;
  customer_billing_name: string | null;
  customer_billing_address: Address$Outbound | null;
  customer_tax_id: string | null;
  payment_processor_metadata: { [k: string]: string };
  metadata: { [k: string]: string | number | number | boolean };
  customer_external_id: string | null;
  products: Array<CheckoutProduct$Outbound>;
  product: CheckoutProduct$Outbound;
  product_price: LegacyRecurringProductPrice$Outbound | ProductPrice$Outbound;
  discount:
    | CheckoutDiscountPercentageOnceForeverDuration$Outbound
    | CheckoutDiscountFixedOnceForeverDuration$Outbound
    | CheckoutDiscountPercentageRepeatDuration$Outbound
    | CheckoutDiscountFixedRepeatDuration$Outbound
    | null;
  subscription_id: string | null;
  attached_custom_fields: Array<AttachedCustomField$Outbound>;
  customer_metadata: { [k: string]: string | number | boolean };
  customer_billing_address_fields:
    CheckoutCustomerBillingAddressFields$Outbound;
};

/** @internal */
export const Checkout$outboundSchema: z.ZodType<
  Checkout$Outbound,
  z.ZodTypeDef,
  Checkout
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  customFieldData: z.record(
    z.nullable(
      z.union([
        z.string(),
        z.number().int(),
        z.boolean(),
        z.date().transform(v => v.toISOString()),
      ]),
    ),
  ).optional(),
  paymentProcessor: PaymentProcessor$outboundSchema,
  status: CheckoutStatus$outboundSchema,
  clientSecret: z.string(),
  url: z.string(),
  expiresAt: z.date().transform(v => v.toISOString()),
  successUrl: z.string(),
  embedOrigin: z.nullable(z.string()),
  amount: z.number().int(),
  discountAmount: z.number().int(),
  netAmount: z.number().int(),
  taxAmount: z.nullable(z.number().int()),
  totalAmount: z.number().int(),
  currency: z.string(),
  productId: z.string(),
  productPriceId: z.string(),
  discountId: z.nullable(z.string()),
  allowDiscountCodes: z.boolean(),
  requireBillingAddress: z.boolean(),
  isDiscountApplicable: z.boolean(),
  isFreeProductPrice: z.boolean(),
  isPaymentRequired: z.boolean(),
  isPaymentSetupRequired: z.boolean(),
  isPaymentFormRequired: z.boolean(),
  customerId: z.nullable(z.string()),
  isBusinessCustomer: z.boolean(),
  customerName: z.nullable(z.string()),
  customerEmail: z.nullable(z.string()),
  customerIpAddress: z.nullable(z.string()),
  customerBillingName: z.nullable(z.string()),
  customerBillingAddress: z.nullable(Address$outboundSchema),
  customerTaxId: z.nullable(z.string()),
  paymentProcessorMetadata: z.record(z.string()),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  customerExternalId: z.nullable(z.string()),
  products: z.array(CheckoutProduct$outboundSchema),
  product: CheckoutProduct$outboundSchema,
  productPrice: z.union([
    LegacyRecurringProductPrice$outboundSchema,
    ProductPrice$outboundSchema,
  ]),
  discount: z.nullable(
    z.union([
      CheckoutDiscountPercentageOnceForeverDuration$outboundSchema,
      CheckoutDiscountFixedOnceForeverDuration$outboundSchema,
      CheckoutDiscountPercentageRepeatDuration$outboundSchema,
      CheckoutDiscountFixedRepeatDuration$outboundSchema,
    ]),
  ),
  subscriptionId: z.nullable(z.string()),
  attachedCustomFields: z.array(AttachedCustomField$outboundSchema),
  customerMetadata: z.record(
    z.union([z.string(), z.number().int(), z.boolean()]),
  ),
  customerBillingAddressFields:
    CheckoutCustomerBillingAddressFields$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    customFieldData: "custom_field_data",
    paymentProcessor: "payment_processor",
    clientSecret: "client_secret",
    expiresAt: "expires_at",
    successUrl: "success_url",
    embedOrigin: "embed_origin",
    discountAmount: "discount_amount",
    netAmount: "net_amount",
    taxAmount: "tax_amount",
    totalAmount: "total_amount",
    productId: "product_id",
    productPriceId: "product_price_id",
    discountId: "discount_id",
    allowDiscountCodes: "allow_discount_codes",
    requireBillingAddress: "require_billing_address",
    isDiscountApplicable: "is_discount_applicable",
    isFreeProductPrice: "is_free_product_price",
    isPaymentRequired: "is_payment_required",
    isPaymentSetupRequired: "is_payment_setup_required",
    isPaymentFormRequired: "is_payment_form_required",
    customerId: "customer_id",
    isBusinessCustomer: "is_business_customer",
    customerName: "customer_name",
    customerEmail: "customer_email",
    customerIpAddress: "customer_ip_address",
    customerBillingName: "customer_billing_name",
    customerBillingAddress: "customer_billing_address",
    customerTaxId: "customer_tax_id",
    paymentProcessorMetadata: "payment_processor_metadata",
    customerExternalId: "customer_external_id",
    productPrice: "product_price",
    subscriptionId: "subscription_id",
    attachedCustomFields: "attached_custom_fields",
    customerMetadata: "customer_metadata",
    customerBillingAddressFields: "customer_billing_address_fields",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Checkout$ {
  /** @deprecated use `Checkout$inboundSchema` instead. */
  export const inboundSchema = Checkout$inboundSchema;
  /** @deprecated use `Checkout$outboundSchema` instead. */
  export const outboundSchema = Checkout$outboundSchema;
  /** @deprecated use `Checkout$Outbound` instead. */
  export type Outbound = Checkout$Outbound;
}

export function checkoutToJSON(checkout: Checkout): string {
  return JSON.stringify(Checkout$outboundSchema.parse(checkout));
}

export function checkoutFromJSON(
  jsonString: string,
): SafeParseResult<Checkout, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Checkout$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Checkout' from JSON`,
  );
}
