/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const PaymentSortProperty = {
  CreatedAt: "created_at",
  MinusCreatedAt: "-created_at",
  Status: "status",
  MinusStatus: "-status",
  Amount: "amount",
  MinusAmount: "-amount",
  Method: "method",
  MinusMethod: "-method",
} as const;
export type PaymentSortProperty = ClosedEnum<typeof PaymentSortProperty>;

/** @internal */
export const PaymentSortProperty$inboundSchema: z.ZodNativeEnum<
  typeof PaymentSortProperty
> = z.nativeEnum(PaymentSortProperty);

/** @internal */
export const PaymentSortProperty$outboundSchema: z.ZodNativeEnum<
  typeof PaymentSortProperty
> = PaymentSortProperty$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace PaymentSortProperty$ {
  /** @deprecated use `PaymentSortProperty$inboundSchema` instead. */
  export const inboundSchema = PaymentSortProperty$inboundSchema;
  /** @deprecated use `PaymentSortProperty$outboundSchema` instead. */
  export const outboundSchema = PaymentSortProperty$outboundSchema;
}
