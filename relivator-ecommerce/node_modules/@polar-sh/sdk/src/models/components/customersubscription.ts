/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomerCancellationReason,
  CustomerCancellationReason$inboundSchema,
  CustomerCancellationReason$outboundSchema,
} from "./customercancellationreason.js";
import {
  CustomerSubscriptionMeter,
  CustomerSubscriptionMeter$inboundSchema,
  CustomerSubscriptionMeter$Outbound,
  CustomerSubscriptionMeter$outboundSchema,
} from "./customersubscriptionmeter.js";
import {
  CustomerSubscriptionProduct,
  CustomerSubscriptionProduct$inboundSchema,
  CustomerSubscriptionProduct$Outbound,
  CustomerSubscriptionProduct$outboundSchema,
} from "./customersubscriptionproduct.js";
import {
  LegacyRecurringProductPrice,
  LegacyRecurringProductPrice$inboundSchema,
  LegacyRecurringProductPrice$Outbound,
  LegacyRecurringProductPrice$outboundSchema,
} from "./legacyrecurringproductprice.js";
import {
  ProductPrice,
  ProductPrice$inboundSchema,
  ProductPrice$Outbound,
  ProductPrice$outboundSchema,
} from "./productprice.js";
import {
  SubscriptionRecurringInterval,
  SubscriptionRecurringInterval$inboundSchema,
  SubscriptionRecurringInterval$outboundSchema,
} from "./subscriptionrecurringinterval.js";
import {
  SubscriptionStatus,
  SubscriptionStatus$inboundSchema,
  SubscriptionStatus$outboundSchema,
} from "./subscriptionstatus.js";

export type CustomerSubscriptionPrices =
  | LegacyRecurringProductPrice
  | ProductPrice;

export type CustomerSubscription = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * The amount of the subscription.
   */
  amount: number;
  /**
   * The currency of the subscription.
   */
  currency: string;
  recurringInterval: SubscriptionRecurringInterval;
  status: SubscriptionStatus;
  /**
   * The start timestamp of the current billing period.
   */
  currentPeriodStart: Date;
  /**
   * The end timestamp of the current billing period.
   */
  currentPeriodEnd: Date | null;
  /**
   * Whether the subscription will be canceled at the end of the current period.
   */
  cancelAtPeriodEnd: boolean;
  /**
   * The timestamp when the subscription was canceled. The subscription might still be active if `cancel_at_period_end` is `true`.
   */
  canceledAt: Date | null;
  /**
   * The timestamp when the subscription started.
   */
  startedAt: Date | null;
  /**
   * The timestamp when the subscription will end.
   */
  endsAt: Date | null;
  /**
   * The timestamp when the subscription ended.
   */
  endedAt: Date | null;
  /**
   * The ID of the subscribed customer.
   */
  customerId: string;
  /**
   * The ID of the subscribed product.
   */
  productId: string;
  /**
   * The ID of the applied discount, if any.
   */
  discountId: string | null;
  checkoutId: string | null;
  customerCancellationReason: CustomerCancellationReason | null;
  customerCancellationComment: string | null;
  product: CustomerSubscriptionProduct;
  /**
   * List of enabled prices for the subscription.
   */
  prices: Array<LegacyRecurringProductPrice | ProductPrice>;
  /**
   * List of meters associated with the subscription.
   */
  meters: Array<CustomerSubscriptionMeter>;
};

/** @internal */
export const CustomerSubscriptionPrices$inboundSchema: z.ZodType<
  CustomerSubscriptionPrices,
  z.ZodTypeDef,
  unknown
> = z.union([
  LegacyRecurringProductPrice$inboundSchema,
  ProductPrice$inboundSchema,
]);

/** @internal */
export type CustomerSubscriptionPrices$Outbound =
  | LegacyRecurringProductPrice$Outbound
  | ProductPrice$Outbound;

/** @internal */
export const CustomerSubscriptionPrices$outboundSchema: z.ZodType<
  CustomerSubscriptionPrices$Outbound,
  z.ZodTypeDef,
  CustomerSubscriptionPrices
> = z.union([
  LegacyRecurringProductPrice$outboundSchema,
  ProductPrice$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerSubscriptionPrices$ {
  /** @deprecated use `CustomerSubscriptionPrices$inboundSchema` instead. */
  export const inboundSchema = CustomerSubscriptionPrices$inboundSchema;
  /** @deprecated use `CustomerSubscriptionPrices$outboundSchema` instead. */
  export const outboundSchema = CustomerSubscriptionPrices$outboundSchema;
  /** @deprecated use `CustomerSubscriptionPrices$Outbound` instead. */
  export type Outbound = CustomerSubscriptionPrices$Outbound;
}

export function customerSubscriptionPricesToJSON(
  customerSubscriptionPrices: CustomerSubscriptionPrices,
): string {
  return JSON.stringify(
    CustomerSubscriptionPrices$outboundSchema.parse(customerSubscriptionPrices),
  );
}

export function customerSubscriptionPricesFromJSON(
  jsonString: string,
): SafeParseResult<CustomerSubscriptionPrices, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerSubscriptionPrices$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerSubscriptionPrices' from JSON`,
  );
}

/** @internal */
export const CustomerSubscription$inboundSchema: z.ZodType<
  CustomerSubscription,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  amount: z.number().int(),
  currency: z.string(),
  recurring_interval: SubscriptionRecurringInterval$inboundSchema,
  status: SubscriptionStatus$inboundSchema,
  current_period_start: z.string().datetime({ offset: true }).transform(v =>
    new Date(v)
  ),
  current_period_end: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  cancel_at_period_end: z.boolean(),
  canceled_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  started_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  ends_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  ended_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  customer_id: z.string(),
  product_id: z.string(),
  discount_id: z.nullable(z.string()),
  checkout_id: z.nullable(z.string()),
  customer_cancellation_reason: z.nullable(
    CustomerCancellationReason$inboundSchema,
  ),
  customer_cancellation_comment: z.nullable(z.string()),
  product: CustomerSubscriptionProduct$inboundSchema,
  prices: z.array(
    z.union([
      LegacyRecurringProductPrice$inboundSchema,
      ProductPrice$inboundSchema,
    ]),
  ),
  meters: z.array(CustomerSubscriptionMeter$inboundSchema),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "recurring_interval": "recurringInterval",
    "current_period_start": "currentPeriodStart",
    "current_period_end": "currentPeriodEnd",
    "cancel_at_period_end": "cancelAtPeriodEnd",
    "canceled_at": "canceledAt",
    "started_at": "startedAt",
    "ends_at": "endsAt",
    "ended_at": "endedAt",
    "customer_id": "customerId",
    "product_id": "productId",
    "discount_id": "discountId",
    "checkout_id": "checkoutId",
    "customer_cancellation_reason": "customerCancellationReason",
    "customer_cancellation_comment": "customerCancellationComment",
  });
});

/** @internal */
export type CustomerSubscription$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  amount: number;
  currency: string;
  recurring_interval: string;
  status: string;
  current_period_start: string;
  current_period_end: string | null;
  cancel_at_period_end: boolean;
  canceled_at: string | null;
  started_at: string | null;
  ends_at: string | null;
  ended_at: string | null;
  customer_id: string;
  product_id: string;
  discount_id: string | null;
  checkout_id: string | null;
  customer_cancellation_reason: string | null;
  customer_cancellation_comment: string | null;
  product: CustomerSubscriptionProduct$Outbound;
  prices: Array<LegacyRecurringProductPrice$Outbound | ProductPrice$Outbound>;
  meters: Array<CustomerSubscriptionMeter$Outbound>;
};

/** @internal */
export const CustomerSubscription$outboundSchema: z.ZodType<
  CustomerSubscription$Outbound,
  z.ZodTypeDef,
  CustomerSubscription
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  amount: z.number().int(),
  currency: z.string(),
  recurringInterval: SubscriptionRecurringInterval$outboundSchema,
  status: SubscriptionStatus$outboundSchema,
  currentPeriodStart: z.date().transform(v => v.toISOString()),
  currentPeriodEnd: z.nullable(z.date().transform(v => v.toISOString())),
  cancelAtPeriodEnd: z.boolean(),
  canceledAt: z.nullable(z.date().transform(v => v.toISOString())),
  startedAt: z.nullable(z.date().transform(v => v.toISOString())),
  endsAt: z.nullable(z.date().transform(v => v.toISOString())),
  endedAt: z.nullable(z.date().transform(v => v.toISOString())),
  customerId: z.string(),
  productId: z.string(),
  discountId: z.nullable(z.string()),
  checkoutId: z.nullable(z.string()),
  customerCancellationReason: z.nullable(
    CustomerCancellationReason$outboundSchema,
  ),
  customerCancellationComment: z.nullable(z.string()),
  product: CustomerSubscriptionProduct$outboundSchema,
  prices: z.array(
    z.union([
      LegacyRecurringProductPrice$outboundSchema,
      ProductPrice$outboundSchema,
    ]),
  ),
  meters: z.array(CustomerSubscriptionMeter$outboundSchema),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    recurringInterval: "recurring_interval",
    currentPeriodStart: "current_period_start",
    currentPeriodEnd: "current_period_end",
    cancelAtPeriodEnd: "cancel_at_period_end",
    canceledAt: "canceled_at",
    startedAt: "started_at",
    endsAt: "ends_at",
    endedAt: "ended_at",
    customerId: "customer_id",
    productId: "product_id",
    discountId: "discount_id",
    checkoutId: "checkout_id",
    customerCancellationReason: "customer_cancellation_reason",
    customerCancellationComment: "customer_cancellation_comment",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerSubscription$ {
  /** @deprecated use `CustomerSubscription$inboundSchema` instead. */
  export const inboundSchema = CustomerSubscription$inboundSchema;
  /** @deprecated use `CustomerSubscription$outboundSchema` instead. */
  export const outboundSchema = CustomerSubscription$outboundSchema;
  /** @deprecated use `CustomerSubscription$Outbound` instead. */
  export type Outbound = CustomerSubscription$Outbound;
}

export function customerSubscriptionToJSON(
  customerSubscription: CustomerSubscription,
): string {
  return JSON.stringify(
    CustomerSubscription$outboundSchema.parse(customerSubscription),
  );
}

export function customerSubscriptionFromJSON(
  jsonString: string,
): SafeParseResult<CustomerSubscription, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerSubscription$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerSubscription' from JSON`,
  );
}
