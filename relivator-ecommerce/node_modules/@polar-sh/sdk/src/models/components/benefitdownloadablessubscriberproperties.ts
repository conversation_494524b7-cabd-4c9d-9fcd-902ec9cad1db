/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type BenefitDownloadablesSubscriberProperties = {
  activeFiles: Array<string>;
};

/** @internal */
export const BenefitDownloadablesSubscriberProperties$inboundSchema: z.ZodType<
  BenefitDownloadablesSubscriberProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  active_files: z.array(z.string()),
}).transform((v) => {
  return remap$(v, {
    "active_files": "activeFiles",
  });
});

/** @internal */
export type BenefitDownloadablesSubscriberProperties$Outbound = {
  active_files: Array<string>;
};

/** @internal */
export const BenefitDownloadablesSubscriberProperties$outboundSchema: z.ZodType<
  BenefitDownloadablesSubscriberProperties$Outbound,
  z.ZodTypeDef,
  BenefitDownloadablesSubscriberProperties
> = z.object({
  activeFiles: z.array(z.string()),
}).transform((v) => {
  return remap$(v, {
    activeFiles: "active_files",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDownloadablesSubscriberProperties$ {
  /** @deprecated use `BenefitDownloadablesSubscriberProperties$inboundSchema` instead. */
  export const inboundSchema =
    BenefitDownloadablesSubscriberProperties$inboundSchema;
  /** @deprecated use `BenefitDownloadablesSubscriberProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitDownloadablesSubscriberProperties$outboundSchema;
  /** @deprecated use `BenefitDownloadablesSubscriberProperties$Outbound` instead. */
  export type Outbound = BenefitDownloadablesSubscriberProperties$Outbound;
}

export function benefitDownloadablesSubscriberPropertiesToJSON(
  benefitDownloadablesSubscriberProperties:
    BenefitDownloadablesSubscriberProperties,
): string {
  return JSON.stringify(
    BenefitDownloadablesSubscriberProperties$outboundSchema.parse(
      benefitDownloadablesSubscriberProperties,
    ),
  );
}

export function benefitDownloadablesSubscriberPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<
  BenefitDownloadablesSubscriberProperties,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitDownloadablesSubscriberProperties$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'BenefitDownloadablesSubscriberProperties' from JSON`,
  );
}
