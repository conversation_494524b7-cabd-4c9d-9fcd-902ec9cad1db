/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  MetricsIntervalLimit,
  MetricsIntervalLimit$inboundSchema,
  MetricsIntervalLimit$Outbound,
  MetricsIntervalLimit$outboundSchema,
} from "./metricsintervallimit.js";

/**
 * Date interval limits to get metrics for each interval.
 */
export type MetricsIntervalsLimits = {
  /**
   * Date interval limit to get metrics for a given interval.
   */
  hour: MetricsIntervalLimit;
  /**
   * Date interval limit to get metrics for a given interval.
   */
  day: MetricsIntervalLimit;
  /**
   * Date interval limit to get metrics for a given interval.
   */
  week: MetricsIntervalLimit;
  /**
   * Date interval limit to get metrics for a given interval.
   */
  month: MetricsIntervalLimit;
  /**
   * Date interval limit to get metrics for a given interval.
   */
  year: MetricsIntervalLimit;
};

/** @internal */
export const MetricsIntervalsLimits$inboundSchema: z.ZodType<
  MetricsIntervalsLimits,
  z.ZodTypeDef,
  unknown
> = z.object({
  hour: MetricsIntervalLimit$inboundSchema,
  day: MetricsIntervalLimit$inboundSchema,
  week: MetricsIntervalLimit$inboundSchema,
  month: MetricsIntervalLimit$inboundSchema,
  year: MetricsIntervalLimit$inboundSchema,
});

/** @internal */
export type MetricsIntervalsLimits$Outbound = {
  hour: MetricsIntervalLimit$Outbound;
  day: MetricsIntervalLimit$Outbound;
  week: MetricsIntervalLimit$Outbound;
  month: MetricsIntervalLimit$Outbound;
  year: MetricsIntervalLimit$Outbound;
};

/** @internal */
export const MetricsIntervalsLimits$outboundSchema: z.ZodType<
  MetricsIntervalsLimits$Outbound,
  z.ZodTypeDef,
  MetricsIntervalsLimits
> = z.object({
  hour: MetricsIntervalLimit$outboundSchema,
  day: MetricsIntervalLimit$outboundSchema,
  week: MetricsIntervalLimit$outboundSchema,
  month: MetricsIntervalLimit$outboundSchema,
  year: MetricsIntervalLimit$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MetricsIntervalsLimits$ {
  /** @deprecated use `MetricsIntervalsLimits$inboundSchema` instead. */
  export const inboundSchema = MetricsIntervalsLimits$inboundSchema;
  /** @deprecated use `MetricsIntervalsLimits$outboundSchema` instead. */
  export const outboundSchema = MetricsIntervalsLimits$outboundSchema;
  /** @deprecated use `MetricsIntervalsLimits$Outbound` instead. */
  export type Outbound = MetricsIntervalsLimits$Outbound;
}

export function metricsIntervalsLimitsToJSON(
  metricsIntervalsLimits: MetricsIntervalsLimits,
): string {
  return JSON.stringify(
    MetricsIntervalsLimits$outboundSchema.parse(metricsIntervalsLimits),
  );
}

export function metricsIntervalsLimitsFromJSON(
  jsonString: string,
): SafeParseResult<MetricsIntervalsLimits, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => MetricsIntervalsLimits$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'MetricsIntervalsLimits' from JSON`,
  );
}
