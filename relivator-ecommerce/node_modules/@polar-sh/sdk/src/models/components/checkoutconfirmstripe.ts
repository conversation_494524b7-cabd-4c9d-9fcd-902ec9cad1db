/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Address,
  Address$inboundSchema,
  Address$Outbound,
  Address$outboundSchema,
} from "./address.js";

export type CheckoutConfirmStripeCustomFieldData =
  | string
  | number
  | boolean
  | Date;

/**
 * Confirm a checkout session using a Stripe confirmation token.
 */
export type CheckoutConfirmStripe = {
  /**
   * Key-value object storing custom field values.
   */
  customFieldData?:
    | { [k: string]: string | number | boolean | Date | null }
    | undefined;
  /**
   * ID of the product to checkout. Must be present in the checkout's product list.
   */
  productId?: string | null | undefined;
  /**
   * ID of the product price to checkout. Must correspond to a price present in the checkout's product list.
   *
   * @deprecated field: This will be removed in a future release, please migrate away from it as soon as possible.
   */
  productPriceId?: string | null | undefined;
  amount?: number | null | undefined;
  isBusinessCustomer?: boolean | null | undefined;
  customerName?: string | null | undefined;
  customerEmail?: string | null | undefined;
  customerBillingName?: string | null | undefined;
  customerBillingAddress?: Address | null | undefined;
  customerTaxId?: string | null | undefined;
  /**
   * Discount code to apply to the checkout.
   */
  discountCode?: string | null | undefined;
  /**
   * ID of the Stripe confirmation token. Required for fixed prices and custom prices.
   */
  confirmationTokenId?: string | null | undefined;
};

/** @internal */
export const CheckoutConfirmStripeCustomFieldData$inboundSchema: z.ZodType<
  CheckoutConfirmStripeCustomFieldData,
  z.ZodTypeDef,
  unknown
> = z.union([
  z.string(),
  z.number().int(),
  z.boolean(),
  z.string().datetime({ offset: true }).transform(v => new Date(v)),
]);

/** @internal */
export type CheckoutConfirmStripeCustomFieldData$Outbound =
  | string
  | number
  | boolean
  | string;

/** @internal */
export const CheckoutConfirmStripeCustomFieldData$outboundSchema: z.ZodType<
  CheckoutConfirmStripeCustomFieldData$Outbound,
  z.ZodTypeDef,
  CheckoutConfirmStripeCustomFieldData
> = z.union([
  z.string(),
  z.number().int(),
  z.boolean(),
  z.date().transform(v => v.toISOString()),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutConfirmStripeCustomFieldData$ {
  /** @deprecated use `CheckoutConfirmStripeCustomFieldData$inboundSchema` instead. */
  export const inboundSchema =
    CheckoutConfirmStripeCustomFieldData$inboundSchema;
  /** @deprecated use `CheckoutConfirmStripeCustomFieldData$outboundSchema` instead. */
  export const outboundSchema =
    CheckoutConfirmStripeCustomFieldData$outboundSchema;
  /** @deprecated use `CheckoutConfirmStripeCustomFieldData$Outbound` instead. */
  export type Outbound = CheckoutConfirmStripeCustomFieldData$Outbound;
}

export function checkoutConfirmStripeCustomFieldDataToJSON(
  checkoutConfirmStripeCustomFieldData: CheckoutConfirmStripeCustomFieldData,
): string {
  return JSON.stringify(
    CheckoutConfirmStripeCustomFieldData$outboundSchema.parse(
      checkoutConfirmStripeCustomFieldData,
    ),
  );
}

export function checkoutConfirmStripeCustomFieldDataFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutConfirmStripeCustomFieldData, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      CheckoutConfirmStripeCustomFieldData$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutConfirmStripeCustomFieldData' from JSON`,
  );
}

/** @internal */
export const CheckoutConfirmStripe$inboundSchema: z.ZodType<
  CheckoutConfirmStripe,
  z.ZodTypeDef,
  unknown
> = z.object({
  custom_field_data: z.record(
    z.nullable(
      z.union([
        z.string(),
        z.number().int(),
        z.boolean(),
        z.string().datetime({ offset: true }).transform(v => new Date(v)),
      ]),
    ),
  ).optional(),
  product_id: z.nullable(z.string()).optional(),
  product_price_id: z.nullable(z.string()).optional(),
  amount: z.nullable(z.number().int()).optional(),
  is_business_customer: z.nullable(z.boolean()).optional(),
  customer_name: z.nullable(z.string()).optional(),
  customer_email: z.nullable(z.string()).optional(),
  customer_billing_name: z.nullable(z.string()).optional(),
  customer_billing_address: z.nullable(Address$inboundSchema).optional(),
  customer_tax_id: z.nullable(z.string()).optional(),
  discount_code: z.nullable(z.string()).optional(),
  confirmation_token_id: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    "custom_field_data": "customFieldData",
    "product_id": "productId",
    "product_price_id": "productPriceId",
    "is_business_customer": "isBusinessCustomer",
    "customer_name": "customerName",
    "customer_email": "customerEmail",
    "customer_billing_name": "customerBillingName",
    "customer_billing_address": "customerBillingAddress",
    "customer_tax_id": "customerTaxId",
    "discount_code": "discountCode",
    "confirmation_token_id": "confirmationTokenId",
  });
});

/** @internal */
export type CheckoutConfirmStripe$Outbound = {
  custom_field_data?:
    | { [k: string]: string | number | boolean | string | null }
    | undefined;
  product_id?: string | null | undefined;
  product_price_id?: string | null | undefined;
  amount?: number | null | undefined;
  is_business_customer?: boolean | null | undefined;
  customer_name?: string | null | undefined;
  customer_email?: string | null | undefined;
  customer_billing_name?: string | null | undefined;
  customer_billing_address?: Address$Outbound | null | undefined;
  customer_tax_id?: string | null | undefined;
  discount_code?: string | null | undefined;
  confirmation_token_id?: string | null | undefined;
};

/** @internal */
export const CheckoutConfirmStripe$outboundSchema: z.ZodType<
  CheckoutConfirmStripe$Outbound,
  z.ZodTypeDef,
  CheckoutConfirmStripe
> = z.object({
  customFieldData: z.record(
    z.nullable(
      z.union([
        z.string(),
        z.number().int(),
        z.boolean(),
        z.date().transform(v => v.toISOString()),
      ]),
    ),
  ).optional(),
  productId: z.nullable(z.string()).optional(),
  productPriceId: z.nullable(z.string()).optional(),
  amount: z.nullable(z.number().int()).optional(),
  isBusinessCustomer: z.nullable(z.boolean()).optional(),
  customerName: z.nullable(z.string()).optional(),
  customerEmail: z.nullable(z.string()).optional(),
  customerBillingName: z.nullable(z.string()).optional(),
  customerBillingAddress: z.nullable(Address$outboundSchema).optional(),
  customerTaxId: z.nullable(z.string()).optional(),
  discountCode: z.nullable(z.string()).optional(),
  confirmationTokenId: z.nullable(z.string()).optional(),
}).transform((v) => {
  return remap$(v, {
    customFieldData: "custom_field_data",
    productId: "product_id",
    productPriceId: "product_price_id",
    isBusinessCustomer: "is_business_customer",
    customerName: "customer_name",
    customerEmail: "customer_email",
    customerBillingName: "customer_billing_name",
    customerBillingAddress: "customer_billing_address",
    customerTaxId: "customer_tax_id",
    discountCode: "discount_code",
    confirmationTokenId: "confirmation_token_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutConfirmStripe$ {
  /** @deprecated use `CheckoutConfirmStripe$inboundSchema` instead. */
  export const inboundSchema = CheckoutConfirmStripe$inboundSchema;
  /** @deprecated use `CheckoutConfirmStripe$outboundSchema` instead. */
  export const outboundSchema = CheckoutConfirmStripe$outboundSchema;
  /** @deprecated use `CheckoutConfirmStripe$Outbound` instead. */
  export type Outbound = CheckoutConfirmStripe$Outbound;
}

export function checkoutConfirmStripeToJSON(
  checkoutConfirmStripe: CheckoutConfirmStripe,
): string {
  return JSON.stringify(
    CheckoutConfirmStripe$outboundSchema.parse(checkoutConfirmStripe),
  );
}

export function checkoutConfirmStripeFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutConfirmStripe, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutConfirmStripe$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutConfirmStripe' from JSON`,
  );
}
