/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const CustomFieldSortProperty = {
  CreatedAt: "created_at",
  MinusCreatedAt: "-created_at",
  Slug: "slug",
  MinusSlug: "-slug",
  Name: "name",
  MinusName: "-name",
  Type: "type",
  MinusType: "-type",
} as const;
export type CustomFieldSortProperty = ClosedEnum<
  typeof CustomFieldSortProperty
>;

/** @internal */
export const CustomFieldSortProperty$inboundSchema: z.ZodNativeEnum<
  typeof CustomFieldSortProperty
> = z.nativeEnum(CustomFieldSortProperty);

/** @internal */
export const CustomFieldSortProperty$outboundSchema: z.ZodNativeEnum<
  typeof CustomFieldSortProperty
> = CustomFieldSortProperty$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldSortProperty$ {
  /** @deprecated use `CustomFieldSortProperty$inboundSchema` instead. */
  export const inboundSchema = CustomFieldSortProperty$inboundSchema;
  /** @deprecated use `CustomFieldSortProperty$outboundSchema` instead. */
  export const outboundSchema = CustomFieldSortProperty$outboundSchema;
}
