/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type BenefitGrantDownloadablesProperties = {
  files?: Array<string> | undefined;
};

/** @internal */
export const BenefitGrantDownloadablesProperties$inboundSchema: z.ZodType<
  BenefitGrantDownloadablesProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  files: z.array(z.string()).optional(),
});

/** @internal */
export type BenefitGrantDownloadablesProperties$Outbound = {
  files?: Array<string> | undefined;
};

/** @internal */
export const BenefitGrantDownloadablesProperties$outboundSchema: z.ZodType<
  BenefitGrantDownloadablesProperties$Outbound,
  z.ZodTypeDef,
  BenefitGrantDownloadablesProperties
> = z.object({
  files: z.array(z.string()).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGrantDownloadablesProperties$ {
  /** @deprecated use `BenefitGrantDownloadablesProperties$inboundSchema` instead. */
  export const inboundSchema =
    BenefitGrantDownloadablesProperties$inboundSchema;
  /** @deprecated use `BenefitGrantDownloadablesProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitGrantDownloadablesProperties$outboundSchema;
  /** @deprecated use `BenefitGrantDownloadablesProperties$Outbound` instead. */
  export type Outbound = BenefitGrantDownloadablesProperties$Outbound;
}

export function benefitGrantDownloadablesPropertiesToJSON(
  benefitGrantDownloadablesProperties: BenefitGrantDownloadablesProperties,
): string {
  return JSON.stringify(
    BenefitGrantDownloadablesProperties$outboundSchema.parse(
      benefitGrantDownloadablesProperties,
    ),
  );
}

export function benefitGrantDownloadablesPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGrantDownloadablesProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitGrantDownloadablesProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGrantDownloadablesProperties' from JSON`,
  );
}
