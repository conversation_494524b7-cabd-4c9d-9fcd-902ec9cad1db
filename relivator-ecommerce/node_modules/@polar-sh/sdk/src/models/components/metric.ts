/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  MetricType,
  MetricType$inboundSchema,
  MetricType$outboundSchema,
} from "./metrictype.js";

/**
 * Information about a metric.
 */
export type Metric = {
  /**
   * Unique identifier for the metric.
   */
  slug: string;
  /**
   * Human-readable name for the metric.
   */
  displayName: string;
  type: MetricType;
};

/** @internal */
export const Metric$inboundSchema: z.ZodType<Metric, z.ZodTypeDef, unknown> = z
  .object({
    slug: z.string(),
    display_name: z.string(),
    type: MetricType$inboundSchema,
  }).transform((v) => {
    return remap$(v, {
      "display_name": "displayName",
    });
  });

/** @internal */
export type Metric$Outbound = {
  slug: string;
  display_name: string;
  type: string;
};

/** @internal */
export const Metric$outboundSchema: z.ZodType<
  Metric$Outbound,
  z.ZodTypeDef,
  Metric
> = z.object({
  slug: z.string(),
  displayName: z.string(),
  type: MetricType$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    displayName: "display_name",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Metric$ {
  /** @deprecated use `Metric$inboundSchema` instead. */
  export const inboundSchema = Metric$inboundSchema;
  /** @deprecated use `Metric$outboundSchema` instead. */
  export const outboundSchema = Metric$outboundSchema;
  /** @deprecated use `Metric$Outbound` instead. */
  export type Outbound = Metric$Outbound;
}

export function metricToJSON(metric: Metric): string {
  return JSON.stringify(Metric$outboundSchema.parse(metric));
}

export function metricFromJSON(
  jsonString: string,
): SafeParseResult<Metric, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Metric$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Metric' from JSON`,
  );
}
