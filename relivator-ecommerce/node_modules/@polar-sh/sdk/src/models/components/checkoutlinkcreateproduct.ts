/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type CheckoutLinkCreateProductMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * Schema to create a new checkout link from a a single product.
 *
 * @remarks
 *
 * **Deprecated**: Use `CheckoutLinkCreateProducts` instead.
 */
export type CheckoutLinkCreateProduct = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  /**
   * Payment processor to use. Currently only Stripe is supported.
   */
  paymentProcessor?: "stripe" | undefined;
  /**
   * Optional label to distinguish links internally
   */
  label?: string | null | undefined;
  /**
   * Whether to allow the customer to apply discount codes. If you apply a discount through `discount_id`, it'll still be applied, but the customer won't be able to change it.
   */
  allowDiscountCodes?: boolean | undefined;
  /**
   * Whether to require the customer to fill their full billing address, instead of just the country. Customers in the US will always be required to fill their full address, regardless of this setting.
   */
  requireBillingAddress?: boolean | undefined;
  /**
   * ID of the discount to apply to the checkout. If the discount is not applicable anymore when opening the checkout link, it'll be ignored.
   */
  discountId?: string | null | undefined;
  /**
   * URL where the customer will be redirected after a successful payment.You can add the `checkout_id={CHECKOUT_ID}` query parameter to retrieve the checkout session id.
   */
  successUrl?: string | null | undefined;
  productId: string;
};

/** @internal */
export const CheckoutLinkCreateProductMetadata$inboundSchema: z.ZodType<
  CheckoutLinkCreateProductMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CheckoutLinkCreateProductMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CheckoutLinkCreateProductMetadata$outboundSchema: z.ZodType<
  CheckoutLinkCreateProductMetadata$Outbound,
  z.ZodTypeDef,
  CheckoutLinkCreateProductMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutLinkCreateProductMetadata$ {
  /** @deprecated use `CheckoutLinkCreateProductMetadata$inboundSchema` instead. */
  export const inboundSchema = CheckoutLinkCreateProductMetadata$inboundSchema;
  /** @deprecated use `CheckoutLinkCreateProductMetadata$outboundSchema` instead. */
  export const outboundSchema =
    CheckoutLinkCreateProductMetadata$outboundSchema;
  /** @deprecated use `CheckoutLinkCreateProductMetadata$Outbound` instead. */
  export type Outbound = CheckoutLinkCreateProductMetadata$Outbound;
}

export function checkoutLinkCreateProductMetadataToJSON(
  checkoutLinkCreateProductMetadata: CheckoutLinkCreateProductMetadata,
): string {
  return JSON.stringify(
    CheckoutLinkCreateProductMetadata$outboundSchema.parse(
      checkoutLinkCreateProductMetadata,
    ),
  );
}

export function checkoutLinkCreateProductMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutLinkCreateProductMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutLinkCreateProductMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutLinkCreateProductMetadata' from JSON`,
  );
}

/** @internal */
export const CheckoutLinkCreateProduct$inboundSchema: z.ZodType<
  CheckoutLinkCreateProduct,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  payment_processor: z.literal("stripe").optional(),
  label: z.nullable(z.string()).optional(),
  allow_discount_codes: z.boolean().default(true),
  require_billing_address: z.boolean().default(false),
  discount_id: z.nullable(z.string()).optional(),
  success_url: z.nullable(z.string()).optional(),
  product_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "payment_processor": "paymentProcessor",
    "allow_discount_codes": "allowDiscountCodes",
    "require_billing_address": "requireBillingAddress",
    "discount_id": "discountId",
    "success_url": "successUrl",
    "product_id": "productId",
  });
});

/** @internal */
export type CheckoutLinkCreateProduct$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  payment_processor: "stripe";
  label?: string | null | undefined;
  allow_discount_codes: boolean;
  require_billing_address: boolean;
  discount_id?: string | null | undefined;
  success_url?: string | null | undefined;
  product_id: string;
};

/** @internal */
export const CheckoutLinkCreateProduct$outboundSchema: z.ZodType<
  CheckoutLinkCreateProduct$Outbound,
  z.ZodTypeDef,
  CheckoutLinkCreateProduct
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  paymentProcessor: z.literal("stripe").default("stripe" as const),
  label: z.nullable(z.string()).optional(),
  allowDiscountCodes: z.boolean().default(true),
  requireBillingAddress: z.boolean().default(false),
  discountId: z.nullable(z.string()).optional(),
  successUrl: z.nullable(z.string()).optional(),
  productId: z.string(),
}).transform((v) => {
  return remap$(v, {
    paymentProcessor: "payment_processor",
    allowDiscountCodes: "allow_discount_codes",
    requireBillingAddress: "require_billing_address",
    discountId: "discount_id",
    successUrl: "success_url",
    productId: "product_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutLinkCreateProduct$ {
  /** @deprecated use `CheckoutLinkCreateProduct$inboundSchema` instead. */
  export const inboundSchema = CheckoutLinkCreateProduct$inboundSchema;
  /** @deprecated use `CheckoutLinkCreateProduct$outboundSchema` instead. */
  export const outboundSchema = CheckoutLinkCreateProduct$outboundSchema;
  /** @deprecated use `CheckoutLinkCreateProduct$Outbound` instead. */
  export type Outbound = CheckoutLinkCreateProduct$Outbound;
}

export function checkoutLinkCreateProductToJSON(
  checkoutLinkCreateProduct: CheckoutLinkCreateProduct,
): string {
  return JSON.stringify(
    CheckoutLinkCreateProduct$outboundSchema.parse(checkoutLinkCreateProduct),
  );
}

export function checkoutLinkCreateProductFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutLinkCreateProduct, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutLinkCreateProduct$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutLinkCreateProduct' from JSON`,
  );
}
