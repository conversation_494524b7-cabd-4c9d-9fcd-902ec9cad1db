/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type UserInfoOrganization = {
  sub: string;
  name?: string | null | undefined;
};

/** @internal */
export const UserInfoOrganization$inboundSchema: z.ZodType<
  UserInfoOrganization,
  z.ZodTypeDef,
  unknown
> = z.object({
  sub: z.string(),
  name: z.nullable(z.string()).optional(),
});

/** @internal */
export type UserInfoOrganization$Outbound = {
  sub: string;
  name?: string | null | undefined;
};

/** @internal */
export const UserInfoOrganization$outboundSchema: z.ZodType<
  UserInfoOrganization$Outbound,
  z.ZodTypeDef,
  UserInfoOrganization
> = z.object({
  sub: z.string(),
  name: z.nullable(z.string()).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace UserInfoOrganization$ {
  /** @deprecated use `UserInfoOrganization$inboundSchema` instead. */
  export const inboundSchema = UserInfoOrganization$inboundSchema;
  /** @deprecated use `UserInfoOrganization$outboundSchema` instead. */
  export const outboundSchema = UserInfoOrganization$outboundSchema;
  /** @deprecated use `UserInfoOrganization$Outbound` instead. */
  export type Outbound = UserInfoOrganization$Outbound;
}

export function userInfoOrganizationToJSON(
  userInfoOrganization: UserInfoOrganization,
): string {
  return JSON.stringify(
    UserInfoOrganization$outboundSchema.parse(userInfoOrganization),
  );
}

export function userInfoOrganizationFromJSON(
  jsonString: string,
): SafeParseResult<UserInfoOrganization, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => UserInfoOrganization$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'UserInfoOrganization' from JSON`,
  );
}
