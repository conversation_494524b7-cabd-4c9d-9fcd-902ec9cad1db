/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const OrderBillingReason = {
  Purchase: "purchase",
  SubscriptionCreate: "subscription_create",
  SubscriptionCycle: "subscription_cycle",
  SubscriptionUpdate: "subscription_update",
} as const;
export type OrderBillingReason = ClosedEnum<typeof OrderBillingReason>;

/** @internal */
export const OrderBillingReason$inboundSchema: z.ZodNativeEnum<
  typeof OrderBillingReason
> = z.nativeEnum(OrderBillingReason);

/** @internal */
export const OrderBillingReason$outboundSchema: z.ZodNativeEnum<
  typeof OrderBillingReason
> = OrderBillingReason$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrderBillingReason$ {
  /** @deprecated use `OrderBillingReason$inboundSchema` instead. */
  export const inboundSchema = OrderBillingReason$inboundSchema;
  /** @deprecated use `OrderBillingReason$outboundSchema` instead. */
  export const outboundSchema = OrderBillingReason$outboundSchema;
}
