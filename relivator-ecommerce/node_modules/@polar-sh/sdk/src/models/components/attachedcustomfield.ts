/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomField,
  CustomField$inboundSchema,
  CustomField$Outbound,
  CustomField$outboundSchema,
} from "./customfield.js";

/**
 * Schema of a custom field attached to a resource.
 */
export type AttachedCustomField = {
  /**
   * ID of the custom field.
   */
  customFieldId: string;
  customField: CustomField;
  /**
   * Order of the custom field in the resource.
   */
  order: number;
  /**
   * Whether the value is required for this custom field.
   */
  required: boolean;
};

/** @internal */
export const AttachedCustomField$inboundSchema: z.ZodType<
  AttachedCustomField,
  z.ZodTypeDef,
  unknown
> = z.object({
  custom_field_id: z.string(),
  custom_field: CustomField$inboundSchema,
  order: z.number().int(),
  required: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    "custom_field_id": "customFieldId",
    "custom_field": "customField",
  });
});

/** @internal */
export type AttachedCustomField$Outbound = {
  custom_field_id: string;
  custom_field: CustomField$Outbound;
  order: number;
  required: boolean;
};

/** @internal */
export const AttachedCustomField$outboundSchema: z.ZodType<
  AttachedCustomField$Outbound,
  z.ZodTypeDef,
  AttachedCustomField
> = z.object({
  customFieldId: z.string(),
  customField: CustomField$outboundSchema,
  order: z.number().int(),
  required: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    customFieldId: "custom_field_id",
    customField: "custom_field",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace AttachedCustomField$ {
  /** @deprecated use `AttachedCustomField$inboundSchema` instead. */
  export const inboundSchema = AttachedCustomField$inboundSchema;
  /** @deprecated use `AttachedCustomField$outboundSchema` instead. */
  export const outboundSchema = AttachedCustomField$outboundSchema;
  /** @deprecated use `AttachedCustomField$Outbound` instead. */
  export type Outbound = AttachedCustomField$Outbound;
}

export function attachedCustomFieldToJSON(
  attachedCustomField: AttachedCustomField,
): string {
  return JSON.stringify(
    AttachedCustomField$outboundSchema.parse(attachedCustomField),
  );
}

export function attachedCustomFieldFromJSON(
  jsonString: string,
): SafeParseResult<AttachedCustomField, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => AttachedCustomField$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'AttachedCustomField' from JSON`,
  );
}
