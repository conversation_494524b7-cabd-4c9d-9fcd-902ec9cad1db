/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ProductPriceType,
  ProductPriceType$inboundSchema,
  ProductPriceType$outboundSchema,
} from "./productpricetype.js";
import {
  SubscriptionRecurringInterval,
  SubscriptionRecurringInterval$inboundSchema,
  SubscriptionRecurringInterval$outboundSchema,
} from "./subscriptionrecurringinterval.js";

/**
 * A fixed price for a product.
 */
export type ProductPriceFixed = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the price.
   */
  id: string;
  amountType?: "fixed" | undefined;
  /**
   * Whether the price is archived and no longer available.
   */
  isArchived: boolean;
  /**
   * The ID of the product owning the price.
   */
  productId: string;
  type: ProductPriceType;
  /**
   * @deprecated field: This will be removed in a future release, please migrate away from it as soon as possible.
   */
  recurringInterval: SubscriptionRecurringInterval | null;
  /**
   * The currency.
   */
  priceCurrency: string;
  /**
   * The price in cents.
   */
  priceAmount: number;
};

/** @internal */
export const ProductPriceFixed$inboundSchema: z.ZodType<
  ProductPriceFixed,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  amount_type: z.literal("fixed").optional(),
  is_archived: z.boolean(),
  product_id: z.string(),
  type: ProductPriceType$inboundSchema,
  recurring_interval: z.nullable(SubscriptionRecurringInterval$inboundSchema),
  price_currency: z.string(),
  price_amount: z.number().int(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "amount_type": "amountType",
    "is_archived": "isArchived",
    "product_id": "productId",
    "recurring_interval": "recurringInterval",
    "price_currency": "priceCurrency",
    "price_amount": "priceAmount",
  });
});

/** @internal */
export type ProductPriceFixed$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  amount_type: "fixed";
  is_archived: boolean;
  product_id: string;
  type: string;
  recurring_interval: string | null;
  price_currency: string;
  price_amount: number;
};

/** @internal */
export const ProductPriceFixed$outboundSchema: z.ZodType<
  ProductPriceFixed$Outbound,
  z.ZodTypeDef,
  ProductPriceFixed
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  amountType: z.literal("fixed").default("fixed" as const),
  isArchived: z.boolean(),
  productId: z.string(),
  type: ProductPriceType$outboundSchema,
  recurringInterval: z.nullable(SubscriptionRecurringInterval$outboundSchema),
  priceCurrency: z.string(),
  priceAmount: z.number().int(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    amountType: "amount_type",
    isArchived: "is_archived",
    productId: "product_id",
    recurringInterval: "recurring_interval",
    priceCurrency: "price_currency",
    priceAmount: "price_amount",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ProductPriceFixed$ {
  /** @deprecated use `ProductPriceFixed$inboundSchema` instead. */
  export const inboundSchema = ProductPriceFixed$inboundSchema;
  /** @deprecated use `ProductPriceFixed$outboundSchema` instead. */
  export const outboundSchema = ProductPriceFixed$outboundSchema;
  /** @deprecated use `ProductPriceFixed$Outbound` instead. */
  export type Outbound = ProductPriceFixed$Outbound;
}

export function productPriceFixedToJSON(
  productPriceFixed: ProductPriceFixed,
): string {
  return JSON.stringify(
    ProductPriceFixed$outboundSchema.parse(productPriceFixed),
  );
}

export function productPriceFixedFromJSON(
  jsonString: string,
): SafeParseResult<ProductPriceFixed, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ProductPriceFixed$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ProductPriceFixed' from JSON`,
  );
}
