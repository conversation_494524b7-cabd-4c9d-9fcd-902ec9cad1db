/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  OrganizationSocialPlatforms,
  OrganizationSocialPlatforms$inboundSchema,
  OrganizationSocialPlatforms$outboundSchema,
} from "./organizationsocialplatforms.js";

export type OrganizationSocialLink = {
  platform: OrganizationSocialPlatforms;
  /**
   * The URL to the organization profile
   */
  url: string;
};

/** @internal */
export const OrganizationSocialLink$inboundSchema: z.ZodType<
  OrganizationSocialLink,
  z.ZodTypeDef,
  unknown
> = z.object({
  platform: OrganizationSocialPlatforms$inboundSchema,
  url: z.string(),
});

/** @internal */
export type OrganizationSocialLink$Outbound = {
  platform: string;
  url: string;
};

/** @internal */
export const OrganizationSocialLink$outboundSchema: z.ZodType<
  OrganizationSocialLink$Outbound,
  z.ZodTypeDef,
  OrganizationSocialLink
> = z.object({
  platform: OrganizationSocialPlatforms$outboundSchema,
  url: z.string(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrganizationSocialLink$ {
  /** @deprecated use `OrganizationSocialLink$inboundSchema` instead. */
  export const inboundSchema = OrganizationSocialLink$inboundSchema;
  /** @deprecated use `OrganizationSocialLink$outboundSchema` instead. */
  export const outboundSchema = OrganizationSocialLink$outboundSchema;
  /** @deprecated use `OrganizationSocialLink$Outbound` instead. */
  export type Outbound = OrganizationSocialLink$Outbound;
}

export function organizationSocialLinkToJSON(
  organizationSocialLink: OrganizationSocialLink,
): string {
  return JSON.stringify(
    OrganizationSocialLink$outboundSchema.parse(organizationSocialLink),
  );
}

export function organizationSocialLinkFromJSON(
  jsonString: string,
): SafeParseResult<OrganizationSocialLink, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrganizationSocialLink$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrganizationSocialLink' from JSON`,
  );
}
