/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitMeterCreditCreateProperties,
  BenefitMeterCreditCreateProperties$inboundSchema,
  BenefitMeterCreditCreateProperties$Outbound,
  BenefitMeterCreditCreateProperties$outboundSchema,
} from "./benefitmetercreditcreateproperties.js";

export type BenefitMeterCreditUpdateMetadata =
  | string
  | number
  | number
  | boolean;

export type BenefitMeterCreditUpdate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  /**
   * The description of the benefit. Will be displayed on products having this benefit.
   */
  description?: string | null | undefined;
  type?: "meter_credit" | undefined;
  properties?: BenefitMeterCreditCreateProperties | null | undefined;
};

/** @internal */
export const BenefitMeterCreditUpdateMetadata$inboundSchema: z.ZodType<
  BenefitMeterCreditUpdateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitMeterCreditUpdateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitMeterCreditUpdateMetadata$outboundSchema: z.ZodType<
  BenefitMeterCreditUpdateMetadata$Outbound,
  z.ZodTypeDef,
  BenefitMeterCreditUpdateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitMeterCreditUpdateMetadata$ {
  /** @deprecated use `BenefitMeterCreditUpdateMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitMeterCreditUpdateMetadata$inboundSchema;
  /** @deprecated use `BenefitMeterCreditUpdateMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitMeterCreditUpdateMetadata$outboundSchema;
  /** @deprecated use `BenefitMeterCreditUpdateMetadata$Outbound` instead. */
  export type Outbound = BenefitMeterCreditUpdateMetadata$Outbound;
}

export function benefitMeterCreditUpdateMetadataToJSON(
  benefitMeterCreditUpdateMetadata: BenefitMeterCreditUpdateMetadata,
): string {
  return JSON.stringify(
    BenefitMeterCreditUpdateMetadata$outboundSchema.parse(
      benefitMeterCreditUpdateMetadata,
    ),
  );
}

export function benefitMeterCreditUpdateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitMeterCreditUpdateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitMeterCreditUpdateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitMeterCreditUpdateMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitMeterCreditUpdate$inboundSchema: z.ZodType<
  BenefitMeterCreditUpdate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  description: z.nullable(z.string()).optional(),
  type: z.literal("meter_credit").optional(),
  properties: z.nullable(BenefitMeterCreditCreateProperties$inboundSchema)
    .optional(),
});

/** @internal */
export type BenefitMeterCreditUpdate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  description?: string | null | undefined;
  type: "meter_credit";
  properties?: BenefitMeterCreditCreateProperties$Outbound | null | undefined;
};

/** @internal */
export const BenefitMeterCreditUpdate$outboundSchema: z.ZodType<
  BenefitMeterCreditUpdate$Outbound,
  z.ZodTypeDef,
  BenefitMeterCreditUpdate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  description: z.nullable(z.string()).optional(),
  type: z.literal("meter_credit").default("meter_credit" as const),
  properties: z.nullable(BenefitMeterCreditCreateProperties$outboundSchema)
    .optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitMeterCreditUpdate$ {
  /** @deprecated use `BenefitMeterCreditUpdate$inboundSchema` instead. */
  export const inboundSchema = BenefitMeterCreditUpdate$inboundSchema;
  /** @deprecated use `BenefitMeterCreditUpdate$outboundSchema` instead. */
  export const outboundSchema = BenefitMeterCreditUpdate$outboundSchema;
  /** @deprecated use `BenefitMeterCreditUpdate$Outbound` instead. */
  export type Outbound = BenefitMeterCreditUpdate$Outbound;
}

export function benefitMeterCreditUpdateToJSON(
  benefitMeterCreditUpdate: BenefitMeterCreditUpdate,
): string {
  return JSON.stringify(
    BenefitMeterCreditUpdate$outboundSchema.parse(benefitMeterCreditUpdate),
  );
}

export function benefitMeterCreditUpdateFromJSON(
  jsonString: string,
): SafeParseResult<BenefitMeterCreditUpdate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitMeterCreditUpdate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitMeterCreditUpdate' from JSON`,
  );
}
