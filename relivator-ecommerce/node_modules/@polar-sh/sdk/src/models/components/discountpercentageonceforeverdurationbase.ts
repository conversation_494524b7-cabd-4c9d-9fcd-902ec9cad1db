/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountDuration,
  DiscountDuration$inboundSchema,
  DiscountDuration$outboundSchema,
} from "./discountduration.js";
import {
  DiscountType,
  DiscountType$inboundSchema,
  DiscountType$outboundSchema,
} from "./discounttype.js";

export type DiscountPercentageOnceForeverDurationBaseMetadata =
  | string
  | number
  | number
  | boolean;

export type DiscountPercentageOnceForeverDurationBase = {
  duration: DiscountDuration;
  type: DiscountType;
  basisPoints: number;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Name of the discount. Will be displayed to the customer when the discount is applied.
   */
  name: string;
  /**
   * Code customers can use to apply the discount during checkout.
   */
  code: string | null;
  /**
   * Timestamp after which the discount is redeemable.
   */
  startsAt: Date | null;
  /**
   * Timestamp after which the discount is no longer redeemable.
   */
  endsAt: Date | null;
  /**
   * Maximum number of times the discount can be redeemed.
   */
  maxRedemptions: number | null;
  /**
   * Number of times the discount has been redeemed.
   */
  redemptionsCount: number;
  /**
   * The organization ID.
   */
  organizationId: string;
};

/** @internal */
export const DiscountPercentageOnceForeverDurationBaseMetadata$inboundSchema:
  z.ZodType<
    DiscountPercentageOnceForeverDurationBaseMetadata,
    z.ZodTypeDef,
    unknown
  > = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type DiscountPercentageOnceForeverDurationBaseMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const DiscountPercentageOnceForeverDurationBaseMetadata$outboundSchema:
  z.ZodType<
    DiscountPercentageOnceForeverDurationBaseMetadata$Outbound,
    z.ZodTypeDef,
    DiscountPercentageOnceForeverDurationBaseMetadata
  > = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountPercentageOnceForeverDurationBaseMetadata$ {
  /** @deprecated use `DiscountPercentageOnceForeverDurationBaseMetadata$inboundSchema` instead. */
  export const inboundSchema =
    DiscountPercentageOnceForeverDurationBaseMetadata$inboundSchema;
  /** @deprecated use `DiscountPercentageOnceForeverDurationBaseMetadata$outboundSchema` instead. */
  export const outboundSchema =
    DiscountPercentageOnceForeverDurationBaseMetadata$outboundSchema;
  /** @deprecated use `DiscountPercentageOnceForeverDurationBaseMetadata$Outbound` instead. */
  export type Outbound =
    DiscountPercentageOnceForeverDurationBaseMetadata$Outbound;
}

export function discountPercentageOnceForeverDurationBaseMetadataToJSON(
  discountPercentageOnceForeverDurationBaseMetadata:
    DiscountPercentageOnceForeverDurationBaseMetadata,
): string {
  return JSON.stringify(
    DiscountPercentageOnceForeverDurationBaseMetadata$outboundSchema.parse(
      discountPercentageOnceForeverDurationBaseMetadata,
    ),
  );
}

export function discountPercentageOnceForeverDurationBaseMetadataFromJSON(
  jsonString: string,
): SafeParseResult<
  DiscountPercentageOnceForeverDurationBaseMetadata,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      DiscountPercentageOnceForeverDurationBaseMetadata$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'DiscountPercentageOnceForeverDurationBaseMetadata' from JSON`,
  );
}

/** @internal */
export const DiscountPercentageOnceForeverDurationBase$inboundSchema: z.ZodType<
  DiscountPercentageOnceForeverDurationBase,
  z.ZodTypeDef,
  unknown
> = z.object({
  duration: DiscountDuration$inboundSchema,
  type: DiscountType$inboundSchema,
  basis_points: z.number().int(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  name: z.string(),
  code: z.nullable(z.string()),
  starts_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  ends_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  max_redemptions: z.nullable(z.number().int()),
  redemptions_count: z.number().int(),
  organization_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "basis_points": "basisPoints",
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "starts_at": "startsAt",
    "ends_at": "endsAt",
    "max_redemptions": "maxRedemptions",
    "redemptions_count": "redemptionsCount",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type DiscountPercentageOnceForeverDurationBase$Outbound = {
  duration: string;
  type: string;
  basis_points: number;
  created_at: string;
  modified_at: string | null;
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  name: string;
  code: string | null;
  starts_at: string | null;
  ends_at: string | null;
  max_redemptions: number | null;
  redemptions_count: number;
  organization_id: string;
};

/** @internal */
export const DiscountPercentageOnceForeverDurationBase$outboundSchema:
  z.ZodType<
    DiscountPercentageOnceForeverDurationBase$Outbound,
    z.ZodTypeDef,
    DiscountPercentageOnceForeverDurationBase
  > = z.object({
    duration: DiscountDuration$outboundSchema,
    type: DiscountType$outboundSchema,
    basisPoints: z.number().int(),
    createdAt: z.date().transform(v => v.toISOString()),
    modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
    id: z.string(),
    metadata: z.record(
      z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
    ),
    name: z.string(),
    code: z.nullable(z.string()),
    startsAt: z.nullable(z.date().transform(v => v.toISOString())),
    endsAt: z.nullable(z.date().transform(v => v.toISOString())),
    maxRedemptions: z.nullable(z.number().int()),
    redemptionsCount: z.number().int(),
    organizationId: z.string(),
  }).transform((v) => {
    return remap$(v, {
      basisPoints: "basis_points",
      createdAt: "created_at",
      modifiedAt: "modified_at",
      startsAt: "starts_at",
      endsAt: "ends_at",
      maxRedemptions: "max_redemptions",
      redemptionsCount: "redemptions_count",
      organizationId: "organization_id",
    });
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountPercentageOnceForeverDurationBase$ {
  /** @deprecated use `DiscountPercentageOnceForeverDurationBase$inboundSchema` instead. */
  export const inboundSchema =
    DiscountPercentageOnceForeverDurationBase$inboundSchema;
  /** @deprecated use `DiscountPercentageOnceForeverDurationBase$outboundSchema` instead. */
  export const outboundSchema =
    DiscountPercentageOnceForeverDurationBase$outboundSchema;
  /** @deprecated use `DiscountPercentageOnceForeverDurationBase$Outbound` instead. */
  export type Outbound = DiscountPercentageOnceForeverDurationBase$Outbound;
}

export function discountPercentageOnceForeverDurationBaseToJSON(
  discountPercentageOnceForeverDurationBase:
    DiscountPercentageOnceForeverDurationBase,
): string {
  return JSON.stringify(
    DiscountPercentageOnceForeverDurationBase$outboundSchema.parse(
      discountPercentageOnceForeverDurationBase,
    ),
  );
}

export function discountPercentageOnceForeverDurationBaseFromJSON(
  jsonString: string,
): SafeParseResult<
  DiscountPercentageOnceForeverDurationBase,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      DiscountPercentageOnceForeverDurationBase$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'DiscountPercentageOnceForeverDurationBase' from JSON`,
  );
}
