/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountDuration,
  DiscountDuration$inboundSchema,
  DiscountDuration$outboundSchema,
} from "./discountduration.js";
import {
  DiscountProduct,
  DiscountProduct$inboundSchema,
  DiscountProduct$Outbound,
  DiscountProduct$outboundSchema,
} from "./discountproduct.js";
import {
  DiscountType,
  DiscountType$inboundSchema,
  DiscountType$outboundSchema,
} from "./discounttype.js";

export type DiscountFixedOnceForeverDurationMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * Schema for a fixed amount discount that is applied once or forever.
 */
export type DiscountFixedOnceForeverDuration = {
  duration: DiscountDuration;
  type: DiscountType;
  amount: number;
  currency: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Name of the discount. Will be displayed to the customer when the discount is applied.
   */
  name: string;
  /**
   * Code customers can use to apply the discount during checkout.
   */
  code: string | null;
  /**
   * Timestamp after which the discount is redeemable.
   */
  startsAt: Date | null;
  /**
   * Timestamp after which the discount is no longer redeemable.
   */
  endsAt: Date | null;
  /**
   * Maximum number of times the discount can be redeemed.
   */
  maxRedemptions: number | null;
  /**
   * Number of times the discount has been redeemed.
   */
  redemptionsCount: number;
  /**
   * The organization ID.
   */
  organizationId: string;
  products: Array<DiscountProduct>;
};

/** @internal */
export const DiscountFixedOnceForeverDurationMetadata$inboundSchema: z.ZodType<
  DiscountFixedOnceForeverDurationMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type DiscountFixedOnceForeverDurationMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const DiscountFixedOnceForeverDurationMetadata$outboundSchema: z.ZodType<
  DiscountFixedOnceForeverDurationMetadata$Outbound,
  z.ZodTypeDef,
  DiscountFixedOnceForeverDurationMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountFixedOnceForeverDurationMetadata$ {
  /** @deprecated use `DiscountFixedOnceForeverDurationMetadata$inboundSchema` instead. */
  export const inboundSchema =
    DiscountFixedOnceForeverDurationMetadata$inboundSchema;
  /** @deprecated use `DiscountFixedOnceForeverDurationMetadata$outboundSchema` instead. */
  export const outboundSchema =
    DiscountFixedOnceForeverDurationMetadata$outboundSchema;
  /** @deprecated use `DiscountFixedOnceForeverDurationMetadata$Outbound` instead. */
  export type Outbound = DiscountFixedOnceForeverDurationMetadata$Outbound;
}

export function discountFixedOnceForeverDurationMetadataToJSON(
  discountFixedOnceForeverDurationMetadata:
    DiscountFixedOnceForeverDurationMetadata,
): string {
  return JSON.stringify(
    DiscountFixedOnceForeverDurationMetadata$outboundSchema.parse(
      discountFixedOnceForeverDurationMetadata,
    ),
  );
}

export function discountFixedOnceForeverDurationMetadataFromJSON(
  jsonString: string,
): SafeParseResult<
  DiscountFixedOnceForeverDurationMetadata,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      DiscountFixedOnceForeverDurationMetadata$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'DiscountFixedOnceForeverDurationMetadata' from JSON`,
  );
}

/** @internal */
export const DiscountFixedOnceForeverDuration$inboundSchema: z.ZodType<
  DiscountFixedOnceForeverDuration,
  z.ZodTypeDef,
  unknown
> = z.object({
  duration: DiscountDuration$inboundSchema,
  type: DiscountType$inboundSchema,
  amount: z.number().int(),
  currency: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  name: z.string(),
  code: z.nullable(z.string()),
  starts_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  ends_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  max_redemptions: z.nullable(z.number().int()),
  redemptions_count: z.number().int(),
  organization_id: z.string(),
  products: z.array(DiscountProduct$inboundSchema),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "starts_at": "startsAt",
    "ends_at": "endsAt",
    "max_redemptions": "maxRedemptions",
    "redemptions_count": "redemptionsCount",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type DiscountFixedOnceForeverDuration$Outbound = {
  duration: string;
  type: string;
  amount: number;
  currency: string;
  created_at: string;
  modified_at: string | null;
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  name: string;
  code: string | null;
  starts_at: string | null;
  ends_at: string | null;
  max_redemptions: number | null;
  redemptions_count: number;
  organization_id: string;
  products: Array<DiscountProduct$Outbound>;
};

/** @internal */
export const DiscountFixedOnceForeverDuration$outboundSchema: z.ZodType<
  DiscountFixedOnceForeverDuration$Outbound,
  z.ZodTypeDef,
  DiscountFixedOnceForeverDuration
> = z.object({
  duration: DiscountDuration$outboundSchema,
  type: DiscountType$outboundSchema,
  amount: z.number().int(),
  currency: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  name: z.string(),
  code: z.nullable(z.string()),
  startsAt: z.nullable(z.date().transform(v => v.toISOString())),
  endsAt: z.nullable(z.date().transform(v => v.toISOString())),
  maxRedemptions: z.nullable(z.number().int()),
  redemptionsCount: z.number().int(),
  organizationId: z.string(),
  products: z.array(DiscountProduct$outboundSchema),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    startsAt: "starts_at",
    endsAt: "ends_at",
    maxRedemptions: "max_redemptions",
    redemptionsCount: "redemptions_count",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountFixedOnceForeverDuration$ {
  /** @deprecated use `DiscountFixedOnceForeverDuration$inboundSchema` instead. */
  export const inboundSchema = DiscountFixedOnceForeverDuration$inboundSchema;
  /** @deprecated use `DiscountFixedOnceForeverDuration$outboundSchema` instead. */
  export const outboundSchema = DiscountFixedOnceForeverDuration$outboundSchema;
  /** @deprecated use `DiscountFixedOnceForeverDuration$Outbound` instead. */
  export type Outbound = DiscountFixedOnceForeverDuration$Outbound;
}

export function discountFixedOnceForeverDurationToJSON(
  discountFixedOnceForeverDuration: DiscountFixedOnceForeverDuration,
): string {
  return JSON.stringify(
    DiscountFixedOnceForeverDuration$outboundSchema.parse(
      discountFixedOnceForeverDuration,
    ),
  );
}

export function discountFixedOnceForeverDurationFromJSON(
  jsonString: string,
): SafeParseResult<DiscountFixedOnceForeverDuration, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DiscountFixedOnceForeverDuration$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DiscountFixedOnceForeverDuration' from JSON`,
  );
}
