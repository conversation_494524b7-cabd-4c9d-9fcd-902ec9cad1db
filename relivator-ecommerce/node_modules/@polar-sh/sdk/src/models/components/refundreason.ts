/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const RefundReason = {
  Duplicate: "duplicate",
  Fraudulent: "fraudulent",
  CustomerRequest: "customer_request",
  ServiceDisruption: "service_disruption",
  SatisfactionGuarantee: "satisfaction_guarantee",
  Other: "other",
} as const;
export type RefundReason = ClosedEnum<typeof RefundReason>;

/** @internal */
export const RefundReason$inboundSchema: z.ZodNativeEnum<typeof RefundReason> =
  z.nativeEnum(RefundReason);

/** @internal */
export const RefundReason$outboundSchema: z.ZodNativeEnum<typeof RefundReason> =
  RefundReason$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace RefundReason$ {
  /** @deprecated use `RefundReason$inboundSchema` instead. */
  export const inboundSchema = RefundReason$inboundSchema;
  /** @deprecated use `RefundReason$outboundSchema` instead. */
  export const outboundSchema = RefundReason$outboundSchema;
}
