/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountDuration,
  DiscountDuration$inboundSchema,
  DiscountDuration$outboundSchema,
} from "./discountduration.js";
import {
  DiscountType,
  DiscountType$inboundSchema,
  DiscountType$outboundSchema,
} from "./discounttype.js";

/**
 * Schema for a fixed amount discount that is applied once or forever.
 */
export type CheckoutDiscountFixedOnceForeverDuration = {
  duration: DiscountDuration;
  type: DiscountType;
  amount: number;
  currency: string;
  /**
   * The ID of the object.
   */
  id: string;
  name: string;
  code: string | null;
};

/** @internal */
export const CheckoutDiscountFixedOnceForeverDuration$inboundSchema: z.ZodType<
  CheckoutDiscountFixedOnceForeverDuration,
  z.ZodTypeDef,
  unknown
> = z.object({
  duration: DiscountDuration$inboundSchema,
  type: DiscountType$inboundSchema,
  amount: z.number().int(),
  currency: z.string(),
  id: z.string(),
  name: z.string(),
  code: z.nullable(z.string()),
});

/** @internal */
export type CheckoutDiscountFixedOnceForeverDuration$Outbound = {
  duration: string;
  type: string;
  amount: number;
  currency: string;
  id: string;
  name: string;
  code: string | null;
};

/** @internal */
export const CheckoutDiscountFixedOnceForeverDuration$outboundSchema: z.ZodType<
  CheckoutDiscountFixedOnceForeverDuration$Outbound,
  z.ZodTypeDef,
  CheckoutDiscountFixedOnceForeverDuration
> = z.object({
  duration: DiscountDuration$outboundSchema,
  type: DiscountType$outboundSchema,
  amount: z.number().int(),
  currency: z.string(),
  id: z.string(),
  name: z.string(),
  code: z.nullable(z.string()),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutDiscountFixedOnceForeverDuration$ {
  /** @deprecated use `CheckoutDiscountFixedOnceForeverDuration$inboundSchema` instead. */
  export const inboundSchema =
    CheckoutDiscountFixedOnceForeverDuration$inboundSchema;
  /** @deprecated use `CheckoutDiscountFixedOnceForeverDuration$outboundSchema` instead. */
  export const outboundSchema =
    CheckoutDiscountFixedOnceForeverDuration$outboundSchema;
  /** @deprecated use `CheckoutDiscountFixedOnceForeverDuration$Outbound` instead. */
  export type Outbound = CheckoutDiscountFixedOnceForeverDuration$Outbound;
}

export function checkoutDiscountFixedOnceForeverDurationToJSON(
  checkoutDiscountFixedOnceForeverDuration:
    CheckoutDiscountFixedOnceForeverDuration,
): string {
  return JSON.stringify(
    CheckoutDiscountFixedOnceForeverDuration$outboundSchema.parse(
      checkoutDiscountFixedOnceForeverDuration,
    ),
  );
}

export function checkoutDiscountFixedOnceForeverDurationFromJSON(
  jsonString: string,
): SafeParseResult<
  CheckoutDiscountFixedOnceForeverDuration,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      CheckoutDiscountFixedOnceForeverDuration$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'CheckoutDiscountFixedOnceForeverDuration' from JSON`,
  );
}
