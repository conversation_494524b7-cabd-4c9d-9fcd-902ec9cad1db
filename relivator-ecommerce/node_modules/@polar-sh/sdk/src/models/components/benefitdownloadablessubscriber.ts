/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitDownloadablesSubscriberProperties,
  BenefitDownloadablesSubscriberProperties$inboundSchema,
  BenefitDownloadablesSubscriberProperties$Outbound,
  BenefitDownloadablesSubscriberProperties$outboundSchema,
} from "./benefitdownloadablessubscriberproperties.js";
import {
  Organization,
  Organization$inboundSchema,
  Organization$Outbound,
  Organization$outboundSchema,
} from "./organization.js";

export type BenefitDownloadablesSubscriberMetadata =
  | string
  | number
  | number
  | boolean;

export type BenefitDownloadablesSubscriber = {
  /**
   * The ID of the benefit.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  type?: "downloadables" | undefined;
  /**
   * The description of the benefit.
   */
  description: string;
  /**
   * Whether the benefit is selectable when creating a product.
   */
  selectable: boolean;
  /**
   * Whether the benefit is deletable.
   */
  deletable: boolean;
  /**
   * The ID of the organization owning the benefit.
   */
  organizationId: string;
  metadata: { [k: string]: string | number | number | boolean };
  organization: Organization;
  properties: BenefitDownloadablesSubscriberProperties;
};

/** @internal */
export const BenefitDownloadablesSubscriberMetadata$inboundSchema: z.ZodType<
  BenefitDownloadablesSubscriberMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitDownloadablesSubscriberMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitDownloadablesSubscriberMetadata$outboundSchema: z.ZodType<
  BenefitDownloadablesSubscriberMetadata$Outbound,
  z.ZodTypeDef,
  BenefitDownloadablesSubscriberMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDownloadablesSubscriberMetadata$ {
  /** @deprecated use `BenefitDownloadablesSubscriberMetadata$inboundSchema` instead. */
  export const inboundSchema =
    BenefitDownloadablesSubscriberMetadata$inboundSchema;
  /** @deprecated use `BenefitDownloadablesSubscriberMetadata$outboundSchema` instead. */
  export const outboundSchema =
    BenefitDownloadablesSubscriberMetadata$outboundSchema;
  /** @deprecated use `BenefitDownloadablesSubscriberMetadata$Outbound` instead. */
  export type Outbound = BenefitDownloadablesSubscriberMetadata$Outbound;
}

export function benefitDownloadablesSubscriberMetadataToJSON(
  benefitDownloadablesSubscriberMetadata:
    BenefitDownloadablesSubscriberMetadata,
): string {
  return JSON.stringify(
    BenefitDownloadablesSubscriberMetadata$outboundSchema.parse(
      benefitDownloadablesSubscriberMetadata,
    ),
  );
}

export function benefitDownloadablesSubscriberMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitDownloadablesSubscriberMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitDownloadablesSubscriberMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitDownloadablesSubscriberMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitDownloadablesSubscriber$inboundSchema: z.ZodType<
  BenefitDownloadablesSubscriber,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  type: z.literal("downloadables").optional(),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organization_id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  organization: Organization$inboundSchema,
  properties: BenefitDownloadablesSubscriberProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitDownloadablesSubscriber$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  type: "downloadables";
  description: string;
  selectable: boolean;
  deletable: boolean;
  organization_id: string;
  metadata: { [k: string]: string | number | number | boolean };
  organization: Organization$Outbound;
  properties: BenefitDownloadablesSubscriberProperties$Outbound;
};

/** @internal */
export const BenefitDownloadablesSubscriber$outboundSchema: z.ZodType<
  BenefitDownloadablesSubscriber$Outbound,
  z.ZodTypeDef,
  BenefitDownloadablesSubscriber
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  type: z.literal("downloadables").default("downloadables" as const),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organizationId: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  organization: Organization$outboundSchema,
  properties: BenefitDownloadablesSubscriberProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDownloadablesSubscriber$ {
  /** @deprecated use `BenefitDownloadablesSubscriber$inboundSchema` instead. */
  export const inboundSchema = BenefitDownloadablesSubscriber$inboundSchema;
  /** @deprecated use `BenefitDownloadablesSubscriber$outboundSchema` instead. */
  export const outboundSchema = BenefitDownloadablesSubscriber$outboundSchema;
  /** @deprecated use `BenefitDownloadablesSubscriber$Outbound` instead. */
  export type Outbound = BenefitDownloadablesSubscriber$Outbound;
}

export function benefitDownloadablesSubscriberToJSON(
  benefitDownloadablesSubscriber: BenefitDownloadablesSubscriber,
): string {
  return JSON.stringify(
    BenefitDownloadablesSubscriber$outboundSchema.parse(
      benefitDownloadablesSubscriber,
    ),
  );
}

export function benefitDownloadablesSubscriberFromJSON(
  jsonString: string,
): SafeParseResult<BenefitDownloadablesSubscriber, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitDownloadablesSubscriber$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitDownloadablesSubscriber' from JSON`,
  );
}
