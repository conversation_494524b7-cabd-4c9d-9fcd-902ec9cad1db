/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type BenefitGrantLicenseKeysProperties = {
  licenseKeyId?: string | undefined;
  displayKey?: string | undefined;
};

/** @internal */
export const BenefitGrantLicenseKeysProperties$inboundSchema: z.ZodType<
  BenefitGrantLicenseKeysProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  license_key_id: z.string().optional(),
  display_key: z.string().optional(),
}).transform((v) => {
  return remap$(v, {
    "license_key_id": "licenseKeyId",
    "display_key": "displayKey",
  });
});

/** @internal */
export type BenefitGrantLicenseKeysProperties$Outbound = {
  license_key_id?: string | undefined;
  display_key?: string | undefined;
};

/** @internal */
export const BenefitGrantLicenseKeysProperties$outboundSchema: z.ZodType<
  BenefitGrantLicenseKeysProperties$Outbound,
  z.ZodTypeDef,
  BenefitGrantLicenseKeysProperties
> = z.object({
  licenseKeyId: z.string().optional(),
  displayKey: z.string().optional(),
}).transform((v) => {
  return remap$(v, {
    licenseKeyId: "license_key_id",
    displayKey: "display_key",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitGrantLicenseKeysProperties$ {
  /** @deprecated use `BenefitGrantLicenseKeysProperties$inboundSchema` instead. */
  export const inboundSchema = BenefitGrantLicenseKeysProperties$inboundSchema;
  /** @deprecated use `BenefitGrantLicenseKeysProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitGrantLicenseKeysProperties$outboundSchema;
  /** @deprecated use `BenefitGrantLicenseKeysProperties$Outbound` instead. */
  export type Outbound = BenefitGrantLicenseKeysProperties$Outbound;
}

export function benefitGrantLicenseKeysPropertiesToJSON(
  benefitGrantLicenseKeysProperties: BenefitGrantLicenseKeysProperties,
): string {
  return JSON.stringify(
    BenefitGrantLicenseKeysProperties$outboundSchema.parse(
      benefitGrantLicenseKeysProperties,
    ),
  );
}

export function benefitGrantLicenseKeysPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<BenefitGrantLicenseKeysProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitGrantLicenseKeysProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitGrantLicenseKeysProperties' from JSON`,
  );
}
