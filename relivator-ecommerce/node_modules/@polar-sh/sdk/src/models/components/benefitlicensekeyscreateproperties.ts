/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitLicenseKeyActivationCreateProperties,
  BenefitLicenseKeyActivationCreateProperties$inboundSchema,
  BenefitLicenseKeyActivationCreateProperties$Outbound,
  BenefitLicenseKeyActivationCreateProperties$outboundSchema,
} from "./benefitlicensekeyactivationcreateproperties.js";
import {
  BenefitLicenseKeyExpirationProperties,
  BenefitLicenseKeyExpirationProperties$inboundSchema,
  BenefitLicenseKeyExpirationProperties$Outbound,
  BenefitLicenseKeyExpirationProperties$outboundSchema,
} from "./benefitlicensekeyexpirationproperties.js";

export type BenefitLicenseKeysCreateProperties = {
  prefix?: string | null | undefined;
  expires?: BenefitLicenseKeyExpirationProperties | null | undefined;
  activations?: BenefitLicenseKeyActivationCreateProperties | null | undefined;
  limitUsage?: number | null | undefined;
};

/** @internal */
export const BenefitLicenseKeysCreateProperties$inboundSchema: z.ZodType<
  BenefitLicenseKeysCreateProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  prefix: z.nullable(z.string()).optional(),
  expires: z.nullable(BenefitLicenseKeyExpirationProperties$inboundSchema)
    .optional(),
  activations: z.nullable(
    BenefitLicenseKeyActivationCreateProperties$inboundSchema,
  ).optional(),
  limit_usage: z.nullable(z.number().int()).optional(),
}).transform((v) => {
  return remap$(v, {
    "limit_usage": "limitUsage",
  });
});

/** @internal */
export type BenefitLicenseKeysCreateProperties$Outbound = {
  prefix?: string | null | undefined;
  expires?: BenefitLicenseKeyExpirationProperties$Outbound | null | undefined;
  activations?:
    | BenefitLicenseKeyActivationCreateProperties$Outbound
    | null
    | undefined;
  limit_usage?: number | null | undefined;
};

/** @internal */
export const BenefitLicenseKeysCreateProperties$outboundSchema: z.ZodType<
  BenefitLicenseKeysCreateProperties$Outbound,
  z.ZodTypeDef,
  BenefitLicenseKeysCreateProperties
> = z.object({
  prefix: z.nullable(z.string()).optional(),
  expires: z.nullable(BenefitLicenseKeyExpirationProperties$outboundSchema)
    .optional(),
  activations: z.nullable(
    BenefitLicenseKeyActivationCreateProperties$outboundSchema,
  ).optional(),
  limitUsage: z.nullable(z.number().int()).optional(),
}).transform((v) => {
  return remap$(v, {
    limitUsage: "limit_usage",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitLicenseKeysCreateProperties$ {
  /** @deprecated use `BenefitLicenseKeysCreateProperties$inboundSchema` instead. */
  export const inboundSchema = BenefitLicenseKeysCreateProperties$inboundSchema;
  /** @deprecated use `BenefitLicenseKeysCreateProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitLicenseKeysCreateProperties$outboundSchema;
  /** @deprecated use `BenefitLicenseKeysCreateProperties$Outbound` instead. */
  export type Outbound = BenefitLicenseKeysCreateProperties$Outbound;
}

export function benefitLicenseKeysCreatePropertiesToJSON(
  benefitLicenseKeysCreateProperties: BenefitLicenseKeysCreateProperties,
): string {
  return JSON.stringify(
    BenefitLicenseKeysCreateProperties$outboundSchema.parse(
      benefitLicenseKeysCreateProperties,
    ),
  );
}

export function benefitLicenseKeysCreatePropertiesFromJSON(
  jsonString: string,
): SafeParseResult<BenefitLicenseKeysCreateProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitLicenseKeysCreateProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitLicenseKeysCreateProperties' from JSON`,
  );
}
