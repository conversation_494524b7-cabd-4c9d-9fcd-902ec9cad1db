/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type CheckoutCustomerBillingAddressFields = {
  country: boolean;
  state: boolean;
  city: boolean;
  postalCode: boolean;
  line1: boolean;
  line2: boolean;
};

/** @internal */
export const CheckoutCustomerBillingAddressFields$inboundSchema: z.ZodType<
  CheckoutCustomerBillingAddressFields,
  z.ZodTypeDef,
  unknown
> = z.object({
  country: z.boolean(),
  state: z.boolean(),
  city: z.boolean(),
  postal_code: z.boolean(),
  line1: z.boolean(),
  line2: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    "postal_code": "postalCode",
  });
});

/** @internal */
export type CheckoutCustomerBillingAddressFields$Outbound = {
  country: boolean;
  state: boolean;
  city: boolean;
  postal_code: boolean;
  line1: boolean;
  line2: boolean;
};

/** @internal */
export const CheckoutCustomerBillingAddressFields$outboundSchema: z.ZodType<
  CheckoutCustomerBillingAddressFields$Outbound,
  z.ZodTypeDef,
  CheckoutCustomerBillingAddressFields
> = z.object({
  country: z.boolean(),
  state: z.boolean(),
  city: z.boolean(),
  postalCode: z.boolean(),
  line1: z.boolean(),
  line2: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    postalCode: "postal_code",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutCustomerBillingAddressFields$ {
  /** @deprecated use `CheckoutCustomerBillingAddressFields$inboundSchema` instead. */
  export const inboundSchema =
    CheckoutCustomerBillingAddressFields$inboundSchema;
  /** @deprecated use `CheckoutCustomerBillingAddressFields$outboundSchema` instead. */
  export const outboundSchema =
    CheckoutCustomerBillingAddressFields$outboundSchema;
  /** @deprecated use `CheckoutCustomerBillingAddressFields$Outbound` instead. */
  export type Outbound = CheckoutCustomerBillingAddressFields$Outbound;
}

export function checkoutCustomerBillingAddressFieldsToJSON(
  checkoutCustomerBillingAddressFields: CheckoutCustomerBillingAddressFields,
): string {
  return JSON.stringify(
    CheckoutCustomerBillingAddressFields$outboundSchema.parse(
      checkoutCustomerBillingAddressFields,
    ),
  );
}

export function checkoutCustomerBillingAddressFieldsFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutCustomerBillingAddressFields, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      CheckoutCustomerBillingAddressFields$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutCustomerBillingAddressFields' from JSON`,
  );
}
