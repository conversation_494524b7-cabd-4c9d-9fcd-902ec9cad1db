/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  LicenseKeyActivationBase,
  LicenseKeyActivationBase$inboundSchema,
  LicenseKeyActivationBase$Outbound,
  LicenseKeyActivationBase$outboundSchema,
} from "./licensekeyactivationbase.js";
import {
  LicenseKeyCustomer,
  LicenseKeyCustomer$inboundSchema,
  LicenseKeyCustomer$Outbound,
  LicenseKeyCustomer$outboundSchema,
} from "./licensekeycustomer.js";
import {
  LicenseKeyStatus,
  LicenseKeyStatus$inboundSchema,
  LicenseKeyStatus$outboundSchema,
} from "./licensekeystatus.js";

export type LicenseKeyWithActivations = {
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  organizationId: string;
  customerId: string;
  customer: LicenseKeyCustomer;
  /**
   * The benefit ID.
   */
  benefitId: string;
  key: string;
  displayKey: string;
  status: LicenseKeyStatus;
  limitActivations: number | null;
  usage: number;
  limitUsage: number | null;
  validations: number;
  lastValidatedAt: Date | null;
  expiresAt: Date | null;
  activations: Array<LicenseKeyActivationBase>;
};

/** @internal */
export const LicenseKeyWithActivations$inboundSchema: z.ZodType<
  LicenseKeyWithActivations,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  organization_id: z.string(),
  customer_id: z.string(),
  customer: LicenseKeyCustomer$inboundSchema,
  benefit_id: z.string(),
  key: z.string(),
  display_key: z.string(),
  status: LicenseKeyStatus$inboundSchema,
  limit_activations: z.nullable(z.number().int()),
  usage: z.number().int(),
  limit_usage: z.nullable(z.number().int()),
  validations: z.number().int(),
  last_validated_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  expires_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  activations: z.array(LicenseKeyActivationBase$inboundSchema),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
    "customer_id": "customerId",
    "benefit_id": "benefitId",
    "display_key": "displayKey",
    "limit_activations": "limitActivations",
    "limit_usage": "limitUsage",
    "last_validated_at": "lastValidatedAt",
    "expires_at": "expiresAt",
  });
});

/** @internal */
export type LicenseKeyWithActivations$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  organization_id: string;
  customer_id: string;
  customer: LicenseKeyCustomer$Outbound;
  benefit_id: string;
  key: string;
  display_key: string;
  status: string;
  limit_activations: number | null;
  usage: number;
  limit_usage: number | null;
  validations: number;
  last_validated_at: string | null;
  expires_at: string | null;
  activations: Array<LicenseKeyActivationBase$Outbound>;
};

/** @internal */
export const LicenseKeyWithActivations$outboundSchema: z.ZodType<
  LicenseKeyWithActivations$Outbound,
  z.ZodTypeDef,
  LicenseKeyWithActivations
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  organizationId: z.string(),
  customerId: z.string(),
  customer: LicenseKeyCustomer$outboundSchema,
  benefitId: z.string(),
  key: z.string(),
  displayKey: z.string(),
  status: LicenseKeyStatus$outboundSchema,
  limitActivations: z.nullable(z.number().int()),
  usage: z.number().int(),
  limitUsage: z.nullable(z.number().int()),
  validations: z.number().int(),
  lastValidatedAt: z.nullable(z.date().transform(v => v.toISOString())),
  expiresAt: z.nullable(z.date().transform(v => v.toISOString())),
  activations: z.array(LicenseKeyActivationBase$outboundSchema),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
    customerId: "customer_id",
    benefitId: "benefit_id",
    displayKey: "display_key",
    limitActivations: "limit_activations",
    limitUsage: "limit_usage",
    lastValidatedAt: "last_validated_at",
    expiresAt: "expires_at",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LicenseKeyWithActivations$ {
  /** @deprecated use `LicenseKeyWithActivations$inboundSchema` instead. */
  export const inboundSchema = LicenseKeyWithActivations$inboundSchema;
  /** @deprecated use `LicenseKeyWithActivations$outboundSchema` instead. */
  export const outboundSchema = LicenseKeyWithActivations$outboundSchema;
  /** @deprecated use `LicenseKeyWithActivations$Outbound` instead. */
  export type Outbound = LicenseKeyWithActivations$Outbound;
}

export function licenseKeyWithActivationsToJSON(
  licenseKeyWithActivations: LicenseKeyWithActivations,
): string {
  return JSON.stringify(
    LicenseKeyWithActivations$outboundSchema.parse(licenseKeyWithActivations),
  );
}

export function licenseKeyWithActivationsFromJSON(
  jsonString: string,
): SafeParseResult<LicenseKeyWithActivations, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LicenseKeyWithActivations$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LicenseKeyWithActivations' from JSON`,
  );
}
