/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitPublic,
  BenefitPublic$inboundSchema,
  BenefitPublic$Outbound,
  BenefitPublic$outboundSchema,
} from "./benefitpublic.js";
import {
  LegacyRecurringProductPrice,
  LegacyRecurringProductPrice$inboundSchema,
  LegacyRecurringProductPrice$Outbound,
  LegacyRecurringProductPrice$outboundSchema,
} from "./legacyrecurringproductprice.js";
import {
  ProductMediaFileRead,
  ProductMediaFileRead$inboundSchema,
  ProductMediaFileRead$Outbound,
  ProductMediaFileRead$outboundSchema,
} from "./productmediafileread.js";
import {
  ProductPrice,
  ProductPrice$inboundSchema,
  ProductPrice$Outbound,
  ProductPrice$outboundSchema,
} from "./productprice.js";
import {
  SubscriptionRecurringInterval,
  SubscriptionRecurringInterval$inboundSchema,
  SubscriptionRecurringInterval$outboundSchema,
} from "./subscriptionrecurringinterval.js";

export type CheckoutProductPrices = LegacyRecurringProductPrice | ProductPrice;

/**
 * Product data for a checkout session.
 */
export type CheckoutProduct = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the product.
   */
  id: string;
  /**
   * The name of the product.
   */
  name: string;
  /**
   * The description of the product.
   */
  description: string | null;
  /**
   * The recurring interval of the product. If `None`, the product is a one-time purchase.
   */
  recurringInterval: SubscriptionRecurringInterval | null;
  /**
   * Whether the product is a subscription.
   */
  isRecurring: boolean;
  /**
   * Whether the product is archived and no longer available.
   */
  isArchived: boolean;
  /**
   * The ID of the organization owning the product.
   */
  organizationId: string;
  /**
   * List of prices for this product.
   */
  prices: Array<LegacyRecurringProductPrice | ProductPrice>;
  /**
   * List of benefits granted by the product.
   */
  benefits: Array<BenefitPublic>;
  /**
   * List of medias associated to the product.
   */
  medias: Array<ProductMediaFileRead>;
};

/** @internal */
export const CheckoutProductPrices$inboundSchema: z.ZodType<
  CheckoutProductPrices,
  z.ZodTypeDef,
  unknown
> = z.union([
  LegacyRecurringProductPrice$inboundSchema,
  ProductPrice$inboundSchema,
]);

/** @internal */
export type CheckoutProductPrices$Outbound =
  | LegacyRecurringProductPrice$Outbound
  | ProductPrice$Outbound;

/** @internal */
export const CheckoutProductPrices$outboundSchema: z.ZodType<
  CheckoutProductPrices$Outbound,
  z.ZodTypeDef,
  CheckoutProductPrices
> = z.union([
  LegacyRecurringProductPrice$outboundSchema,
  ProductPrice$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutProductPrices$ {
  /** @deprecated use `CheckoutProductPrices$inboundSchema` instead. */
  export const inboundSchema = CheckoutProductPrices$inboundSchema;
  /** @deprecated use `CheckoutProductPrices$outboundSchema` instead. */
  export const outboundSchema = CheckoutProductPrices$outboundSchema;
  /** @deprecated use `CheckoutProductPrices$Outbound` instead. */
  export type Outbound = CheckoutProductPrices$Outbound;
}

export function checkoutProductPricesToJSON(
  checkoutProductPrices: CheckoutProductPrices,
): string {
  return JSON.stringify(
    CheckoutProductPrices$outboundSchema.parse(checkoutProductPrices),
  );
}

export function checkoutProductPricesFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutProductPrices, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutProductPrices$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutProductPrices' from JSON`,
  );
}

/** @internal */
export const CheckoutProduct$inboundSchema: z.ZodType<
  CheckoutProduct,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  name: z.string(),
  description: z.nullable(z.string()),
  recurring_interval: z.nullable(SubscriptionRecurringInterval$inboundSchema),
  is_recurring: z.boolean(),
  is_archived: z.boolean(),
  organization_id: z.string(),
  prices: z.array(
    z.union([
      LegacyRecurringProductPrice$inboundSchema,
      ProductPrice$inboundSchema,
    ]),
  ),
  benefits: z.array(BenefitPublic$inboundSchema),
  medias: z.array(ProductMediaFileRead$inboundSchema),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "recurring_interval": "recurringInterval",
    "is_recurring": "isRecurring",
    "is_archived": "isArchived",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type CheckoutProduct$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  name: string;
  description: string | null;
  recurring_interval: string | null;
  is_recurring: boolean;
  is_archived: boolean;
  organization_id: string;
  prices: Array<LegacyRecurringProductPrice$Outbound | ProductPrice$Outbound>;
  benefits: Array<BenefitPublic$Outbound>;
  medias: Array<ProductMediaFileRead$Outbound>;
};

/** @internal */
export const CheckoutProduct$outboundSchema: z.ZodType<
  CheckoutProduct$Outbound,
  z.ZodTypeDef,
  CheckoutProduct
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  name: z.string(),
  description: z.nullable(z.string()),
  recurringInterval: z.nullable(SubscriptionRecurringInterval$outboundSchema),
  isRecurring: z.boolean(),
  isArchived: z.boolean(),
  organizationId: z.string(),
  prices: z.array(
    z.union([
      LegacyRecurringProductPrice$outboundSchema,
      ProductPrice$outboundSchema,
    ]),
  ),
  benefits: z.array(BenefitPublic$outboundSchema),
  medias: z.array(ProductMediaFileRead$outboundSchema),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    recurringInterval: "recurring_interval",
    isRecurring: "is_recurring",
    isArchived: "is_archived",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutProduct$ {
  /** @deprecated use `CheckoutProduct$inboundSchema` instead. */
  export const inboundSchema = CheckoutProduct$inboundSchema;
  /** @deprecated use `CheckoutProduct$outboundSchema` instead. */
  export const outboundSchema = CheckoutProduct$outboundSchema;
  /** @deprecated use `CheckoutProduct$Outbound` instead. */
  export type Outbound = CheckoutProduct$Outbound;
}

export function checkoutProductToJSON(
  checkoutProduct: CheckoutProduct,
): string {
  return JSON.stringify(CheckoutProduct$outboundSchema.parse(checkoutProduct));
}

export function checkoutProductFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutProduct, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutProduct$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutProduct' from JSON`,
  );
}
