/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const DiscountDuration = {
  Once: "once",
  Forever: "forever",
  Repeating: "repeating",
} as const;
export type DiscountDuration = ClosedEnum<typeof DiscountDuration>;

/** @internal */
export const DiscountDuration$inboundSchema: z.ZodNativeEnum<
  typeof DiscountDuration
> = z.nativeEnum(DiscountDuration);

/** @internal */
export const DiscountDuration$outboundSchema: z.ZodNativeEnum<
  typeof DiscountDuration
> = DiscountDuration$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountDuration$ {
  /** @deprecated use `DiscountDuration$inboundSchema` instead. */
  export const inboundSchema = DiscountDuration$inboundSchema;
  /** @deprecated use `DiscountDuration$outboundSchema` instead. */
  export const outboundSchema = DiscountDuration$outboundSchema;
}
