/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const Scope = {
  Openid: "openid",
  Profile: "profile",
  Email: "email",
  UserRead: "user:read",
  Admin: "admin",
  WebDefault: "web_default",
  OrganizationsRead: "organizations:read",
  OrganizationsWrite: "organizations:write",
  CustomFieldsRead: "custom_fields:read",
  CustomFieldsWrite: "custom_fields:write",
  DiscountsRead: "discounts:read",
  DiscountsWrite: "discounts:write",
  CheckoutLinksRead: "checkout_links:read",
  CheckoutLinksWrite: "checkout_links:write",
  CheckoutsRead: "checkouts:read",
  CheckoutsWrite: "checkouts:write",
  ProductsRead: "products:read",
  ProductsWrite: "products:write",
  BenefitsRead: "benefits:read",
  BenefitsWrite: "benefits:write",
  EventsRead: "events:read",
  EventsWrite: "events:write",
  MetersRead: "meters:read",
  MetersWrite: "meters:write",
  FilesRead: "files:read",
  FilesWrite: "files:write",
  SubscriptionsRead: "subscriptions:read",
  SubscriptionsWrite: "subscriptions:write",
  CustomersRead: "customers:read",
  CustomersWrite: "customers:write",
  CustomerMetersRead: "customer_meters:read",
  CustomerSessionsWrite: "customer_sessions:write",
  OrdersRead: "orders:read",
  RefundsRead: "refunds:read",
  RefundsWrite: "refunds:write",
  PaymentsRead: "payments:read",
  MetricsRead: "metrics:read",
  WebhooksRead: "webhooks:read",
  WebhooksWrite: "webhooks:write",
  ExternalOrganizationsRead: "external_organizations:read",
  LicenseKeysRead: "license_keys:read",
  LicenseKeysWrite: "license_keys:write",
  RepositoriesRead: "repositories:read",
  RepositoriesWrite: "repositories:write",
  IssuesRead: "issues:read",
  IssuesWrite: "issues:write",
  CustomerPortalRead: "customer_portal:read",
  CustomerPortalWrite: "customer_portal:write",
  NotificationRecipientsRead: "notification_recipients:read",
  NotificationRecipientsWrite: "notification_recipients:write",
} as const;
export type Scope = ClosedEnum<typeof Scope>;

/** @internal */
export const Scope$inboundSchema: z.ZodNativeEnum<typeof Scope> = z.nativeEnum(
  Scope,
);

/** @internal */
export const Scope$outboundSchema: z.ZodNativeEnum<typeof Scope> =
  Scope$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Scope$ {
  /** @deprecated use `Scope$inboundSchema` instead. */
  export const inboundSchema = Scope$inboundSchema;
  /** @deprecated use `Scope$outboundSchema` instead. */
  export const outboundSchema = Scope$outboundSchema;
}
