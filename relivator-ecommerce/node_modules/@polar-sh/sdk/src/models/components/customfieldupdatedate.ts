/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomFieldDateProperties,
  CustomFieldDateProperties$inboundSchema,
  CustomFieldDateProperties$Outbound,
  CustomFieldDateProperties$outboundSchema,
} from "./customfielddateproperties.js";

export type CustomFieldUpdateDateMetadata = string | number | number | boolean;

/**
 * Schema to update a custom field of type date.
 */
export type CustomFieldUpdateDate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  name?: string | null | undefined;
  slug?: string | null | undefined;
  type?: "date" | undefined;
  properties?: CustomFieldDateProperties | null | undefined;
};

/** @internal */
export const CustomFieldUpdateDateMetadata$inboundSchema: z.ZodType<
  CustomFieldUpdateDateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomFieldUpdateDateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomFieldUpdateDateMetadata$outboundSchema: z.ZodType<
  CustomFieldUpdateDateMetadata$Outbound,
  z.ZodTypeDef,
  CustomFieldUpdateDateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldUpdateDateMetadata$ {
  /** @deprecated use `CustomFieldUpdateDateMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomFieldUpdateDateMetadata$inboundSchema;
  /** @deprecated use `CustomFieldUpdateDateMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomFieldUpdateDateMetadata$outboundSchema;
  /** @deprecated use `CustomFieldUpdateDateMetadata$Outbound` instead. */
  export type Outbound = CustomFieldUpdateDateMetadata$Outbound;
}

export function customFieldUpdateDateMetadataToJSON(
  customFieldUpdateDateMetadata: CustomFieldUpdateDateMetadata,
): string {
  return JSON.stringify(
    CustomFieldUpdateDateMetadata$outboundSchema.parse(
      customFieldUpdateDateMetadata,
    ),
  );
}

export function customFieldUpdateDateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldUpdateDateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldUpdateDateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldUpdateDateMetadata' from JSON`,
  );
}

/** @internal */
export const CustomFieldUpdateDate$inboundSchema: z.ZodType<
  CustomFieldUpdateDate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.nullable(z.string()).optional(),
  slug: z.nullable(z.string()).optional(),
  type: z.literal("date").optional(),
  properties: z.nullable(CustomFieldDateProperties$inboundSchema).optional(),
});

/** @internal */
export type CustomFieldUpdateDate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  name?: string | null | undefined;
  slug?: string | null | undefined;
  type: "date";
  properties?: CustomFieldDateProperties$Outbound | null | undefined;
};

/** @internal */
export const CustomFieldUpdateDate$outboundSchema: z.ZodType<
  CustomFieldUpdateDate$Outbound,
  z.ZodTypeDef,
  CustomFieldUpdateDate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.nullable(z.string()).optional(),
  slug: z.nullable(z.string()).optional(),
  type: z.literal("date").default("date" as const),
  properties: z.nullable(CustomFieldDateProperties$outboundSchema).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldUpdateDate$ {
  /** @deprecated use `CustomFieldUpdateDate$inboundSchema` instead. */
  export const inboundSchema = CustomFieldUpdateDate$inboundSchema;
  /** @deprecated use `CustomFieldUpdateDate$outboundSchema` instead. */
  export const outboundSchema = CustomFieldUpdateDate$outboundSchema;
  /** @deprecated use `CustomFieldUpdateDate$Outbound` instead. */
  export type Outbound = CustomFieldUpdateDate$Outbound;
}

export function customFieldUpdateDateToJSON(
  customFieldUpdateDate: CustomFieldUpdateDate,
): string {
  return JSON.stringify(
    CustomFieldUpdateDate$outboundSchema.parse(customFieldUpdateDate),
  );
}

export function customFieldUpdateDateFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldUpdateDate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldUpdateDate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldUpdateDate' from JSON`,
  );
}
