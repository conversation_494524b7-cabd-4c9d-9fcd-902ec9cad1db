/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const OrderStatus = {
  Pending: "pending",
  Paid: "paid",
  Refunded: "refunded",
  PartiallyRefunded: "partially_refunded",
} as const;
export type OrderStatus = ClosedEnum<typeof OrderStatus>;

/** @internal */
export const OrderStatus$inboundSchema: z.ZodNativeEnum<typeof OrderStatus> = z
  .nativeEnum(OrderStatus);

/** @internal */
export const OrderStatus$outboundSchema: z.ZodNativeEnum<typeof OrderStatus> =
  OrderStatus$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrderStatus$ {
  /** @deprecated use `OrderStatus$inboundSchema` instead. */
  export const inboundSchema = OrderStatus$inboundSchema;
  /** @deprecated use `OrderStatus$outboundSchema` instead. */
  export const outboundSchema = OrderStatus$outboundSchema;
}
