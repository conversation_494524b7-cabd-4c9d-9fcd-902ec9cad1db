/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod = {
  ClientSecretBasic: "client_secret_basic",
  ClientSecretPost: "client_secret_post",
  None: "none",
} as const;
export type OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod = ClosedEnum<
  typeof OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod
>;

export const OAuth2ClientConfigurationUpdateGrantTypes = {
  AuthorizationCode: "authorization_code",
  RefreshToken: "refresh_token",
} as const;
export type OAuth2ClientConfigurationUpdateGrantTypes = ClosedEnum<
  typeof OAuth2ClientConfigurationUpdateGrantTypes
>;

export type OAuth2ClientConfigurationUpdate = {
  redirectUris: Array<string>;
  tokenEndpointAuthMethod?:
    | OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod
    | undefined;
  grantTypes?: Array<OAuth2ClientConfigurationUpdateGrantTypes> | undefined;
  responseTypes?: Array<string> | undefined;
  scope?: string | undefined;
  clientName: string;
  clientUri?: string | null | undefined;
  logoUri?: string | null | undefined;
  tosUri?: string | null | undefined;
  policyUri?: string | null | undefined;
  clientId: string;
};

/** @internal */
export const OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod$inboundSchema:
  z.ZodNativeEnum<
    typeof OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod
  > = z.nativeEnum(OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod);

/** @internal */
export const OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod$outboundSchema:
  z.ZodNativeEnum<
    typeof OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod
  > = OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod$ {
  /** @deprecated use `OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod$inboundSchema` instead. */
  export const inboundSchema =
    OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod$inboundSchema;
  /** @deprecated use `OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod$outboundSchema` instead. */
  export const outboundSchema =
    OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod$outboundSchema;
}

/** @internal */
export const OAuth2ClientConfigurationUpdateGrantTypes$inboundSchema:
  z.ZodNativeEnum<typeof OAuth2ClientConfigurationUpdateGrantTypes> = z
    .nativeEnum(OAuth2ClientConfigurationUpdateGrantTypes);

/** @internal */
export const OAuth2ClientConfigurationUpdateGrantTypes$outboundSchema:
  z.ZodNativeEnum<typeof OAuth2ClientConfigurationUpdateGrantTypes> =
    OAuth2ClientConfigurationUpdateGrantTypes$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OAuth2ClientConfigurationUpdateGrantTypes$ {
  /** @deprecated use `OAuth2ClientConfigurationUpdateGrantTypes$inboundSchema` instead. */
  export const inboundSchema =
    OAuth2ClientConfigurationUpdateGrantTypes$inboundSchema;
  /** @deprecated use `OAuth2ClientConfigurationUpdateGrantTypes$outboundSchema` instead. */
  export const outboundSchema =
    OAuth2ClientConfigurationUpdateGrantTypes$outboundSchema;
}

/** @internal */
export const OAuth2ClientConfigurationUpdate$inboundSchema: z.ZodType<
  OAuth2ClientConfigurationUpdate,
  z.ZodTypeDef,
  unknown
> = z.object({
  redirect_uris: z.array(z.string()),
  token_endpoint_auth_method:
    OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod$inboundSchema
      .default("client_secret_post"),
  grant_types: z.array(OAuth2ClientConfigurationUpdateGrantTypes$inboundSchema)
    .optional(),
  response_types: z.array(z.string()).optional(),
  scope: z.string().default(
    "openid profile email user:read organizations:read organizations:write custom_fields:read custom_fields:write discounts:read discounts:write checkout_links:read checkout_links:write checkouts:read checkouts:write products:read products:write benefits:read benefits:write events:read events:write meters:read meters:write files:read files:write subscriptions:read subscriptions:write customers:read customers:write customer_meters:read customer_sessions:write orders:read refunds:read refunds:write payments:read metrics:read webhooks:read webhooks:write external_organizations:read license_keys:read license_keys:write repositories:read repositories:write issues:read issues:write customer_portal:read customer_portal:write notification_recipients:read notification_recipients:write",
  ),
  client_name: z.string(),
  client_uri: z.nullable(z.string()).optional(),
  logo_uri: z.nullable(z.string()).optional(),
  tos_uri: z.nullable(z.string()).optional(),
  policy_uri: z.nullable(z.string()).optional(),
  client_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "redirect_uris": "redirectUris",
    "token_endpoint_auth_method": "tokenEndpointAuthMethod",
    "grant_types": "grantTypes",
    "response_types": "responseTypes",
    "client_name": "clientName",
    "client_uri": "clientUri",
    "logo_uri": "logoUri",
    "tos_uri": "tosUri",
    "policy_uri": "policyUri",
    "client_id": "clientId",
  });
});

/** @internal */
export type OAuth2ClientConfigurationUpdate$Outbound = {
  redirect_uris: Array<string>;
  token_endpoint_auth_method: string;
  grant_types?: Array<string> | undefined;
  response_types?: Array<string> | undefined;
  scope: string;
  client_name: string;
  client_uri?: string | null | undefined;
  logo_uri?: string | null | undefined;
  tos_uri?: string | null | undefined;
  policy_uri?: string | null | undefined;
  client_id: string;
};

/** @internal */
export const OAuth2ClientConfigurationUpdate$outboundSchema: z.ZodType<
  OAuth2ClientConfigurationUpdate$Outbound,
  z.ZodTypeDef,
  OAuth2ClientConfigurationUpdate
> = z.object({
  redirectUris: z.array(z.string()),
  tokenEndpointAuthMethod:
    OAuth2ClientConfigurationUpdateTokenEndpointAuthMethod$outboundSchema
      .default("client_secret_post"),
  grantTypes: z.array(OAuth2ClientConfigurationUpdateGrantTypes$outboundSchema)
    .optional(),
  responseTypes: z.array(z.string()).optional(),
  scope: z.string().default(
    "openid profile email user:read organizations:read organizations:write custom_fields:read custom_fields:write discounts:read discounts:write checkout_links:read checkout_links:write checkouts:read checkouts:write products:read products:write benefits:read benefits:write events:read events:write meters:read meters:write files:read files:write subscriptions:read subscriptions:write customers:read customers:write customer_meters:read customer_sessions:write orders:read refunds:read refunds:write payments:read metrics:read webhooks:read webhooks:write external_organizations:read license_keys:read license_keys:write repositories:read repositories:write issues:read issues:write customer_portal:read customer_portal:write notification_recipients:read notification_recipients:write",
  ),
  clientName: z.string(),
  clientUri: z.nullable(z.string()).optional(),
  logoUri: z.nullable(z.string()).optional(),
  tosUri: z.nullable(z.string()).optional(),
  policyUri: z.nullable(z.string()).optional(),
  clientId: z.string(),
}).transform((v) => {
  return remap$(v, {
    redirectUris: "redirect_uris",
    tokenEndpointAuthMethod: "token_endpoint_auth_method",
    grantTypes: "grant_types",
    responseTypes: "response_types",
    clientName: "client_name",
    clientUri: "client_uri",
    logoUri: "logo_uri",
    tosUri: "tos_uri",
    policyUri: "policy_uri",
    clientId: "client_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OAuth2ClientConfigurationUpdate$ {
  /** @deprecated use `OAuth2ClientConfigurationUpdate$inboundSchema` instead. */
  export const inboundSchema = OAuth2ClientConfigurationUpdate$inboundSchema;
  /** @deprecated use `OAuth2ClientConfigurationUpdate$outboundSchema` instead. */
  export const outboundSchema = OAuth2ClientConfigurationUpdate$outboundSchema;
  /** @deprecated use `OAuth2ClientConfigurationUpdate$Outbound` instead. */
  export type Outbound = OAuth2ClientConfigurationUpdate$Outbound;
}

export function oAuth2ClientConfigurationUpdateToJSON(
  oAuth2ClientConfigurationUpdate: OAuth2ClientConfigurationUpdate,
): string {
  return JSON.stringify(
    OAuth2ClientConfigurationUpdate$outboundSchema.parse(
      oAuth2ClientConfigurationUpdate,
    ),
  );
}

export function oAuth2ClientConfigurationUpdateFromJSON(
  jsonString: string,
): SafeParseResult<OAuth2ClientConfigurationUpdate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OAuth2ClientConfigurationUpdate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OAuth2ClientConfigurationUpdate' from JSON`,
  );
}
