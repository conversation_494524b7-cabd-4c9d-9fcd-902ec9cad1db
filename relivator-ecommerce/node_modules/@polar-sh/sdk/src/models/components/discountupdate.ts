/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountDuration,
  DiscountDuration$inboundSchema,
  DiscountDuration$outboundSchema,
} from "./discountduration.js";
import {
  DiscountType,
  DiscountType$inboundSchema,
  DiscountType$outboundSchema,
} from "./discounttype.js";

export type DiscountUpdateMetadata = string | number | number | boolean;

/**
 * Schema to update a discount.
 */
export type DiscountUpdate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  name?: string | null | undefined;
  code?: string | null | undefined;
  startsAt?: Date | null | undefined;
  endsAt?: Date | null | undefined;
  maxRedemptions?: number | null | undefined;
  duration?: DiscountDuration | null | undefined;
  durationInMonths?: number | null | undefined;
  type?: DiscountType | null | undefined;
  amount?: number | null | undefined;
  currency?: string | null | undefined;
  basisPoints?: number | null | undefined;
  products?: Array<string> | null | undefined;
};

/** @internal */
export const DiscountUpdateMetadata$inboundSchema: z.ZodType<
  DiscountUpdateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type DiscountUpdateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const DiscountUpdateMetadata$outboundSchema: z.ZodType<
  DiscountUpdateMetadata$Outbound,
  z.ZodTypeDef,
  DiscountUpdateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountUpdateMetadata$ {
  /** @deprecated use `DiscountUpdateMetadata$inboundSchema` instead. */
  export const inboundSchema = DiscountUpdateMetadata$inboundSchema;
  /** @deprecated use `DiscountUpdateMetadata$outboundSchema` instead. */
  export const outboundSchema = DiscountUpdateMetadata$outboundSchema;
  /** @deprecated use `DiscountUpdateMetadata$Outbound` instead. */
  export type Outbound = DiscountUpdateMetadata$Outbound;
}

export function discountUpdateMetadataToJSON(
  discountUpdateMetadata: DiscountUpdateMetadata,
): string {
  return JSON.stringify(
    DiscountUpdateMetadata$outboundSchema.parse(discountUpdateMetadata),
  );
}

export function discountUpdateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<DiscountUpdateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DiscountUpdateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DiscountUpdateMetadata' from JSON`,
  );
}

/** @internal */
export const DiscountUpdate$inboundSchema: z.ZodType<
  DiscountUpdate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.nullable(z.string()).optional(),
  code: z.nullable(z.string()).optional(),
  starts_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  ends_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
  max_redemptions: z.nullable(z.number().int()).optional(),
  duration: z.nullable(DiscountDuration$inboundSchema).optional(),
  duration_in_months: z.nullable(z.number().int()).optional(),
  type: z.nullable(DiscountType$inboundSchema).optional(),
  amount: z.nullable(z.number().int()).optional(),
  currency: z.nullable(z.string().default("usd")),
  basis_points: z.nullable(z.number().int()).optional(),
  products: z.nullable(z.array(z.string())).optional(),
}).transform((v) => {
  return remap$(v, {
    "starts_at": "startsAt",
    "ends_at": "endsAt",
    "max_redemptions": "maxRedemptions",
    "duration_in_months": "durationInMonths",
    "basis_points": "basisPoints",
  });
});

/** @internal */
export type DiscountUpdate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  name?: string | null | undefined;
  code?: string | null | undefined;
  starts_at?: string | null | undefined;
  ends_at?: string | null | undefined;
  max_redemptions?: number | null | undefined;
  duration?: string | null | undefined;
  duration_in_months?: number | null | undefined;
  type?: string | null | undefined;
  amount?: number | null | undefined;
  currency: string | null;
  basis_points?: number | null | undefined;
  products?: Array<string> | null | undefined;
};

/** @internal */
export const DiscountUpdate$outboundSchema: z.ZodType<
  DiscountUpdate$Outbound,
  z.ZodTypeDef,
  DiscountUpdate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  name: z.nullable(z.string()).optional(),
  code: z.nullable(z.string()).optional(),
  startsAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
  endsAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
  maxRedemptions: z.nullable(z.number().int()).optional(),
  duration: z.nullable(DiscountDuration$outboundSchema).optional(),
  durationInMonths: z.nullable(z.number().int()).optional(),
  type: z.nullable(DiscountType$outboundSchema).optional(),
  amount: z.nullable(z.number().int()).optional(),
  currency: z.nullable(z.string().default("usd")),
  basisPoints: z.nullable(z.number().int()).optional(),
  products: z.nullable(z.array(z.string())).optional(),
}).transform((v) => {
  return remap$(v, {
    startsAt: "starts_at",
    endsAt: "ends_at",
    maxRedemptions: "max_redemptions",
    durationInMonths: "duration_in_months",
    basisPoints: "basis_points",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountUpdate$ {
  /** @deprecated use `DiscountUpdate$inboundSchema` instead. */
  export const inboundSchema = DiscountUpdate$inboundSchema;
  /** @deprecated use `DiscountUpdate$outboundSchema` instead. */
  export const outboundSchema = DiscountUpdate$outboundSchema;
  /** @deprecated use `DiscountUpdate$Outbound` instead. */
  export type Outbound = DiscountUpdate$Outbound;
}

export function discountUpdateToJSON(discountUpdate: DiscountUpdate): string {
  return JSON.stringify(DiscountUpdate$outboundSchema.parse(discountUpdate));
}

export function discountUpdateFromJSON(
  jsonString: string,
): SafeParseResult<DiscountUpdate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DiscountUpdate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DiscountUpdate' from JSON`,
  );
}
