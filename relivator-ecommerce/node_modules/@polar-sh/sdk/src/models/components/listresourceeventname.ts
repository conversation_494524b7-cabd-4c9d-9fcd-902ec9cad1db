/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  EventName,
  EventName$inboundSchema,
  EventName$Outbound,
  EventName$outboundSchema,
} from "./eventname.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";

export type ListResourceEventName = {
  items: Array<EventName>;
  pagination: Pagination;
};

/** @internal */
export const ListResourceEventName$inboundSchema: z.ZodType<
  ListResourceEventName,
  z.ZodTypeDef,
  unknown
> = z.object({
  items: z.array(EventName$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ListResourceEventName$Outbound = {
  items: Array<EventName$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResourceEventName$outboundSchema: z.ZodType<
  ListResourceEventName$Outbound,
  z.ZodTypeDef,
  ListResourceEventName
> = z.object({
  items: z.array(EventName$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResourceEventName$ {
  /** @deprecated use `ListResourceEventName$inboundSchema` instead. */
  export const inboundSchema = ListResourceEventName$inboundSchema;
  /** @deprecated use `ListResourceEventName$outboundSchema` instead. */
  export const outboundSchema = ListResourceEventName$outboundSchema;
  /** @deprecated use `ListResourceEventName$Outbound` instead. */
  export type Outbound = ListResourceEventName$Outbound;
}

export function listResourceEventNameToJSON(
  listResourceEventName: ListResourceEventName,
): string {
  return JSON.stringify(
    ListResourceEventName$outboundSchema.parse(listResourceEventName),
  );
}

export function listResourceEventNameFromJSON(
  jsonString: string,
): SafeParseResult<ListResourceEventName, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListResourceEventName$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListResourceEventName' from JSON`,
  );
}
