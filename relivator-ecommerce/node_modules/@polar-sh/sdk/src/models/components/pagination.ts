/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type Pagination = {
  totalCount: number;
  maxPage: number;
};

/** @internal */
export const Pagination$inboundSchema: z.ZodType<
  Pagination,
  z.ZodTypeDef,
  unknown
> = z.object({
  total_count: z.number().int(),
  max_page: z.number().int(),
}).transform((v) => {
  return remap$(v, {
    "total_count": "totalCount",
    "max_page": "maxPage",
  });
});

/** @internal */
export type Pagination$Outbound = {
  total_count: number;
  max_page: number;
};

/** @internal */
export const Pagination$outboundSchema: z.ZodType<
  Pagination$Outbound,
  z.ZodTypeDef,
  Pagination
> = z.object({
  totalCount: z.number().int(),
  maxPage: z.number().int(),
}).transform((v) => {
  return remap$(v, {
    totalCount: "total_count",
    maxPage: "max_page",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Pagination$ {
  /** @deprecated use `Pagination$inboundSchema` instead. */
  export const inboundSchema = Pagination$inboundSchema;
  /** @deprecated use `Pagination$outboundSchema` instead. */
  export const outboundSchema = Pagination$outboundSchema;
  /** @deprecated use `Pagination$Outbound` instead. */
  export type Outbound = Pagination$Outbound;
}

export function paginationToJSON(pagination: Pagination): string {
  return JSON.stringify(Pagination$outboundSchema.parse(pagination));
}

export function paginationFromJSON(
  jsonString: string,
): SafeParseResult<Pagination, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Pagination$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Pagination' from JSON`,
  );
}
