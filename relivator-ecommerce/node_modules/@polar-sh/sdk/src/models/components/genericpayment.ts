/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  PaymentProcessor,
  PaymentProcessor$inboundSchema,
  PaymentProcessor$outboundSchema,
} from "./paymentprocessor.js";
import {
  PaymentStatus,
  PaymentStatus$inboundSchema,
  PaymentStatus$outboundSchema,
} from "./paymentstatus.js";

/**
 * Schema of a payment with a generic payment method.
 */
export type GenericPayment = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  processor: PaymentProcessor;
  status: PaymentStatus;
  /**
   * The payment amount in cents.
   */
  amount: number;
  /**
   * The payment currency. Currently, only `usd` is supported.
   */
  currency: string;
  /**
   * The payment method used.
   */
  method: string;
  /**
   * Error code, if the payment was declined.
   */
  declineReason: string | null;
  /**
   * Human-reasable error message, if the payment was declined.
   */
  declineMessage: string | null;
  /**
   * The ID of the organization that owns the payment.
   */
  organizationId: string;
  /**
   * The ID of the checkout session associated with this payment.
   */
  checkoutId: string | null;
  /**
   * The ID of the order associated with this payment.
   */
  orderId: string | null;
};

/** @internal */
export const GenericPayment$inboundSchema: z.ZodType<
  GenericPayment,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  processor: PaymentProcessor$inboundSchema,
  status: PaymentStatus$inboundSchema,
  amount: z.number().int(),
  currency: z.string(),
  method: z.string(),
  decline_reason: z.nullable(z.string()),
  decline_message: z.nullable(z.string()),
  organization_id: z.string(),
  checkout_id: z.nullable(z.string()),
  order_id: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "decline_reason": "declineReason",
    "decline_message": "declineMessage",
    "organization_id": "organizationId",
    "checkout_id": "checkoutId",
    "order_id": "orderId",
  });
});

/** @internal */
export type GenericPayment$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  processor: string;
  status: string;
  amount: number;
  currency: string;
  method: string;
  decline_reason: string | null;
  decline_message: string | null;
  organization_id: string;
  checkout_id: string | null;
  order_id: string | null;
};

/** @internal */
export const GenericPayment$outboundSchema: z.ZodType<
  GenericPayment$Outbound,
  z.ZodTypeDef,
  GenericPayment
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  processor: PaymentProcessor$outboundSchema,
  status: PaymentStatus$outboundSchema,
  amount: z.number().int(),
  currency: z.string(),
  method: z.string(),
  declineReason: z.nullable(z.string()),
  declineMessage: z.nullable(z.string()),
  organizationId: z.string(),
  checkoutId: z.nullable(z.string()),
  orderId: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    declineReason: "decline_reason",
    declineMessage: "decline_message",
    organizationId: "organization_id",
    checkoutId: "checkout_id",
    orderId: "order_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace GenericPayment$ {
  /** @deprecated use `GenericPayment$inboundSchema` instead. */
  export const inboundSchema = GenericPayment$inboundSchema;
  /** @deprecated use `GenericPayment$outboundSchema` instead. */
  export const outboundSchema = GenericPayment$outboundSchema;
  /** @deprecated use `GenericPayment$Outbound` instead. */
  export type Outbound = GenericPayment$Outbound;
}

export function genericPaymentToJSON(genericPayment: GenericPayment): string {
  return JSON.stringify(GenericPayment$outboundSchema.parse(genericPayment));
}

export function genericPaymentFromJSON(
  jsonString: string,
): SafeParseResult<GenericPayment, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => GenericPayment$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'GenericPayment' from JSON`,
  );
}
