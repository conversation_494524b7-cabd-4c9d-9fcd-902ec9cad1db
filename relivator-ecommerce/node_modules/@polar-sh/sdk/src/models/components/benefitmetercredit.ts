/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitMeterCreditProperties,
  BenefitMeterCreditProperties$inboundSchema,
  BenefitMeterCreditProperties$Outbound,
  BenefitMeterCreditProperties$outboundSchema,
} from "./benefitmetercreditproperties.js";

export type BenefitMeterCreditMetadata = string | number | number | boolean;

/**
 * A benefit of type `meter_unit`.
 *
 * @remarks
 *
 * Use it to grant a number of units on a specific meter.
 */
export type BenefitMeterCredit = {
  /**
   * The ID of the benefit.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  type?: "meter_credit" | undefined;
  /**
   * The description of the benefit.
   */
  description: string;
  /**
   * Whether the benefit is selectable when creating a product.
   */
  selectable: boolean;
  /**
   * Whether the benefit is deletable.
   */
  deletable: boolean;
  /**
   * The ID of the organization owning the benefit.
   */
  organizationId: string;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Properties for a benefit of type `meter_unit`.
   */
  properties: BenefitMeterCreditProperties;
};

/** @internal */
export const BenefitMeterCreditMetadata$inboundSchema: z.ZodType<
  BenefitMeterCreditMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitMeterCreditMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitMeterCreditMetadata$outboundSchema: z.ZodType<
  BenefitMeterCreditMetadata$Outbound,
  z.ZodTypeDef,
  BenefitMeterCreditMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitMeterCreditMetadata$ {
  /** @deprecated use `BenefitMeterCreditMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitMeterCreditMetadata$inboundSchema;
  /** @deprecated use `BenefitMeterCreditMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitMeterCreditMetadata$outboundSchema;
  /** @deprecated use `BenefitMeterCreditMetadata$Outbound` instead. */
  export type Outbound = BenefitMeterCreditMetadata$Outbound;
}

export function benefitMeterCreditMetadataToJSON(
  benefitMeterCreditMetadata: BenefitMeterCreditMetadata,
): string {
  return JSON.stringify(
    BenefitMeterCreditMetadata$outboundSchema.parse(benefitMeterCreditMetadata),
  );
}

export function benefitMeterCreditMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitMeterCreditMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitMeterCreditMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitMeterCreditMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitMeterCredit$inboundSchema: z.ZodType<
  BenefitMeterCredit,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  type: z.literal("meter_credit").optional(),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organization_id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  properties: BenefitMeterCreditProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitMeterCredit$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  type: "meter_credit";
  description: string;
  selectable: boolean;
  deletable: boolean;
  organization_id: string;
  metadata: { [k: string]: string | number | number | boolean };
  properties: BenefitMeterCreditProperties$Outbound;
};

/** @internal */
export const BenefitMeterCredit$outboundSchema: z.ZodType<
  BenefitMeterCredit$Outbound,
  z.ZodTypeDef,
  BenefitMeterCredit
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  type: z.literal("meter_credit").default("meter_credit" as const),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organizationId: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  properties: BenefitMeterCreditProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitMeterCredit$ {
  /** @deprecated use `BenefitMeterCredit$inboundSchema` instead. */
  export const inboundSchema = BenefitMeterCredit$inboundSchema;
  /** @deprecated use `BenefitMeterCredit$outboundSchema` instead. */
  export const outboundSchema = BenefitMeterCredit$outboundSchema;
  /** @deprecated use `BenefitMeterCredit$Outbound` instead. */
  export type Outbound = BenefitMeterCredit$Outbound;
}

export function benefitMeterCreditToJSON(
  benefitMeterCredit: BenefitMeterCredit,
): string {
  return JSON.stringify(
    BenefitMeterCredit$outboundSchema.parse(benefitMeterCredit),
  );
}

export function benefitMeterCreditFromJSON(
  jsonString: string,
): SafeParseResult<BenefitMeterCredit, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitMeterCredit$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitMeterCredit' from JSON`,
  );
}
