/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountFixedOnceForeverDuration,
  DiscountFixedOnceForeverDuration$inboundSchema,
  DiscountFixedOnceForeverDuration$Outbound,
  DiscountFixedOnceForeverDuration$outboundSchema,
} from "./discountfixedonceforeverduration.js";
import {
  DiscountFixedRepeatDuration,
  DiscountFixedRepeatDuration$inboundSchema,
  DiscountFixedRepeatDuration$Outbound,
  DiscountFixedRepeatDuration$outboundSchema,
} from "./discountfixedrepeatduration.js";
import {
  DiscountPercentageOnceForeverDuration,
  DiscountPercentageOnceForeverDuration$inboundSchema,
  DiscountPercentageOnceForeverDuration$Outbound,
  DiscountPercentageOnceForeverDuration$outboundSchema,
} from "./discountpercentageonceforeverduration.js";
import {
  DiscountPercentageRepeatDuration,
  DiscountPercentageRepeatDuration$inboundSchema,
  DiscountPercentageRepeatDuration$Outbound,
  DiscountPercentageRepeatDuration$outboundSchema,
} from "./discountpercentagerepeatduration.js";

export type Discount =
  | DiscountPercentageOnceForeverDuration
  | DiscountFixedOnceForeverDuration
  | DiscountPercentageRepeatDuration
  | DiscountFixedRepeatDuration;

/** @internal */
export const Discount$inboundSchema: z.ZodType<
  Discount,
  z.ZodTypeDef,
  unknown
> = z.union([
  DiscountPercentageOnceForeverDuration$inboundSchema,
  DiscountFixedOnceForeverDuration$inboundSchema,
  DiscountPercentageRepeatDuration$inboundSchema,
  DiscountFixedRepeatDuration$inboundSchema,
]);

/** @internal */
export type Discount$Outbound =
  | DiscountPercentageOnceForeverDuration$Outbound
  | DiscountFixedOnceForeverDuration$Outbound
  | DiscountPercentageRepeatDuration$Outbound
  | DiscountFixedRepeatDuration$Outbound;

/** @internal */
export const Discount$outboundSchema: z.ZodType<
  Discount$Outbound,
  z.ZodTypeDef,
  Discount
> = z.union([
  DiscountPercentageOnceForeverDuration$outboundSchema,
  DiscountFixedOnceForeverDuration$outboundSchema,
  DiscountPercentageRepeatDuration$outboundSchema,
  DiscountFixedRepeatDuration$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Discount$ {
  /** @deprecated use `Discount$inboundSchema` instead. */
  export const inboundSchema = Discount$inboundSchema;
  /** @deprecated use `Discount$outboundSchema` instead. */
  export const outboundSchema = Discount$outboundSchema;
  /** @deprecated use `Discount$Outbound` instead. */
  export type Outbound = Discount$Outbound;
}

export function discountToJSON(discount: Discount): string {
  return JSON.stringify(Discount$outboundSchema.parse(discount));
}

export function discountFromJSON(
  jsonString: string,
): SafeParseResult<Discount, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Discount$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Discount' from JSON`,
  );
}
