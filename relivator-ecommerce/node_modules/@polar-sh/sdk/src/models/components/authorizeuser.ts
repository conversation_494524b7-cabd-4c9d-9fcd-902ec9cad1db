/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type AuthorizeUser = {
  id: string;
  email: string;
  avatarUrl: string | null;
};

/** @internal */
export const AuthorizeUser$inboundSchema: z.ZodType<
  AuthorizeUser,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  email: z.string(),
  avatar_url: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    "avatar_url": "avatarUrl",
  });
});

/** @internal */
export type AuthorizeUser$Outbound = {
  id: string;
  email: string;
  avatar_url: string | null;
};

/** @internal */
export const AuthorizeUser$outboundSchema: z.ZodType<
  AuthorizeUser$Outbound,
  z.ZodTypeDef,
  AuthorizeUser
> = z.object({
  id: z.string(),
  email: z.string(),
  avatarUrl: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    avatarUrl: "avatar_url",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace AuthorizeUser$ {
  /** @deprecated use `AuthorizeUser$inboundSchema` instead. */
  export const inboundSchema = AuthorizeUser$inboundSchema;
  /** @deprecated use `AuthorizeUser$outboundSchema` instead. */
  export const outboundSchema = AuthorizeUser$outboundSchema;
  /** @deprecated use `AuthorizeUser$Outbound` instead. */
  export type Outbound = AuthorizeUser$Outbound;
}

export function authorizeUserToJSON(authorizeUser: AuthorizeUser): string {
  return JSON.stringify(AuthorizeUser$outboundSchema.parse(authorizeUser));
}

export function authorizeUserFromJSON(
  jsonString: string,
): SafeParseResult<AuthorizeUser, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => AuthorizeUser$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'AuthorizeUser' from JSON`,
  );
}
