/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomFieldSelectProperties,
  CustomFieldSelectProperties$inboundSchema,
  CustomFieldSelectProperties$Outbound,
  CustomFieldSelectProperties$outboundSchema,
} from "./customfieldselectproperties.js";

export type CustomFieldSelectMetadata = string | number | number | boolean;

/**
 * Schema for a custom field of type select.
 */
export type CustomFieldSelect = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  type?: "select" | undefined;
  /**
   * Identifier of the custom field. It'll be used as key when storing the value.
   */
  slug: string;
  /**
   * Name of the custom field.
   */
  name: string;
  /**
   * The ID of the organization owning the custom field.
   */
  organizationId: string;
  properties: CustomFieldSelectProperties;
};

/** @internal */
export const CustomFieldSelectMetadata$inboundSchema: z.ZodType<
  CustomFieldSelectMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomFieldSelectMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomFieldSelectMetadata$outboundSchema: z.ZodType<
  CustomFieldSelectMetadata$Outbound,
  z.ZodTypeDef,
  CustomFieldSelectMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldSelectMetadata$ {
  /** @deprecated use `CustomFieldSelectMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomFieldSelectMetadata$inboundSchema;
  /** @deprecated use `CustomFieldSelectMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomFieldSelectMetadata$outboundSchema;
  /** @deprecated use `CustomFieldSelectMetadata$Outbound` instead. */
  export type Outbound = CustomFieldSelectMetadata$Outbound;
}

export function customFieldSelectMetadataToJSON(
  customFieldSelectMetadata: CustomFieldSelectMetadata,
): string {
  return JSON.stringify(
    CustomFieldSelectMetadata$outboundSchema.parse(customFieldSelectMetadata),
  );
}

export function customFieldSelectMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldSelectMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldSelectMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldSelectMetadata' from JSON`,
  );
}

/** @internal */
export const CustomFieldSelect$inboundSchema: z.ZodType<
  CustomFieldSelect,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  type: z.literal("select").optional(),
  slug: z.string(),
  name: z.string(),
  organization_id: z.string(),
  properties: CustomFieldSelectProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type CustomFieldSelect$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  type: "select";
  slug: string;
  name: string;
  organization_id: string;
  properties: CustomFieldSelectProperties$Outbound;
};

/** @internal */
export const CustomFieldSelect$outboundSchema: z.ZodType<
  CustomFieldSelect$Outbound,
  z.ZodTypeDef,
  CustomFieldSelect
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  type: z.literal("select").default("select" as const),
  slug: z.string(),
  name: z.string(),
  organizationId: z.string(),
  properties: CustomFieldSelectProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldSelect$ {
  /** @deprecated use `CustomFieldSelect$inboundSchema` instead. */
  export const inboundSchema = CustomFieldSelect$inboundSchema;
  /** @deprecated use `CustomFieldSelect$outboundSchema` instead. */
  export const outboundSchema = CustomFieldSelect$outboundSchema;
  /** @deprecated use `CustomFieldSelect$Outbound` instead. */
  export type Outbound = CustomFieldSelect$Outbound;
}

export function customFieldSelectToJSON(
  customFieldSelect: CustomFieldSelect,
): string {
  return JSON.stringify(
    CustomFieldSelect$outboundSchema.parse(customFieldSelect),
  );
}

export function customFieldSelectFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldSelect, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldSelect$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldSelect' from JSON`,
  );
}
