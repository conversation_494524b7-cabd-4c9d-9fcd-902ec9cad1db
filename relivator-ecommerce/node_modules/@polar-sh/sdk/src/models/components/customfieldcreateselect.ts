/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomFieldSelectProperties,
  CustomFieldSelectProperties$inboundSchema,
  CustomFieldSelectProperties$Outbound,
  CustomFieldSelectProperties$outboundSchema,
} from "./customfieldselectproperties.js";

export type CustomFieldCreateSelectMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * Schema to create a custom field of type select.
 */
export type CustomFieldCreateSelect = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type?: "select" | undefined;
  /**
   * Identifier of the custom field. It'll be used as key when storing the value. Must be unique across the organization.It can only contain ASCII letters, numbers and hyphens.
   */
  slug: string;
  /**
   * Name of the custom field.
   */
  name: string;
  /**
   * The ID of the organization owning the custom field. **Required unless you use an organization token.**
   */
  organizationId?: string | null | undefined;
  properties: CustomFieldSelectProperties;
};

/** @internal */
export const CustomFieldCreateSelectMetadata$inboundSchema: z.ZodType<
  CustomFieldCreateSelectMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomFieldCreateSelectMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomFieldCreateSelectMetadata$outboundSchema: z.ZodType<
  CustomFieldCreateSelectMetadata$Outbound,
  z.ZodTypeDef,
  CustomFieldCreateSelectMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldCreateSelectMetadata$ {
  /** @deprecated use `CustomFieldCreateSelectMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomFieldCreateSelectMetadata$inboundSchema;
  /** @deprecated use `CustomFieldCreateSelectMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomFieldCreateSelectMetadata$outboundSchema;
  /** @deprecated use `CustomFieldCreateSelectMetadata$Outbound` instead. */
  export type Outbound = CustomFieldCreateSelectMetadata$Outbound;
}

export function customFieldCreateSelectMetadataToJSON(
  customFieldCreateSelectMetadata: CustomFieldCreateSelectMetadata,
): string {
  return JSON.stringify(
    CustomFieldCreateSelectMetadata$outboundSchema.parse(
      customFieldCreateSelectMetadata,
    ),
  );
}

export function customFieldCreateSelectMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldCreateSelectMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldCreateSelectMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldCreateSelectMetadata' from JSON`,
  );
}

/** @internal */
export const CustomFieldCreateSelect$inboundSchema: z.ZodType<
  CustomFieldCreateSelect,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("select").optional(),
  slug: z.string(),
  name: z.string(),
  organization_id: z.nullable(z.string()).optional(),
  properties: CustomFieldSelectProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
  });
});

/** @internal */
export type CustomFieldCreateSelect$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type: "select";
  slug: string;
  name: string;
  organization_id?: string | null | undefined;
  properties: CustomFieldSelectProperties$Outbound;
};

/** @internal */
export const CustomFieldCreateSelect$outboundSchema: z.ZodType<
  CustomFieldCreateSelect$Outbound,
  z.ZodTypeDef,
  CustomFieldCreateSelect
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("select").default("select" as const),
  slug: z.string(),
  name: z.string(),
  organizationId: z.nullable(z.string()).optional(),
  properties: CustomFieldSelectProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldCreateSelect$ {
  /** @deprecated use `CustomFieldCreateSelect$inboundSchema` instead. */
  export const inboundSchema = CustomFieldCreateSelect$inboundSchema;
  /** @deprecated use `CustomFieldCreateSelect$outboundSchema` instead. */
  export const outboundSchema = CustomFieldCreateSelect$outboundSchema;
  /** @deprecated use `CustomFieldCreateSelect$Outbound` instead. */
  export type Outbound = CustomFieldCreateSelect$Outbound;
}

export function customFieldCreateSelectToJSON(
  customFieldCreateSelect: CustomFieldCreateSelect,
): string {
  return JSON.stringify(
    CustomFieldCreateSelect$outboundSchema.parse(customFieldCreateSelect),
  );
}

export function customFieldCreateSelectFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldCreateSelect, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldCreateSelect$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldCreateSelect' from JSON`,
  );
}
