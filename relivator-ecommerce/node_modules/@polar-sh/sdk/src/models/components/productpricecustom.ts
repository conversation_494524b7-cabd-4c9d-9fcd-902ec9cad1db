/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ProductPriceType,
  ProductPriceType$inboundSchema,
  ProductPriceType$outboundSchema,
} from "./productpricetype.js";
import {
  SubscriptionRecurringInterval,
  SubscriptionRecurringInterval$inboundSchema,
  SubscriptionRecurringInterval$outboundSchema,
} from "./subscriptionrecurringinterval.js";

/**
 * A pay-what-you-want price for a product.
 */
export type ProductPriceCustom = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the price.
   */
  id: string;
  amountType?: "custom" | undefined;
  /**
   * Whether the price is archived and no longer available.
   */
  isArchived: boolean;
  /**
   * The ID of the product owning the price.
   */
  productId: string;
  type: ProductPriceType;
  /**
   * @deprecated field: This will be removed in a future release, please migrate away from it as soon as possible.
   */
  recurringInterval: SubscriptionRecurringInterval | null;
  /**
   * The currency.
   */
  priceCurrency: string;
  /**
   * The minimum amount the customer can pay.
   */
  minimumAmount: number | null;
  /**
   * The maximum amount the customer can pay.
   */
  maximumAmount: number | null;
  /**
   * The initial amount shown to the customer.
   */
  presetAmount: number | null;
};

/** @internal */
export const ProductPriceCustom$inboundSchema: z.ZodType<
  ProductPriceCustom,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  amount_type: z.literal("custom").optional(),
  is_archived: z.boolean(),
  product_id: z.string(),
  type: ProductPriceType$inboundSchema,
  recurring_interval: z.nullable(SubscriptionRecurringInterval$inboundSchema),
  price_currency: z.string(),
  minimum_amount: z.nullable(z.number().int()),
  maximum_amount: z.nullable(z.number().int()),
  preset_amount: z.nullable(z.number().int()),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "amount_type": "amountType",
    "is_archived": "isArchived",
    "product_id": "productId",
    "recurring_interval": "recurringInterval",
    "price_currency": "priceCurrency",
    "minimum_amount": "minimumAmount",
    "maximum_amount": "maximumAmount",
    "preset_amount": "presetAmount",
  });
});

/** @internal */
export type ProductPriceCustom$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  amount_type: "custom";
  is_archived: boolean;
  product_id: string;
  type: string;
  recurring_interval: string | null;
  price_currency: string;
  minimum_amount: number | null;
  maximum_amount: number | null;
  preset_amount: number | null;
};

/** @internal */
export const ProductPriceCustom$outboundSchema: z.ZodType<
  ProductPriceCustom$Outbound,
  z.ZodTypeDef,
  ProductPriceCustom
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  amountType: z.literal("custom").default("custom" as const),
  isArchived: z.boolean(),
  productId: z.string(),
  type: ProductPriceType$outboundSchema,
  recurringInterval: z.nullable(SubscriptionRecurringInterval$outboundSchema),
  priceCurrency: z.string(),
  minimumAmount: z.nullable(z.number().int()),
  maximumAmount: z.nullable(z.number().int()),
  presetAmount: z.nullable(z.number().int()),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    amountType: "amount_type",
    isArchived: "is_archived",
    productId: "product_id",
    recurringInterval: "recurring_interval",
    priceCurrency: "price_currency",
    minimumAmount: "minimum_amount",
    maximumAmount: "maximum_amount",
    presetAmount: "preset_amount",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ProductPriceCustom$ {
  /** @deprecated use `ProductPriceCustom$inboundSchema` instead. */
  export const inboundSchema = ProductPriceCustom$inboundSchema;
  /** @deprecated use `ProductPriceCustom$outboundSchema` instead. */
  export const outboundSchema = ProductPriceCustom$outboundSchema;
  /** @deprecated use `ProductPriceCustom$Outbound` instead. */
  export type Outbound = ProductPriceCustom$Outbound;
}

export function productPriceCustomToJSON(
  productPriceCustom: ProductPriceCustom,
): string {
  return JSON.stringify(
    ProductPriceCustom$outboundSchema.parse(productPriceCustom),
  );
}

export function productPriceCustomFromJSON(
  jsonString: string,
): SafeParseResult<ProductPriceCustom, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ProductPriceCustom$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ProductPriceCustom' from JSON`,
  );
}
