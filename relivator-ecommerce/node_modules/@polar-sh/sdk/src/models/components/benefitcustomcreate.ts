/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitCustomCreateProperties,
  BenefitCustomCreateProperties$inboundSchema,
  BenefitCustomCreateProperties$Outbound,
  BenefitCustomCreateProperties$outboundSchema,
} from "./benefitcustomcreateproperties.js";

export type BenefitCustomCreateMetadata = string | number | number | boolean;

/**
 * Schema to create a benefit of type `custom`.
 */
export type BenefitCustomCreate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type?: "custom" | undefined;
  /**
   * The description of the benefit. Will be displayed on products having this benefit.
   */
  description: string;
  /**
   * The ID of the organization owning the benefit. **Required unless you use an organization token.**
   */
  organizationId?: string | null | undefined;
  /**
   * Properties for creating a benefit of type `custom`.
   */
  properties: BenefitCustomCreateProperties;
};

/** @internal */
export const BenefitCustomCreateMetadata$inboundSchema: z.ZodType<
  BenefitCustomCreateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitCustomCreateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitCustomCreateMetadata$outboundSchema: z.ZodType<
  BenefitCustomCreateMetadata$Outbound,
  z.ZodTypeDef,
  BenefitCustomCreateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitCustomCreateMetadata$ {
  /** @deprecated use `BenefitCustomCreateMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitCustomCreateMetadata$inboundSchema;
  /** @deprecated use `BenefitCustomCreateMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitCustomCreateMetadata$outboundSchema;
  /** @deprecated use `BenefitCustomCreateMetadata$Outbound` instead. */
  export type Outbound = BenefitCustomCreateMetadata$Outbound;
}

export function benefitCustomCreateMetadataToJSON(
  benefitCustomCreateMetadata: BenefitCustomCreateMetadata,
): string {
  return JSON.stringify(
    BenefitCustomCreateMetadata$outboundSchema.parse(
      benefitCustomCreateMetadata,
    ),
  );
}

export function benefitCustomCreateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitCustomCreateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitCustomCreateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitCustomCreateMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitCustomCreate$inboundSchema: z.ZodType<
  BenefitCustomCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("custom").optional(),
  description: z.string(),
  organization_id: z.nullable(z.string()).optional(),
  properties: BenefitCustomCreateProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitCustomCreate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type: "custom";
  description: string;
  organization_id?: string | null | undefined;
  properties: BenefitCustomCreateProperties$Outbound;
};

/** @internal */
export const BenefitCustomCreate$outboundSchema: z.ZodType<
  BenefitCustomCreate$Outbound,
  z.ZodTypeDef,
  BenefitCustomCreate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("custom").default("custom" as const),
  description: z.string(),
  organizationId: z.nullable(z.string()).optional(),
  properties: BenefitCustomCreateProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitCustomCreate$ {
  /** @deprecated use `BenefitCustomCreate$inboundSchema` instead. */
  export const inboundSchema = BenefitCustomCreate$inboundSchema;
  /** @deprecated use `BenefitCustomCreate$outboundSchema` instead. */
  export const outboundSchema = BenefitCustomCreate$outboundSchema;
  /** @deprecated use `BenefitCustomCreate$Outbound` instead. */
  export type Outbound = BenefitCustomCreate$Outbound;
}

export function benefitCustomCreateToJSON(
  benefitCustomCreate: BenefitCustomCreate,
): string {
  return JSON.stringify(
    BenefitCustomCreate$outboundSchema.parse(benefitCustomCreate),
  );
}

export function benefitCustomCreateFromJSON(
  jsonString: string,
): SafeParseResult<BenefitCustomCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitCustomCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitCustomCreate' from JSON`,
  );
}
