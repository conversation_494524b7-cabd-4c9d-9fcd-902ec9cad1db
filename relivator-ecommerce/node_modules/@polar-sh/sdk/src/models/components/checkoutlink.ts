/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CheckoutLinkProduct,
  CheckoutLinkProduct$inboundSchema,
  CheckoutLinkProduct$Outbound,
  CheckoutLinkProduct$outboundSchema,
} from "./checkoutlinkproduct.js";
import {
  DiscountFixedOnceForeverDurationBase,
  DiscountFixedOnceForeverDurationBase$inboundSchema,
  DiscountFixedOnceForeverDurationBase$Outbound,
  DiscountFixedOnceForeverDurationBase$outboundSchema,
} from "./discountfixedonceforeverdurationbase.js";
import {
  DiscountFixedRepeatDurationBase,
  DiscountFixedRepeatDurationBase$inboundSchema,
  DiscountFixedRepeatDurationBase$Outbound,
  DiscountFixedRepeatDurationBase$outboundSchema,
} from "./discountfixedrepeatdurationbase.js";
import {
  DiscountPercentageOnceForeverDurationBase,
  DiscountPercentageOnceForeverDurationBase$inboundSchema,
  DiscountPercentageOnceForeverDurationBase$Outbound,
  DiscountPercentageOnceForeverDurationBase$outboundSchema,
} from "./discountpercentageonceforeverdurationbase.js";
import {
  DiscountPercentageRepeatDurationBase,
  DiscountPercentageRepeatDurationBase$inboundSchema,
  DiscountPercentageRepeatDurationBase$Outbound,
  DiscountPercentageRepeatDurationBase$outboundSchema,
} from "./discountpercentagerepeatdurationbase.js";
import {
  PaymentProcessor,
  PaymentProcessor$inboundSchema,
  PaymentProcessor$outboundSchema,
} from "./paymentprocessor.js";

export type CheckoutLinkMetadata = string | number | number | boolean;

export type CheckoutLinkDiscount =
  | DiscountPercentageOnceForeverDurationBase
  | DiscountFixedOnceForeverDurationBase
  | DiscountPercentageRepeatDurationBase
  | DiscountFixedRepeatDurationBase;

/**
 * Checkout link data.
 */
export type CheckoutLink = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  paymentProcessor: PaymentProcessor;
  /**
   * Client secret used to access the checkout link.
   */
  clientSecret: string;
  /**
   * URL where the customer will be redirected after a successful payment.
   */
  successUrl: string | null;
  /**
   * Optional label to distinguish links internally
   */
  label: string | null;
  /**
   * Whether to allow the customer to apply discount codes. If you apply a discount through `discount_id`, it'll still be applied, but the customer won't be able to change it.
   */
  allowDiscountCodes: boolean;
  /**
   * Whether to require the customer to fill their full billing address, instead of just the country. Customers in the US will always be required to fill their full address, regardless of this setting.
   */
  requireBillingAddress: boolean;
  /**
   * ID of the discount to apply to the checkout. If the discount is not applicable anymore when opening the checkout link, it'll be ignored.
   */
  discountId: string | null;
  /**
   * The organization ID.
   */
  organizationId: string;
  products: Array<CheckoutLinkProduct>;
  discount:
    | DiscountPercentageOnceForeverDurationBase
    | DiscountFixedOnceForeverDurationBase
    | DiscountPercentageRepeatDurationBase
    | DiscountFixedRepeatDurationBase
    | null;
  url: string;
};

/** @internal */
export const CheckoutLinkMetadata$inboundSchema: z.ZodType<
  CheckoutLinkMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CheckoutLinkMetadata$Outbound = string | number | number | boolean;

/** @internal */
export const CheckoutLinkMetadata$outboundSchema: z.ZodType<
  CheckoutLinkMetadata$Outbound,
  z.ZodTypeDef,
  CheckoutLinkMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutLinkMetadata$ {
  /** @deprecated use `CheckoutLinkMetadata$inboundSchema` instead. */
  export const inboundSchema = CheckoutLinkMetadata$inboundSchema;
  /** @deprecated use `CheckoutLinkMetadata$outboundSchema` instead. */
  export const outboundSchema = CheckoutLinkMetadata$outboundSchema;
  /** @deprecated use `CheckoutLinkMetadata$Outbound` instead. */
  export type Outbound = CheckoutLinkMetadata$Outbound;
}

export function checkoutLinkMetadataToJSON(
  checkoutLinkMetadata: CheckoutLinkMetadata,
): string {
  return JSON.stringify(
    CheckoutLinkMetadata$outboundSchema.parse(checkoutLinkMetadata),
  );
}

export function checkoutLinkMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutLinkMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutLinkMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutLinkMetadata' from JSON`,
  );
}

/** @internal */
export const CheckoutLinkDiscount$inboundSchema: z.ZodType<
  CheckoutLinkDiscount,
  z.ZodTypeDef,
  unknown
> = z.union([
  DiscountPercentageOnceForeverDurationBase$inboundSchema,
  DiscountFixedOnceForeverDurationBase$inboundSchema,
  DiscountPercentageRepeatDurationBase$inboundSchema,
  DiscountFixedRepeatDurationBase$inboundSchema,
]);

/** @internal */
export type CheckoutLinkDiscount$Outbound =
  | DiscountPercentageOnceForeverDurationBase$Outbound
  | DiscountFixedOnceForeverDurationBase$Outbound
  | DiscountPercentageRepeatDurationBase$Outbound
  | DiscountFixedRepeatDurationBase$Outbound;

/** @internal */
export const CheckoutLinkDiscount$outboundSchema: z.ZodType<
  CheckoutLinkDiscount$Outbound,
  z.ZodTypeDef,
  CheckoutLinkDiscount
> = z.union([
  DiscountPercentageOnceForeverDurationBase$outboundSchema,
  DiscountFixedOnceForeverDurationBase$outboundSchema,
  DiscountPercentageRepeatDurationBase$outboundSchema,
  DiscountFixedRepeatDurationBase$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutLinkDiscount$ {
  /** @deprecated use `CheckoutLinkDiscount$inboundSchema` instead. */
  export const inboundSchema = CheckoutLinkDiscount$inboundSchema;
  /** @deprecated use `CheckoutLinkDiscount$outboundSchema` instead. */
  export const outboundSchema = CheckoutLinkDiscount$outboundSchema;
  /** @deprecated use `CheckoutLinkDiscount$Outbound` instead. */
  export type Outbound = CheckoutLinkDiscount$Outbound;
}

export function checkoutLinkDiscountToJSON(
  checkoutLinkDiscount: CheckoutLinkDiscount,
): string {
  return JSON.stringify(
    CheckoutLinkDiscount$outboundSchema.parse(checkoutLinkDiscount),
  );
}

export function checkoutLinkDiscountFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutLinkDiscount, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutLinkDiscount$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutLinkDiscount' from JSON`,
  );
}

/** @internal */
export const CheckoutLink$inboundSchema: z.ZodType<
  CheckoutLink,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  payment_processor: PaymentProcessor$inboundSchema,
  client_secret: z.string(),
  success_url: z.nullable(z.string()),
  label: z.nullable(z.string()),
  allow_discount_codes: z.boolean(),
  require_billing_address: z.boolean(),
  discount_id: z.nullable(z.string()),
  organization_id: z.string(),
  products: z.array(CheckoutLinkProduct$inboundSchema),
  discount: z.nullable(
    z.union([
      DiscountPercentageOnceForeverDurationBase$inboundSchema,
      DiscountFixedOnceForeverDurationBase$inboundSchema,
      DiscountPercentageRepeatDurationBase$inboundSchema,
      DiscountFixedRepeatDurationBase$inboundSchema,
    ]),
  ),
  url: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "payment_processor": "paymentProcessor",
    "client_secret": "clientSecret",
    "success_url": "successUrl",
    "allow_discount_codes": "allowDiscountCodes",
    "require_billing_address": "requireBillingAddress",
    "discount_id": "discountId",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type CheckoutLink$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  payment_processor: string;
  client_secret: string;
  success_url: string | null;
  label: string | null;
  allow_discount_codes: boolean;
  require_billing_address: boolean;
  discount_id: string | null;
  organization_id: string;
  products: Array<CheckoutLinkProduct$Outbound>;
  discount:
    | DiscountPercentageOnceForeverDurationBase$Outbound
    | DiscountFixedOnceForeverDurationBase$Outbound
    | DiscountPercentageRepeatDurationBase$Outbound
    | DiscountFixedRepeatDurationBase$Outbound
    | null;
  url: string;
};

/** @internal */
export const CheckoutLink$outboundSchema: z.ZodType<
  CheckoutLink$Outbound,
  z.ZodTypeDef,
  CheckoutLink
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  paymentProcessor: PaymentProcessor$outboundSchema,
  clientSecret: z.string(),
  successUrl: z.nullable(z.string()),
  label: z.nullable(z.string()),
  allowDiscountCodes: z.boolean(),
  requireBillingAddress: z.boolean(),
  discountId: z.nullable(z.string()),
  organizationId: z.string(),
  products: z.array(CheckoutLinkProduct$outboundSchema),
  discount: z.nullable(
    z.union([
      DiscountPercentageOnceForeverDurationBase$outboundSchema,
      DiscountFixedOnceForeverDurationBase$outboundSchema,
      DiscountPercentageRepeatDurationBase$outboundSchema,
      DiscountFixedRepeatDurationBase$outboundSchema,
    ]),
  ),
  url: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    paymentProcessor: "payment_processor",
    clientSecret: "client_secret",
    successUrl: "success_url",
    allowDiscountCodes: "allow_discount_codes",
    requireBillingAddress: "require_billing_address",
    discountId: "discount_id",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CheckoutLink$ {
  /** @deprecated use `CheckoutLink$inboundSchema` instead. */
  export const inboundSchema = CheckoutLink$inboundSchema;
  /** @deprecated use `CheckoutLink$outboundSchema` instead. */
  export const outboundSchema = CheckoutLink$outboundSchema;
  /** @deprecated use `CheckoutLink$Outbound` instead. */
  export type Outbound = CheckoutLink$Outbound;
}

export function checkoutLinkToJSON(checkoutLink: CheckoutLink): string {
  return JSON.stringify(CheckoutLink$outboundSchema.parse(checkoutLink));
}

export function checkoutLinkFromJSON(
  jsonString: string,
): SafeParseResult<CheckoutLink, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CheckoutLink$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CheckoutLink' from JSON`,
  );
}
