/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  LicenseKeyStatus,
  LicenseKeyStatus$inboundSchema,
  LicenseKeyStatus$outboundSchema,
} from "./licensekeystatus.js";

export type LicenseKeyUpdate = {
  status?: LicenseKeyStatus | null | undefined;
  usage?: number | undefined;
  limitActivations?: number | null | undefined;
  limitUsage?: number | null | undefined;
  expiresAt?: Date | null | undefined;
};

/** @internal */
export const LicenseKeyUpdate$inboundSchema: z.ZodType<
  LicenseKeyUpdate,
  z.ZodTypeDef,
  unknown
> = z.object({
  status: z.nullable(LicenseKeyStatus$inboundSchema).optional(),
  usage: z.number().int().default(0),
  limit_activations: z.nullable(z.number().int()).optional(),
  limit_usage: z.nullable(z.number().int()).optional(),
  expires_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ).optional(),
}).transform((v) => {
  return remap$(v, {
    "limit_activations": "limitActivations",
    "limit_usage": "limitUsage",
    "expires_at": "expiresAt",
  });
});

/** @internal */
export type LicenseKeyUpdate$Outbound = {
  status?: string | null | undefined;
  usage: number;
  limit_activations?: number | null | undefined;
  limit_usage?: number | null | undefined;
  expires_at?: string | null | undefined;
};

/** @internal */
export const LicenseKeyUpdate$outboundSchema: z.ZodType<
  LicenseKeyUpdate$Outbound,
  z.ZodTypeDef,
  LicenseKeyUpdate
> = z.object({
  status: z.nullable(LicenseKeyStatus$outboundSchema).optional(),
  usage: z.number().int().default(0),
  limitActivations: z.nullable(z.number().int()).optional(),
  limitUsage: z.nullable(z.number().int()).optional(),
  expiresAt: z.nullable(z.date().transform(v => v.toISOString())).optional(),
}).transform((v) => {
  return remap$(v, {
    limitActivations: "limit_activations",
    limitUsage: "limit_usage",
    expiresAt: "expires_at",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LicenseKeyUpdate$ {
  /** @deprecated use `LicenseKeyUpdate$inboundSchema` instead. */
  export const inboundSchema = LicenseKeyUpdate$inboundSchema;
  /** @deprecated use `LicenseKeyUpdate$outboundSchema` instead. */
  export const outboundSchema = LicenseKeyUpdate$outboundSchema;
  /** @deprecated use `LicenseKeyUpdate$Outbound` instead. */
  export type Outbound = LicenseKeyUpdate$Outbound;
}

export function licenseKeyUpdateToJSON(
  licenseKeyUpdate: LicenseKeyUpdate,
): string {
  return JSON.stringify(
    LicenseKeyUpdate$outboundSchema.parse(licenseKeyUpdate),
  );
}

export function licenseKeyUpdateFromJSON(
  jsonString: string,
): SafeParseResult<LicenseKeyUpdate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LicenseKeyUpdate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LicenseKeyUpdate' from JSON`,
  );
}
