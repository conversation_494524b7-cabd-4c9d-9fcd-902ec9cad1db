/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";
import {
  Product,
  Product$inboundSchema,
  Product$Outbound,
  Product$outboundSchema,
} from "./product.js";

export type ListResourceProduct = {
  items: Array<Product>;
  pagination: Pagination;
};

/** @internal */
export const ListResourceProduct$inboundSchema: z.ZodType<
  ListResourceProduct,
  z.ZodTypeDef,
  unknown
> = z.object({
  items: z.array(Product$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ListResourceProduct$Outbound = {
  items: Array<Product$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ListResourceProduct$outboundSchema: z.ZodType<
  ListResourceProduct$Outbound,
  z.ZodTypeDef,
  ListResourceProduct
> = z.object({
  items: z.array(Product$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListResourceProduct$ {
  /** @deprecated use `ListResourceProduct$inboundSchema` instead. */
  export const inboundSchema = ListResourceProduct$inboundSchema;
  /** @deprecated use `ListResourceProduct$outboundSchema` instead. */
  export const outboundSchema = ListResourceProduct$outboundSchema;
  /** @deprecated use `ListResourceProduct$Outbound` instead. */
  export type Outbound = ListResourceProduct$Outbound;
}

export function listResourceProductToJSON(
  listResourceProduct: ListResourceProduct,
): string {
  return JSON.stringify(
    ListResourceProduct$outboundSchema.parse(listResourceProduct),
  );
}

export function listResourceProductFromJSON(
  jsonString: string,
): SafeParseResult<ListResourceProduct, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListResourceProduct$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListResourceProduct' from JSON`,
  );
}
