/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const RefundSortProperty = {
  CreatedAt: "created_at",
  MinusCreatedAt: "-created_at",
  Amount: "amount",
  MinusAmount: "-amount",
} as const;
export type RefundSortProperty = ClosedEnum<typeof RefundSortProperty>;

/** @internal */
export const RefundSortProperty$inboundSchema: z.ZodNativeEnum<
  typeof RefundSortProperty
> = z.nativeEnum(RefundSortProperty);

/** @internal */
export const RefundSortProperty$outboundSchema: z.ZodNativeEnum<
  typeof RefundSortProperty
> = RefundSortProperty$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace RefundSortProperty$ {
  /** @deprecated use `RefundSortProperty$inboundSchema` instead. */
  export const inboundSchema = RefundSortProperty$inboundSchema;
  /** @deprecated use `RefundSortProperty$outboundSchema` instead. */
  export const outboundSchema = RefundSortProperty$outboundSchema;
}
