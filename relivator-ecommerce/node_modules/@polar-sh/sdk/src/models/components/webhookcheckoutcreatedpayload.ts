/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Checkout,
  Checkout$inboundSchema,
  Checkout$Outbound,
  Checkout$outboundSchema,
} from "./checkout.js";

/**
 * Sent when a new checkout is created.
 *
 * @remarks
 *
 * **Discord & Slack support:** Basic
 */
export type WebhookCheckoutCreatedPayload = {
  type?: "checkout.created" | undefined;
  /**
   * Checkout session data retrieved using an access token.
   */
  data: Checkout;
};

/** @internal */
export const WebhookCheckoutCreatedPayload$inboundSchema: z.ZodType<
  WebhookCheckoutCreatedPayload,
  z.ZodTypeDef,
  unknown
> = z.object({
  type: z.literal("checkout.created").optional(),
  data: Checkout$inboundSchema,
});

/** @internal */
export type WebhookCheckoutCreatedPayload$Outbound = {
  type: "checkout.created";
  data: Checkout$Outbound;
};

/** @internal */
export const WebhookCheckoutCreatedPayload$outboundSchema: z.ZodType<
  WebhookCheckoutCreatedPayload$Outbound,
  z.ZodTypeDef,
  WebhookCheckoutCreatedPayload
> = z.object({
  type: z.literal("checkout.created").default("checkout.created" as const),
  data: Checkout$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace WebhookCheckoutCreatedPayload$ {
  /** @deprecated use `WebhookCheckoutCreatedPayload$inboundSchema` instead. */
  export const inboundSchema = WebhookCheckoutCreatedPayload$inboundSchema;
  /** @deprecated use `WebhookCheckoutCreatedPayload$outboundSchema` instead. */
  export const outboundSchema = WebhookCheckoutCreatedPayload$outboundSchema;
  /** @deprecated use `WebhookCheckoutCreatedPayload$Outbound` instead. */
  export type Outbound = WebhookCheckoutCreatedPayload$Outbound;
}

export function webhookCheckoutCreatedPayloadToJSON(
  webhookCheckoutCreatedPayload: WebhookCheckoutCreatedPayload,
): string {
  return JSON.stringify(
    WebhookCheckoutCreatedPayload$outboundSchema.parse(
      webhookCheckoutCreatedPayload,
    ),
  );
}

export function webhookCheckoutCreatedPayloadFromJSON(
  jsonString: string,
): SafeParseResult<WebhookCheckoutCreatedPayload, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => WebhookCheckoutCreatedPayload$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'WebhookCheckoutCreatedPayload' from JSON`,
  );
}
