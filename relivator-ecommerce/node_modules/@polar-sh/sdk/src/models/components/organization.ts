/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  OrganizationFeatureSettings,
  OrganizationFeatureSettings$inboundSchema,
  OrganizationFeatureSettings$Outbound,
  OrganizationFeatureSettings$outboundSchema,
} from "./organizationfeaturesettings.js";
import {
  OrganizationSocialLink,
  OrganizationSocialLink$inboundSchema,
  OrganizationSocialLink$Outbound,
  OrganizationSocialLink$outboundSchema,
} from "./organizationsociallink.js";
import {
  OrganizationSubscriptionSettings,
  OrganizationSubscriptionSettings$inboundSchema,
  OrganizationSubscriptionSettings$Outbound,
  OrganizationSubscriptionSettings$outboundSchema,
} from "./organizationsubscriptionsettings.js";

export type Organization = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The organization ID.
   */
  id: string;
  /**
   * Organization name shown in checkout, customer portal, emails etc.
   */
  name: string;
  /**
   * Unique organization slug in checkout, customer portal and credit card statements.
   */
  slug: string;
  /**
   * Avatar URL shown in checkout, customer portal, emails etc.
   */
  avatarUrl: string | null;
  /**
   * Public support email.
   */
  email: string | null;
  /**
   * Official website of the organization.
   */
  website: string | null;
  /**
   * Links to social profiles.
   */
  socials: Array<OrganizationSocialLink>;
  /**
   * When the business details were submitted.
   */
  detailsSubmittedAt: Date | null;
  /**
   * Organization feature settings
   */
  featureSettings: OrganizationFeatureSettings | null;
  subscriptionSettings: OrganizationSubscriptionSettings;
};

/** @internal */
export const Organization$inboundSchema: z.ZodType<
  Organization,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  name: z.string(),
  slug: z.string(),
  avatar_url: z.nullable(z.string()),
  email: z.nullable(z.string()),
  website: z.nullable(z.string()),
  socials: z.array(OrganizationSocialLink$inboundSchema),
  details_submitted_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  feature_settings: z.nullable(OrganizationFeatureSettings$inboundSchema),
  subscription_settings: OrganizationSubscriptionSettings$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "avatar_url": "avatarUrl",
    "details_submitted_at": "detailsSubmittedAt",
    "feature_settings": "featureSettings",
    "subscription_settings": "subscriptionSettings",
  });
});

/** @internal */
export type Organization$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  name: string;
  slug: string;
  avatar_url: string | null;
  email: string | null;
  website: string | null;
  socials: Array<OrganizationSocialLink$Outbound>;
  details_submitted_at: string | null;
  feature_settings: OrganizationFeatureSettings$Outbound | null;
  subscription_settings: OrganizationSubscriptionSettings$Outbound;
};

/** @internal */
export const Organization$outboundSchema: z.ZodType<
  Organization$Outbound,
  z.ZodTypeDef,
  Organization
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  name: z.string(),
  slug: z.string(),
  avatarUrl: z.nullable(z.string()),
  email: z.nullable(z.string()),
  website: z.nullable(z.string()),
  socials: z.array(OrganizationSocialLink$outboundSchema),
  detailsSubmittedAt: z.nullable(z.date().transform(v => v.toISOString())),
  featureSettings: z.nullable(OrganizationFeatureSettings$outboundSchema),
  subscriptionSettings: OrganizationSubscriptionSettings$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    avatarUrl: "avatar_url",
    detailsSubmittedAt: "details_submitted_at",
    featureSettings: "feature_settings",
    subscriptionSettings: "subscription_settings",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Organization$ {
  /** @deprecated use `Organization$inboundSchema` instead. */
  export const inboundSchema = Organization$inboundSchema;
  /** @deprecated use `Organization$outboundSchema` instead. */
  export const outboundSchema = Organization$outboundSchema;
  /** @deprecated use `Organization$Outbound` instead. */
  export type Outbound = Organization$Outbound;
}

export function organizationToJSON(organization: Organization): string {
  return JSON.stringify(Organization$outboundSchema.parse(organization));
}

export function organizationFromJSON(
  jsonString: string,
): SafeParseResult<Organization, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Organization$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Organization' from JSON`,
  );
}
