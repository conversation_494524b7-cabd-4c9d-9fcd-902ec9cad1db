/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Address,
  Address$inboundSchema,
  Address$Outbound,
  Address$outboundSchema,
} from "./address.js";
import {
  TaxIDFormat,
  TaxIDFormat$inboundSchema,
  TaxIDFormat$outboundSchema,
} from "./taxidformat.js";

export type LicenseKeyCustomerMetadata = string | number | number | boolean;

export type LicenseKeyCustomerTaxId = string | TaxIDFormat;

export type LicenseKeyCustomer = {
  /**
   * The ID of the customer.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * The ID of the customer in your system. This must be unique within the organization. Once set, it can't be updated.
   */
  externalId: string | null;
  /**
   * The email address of the customer. This must be unique within the organization.
   */
  email: string;
  /**
   * Whether the customer email address is verified. The address is automatically verified when the customer accesses the customer portal using their email address.
   */
  emailVerified: boolean;
  /**
   * The name of the customer.
   */
  name: string | null;
  billingAddress: Address | null;
  taxId: Array<string | TaxIDFormat | null> | null;
  /**
   * The ID of the organization owning the customer.
   */
  organizationId: string;
  /**
   * Timestamp for when the customer was soft deleted.
   */
  deletedAt: Date | null;
  avatarUrl: string;
};

/** @internal */
export const LicenseKeyCustomerMetadata$inboundSchema: z.ZodType<
  LicenseKeyCustomerMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type LicenseKeyCustomerMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const LicenseKeyCustomerMetadata$outboundSchema: z.ZodType<
  LicenseKeyCustomerMetadata$Outbound,
  z.ZodTypeDef,
  LicenseKeyCustomerMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LicenseKeyCustomerMetadata$ {
  /** @deprecated use `LicenseKeyCustomerMetadata$inboundSchema` instead. */
  export const inboundSchema = LicenseKeyCustomerMetadata$inboundSchema;
  /** @deprecated use `LicenseKeyCustomerMetadata$outboundSchema` instead. */
  export const outboundSchema = LicenseKeyCustomerMetadata$outboundSchema;
  /** @deprecated use `LicenseKeyCustomerMetadata$Outbound` instead. */
  export type Outbound = LicenseKeyCustomerMetadata$Outbound;
}

export function licenseKeyCustomerMetadataToJSON(
  licenseKeyCustomerMetadata: LicenseKeyCustomerMetadata,
): string {
  return JSON.stringify(
    LicenseKeyCustomerMetadata$outboundSchema.parse(licenseKeyCustomerMetadata),
  );
}

export function licenseKeyCustomerMetadataFromJSON(
  jsonString: string,
): SafeParseResult<LicenseKeyCustomerMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LicenseKeyCustomerMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LicenseKeyCustomerMetadata' from JSON`,
  );
}

/** @internal */
export const LicenseKeyCustomerTaxId$inboundSchema: z.ZodType<
  LicenseKeyCustomerTaxId,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), TaxIDFormat$inboundSchema]);

/** @internal */
export type LicenseKeyCustomerTaxId$Outbound = string | string;

/** @internal */
export const LicenseKeyCustomerTaxId$outboundSchema: z.ZodType<
  LicenseKeyCustomerTaxId$Outbound,
  z.ZodTypeDef,
  LicenseKeyCustomerTaxId
> = z.union([z.string(), TaxIDFormat$outboundSchema]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LicenseKeyCustomerTaxId$ {
  /** @deprecated use `LicenseKeyCustomerTaxId$inboundSchema` instead. */
  export const inboundSchema = LicenseKeyCustomerTaxId$inboundSchema;
  /** @deprecated use `LicenseKeyCustomerTaxId$outboundSchema` instead. */
  export const outboundSchema = LicenseKeyCustomerTaxId$outboundSchema;
  /** @deprecated use `LicenseKeyCustomerTaxId$Outbound` instead. */
  export type Outbound = LicenseKeyCustomerTaxId$Outbound;
}

export function licenseKeyCustomerTaxIdToJSON(
  licenseKeyCustomerTaxId: LicenseKeyCustomerTaxId,
): string {
  return JSON.stringify(
    LicenseKeyCustomerTaxId$outboundSchema.parse(licenseKeyCustomerTaxId),
  );
}

export function licenseKeyCustomerTaxIdFromJSON(
  jsonString: string,
): SafeParseResult<LicenseKeyCustomerTaxId, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LicenseKeyCustomerTaxId$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LicenseKeyCustomerTaxId' from JSON`,
  );
}

/** @internal */
export const LicenseKeyCustomer$inboundSchema: z.ZodType<
  LicenseKeyCustomer,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  external_id: z.nullable(z.string()),
  email: z.string(),
  email_verified: z.boolean(),
  name: z.nullable(z.string()),
  billing_address: z.nullable(Address$inboundSchema),
  tax_id: z.nullable(
    z.array(z.nullable(z.union([z.string(), TaxIDFormat$inboundSchema]))),
  ),
  organization_id: z.string(),
  deleted_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  avatar_url: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "external_id": "externalId",
    "email_verified": "emailVerified",
    "billing_address": "billingAddress",
    "tax_id": "taxId",
    "organization_id": "organizationId",
    "deleted_at": "deletedAt",
    "avatar_url": "avatarUrl",
  });
});

/** @internal */
export type LicenseKeyCustomer$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  metadata: { [k: string]: string | number | number | boolean };
  external_id: string | null;
  email: string;
  email_verified: boolean;
  name: string | null;
  billing_address: Address$Outbound | null;
  tax_id: Array<string | string | null> | null;
  organization_id: string;
  deleted_at: string | null;
  avatar_url: string;
};

/** @internal */
export const LicenseKeyCustomer$outboundSchema: z.ZodType<
  LicenseKeyCustomer$Outbound,
  z.ZodTypeDef,
  LicenseKeyCustomer
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  externalId: z.nullable(z.string()),
  email: z.string(),
  emailVerified: z.boolean(),
  name: z.nullable(z.string()),
  billingAddress: z.nullable(Address$outboundSchema),
  taxId: z.nullable(
    z.array(z.nullable(z.union([z.string(), TaxIDFormat$outboundSchema]))),
  ),
  organizationId: z.string(),
  deletedAt: z.nullable(z.date().transform(v => v.toISOString())),
  avatarUrl: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    externalId: "external_id",
    emailVerified: "email_verified",
    billingAddress: "billing_address",
    taxId: "tax_id",
    organizationId: "organization_id",
    deletedAt: "deleted_at",
    avatarUrl: "avatar_url",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace LicenseKeyCustomer$ {
  /** @deprecated use `LicenseKeyCustomer$inboundSchema` instead. */
  export const inboundSchema = LicenseKeyCustomer$inboundSchema;
  /** @deprecated use `LicenseKeyCustomer$outboundSchema` instead. */
  export const outboundSchema = LicenseKeyCustomer$outboundSchema;
  /** @deprecated use `LicenseKeyCustomer$Outbound` instead. */
  export type Outbound = LicenseKeyCustomer$Outbound;
}

export function licenseKeyCustomerToJSON(
  licenseKeyCustomer: LicenseKeyCustomer,
): string {
  return JSON.stringify(
    LicenseKeyCustomer$outboundSchema.parse(licenseKeyCustomer),
  );
}

export function licenseKeyCustomerFromJSON(
  jsonString: string,
): SafeParseResult<LicenseKeyCustomer, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => LicenseKeyCustomer$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'LicenseKeyCustomer' from JSON`,
  );
}
