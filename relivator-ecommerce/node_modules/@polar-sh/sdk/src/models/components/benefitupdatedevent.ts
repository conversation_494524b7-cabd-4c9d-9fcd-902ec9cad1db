/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitGrantMetadata,
  BenefitGrantMetadata$inboundSchema,
  BenefitGrantMetadata$Outbound,
  BenefitGrantMetadata$outboundSchema,
} from "./benefitgrantmetadata.js";
import {
  Customer,
  Customer$inboundSchema,
  Customer$Outbound,
  Customer$outboundSchema,
} from "./customer.js";

/**
 * An event created by <PERSON> when a benefit is updated.
 */
export type BenefitUpdatedEvent = {
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * The timestamp of the event.
   */
  timestamp: Date;
  /**
   * The ID of the organization owning the event.
   */
  organizationId: string;
  /**
   * ID of the customer in your Polar organization associated with the event.
   */
  customerId: string | null;
  /**
   * The customer associated with the event.
   */
  customer: Customer | null;
  /**
   * ID of the customer in your system associated with the event.
   */
  externalCustomerId: string | null;
  /**
   * The source of the event. `system` events are created by Polar. `user` events are the one you create through our ingestion API.
   */
  source?: "system" | undefined;
  /**
   * The name of the event.
   */
  name?: "benefit.updated" | undefined;
  metadata: BenefitGrantMetadata;
};

/** @internal */
export const BenefitUpdatedEvent$inboundSchema: z.ZodType<
  BenefitUpdatedEvent,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  timestamp: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  organization_id: z.string(),
  customer_id: z.nullable(z.string()),
  customer: z.nullable(Customer$inboundSchema),
  external_customer_id: z.nullable(z.string()),
  source: z.literal("system").optional(),
  name: z.literal("benefit.updated").optional(),
  metadata: BenefitGrantMetadata$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
    "customer_id": "customerId",
    "external_customer_id": "externalCustomerId",
  });
});

/** @internal */
export type BenefitUpdatedEvent$Outbound = {
  id: string;
  timestamp: string;
  organization_id: string;
  customer_id: string | null;
  customer: Customer$Outbound | null;
  external_customer_id: string | null;
  source: "system";
  name: "benefit.updated";
  metadata: BenefitGrantMetadata$Outbound;
};

/** @internal */
export const BenefitUpdatedEvent$outboundSchema: z.ZodType<
  BenefitUpdatedEvent$Outbound,
  z.ZodTypeDef,
  BenefitUpdatedEvent
> = z.object({
  id: z.string(),
  timestamp: z.date().transform(v => v.toISOString()),
  organizationId: z.string(),
  customerId: z.nullable(z.string()),
  customer: z.nullable(Customer$outboundSchema),
  externalCustomerId: z.nullable(z.string()),
  source: z.literal("system").default("system" as const),
  name: z.literal("benefit.updated").default("benefit.updated" as const),
  metadata: BenefitGrantMetadata$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
    customerId: "customer_id",
    externalCustomerId: "external_customer_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitUpdatedEvent$ {
  /** @deprecated use `BenefitUpdatedEvent$inboundSchema` instead. */
  export const inboundSchema = BenefitUpdatedEvent$inboundSchema;
  /** @deprecated use `BenefitUpdatedEvent$outboundSchema` instead. */
  export const outboundSchema = BenefitUpdatedEvent$outboundSchema;
  /** @deprecated use `BenefitUpdatedEvent$Outbound` instead. */
  export type Outbound = BenefitUpdatedEvent$Outbound;
}

export function benefitUpdatedEventToJSON(
  benefitUpdatedEvent: BenefitUpdatedEvent,
): string {
  return JSON.stringify(
    BenefitUpdatedEvent$outboundSchema.parse(benefitUpdatedEvent),
  );
}

export function benefitUpdatedEventFromJSON(
  jsonString: string,
): SafeParseResult<BenefitUpdatedEvent, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitUpdatedEvent$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitUpdatedEvent' from JSON`,
  );
}
