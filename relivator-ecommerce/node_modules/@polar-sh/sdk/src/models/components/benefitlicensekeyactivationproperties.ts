/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type BenefitLicenseKeyActivationProperties = {
  limit: number;
  enableCustomerAdmin: boolean;
};

/** @internal */
export const BenefitLicenseKeyActivationProperties$inboundSchema: z.ZodType<
  BenefitLicenseKeyActivationProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  limit: z.number().int(),
  enable_customer_admin: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    "enable_customer_admin": "enableCustomerAdmin",
  });
});

/** @internal */
export type BenefitLicenseKeyActivationProperties$Outbound = {
  limit: number;
  enable_customer_admin: boolean;
};

/** @internal */
export const BenefitLicenseKeyActivationProperties$outboundSchema: z.ZodType<
  BenefitLicenseKeyActivationProperties$Outbound,
  z.ZodTypeDef,
  BenefitLicenseKeyActivationProperties
> = z.object({
  limit: z.number().int(),
  enableCustomerAdmin: z.boolean(),
}).transform((v) => {
  return remap$(v, {
    enableCustomerAdmin: "enable_customer_admin",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitLicenseKeyActivationProperties$ {
  /** @deprecated use `BenefitLicenseKeyActivationProperties$inboundSchema` instead. */
  export const inboundSchema =
    BenefitLicenseKeyActivationProperties$inboundSchema;
  /** @deprecated use `BenefitLicenseKeyActivationProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitLicenseKeyActivationProperties$outboundSchema;
  /** @deprecated use `BenefitLicenseKeyActivationProperties$Outbound` instead. */
  export type Outbound = BenefitLicenseKeyActivationProperties$Outbound;
}

export function benefitLicenseKeyActivationPropertiesToJSON(
  benefitLicenseKeyActivationProperties: BenefitLicenseKeyActivationProperties,
): string {
  return JSON.stringify(
    BenefitLicenseKeyActivationProperties$outboundSchema.parse(
      benefitLicenseKeyActivationProperties,
    ),
  );
}

export function benefitLicenseKeyActivationPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<BenefitLicenseKeyActivationProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitLicenseKeyActivationProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitLicenseKeyActivationProperties' from JSON`,
  );
}
