/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  DiscountDuration,
  DiscountDuration$inboundSchema,
  DiscountDuration$outboundSchema,
} from "./discountduration.js";
import {
  DiscountProduct,
  DiscountProduct$inboundSchema,
  DiscountProduct$Outbound,
  DiscountProduct$outboundSchema,
} from "./discountproduct.js";
import {
  DiscountType,
  DiscountType$inboundSchema,
  DiscountType$outboundSchema,
} from "./discounttype.js";

export type DiscountPercentageRepeatDurationMetadata =
  | string
  | number
  | number
  | boolean;

/**
 * Schema for a percentage discount that is applied on every invoice
 *
 * @remarks
 * for a certain number of months.
 */
export type DiscountPercentageRepeatDuration = {
  duration: DiscountDuration;
  durationInMonths: number;
  type: DiscountType;
  basisPoints: number;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Name of the discount. Will be displayed to the customer when the discount is applied.
   */
  name: string;
  /**
   * Code customers can use to apply the discount during checkout.
   */
  code: string | null;
  /**
   * Timestamp after which the discount is redeemable.
   */
  startsAt: Date | null;
  /**
   * Timestamp after which the discount is no longer redeemable.
   */
  endsAt: Date | null;
  /**
   * Maximum number of times the discount can be redeemed.
   */
  maxRedemptions: number | null;
  /**
   * Number of times the discount has been redeemed.
   */
  redemptionsCount: number;
  /**
   * The organization ID.
   */
  organizationId: string;
  products: Array<DiscountProduct>;
};

/** @internal */
export const DiscountPercentageRepeatDurationMetadata$inboundSchema: z.ZodType<
  DiscountPercentageRepeatDurationMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type DiscountPercentageRepeatDurationMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const DiscountPercentageRepeatDurationMetadata$outboundSchema: z.ZodType<
  DiscountPercentageRepeatDurationMetadata$Outbound,
  z.ZodTypeDef,
  DiscountPercentageRepeatDurationMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountPercentageRepeatDurationMetadata$ {
  /** @deprecated use `DiscountPercentageRepeatDurationMetadata$inboundSchema` instead. */
  export const inboundSchema =
    DiscountPercentageRepeatDurationMetadata$inboundSchema;
  /** @deprecated use `DiscountPercentageRepeatDurationMetadata$outboundSchema` instead. */
  export const outboundSchema =
    DiscountPercentageRepeatDurationMetadata$outboundSchema;
  /** @deprecated use `DiscountPercentageRepeatDurationMetadata$Outbound` instead. */
  export type Outbound = DiscountPercentageRepeatDurationMetadata$Outbound;
}

export function discountPercentageRepeatDurationMetadataToJSON(
  discountPercentageRepeatDurationMetadata:
    DiscountPercentageRepeatDurationMetadata,
): string {
  return JSON.stringify(
    DiscountPercentageRepeatDurationMetadata$outboundSchema.parse(
      discountPercentageRepeatDurationMetadata,
    ),
  );
}

export function discountPercentageRepeatDurationMetadataFromJSON(
  jsonString: string,
): SafeParseResult<
  DiscountPercentageRepeatDurationMetadata,
  SDKValidationError
> {
  return safeParse(
    jsonString,
    (x) =>
      DiscountPercentageRepeatDurationMetadata$inboundSchema.parse(
        JSON.parse(x),
      ),
    `Failed to parse 'DiscountPercentageRepeatDurationMetadata' from JSON`,
  );
}

/** @internal */
export const DiscountPercentageRepeatDuration$inboundSchema: z.ZodType<
  DiscountPercentageRepeatDuration,
  z.ZodTypeDef,
  unknown
> = z.object({
  duration: DiscountDuration$inboundSchema,
  duration_in_months: z.number().int(),
  type: DiscountType$inboundSchema,
  basis_points: z.number().int(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  name: z.string(),
  code: z.nullable(z.string()),
  starts_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  ends_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  max_redemptions: z.nullable(z.number().int()),
  redemptions_count: z.number().int(),
  organization_id: z.string(),
  products: z.array(DiscountProduct$inboundSchema),
}).transform((v) => {
  return remap$(v, {
    "duration_in_months": "durationInMonths",
    "basis_points": "basisPoints",
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "starts_at": "startsAt",
    "ends_at": "endsAt",
    "max_redemptions": "maxRedemptions",
    "redemptions_count": "redemptionsCount",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type DiscountPercentageRepeatDuration$Outbound = {
  duration: string;
  duration_in_months: number;
  type: string;
  basis_points: number;
  created_at: string;
  modified_at: string | null;
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  name: string;
  code: string | null;
  starts_at: string | null;
  ends_at: string | null;
  max_redemptions: number | null;
  redemptions_count: number;
  organization_id: string;
  products: Array<DiscountProduct$Outbound>;
};

/** @internal */
export const DiscountPercentageRepeatDuration$outboundSchema: z.ZodType<
  DiscountPercentageRepeatDuration$Outbound,
  z.ZodTypeDef,
  DiscountPercentageRepeatDuration
> = z.object({
  duration: DiscountDuration$outboundSchema,
  durationInMonths: z.number().int(),
  type: DiscountType$outboundSchema,
  basisPoints: z.number().int(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  name: z.string(),
  code: z.nullable(z.string()),
  startsAt: z.nullable(z.date().transform(v => v.toISOString())),
  endsAt: z.nullable(z.date().transform(v => v.toISOString())),
  maxRedemptions: z.nullable(z.number().int()),
  redemptionsCount: z.number().int(),
  organizationId: z.string(),
  products: z.array(DiscountProduct$outboundSchema),
}).transform((v) => {
  return remap$(v, {
    durationInMonths: "duration_in_months",
    basisPoints: "basis_points",
    createdAt: "created_at",
    modifiedAt: "modified_at",
    startsAt: "starts_at",
    endsAt: "ends_at",
    maxRedemptions: "max_redemptions",
    redemptionsCount: "redemptions_count",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace DiscountPercentageRepeatDuration$ {
  /** @deprecated use `DiscountPercentageRepeatDuration$inboundSchema` instead. */
  export const inboundSchema = DiscountPercentageRepeatDuration$inboundSchema;
  /** @deprecated use `DiscountPercentageRepeatDuration$outboundSchema` instead. */
  export const outboundSchema = DiscountPercentageRepeatDuration$outboundSchema;
  /** @deprecated use `DiscountPercentageRepeatDuration$Outbound` instead. */
  export type Outbound = DiscountPercentageRepeatDuration$Outbound;
}

export function discountPercentageRepeatDurationToJSON(
  discountPercentageRepeatDuration: DiscountPercentageRepeatDuration,
): string {
  return JSON.stringify(
    DiscountPercentageRepeatDuration$outboundSchema.parse(
      discountPercentageRepeatDuration,
    ),
  );
}

export function discountPercentageRepeatDurationFromJSON(
  jsonString: string,
): SafeParseResult<DiscountPercentageRepeatDuration, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => DiscountPercentageRepeatDuration$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'DiscountPercentageRepeatDuration' from JSON`,
  );
}
