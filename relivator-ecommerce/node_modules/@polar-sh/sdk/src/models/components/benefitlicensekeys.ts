/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitLicenseKeysProperties,
  BenefitLicenseKeysProperties$inboundSchema,
  BenefitLicenseKeysProperties$Outbound,
  BenefitLicenseKeysProperties$outboundSchema,
} from "./benefitlicensekeysproperties.js";

export type BenefitLicenseKeysMetadata = string | number | number | boolean;

export type BenefitLicenseKeys = {
  /**
   * The ID of the benefit.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  type?: "license_keys" | undefined;
  /**
   * The description of the benefit.
   */
  description: string;
  /**
   * Whether the benefit is selectable when creating a product.
   */
  selectable: boolean;
  /**
   * Whether the benefit is deletable.
   */
  deletable: boolean;
  /**
   * The ID of the organization owning the benefit.
   */
  organizationId: string;
  metadata: { [k: string]: string | number | number | boolean };
  properties: BenefitLicenseKeysProperties;
};

/** @internal */
export const BenefitLicenseKeysMetadata$inboundSchema: z.ZodType<
  BenefitLicenseKeysMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitLicenseKeysMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitLicenseKeysMetadata$outboundSchema: z.ZodType<
  BenefitLicenseKeysMetadata$Outbound,
  z.ZodTypeDef,
  BenefitLicenseKeysMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitLicenseKeysMetadata$ {
  /** @deprecated use `BenefitLicenseKeysMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitLicenseKeysMetadata$inboundSchema;
  /** @deprecated use `BenefitLicenseKeysMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitLicenseKeysMetadata$outboundSchema;
  /** @deprecated use `BenefitLicenseKeysMetadata$Outbound` instead. */
  export type Outbound = BenefitLicenseKeysMetadata$Outbound;
}

export function benefitLicenseKeysMetadataToJSON(
  benefitLicenseKeysMetadata: BenefitLicenseKeysMetadata,
): string {
  return JSON.stringify(
    BenefitLicenseKeysMetadata$outboundSchema.parse(benefitLicenseKeysMetadata),
  );
}

export function benefitLicenseKeysMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitLicenseKeysMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitLicenseKeysMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitLicenseKeysMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitLicenseKeys$inboundSchema: z.ZodType<
  BenefitLicenseKeys,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  type: z.literal("license_keys").optional(),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organization_id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  properties: BenefitLicenseKeysProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitLicenseKeys$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  type: "license_keys";
  description: string;
  selectable: boolean;
  deletable: boolean;
  organization_id: string;
  metadata: { [k: string]: string | number | number | boolean };
  properties: BenefitLicenseKeysProperties$Outbound;
};

/** @internal */
export const BenefitLicenseKeys$outboundSchema: z.ZodType<
  BenefitLicenseKeys$Outbound,
  z.ZodTypeDef,
  BenefitLicenseKeys
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  type: z.literal("license_keys").default("license_keys" as const),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organizationId: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  properties: BenefitLicenseKeysProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitLicenseKeys$ {
  /** @deprecated use `BenefitLicenseKeys$inboundSchema` instead. */
  export const inboundSchema = BenefitLicenseKeys$inboundSchema;
  /** @deprecated use `BenefitLicenseKeys$outboundSchema` instead. */
  export const outboundSchema = BenefitLicenseKeys$outboundSchema;
  /** @deprecated use `BenefitLicenseKeys$Outbound` instead. */
  export type Outbound = BenefitLicenseKeys$Outbound;
}

export function benefitLicenseKeysToJSON(
  benefitLicenseKeys: BenefitLicenseKeys,
): string {
  return JSON.stringify(
    BenefitLicenseKeys$outboundSchema.parse(benefitLicenseKeys),
  );
}

export function benefitLicenseKeysFromJSON(
  jsonString: string,
): SafeParseResult<BenefitLicenseKeys, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitLicenseKeys$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitLicenseKeys' from JSON`,
  );
}
