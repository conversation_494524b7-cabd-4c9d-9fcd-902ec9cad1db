/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * Properties available to subscribers for a benefit of type `discord`.
 */
export type BenefitDiscordSubscriberProperties = {
  /**
   * The ID of the Discord server.
   */
  guildId: string;
};

/** @internal */
export const BenefitDiscordSubscriberProperties$inboundSchema: z.ZodType<
  BenefitDiscordSubscriberProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  guild_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "guild_id": "guildId",
  });
});

/** @internal */
export type BenefitDiscordSubscriberProperties$Outbound = {
  guild_id: string;
};

/** @internal */
export const BenefitDiscordSubscriberProperties$outboundSchema: z.ZodType<
  BenefitDiscordSubscriberProperties$Outbound,
  z.ZodTypeDef,
  BenefitDiscordSubscriberProperties
> = z.object({
  guildId: z.string(),
}).transform((v) => {
  return remap$(v, {
    guildId: "guild_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDiscordSubscriberProperties$ {
  /** @deprecated use `BenefitDiscordSubscriberProperties$inboundSchema` instead. */
  export const inboundSchema = BenefitDiscordSubscriberProperties$inboundSchema;
  /** @deprecated use `BenefitDiscordSubscriberProperties$outboundSchema` instead. */
  export const outboundSchema =
    BenefitDiscordSubscriberProperties$outboundSchema;
  /** @deprecated use `BenefitDiscordSubscriberProperties$Outbound` instead. */
  export type Outbound = BenefitDiscordSubscriberProperties$Outbound;
}

export function benefitDiscordSubscriberPropertiesToJSON(
  benefitDiscordSubscriberProperties: BenefitDiscordSubscriberProperties,
): string {
  return JSON.stringify(
    BenefitDiscordSubscriberProperties$outboundSchema.parse(
      benefitDiscordSubscriberProperties,
    ),
  );
}

export function benefitDiscordSubscriberPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<BenefitDiscordSubscriberProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitDiscordSubscriberProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitDiscordSubscriberProperties' from JSON`,
  );
}
