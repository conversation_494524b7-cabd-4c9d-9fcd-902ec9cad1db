/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitCustom,
  BenefitCustom$inboundSchema,
  BenefitCustom$Outbound,
  BenefitCustom$outboundSchema,
} from "./benefitcustom.js";
import {
  BenefitDiscord,
  BenefitDiscord$inboundSchema,
  BenefitDiscord$Outbound,
  BenefitDiscord$outboundSchema,
} from "./benefitdiscord.js";
import {
  BenefitDownloadables,
  BenefitDownloadables$inboundSchema,
  BenefitDownloadables$Outbound,
  BenefitDownloadables$outboundSchema,
} from "./benefitdownloadables.js";
import {
  BenefitGitHubRepository,
  BenefitGitHubRepository$inboundSchema,
  BenefitGitHubRepository$Outbound,
  BenefitGitHubRepository$outboundSchema,
} from "./benefitgithubrepository.js";
import {
  BenefitLicenseKeys,
  BenefitLicenseKeys$inboundSchema,
  BenefitLicenseKeys$Outbound,
  BenefitLicenseKeys$outboundSchema,
} from "./benefitlicensekeys.js";
import {
  BenefitMeterCredit,
  BenefitMeterCredit$inboundSchema,
  BenefitMeterCredit$Outbound,
  BenefitMeterCredit$outboundSchema,
} from "./benefitmetercredit.js";

export type Benefit =
  | BenefitCustom
  | BenefitDiscord
  | BenefitGitHubRepository
  | BenefitDownloadables
  | BenefitLicenseKeys
  | BenefitMeterCredit;

/** @internal */
export const Benefit$inboundSchema: z.ZodType<Benefit, z.ZodTypeDef, unknown> =
  z.union([
    BenefitCustom$inboundSchema,
    BenefitDiscord$inboundSchema,
    BenefitGitHubRepository$inboundSchema,
    BenefitDownloadables$inboundSchema,
    BenefitLicenseKeys$inboundSchema,
    BenefitMeterCredit$inboundSchema,
  ]);

/** @internal */
export type Benefit$Outbound =
  | BenefitCustom$Outbound
  | BenefitDiscord$Outbound
  | BenefitGitHubRepository$Outbound
  | BenefitDownloadables$Outbound
  | BenefitLicenseKeys$Outbound
  | BenefitMeterCredit$Outbound;

/** @internal */
export const Benefit$outboundSchema: z.ZodType<
  Benefit$Outbound,
  z.ZodTypeDef,
  Benefit
> = z.union([
  BenefitCustom$outboundSchema,
  BenefitDiscord$outboundSchema,
  BenefitGitHubRepository$outboundSchema,
  BenefitDownloadables$outboundSchema,
  BenefitLicenseKeys$outboundSchema,
  BenefitMeterCredit$outboundSchema,
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Benefit$ {
  /** @deprecated use `Benefit$inboundSchema` instead. */
  export const inboundSchema = Benefit$inboundSchema;
  /** @deprecated use `Benefit$outboundSchema` instead. */
  export const outboundSchema = Benefit$outboundSchema;
  /** @deprecated use `Benefit$Outbound` instead. */
  export type Outbound = Benefit$Outbound;
}

export function benefitToJSON(benefit: Benefit): string {
  return JSON.stringify(Benefit$outboundSchema.parse(benefit));
}

export function benefitFromJSON(
  jsonString: string,
): SafeParseResult<Benefit, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Benefit$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Benefit' from JSON`,
  );
}
