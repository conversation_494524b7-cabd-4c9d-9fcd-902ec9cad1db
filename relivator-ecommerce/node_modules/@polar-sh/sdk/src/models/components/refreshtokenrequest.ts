/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type RefreshTokenRequest = {
  grantType?: "refresh_token" | undefined;
  clientId: string;
  clientSecret: string;
  refreshToken: string;
};

/** @internal */
export const RefreshTokenRequest$inboundSchema: z.ZodType<
  RefreshTokenRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  grant_type: z.literal("refresh_token").optional(),
  client_id: z.string(),
  client_secret: z.string(),
  refresh_token: z.string(),
}).transform((v) => {
  return remap$(v, {
    "grant_type": "grantType",
    "client_id": "clientId",
    "client_secret": "clientSecret",
    "refresh_token": "refreshToken",
  });
});

/** @internal */
export type RefreshTokenRequest$Outbound = {
  grant_type: "refresh_token";
  client_id: string;
  client_secret: string;
  refresh_token: string;
};

/** @internal */
export const RefreshTokenRequest$outboundSchema: z.ZodType<
  RefreshTokenRequest$Outbound,
  z.ZodTypeDef,
  RefreshTokenRequest
> = z.object({
  grantType: z.literal("refresh_token").default("refresh_token" as const),
  clientId: z.string(),
  clientSecret: z.string(),
  refreshToken: z.string(),
}).transform((v) => {
  return remap$(v, {
    grantType: "grant_type",
    clientId: "client_id",
    clientSecret: "client_secret",
    refreshToken: "refresh_token",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace RefreshTokenRequest$ {
  /** @deprecated use `RefreshTokenRequest$inboundSchema` instead. */
  export const inboundSchema = RefreshTokenRequest$inboundSchema;
  /** @deprecated use `RefreshTokenRequest$outboundSchema` instead. */
  export const outboundSchema = RefreshTokenRequest$outboundSchema;
  /** @deprecated use `RefreshTokenRequest$Outbound` instead. */
  export type Outbound = RefreshTokenRequest$Outbound;
}

export function refreshTokenRequestToJSON(
  refreshTokenRequest: RefreshTokenRequest,
): string {
  return JSON.stringify(
    RefreshTokenRequest$outboundSchema.parse(refreshTokenRequest),
  );
}

export function refreshTokenRequestFromJSON(
  jsonString: string,
): SafeParseResult<RefreshTokenRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => RefreshTokenRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'RefreshTokenRequest' from JSON`,
  );
}
