/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitDiscordProperties,
  BenefitDiscordProperties$inboundSchema,
  BenefitDiscordProperties$Outbound,
  BenefitDiscordProperties$outboundSchema,
} from "./benefitdiscordproperties.js";

export type BenefitDiscordMetadata = string | number | number | boolean;

/**
 * A benefit of type `discord`.
 *
 * @remarks
 *
 * Use it to automatically invite your backers to a Discord server.
 */
export type BenefitDiscord = {
  /**
   * The ID of the benefit.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  type?: "discord" | undefined;
  /**
   * The description of the benefit.
   */
  description: string;
  /**
   * Whether the benefit is selectable when creating a product.
   */
  selectable: boolean;
  /**
   * Whether the benefit is deletable.
   */
  deletable: boolean;
  /**
   * The ID of the organization owning the benefit.
   */
  organizationId: string;
  metadata: { [k: string]: string | number | number | boolean };
  /**
   * Properties for a benefit of type `discord`.
   */
  properties: BenefitDiscordProperties;
};

/** @internal */
export const BenefitDiscordMetadata$inboundSchema: z.ZodType<
  BenefitDiscordMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitDiscordMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitDiscordMetadata$outboundSchema: z.ZodType<
  BenefitDiscordMetadata$Outbound,
  z.ZodTypeDef,
  BenefitDiscordMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDiscordMetadata$ {
  /** @deprecated use `BenefitDiscordMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitDiscordMetadata$inboundSchema;
  /** @deprecated use `BenefitDiscordMetadata$outboundSchema` instead. */
  export const outboundSchema = BenefitDiscordMetadata$outboundSchema;
  /** @deprecated use `BenefitDiscordMetadata$Outbound` instead. */
  export type Outbound = BenefitDiscordMetadata$Outbound;
}

export function benefitDiscordMetadataToJSON(
  benefitDiscordMetadata: BenefitDiscordMetadata,
): string {
  return JSON.stringify(
    BenefitDiscordMetadata$outboundSchema.parse(benefitDiscordMetadata),
  );
}

export function benefitDiscordMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitDiscordMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitDiscordMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitDiscordMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitDiscord$inboundSchema: z.ZodType<
  BenefitDiscord,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  type: z.literal("discord").optional(),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organization_id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  properties: BenefitDiscordProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitDiscord$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  type: "discord";
  description: string;
  selectable: boolean;
  deletable: boolean;
  organization_id: string;
  metadata: { [k: string]: string | number | number | boolean };
  properties: BenefitDiscordProperties$Outbound;
};

/** @internal */
export const BenefitDiscord$outboundSchema: z.ZodType<
  BenefitDiscord$Outbound,
  z.ZodTypeDef,
  BenefitDiscord
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  type: z.literal("discord").default("discord" as const),
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organizationId: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  properties: BenefitDiscordProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDiscord$ {
  /** @deprecated use `BenefitDiscord$inboundSchema` instead. */
  export const inboundSchema = BenefitDiscord$inboundSchema;
  /** @deprecated use `BenefitDiscord$outboundSchema` instead. */
  export const outboundSchema = BenefitDiscord$outboundSchema;
  /** @deprecated use `BenefitDiscord$Outbound` instead. */
  export type Outbound = BenefitDiscord$Outbound;
}

export function benefitDiscordToJSON(benefitDiscord: BenefitDiscord): string {
  return JSON.stringify(BenefitDiscord$outboundSchema.parse(benefitDiscord));
}

export function benefitDiscordFromJSON(
  jsonString: string,
): SafeParseResult<BenefitDiscord, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitDiscord$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitDiscord' from JSON`,
  );
}
