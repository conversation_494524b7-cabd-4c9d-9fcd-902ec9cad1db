/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { ClosedEnum } from "../../types/enums.js";

export const MeterSortProperty = {
  CreatedAt: "created_at",
  MinusCreatedAt: "-created_at",
  Name: "name",
  MinusName: "-name",
} as const;
export type MeterSortProperty = ClosedEnum<typeof MeterSortProperty>;

/** @internal */
export const MeterSortProperty$inboundSchema: z.ZodNativeEnum<
  typeof MeterSortProperty
> = z.nativeEnum(MeterSortProperty);

/** @internal */
export const MeterSortProperty$outboundSchema: z.<PERSON>odNative<PERSON>num<
  typeof MeterSortProperty
> = MeterSortProperty$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace MeterSortProperty$ {
  /** @deprecated use `MeterSortProperty$inboundSchema` instead. */
  export const inboundSchema = MeterSortProperty$inboundSchema;
  /** @deprecated use `MeterSortProperty$outboundSchema` instead. */
  export const outboundSchema = MeterSortProperty$outboundSchema;
}
