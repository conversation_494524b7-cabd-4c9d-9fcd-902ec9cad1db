/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const TokenTypeHint = {
  AccessToken: "access_token",
  RefreshToken: "refresh_token",
} as const;
export type TokenTypeHint = ClosedEnum<typeof TokenTypeHint>;

export type RevokeTokenRequest = {
  token: string;
  tokenTypeHint?: TokenTypeHint | null | undefined;
  clientId: string;
  clientSecret: string;
};

/** @internal */
export const TokenTypeHint$inboundSchema: z.ZodNativeEnum<
  typeof TokenTypeHint
> = z.nativeEnum(TokenTypeHint);

/** @internal */
export const TokenTypeHint$outboundSchema: z.ZodNativeEnum<
  typeof TokenTypeHint
> = TokenTypeHint$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace TokenTypeHint$ {
  /** @deprecated use `TokenTypeHint$inboundSchema` instead. */
  export const inboundSchema = TokenTypeHint$inboundSchema;
  /** @deprecated use `TokenTypeHint$outboundSchema` instead. */
  export const outboundSchema = TokenTypeHint$outboundSchema;
}

/** @internal */
export const RevokeTokenRequest$inboundSchema: z.ZodType<
  RevokeTokenRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  token: z.string(),
  token_type_hint: z.nullable(TokenTypeHint$inboundSchema).optional(),
  client_id: z.string(),
  client_secret: z.string(),
}).transform((v) => {
  return remap$(v, {
    "token_type_hint": "tokenTypeHint",
    "client_id": "clientId",
    "client_secret": "clientSecret",
  });
});

/** @internal */
export type RevokeTokenRequest$Outbound = {
  token: string;
  token_type_hint?: string | null | undefined;
  client_id: string;
  client_secret: string;
};

/** @internal */
export const RevokeTokenRequest$outboundSchema: z.ZodType<
  RevokeTokenRequest$Outbound,
  z.ZodTypeDef,
  RevokeTokenRequest
> = z.object({
  token: z.string(),
  tokenTypeHint: z.nullable(TokenTypeHint$outboundSchema).optional(),
  clientId: z.string(),
  clientSecret: z.string(),
}).transform((v) => {
  return remap$(v, {
    tokenTypeHint: "token_type_hint",
    clientId: "client_id",
    clientSecret: "client_secret",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace RevokeTokenRequest$ {
  /** @deprecated use `RevokeTokenRequest$inboundSchema` instead. */
  export const inboundSchema = RevokeTokenRequest$inboundSchema;
  /** @deprecated use `RevokeTokenRequest$outboundSchema` instead. */
  export const outboundSchema = RevokeTokenRequest$outboundSchema;
  /** @deprecated use `RevokeTokenRequest$Outbound` instead. */
  export type Outbound = RevokeTokenRequest$Outbound;
}

export function revokeTokenRequestToJSON(
  revokeTokenRequest: RevokeTokenRequest,
): string {
  return JSON.stringify(
    RevokeTokenRequest$outboundSchema.parse(revokeTokenRequest),
  );
}

export function revokeTokenRequestFromJSON(
  jsonString: string,
): SafeParseResult<RevokeTokenRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => RevokeTokenRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'RevokeTokenRequest' from JSON`,
  );
}
