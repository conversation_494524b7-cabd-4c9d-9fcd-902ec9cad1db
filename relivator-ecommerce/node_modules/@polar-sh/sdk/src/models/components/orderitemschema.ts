/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

/**
 * An order line item.
 */
export type OrderItemSchema = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  /**
   * Description of the line item charge.
   */
  label: string;
  /**
   * Amount in cents, before discounts and taxes.
   */
  amount: number;
  /**
   * Sales tax amount in cents.
   */
  taxAmount: number;
  /**
   * Whether this charge is due to a proration.
   */
  proration: boolean;
  /**
   * Associated price ID, if any.
   */
  productPriceId: string | null;
};

/** @internal */
export const OrderItemSchema$inboundSchema: z.ZodType<
  OrderItemSchema,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  label: z.string(),
  amount: z.number().int(),
  tax_amount: z.number().int(),
  proration: z.boolean(),
  product_price_id: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "tax_amount": "taxAmount",
    "product_price_id": "productPriceId",
  });
});

/** @internal */
export type OrderItemSchema$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  label: string;
  amount: number;
  tax_amount: number;
  proration: boolean;
  product_price_id: string | null;
};

/** @internal */
export const OrderItemSchema$outboundSchema: z.ZodType<
  OrderItemSchema$Outbound,
  z.ZodTypeDef,
  OrderItemSchema
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  label: z.string(),
  amount: z.number().int(),
  taxAmount: z.number().int(),
  proration: z.boolean(),
  productPriceId: z.nullable(z.string()),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    taxAmount: "tax_amount",
    productPriceId: "product_price_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace OrderItemSchema$ {
  /** @deprecated use `OrderItemSchema$inboundSchema` instead. */
  export const inboundSchema = OrderItemSchema$inboundSchema;
  /** @deprecated use `OrderItemSchema$outboundSchema` instead. */
  export const outboundSchema = OrderItemSchema$outboundSchema;
  /** @deprecated use `OrderItemSchema$Outbound` instead. */
  export type Outbound = OrderItemSchema$Outbound;
}

export function orderItemSchemaToJSON(
  orderItemSchema: OrderItemSchema,
): string {
  return JSON.stringify(OrderItemSchema$outboundSchema.parse(orderItemSchema));
}

export function orderItemSchemaFromJSON(
  jsonString: string,
): SafeParseResult<OrderItemSchema, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => OrderItemSchema$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'OrderItemSchema' from JSON`,
  );
}
