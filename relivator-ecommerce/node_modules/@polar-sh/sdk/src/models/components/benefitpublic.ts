/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitType,
  BenefitType$inboundSchema,
  BenefitType$outboundSchema,
} from "./benefittype.js";

export type BenefitPublic = {
  /**
   * The ID of the benefit.
   */
  id: string;
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  type: BenefitType;
  /**
   * The description of the benefit.
   */
  description: string;
  /**
   * Whether the benefit is selectable when creating a product.
   */
  selectable: boolean;
  /**
   * Whether the benefit is deletable.
   */
  deletable: boolean;
  /**
   * The ID of the organization owning the benefit.
   */
  organizationId: string;
};

/** @internal */
export const BenefitPublic$inboundSchema: z.ZodType<
  BenefitPublic,
  z.ZodTypeDef,
  unknown
> = z.object({
  id: z.string(),
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  type: BenefitType$inboundSchema,
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organization_id: z.string(),
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitPublic$Outbound = {
  id: string;
  created_at: string;
  modified_at: string | null;
  type: string;
  description: string;
  selectable: boolean;
  deletable: boolean;
  organization_id: string;
};

/** @internal */
export const BenefitPublic$outboundSchema: z.ZodType<
  BenefitPublic$Outbound,
  z.ZodTypeDef,
  BenefitPublic
> = z.object({
  id: z.string(),
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  type: BenefitType$outboundSchema,
  description: z.string(),
  selectable: z.boolean(),
  deletable: z.boolean(),
  organizationId: z.string(),
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitPublic$ {
  /** @deprecated use `BenefitPublic$inboundSchema` instead. */
  export const inboundSchema = BenefitPublic$inboundSchema;
  /** @deprecated use `BenefitPublic$outboundSchema` instead. */
  export const outboundSchema = BenefitPublic$outboundSchema;
  /** @deprecated use `BenefitPublic$Outbound` instead. */
  export type Outbound = BenefitPublic$Outbound;
}

export function benefitPublicToJSON(benefitPublic: BenefitPublic): string {
  return JSON.stringify(BenefitPublic$outboundSchema.parse(benefitPublic));
}

export function benefitPublicFromJSON(
  jsonString: string,
): SafeParseResult<BenefitPublic, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitPublic$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitPublic' from JSON`,
  );
}
