/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomFieldCheckboxProperties,
  CustomFieldCheckboxProperties$inboundSchema,
  CustomFieldCheckboxProperties$Outbound,
  CustomFieldCheckboxProperties$outboundSchema,
} from "./customfieldcheckboxproperties.js";

export type CustomFieldCheckboxMetadata = string | number | number | boolean;

/**
 * Schema for a custom field of type checkbox.
 */
export type CustomFieldCheckbox = {
  /**
   * Creation timestamp of the object.
   */
  createdAt: Date;
  /**
   * Last modification timestamp of the object.
   */
  modifiedAt: Date | null;
  /**
   * The ID of the object.
   */
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  type?: "checkbox" | undefined;
  /**
   * Identifier of the custom field. It'll be used as key when storing the value.
   */
  slug: string;
  /**
   * Name of the custom field.
   */
  name: string;
  /**
   * The ID of the organization owning the custom field.
   */
  organizationId: string;
  properties: CustomFieldCheckboxProperties;
};

/** @internal */
export const CustomFieldCheckboxMetadata$inboundSchema: z.ZodType<
  CustomFieldCheckboxMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomFieldCheckboxMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomFieldCheckboxMetadata$outboundSchema: z.ZodType<
  CustomFieldCheckboxMetadata$Outbound,
  z.ZodTypeDef,
  CustomFieldCheckboxMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldCheckboxMetadata$ {
  /** @deprecated use `CustomFieldCheckboxMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomFieldCheckboxMetadata$inboundSchema;
  /** @deprecated use `CustomFieldCheckboxMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomFieldCheckboxMetadata$outboundSchema;
  /** @deprecated use `CustomFieldCheckboxMetadata$Outbound` instead. */
  export type Outbound = CustomFieldCheckboxMetadata$Outbound;
}

export function customFieldCheckboxMetadataToJSON(
  customFieldCheckboxMetadata: CustomFieldCheckboxMetadata,
): string {
  return JSON.stringify(
    CustomFieldCheckboxMetadata$outboundSchema.parse(
      customFieldCheckboxMetadata,
    ),
  );
}

export function customFieldCheckboxMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldCheckboxMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldCheckboxMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldCheckboxMetadata' from JSON`,
  );
}

/** @internal */
export const CustomFieldCheckbox$inboundSchema: z.ZodType<
  CustomFieldCheckbox,
  z.ZodTypeDef,
  unknown
> = z.object({
  created_at: z.string().datetime({ offset: true }).transform(v => new Date(v)),
  modified_at: z.nullable(
    z.string().datetime({ offset: true }).transform(v => new Date(v)),
  ),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  type: z.literal("checkbox").optional(),
  slug: z.string(),
  name: z.string(),
  organization_id: z.string(),
  properties: CustomFieldCheckboxProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "created_at": "createdAt",
    "modified_at": "modifiedAt",
    "organization_id": "organizationId",
  });
});

/** @internal */
export type CustomFieldCheckbox$Outbound = {
  created_at: string;
  modified_at: string | null;
  id: string;
  metadata: { [k: string]: string | number | number | boolean };
  type: "checkbox";
  slug: string;
  name: string;
  organization_id: string;
  properties: CustomFieldCheckboxProperties$Outbound;
};

/** @internal */
export const CustomFieldCheckbox$outboundSchema: z.ZodType<
  CustomFieldCheckbox$Outbound,
  z.ZodTypeDef,
  CustomFieldCheckbox
> = z.object({
  createdAt: z.date().transform(v => v.toISOString()),
  modifiedAt: z.nullable(z.date().transform(v => v.toISOString())),
  id: z.string(),
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ),
  type: z.literal("checkbox").default("checkbox" as const),
  slug: z.string(),
  name: z.string(),
  organizationId: z.string(),
  properties: CustomFieldCheckboxProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    createdAt: "created_at",
    modifiedAt: "modified_at",
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldCheckbox$ {
  /** @deprecated use `CustomFieldCheckbox$inboundSchema` instead. */
  export const inboundSchema = CustomFieldCheckbox$inboundSchema;
  /** @deprecated use `CustomFieldCheckbox$outboundSchema` instead. */
  export const outboundSchema = CustomFieldCheckbox$outboundSchema;
  /** @deprecated use `CustomFieldCheckbox$Outbound` instead. */
  export type Outbound = CustomFieldCheckbox$Outbound;
}

export function customFieldCheckboxToJSON(
  customFieldCheckbox: CustomFieldCheckbox,
): string {
  return JSON.stringify(
    CustomFieldCheckbox$outboundSchema.parse(customFieldCheckbox),
  );
}

export function customFieldCheckboxFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldCheckbox, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldCheckbox$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldCheckbox' from JSON`,
  );
}
