/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  CustomFieldCheckbox,
  CustomFieldCheckbox$inboundSchema,
  CustomFieldCheckbox$Outbound,
  CustomFieldCheckbox$outboundSchema,
} from "./customfieldcheckbox.js";
import {
  CustomFieldDate,
  CustomFieldDate$inboundSchema,
  CustomFieldDate$Outbound,
  CustomFieldDate$outboundSchema,
} from "./customfielddate.js";
import {
  CustomFieldNumber,
  CustomFieldNumber$inboundSchema,
  CustomFieldNumber$Outbound,
  CustomFieldNumber$outboundSchema,
} from "./customfieldnumber.js";
import {
  CustomFieldSelect,
  CustomFieldSelect$inboundSchema,
  CustomFieldSelect$Outbound,
  CustomFieldSelect$outboundSchema,
} from "./customfieldselect.js";
import {
  CustomFieldText,
  CustomFieldText$inboundSchema,
  CustomFieldText$Outbound,
  CustomFieldText$outboundSchema,
} from "./customfieldtext.js";

export type CustomField =
  | (CustomFieldCheckbox & { type: "checkbox" })
  | (CustomFieldDate & { type: "date" })
  | (CustomFieldNumber & { type: "number" })
  | (CustomFieldSelect & { type: "select" })
  | (CustomFieldText & { type: "text" });

/** @internal */
export const CustomField$inboundSchema: z.ZodType<
  CustomField,
  z.ZodTypeDef,
  unknown
> = z.union([
  CustomFieldCheckbox$inboundSchema.and(
    z.object({ type: z.literal("checkbox") }).transform((v) => ({
      type: v.type,
    })),
  ),
  CustomFieldDate$inboundSchema.and(
    z.object({ type: z.literal("date") }).transform((v) => ({ type: v.type })),
  ),
  CustomFieldNumber$inboundSchema.and(
    z.object({ type: z.literal("number") }).transform((v) => ({
      type: v.type,
    })),
  ),
  CustomFieldSelect$inboundSchema.and(
    z.object({ type: z.literal("select") }).transform((v) => ({
      type: v.type,
    })),
  ),
  CustomFieldText$inboundSchema.and(
    z.object({ type: z.literal("text") }).transform((v) => ({ type: v.type })),
  ),
]);

/** @internal */
export type CustomField$Outbound =
  | (CustomFieldCheckbox$Outbound & { type: "checkbox" })
  | (CustomFieldDate$Outbound & { type: "date" })
  | (CustomFieldNumber$Outbound & { type: "number" })
  | (CustomFieldSelect$Outbound & { type: "select" })
  | (CustomFieldText$Outbound & { type: "text" });

/** @internal */
export const CustomField$outboundSchema: z.ZodType<
  CustomField$Outbound,
  z.ZodTypeDef,
  CustomField
> = z.union([
  CustomFieldCheckbox$outboundSchema.and(
    z.object({ type: z.literal("checkbox") }).transform((v) => ({
      type: v.type,
    })),
  ),
  CustomFieldDate$outboundSchema.and(
    z.object({ type: z.literal("date") }).transform((v) => ({ type: v.type })),
  ),
  CustomFieldNumber$outboundSchema.and(
    z.object({ type: z.literal("number") }).transform((v) => ({
      type: v.type,
    })),
  ),
  CustomFieldSelect$outboundSchema.and(
    z.object({ type: z.literal("select") }).transform((v) => ({
      type: v.type,
    })),
  ),
  CustomFieldText$outboundSchema.and(
    z.object({ type: z.literal("text") }).transform((v) => ({ type: v.type })),
  ),
]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomField$ {
  /** @deprecated use `CustomField$inboundSchema` instead. */
  export const inboundSchema = CustomField$inboundSchema;
  /** @deprecated use `CustomField$outboundSchema` instead. */
  export const outboundSchema = CustomField$outboundSchema;
  /** @deprecated use `CustomField$Outbound` instead. */
  export type Outbound = CustomField$Outbound;
}

export function customFieldToJSON(customField: CustomField): string {
  return JSON.stringify(CustomField$outboundSchema.parse(customField));
}

export function customFieldFromJSON(
  jsonString: string,
): SafeParseResult<CustomField, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomField$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomField' from JSON`,
  );
}
