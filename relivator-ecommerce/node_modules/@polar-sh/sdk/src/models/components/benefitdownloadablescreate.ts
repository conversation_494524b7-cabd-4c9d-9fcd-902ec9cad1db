/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  BenefitDownloadablesCreateProperties,
  BenefitDownloadablesCreateProperties$inboundSchema,
  BenefitDownloadablesCreateProperties$Outbound,
  BenefitDownloadablesCreateProperties$outboundSchema,
} from "./benefitdownloadablescreateproperties.js";

export type BenefitDownloadablesCreateMetadata =
  | string
  | number
  | number
  | boolean;

export type BenefitDownloadablesCreate = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type?: "downloadables" | undefined;
  /**
   * The description of the benefit. Will be displayed on products having this benefit.
   */
  description: string;
  /**
   * The ID of the organization owning the benefit. **Required unless you use an organization token.**
   */
  organizationId?: string | null | undefined;
  properties: BenefitDownloadablesCreateProperties;
};

/** @internal */
export const BenefitDownloadablesCreateMetadata$inboundSchema: z.ZodType<
  BenefitDownloadablesCreateMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type BenefitDownloadablesCreateMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const BenefitDownloadablesCreateMetadata$outboundSchema: z.ZodType<
  BenefitDownloadablesCreateMetadata$Outbound,
  z.ZodTypeDef,
  BenefitDownloadablesCreateMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDownloadablesCreateMetadata$ {
  /** @deprecated use `BenefitDownloadablesCreateMetadata$inboundSchema` instead. */
  export const inboundSchema = BenefitDownloadablesCreateMetadata$inboundSchema;
  /** @deprecated use `BenefitDownloadablesCreateMetadata$outboundSchema` instead. */
  export const outboundSchema =
    BenefitDownloadablesCreateMetadata$outboundSchema;
  /** @deprecated use `BenefitDownloadablesCreateMetadata$Outbound` instead. */
  export type Outbound = BenefitDownloadablesCreateMetadata$Outbound;
}

export function benefitDownloadablesCreateMetadataToJSON(
  benefitDownloadablesCreateMetadata: BenefitDownloadablesCreateMetadata,
): string {
  return JSON.stringify(
    BenefitDownloadablesCreateMetadata$outboundSchema.parse(
      benefitDownloadablesCreateMetadata,
    ),
  );
}

export function benefitDownloadablesCreateMetadataFromJSON(
  jsonString: string,
): SafeParseResult<BenefitDownloadablesCreateMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) =>
      BenefitDownloadablesCreateMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitDownloadablesCreateMetadata' from JSON`,
  );
}

/** @internal */
export const BenefitDownloadablesCreate$inboundSchema: z.ZodType<
  BenefitDownloadablesCreate,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("downloadables").optional(),
  description: z.string(),
  organization_id: z.nullable(z.string()).optional(),
  properties: BenefitDownloadablesCreateProperties$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "organization_id": "organizationId",
  });
});

/** @internal */
export type BenefitDownloadablesCreate$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  type: "downloadables";
  description: string;
  organization_id?: string | null | undefined;
  properties: BenefitDownloadablesCreateProperties$Outbound;
};

/** @internal */
export const BenefitDownloadablesCreate$outboundSchema: z.ZodType<
  BenefitDownloadablesCreate$Outbound,
  z.ZodTypeDef,
  BenefitDownloadablesCreate
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  type: z.literal("downloadables").default("downloadables" as const),
  description: z.string(),
  organizationId: z.nullable(z.string()).optional(),
  properties: BenefitDownloadablesCreateProperties$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    organizationId: "organization_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace BenefitDownloadablesCreate$ {
  /** @deprecated use `BenefitDownloadablesCreate$inboundSchema` instead. */
  export const inboundSchema = BenefitDownloadablesCreate$inboundSchema;
  /** @deprecated use `BenefitDownloadablesCreate$outboundSchema` instead. */
  export const outboundSchema = BenefitDownloadablesCreate$outboundSchema;
  /** @deprecated use `BenefitDownloadablesCreate$Outbound` instead. */
  export type Outbound = BenefitDownloadablesCreate$Outbound;
}

export function benefitDownloadablesCreateToJSON(
  benefitDownloadablesCreate: BenefitDownloadablesCreate,
): string {
  return JSON.stringify(
    BenefitDownloadablesCreate$outboundSchema.parse(benefitDownloadablesCreate),
  );
}

export function benefitDownloadablesCreateFromJSON(
  jsonString: string,
): SafeParseResult<BenefitDownloadablesCreate, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => BenefitDownloadablesCreate$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'BenefitDownloadablesCreate' from JSON`,
  );
}
