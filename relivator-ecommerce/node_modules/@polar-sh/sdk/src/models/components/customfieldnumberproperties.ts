/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type CustomFieldNumberProperties = {
  formLabel?: string | undefined;
  formHelpText?: string | undefined;
  formPlaceholder?: string | undefined;
  ge?: number | undefined;
  le?: number | undefined;
};

/** @internal */
export const CustomFieldNumberProperties$inboundSchema: z.ZodType<
  CustomFieldNumberProperties,
  z.ZodTypeDef,
  unknown
> = z.object({
  form_label: z.string().optional(),
  form_help_text: z.string().optional(),
  form_placeholder: z.string().optional(),
  ge: z.number().int().optional(),
  le: z.number().int().optional(),
}).transform((v) => {
  return remap$(v, {
    "form_label": "formLabel",
    "form_help_text": "formHelpText",
    "form_placeholder": "formPlaceholder",
  });
});

/** @internal */
export type CustomFieldNumberProperties$Outbound = {
  form_label?: string | undefined;
  form_help_text?: string | undefined;
  form_placeholder?: string | undefined;
  ge?: number | undefined;
  le?: number | undefined;
};

/** @internal */
export const CustomFieldNumberProperties$outboundSchema: z.ZodType<
  CustomFieldNumberProperties$Outbound,
  z.ZodTypeDef,
  CustomFieldNumberProperties
> = z.object({
  formLabel: z.string().optional(),
  formHelpText: z.string().optional(),
  formPlaceholder: z.string().optional(),
  ge: z.number().int().optional(),
  le: z.number().int().optional(),
}).transform((v) => {
  return remap$(v, {
    formLabel: "form_label",
    formHelpText: "form_help_text",
    formPlaceholder: "form_placeholder",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomFieldNumberProperties$ {
  /** @deprecated use `CustomFieldNumberProperties$inboundSchema` instead. */
  export const inboundSchema = CustomFieldNumberProperties$inboundSchema;
  /** @deprecated use `CustomFieldNumberProperties$outboundSchema` instead. */
  export const outboundSchema = CustomFieldNumberProperties$outboundSchema;
  /** @deprecated use `CustomFieldNumberProperties$Outbound` instead. */
  export type Outbound = CustomFieldNumberProperties$Outbound;
}

export function customFieldNumberPropertiesToJSON(
  customFieldNumberProperties: CustomFieldNumberProperties,
): string {
  return JSON.stringify(
    CustomFieldNumberProperties$outboundSchema.parse(
      customFieldNumberProperties,
    ),
  );
}

export function customFieldNumberPropertiesFromJSON(
  jsonString: string,
): SafeParseResult<CustomFieldNumberProperties, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomFieldNumberProperties$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomFieldNumberProperties' from JSON`,
  );
}
