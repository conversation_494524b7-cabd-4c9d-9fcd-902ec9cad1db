/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Address,
  Address$inboundSchema,
  Address$Outbound,
  Address$outboundSchema,
} from "./address.js";
import {
  TaxIDFormat,
  TaxIDFormat$inboundSchema,
  TaxIDFormat$outboundSchema,
} from "./taxidformat.js";

export type CustomerUpdateExternalIDMetadata =
  | string
  | number
  | number
  | boolean;

export type CustomerUpdateExternalIDTaxID = string | TaxIDFormat;

export type CustomerUpdateExternalID = {
  /**
   * Key-value object allowing you to store additional information.
   *
   * @remarks
   *
   * The key must be a string with a maximum length of **40 characters**.
   * The value must be either:
   *
   * * A string with a maximum length of **500 characters**
   * * An integer
   * * A floating-point number
   * * A boolean
   *
   * You can store up to **50 key-value pairs**.
   */
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  /**
   * The email address of the customer. This must be unique within the organization.
   */
  email?: string | null | undefined;
  /**
   * The name of the customer.
   */
  name?: string | null | undefined;
  billingAddress?: Address | null | undefined;
  taxId?: Array<string | TaxIDFormat | null> | null | undefined;
};

/** @internal */
export const CustomerUpdateExternalIDMetadata$inboundSchema: z.ZodType<
  CustomerUpdateExternalIDMetadata,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/** @internal */
export type CustomerUpdateExternalIDMetadata$Outbound =
  | string
  | number
  | number
  | boolean;

/** @internal */
export const CustomerUpdateExternalIDMetadata$outboundSchema: z.ZodType<
  CustomerUpdateExternalIDMetadata$Outbound,
  z.ZodTypeDef,
  CustomerUpdateExternalIDMetadata
> = z.union([z.string(), z.number().int(), z.number(), z.boolean()]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerUpdateExternalIDMetadata$ {
  /** @deprecated use `CustomerUpdateExternalIDMetadata$inboundSchema` instead. */
  export const inboundSchema = CustomerUpdateExternalIDMetadata$inboundSchema;
  /** @deprecated use `CustomerUpdateExternalIDMetadata$outboundSchema` instead. */
  export const outboundSchema = CustomerUpdateExternalIDMetadata$outboundSchema;
  /** @deprecated use `CustomerUpdateExternalIDMetadata$Outbound` instead. */
  export type Outbound = CustomerUpdateExternalIDMetadata$Outbound;
}

export function customerUpdateExternalIDMetadataToJSON(
  customerUpdateExternalIDMetadata: CustomerUpdateExternalIDMetadata,
): string {
  return JSON.stringify(
    CustomerUpdateExternalIDMetadata$outboundSchema.parse(
      customerUpdateExternalIDMetadata,
    ),
  );
}

export function customerUpdateExternalIDMetadataFromJSON(
  jsonString: string,
): SafeParseResult<CustomerUpdateExternalIDMetadata, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerUpdateExternalIDMetadata$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerUpdateExternalIDMetadata' from JSON`,
  );
}

/** @internal */
export const CustomerUpdateExternalIDTaxID$inboundSchema: z.ZodType<
  CustomerUpdateExternalIDTaxID,
  z.ZodTypeDef,
  unknown
> = z.union([z.string(), TaxIDFormat$inboundSchema]);

/** @internal */
export type CustomerUpdateExternalIDTaxID$Outbound = string | string;

/** @internal */
export const CustomerUpdateExternalIDTaxID$outboundSchema: z.ZodType<
  CustomerUpdateExternalIDTaxID$Outbound,
  z.ZodTypeDef,
  CustomerUpdateExternalIDTaxID
> = z.union([z.string(), TaxIDFormat$outboundSchema]);

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerUpdateExternalIDTaxID$ {
  /** @deprecated use `CustomerUpdateExternalIDTaxID$inboundSchema` instead. */
  export const inboundSchema = CustomerUpdateExternalIDTaxID$inboundSchema;
  /** @deprecated use `CustomerUpdateExternalIDTaxID$outboundSchema` instead. */
  export const outboundSchema = CustomerUpdateExternalIDTaxID$outboundSchema;
  /** @deprecated use `CustomerUpdateExternalIDTaxID$Outbound` instead. */
  export type Outbound = CustomerUpdateExternalIDTaxID$Outbound;
}

export function customerUpdateExternalIDTaxIDToJSON(
  customerUpdateExternalIDTaxID: CustomerUpdateExternalIDTaxID,
): string {
  return JSON.stringify(
    CustomerUpdateExternalIDTaxID$outboundSchema.parse(
      customerUpdateExternalIDTaxID,
    ),
  );
}

export function customerUpdateExternalIDTaxIDFromJSON(
  jsonString: string,
): SafeParseResult<CustomerUpdateExternalIDTaxID, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerUpdateExternalIDTaxID$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerUpdateExternalIDTaxID' from JSON`,
  );
}

/** @internal */
export const CustomerUpdateExternalID$inboundSchema: z.ZodType<
  CustomerUpdateExternalID,
  z.ZodTypeDef,
  unknown
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  email: z.nullable(z.string()).optional(),
  name: z.nullable(z.string()).optional(),
  billing_address: z.nullable(Address$inboundSchema).optional(),
  tax_id: z.nullable(
    z.array(z.nullable(z.union([z.string(), TaxIDFormat$inboundSchema]))),
  ).optional(),
}).transform((v) => {
  return remap$(v, {
    "billing_address": "billingAddress",
    "tax_id": "taxId",
  });
});

/** @internal */
export type CustomerUpdateExternalID$Outbound = {
  metadata?: { [k: string]: string | number | number | boolean } | undefined;
  email?: string | null | undefined;
  name?: string | null | undefined;
  billing_address?: Address$Outbound | null | undefined;
  tax_id?: Array<string | string | null> | null | undefined;
};

/** @internal */
export const CustomerUpdateExternalID$outboundSchema: z.ZodType<
  CustomerUpdateExternalID$Outbound,
  z.ZodTypeDef,
  CustomerUpdateExternalID
> = z.object({
  metadata: z.record(
    z.union([z.string(), z.number().int(), z.number(), z.boolean()]),
  ).optional(),
  email: z.nullable(z.string()).optional(),
  name: z.nullable(z.string()).optional(),
  billingAddress: z.nullable(Address$outboundSchema).optional(),
  taxId: z.nullable(
    z.array(z.nullable(z.union([z.string(), TaxIDFormat$outboundSchema]))),
  ).optional(),
}).transform((v) => {
  return remap$(v, {
    billingAddress: "billing_address",
    taxId: "tax_id",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace CustomerUpdateExternalID$ {
  /** @deprecated use `CustomerUpdateExternalID$inboundSchema` instead. */
  export const inboundSchema = CustomerUpdateExternalID$inboundSchema;
  /** @deprecated use `CustomerUpdateExternalID$outboundSchema` instead. */
  export const outboundSchema = CustomerUpdateExternalID$outboundSchema;
  /** @deprecated use `CustomerUpdateExternalID$Outbound` instead. */
  export type Outbound = CustomerUpdateExternalID$Outbound;
}

export function customerUpdateExternalIDToJSON(
  customerUpdateExternalID: CustomerUpdateExternalID,
): string {
  return JSON.stringify(
    CustomerUpdateExternalID$outboundSchema.parse(customerUpdateExternalID),
  );
}

export function customerUpdateExternalIDFromJSON(
  jsonString: string,
): SafeParseResult<CustomerUpdateExternalID, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => CustomerUpdateExternalID$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'CustomerUpdateExternalID' from JSON`,
  );
}
