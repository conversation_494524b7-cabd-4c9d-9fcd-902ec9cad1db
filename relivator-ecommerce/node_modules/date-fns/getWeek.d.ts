import type {
  ContextOptions,
  DateArg,
  FirstWeekContainsDateOptions,
  LocalizedOptions,
  WeekOptions,
} from "./types.js";
/**
 * The {@link getWeek} function options.
 */
export interface GetWeekOptions
  extends LocalizedOptions<"options">,
    WeekOptions,
    FirstWeekContainsDateOptions,
    ContextOptions<Date> {}
/**
 * @name getWeek
 * @category Week Helpers
 * @summary Get the local week index of the given date.
 *
 * @description
 * Get the local week index of the given date.
 * The exact calculation depends on the values of
 * `options.weekStartsOn` (which is the index of the first day of the week)
 * and `options.firstWeekContainsDate` (which is the day of January, which is always in
 * the first week of the week-numbering year)
 *
 * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system
 *
 * @param date - The given date
 * @param options - An object with options
 *
 * @returns The week
 *
 * @example
 * // Which week of the local week numbering year is 2 January 2005 with default options?
 * const result = getWeek(new Date(2005, 0, 2))
 * //=> 2
 *
 * @example
 * // Which week of the local week numbering year is 2 January 2005,
 * // if Monday is the first day of the week,
 * // and the first week of the year always contains 4 January?
 * const result = getWeek(new Date(2005, 0, 2), {
 *   weekStartsOn: 1,
 *   firstWeekContainsDate: 4
 * })
 * //=> 53
 */
export declare function getWeek(
  date: DateArg<Date> & {},
  options?: GetWeekOptions | undefined,
): number;
