import { formatDistance } from "./sq/_lib/formatDistance.js";
import { formatLong } from "./sq/_lib/formatLong.js";
import { formatRelative } from "./sq/_lib/formatRelative.js";
import { localize } from "./sq/_lib/localize.js";
import { match } from "./sq/_lib/match.js";

/**
 * @category Locales
 * @summary Albanian locale.
 * @language Shqip
 * @iso-639-2 sqi
 * <AUTHOR> <PERSON> [@arditdine](https://github.com/arditdine)
 */
export const sq = {
  code: "sq",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default sq;
