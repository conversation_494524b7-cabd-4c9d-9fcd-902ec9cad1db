import type { WatchListener } from 'node:fs';
import type { ConfigurationChief } from '../ConfigurationChief.js';
import type { ConsoleStreamer } from '../ConsoleStreamer.js';
import type { IssueCollector } from '../IssueCollector.js';
import type { PrincipalFactory } from '../PrincipalFactory.js';
import type { ProjectPrincipal } from '../ProjectPrincipal.js';
import type { Report } from '../types/issues.js';
import type { ModuleGraph } from '../types/module-graph.js';
type Watch = {
    analyzedFiles: Set<string>;
    analyzeSourceFile: (filePath: string, principal: ProjectPrincipal) => void;
    chief: ConfigurationChief;
    collector: IssueCollector;
    analyze: () => Promise<void>;
    cwd: string;
    factory: PrincipalFactory;
    graph: ModuleGraph;
    isDebug: boolean;
    isIgnored: (path: string) => boolean;
    report: Report;
    streamer: ConsoleStreamer;
    unreferencedFiles: Set<string>;
};
export declare const getWatchHandler: ({ analyzedFiles, analyzeSourceFile, chief, collector, analyze, cwd, factory, graph, isDebug, isIgnored, report, streamer, unreferencedFiles, }: Watch) => Promise<WatchListener<string | Buffer<ArrayBufferLike>>>;
export {};
