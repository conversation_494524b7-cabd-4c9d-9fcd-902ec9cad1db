import { CacheConsultant } from './CacheConsultant.js';
import { type Workspace } from './ConfigurationChief.js';
import type { PluginName } from './types/PluginNames.js';
import type { Configuration, GetReferencedInternalFilePath, GetSourceFile, WorkspaceConfiguration } from './types/config.js';
import type { ConfigurationHints } from './types/issues.js';
import type { PackageJson } from './types/package-json.js';
import type { DependencySet } from './types/workspace.js';
import { type Input } from './util/input.js';
type WorkspaceManagerOptions = {
    name: string;
    dir: string;
    cwd: string;
    config: WorkspaceConfiguration;
    manifest: PackageJson;
    dependencies: DependencySet;
    getReferencedInternalFilePath: GetReferencedInternalFilePath;
    findWorkspaceByFilePath: (filePath: string) => Workspace | undefined;
    getSourceFile: GetSourceFile;
    rootIgnore: Configuration['ignore'];
    negatedWorkspacePatterns: string[];
    ignoredWorkspacePatterns: string[];
    enabledPluginsInAncestors: string[];
    isProduction: boolean;
    isStrict: boolean;
    isCache: boolean;
    cacheLocation: string;
    configFilesMap: Map<string, Map<PluginName, Set<string>>>;
};
type CacheItem = {
    resolveConfig?: Input[];
    resolveFromAST?: Input[];
};
export declare class WorkspaceWorker {
    name: string;
    dir: string;
    cwd: string;
    config: WorkspaceConfiguration;
    manifest: PackageJson;
    dependencies: DependencySet;
    getReferencedInternalFilePath: GetReferencedInternalFilePath;
    findWorkspaceByFilePath: (filePath: string) => Workspace | undefined;
    getSourceFile: GetSourceFile;
    isProduction: boolean;
    isStrict: boolean;
    rootIgnore: Configuration['ignore'];
    negatedWorkspacePatterns: string[];
    ignoredWorkspacePatterns: string[];
    enabledPluginsMap: Record<PluginName, boolean>;
    enabledPlugins: PluginName[];
    enabledPluginsInAncestors: string[];
    cache: CacheConsultant<CacheItem>;
    configFilesMap: Map<string, Map<PluginName, Set<string>>>;
    constructor({ name, dir, cwd, config, manifest, dependencies, isProduction, isStrict, rootIgnore, negatedWorkspacePatterns, ignoredWorkspacePatterns, enabledPluginsInAncestors, getReferencedInternalFilePath, findWorkspaceByFilePath, getSourceFile, isCache, cacheLocation, configFilesMap, }: WorkspaceManagerOptions);
    init(): Promise<void>;
    private determineEnabledPlugins;
    private getConfigForPlugin;
    getEntryFilePatterns(): string[];
    getProjectFilePatterns(projectFilePatterns: string[]): string[];
    getPluginProjectFilePatterns(patterns?: string[]): string[];
    getPluginConfigPatterns(): string[];
    getPluginEntryFilePatterns(patterns: string[]): string[];
    getProductionEntryFilePatterns(negatedTestFilePatterns: string[]): string[];
    getProductionProjectFilePatterns(negatedTestFilePatterns: string[]): string[];
    private getConfigurationFilePatterns;
    getIgnorePatterns(): string[];
    runPlugins(): Promise<Input[]>;
    getConfigurationHints(type: 'entry' | 'project', patterns: string[], filePaths: string[], includedPaths: Set<string>): ConfigurationHints;
    onDispose(): void;
}
export {};
