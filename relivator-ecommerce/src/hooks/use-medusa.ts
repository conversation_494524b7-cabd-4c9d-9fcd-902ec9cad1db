"use client"

import { useEffect, useState } from "react"
import { medusa, type Product, type Cart } from "@/lib/medusa"

// Hook for fetching products
export function useProducts() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchProducts() {
      try {
        setLoading(true)
        const response = await medusa.store.product.list()
        setProducts(response.products || [])
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch products")
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [])

  return { products, loading, error }
}

// Hook for cart management
export function useCart() {
  const [cart, setCart] = useState<Cart | null>(null)
  const [loading, setLoading] = useState(false)

  // Create or retrieve cart
  const initializeCart = async () => {
    try {
      setLoading(true)
      let cartId = localStorage.getItem("medusa_cart_id")
      
      if (cartId) {
        // Try to retrieve existing cart
        try {
          const response = await medusa.store.cart.retrieve(cartId)
          setCart(response.cart)
          return response.cart
        } catch {
          // Cart doesn't exist, create new one
          cartId = null
        }
      }
      
      if (!cartId) {
        // Create new cart
        const response = await medusa.store.cart.create({})
        setCart(response.cart)
        localStorage.setItem("medusa_cart_id", response.cart.id)
        return response.cart
      }
    } catch (error) {
      console.error("Failed to initialize cart:", error)
    } finally {
      setLoading(false)
    }
  }

  // Add item to cart
  const addToCart = async (variantId: string, quantity: number = 1) => {
    try {
      setLoading(true)
      const currentCart = cart || await initializeCart()
      if (!currentCart) return

      const response = await medusa.store.cart.lineItem.create(currentCart.id, {
        variant_id: variantId,
        quantity,
      })
      setCart(response.cart)
    } catch (error) {
      console.error("Failed to add to cart:", error)
    } finally {
      setLoading(false)
    }
  }

  // Remove item from cart
  const removeFromCart = async (lineItemId: string) => {
    try {
      setLoading(true)
      if (!cart) return

      const response = await medusa.store.cart.lineItem.delete(cart.id, lineItemId)
      setCart(response.cart)
    } catch (error) {
      console.error("Failed to remove from cart:", error)
    } finally {
      setLoading(false)
    }
  }

  // Update item quantity
  const updateQuantity = async (lineItemId: string, quantity: number) => {
    try {
      setLoading(true)
      if (!cart) return

      const response = await medusa.store.cart.lineItem.update(cart.id, lineItemId, {
        quantity,
      })
      setCart(response.cart)
    } catch (error) {
      console.error("Failed to update quantity:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    initializeCart()
  }, [])

  return {
    cart,
    loading,
    addToCart,
    removeFromCart,
    updateQuantity,
    initializeCart,
  }
}
