{"name": "relivator", "description": "Relivator is a robust eCommerce template built with Next.js. It's designed for developers who want a fast, modern, and scalable foundation without reinventing the backend.", "version": "1.4.6", "type": "module", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbopack", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:auth": "bun src/lib/auth-db.ts", "ui": "bunx --bun shadcn@latest add", "latest": "bun update --latest && bun check", "check": "tsc --noEmit && eslint --cache --fix . && biome check --fix --unsafe . && knip", "tests": "bun test ./.tests"}, "dependencies": {"@medusajs/js-sdk": "^2.8.4", "@paralleldrive/cuid2": "^2.2.2", "@polar-sh/better-auth": "^0.1.1", "@polar-sh/sdk": "^0.32.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@tanstack/react-form": "^1.9.1", "@tanstack/react-table": "8.21.3", "@tanstack/table-core": "^8.21.3", "@uploadthing/react": "^7.3.0", "@vercel/speed-insights": "^1.2.0", "animejs": "^4.0.2", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "framer-motion": "^12.10.1", "fs-extra": "^11.3.0", "lucide-react": "^0.508.0", "next": "15.3.2", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "ofetch": "^1.4.1", "pathe": "^2.0.3", "pg": "^8.15.6", "postgres": "^3.4.5", "react": "^19.1.0", "react-day-picker": "9.6.7", "react-dom": "^19.1.0", "server-only": "^0.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.6.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.4"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint-react/eslint-plugin": "^1.49.0", "@eslint/js": "^9.26.0", "@tailwindcss/postcss": "^4.1.5", "@total-typescript/ts-reset": "^0.6.1", "@types/bun": "^1.2.12", "@types/fs-extra": "^11.0.4", "@types/node": "^22.15.15", "@types/pg": "^8.15.0", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@types/uuid": "^10.0.0", "@typescript-eslint/parser": "^8.32.0", "drizzle-kit": "^0.31.1", "eslint": "^9.26.0", "eslint-plugin-perfectionist": "^4.12.3", "eslint-plugin-readable-tailwind": "^2.1.1", "execa": "^9.5.2", "knip": "^5.55.0", "magic-string": "^0.30.17", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0"}}